import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  message,
  Typography,
  Space,
  Upload,
} from 'antd';
import { MailOutlined, UploadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PhoneNumberInput from '../components/PhoneNumberInput';
import '../styles/AccountRecovery.css';

const { Title, Text } = Typography;
const { TextArea } = Input;

const AccountRecovery = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const handleSubmit = async (values) => {
    setIsLoading(true);
    try {
      // Create a FormData object to handle file uploads
      const formData = new FormData();
      
      // Add form values to FormData
      Object.keys(values).forEach(key => {
        if (key !== 'documents') {
          formData.append(key, values[key]);
        }
      });
      
      // Add files to FormData
      fileList.forEach(file => {
        formData.append('documents', file.originFileObj);
      });
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      message.success(t('accountRecovery.messages.success'));
      navigate('/login');
    } catch (error) {
      message.error(t('accountRecovery.messages.error'));
    } finally {
      setIsLoading(false);
    }
  };

  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  };

  const handleFileChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  const beforeUpload = (file) => {
    const isValidFileType = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/jpg'
    ].includes(file.type);
    
    if (!isValidFileType) {
      message.error(t('accountRecovery.form.validation.fileType'));
    }
    
    const isLessThan10MB = file.size / 1024 / 1024 < 10;
    if (!isLessThan10MB) {
      message.error(t('accountRecovery.form.validation.fileSize'));
    }
    
    return false; // Prevent auto upload
  };

  return (
    <div className="recovery-container">
      <div className="recovery-content">
        <div className="recovery-header">
          <Title level={2}>{t('accountRecovery.title')}</Title>
          <Text type="secondary">
            {t('accountRecovery.subtitle')}
          </Text>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="recovery-form"
        >
          <Form.Item
            label={t('accountRecovery.form.labels.phoneNumber')}
            name="phoneNumber"
            rules={[
              { required: true, message: t('accountRecovery.form.validation.phoneRequired') },
              {
                pattern: /^[0-9]{8}$/,
                message: t('accountRecovery.form.validation.phoneFormat'),
              },
            ]}
          >
            <PhoneNumberInput />
          </Form.Item>

          {/* <Form.Item
            label={t('accountRecovery.form.labels.email')}
            name="email"
            rules={[
              { required: true, message: t('accountRecovery.form.validation.emailRequired') },
              { type: 'email', message: t('accountRecovery.form.validation.emailFormat') },
            ]}
          >
            <Input
              prefix={<MailOutlined style={{ marginRight: '8px' }} />}
              placeholder={t('accountRecovery.form.placeholders.email')}
              size="large"
            />
          </Form.Item> */}

          <Form.Item
            label={t('accountRecovery.form.labels.details')}
            name="details"
            rules={[
              { required: true, message: t('accountRecovery.form.validation.detailsRequired') },
              { min: 20, message: t('accountRecovery.form.validation.detailsLength') },
            ]}
          >
            <TextArea
              placeholder={t('accountRecovery.form.placeholders.details')}
              rows={4}
              size="large"
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="documents"
            label={t('accountRecovery.form.labels.documents')}
            valuePropName="fileList"
            getValueFromEvent={normFile}
            extra={t('accountRecovery.form.hints.documents')}
            rules={[
              { required: true, message: t('accountRecovery.form.validation.documentsRequired') }
            ]}
          >
            <Upload
              listType="picture"
              fileList={fileList}
              beforeUpload={beforeUpload}
              onChange={handleFileChange}
              multiple
              maxCount={3}
            >
              <Button icon={<UploadOutlined />} size="large">
                {t('accountRecovery.buttons.upload')}
              </Button>
            </Upload>
          </Form.Item>

          <Form.Item>
            <Space className="recovery-form-buttons" style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                size="large"
                onClick={() => navigate(-1)}
              >
                {t('accountRecovery.buttons.back')}
              </Button>
              <Button
                size="large"
                type="primary"
                htmlType="submit"
                loading={isLoading}
              >
                {t('accountRecovery.buttons.submit')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default AccountRecovery; 