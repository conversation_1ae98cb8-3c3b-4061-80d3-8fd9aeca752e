import React, { useState, useEffect } from 'react';
import { Tabs, Spin, Alert, App } from 'antd';
import {
    BarChartOutlined,
    TableOutlined,
} from '@ant-design/icons';
import '../../styles/ReportPage.css';
import ReportDashboard from './components/SingleEventReportsTab';
import HistoricalDataTab from './components/HistoricalDataTab';
import { useTranslation } from 'react-i18next';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { eventService } from '../../services/eventService';
import { fetchEventRegistrationsList } from '../../hooks/useVerificationQueries';
import { useUser } from '../../contexts/UserContext';
import { useOrganization } from '../../contexts/OrganizationContext';
import { ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';

// Main component for event reports
const ReportPage = () => {
    const { t } = useTranslation();
    const { eventId } = useParams();
    const { message } = App.useApp();
    const [activeTab, setActiveTab] = useState('dashboard');
    const location = useLocation();
    const navigate = useNavigate();
    const { currentUser } = useUser();
    const { currentOrganization } = useOrganization();
    const isSuperAdmin = currentUser?.role === 'super_admin';
    
    // Check if a tab parameter is present in the URL query
    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const tabParam = params.get('tab');
        if (tabParam && (tabParam === 'dashboard' || tabParam === 'historical')) {
            setActiveTab(tabParam);
        }
    }, [location.search]);
    
    // State management
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [eventDetails, setEventDetails] = useState(null);
    const [registrationsData, setRegistrationsData] = useState([]);
    const [statisticsData, setStatisticsData] = useState(null);

    // Load event details and registrations
    useEffect(() => {
        const fetchEventData = async () => {
            if (!eventId || !currentOrganization?.id) return;
            
            setLoading(true);
            setError(null);
            
            try {
                // Get event details first to check permissions
                const eventDetailsResponse = await eventService.getPublicEventDetail(eventId);
                
                // Check permissions for ALL_ORGANIZATION_ID events
                if (eventDetailsResponse.organization_id === ALL_ORGANIZATION_ID && !isSuperAdmin) {
                    message.error(t('messages.notAvailableForAllOrgs'));
                    navigate('/error/403');
                    return;
                }
                
                setEventDetails(eventDetailsResponse);
                
                // Try to use the new statistics API first
                try {
                    const statisticsResponse = await eventService.getSpecificEventStatistics(
                        eventDetailsResponse.organization_id, 
                        eventId
                    );
                    console.log('Received statistics from API:', statisticsResponse);
                    setStatisticsData(statisticsResponse);
                    
                    // We still need registrations data for historical tab
                    const registrationsResponse = await fetchEventRegistrationsList(eventId, null, 1, 20);
                    setRegistrationsData(Array.isArray(registrationsResponse.registrations) 
                        ? registrationsResponse.registrations 
                        : []);
                } catch (statsError) {
                    console.warn('New statistics API not available, falling back to registration data:', statsError);
                    // Fallback to old method if new API is not available
                    const registrationsResponse = await fetchEventRegistrationsList(eventId, null, 1, 100); // Get more data for calculations
                    setRegistrationsData(Array.isArray(registrationsResponse.registrations) 
                        ? registrationsResponse.registrations 
                        : []);
                    setStatisticsData(null); // No statistics data available
                }
            } catch (err) {
                console.error('Error fetching data:', err);
                setError(err.message || t('adminEvents.singleEventReport.errors.fetchEventDetailsFailed'));
                message.error(t('messages.fetchError'));
            } finally {
                setLoading(false);
            }
        };

        fetchEventData();
    }, [eventId, currentOrganization?.id, t, message, isSuperAdmin, navigate]);

    // Convert new API statistics data to component format
    const convertApiStatisticsToComponentFormat = (apiStats) => {
        if (!apiStats) return null;

        // Convert age groups from API format to component format
        const ageGroupMapping = {
            '0-17': 'under18',
            '18-24': '18to35',
            '25-34': '18to35',
            '21-30': '18to35',
            '35-44': '36to55',
            '45-54': '36to55',
            '55-64': 'over55',
            '65+': 'over55'
        };

        const ageGroupColors = {
            'under18': 'border-indigo-400',
            '18to35': 'border-indigo-500',
            '36to55': 'border-violet-500',
            'over55': 'border-purple-500',
            'unknown': 'border-gray-500'
        };

        // Combine participants and volunteers age data
        const combinedAgeGroups = {};
        [...(apiStats.participants_by_age || []), ...(apiStats.volunteers_by_age || [])].forEach(item => {
            const mappedGroup = ageGroupMapping[item.age_range] || 'unknown';
            combinedAgeGroups[mappedGroup] = (combinedAgeGroups[mappedGroup] || 0) + item.count;
        });

        const totalAgeCount = Object.values(combinedAgeGroups).reduce((sum, count) => sum + count, 0);
        const convertedAgeGroups = Object.entries(combinedAgeGroups).map(([name, amount]) => ({
            name,
            amount,
            share: totalAgeCount ? `${Math.round((amount / totalAgeCount) * 100)}%` : '0%',
            borderColor: ageGroupColors[name] || 'border-gray-500'
        }));

        // Convert gender data from API format to component format
        const genderMapping = {
            'm': 'male',
            'f': 'female',
            'o': 'unknown'
        };

        const genderColors = {
            'male': 'border-blue-500',
            'female': 'border-red-500',
            'unknown': 'border-gray-500'
        };

        const combinedGenderGroups = {};
        [...(apiStats.participants_by_gender || []), ...(apiStats.volunteers_by_gender || [])].forEach(item => {
            const mappedGender = genderMapping[item.gender] || 'unknown';
            combinedGenderGroups[mappedGender] = (combinedGenderGroups[mappedGender] || 0) + item.count;
        });

        const totalGenderCount = Object.values(combinedGenderGroups).reduce((sum, count) => sum + count, 0);
        const convertedGenderData = Object.entries(combinedGenderGroups).map(([name, amount]) => ({
            name,
            amount,
            share: totalGenderCount ? `${Math.round((amount / totalGenderCount) * 100)}%` : '0%',
            borderColor: genderColors[name] || 'border-gray-500'
        }));

        // Convert trend data from API format to component format
        const participantsByDate = apiStats.participants_by_date || [];
        const volunteersByDate = apiStats.volunteers_by_date || [];
        
        // Create a map of all dates
        const dateMap = new Map();
        participantsByDate.forEach(item => {
            dateMap.set(item.date, { date: item.date, Participants: item.count, Volunteers: 0 });
        });
        volunteersByDate.forEach(item => {
            const existing = dateMap.get(item.date);
            if (existing) {
                existing.Volunteers = item.count;
            } else {
                dateMap.set(item.date, { date: item.date, Participants: 0, Volunteers: item.count });
            }
        });

        const convertedTrendData = Array.from(dateMap.values()).sort((a, b) => new Date(a.date) - new Date(b.date));

        return {
            statistics: {
                participants: {
                    total: apiStats.participants_count || 0,
                    change: '0%',
                    changeType: 'positive'
                },
                volunteers: {
                    total: apiStats.volunteers_count || 0,
                    change: '0%',
                    changeType: 'positive'
                },
                registration: {
                    current: (apiStats.participants_count || 0) + (apiStats.volunteers_count || 0),
                    maximum: apiStats.max_participants || 100,
                    percentage: apiStats.max_participants 
                        ? Math.round(((apiStats.participants_count || 0) + (apiStats.volunteers_count || 0)) / apiStats.max_participants * 100)
                        : 0
                }
            },
            ageGroups: convertedAgeGroups,
            genderData: convertedGenderData,
            trendData: convertedTrendData
        };
    };

    // Calculate age groups from registration data
    const calculateAgeGroups = () => {
        if (!registrationsData || registrationsData.length === 0) return [];
        
        const ageGroups = {
            "under18": { amount: 0, borderColor: 'border-indigo-400' },
            "18to35": { amount: 0, borderColor: 'border-indigo-500' },
            "36to55": { amount: 0, borderColor: 'border-violet-500' },
            "over55": { amount: 0, borderColor: 'border-purple-500' },
            "unknown": { amount: 0, borderColor: 'border-gray-500' }
        };
        
        const currentYear = new Date().getFullYear();
        let totalParticipants = 0;
        
        registrationsData.forEach(reg => {
            // Try to get date of birth from various possible fields
            const dob = reg.user_date_of_birth || reg.date_of_birth || reg.dob;
            
            if (dob) {
                try {
                    const birthYear = new Date(dob).getFullYear();
                    const age = currentYear - birthYear;
                    
                    if (age < 18) ageGroups["under18"].amount++;
                    else if (age >= 18 && age <= 35) ageGroups["18to35"].amount++;
                    else if (age >= 36 && age <= 55) ageGroups["36to55"].amount++;
                    else if (age > 55) ageGroups["over55"].amount++;
                    
                    totalParticipants++;
                } catch (e) {
                    ageGroups["unknown"].amount++;
                    totalParticipants++;
                    console.warn('Invalid date format for DOB:', dob);
                }
            } else {
                ageGroups["unknown"].amount++;
                totalParticipants++;
            }
        });
        
        // Calculate share percentages
        return Object.entries(ageGroups).map(([name, data]) => ({
            name,
            amount: data.amount,
            share: totalParticipants ? `${Math.round((data.amount / totalParticipants) * 100)}%` : '0%',
            borderColor: data.borderColor
        }));
    };
    
    // Calculate gender distribution from registration data
    const calculateGenderDistribution = () => {
        if (!registrationsData || registrationsData.length === 0) return [];
        
        const genderCount = {
            "male": { amount: 0, borderColor: 'border-blue-500' },
            "female": { amount: 0, borderColor: 'border-red-500' },
            "unknown": { amount: 0, borderColor: 'border-gray-500' }
        };
        
        let totalWithGender = 0;
        
        registrationsData.forEach(reg => {
            // Try to get gender from various possible fields
            const gender = reg.user_gender || reg.gender;
            
            if (gender) {
                const lowerGender = gender.toLowerCase();
                if (lowerGender === 'male' || lowerGender === 'm') genderCount.male.amount++;
                else if (lowerGender === 'female' || lowerGender === 'f') genderCount.female.amount++;
                else {
                    genderCount.unknown.amount++;
                }
                
                totalWithGender++;
            } else {
                genderCount.unknown.amount++;
                totalWithGender++;
            }
        });
        
        // Calculate share percentages
        return Object.entries(genderCount).map(([name, data]) => ({
            name,
            amount: data.amount,
            share: totalWithGender ? `${Math.round((data.amount / totalWithGender) * 100)}%` : '0%',
            borderColor: data.borderColor
        }));
    };
    
    // Calculate registration trend data from registered_at dates
    const calculateTrendData = () => {
        if (!registrationsData || registrationsData.length === 0) return [];
        
        const trendMap = new Map();
        
        // First, collect all unique registration dates
        const allDates = [];
        
        // For each registration, get its date and add to our collection
        registrationsData.forEach(reg => {
            if (reg.registered_at) {
                const dateStr = new Date(reg.registered_at).toISOString().split('T')[0];
                if (!allDates.includes(dateStr)) {
                    allDates.push(dateStr);
                }
            }
        });
        
        // Sort dates chronologically
        allDates.sort((a, b) => new Date(a) - new Date(b));
        
        // If we have dates, make sure all days between first registration and event end are included
        if (allDates.length > 0) {
            const firstDate = new Date(allDates[0]);
            
            // Get event end date (if available) or use today's date
            let endDate;
            if (eventDetails && eventDetails.end_time && new Date(eventDetails.end_time) > firstDate) {
                endDate = new Date(eventDetails.end_time);
            } else {
                // If no end date or it's before the first registration, use today or last registration
                const today = new Date();
                const lastRegistrationDate = new Date(allDates[allDates.length - 1]);
                endDate = today > lastRegistrationDate ? today : lastRegistrationDate;
            }
            
            // Fill in all dates between first registration and event end (or today)
            for (let date = new Date(firstDate); date <= endDate; date.setDate(date.getDate() + 1)) {
                const dateStr = date.toISOString().split('T')[0];
                trendMap.set(dateStr, { date: dateStr, Participants: 0, Volunteers: 0 });
            }
            
            // Now count registrations for each date
            registrationsData.forEach(reg => {
                if (reg.registered_at) {
                    const dateStr = new Date(reg.registered_at).toISOString().split('T')[0];
                    
                    if (trendMap.has(dateStr)) {
                        const data = trendMap.get(dateStr);
                        
                        // Check if registration is for a volunteer or participant
                        if (reg.registration_role === 'staff' || reg.registration_role === 'volunteer' || reg.is_staff) {
                            data.Volunteers += 1;
                        } else {
                            data.Participants += 1;
                        }
                        
                        trendMap.set(dateStr, data);
                    } else {
                        console.warn(`Date ${dateStr} not found in trendMap, this shouldn't happen`);
                    }
                }
            });
        } else {
            // If no valid dates found, return empty array
            return [];
        }
        
        // Convert to array and sort by date
        return Array.from(trendMap.values()).sort((a, b) => new Date(a.date) - new Date(b.date));
    };

    // Get processed data from new API or calculate from registrations
    const getProcessedData = () => {
        if (statisticsData) {
            const convertedData = convertApiStatisticsToComponentFormat(statisticsData);
            console.log('Converted statistics data:', convertedData);
            return convertedData;
        } else {
            // Fallback to calculations if statistics data is not available
            const fallbackData = {
                statistics: {
                    participants: {
                        total: registrationsData.filter(reg => 
                            reg.registration_role !== 'staff' && reg.registration_role !== 'volunteer' && !reg.is_staff
                        ).length,
                        change: '0%',
                        changeType: 'positive'
                    },
                    volunteers: {
                        total: registrationsData.filter(reg => 
                            reg.registration_role === 'staff' || reg.registration_role === 'volunteer' || reg.is_staff
                        ).length,
                        change: '0%',
                        changeType: 'positive'
                    },
                    registration: {
                        current: registrationsData.length,
                        maximum: eventDetails?.participant_limit || 100,
                        percentage: eventDetails?.participant_limit 
                            ? Math.round((registrationsData.length / eventDetails.participant_limit) * 100)
                            : 0
                    }
                },
                ageGroups: calculateAgeGroups(),
                genderData: calculateGenderDistribution(),
                trendData: calculateTrendData()
            };
            console.log('Fallback processed data:', fallbackData);
            return fallbackData;
        }
    };

    const processedData = getProcessedData();
    const processedStatistics = processedData.statistics;

    // Handle tab change
    const handleTabChange = (key) => {
        setActiveTab(key);
        
        // Update URL with the tab parameter without reloading the page
        const params = new URLSearchParams(location.search);
        params.set('tab', key);
        navigate(`/events/${eventId}/event-reports?${params.toString()}`, { replace: true });
    };

    let items = [
        {
            key: 'dashboard',
            label: t('adminEvents.singleEventReport.tabs.dashboard'),
            icon: <BarChartOutlined />,
            children: loading ? (
                <div className="flex justify-center items-center h-[400px]">
                    <Spin size="large" />
                </div>
            ) : (
                <ReportDashboard 
                    eventId={eventId} 
                    participantData={registrationsData}
                    statistics={processedStatistics}
                    dataByAgeGroup={processedData.ageGroups}
                    dataByGender={processedData.genderData}
                    trendData={processedData.trendData}
                />
            ),
        },
        {
            key: 'historical',
            label: t('adminEvents.singleEventReport.attendance.title'),
            icon: <TableOutlined />,
            children: (
                <HistoricalDataTab 
                    eventId={eventId}
                    registrationsData={registrationsData}
                    loading={loading}
                />
            ),
        },
    ];

    return (
        <div className="p-0 md:p-4">
            <Tabs
                activeKey={activeTab}
                onChange={handleTabChange}
                items={items}
            />
        </div>
    );
};

export default ReportPage;
