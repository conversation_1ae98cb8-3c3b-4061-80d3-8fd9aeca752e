-- Drop existing tables related to post tags
DROP TABLE IF EXISTS "post_post_tags";
DROP TABLE IF EXISTS "post_tags";

-- Drop existing tables related to event tags
DROP TABLE IF EXISTS "event_event_tags";
DROP TABLE IF EXISTS "event_tags";

-- Create the new "post_tags" table with i18n columns
CREATE TABLE "post_tags" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name_en" VARCHAR(255) NOT NULL,
  "name_zh_hk" VARCHAR(255) NOT NULL,
  "name_zh_cn" VARCHAR(255) NOT NULL,
  "description_en" TEXT,
  "description_zh_hk" TEXT,
  "description_zh_cn" TEXT,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  UNIQUE ("name_en"),
  UNIQUE ("name_zh_hk"),
  UNIQUE ("name_zh_cn")
);

-- Create the new "event_tags" table with i18n columns
CREATE TABLE "event_tags" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "name_en" VARCHAR(100) NOT NULL,
    "name_zh_hk" VARCHAR(100) NOT NULL,
    "name_zh_cn" VARCHAR(100) NOT NULL,
    "description_en" TEXT,
    "description_zh_hk" TEXT,
    "description_zh_cn" TEXT,
    "created_by_user_id" UUID REFERENCES "users"("id"),
    "is_globally_approved" BOOLEAN NOT NULL DEFAULT TRUE,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE ("name_en"),
    UNIQUE ("name_zh_hk"),
    UNIQUE ("name_zh_cn")
);

-- Recreate the "post_post_tags" table
CREATE TABLE "post_post_tags" (
  "post_id" uuid NOT NULL,
  "tag_id" uuid NOT NULL,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  PRIMARY KEY ("post_id", "tag_id")
);

-- Recreate the "event_event_tags" table
CREATE TABLE "event_event_tags" (
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "event_tag_id" UUID NOT NULL REFERENCES "event_tags"("id") ON DELETE CASCADE,
    PRIMARY KEY ("event_id", "event_tag_id")
);


-- Add foreign keys for post_post_tags
ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("post_id") REFERENCES "posts" ("id") ON DELETE CASCADE;
ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("tag_id") REFERENCES "post_tags" ("id") ON DELETE CASCADE;

-- Seed initial event tags with the new structure
INSERT INTO "event_tags" (name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, is_globally_approved, created_by_user_id) VALUES
('Exchange Group / Exchange Tour', '交流團', '交流团', 'Events related to exchange programs or tours.', '交流團相關活動。', '交流团相关活动。', TRUE, NULL),
('Youth Activity', '青少年活動', '青少年活动', 'Activities specifically for youth.', '青少年專屬活動。', '青少年专属活动。', TRUE, NULL),
('Senior Activity', '長者活動', '长者活动', 'Activities designed for seniors.', '長者專屬活動。', '长者专属活动。', TRUE, NULL),
('Funding Application / Grant', '資助申請', '资助申请', 'Events or information related to funding applications or grants.', '資助申請或撥款相關活動資訊。', '资助申请或拨款相关活动资讯。', TRUE, NULL),
('Volunteer Service', '義務服務', '义务服务', 'Opportunities for volunteer service.', '義務服務機會。', '义务服务机会。', TRUE, NULL),
('Material Distribution', '物資派貨', '物资派送 / 物资分发', 'Events involving the distribution of materials or goods.', '物資派送或分發活動。', '物资派送或分发活动。', TRUE, NULL),
('Government Funding Scheme', '政府資助計劃', '政府资助计划', 'Events related to government funding schemes.', '政府資助計劃相關活動。', '政府资助计划相关活动。', TRUE, NULL);

-- Seed initial post tags with the new structure
INSERT INTO "post_tags" (name_en, name_zh_hk, name_zh_cn) VALUES
('Government News', '政府新聞', '政府新闻'),
('Community Update', '社區動態', '社区动态'),
('Event Highlight', '活動亮點', '活动亮点'),
('Member Story', '會員故事', '会员故事'),
('Official Announcement', '官方公告', '官方公告');

CREATE TRIGGER set_timestamp_event_tags
BEFORE UPDATE ON "event_tags"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_post_tags
BEFORE UPDATE ON "post_tags"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
