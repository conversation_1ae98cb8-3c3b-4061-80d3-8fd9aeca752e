import React from 'react';
import {
    FileOutlined,
    FilePdfOutlined,
    FileWordOutlined,
    FileExcelOutlined,
    FilePptOutlined,
    FileZipOutlined,
    FileImageOutlined
} from '@ant-design/icons';

/**
 * Helper function to get file icon based on file type
 * @param {string} fileName - The name of the file
 * @param {string} [fileType] - The MIME type of the file (e.g., 'image/jpeg', 'application/pdf')
 * @returns {JSX.Element} - The appropriate icon component
 */
export const getFileIcon = (fileName, fileType) => {
    let typeToMatch = '';

    if (fileType) {
        const mainType = fileType.split('/')[0];
        const subType = fileType.split('/')[1];

        if (mainType === 'image') {
            typeToMatch = 'image';
        } else if (mainType === 'application' && subType === 'pdf') {
            typeToMatch = 'pdf';
        } else if (mainType === 'application' && (subType === 'msword' || subType === 'vnd.openxmlformats-officedocument.wordprocessingml.document')) {
            typeToMatch = 'doc';
        } else if (mainType === 'application' && (subType === 'vnd.ms-excel' || subType === 'vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
            typeToMatch = 'xls';
        } else if (mainType === 'application' && (subType === 'vnd.ms-powerpoint' || subType === 'vnd.openxmlformats-officedocument.presentationml.presentation')) {
            typeToMatch = 'ppt';
        } else if (mainType === 'application' && (subType === 'zip' || subType === 'x-zip-compressed')) {
            typeToMatch = 'zip';
        }
        // Add more MIME type checks as needed
    }

    if (!typeToMatch && fileName) {
        const extension = fileName.split('.').pop()?.toLowerCase() || '';
        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension)) {
            typeToMatch = 'image';
        } else if (extension === 'pdf') {
            typeToMatch = 'pdf';
        } else if (['doc', 'docx'].includes(extension)) {
            typeToMatch = 'doc';
        } else if (['xls', 'xlsx'].includes(extension)) {
            typeToMatch = 'xls';
        } else if (['ppt', 'pptx'].includes(extension)) {
            typeToMatch = 'ppt';
        } else if (['zip', 'rar', '7z'].includes(extension)) {
            typeToMatch = 'zip';
        }
    }


    switch (typeToMatch) {
        case 'pdf':
            return <FilePdfOutlined style={{ fontSize: '24px', color: '#ff4d4f' }} />;
        case 'doc':
        case 'docx':
            return <FileWordOutlined style={{ fontSize: '24px', color: '#1890ff' }} />;
        case 'xls':
        case 'xlsx':
            return <FileExcelOutlined style={{ fontSize: '24px', color: '#52c41a' }} />;
        case 'ppt':
        case 'pptx':
            return <FilePptOutlined style={{ fontSize: '24px', color: '#fa8c16' }} />;
        case 'zip':
        case 'rar':
            return <FileZipOutlined style={{ fontSize: '24px', color: '#722ed1' }} />;
        case 'image':
            return <FileImageOutlined style={{ fontSize: '24px', color: '#13c2c2' }} />;
        default:
            return <FileOutlined style={{ fontSize: '24px', color: '#8c8c8c' }} />;
    }
};