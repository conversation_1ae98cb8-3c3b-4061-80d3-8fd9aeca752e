// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: post_tags.sql

package db

import (
	"context"

	"github.com/google/uuid"
)

const addTagToPost = `-- name: AddTagToPost :exec
INSERT INTO post_post_tags (
    post_id,
    tag_id
) VALUES (
    $1, $2
) ON CONFLICT (post_id, tag_id) DO NOTHING
`

type AddTagToPostParams struct {
	PostID uuid.UUID `db:"post_id" json:"post_id"`
	TagID  uuid.UUID `db:"tag_id" json:"tag_id"`
}

func (q *Queries) AddTagToPost(ctx context.Context, arg AddTagToPostParams) error {
	_, err := q.db.Exec(ctx, addTagToPost, arg.PostID, arg.TagID)
	return err
}

const createPostTag = `-- name: CreatePostTag :one
INSERT INTO post_tags (
    name_en,
    name_zh_hk,
    name_zh_cn,
    description_en,
    description_zh_hk,
    description_zh_cn
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_at, updated_at
`

type CreatePostTagParams struct {
	NameEn          string  `db:"name_en" json:"name_en"`
	NameZhHk        string  `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn        string  `db:"name_zh_cn" json:"name_zh_cn"`
	DescriptionEn   *string `db:"description_en" json:"description_en"`
	DescriptionZhHk *string `db:"description_zh_hk" json:"description_zh_hk"`
	DescriptionZhCn *string `db:"description_zh_cn" json:"description_zh_cn"`
}

func (q *Queries) CreatePostTag(ctx context.Context, arg CreatePostTagParams) (PostTag, error) {
	row := q.db.QueryRow(ctx, createPostTag,
		arg.NameEn,
		arg.NameZhHk,
		arg.NameZhCn,
		arg.DescriptionEn,
		arg.DescriptionZhHk,
		arg.DescriptionZhCn,
	)
	var i PostTag
	err := row.Scan(
		&i.ID,
		&i.NameEn,
		&i.NameZhHk,
		&i.NameZhCn,
		&i.DescriptionEn,
		&i.DescriptionZhHk,
		&i.DescriptionZhCn,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deletePostTag = `-- name: DeletePostTag :exec
DELETE FROM post_tags
WHERE id = $1
`

func (q *Queries) DeletePostTag(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deletePostTag, id)
	return err
}

const getPostTagByID = `-- name: GetPostTagByID :one
SELECT id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_at, updated_at FROM post_tags
WHERE id = $1
`

func (q *Queries) GetPostTagByID(ctx context.Context, id uuid.UUID) (PostTag, error) {
	row := q.db.QueryRow(ctx, getPostTagByID, id)
	var i PostTag
	err := row.Scan(
		&i.ID,
		&i.NameEn,
		&i.NameZhHk,
		&i.NameZhCn,
		&i.DescriptionEn,
		&i.DescriptionZhHk,
		&i.DescriptionZhCn,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getPostTagByName = `-- name: GetPostTagByName :one
SELECT id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_at, updated_at FROM post_tags
WHERE name_en = $1 OR name_zh_hk = $1 OR name_zh_cn = $1
`

func (q *Queries) GetPostTagByName(ctx context.Context, nameEn string) (PostTag, error) {
	row := q.db.QueryRow(ctx, getPostTagByName, nameEn)
	var i PostTag
	err := row.Scan(
		&i.ID,
		&i.NameEn,
		&i.NameZhHk,
		&i.NameZhCn,
		&i.DescriptionEn,
		&i.DescriptionZhHk,
		&i.DescriptionZhCn,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getPostsForTag = `-- name: GetPostsForTag :many
SELECT p.id, p.organization_id, p.title, p.slug, p.status, p.published_at, p.created_at, p.updated_at, p.content, p.author_id, p.updated_by
FROM posts p
JOIN post_post_tags ppt ON p.id = ppt.post_id
WHERE ppt.tag_id = $1
ORDER BY p.created_at DESC
`

func (q *Queries) GetPostsForTag(ctx context.Context, tagID uuid.UUID) ([]Post, error) {
	rows, err := q.db.Query(ctx, getPostsForTag, tagID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Post{}
	for rows.Next() {
		var i Post
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.Slug,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Content,
			&i.AuthorID,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTagsForPost = `-- name: GetTagsForPost :many
SELECT pt.id, pt.name_en, pt.name_zh_hk, pt.name_zh_cn, pt.description_en, pt.description_zh_hk, pt.description_zh_cn, pt.created_at, pt.updated_at
FROM post_tags pt
JOIN post_post_tags ppt ON pt.id = ppt.tag_id
WHERE ppt.post_id = $1
ORDER BY pt.name_en
`

func (q *Queries) GetTagsForPost(ctx context.Context, postID uuid.UUID) ([]PostTag, error) {
	rows, err := q.db.Query(ctx, getTagsForPost, postID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []PostTag{}
	for rows.Next() {
		var i PostTag
		if err := rows.Scan(
			&i.ID,
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.DescriptionEn,
			&i.DescriptionZhHk,
			&i.DescriptionZhCn,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPostTags = `-- name: ListPostTags :many
SELECT id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_at, updated_at FROM post_tags
ORDER BY name_en
`

func (q *Queries) ListPostTags(ctx context.Context) ([]PostTag, error) {
	rows, err := q.db.Query(ctx, listPostTags)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []PostTag{}
	for rows.Next() {
		var i PostTag
		if err := rows.Scan(
			&i.ID,
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.DescriptionEn,
			&i.DescriptionZhHk,
			&i.DescriptionZhCn,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const removeTagFromPost = `-- name: RemoveTagFromPost :exec
DELETE FROM post_post_tags
WHERE post_id = $1 AND tag_id = $2
`

type RemoveTagFromPostParams struct {
	PostID uuid.UUID `db:"post_id" json:"post_id"`
	TagID  uuid.UUID `db:"tag_id" json:"tag_id"`
}

func (q *Queries) RemoveTagFromPost(ctx context.Context, arg RemoveTagFromPostParams) error {
	_, err := q.db.Exec(ctx, removeTagFromPost, arg.PostID, arg.TagID)
	return err
}

const updatePostTag = `-- name: UpdatePostTag :one
UPDATE post_tags
SET
    name_en = $2,
    name_zh_hk = $3,
    name_zh_cn = $4,
    description_en = $5,
    description_zh_hk = $6,
    description_zh_cn = $7,
    updated_at = NOW()
WHERE id = $1
RETURNING id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_at, updated_at
`

type UpdatePostTagParams struct {
	ID              uuid.UUID `db:"id" json:"id"`
	NameEn          string    `db:"name_en" json:"name_en"`
	NameZhHk        string    `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn        string    `db:"name_zh_cn" json:"name_zh_cn"`
	DescriptionEn   *string   `db:"description_en" json:"description_en"`
	DescriptionZhHk *string   `db:"description_zh_hk" json:"description_zh_hk"`
	DescriptionZhCn *string   `db:"description_zh_cn" json:"description_zh_cn"`
}

func (q *Queries) UpdatePostTag(ctx context.Context, arg UpdatePostTagParams) (PostTag, error) {
	row := q.db.QueryRow(ctx, updatePostTag,
		arg.ID,
		arg.NameEn,
		arg.NameZhHk,
		arg.NameZhCn,
		arg.DescriptionEn,
		arg.DescriptionZhHk,
		arg.DescriptionZhCn,
	)
	var i PostTag
	err := row.Scan(
		&i.ID,
		&i.NameEn,
		&i.NameZhHk,
		&i.NameZhCn,
		&i.DescriptionEn,
		&i.DescriptionZhHk,
		&i.DescriptionZhCn,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
