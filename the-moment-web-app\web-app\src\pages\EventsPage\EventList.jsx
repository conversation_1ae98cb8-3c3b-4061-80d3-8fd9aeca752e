import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Row, Col, Pagination, Empty, Spin, Skeleton, App } from 'antd';
import {
    CalendarOutlined,
    EnvironmentOutlined,
    DollarOutlined,
    EyeInvisibleOutlined,
    UsergroupAddOutlined,
    PictureOutlined
} from '@ant-design/icons';
import '../../styles/EventList.css';
import EventFilter from '../../components/EventFilter';
import { formatDate } from '../../utils/dateFormatter';
import { useTranslation } from 'react-i18next';
import { formatNumber } from '../../utils/numberFormatter';
import { eventService } from '../../services/eventService';
import { useAuth } from '../../contexts/AuthContext';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import dayjs from 'dayjs'; // Import dayjs for date formatting to ISO
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

const DEFAULT_TIMEZONE = 'Asia/Hong_Kong';

const EventList = () => {
    const { message } = App.useApp();
    const [events, setEvents] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [filters, setFilters] = useState({
        searchText: '',
        dateRange: [dayjs().startOf('day'), dayjs().add(30, 'day').endOf('day')],
        organizationId: null
    });
    const [loading, setLoading] = useState(true);
    const [imageLoadingStates, setImageLoadingStates] = useState({});
    const [imageErrorStates, setImageErrorStates] = useState({});
    const navigate = useNavigate();
    const { i18n, t } = useTranslation();
    const { currentOrganization } = useOrganization();

    const { user } = useAuth();
    const isAdmin = user?.role === 'admin' || user?.role === 'super_admin';
    const isSuperAdmin = user?.role === 'super_admin';

    const pageSize = 12; // 12 cards per page
    const [isInitialLoad, setIsInitialLoad] = useState(true);
    const currentOrgRef = useRef(null);
    const messageRef = useRef();
    const tRef = useRef();
    
    // Refs for tracking previous filter state to avoid redundant fetches
    const lastFiltersRef = useRef(null);
    const lastPageRef = useRef(null);

    // Store t and message in refs to avoid dependency changes
    useEffect(() => {
        messageRef.current = message;
        tRef.current = t;
    }, [message, t]);

    // Update filters when organization changes
    useEffect(() => {
        // Store current organization in ref to avoid dependency issue
        currentOrgRef.current = currentOrganization;
        
        setFilters(prevFilters => {
            // Don't update if organization ID is the same to prevent unnecessary renders
            if (prevFilters.organizationId === (currentOrganization?.id || ALL_ORGANIZATION_ID)) {
                return prevFilters;
            }
            
            const newFilters = {
                ...prevFilters,
                organizationId: currentOrganization ? currentOrganization.id : ALL_ORGANIZATION_ID,
            };
            return newFilters;
        });
    }, [currentOrganization]);

    // Callback for EventFilter component
    const handleFilterChange = useCallback((newFiltersFromEventFilter) => {
        setFilters(prevFilters => {
            let determinedOrgId;

            // Priority to newFiltersFromEventFilter if it provides a valid organizationId
            if (newFiltersFromEventFilter.hasOwnProperty('organizationId') &&
                newFiltersFromEventFilter.organizationId !== undefined &&
                newFiltersFromEventFilter.organizationId !== null &&
                newFiltersFromEventFilter.organizationId !== '') {
                determinedOrgId = newFiltersFromEventFilter.organizationId;
            } else {
                // Fallback to prevFilters.organizationId (from context or previous state)
                determinedOrgId = prevFilters.organizationId;
            }

            // If still no valid organizationId (e.g., context was null/empty, EventFilter didn't send one or sent empty),
            // then use the global default.
            if (determinedOrgId === undefined || determinedOrgId === null || determinedOrgId === '') {
                determinedOrgId = ALL_ORGANIZATION_ID;
            }

            const updatedFilters = {
                ...prevFilters, // Keep other existing filters from previous state
                ...newFiltersFromEventFilter, // Apply all incoming changes from EventFilter
                organizationId: determinedOrgId, // Explicitly set the determined organizationId
            };
            
            return updatedFilters;
        });
        setCurrentPage(1); // Reset to page 1 when filters change
    }, []); // Empty dependency array is fine here because setFilters updater function gets the latest prevFilters

    const fetchEvents = useCallback(async (page, currentFilters) => {
        setLoading(true);
        try {
            let startDateISO, endDateISO;
            if (currentFilters.dateRange && currentFilters.dateRange[0]) {
                startDateISO = dayjs(currentFilters.dateRange[0]).tz(DEFAULT_TIMEZONE, true).startOf('day').utc().format();
            }
            if (currentFilters.dateRange && currentFilters.dateRange[1]) {
                endDateISO = dayjs(currentFilters.dateRange[1]).tz(DEFAULT_TIMEZONE, true).endOf('day').utc().format();
            }

            const apiParams = {
                limit: pageSize,
                offset: (page - 1) * pageSize,
                ...(currentFilters.searchText && { search_term: currentFilters.searchText }),
                ...(startDateISO && { startDate: startDateISO }),
                ...(endDateISO && { endDate: endDateISO }),
                ...(currentFilters.eventTypes && currentFilters.eventTypes.length > 0 && { tagIds: currentFilters.eventTypes }),
                ...(currentFilters.verificationTypes && currentFilters.verificationTypes.length > 0 && { eventVerificationTypeKeys: currentFilters.verificationTypes }),
                ...(currentFilters.fundingTypes && currentFilters.fundingTypes.length > 0 && { governmentFundingTypeIds: currentFilters.fundingTypes }),
            };

            // Determine status for API call
            if (isAdmin && currentOrgRef.current && currentOrgRef.current.id !== ALL_ORGANIZATION_ID) {
                // Admin viewing a specific org's public page: force 'published'
                apiParams.status = 'published';
            } else if (isAdmin && currentFilters.status) {
                // Admin viewing "All Orgs" or no specific org, respect filter if present
                apiParams.status = currentFilters.status;
            } else if (!isAdmin) {
                // Non-admin always sees published
                apiParams.status = 'published'; // Ensure this is sent to API, or rely on API default
            }

            // The eventService.listPublicEvents is now responsible for adding `org_id` to the API call if `organizationId` is present.
            if (currentFilters.organizationId) {
                if (currentFilters.organizationId === ALL_ORGANIZATION_ID) {
                    if (isSuperAdmin) {
                        // For super_admin, if "All Organizations" is selected, pass the ALL_ORGANIZATION_ID
                        apiParams.org_id = ALL_ORGANIZATION_ID; // Assuming backend expects 'org_id'
                    }
                    // For other roles (or if backend handles missing org_id as 'all'),
                    // do not add org_id, or ensure eventService handles it.
                    // Current logic outside this block handles non-ALL_ORGANIZATION_ID cases.
                } else {
                    // A specific organization is selected
                    apiParams.org_id = currentFilters.organizationId; // Assuming backend expects 'org_id'
                    
                    // For non-super admin users, add org_id_2 to include events from ALL_ORGANIZATION_ID as well
                    if (!isSuperAdmin) {
                        apiParams.org_id2 = ALL_ORGANIZATION_ID;
                    }
                }
            }

            let responseData;

            responseData = await eventService.listPublicEvents(apiParams);

            const fetchedEvents = responseData.events || [];

            if (!Array.isArray(fetchedEvents)) {
                console.error('Error: Fetched events data is not an array:', fetchedEvents);
                messageRef.current.error(tRef.current('messages.fetchError'));
                setEvents([]);
                setTotal(0);
                setLoading(false);
                return;
            }

            // Admin visibility client-side filter might still be needed if API doesn't filter hidden ones for admins
            // const eventsToDisplay = isAdmin
            //     ? fetchedEvents
            //     : fetchedEvents.filter(event => event.status === 'published');
            
            // New display logic: If API was queried with status='published', all fetchedEvents are fine.
            // If admin is viewing a specific org, we already forced 'published'.
            // If admin is viewing "All Orgs", they might see other statuses based on filter, so fetchedEvents is fine.
            // If not admin, API should only return published (or we filter client-side if not).
            let eventsToDisplay = fetchedEvents;
            if (!isAdmin) { // Double-check for non-admins, ensure only published if API didn't strictly filter
                eventsToDisplay = fetchedEvents.filter(event => event.status === 'published');
            } else if (isAdmin && currentOrgRef.current && currentOrgRef.current.id !== ALL_ORGANIZATION_ID) {
                // For admin on specific org's public page, we fetched 'published', so ensure they are all 'published'.
                // This acts as a safeguard if the API somehow returned other statuses despite the 'published' query param.
                eventsToDisplay = fetchedEvents.filter(event => event.status === 'published');
            }

            setEvents(eventsToDisplay);
            setTotal(responseData.total || eventsToDisplay.length);
            setCurrentPage(responseData.currentPage || page);

        } catch (error) {
            messageRef.current.error(tRef.current('messages.fetchError'));
            console.error("Error fetching events:", error);
            setEvents([]);
            setTotal(0);
        } finally {
            setLoading(false);
            if (isInitialLoad) setIsInitialLoad(false);
        }
    }, [isAdmin, isSuperAdmin, isInitialLoad, pageSize]); // Removed message, t from dependencies

    // Effect for handling both page and filter changes
    useEffect(() => {
        // Check if anything has actually changed that would require a new fetch
        const shouldFetch = 
            // First time this effect runs
            lastFiltersRef.current === null || 
            lastPageRef.current === null ||
            // Page changed
            lastPageRef.current !== currentPage ||
            // Some actual filter value changed
            lastFiltersRef.current?.searchText !== filters.searchText ||
            lastFiltersRef.current?.organizationId !== filters.organizationId ||
            // Compare date ranges (only compare if both exist)
            (lastFiltersRef.current?.dateRange && filters.dateRange 
                ? (lastFiltersRef.current.dateRange[0]?.format() !== filters.dateRange[0]?.format() ||
                   lastFiltersRef.current.dateRange[1]?.format() !== filters.dateRange[1]?.format())
                : lastFiltersRef.current?.dateRange !== filters.dateRange) ||
            // Add other filter comparisons as needed
            JSON.stringify(lastFiltersRef.current?.eventTypes) !== JSON.stringify(filters.eventTypes) ||
            JSON.stringify(lastFiltersRef.current?.verificationTypes) !== JSON.stringify(filters.verificationTypes) ||
            JSON.stringify(lastFiltersRef.current?.fundingTypes) !== JSON.stringify(filters.fundingTypes);
        
        if (shouldFetch) {
            // Fetch events when currentPage or filters change,
            // but only if organizationId is set (either from context or default).
            if (filters.organizationId !== undefined && filters.organizationId !== null) {
                fetchEvents(currentPage, filters);
                
                // Update references
                lastFiltersRef.current = {...filters};
                lastPageRef.current = currentPage;
            } 
        }
        // Do not fetch if filters.organizationId is still undefined/null (initial state before context resolves)
    }, [currentPage, filters, fetchEvents]);

    const cardClick = useCallback((eventId) => {
        navigate(`/events/${eventId}`);
    }, [navigate]);

    const handleImageLoad = useCallback((eventId) => {
        setImageLoadingStates(prev => ({
            ...prev,
            [eventId]: false
        }));
    }, []);

    const handleImageError = useCallback((eventId, e) => {
        // Instead of setting fallback image, mark the image as having an error
        setImageErrorStates(prev => ({
            ...prev,
            [eventId]: true
        }));
        setImageLoadingStates(prev => ({
            ...prev,
            [eventId]: false
        }));
    }, []);

    const renderEventImage = (event) => {
        // Initialize loading state for this image if not exists
        if (imageLoadingStates[event.id] === undefined) {
            setImageLoadingStates(prev => ({
                ...prev,
                [event.id]: true
            }));
        }

        // API provides banner_image_urls and media_items. Prioritize banner_image_urls.
        let imageUrl = '';
        if (event.banner_image_urls && event.banner_image_urls.length > 0) {
            imageUrl = event.banner_image_urls[0];
        } else if (event.media_items && event.media_items.length > 0 && event.media_items[0].file_path) {
            // Assuming media_items contains images and the first one can be used as a fallback banner.
            // This might need filtering if media_items can contain non-image types.
            if (event.media_items[0].file_type && event.media_items[0].file_type.startsWith('image/')) {
                imageUrl = event.media_items[0].file_path;
            }
        }

        return (
            <div className="eventList-image-container">
                {imageLoadingStates[event.id] && (
                    <div className="eventList-image-skeleton">
                        <Skeleton.Image active className="event-skeleton" />
                    </div>
                )}
                
                {imageErrorStates[event.id] ? (
                    <div className="h-full w-full flex items-center justify-center bg-gray-200 rounded-t-lg">
                        <div className="text-center">
                            <PictureOutlined className="text-3xl text-gray-400" />
                            <div className="text-gray-500 mt-2">{t('common.noImage')}</div>
                        </div>
                    </div>
                ) : (
                    <img
                        alt={event.title || "Event"}
                        src={imageUrl}
                        className="eventList-image"
                        style={{ display: imageLoadingStates[event.id] ? 'none' : 'block' }}
                        onLoad={() => handleImageLoad(event.id)}
                        onError={(e) => handleImageError(event.id, e)}
                    />
                )}
            </div>
        );
    };

    const renderEventCards = () => {
        if (loading) {
            return (
                <div className="events-loading-container">
                    <Spin size="large" />
                </div>
            );
        }

        if (!events.length) {
            return (
                <div className="events-empty-container">
                    <Empty />
                </div>
            );
        }

        return (
            <>
                <Row gutter={[16, 16]} className="event-cards-row">
                    {events.map((event) => (
                        <Col
                            xs={24} sm={12} md={8} lg={6} xl={6} xxl={4}
                            key={event.id}
                        >
                            <Card
                                hoverable
                                className={`eventList-event-card ${event.status !== 'published' && isAdmin ? 'border-gray-300 bg-gray-100' : 'hover:shadow-lg'}`}
                                cover={
                                    <div className={`relative ${event.status !== 'published' && isAdmin ? 'grayscale' : ''} overflow-hidden rounded-t-lg`}>
                                        {renderEventImage(event)}
                                        {event.status !== 'published' && isAdmin && (
                                            <div className="absolute inset-0 bg-gray-900/50 flex items-center justify-center">
                                                <div className="text-white text-sm flex items-center gap-1.5">
                                                    <EyeInvisibleOutlined />
                                                    <span>{t(`eventStatus.${event.status}`, event.status)}</span>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                }
                                onClick={() => cardClick(event.id)}
                            >
                                <Card.Meta
                                    title={<div className={`text-lg font-bold truncate ${event.status !== 'published' && isAdmin ? 'text-gray-600' : 'text-gray-800'}`}>{event.title}</div>}
                                    description={
                                        <div className={`space-y-2 mt-2 ${event.status !== 'published' && isAdmin ? 'text-gray-500' : 'text-gray-600'}`}> {/* Check status */}
                                            <div className="flex items-center text-sm">
                                                <CalendarOutlined className="mr-2" />
                                                <span className="truncate">{formatDate(event.start_time, i18n.language)}</span> {/* Use start_time */}
                                            </div>
                                            <div className="flex items-center text-sm">
                                                <EnvironmentOutlined className="mr-2" />
                                                <span className="truncate">
                                                    {event.location_type === 'physical' && event.location_full_address}
                                                    {event.location_type === 'online' && t('eventIntroduction.eventInfo.locationType.online')}
                                                    {event.location_type === 'hybrid' && `${event.location_full_address || ''} ${event.location_online_url ? `/ ${t('eventIntroduction.eventInfo.locationType.online')}` : ''}`.trim()}
                                                    {(!event.location_type ||
                                                        (event.location_type === 'physical' && !event.location_full_address) ||
                                                        (event.location_type === 'online' && !event.location_online_url) ||
                                                        (event.location_type === 'hybrid' && !event.location_full_address && !event.location_online_url)
                                                    ) && t('home.tbdLocation')}
                                                </span>
                                            </div>
                                            <div className="flex items-center text-sm">
                                                <DollarOutlined className="mr-2" />
                                                <span className="truncate">
                                                    {/* Price from API is a string, e.g., "$50" or "Free" or numeric string */}
                                                    {(typeof event.price === 'string' && (event.price.toLowerCase() === 'free' || event.price === '0')) || event.price === 0
                                                        ? <span className="bg-green-100 text-green-800 font-medium px-2 py-0.5 rounded-full text-xs">
                                                            {t('common.free')}
                                                        </span>
                                                        : `HKD $${formatNumber(parseFloat(String(event.price).replace(/[^\d.]/g, '')))}`
                                                    }
                                                </span>
                                            </div>

                                            <div className="flex items-center text-sm">
                                                <UsergroupAddOutlined className="mr-2" />
                                                <span className="truncate">
                                                    {formatNumber(event?.registered_count || 0)}
                                                    {event.participant_limit !== undefined ? ` / ${formatNumber(event.participant_limit)}` : ''}
                                                </span>
                                            </div>

                                        </div>
                                    }
                                />
                            </Card>
                        </Col>
                    ))}
                </Row>

                {/* Only show pagination if there are events */}
                {events.length > 0 && (
                    <>
                        <br />
                        <Pagination
                            align="center"
                            current={currentPage}
                            pageSize={pageSize}
                            total={total}
                            showTotal={total => t('common.totalItems', { total: total })}
                            onChange={(page) => setCurrentPage(page)}
                        />
                    </>
                )}
            </>
        );
    };

    return (
        <div className="event-list-page text-[#333] text-base m-0 whitespace-nowrap overflow-hidden text-ellipsis max-w-full px-4 py-6 md:px-6 bg-white">
            {/* Filters */}
            <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
                <div className="w-full md:w-auto flex-grow">
                    <EventFilter
                        onFilter={handleFilterChange}
                        currentOrganization={currentOrganization}
                        showCreateButton={false}
                        initialFilters={filters}
                    />
                </div>
            </div>

            {renderEventCards()}
        </div>
    );
};

export default EventList;
