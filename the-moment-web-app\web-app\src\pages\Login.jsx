import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  message,
  Typography,
  ConfigProvider,
  Checkbox,
  Modal,
  Tabs,
} from 'antd';
import { WhatsAppOutlined, LockOutlined, ArrowLeftOutlined, MailOutlined, UserOutlined, MobileOutlined } from '@ant-design/icons';
import { useTranslation, Trans } from 'react-i18next';
import '../styles/Login.css';

import streetFoodSvg from '../assets/images/login/street_food.svg';
import eventsSvg from '../assets/images/login/events.svg';
import chartsSvg from '../assets/images/login/charts.svg';
import PhoneNumberInput from '../components/PhoneNumberInput';
// import UsernameSetup from '../components/UsernameSetup'; // Assuming UsernameSetup might be part of registration flow
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/authService';
import { profileService } from '../services/profileService'; // Keep for potential profile updates

const { Text } = Typography;
const { TabPane } = Tabs;

// --- PKCE Helper (Simplified and Insecure - For Demo Only) ---
// IMPORTANT: This is NOT a secure way to generate PKCE codes.
// For production, use a proper library or crypto.subtle for SHA256.
const generateRandomString = (length = 43) => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

// In a real app, this would be crypto.subtle.digest('SHA-256', verifier) then base64url
const generateCodeChallenge = async (verifier) => {
  // Placeholder: In a real app, this would be a SHA256 hash then Base64URL encoded.
  // For this demo, returning a modified verifier as a stand-in.
  // This is NOT PKCE compliant.
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(verifier);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    // Base64URL encode (simplified, not handling padding correctly for all cases)
    let binary = '';
    const bytes = new Uint8Array(hashBuffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  } catch (e) {
    console.warn("Crypto API for SHA256 not available or failed, using fallback for challenge (INSECURE):", e);
    return verifier.substring(0, 40); // Highly insecure fallback
  }
};
// --- End PKCE Helper ---

const Login = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [countdown, setCountdown] = useState(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [form] = Form.useForm();
  const [staffForm] = Form.useForm();
  const { login: contextLogin, isAuthenticated } = useAuth(); // Renamed to avoid conflict
  const [isLoading, setIsLoading] = useState(false);
  const [isVerificationLoading, setIsVerificationLoading] = useState(false);
  const { t } = useTranslation();
  const [showUsernameSetup, setShowUsernameSetup] = useState(false);
  const [pendingUserData, setPendingUserData] = useState(null);
  const [activeTab, setActiveTab] = useState('user');
  const [otpChannel, setOtpChannel] = useState('whatsapp'); // 'sms' or 'whatsapp' - temporarily only WhatsApp supported
  const [isNewUser, setIsNewUser] = useState(null); // null, true (new), or false (existing)
  const [showDisplayNameInput, setShowDisplayNameInput] = useState(false);
  const [isVerificationSent, setIsVerificationSent] = useState(false); // Track if verification code was sent

  // PKCE related state
  const [pkceState, setPkceState] = useState(null); // To store { verifier, challenge, state }

  const images = [
    { src: streetFoodSvg, alt: "Street Food" },
    { src: eventsSvg, alt: "Events" },
    { src: chartsSvg, alt: "Charts" }
  ];

  // Get returnTo parameter from URL
  const getReturnUrl = useCallback(() => {
    const searchParams = new URLSearchParams(location.search);
    return searchParams.get('returnTo') || '/';
  }, [location.search]);

  useEffect(() => {
    if (isAuthenticated()) {
        navigate('/');
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    const rotationInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 3000);
    return () => clearInterval(rotationInterval);
  }, [images.length]);

  const preparePkceCodes = async () => {
    const verifier = generateRandomString();
    const challenge = await generateCodeChallenge(verifier);
    const antiCsrfState = generateRandomString(32); // For CSRF protection
    return { verifier, challenge, antiCsrfState };
  };
  
  // Step 1: Send Verification Code (Initiate OTP)
  const sendVerificationCode = async () => {
    try {
      const localPhoneNumber = form.getFieldValue('phoneNumber'); // This is the 8-digit local part
      await form.validateFields(['phoneNumber']); // Validate only phone number initially
      setIsVerificationLoading(true);

      const fullPhoneNumber = `+852${localPhoneNumber}`;
      const clientIdForApi = `852${localPhoneNumber}`;

      // Step 1: Check if phone exists
      const phoneCheckResponse = await authService.checkPhone(fullPhoneNumber);
      const userExists = phoneCheckResponse.exists; // Assuming response.data.exists
      setIsNewUser(!userExists);

      const { verifier, challenge, antiCsrfState } = await preparePkceCodes();
      setPkceState({ verifier, antiCsrfState_sent: antiCsrfState });

      let response;
      if (!userExists) { // New user: initiate registration
        setShowDisplayNameInput(true); // Show display name input for new users
        response = await authService.initiatePhoneRegistration(
          fullPhoneNumber,
          clientIdForApi,
          challenge,
          'S256',
          antiCsrfState,
          otpChannel // Pass selected OTP channel
        );
      } else { // Existing user: initiate login
        setShowDisplayNameInput(false);
        response = await authService.initiatePhoneOTP(
          fullPhoneNumber,
          clientIdForApi,
          challenge,
          'S256',
          antiCsrfState,
          otpChannel // Pass selected OTP channel
        );
      }
      
      setPkceState(prev => ({ ...prev, antiCsrfState_received: response.state }));
      message.success(t('login.messages.codeSent'));
      
      setIsVerificationSent(true); // Mark verification code as sent
      
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error("Error sending verification code:", error);
      message.error(t('login.messages.sendCodeError') || error.message);
    } finally {
      setIsVerificationLoading(false);
    }
  };

  // Step 2: Handle Login (Verify OTP)
  const handleLogin = async (values) => {
    setIsLoading(true);
    try {
      const { phoneNumber: localPhoneNumber, verificationCode: otp, agreement, displayName } = values;
      
      if (!agreement) {
        message.error(t('login.messages.agreementRequired'));
        setIsLoading(false);
        return;
      }

      if (!pkceState || !pkceState.verifier || !pkceState.antiCsrfState_received || !pkceState.antiCsrfState_sent) {
        message.error(t('login.errors.pkceStateMissing'));
        setIsLoading(false);
        return;
      }
      // Basic CSRF check
      if (pkceState.antiCsrfState_sent !== pkceState.antiCsrfState_received) {
          message.error(t('login.errors.csrfMismatch'));
          setIsLoading(false);
          return;
      }

      // TODO: Determine if it was login or registration verify call needed
      // Assuming login:
      let response;
      if (isNewUser === true) { // New user registration
        if (!displayName || displayName.trim() === '') {
          message.error(t('login.form.validation.displayNameRequired'));
          setIsLoading(false);
          return;
        }
        response = await authService.verifyPhoneRegistration(
          pkceState.antiCsrfState_received,
          otp,
          pkceState.verifier,
          displayName,
          // interfaceLanguage and communicationLanguage can be added if needed
        );
      } else if (isNewUser === false) { // Existing user login
        response = await authService.verifyPhoneOTP(
          pkceState.antiCsrfState_received, // The state returned by initiate endpoint
          otp,
          pkceState.verifier
        );
      } else {
        // Should not happen if sendVerificationCode was successful
        message.error(t('login.errors.userStatusUnknown'));
        setIsLoading(false);
        return;
      }
      
      // Tokens are handled by authService internally. AuthContext needs the userId.
      // For registration, response.user.id can be used. For login, response.userId.
      const userIdToLogin = isNewUser ? response.user.id : response.userId;

      if (isNewUser === true && displayName && displayName.trim() !== '') {
        try {
          // Tokens are set by authService.verifyPhoneRegistration, so updateUserProfile should work.
          // response.user from verifyPhoneRegistration contains the profile *before* this explicit update.
          await profileService.updateUserProfile({ display_name: displayName });
          // AuthContext will fetch the latest profile via contextLogin's call to fetchUserProfile.
          message.info(t('login.messages.displayNameUpdated')); // Optional: inform user
        } catch (updateError) {
          console.error("Failed to explicitly update display name:", updateError);
          // Log the error, but proceed with login as registration itself was successful.
          // A more prominent error could be shown if this update is critical.
          message.warning(t('login.messages.displayNameUpdateFailed', 'Display name updated, but a final sync may take a moment.'));
        }
      }

      await contextLogin(userIdToLogin);
      message.success((isNewUser ? t('login.messages.registrationSuccess') : t('login.messages.loginSuccess')) || response.message);
      navigate(getReturnUrl());

    } catch (error) {
      console.error("Error during login:", error);
      message.error(t('login.messages.loginError') || error.response?.data?.message || error.message);
    } finally {
      setIsLoading(false);
      setPkceState(null); // Clear PKCE state after use
    }
  };

  const handleStaffLogin = async (values) => {
    setIsLoading(true);
    const { email, password } = values;
    const clientIdForStaff = email;

    try {
      // 1. Check staff email
      const emailCheckResponse = await authService.checkStaffEmail(email);
      // Access actual data using .data property
      if (!emailCheckResponse.exists) { 
        message.error(t('login.errors.staffNotFound', 'Staff email not found.'));
        setIsLoading(false);
        return;
      }
      // Access actual data using .data property
      if (!emailCheckResponse.is_staff) { 
        message.error(t('login.errors.userNotStaff', 'This email does not belong to a staff account.'));
        setIsLoading(false);
        return;
      }

      // 2. Prepare PKCE codes and client-generated CSRF state token
      const { verifier, challenge, antiCsrfState: clientGeneratedCsrfToken } = await preparePkceCodes();

      // 3. Initiate staff login
      const initiateResponse = await authService.initiateStaffLogin(
        email,
        clientIdForStaff,
        challenge,
        'S256',
        clientGeneratedCsrfToken
      ); 
      // CORRECTED: Access actual data using .data property for the state from the server
      const flowStateFromServer = initiateResponse.state; 

      // 4. Verify staff login
      const verifyResponse = await authService.verifyStaffLogin(
        flowStateFromServer,
        email,
        password,
        verifier
      );

      // authService.verifyStaffLogin internally handles token storage and fetches the user profile.
      // CORRECTED: Access actual data using .data property
      await contextLogin(verifyResponse.user.id); 
      message.success(t('login.messages.staffLoginSuccess', 'Staff login successful!') || verifyResponse.message); 
      navigate(getReturnUrl());

    } catch (error) {
      console.error("Error during staff login:", error);
      const errorMessage = t('login.errors.staffLoginError', 'Staff login failed. Please try again.') || error.response?.data?.message || error.message;
      message.error(errorMessage);
    } finally {
      setIsLoading(false);
      // No pkceState to clear here as we used local consts for verifier & csrf
    }
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
  };
  
  // Temporarily disabled SMS login - only WhatsApp supported
  // const handleOtpChannelChange = (checked) => {
  //   setOtpChannel(checked ? 'whatsapp' : 'sms');
  // };

  const handleSkipUsername = async () => {
    // If registration was successful but username setup skipped
    // await contextLogin(pendingUserData.id); // pendingUserData would have userId
    message.success(t('login.messages.registrationSuccess'));
    navigate(getReturnUrl());
  };

  // Temporarily disabled SMS login - only WhatsApp supported
  // Custom WhatsApp Switch component 
  // const renderWhatsAppSwitch = () => {
  //   const checked = otpChannel === 'whatsapp';
  //   return (
  //     <div className="whatsapp-switch-wrapper">
  //       <span className="whatsapp-switch-label">
  //         {t('login.form.options.useWhatsApp')}
  //       </span>
  //       <button
  //         type="button"
  //         role="switch"
  //         aria-checked={checked}
  //         onClick={() => handleOtpChannelChange(!checked)}
  //         className={`custom-switch ${checked ? 'switch-checked' : ''}`}
  //       >
  //         <span className="switch-handle">
  //           <span className={`switch-icon-off ${!checked ? 'active' : ''}`}>
  //             <MobileOutlined />
  //           </span>
  //           <span className={`switch-icon-on ${checked ? 'active' : ''}`}>
  //             <WhatsAppOutlined />
  //           </span>
  //         </span>
  //       </button>
  //     </div>
  //   );
  // };

  return (
    <div className="login-container">
      <Button
        className="back-home-button"
        icon={<ArrowLeftOutlined />}
        onClick={() => navigate('/')}
      >
        {t('login.buttons.backHome')}
      </Button>
      <div className="login-left">
        <div className="brand-content">
          <div className="image-carousel-container">
            {images.map((image, index) => (
              <img
                key={index}
                src={image.src}
                alt={image.alt}
                className={`brand-image ${index === currentImageIndex ? 'active' : ''}`}
                style={{
                  opacity: index === currentImageIndex ? 1 : 0,
                  position: index === currentImageIndex ? 'relative' : 'absolute'
                }}
              />
            ))}
          </div>
          <h1>{t('login.welcome')}</h1>
          <p className="brand-description">
            {t('login.description')}
          </p>
        </div>
      </div>

      <div className="login-right">
        <div className="login-form-wrapper">
          {/* UsernameSetup Modal logic would need to be re-evaluated based on chosen registration flow */}
          <div 
            className={`transition-all duration-500 ease-in-out transform absolute w-[420px]
              ${showUsernameSetup ? 'opacity-0 -translate-y-full pointer-events-none' : 'opacity-100 translate-y-0'}`}
          >
            <Tabs 
              activeKey={activeTab} 
              onChange={handleTabChange}
              centered
              className="mb-6"
            >
              <TabPane tab={<span className="px-4 py-2">{t('login.form.labels.userLogin')}</span>} key="user">
                <Form
                  form={form}
                  name="normal_login"
                  className="login-form"
                  initialValues={{
                    remember: true,
                    agreement: true,
                  }}
                  onFinish={handleLogin}
                  layout="vertical"
                >
                  <Text strong style={{ marginBottom: 10, display: 'block' }}>
                    {t('login.form.labels.phoneNumber')}
                  </Text>
                  <Form.Item
                    name="phoneNumber"
                    rules={[
                      { required: true, message: t('login.form.validation.phoneRequired') },
                      {
                        pattern: /^[0-9]{8}$/,
                        message: t('login.form.validation.phoneFormat'),
                      },
                    ]}
                  >
                    <PhoneNumberInput />
                  </Form.Item>
                  <Text type="secondary" style={{ marginTop: -20, marginBottom: 16, display: 'block', fontSize: '12px' }}>
                    {t('login.form.hints.autoRegister', '未註冊的用戶將自動進行註冊')}
                  </Text>

                  <Text strong style={{ marginBottom: 10, display: 'block' }}>
                    {t('login.form.labels.verificationCode')}
                  </Text>
                  <Form.Item
                    name="verificationCode"
                    rules={[
                      { required: true, message: t('login.form.validation.codeRequired') },
                      { len: 6, message: t('login.form.validation.verificationCodeLength') }
                    ]}
                  >
                    <div className="verification-code-container">
                      <Input
                        size="large"
                        prefix={<WhatsAppOutlined />}
                        placeholder={t('login.form.placeholders.verificationCode')}
                        maxLength={6}
                      />
                      <Button
                        size="large"
                        type="primary"
                        onClick={sendVerificationCode}
                        disabled={countdown > 0}
                        loading={isVerificationLoading}
                      >
                        {countdown > 0 ? t('login.buttons.resend', { seconds: countdown }) : t('login.buttons.sendCode')}
                      </Button>
                    </div>
                  </Form.Item>

                  {showDisplayNameInput && isNewUser && (
                    <>
                      <Text strong style={{ marginBottom: 10, display: 'block' }}>
                        {t('login.form.labels.displayName')}
                      </Text>
                      <Form.Item
                        name="displayName"
                        rules={[{ required: true, message: t('login.form.validation.displayNameRequired') }]}
                      >
                        <Input
                          size="large"
                          prefix={<UserOutlined className="site-form-item-icon" />}
                          placeholder={t('login.form.placeholders.displayName')}
                        />
                      </Form.Item>
                    </>
                  )}

                  <div className="login-bottom-actions">
                    {/* Account recovery temporarily disabled
                    <Button
                      type="text"
                      className="account-recovery-link"
                      onClick={() => navigate('/account-recovery')}
                    >
                      {t('login.buttons.accountRecovery')}
                    </Button>
                    */}
                    
                    {/* {renderWhatsAppSwitch()} */}
                  </div>

                  {/* <Form.Item> */}
                    {/* Account recovery temporarily disabled
                    <Button
                      type="text"
                      className="account-recovery-link"
                      onClick={() => navigate('/account-recovery')}
                    >
                      {t('login.buttons.accountRecovery')}
                    </Button>
                    */}
                  {/* </Form.Item> */}

                  <Form.Item>
                    <Button
                      size="large"
                      type="primary"
                      htmlType="submit"
                      className="login-form-button"
                      style={{ width: '100%' }}
                      loading={isLoading}
                      disabled={!isVerificationSent} // Disable button if verification code wasn't sent
                    >
                      {t('login.buttons.continue')}
                    </Button>
                  </Form.Item>
                  
                  <Form.Item
                    name="agreement"
                    valuePropName="checked"
                    rules={[
                      {
                        validator: (_, value) =>
                          value
                            ? Promise.resolve()
                            : Promise.reject(t('register.form.validation.agreementRequired')),
                      },
                    ]}
                  >
                    <Checkbox>
                      <Trans
                        i18nKey="register.agreement.text"
                        components={{
                          terms: <Link to="/user-agreement/terms" target="_blank" rel="noopener noreferrer" className="text-blue-500 text-decoration-line: underline" />,
                          privacy: <Link to="/user-agreement/privacy" target="_blank" rel="noopener noreferrer" className="text-blue-500 text-decoration-line: underline" />
                        }}
                      />
                    </Checkbox>
                  </Form.Item>
                </Form>
              </TabPane>
              
              <TabPane tab={<span className="px-4 py-2">{t('login.form.labels.staffLogin')}</span>} key="staff">
                <Form
                  form={staffForm}
                  name="staff_login"
                  className="login-form"
                  onFinish={handleStaffLogin}
                  layout="vertical"
                >
                  <Text strong style={{ marginBottom: 10, display: 'block' }}>
                    {t('login.form.labels.email')}
                  </Text>
                  <Form.Item
                    name="email"
                    rules={[
                      { required: true, message: t('login.form.validation.emailRequired') },
                      { type: 'email', message: t('login.form.validation.emailFormat') },
                    ]}
                  >
                    <Input 
                      size="large"
                      prefix={<MailOutlined />}
                      placeholder={t('login.form.placeholders.email')}
                    />
                  </Form.Item>
                  
                  <Text strong style={{ marginBottom: 10, display: 'block' }}>
                    {t('login.form.labels.password')}
                  </Text>
                  <Form.Item
                    name="password"
                    rules={[
                      { required: true, message: t('login.form.validation.passwordRequired') },
                    ]}
                  >
                    <Input.Password
                      size="large"
                      prefix={<LockOutlined />}
                      placeholder={t('login.form.placeholders.password')}
                    />
                  </Form.Item>
                  
                  {/* <Form.Item> */}
                    {/* Account recovery temporarily disabled
                    <Button
                      type="text"
                      className="account-recovery-link"
                      onClick={() => navigate('/account-recovery')}
                    >
                      {t('login.buttons.accountRecovery')}
                    </Button>
                    */}
                  {/* </Form.Item> */}
                  
                  <Form.Item>
                    <Button
                      size="large"
                      type="primary"
                      htmlType="submit"
                      className="login-form-button"
                      style={{ width: '100%' }}
                      loading={isLoading}
                    >
                      {t('login.buttons.staffLogin')}
                    </Button>
                  </Form.Item>
                  
                  <Form.Item
                    name="agreement"
                    valuePropName="checked"
                    initialValue={true}
                    rules={[
                      {
                        validator: (_, value) =>
                          value
                            ? Promise.resolve()
                            : Promise.reject(t('register.form.validation.agreementRequired')),
                      },
                    ]}
                  >
                    <Checkbox>
                      <Trans
                        i18nKey="register.agreement.text"
                        components={{
                          terms: <Link to="/user-agreement/terms" target="_blank" rel="noopener noreferrer" className="text-blue-500 text-decoration-line: underline" />,
                          privacy: <Link to="/user-agreement/privacy" target="_blank" rel="noopener noreferrer" className="text-blue-500 text-decoration-line: underline" />
                        }}
                      />
                    </Checkbox>
                  </Form.Item>
                </Form>
              </TabPane>
            </Tabs>
          </div>

          <div 
            className={`transition-all duration-500 ease-in-out transform absolute w-[420px]
              ${showUsernameSetup ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-full pointer-events-none'}`}
          >
            {/* If you need UsernameSetup, uncomment and modify this section */}
            {/* <UsernameSetup
              onSubmit={handleUsernameSubmit}
              onSkip={handleSkipUsername}
              isLoading={isLoading}
            /> */}
            
            {/* Simple username setup form as placeholder */}
            <div className="username-setup">
              <h2>{t('login.usernameModal.title', 'Set Your Display Name')}</h2>
              <Form
                onFinish={handleSkipUsername}
                layout="vertical"
              >
                <Form.Item
                  name="username"
                  label={t('login.form.labels.displayName')}
                  rules={[{ required: true, message: t('login.form.validation.displayNameRequired') }]}
                >
                  <Input placeholder={t('login.form.placeholders.displayName')} />
                </Form.Item>
                
                <Form.Item>
                  <Button type="primary" htmlType="submit" loading={isLoading}>
                    {t('login.buttons.continue')}
                  </Button>
                  <Button 
                    type="link" 
                    onClick={handleSkipUsername} 
                    disabled={isLoading}
                  >
                    {t('login.buttons.skip', 'Skip')}
                  </Button>
                </Form.Item>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
