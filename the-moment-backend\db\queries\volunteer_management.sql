-- name: CreateVolunteerApplication :one
INSERT INTO user_volunteer_applications (
    user_id,
    organization_id,
    motivation
) VALUES (
    $1, $2, $3
)
RETURNING *;

-- name: GetUserVolunteerApplicationForOrganization :one
SELECT * FROM user_volunteer_applications
WHERE user_id = $1 AND organization_id = $2
ORDER BY application_date DESC
LIMIT 1;

-- name: GetUserVolunteerApplicationByID :one
SELECT * FROM user_volunteer_applications
WHERE id = $1 AND user_id = $2;

-- name: ListUserVolunteerApplications :many
SELECT * FROM user_volunteer_applications
WHERE user_id = $1
AND (sqlc.narg('status')::application_status_enum IS NULL OR status = sqlc.narg('status'))
ORDER BY application_date DESC;

-- name: ListPendingVolunteerApplicationsForOrganization :many
SELECT 
    app.*,
    u.display_name as applicant_display_name,
    u.email as applicant_email,
    u.phone as applicant_phone_number
FROM user_volunteer_applications app
JOIN users u ON app.user_id = u.id
WHERE app.organization_id = $1 AND app.status = 'pending'
ORDER BY app.application_date ASC;

-- name: GetVolunteerApplicationDetailsForAdmin :one
SELECT 
    app.*,
    u.display_name as applicant_display_name,
    u.email as applicant_email,
    u.phone as applicant_phone_number,
    reviewer.display_name as reviewer_display_name
FROM user_volunteer_applications app
JOIN users u ON app.user_id = u.id
LEFT JOIN users reviewer ON app.reviewed_by_user_id = reviewer.id
WHERE app.id = $1 AND app.organization_id = $2;

-- name: UpdateVolunteerApplicationStatus :one
UPDATE user_volunteer_applications
SET 
    status = $1,
    reviewed_by_user_id = $2,
    review_date = NOW(),
    admin_notes = $3,
    updated_at = NOW()
WHERE id = $4 AND organization_id = $5
RETURNING *;

-- name: ListOrgVolunteerApplicationsWithFilters :many
SELECT
    app.*,
    u.display_name as applicant_display_name,
    u.email as applicant_email,
    u.phone as applicant_phone_number,
    reviewer.display_name as reviewer_display_name
FROM user_volunteer_applications app
JOIN users u ON app.user_id = u.id
LEFT JOIN users reviewer ON app.reviewed_by_user_id = reviewer.id
WHERE app.organization_id = sqlc.arg(organization_id)
AND (sqlc.narg('status')::varchar IS NULL OR app.status::text = sqlc.narg('status')::varchar)
ORDER BY app.application_date DESC
LIMIT sqlc.arg('limit_val')
OFFSET sqlc.arg('offset_val');

-- name: CountOrgVolunteerApplicationsWithFilters :one
SELECT COUNT(*)
FROM user_volunteer_applications app
WHERE app.organization_id = sqlc.arg(organization_id)
AND (sqlc.narg('status')::varchar IS NULL OR app.status::text = sqlc.narg('status')::varchar);

-- name: SetUserVolunteerApplicationStatusToWithdrawnByUser :one
UPDATE user_volunteer_applications
SET
    status = 'withdrawn', -- Directly set to withdrawn
    updated_at = NOW(),
    -- Clear review fields if withdrawal means it's no longer under review
    reviewed_by_user_id = NULL,
    review_date = NULL,
    admin_notes = NULL
WHERE id = $1 AND user_id = $2 AND status IN ('pending', 'approved') -- Define withdrawable states
RETURNING *;

-- name: GetApprovedEventVolunteerApplication :one
SELECT *
FROM event_volunteer_applications
WHERE user_id = @user_id AND event_id = @event_id AND status = 'approved'
LIMIT 1;

-- name: MarkEventVolunteerAttended :exec
UPDATE event_volunteer_applications
SET attended_at = NOW(), updated_at = NOW()
WHERE user_id = @user_id AND event_id = @event_id AND status = 'approved';

-- name: GetEventVolunteerApplicationDetailsByID :one
SELECT * FROM event_volunteer_applications
WHERE id = $1;
