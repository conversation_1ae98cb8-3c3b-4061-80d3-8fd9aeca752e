CREATE TYPE job_status AS ENUM (
    'pending',
    'running',
    'completed',
    'failed'
);

CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_type VARCHAR(255) NOT NULL,
    payload JSONB,
    status job_status NOT NULL DEFAULT 'pending',
    max_retries INT NOT NULL DEFAULT 3,
    attempts INT NOT NULL DEFAULT 0,
    last_error TEXT,
    run_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_jobs_status_run_at ON jobs (status, run_at);

COMMENT ON COLUMN jobs.job_type IS 'Type of the job, e.g., promote_waitlist, send_email_notification';
COMMENT ON COLUMN jobs.payload IS 'JSON payload with job-specific parameters';
COMMENT ON COLUMN jobs.status IS 'Current status of the job';
COMMENT ON COLUMN jobs.max_retries IS 'Maximum number of times this job will be retried if it fails';
COMMENT ON COLUMN jobs.attempts IS 'Number of times this job has been attempted';
COMMENT ON COLUMN jobs.last_error IS 'Error message from the last failed attempt';
COMMENT ON COLUMN jobs.run_at IS 'Timestamp when the job is scheduled to run (or next retry)'; 