import React from 'react';
import {
    View,
    StyleSheet,
    ScrollView,
    Image,
    Dimensions,
    TouchableOpacity,
    Share,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { appStyleStore } from 'stores/app_style_store';
import { generateShareContent } from 'utils/shareUtils';
// import { EventParticipation } from '@/api/myEvents'; // Commented out
import { ErrorView } from '@/common_modules/ErrorView';
import { MEDIA_BASE_URL, type EventRegistrationPayload } from '@/api/api_config';

const getValidImageUrl = (filePath: string | undefined): string | undefined => {
    if (!filePath) return undefined;
    try {
        const url = new URL(filePath, MEDIA_BASE_URL);
        return new URL(url.pathname, MEDIA_BASE_URL).toString();
    } catch (error) {
        console.warn('Error processing image URL in PastEventManagementScreen:', filePath, error);
        return undefined;
    }
};

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export const PastEventManagementScreen: React.FC = () => {
    const { t, i18n } = useTranslation();
    const router = useRouter();
    const params = useLocalSearchParams<{ event: string }>();
    const theme = appStyleStore(state => state.theme );
    const styles = getStyles(theme);

    let event: EventRegistrationPayload | undefined = undefined; // Changed type from any
    try {
        if (params.event) {
            event = JSON.parse(params.event) as EventRegistrationPayload; // Added type assertion
        }
    } catch (e) {
        console.error("Failed to parse event param:", e);
        // ErrorView will be shown if event remains undefined
    }

    const getRoleColor = (role: 'participant' | 'volunteer') => {
        return role === 'participant' ? theme.system.participant : theme.system.volunteer;
    };

    const handleShare = async () => {
        if (!event) return;
        try {
            // Assuming generateShareContent expects a compatible structure.
            // If generateShareContent needs a specific type, event might need mapping.
            const shareEventData = {
                id: event.event_id, // Adjusted to use event_id from EventRegistrationPayload
                title: event.event_title,
                date: event.event_start_time,
                location: event.event_location_full_address || event.event_location_online_url || '',
                type: event.registration_role, // registration_role should fit 'type'
                participantsCount: event.registered_count || 0, // Ensure this field exists or use appropriate
            };
            const shareContent = generateShareContent({
                event: shareEventData,
                t,
                language: i18n.language
            });
            await Share.share(shareContent);
        } catch (error) {
            console.error('Share error:', error);
        }
    };

    if (!event) {
        return (
            <>
                <Stack.Screen
                    options={{
                        title: t('eventManagement.title'),
                    }}
                />
                <ErrorView
                    onGoBack={() => router.back()}
                />
            </>

        );
    }

    const handleViewEventDetail = () => {
        if (event) {
            router.push({
                pathname: '/explore/cardDetails/EventDetailsScreen',
                params: { eventId: event.event_id, organizationId: event.event_organization_id } // Pass event_id and organization_id
            });
        }
    };

    return (
        <>
            <Stack.Screen
                options={{
                    title: t('eventManagement.title'),
                    headerTitleAlign: 'center',
                    headerShadowVisible: false,
                    headerRight: event ? () => (
                        <TouchableOpacity
                            style={styles.headerButtonContainer}
                            onPress={handleShare}
                        >
                            <MaterialCommunityIcons
                                name="share-variant"
                                size={24}
                                color={theme.system.text}
                            />
                        </TouchableOpacity>
                    ) : undefined,
                }}
            />
            <ScrollView style={styles.container} bounces={false} showsVerticalScrollIndicator={false}>
                {/* Cover Image */}
                <View style={styles.imageContainer}>
                    {event && event.media_items && event.media_items.length > 0 && event.media_items[0].file_path ? ( // Adjusted to use media_items
                        <Image
                            source={{ uri: getValidImageUrl(event.media_items[0].file_path) }} // Adjusted to use media_items
                            style={styles.coverImage}
                            onError={(e) => console.log("Failed to load image:", e.nativeEvent.error)}
                        />
                    ) : (
                        <View style={styles.noImagePlaceholder}>
                            <MaterialCommunityIcons name="image-off" size={50} color={theme.system.secondaryText} />
                        </View>
                    )}
                </View>

                <View style={styles.content}>
                    {/* Header Section (Title, Tags) */}
                    {/* ... (rest of JSX, ensure styles are used) ... */}
                </View>
            </ScrollView>
        </>
    );
};

const getStyles = (theme: any) => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    imageContainer: {
        height: SCREEN_HEIGHT * 0.35,
        width: SCREEN_WIDTH,
        backgroundColor: theme.colors.surfaceVariant,
        justifyContent: 'center',
        alignItems: 'center',
    },
    coverImage: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
    },
    content: {
        flex: 1,
        backgroundColor: theme.colors.background,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        marginTop: -24,
        paddingTop: 24,
        paddingHorizontal: 20,
        paddingBottom: 32,
    },
    headerSection: {
        marginBottom: 24,
    },
    tagContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
        flexWrap: 'wrap',
    },
    roleTag: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        backgroundColor: theme.colors.surfaceVariant,
        paddingHorizontal: 12,
        paddingVertical: 4,
        borderRadius: 16,
        gap: 4,
    },
    roleText: {
        fontSize: 14,
        fontWeight: '500',
        // color is dynamic
    },
    statusTag: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        paddingHorizontal: 12,
        paddingVertical: 4,
        borderRadius: 16,
        gap: 4,
        // backgroundColor is dynamic
    },
    statusText: {
        fontSize: 14,
        fontWeight: '500',
        // color is dynamic
    },
    title: {
        fontSize: 24,
        fontWeight: '700',
        color: theme.system.text,
        marginBottom: 12,
    },
    infoSection: {
        marginBottom: 24,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 12,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: theme.system.text,
    },
    infoCard: {
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
    },
    infoContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    infoTextContainer: {
        marginLeft: 12,
        flex: 1,
    },
    infoText: {
        fontSize: 15,
        color: theme.system.text,
    },
    headerButtonContainer: {
        paddingHorizontal: 16,
    },
    viewDetailButton: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 'auto',
    },
    viewDetailText: {
        fontSize: 14,
        fontWeight: '500',
    },
    noImagePlaceholder: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.surfaceVariant,
    }
    // ... Add other styles from original file, adapting for theme
});

export default PastEventManagementScreen;
