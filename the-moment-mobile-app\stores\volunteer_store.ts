import { create } from 'zustand';
import { AxiosError } from 'axios';
import type { EventVolunteerApplicationPayload } from '@/api/api_config';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

// State for User's Event Volunteer Applications List
interface UserEventVolunteerApplicationsListState {
    applicationsList: EventVolunteerApplicationPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setApplicationsList: (volunteers: EventVolunteerApplicationPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userEventVolunteerApplicationsListStore = create<UserEventVolunteerApplicationsListState>()(persist<UserEventVolunteerApplicationsListState>(
    (set) => ({
        applicationsList: [],
        isFetching: false,
        error: null,
        setApplicationsList: (volunteers: EventVolunteerApplicationPayload[]) => set({ applicationsList: volunteers, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-event-volunteer-applications-list-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

// State for User's Event Volunteer Application Details
interface UserEventVolunteerApplicationDetailsState {
    applicationDetails: EventVolunteerApplicationPayload | null;
    isFetching: boolean;
    error: AxiosError | null;
    setApplicationDetails: (volunteer: EventVolunteerApplicationPayload) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userEventVolunteerApplicationDetailsStore = create<UserEventVolunteerApplicationDetailsState>()(persist<UserEventVolunteerApplicationDetailsState>(
    (set) => ({
        applicationDetails: null,
        isFetching: false,
        error: null,
        setApplicationDetails: (volunteer: EventVolunteerApplicationPayload) => set({ applicationDetails: volunteer, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-event-volunteer-application-details-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));
