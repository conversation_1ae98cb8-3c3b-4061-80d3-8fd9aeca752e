package authn

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/twilio_service"
	"Membership-SAAS-System-Backend/internal/utils"

	"github.com/google/uuid" // For DefaultOrgOwnerID
	"github.com/rs/zerolog/log"

	// "github.com/rs/zerolog/log" // No longer needed here for fatal log on secret
	"context"
	"errors"
	"fmt"
	"mime/multipart"
	"time"

	"Membership-SAAS-System-Backend/internal/token"

	"github.com/jackc/pgx/v5"
)

// OrganizationManager defines the interface AuthnService needs for organization operations.
// This helps break the import cycle with the services package.
type OrganizationManager interface {
	AddUserToDefaultOrganizationAndGrantRole(ctx context.Context, userID uuid.UUID, role string) error
	// Methods needed by authn/organization_handlers.go:
	CreateOrganization(ctx context.Context, creatorUserID uuid.UUID, input payloads.CreateOrganizationRequest) (db.Organization, error)
	ListOrganizations(ctx context.Context, limit int, offset int) ([]db.Organization, int64, error)
	GetOrganizationByID(ctx context.Context, orgID uuid.UUID) (db.Organization, error)
	CheckUserOrganizationRole(ctx context.Context, opts payloads.CheckUserOrganizationRoleOptions) (bool, error)
	UpdateOrganization(ctx context.Context, orgID uuid.UUID, input payloads.UpdateOrganizationRequest) (db.Organization, error)
	DeleteOrganization(ctx context.Context, orgID uuid.UUID) error
	ListUserOrganizations(ctx context.Context, userID uuid.UUID) ([]db.Organization, error)
	JoinOrganization(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) (db.UserOrganizationMembership, error)
	LeaveOrganization(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) error
	UploadOrganizationLogo(ctx context.Context, orgID uuid.UUID, actingUserID uuid.UUID, file *multipart.FileHeader) (db.Organization, error)
}

// AuthFlowType represents the type of authentication flow.
type AuthFlowType string

const (
	AuthFlowTypePhoneOTP          AuthFlowType = "phone_otp"
	AuthFlowTypeEmailPassword     AuthFlowType = "email_password"
	AuthFlowTypePhoneRegistration AuthFlowType = "phone_registration"
	AuthFlowTypePhoneChangeVerify AuthFlowType = "PHONE_CHANGE_VERIFY" // Matches the DB CHECK constraint
	AuthFlowTypeReAuthVerify      AuthFlowType = "re_auth_verify"
)

// AuthnService holds dependencies for authentication handlers.
type AuthnService struct {
	Queries           *db.Queries
	Twilio            *twilio_service.TwilioService
	OrgManager        OrganizationManager // Changed from *services.OrganizationService
	DefaultOrgOwnerID uuid.UUID           // ID of the user designated to own the default org
}

// NewAuthnService creates a new AuthnService.
func NewAuthnService(queries *db.Queries, twilio *twilio_service.TwilioService, orgManager OrganizationManager, defaultOrgOwnerID uuid.UUID) *AuthnService {
	// refreshTokenSecret is no longer passed or checked here.
	// Handlers that require it (e.g., RefreshTokenHandler) will get it from token.GetJWTConfig().
	return &AuthnService{
		Queries:           queries,
		Twilio:            twilio,
		OrgManager:        orgManager, // Store the interface
		DefaultOrgOwnerID: defaultOrgOwnerID,
	}
}

func (s *AuthnService) InitiatePhoneChangeOTP(ctx context.Context, userID uuid.UUID, newPhone, otpChannel, clientID, redirectURI string) (state string, flowID uuid.UUID, err error) {
	logger := log.Ctx(ctx) // Use logger instead of log for contextual logging

	// 1. Generate state
	state = utils.GenerateRandomState(32)

	// 2. Generate internal PKCE (as auth_flows requires code_challenge)
	// For this internal flow, the verifier isn't strictly needed for client-side verification
	// but we need a challenge for the DB.
	internalCodeVerifier := utils.GenerateRandomState(64) // Example length
	internalCodeChallenge := utils.GenerateS256Challenge(internalCodeVerifier)

	// 3. Create Auth Flow
	flowExpiresAt := time.Now().Add(10 * time.Minute) // OTP/flow valid for 10 minutes

	// Use a default clientID and redirectURI if not provided or make them parameters
	// For now, let's assume they are passed in or have sensible defaults.
	// If clientID and redirectURI are specific to this flow:
	// clientID := "internal-phone-change-client"
	// redirectURI := "app://internal/phone-change-verify"

	authFlowParams := db.CreateAuthFlowParams{
		FlowType:            string(AuthFlowTypePhoneChangeVerify),
		CodeChallenge:       internalCodeChallenge,
		CodeChallengeMethod: "S256",
		State:               state,
		ClientID:            clientID,    // Placeholder or specific internal client ID
		RedirectUri:         redirectURI, // Placeholder or specific internal redirect URI
		Phone:               &newPhone,
		Email:               nil,
		ExpiresAt:           flowExpiresAt,
		CodeVerifier:        nil, // Will be nil until token exchange (though we won't use it in this flow)
		// UserID: &userID, // Would add this if schema changes
	}

	createdFlow, err := s.Queries.CreateAuthFlow(ctx, authFlowParams)
	if err != nil {
		logger.Error().Err(err).Str("new_phone", newPhone).Msg("Error creating auth flow for phone change")
		return "", uuid.Nil, fmt.Errorf("failed to initiate phone change flow: %w", err)
	}
	flowID = createdFlow.ID
	logger.Info().Str("new_phone", newPhone).Str("flow_id", flowID.String()).Str("user_id", userID.String()).Msg("Created auth flow for phone change. Attempting to send OTP.")

	// 4. Send OTP
	// Determine OTP channel if not provided by user, or use a default
	if otpChannel == "" {
		// Check user's current preference or default
		// For a new phone, we might just default to "sms" or "whatsapp"
		// For now, assume otpChannel is provided or UserService determines a default
		otpChannel = "sms" // Fallback, should be decided by UserService or config
	}

	otpSid, err := s.Twilio.SendOTP(newPhone, otpChannel)
	if err != nil {
		logger.Error().Err(err).Str("new_phone", newPhone).Str("flow_id", flowID.String()).Msg("Error sending OTP via Twilio for phone change")
		// Consider cleanup of createdFlow if OTP send fails critically?
		return "", flowID, fmt.Errorf("failed to send OTP for phone change: %w", err)
	}
	logger.Info().Str("new_phone", newPhone).Str("flow_id", flowID.String()).Str("otp_sid", otpSid).Msg("OTP sent successfully for phone change")

	// 5. Update auth_flow with otp_sid
	_, errUpdate := s.Queries.UpdateAuthFlowOTPSID(ctx, db.UpdateAuthFlowOTPSIDParams{
		ID:     flowID,
		OtpSid: &otpSid,
	})
	if errUpdate != nil {
		// Log the error but don't necessarily fail the whole operation if OTP was sent.
		// The Verify step will rely on the user entering the code.
		if errors.Is(errUpdate, pgx.ErrNoRows) {
			logger.Warn().Str("flow_id", flowID.String()).Msg("Auth flow (phone change) not updated with otp_sid, likely expired/consumed before update.")
		} else {
			logger.Error().Err(errUpdate).Str("flow_id", flowID.String()).Str("otp_sid", otpSid).Msg("Error updating auth flow (phone change) with otp_sid")
		}
		// Decide if this is a critical failure. For now, proceed if OTP sent.
	} else {
		logger.Info().Str("flow_id", flowID.String()).Msg("Successfully updated auth flow (phone change) with otp_sid.")
	}

	return state, flowID, nil
}

func (s *AuthnService) VerifyPhoneChangeOTP(ctx context.Context, state, otp, expectedNewPhone string) (verified bool, flowID uuid.UUID, err error) {
	logger := log.Ctx(ctx) // Use logger instead of log for contextual logging

	// 1. Fetch Auth Flow
	activeFlow, err := s.Queries.GetAuthFlowByStateAndType(ctx, db.GetAuthFlowByStateAndTypeParams{
		State:    state,
		FlowType: string(AuthFlowTypePhoneChangeVerify),
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Str("state", state).Msg("Invalid or expired phone change state")
			return false, uuid.Nil, fmt.Errorf("invalid or expired phone change state: %s", state)
		}
		logger.Error().Err(err).Str("state", state).Msg("Error fetching auth flow by state for phone change")
		return false, uuid.Nil, fmt.Errorf("failed to retrieve phone change flow: %w", err)
	}
	flowID = activeFlow.ID

	// 2. Validate Phone Number in Flow
	if activeFlow.Phone == nil || *activeFlow.Phone == "" {
		logger.Error().Str("flow_id", flowID.String()).Str("state", state).Msg("Auth flow for phone change is missing phone number.")
		return false, flowID, fmt.Errorf("phone change flow integrity error (missing phone)")
	}
	if *activeFlow.Phone != expectedNewPhone {
		logger.Warn().Str("flow_id", flowID.String()).Str("expected_phone", expectedNewPhone).Str("flow_phone", *activeFlow.Phone).Msg("Phone number in flow does not match expected new phone for change.")
		return false, flowID, fmt.Errorf("phone number mismatch in verification flow")
	}

	// 3. OTP Attempt Limiting
	attempt, err := s.Queries.GetOTPAttempt(ctx, expectedNewPhone)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		logger.Error().Err(err).Str("phone", expectedNewPhone).Msg("Error getting OTP attempt for phone change")
		return false, flowID, fmt.Errorf("failed to process OTP attempts for phone change")
	}
	if err == nil && attempt.LockedUntil != nil && attempt.LockedUntil.After(time.Now()) {
		lockoutMsg := fmt.Sprintf("Too many failed OTP attempts for this phone number. Try again after %s.",
			time.Until(*attempt.LockedUntil).Round(time.Second).String())
		logger.Warn().Str("phone", expectedNewPhone).Msg("Phone locked for OTP (phone change)")
		return false, flowID, errors.New(lockoutMsg)
	}

	// 4. Verify OTP with Twilio
	if activeFlow.OtpSid == nil || *activeFlow.OtpSid == "" {
		logger.Error().Str("flow_id", flowID.String()).Str("state", state).Msg("OTP SID missing in auth flow (phone change). Cannot verify OTP.")
		return false, flowID, fmt.Errorf("OTP verification failed due to missing flow details. Please try initiating again. ")
	}

	otpValid, err := s.Twilio.VerifyOTPWithSID(*activeFlow.OtpSid, otp)
	if err != nil {
		logger.Error().Err(err).Str("otp_sid", *activeFlow.OtpSid).Msg("Error verifying OTP with Twilio for phone change")
		return false, flowID, fmt.Errorf("OTP verification failed during Twilio check: %w", err)
	}

	if !otpValid {
		logger.Warn().Str("otp_sid", *activeFlow.OtpSid).Str("phone", expectedNewPhone).Msg("Invalid OTP provided for phone change")
		// Handle Failed OTP Attempt
		updatedAttempt, upsertErr := s.Queries.UpsertOTPAttempt(ctx, expectedNewPhone)
		if upsertErr != nil {
			logger.Error().Err(upsertErr).Str("phone", expectedNewPhone).Msg("Error upserting OTP attempt for phone change")
		} else if updatedAttempt.AttemptCount >= MaxOTPAttempts { // MaxOTPAttempts from authn_handlers.go
			lockUntilTime := time.Now().Add(OTPLockoutDuration) // OTPLockoutDuration from authn_handlers.go
			_, lockErr := s.Queries.LockOTPAttempts(ctx, db.LockOTPAttemptsParams{
				Phone:       expectedNewPhone,
				LockedUntil: &lockUntilTime,
			})
			if lockErr != nil {
				logger.Error().Err(lockErr).Str("phone", expectedNewPhone).Msg("Error locking OTP attempts for phone change")
			} else {
				logger.Info().Str("phone", expectedNewPhone).Time("lock_until", lockUntilTime).Msg("OTP attempts locked for phone change")
			}
			return false, flowID, fmt.Errorf("too many failed OTP attempts. This phone number is temporarily locked. ")
		}
		return false, flowID, fmt.Errorf("invalid OTP")
	}

	logger.Info().Str("flow_id", flowID.String()).Str("phone", expectedNewPhone).Str("otp_sid", *activeFlow.OtpSid).Msg("OTP verified successfully for phone change")

	// 5. Reset OTP Attempts on Success
	resetErr := s.Queries.ResetOTPAttempts(ctx, expectedNewPhone)
	if resetErr != nil {
		logger.Error().Err(resetErr).Str("phone", expectedNewPhone).Msg("Error resetting OTP attempts after successful phone change verification")
		// Log and continue, phone is verified.
	}

	// 6. Mark Auth Flow as consumed/completed
	// For this internal flow, we don't have a client-provided code_verifier.
	// We stored nil for code_verifier in CreateAuthFlow.
	// UpdateAuthFlowConsumed expects a *string for code_verifier.
	// We can pass nil, or a placeholder if the query requires it. Let's try nil.
	var consumedCodeVerifier *string // = nil

	_, errUpdate := s.Queries.UpdateAuthFlowConsumed(ctx, db.UpdateAuthFlowConsumedParams{
		ID:           flowID,
		CodeVerifier: consumedCodeVerifier, // Pass nil as we didn't use a client verifier
		OtpSid:       activeFlow.OtpSid,    // Persist the SID that was verified
	})
	if errUpdate != nil {
		if errors.Is(errUpdate, pgx.ErrNoRows) {
			logger.Warn().Str("flow_id", flowID.String()).Msg("Auth flow (phone change) not updated (consumed), likely already expired/consumed.")
			// This might be okay if already consumed, means verification was successful before or idempotent.
		} else {
			logger.Error().Err(errUpdate).Str("flow_id", flowID.String()).Msg("Failed to consume auth flow for phone change")
			// Depending on requirements, this might warrant returning an error.
			// For now, if OTP was valid, we return success.
		}
	}

	return true, flowID, nil
}

// InitiateReAuthOTP starts an OTP flow for a logged-in user for a specific purpose.
func (s *AuthnService) InitiateReAuthOTP(ctx context.Context, userID uuid.UUID, purpose string, channel *string) (uuid.UUID, string, error) {
	logger := log.Ctx(ctx)

	// 1. Get user to find their phone number
	user, err := s.Queries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Str("user_id", userID.String()).Msg("User not found for re-auth OTP initiation.")
			return uuid.Nil, "", errors.New("user not found")
		}
		logger.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to get user for re-auth OTP initiation.")
		return uuid.Nil, "", errors.New("failed to retrieve user details")
	}

	if user.Phone == nil || *user.Phone == "" {
		logger.Warn().Str("user_id", userID.String()).Msg("User has no phone number for re-auth OTP.")
		return uuid.Nil, "", errors.New("no phone number on record for OTP")
	}
	userPhone := *user.Phone

	// 2. Determine OTP channel
	otpChannel := "sms" // Default
	if channel != nil && *channel != "" {
		otpChannel = *channel
	} else {
		otpChannel = user.PhoneOtpChannel // User's preference
	}

	// 3. Create an auth_flow record for this attempt
	authFlowParams := db.CreateAuthFlowParams{
		FlowType:  string(AuthFlowTypeReAuthVerify),
		Phone:     &userPhone,
		UserID:    &userID, // Associate flow with the user
		Purpose:   &purpose,
		ExpiresAt: time.Now().Add(10 * time.Minute),
		// PKCE and client details are not used for this internal, server-initiated flow.
		// Using placeholder values to satisfy NOT NULL constraints.
		// TODO: Consider altering table to allow NULLs for these for 're_auth_verify' flow type.
		CodeChallenge:       "n/a",
		CodeChallengeMethod: "n/a",
		State:               uuid.New().String(), // Use a unique state to satisfy UNIQUE constraint.
		ClientID:            "internal_reauth",
		RedirectUri:         "n/a",
	}

	createdFlow, err := s.Queries.CreateAuthFlow(ctx, authFlowParams)
	if err != nil {
		logger.Error().Err(err).Str("user_id", userID.String()).Msg("Error creating auth flow for re-auth")
		return uuid.Nil, "", errors.New("failed to initiate re-authentication flow")
	}

	// 4. Send OTP
	otpSid, err := s.Twilio.SendOTP(userPhone, otpChannel)
	if err != nil {
		logger.Error().Err(err).Str("user_id", userID.String()).Str("phone", userPhone).Msg("Error sending re-auth OTP")
		// Consider marking the flow as failed
		return uuid.Nil, "", errors.New("failed to send OTP")
	}

	// 5. Store OTP SID in the flow
	_, err = s.Queries.UpdateAuthFlowOTPSID(ctx, db.UpdateAuthFlowOTPSIDParams{
		ID:     createdFlow.ID,
		OtpSid: &otpSid,
	})
	if err != nil {
		// Log but don't fail the operation, as OTP is already sent.
		logger.Error().Err(err).Str("flow_id", createdFlow.ID.String()).Msg("Failed to update auth flow with OTP SID for re-auth")
	}

	logger.Info().Str("user_id", userID.String()).Str("flow_id", createdFlow.ID.String()).Msg("Re-authentication OTP initiated successfully.")
	return createdFlow.ID, otpChannel, nil
}

// VerifyReAuthOTP verifies the OTP for a re-authentication flow and returns a short-lived token.
func (s *AuthnService) VerifyReAuthOTP(ctx context.Context, userID uuid.UUID, flowID uuid.UUID, otp string) (string, error) {
	logger := log.Ctx(ctx)

	// 1. Get the auth flow
	authFlow, err := s.Queries.GetAuthFlowByID(ctx, flowID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return "", errors.New("invalid or expired re-authentication session")
		}
		logger.Error().Err(err).Str("flow_id", flowID.String()).Msg("Failed to retrieve auth flow for re-auth verification")
		return "", errors.New("failed to retrieve re-authentication session")
	}

	// 2. Validate the flow
	if authFlow.FlowType != string(AuthFlowTypeReAuthVerify) {
		return "", errors.New("invalid re-authentication session type")
	}
	if authFlow.UserID == nil || *authFlow.UserID != userID {
		return "", errors.New("re-authentication session mismatch")
	}
	if authFlow.ExpiresAt.Before(time.Now()) {
		return "", errors.New("re-authentication session has expired or been used")
	}
	if authFlow.Phone == nil || *authFlow.Phone == "" || authFlow.OtpSid == nil || *authFlow.OtpSid == "" {
		return "", errors.New("invalid re-authentication session state")
	}

	// 3. Verify OTP with Twilio
	verified, err := s.Twilio.VerifyOTPWithSID(*authFlow.OtpSid, otp)
	if err != nil {
		logger.Error().Err(err).Str("flow_id", flowID.String()).Msg("Error verifying re-auth OTP with Twilio")
		return "", errors.New("failed to verify OTP")
	}

	if !verified {
		// Here you would typically add attempt tracking and locking like in the login flow
		return "", errors.New("invalid OTP")
	}

	// 4. Mark flow as consumed
	var consumedCodeVerifier *string
	_, err = s.Queries.UpdateAuthFlowConsumed(ctx, db.UpdateAuthFlowConsumedParams{
		ID:           flowID,
		CodeVerifier: consumedCodeVerifier,
		OtpSid:       authFlow.OtpSid,
	})
	if err != nil {
		logger.Error().Err(err).Str("flow_id", flowID.String()).Msg("Failed to mark re-auth flow as consumed")
		return "", errors.New("failed to finalize re-authentication")
	}

	// 5. Generate short-lived re-auth token
	// This token will contain the user ID and the purpose from the auth flow.
	reauthToken, err := token.GenerateReAuthToken(userID, *authFlow.Purpose)
	if err != nil {
		logger.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to generate re-auth token")
		return "", errors.New("failed to generate re-authentication token")
	}

	logger.Info().Str("user_id", userID.String()).Str("flow_id", flowID.String()).Msg("Re-authentication successful.")
	return reauthToken, nil
}

// Logout revokes a given refresh token.
// It finds the refresh token by its string value and deletes it from the database.
func (s *AuthnService) Logout(ctx context.Context, refreshTokenString string) error {
	logger := log.Ctx(ctx)

	// Hash the incoming refresh token string to match how it's stored in the DB.
	hashedToken := token.HashToken(refreshTokenString) // Assuming token.HashToken function exists

	err := s.Queries.DeleteRefreshTokenByTokenHash(ctx, hashedToken)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// Token not found (based on its hash). This could mean it was already revoked or never existed.
			// For a logout operation, this is not necessarily an error to return to the client.
			logger.Warn().Str("hashedRefreshTokenHint", hashedToken[:min(10, len(hashedToken))]+"...").Msg("Logout attempt for non-existent or already revoked refresh token (hash not found)")
			return nil // Treat as success from the client's perspective
		}
		logger.Error().Err(err).Msg("Error deleting refresh token from database during logout")
		return fmt.Errorf("failed to invalidate refresh token: %w", err)
	}

	logger.Info().Str("hashedRefreshTokenHint", hashedToken[:min(10, len(hashedToken))]+"...").Msg("Successfully revoked refresh token by hash")
	return nil
}

// Helper function to avoid panicking with string slicing if token is too short
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
