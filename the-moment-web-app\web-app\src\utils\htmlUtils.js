import DOMPurify from 'dompurify';
import { generateHTML } from '@tiptap/html';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';

/**
 * Helper function to generate HTML from JSON content with sanitization
 * @param {Object} jsonContent - The JSON content from TipTap editor
 * @returns {string} - Sanitized HTML string
 */
export const generateSafeHTML = (jsonContent) => {
    if (!jsonContent) return '';

    try {
        const html = generateHTML(jsonContent, [
            StarterKit.configure({
                heading: {
                    levels: [1, 2, 3],
                },
            }),
            Underline,
            TextAlign.configure({
                types: ['heading', 'paragraph'],
                alignments: ['left', 'center', 'right'],
            }),
        ]);

        // Sanitize HTML to prevent XSS
        return DOMPurify.sanitize(html, {
            ALLOWED_TAGS: [
                'p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3',
                'ul', 'ol', 'li', 'a', 'span'
            ],
            ALLOWED_ATTR: ['href', 'style', 'class', 'target', 'rel'],
            ALLOWED_STYLES: ['text-align'],
            ADD_ATTR: ['target'], // Add target="_blank" to links
            ADD_TAGS: ['span'],
            FORBID_TAGS: ['script', 'style', 'iframe', 'form', 'input'],
            FORBID_ATTR: ['onerror', 'onload', 'onclick'],
            SANITIZE_DOM: true,
            KEEP_CONTENT: true,
        });
    } catch (error) {
        console.error('Error generating HTML:', error);
        return '';
    }
};
