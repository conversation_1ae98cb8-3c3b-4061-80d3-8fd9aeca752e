package twilio_service

import (
	"errors"
	"fmt"
	"os"
	"strings"

	"github.com/rs/zerolog/log"
	"github.com/twilio/twilio-go"
	twilioApiMessages "github.com/twilio/twilio-go/rest/api/v2010"
	twilioApiVerify "github.com/twilio/twilio-go/rest/verify/v2"
)

const MockOTPSIDPrefix = "SMmockmock"
const MockOTPCode = "123456"

// TwilioService handles interactions with the Twilio Verify API and Messaging API.
type TwilioService struct {
	client                *twilio.RestClient
	verifyServiceSID      string
	messagingFromNumber   string
	messagingServiceSID   string
	mockOTPMode           bool
	mockNotificationsMode bool
}

// NewTwilioService creates a new TwilioService.
func NewTwilioService() (*TwilioService, error) {
	mockOTPEnv := os.Getenv("MOCK_TWILIO_OTP")
	mockOTPMode := mockOTPEnv == "true"

	mockNotificationsEnv := os.Getenv("MOCK_TWILIO_NOTIFICATIONS")
	mockNotificationsMode := mockNotificationsEnv == "true"

	if mockOTPMode {
		log.Warn().Msg("TwilioService: MOCK_TWILIO_OTP is true. Real OTPs will not be sent.")
	}
	if mockNotificationsMode {
		log.Warn().Msg("TwilioService: MOCK_TWILIO_NOTIFICATIONS is true. Real general messages will not be sent.")
	}

	accountSID := os.Getenv("TWILIO_ACCOUNT_SID")
	authToken := os.Getenv("TWILIO_AUTH_TOKEN")
	verifyServiceSID := os.Getenv("TWILIO_VERIFY_SERVICE_SID")
	messagingFromNumber := os.Getenv("TWILIO_MESSAGING_FROM_NUMBER")
	messagingServiceSID := os.Getenv("TWILIO_MESSAGING_SERVICE_SID")

	// Check for core credentials if any service is NOT mocked
	if (!mockOTPMode || !mockNotificationsMode) && (accountSID == "" || authToken == "") {
		return nil, errors.New("Twilio core credentials (TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN) are not set, but at least one Twilio service (OTP or Notifications) is not in mock mode")
	}

	// Check for OTP-specific config if OTPs are not mocked
	if !mockOTPMode && verifyServiceSID == "" {
		log.Warn().Msg("TWILIO_VERIFY_SERVICE_SID is not set. OTP functionality will be impaired as MOCK_TWILIO_OTP is false.")
		// Depending on strictness, could return an error here
	}

	// Check for Messaging-specific config if general messages are not mocked
	if !mockNotificationsMode && messagingFromNumber == "" && messagingServiceSID == "" {
		log.Warn().Msg("Neither TWILIO_MESSAGING_FROM_NUMBER nor TWILIO_MESSAGING_SERVICE_SID is set. General message sending will be impaired as MOCK_TWILIO_NOTIFICATIONS is false.")
		// Depending on strictness, could return an error here
	}

	var client *twilio.RestClient
	// Only create a real client if core credentials are provided (they would be if not fully mocked)
	if accountSID != "" && authToken != "" {
		client = twilio.NewRestClientWithParams(twilio.ClientParams{
			Username:   accountSID,
			Password:   authToken,
			AccountSid: accountSID,
		})
	}

	return &TwilioService{
		client:                client,
		verifyServiceSID:      verifyServiceSID,
		messagingFromNumber:   messagingFromNumber,
		messagingServiceSID:   messagingServiceSID,
		mockOTPMode:           mockOTPMode,
		mockNotificationsMode: mockNotificationsMode,
	}, nil
}

// SendOTP sends an OTP or simulates sending if in mock mode.
func (s *TwilioService) SendOTP(phoneNumber string, channel string) (string, error) {
	if s.mockOTPMode {
		log.Info().Str("phone", phoneNumber).Str("channel", channel).Str("mock_otp", MockOTPCode).Msg("MOCK_TWILIO: Simulated SendOTP")
		return MockOTPSIDPrefix + "1234567890abcdefghijklmn", nil
	}

	if s.client == nil {
		return "", errors.New("Twilio client not initialized for non-mock mode")
	}
	if s.verifyServiceSID == "" {
		return "", errors.New("Twilio Verify Service SID not configured for SendOTP")
	}
	if channel != "sms" && channel != "whatsapp" {
		return "", fmt.Errorf("unsupported OTP channel: %s, use 'sms' or 'whatsapp'", channel)
	}

	params := &twilioApiVerify.CreateVerificationParams{}
	params.SetTo(phoneNumber)
	params.SetChannel(channel)

	resp, err := s.client.VerifyV2.CreateVerification(s.verifyServiceSID, params)
	if err != nil {
		return "", fmt.Errorf("failed to send Twilio OTP to %s: %w", phoneNumber, err)
	}

	if resp.Sid == nil {
		return "", fmt.Errorf("Twilio OTP send response did not include a SID for phone %s", phoneNumber)
	}

	return *resp.Sid, nil
}

// VerifyOTP checks the OTP code using the phone number and OTP code.
// Twilio Verify API often uses the phone number + code for checks, or a verification SID.
// This implementation assumes checking against the phone number as per typical direct OTP check.
// If using Verification SID (otp_sid from SendOTP), the method signature and params would change.
func (s *TwilioService) VerifyOTP(phoneNumber string, otpCode string) (bool, error) {
	// This method is problematic as Twilio Verify V2 primarily uses Verification SIDs for checks.
	// Keeping the mock path for minimal disruption if anything still calls this, but real path should error.
	if s.mockOTPMode {
		log.Info().Str("phone", phoneNumber).Str("otp_code_provided", otpCode).Msg("MOCK_TWILIO: Simulated VerifyOTP (deprecated)")
		if otpCode == MockOTPCode {
			log.Info().Msg("MOCK_TWILIO: Mock OTP code matches (deprecated VerifyOTP).")
			return true, nil
		}
		log.Warn().Msg("MOCK_TWILIO: Mock OTP code does NOT match (deprecated VerifyOTP).")
		return false, nil
	}
	// Real verification via phone+code is not the standard path for Verify V2 SID-based flows.
	return false, errors.New("VerifyOTP method signature needs adjustment to use Verification SID (otp_sid). Use VerifyOTPWithSID instead.")
}

// VerifyOTPWithSID verifies an OTP or simulates verification if in mock mode.
func (s *TwilioService) VerifyOTPWithSID(otpSid string, otpCode string) (bool, error) {
	if s.mockOTPMode {
		log.Info().Str("otp_sid", otpSid).Str("otp_code_provided", otpCode).Msg("MOCK_TWILIO: Simulated VerifyOTPWithSID")
		if otpCode == MockOTPCode {
			log.Info().Msg("MOCK_TWILIO: Mock OTP code matches.")
			return true, nil
		}
		log.Warn().Msg("MOCK_TWILIO: Mock OTP code does NOT match.")
		return false, nil
	}

	if s.client == nil {
		return false, errors.New("Twilio client not initialized for non-mock mode")
	}

	params := &twilioApiVerify.CreateVerificationCheckParams{}
	params.SetVerificationSid(otpSid)
	params.SetCode(otpCode)

	resp, err := s.client.VerifyV2.CreateVerificationCheck(s.verifyServiceSID, params)
	if err != nil {
		return false, fmt.Errorf("failed to verify Twilio OTP for SID %s: %w", otpSid, err)
	}

	if resp.Status == nil {
		return false, fmt.Errorf("Twilio OTP verification response did not include a status for SID %s", otpSid)
	}

	// Log the full response status for debugging
	log.Debug().Str("otp_sid", otpSid).Str("status", *resp.Status).Interface("response", resp).Msg("Twilio VerifyOTPWithSID response")

	return *resp.Status == "approved", nil
}

// SendMessage sends a general message (SMS or WhatsApp) via Twilio.
// channel should be "sms" or "whatsapp".
func (s *TwilioService) SendMessage(toNumber string, messageBody string, channel string) error {
	if s.mockNotificationsMode {
		log.Info().Str("to", toNumber).Str("channel", channel).Str("body", messageBody).Msg("MOCK_TWILIO: Simulated SendMessage (due to MOCK_TWILIO_NOTIFICATIONS=true)")
		return nil
	}

	if s.client == nil {
		return errors.New("Twilio client not initialized for non-mock mode")
	}

	if s.client.Api == nil {
		return errors.New("Twilio ApiService (for messaging) not initialized on client")
	}

	if s.messagingFromNumber == "" && s.messagingServiceSID == "" {
		return errors.New("Twilio messaging not configured (missing FromNumber and MessagingServiceSID)")
	}

	params := &twilioApiMessages.CreateMessageParams{}
	actualTo := toNumber
	actualFrom := s.messagingFromNumber

	if channel == "whatsapp" {
		if !strings.HasPrefix(toNumber, "whatsapp:") {
			actualTo = "whatsapp:" + toNumber
		}
		if s.messagingFromNumber != "" && !strings.HasPrefix(s.messagingFromNumber, "whatsapp:") {
			actualFrom = "whatsapp:" + s.messagingFromNumber
		}
	} else if channel != "sms" {
		return fmt.Errorf("unsupported message channel: %s, use 'sms' or 'whatsapp'", channel)
	}

	params.SetTo(actualTo)
	params.SetBody(messageBody)

	if s.messagingServiceSID != "" {
		params.SetMessagingServiceSid(s.messagingServiceSID)
	} else if actualFrom != "" {
		params.SetFrom(actualFrom)
	} else {
		return errors.New("Twilio messaging 'From' identity (FromNumber or MessagingServiceSID) not available")
	}

	log.Info().Str("to", actualTo).Str("from", actualFrom).Str("messagingServiceSID", s.messagingServiceSID).Str("channel", channel).Msg("Attempting to send message via Twilio")

	_, err := s.client.Api.CreateMessage(params)
	if err != nil {
		log.Error().Err(err).Str("to", actualTo).Msg("Failed to send Twilio message")
		return fmt.Errorf("failed to send Twilio message to %s: %w", actualTo, err)
	}

	log.Info().Str("to", actualTo).Msg("Successfully sent message via Twilio")
	return nil
}

// SendTemplatedMessage sends a templated WhatsApp message using a pre-approved Twilio template.
// toNumber should be the recipient's E.164 formatted phone number.
// templateSid is the SID of the Twilio Message Template (e.g., "HX...").
// templateParams is a map where keys are placeholder numbers (e.g., "1", "2") and values are the substitution strings.
// This method is specifically for WhatsApp and relies on Twilio's Content API features.
func (s *TwilioService) SendTemplatedMessage(toNumber string, templateSid string, templateParams map[string]string) error {
	if s.mockNotificationsMode {
		log.Info().
			Str("to", toNumber).
			Str("templateSid", templateSid).
			Interface("templateParams", templateParams).
			Msg("MOCK_TWILIO: Simulated SendTemplatedMessage (due to MOCK_TWILIO_NOTIFICATIONS=true)")
		return nil
	}

	if s.client == nil {
		return errors.New("Twilio client not initialized for non-mock mode")
	}
	if s.client.Api == nil {
		return errors.New("Twilio ApiService (for messaging) not initialized on client")
	}

	// For templated messages, From is typically the WhatsApp sender registered with Twilio,
	// often managed by MessagingServiceSid or a specific WhatsApp-enabled number.
	// To needs to be prefixed with "whatsapp:"
	actualTo := toNumber
	if !strings.HasPrefix(toNumber, "whatsapp:") {
		actualTo = "whatsapp:" + toNumber
	}

	// Construct ContentVariables string (e.g., "1=John&2=EventName")
	var contentVariablesParts []string
	for key, value := range templateParams {
		contentVariablesParts = append(contentVariablesParts, fmt.Sprintf("%s=%s", key, value))
	}
	contentVariables := strings.Join(contentVariablesParts, "&")

	params := &twilioApiMessages.CreateMessageParams{}
	params.SetTo(actualTo)
	params.SetContentSid(templateSid)
	params.SetContentVariables(contentVariables)

	// Determine 'From'. If MessagingServiceSid is set, it's preferred.
	// Otherwise, use the configured FromNumber, ensuring it's WhatsApp prefixed if necessary.
	var fromValueForLog string
	if s.messagingServiceSID != "" {
		params.SetMessagingServiceSid(s.messagingServiceSID)
		fromValueForLog = "Using MessagingServiceSID: " + s.messagingServiceSID
	} else if s.messagingFromNumber != "" {
		actualFrom := s.messagingFromNumber
		if !strings.HasPrefix(s.messagingFromNumber, "whatsapp:") {
			actualFrom = "whatsapp:" + s.messagingFromNumber
		}
		params.SetFrom(actualFrom)
		fromValueForLog = "Using FromNumber: " + actualFrom
	} else {
		return errors.New("Twilio messaging 'From' identity (FromNumber or MessagingServiceSID) not available for templated message")
	}

	log.Info().
		Str("to", actualTo).
		Str("from_identity_used", fromValueForLog).
		Str("templateSid", templateSid).
		Str("contentVariables", contentVariables).
		Msg("Attempting to send templated WhatsApp message via Twilio")

	_, err := s.client.Api.CreateMessage(params)
	if err != nil {
		log.Error().Err(err).Str("to", actualTo).Str("templateSid", templateSid).Msg("Failed to send Twilio templated message")
		return fmt.Errorf("failed to send Twilio templated message to %s using SID %s: %w", actualTo, templateSid, err)
	}

	log.Info().Str("to", actualTo).Str("templateSid", templateSid).Msg("Successfully sent templated WhatsApp message via Twilio")
	return nil
}
