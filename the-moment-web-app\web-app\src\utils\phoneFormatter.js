/**
 * Format phone number to Hong Kong format
 * Accepts numbers with or without +852 prefix
 * Returns formatted number with +852 prefix
 * Example: 
 * - Input: "91234567" -> Output: "+852 9123 4567"
 * - Input: "85291234567" -> Output: "+852 9123 4567"
 * - Input: "+85291234567" -> Output: "+852 9123 4567"
 */
export const formatHKPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return '';

    // Remove all non-digit characters
    let digits = phoneNumber.replace(/\D/g, '');

    // Remove 852 prefix if exists
    if (digits.startsWith('852')) {
        digits = digits.substring(3);
    }

    // Check if it's a valid HK phone number (8 digits)
    if (digits.length !== 8) {
        return phoneNumber; // Return original if not valid
    }

    // Format as +852 XXXX XXXX
    return `+852 ${digits.substring(0, 4)} ${digits.substring(4)}`;
};

/**
 * Validate if the phone number is a valid Hong Kong phone number
 * Valid formats:
 * - 8 digits (local format)
 * - 11 digits (with 852)
 * - 12 digits (with +852)
 */
export const isValidHKPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return false;

    // Remove all non-digit characters except +
    const cleaned = phoneNumber.replace(/[^\d+]/g, '');

    // Check various formats
    if (cleaned.length === 8) {
        // Local format: 12345678
        return /^[2-9]\d{7}$/.test(cleaned);
    } else if (cleaned.length === 11 && cleaned.startsWith('852')) {
        // With 852: 85212345678
        return /^852[2-9]\d{7}$/.test(cleaned);
    } else if (cleaned.length === 12 && cleaned.startsWith('+852')) {
        // With +852: +85212345678
        return /^\+852[2-9]\d{7}$/.test(cleaned);
    }

    return false;
}; 