# Utilities and Common Components

This directory contains reusable utilities and common components for the membership SaaS system web app. These utilities are designed to reduce code duplication and improve code readability and maintainability.

## API Utilities (`apiUtils.js`)

The `apiUtils.js` file provides standardized helper functions for making API calls and handling API responses.

### Usage Examples

```javascript
import { executeApiCall, getResourceBasePath, createFormDataFromFile } from '../utils/apiUtils';
import api from './api';

// Example: Using executeApiCall for a GET request
const getUsers = async (params) => {
  return executeApiCall(
    () => api.get('/users', { params }),
    'Error fetching users',
    false // Return full response with headers
  );
};

// Example: Using getResourceBasePath to construct API paths
const orgResourcesPath = getResourceBasePath('resources', orgId);

// Example: Creating FormData for file uploads
const formData = createFormDataFromFile(file);
```

## Form Validation Utilities (`formValidation.js`)

The `formValidation.js` file provides common form validation rules for use with Ant Design's Form component.

### Usage Examples

```javascript
import { 
  getRequiredRule, 
  getEmailRules, 
  getPhoneRules,
  getIdValidationRules,
  handleIdDocChange
} from '../utils/formValidation';

// In your form component
<Form.Item
  name="email"
  label="Email"
  rules={getEmailRules()}
>
  <Input />
</Form.Item>

<Form.Item
  name="phone"
  label="Phone Number"
  rules={getPhoneRules()}
>
  <Input />
</Form.Item>

<Form.Item
  name="idNumber"
  label="HKID"
  rules={getIdValidationRules('hkid')}
>
  <Input onChange={(e) => handleIdDocChange(e, form, 'hkid')} />
</Form.Item>
```

## ID Validation Utilities (`idValidationUtils.js`)

The `idValidationUtils.js` file provides utilities for validating and formatting ID documents.

### Usage Examples

```javascript
import { validateHKID, formatHKID, removeHKIDFormat } from '../utils/idValidationUtils';

// Validate an HKID
const validationResult = validateHKID('A123456(7)');
if (validationResult.valid) {
  console.log('HKID is valid');
} else {
  console.error(validationResult.message);
}

// Format an HKID
const formattingResult = formatHKID('********');
if (formattingResult.success) {
  console.log('Formatted HKID:', formattingResult.formattedID); // A123456(7)
}

// Remove formatting from an HKID
const cleanHKID = removeHKIDFormat('A123456(7)'); // ********
```

## Common Components

### FormModal (`components/common/FormModal.js`)

A reusable modal component with form handling capabilities.

```jsx
import FormModal from '../../components/common/FormModal';
import { Form, Input } from 'antd';

const MyFormComponent = () => {
  const [form] = Form.useForm();
  
  return (
    <FormModal
      visible={true}
      onCancel={handleCancel}
      onSubmit={handleSubmit}
      isLoading={isLoading}
      isSubmitting={isSubmitting}
      title="My Form"
      form={form}
      error={error}
    >
      <Form.Item name="name" label="Name" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
    </FormModal>
  );
};
```

### FormSkeleton (`components/common/FormSkeleton.js`)

A skeleton component for showing form loading states.

```jsx
import FormSkeleton from '../../components/common/FormSkeleton';

const MyComponent = () => {
  if (isLoading) {
    return <FormSkeleton rows={5} />;
  }
  
  return <ActualForm />;
};
```

### ErrorDisplay (`components/common/ErrorDisplay.js`)

A standardized way to display errors.

```jsx
import ErrorDisplay from '../../components/common/ErrorDisplay';

const MyComponent = () => {
  return (
    <div>
      <ErrorDisplay 
        message="Failed to load data" 
        description="Please try again later" 
        onRetry={handleRetry} 
      />
    </div>
  );
};
```

### LoadingSpinner (`components/common/LoadingSpinner.js`)

A standardized loading spinner.

```jsx
import LoadingSpinner from '../../components/common/LoadingSpinner';

const MyComponent = () => {
  if (isLoading) {
    return <LoadingSpinner tip="Loading data..." />;
  }
  
  return <ActualContent />;
};
```

### Pagination (`components/common/Pagination.js`)

A standardized pagination component.

```jsx
import Pagination from '../../components/common/Pagination';

const MyListComponent = () => {
  return (
    <div>
      <ListItems items={items} />
      <Pagination 
        currentPage={currentPage} 
        totalItems={totalItems} 
        itemsPerPage={itemsPerPage} 
        onPageChange={handlePageChange} 
      />
    </div>
  );
};
```

## Custom Hooks

### useFormSubmit (`hooks/useFormSubmit.js`)

A hook for standardized form submission with loading and error handling.

```jsx
import useFormSubmit from '../../hooks/useFormSubmit';

const MyFormComponent = () => {
  const { handleSubmit, isSubmitting, error } = useFormSubmit(
    async (values) => {
      // Process form values and make API call
      return await api.post('/resource', values);
    },
    {
      onSuccess: (result) => {
        // Handle success
      },
      successMessage: 'Form submitted successfully',
      errorMessage: 'Failed to submit form'
    }
  );
  
  return (
    <form onSubmit={() => handleSubmit(formValues)}>
      {/* Form fields */}
      <button type="submit" disabled={isSubmitting}>Submit</button>
      {error && <div className="error">{error.message}</div>}
    </form>
  );
};
```

## Best Practices

1. Always use the utility functions and common components when applicable to ensure consistency across the application.
2. When creating new components or pages, consider if there's an opportunity to extract reusable logic into a utility function or common component.
3. Update this README when adding new utilities or common components.
4. Follow the established patterns when extending or modifying existing utilities.
5. Use JSDoc comments to document utility functions and components. 