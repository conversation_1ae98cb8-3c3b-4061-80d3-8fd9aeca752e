-- Revert Seed Organizations and Users

DO $$
DECLARE
    org_tongxing_id uuid;
    org_shatinsi_id uuid;
    org_blue_id uuid;
    org_pink_id uuid;

    user_admin_tongxing_id uuid;
    user_manager_shatinsi_id uuid;
    user_regular1_id uuid;
    user_regular2_id uuid;
    user_regular3_id uuid;
BEGIN
    -- Get IDs needed for deletion (use emails/phones/names as unique identifiers)
    SELECT id INTO org_tongxing_id FROM organizations WHERE name = '同行明天';
    SELECT id INTO org_shatinsi_id FROM organizations WHERE name = '沙田西關愛隊';
    SELECT id INTO org_blue_id FROM organizations WHERE name = '藍色主題組織';
    SELECT id INTO org_pink_id FROM organizations WHERE name = '玫紅色主題組織';

    SELECT id INTO user_admin_tongxing_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO user_manager_shatinsi_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO user_regular1_id FROM users WHERE phone = '+85299991001';
    SELECT id INTO user_regular2_id FROM users WHERE phone = '+85299991002';
    SELECT id INTO user_regular3_id FROM users WHERE phone = '+85299991003';

    -- Delete in reverse order of creation/dependency
    -- 1. Delete Memberships
    DELETE FROM user_organization_memberships WHERE user_id = user_admin_tongxing_id AND organization_id = org_tongxing_id;
    DELETE FROM user_organization_memberships WHERE user_id = user_manager_shatinsi_id AND organization_id = org_shatinsi_id;
    DELETE FROM user_organization_memberships WHERE user_id = user_regular1_id;
    DELETE FROM user_organization_memberships WHERE user_id = user_regular2_id;
    DELETE FROM user_organization_memberships WHERE user_id = user_regular3_id;

    -- 2. Delete Users (Handles cascade or related data should be cleared first)
    DELETE FROM users WHERE id = user_admin_tongxing_id;
    DELETE FROM users WHERE id = user_manager_shatinsi_id;
    DELETE FROM users WHERE id = user_regular1_id;
    DELETE FROM users WHERE id = user_regular2_id;
    DELETE FROM users WHERE id = user_regular3_id;

    -- 3. Delete Organizations
    DELETE FROM organizations WHERE id = org_tongxing_id;
    DELETE FROM organizations WHERE id = org_shatinsi_id;
    DELETE FROM organizations WHERE id = org_blue_id;
    DELETE FROM organizations WHERE id = org_pink_id;

END $$; 