-- Enum for verification types
CREATE TYPE verification_type_enum AS ENUM (
    'hk_id_card',
    'mainland_china_id_card',
    'home_return_permit',
    'passport',
    'hk_youth_plus',
    'address_proof',
    'student_id'
);

-- Enum for verification statuses
CREATE TYPE verification_status_enum AS ENUM (
    'pending',
    'approved',
    'rejected',
    'data_deleted_by_user' -- User requested data deletion
);

-- Table to store uploaded verification documents
CREATE TABLE verification_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    file_data BYTEA NOT NULL, -- Stores the binary data of the file
    uploaded_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE INDEX idx_verification_documents_user_id ON verification_documents(user_id);

-- Central table for user verification requests
CREATE TABLE user_verification_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    verification_type verification_type_enum NOT NULL,
    status verification_status_enum NOT NULL DEFAULT 'pending',
    document_id UUID REFERENCES verification_documents(id) ON DELETE SET NULL, -- Nullable as HK Youth+ has no document
    submitted_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    reviewed_at TIMESTAMPTZ,
    reviewed_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Admin who reviewed
    admin_notes TEXT, -- For rejection reasons or other comments
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE INDEX idx_user_verification_requests_user_id ON user_verification_requests(user_id);
CREATE INDEX idx_user_verification_requests_type ON user_verification_requests(verification_type);
CREATE INDEX idx_user_verification_requests_status ON user_verification_requests(status);

-- Specific verification type tables

-- 1. Hong Kong ID Card
CREATE TABLE verification_hk_id_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID NOT NULL UNIQUE REFERENCES user_verification_requests(id) ON DELETE CASCADE,
    chinese_name TEXT,
    chinese_commercial_code TEXT,
    english_name TEXT,
    sex VARCHAR(10), -- M, F, Other
    date_of_birth TIMESTAMPTZ NOT NULL,
    hk_id_number VARCHAR(20),
    is_permanent_resident BOOLEAN,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX idx_verification_hk_id_cards_request_id ON verification_hk_id_cards(verification_request_id);

-- 2. Mainland China ID Card
CREATE TABLE verification_mainland_china_id_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID NOT NULL UNIQUE REFERENCES user_verification_requests(id) ON DELETE CASCADE,
    chinese_name TEXT,
    sex VARCHAR(10),
    date_of_birth TIMESTAMPTZ NOT NULL,
    mainland_id_number VARCHAR(50),
    valid_until TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX idx_verification_mainland_china_id_cards_request_id ON verification_mainland_china_id_cards(verification_request_id);

-- 3. Home Return Permit / Mainland Travel Permit for Hong Kong Residents
CREATE TABLE verification_home_return_permits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID NOT NULL UNIQUE REFERENCES user_verification_requests(id) ON DELETE CASCADE,
    permit_number VARCHAR(50), -- e.g. H12345678
    issue_date TIMESTAMPTZ NOT NULL,
    expiry_date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX idx_verification_home_return_permits_request_id ON verification_home_return_permits(verification_request_id);

-- 4. Passport
CREATE TABLE verification_passports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID NOT NULL UNIQUE REFERENCES user_verification_requests(id) ON DELETE CASCADE,
    passport_number VARCHAR(50),
    issuing_country VARCHAR(100), -- Consider ISO country codes if standardization needed
    issue_date TIMESTAMPTZ NOT NULL,
    expiry_date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX idx_verification_passports_request_id ON verification_passports(verification_request_id);

-- 5. HK Youth+
CREATE TABLE verification_hk_youth_plus (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID NOT NULL UNIQUE REFERENCES user_verification_requests(id) ON DELETE CASCADE,
    member_number TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX idx_verification_hk_youth_plus_request_id ON verification_hk_youth_plus(verification_request_id);

-- 6. Address Proof
CREATE TABLE verification_address_proofs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID NOT NULL UNIQUE REFERENCES user_verification_requests(id) ON DELETE CASCADE,
    full_address TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX idx_verification_address_proofs_request_id ON verification_address_proofs(verification_request_id);

-- 7. Student ID
CREATE TABLE verification_student_ids (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID NOT NULL UNIQUE REFERENCES user_verification_requests(id) ON DELETE CASCADE,
    school_name TEXT,
    grade TEXT, -- e.g., "Grade 10", "Year 3 University"
    expiry_date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX idx_verification_student_ids_request_id ON verification_student_ids(verification_request_id);

-- Triggers to update 'updated_at' columns
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to all tables with an 'updated_at' column
DO $$
DECLARE
    t_name TEXT;
    trigger_exists BOOLEAN;
BEGIN
    FOR t_name IN
        SELECT table_name
        FROM information_schema.columns
        WHERE column_name = 'updated_at'
          AND table_schema = current_schema() -- only tables in the current schema
          AND (table_name LIKE 'verification_%' OR table_name = 'user_verification_requests') -- Grouped condition
    LOOP
        -- Check if the trigger 'set_timestamp' already exists for the table
        SELECT EXISTS (
            SELECT 1
            FROM pg_trigger t
            JOIN pg_class c ON t.tgrelid = c.oid
            JOIN pg_namespace n ON c.relnamespace = n.oid
            WHERE n.nspname = current_schema()
              AND c.relname = t_name
              AND t.tgname = 'set_timestamp'
        ) INTO trigger_exists;

        IF NOT trigger_exists THEN
            EXECUTE format('CREATE TRIGGER set_timestamp
                            BEFORE UPDATE ON %I
                            FOR EACH ROW EXECUTE PROCEDURE trigger_set_timestamp()', t_name);
        END IF;
    END LOOP;
END;
$$; 