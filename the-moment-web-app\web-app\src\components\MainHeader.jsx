import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout, Button, Space, Avatar, Dropdown, Typography, Select } from 'antd';
import { UserOutlined, MenuOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';
import { findRouteMetaInfo } from '../routes/routeConfig';

const { Header } = Layout;
const { Option } = Select;

const MainHeader = ({
    collapsed,
    pageTitle,
    parentRoute,
    isLoggedIn,
    user,
    userMenuItems,
    onMobileMenuClick,
    isMobile,
    role,
}) => {
    const navigate = useNavigate();
    const { t, i18n } = useTranslation();

    const backButtonStyles = {
        wrapper: {
            display: 'flex',
            alignItems: 'center',
            padding: '8px',
            cursor: 'pointer',
            color: '#8c8c8c',
            transition: 'all 0.3s ease',
            borderRadius: '6px',
            backgroundColor: 'transparent',
        },
        icon: {
            fontSize: '20px',
            display: 'flex',
            alignItems: 'center',
        },
        text: {
            fontSize: '16px',
            fontWeight: 500,
            fontFamily: i18n.language === 'zh-HK' ? "'Noto Sans TC', sans-serif" : "'Poppins', sans-serif",
            marginLeft: '8px',
        }
    };

    const handleLanguageChange = (value) => {
        i18n.changeLanguage(value);
    };

    const getParentTitle = (parentRoute) => {
        if (!parentRoute) return '';
        const parentInfo = findRouteMetaInfo(parentRoute);
        return parentInfo?.translationKey ? t(parentInfo.translationKey) : '';
    };

    return (
        <Header
            style={{
                background: '#fff',
                padding: '0 24px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                position: 'fixed',
                width: isMobile ? '100%' : `calc(100% - ${collapsed ? 80 : 250}px)`,
                zIndex: 1000,
                height: '72px',
                lineHeight: 'normal'
            }}
        >
            {/* Left Section */}
            <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '8px'
            }}>
                {isMobile && (
                    <Button
                        type="text"
                        icon={<MenuOutlined />}
                        onClick={onMobileMenuClick}
                        style={{
                            fontSize: '18px',
                        }}
                    />
                )}
                {parentRoute && (
                    <div
                        style={backButtonStyles.wrapper}
                        onClick={() => navigate(parentRoute)}
                        onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#f5f5f5';
                            e.currentTarget.style.color = '#595959';
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                            e.currentTarget.style.color = '#8c8c8c';
                        }}
                    >
                        <Icon
                            icon="mingcute:arrow-left-line"
                            style={backButtonStyles.icon}
                        />
                        {!isMobile && (
                            <Typography.Text style={backButtonStyles.text}>
                                {getParentTitle(parentRoute)}
                            </Typography.Text>
                        )}
                    </div>
                )}
            </div>

            {/* Center Section - Page Title */}
            <Typography.Title
                level={3}
                style={{
                    margin: 0,
                    padding: 0,
                    fontFamily: i18n.language === 'zh-HK' ? "'Noto Sans TC', sans-serif" : "'Poppins', sans-serif",
                    fontSize: isMobile ? '20px' : '28px',
                    fontWeight: 600,
                    color: '#262626',
                    textAlign: isMobile ? 'right' : 'center',
                    letterSpacing: i18n.language === 'zh-HK' ? '2px' : '0.5px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    flex: 1
                }}
            >
                {pageTitle}
            </Typography.Title>

            {/* Right Section */}
            <div className="flex items-center flex-none gap-4 py-2">
                {!isMobile && (
                    <>
                        <Select
                            value={i18n.language}
                            onChange={handleLanguageChange}
                            size="middle"
                        >
                            <Option value="en">English</Option>
                            <Option value="zh-HK">繁體中文</Option>
                        </Select>

                        {!isLoggedIn && (
                            <Space size={8}>
                                <Button 
                                    type="primary" 
                                    onClick={() => navigate("/login")}
                                    size="middle"
                                >
                                    {t('mainLayout.confirmLogout.buttons.signIn')}
                                </Button>
                            </Space>
                        )}

                        {isLoggedIn && (
                            <Dropdown
                                menu={{
                                    items: userMenuItems,
                                }}
                                arrow={true}
                                placement="bottomRight"
                            >
                                <Space size={12}>
                                    <Avatar 
                                        style={{ 
                                            backgroundColor: '#87d068',
                                            width: 32,
                                            height: 32,
                                        }} 
                                        icon={<UserOutlined />} 
                                    />
                                    <div style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'flex-start'
                                    }}>
                                        <Typography.Text strong style={{ fontSize: '14px', lineHeight: '1.2' }}>
                                            {user?.display_name || 'User'}
                                        </Typography.Text>
                                        <Typography.Text type="secondary" style={{ fontSize: '12px', lineHeight: '1.2' }}>
                                            {t(`mainLayout.roles.${role || 'user'}`)}
                                        </Typography.Text>
                                    </div>
                                </Space>
                            </Dropdown>
                        )}
                    </>
                )}
            </div>
        </Header>
    );
};

export default MainHeader; 