package authn

import (
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/utils"
	"database/sql"
	"errors"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// RefreshTokenRequest defines the structure for the refresh token request.
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// RefreshTokenResponse defines the structure for the refresh token response.
type RefreshTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"` // Usually "Bearer"
}

// RefreshTokenHandler godoc
// @Summary Refresh JWT tokens
// @Description Issues new access and refresh tokens using a valid refresh token.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body RefreshTokenRequest true "Refresh token"
// @Success 200 {object} RefreshTokenResponse "New access token, new refresh token, and token type"
// @Failure 400 {object} map[string]string "Invalid request payload or Refresh token is required"
// @Failure 401 {object} map[string]string "Invalid or expired refresh token or Refresh token expired"
// @Failure 500 {object} map[string]string "Could not process token due to server configuration error or Failed to process refresh token or Failed to generate new tokens"
// @Router /authn/token/refresh [post]
func (s *AuthnService) RefreshTokenHandler(c echo.Context) error {
	ctx := c.Request().Context()

	req := new(RefreshTokenRequest)
	if err := c.Bind(req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("RefreshTokenHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}
	// It's good practice to validate the request with the validator if Echo is configured with one.
	// Assuming c.Validate(req) is available and works (requires a validator to be registered with Echo instance).
	if err := c.Validate(req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("RefreshTokenHandler: Validation failed for refresh token request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Refresh token is required"})
	}

	// 1. Parse and validate the incoming refresh token JWT itself.
	jwtConf, err := token.GetJWTConfig()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("RefreshTokenHandler: Failed to get JWT config")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Could not process token due to server configuration error"})
	}

	claims, err := token.ParseToken(req.RefreshToken, jwtConf.RefreshTokenSecret)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("RefreshTokenHandler: Failed to parse or validate incoming refresh token JWT")
		return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Invalid or expired refresh token"})
	}

	// 2. Hash the incoming token string to look it up in the database.
	hashedIncomingToken := token.HashToken(req.RefreshToken)

	// 3. Fetch the refresh token from the database using its hash.
	storedToken, err := s.Queries.GetRefreshTokenByTokenHash(ctx, hashedIncomingToken)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// Log a minimal amount of the token hash for correlation if needed, but be cautious.
			hashPrefix := ""
			if len(hashedIncomingToken) > 16 {
				hashPrefix = hashedIncomingToken[:16] + "..."
			} else {
				hashPrefix = hashedIncomingToken
			}
			log.Ctx(ctx).Warn().Str("hashed_token_prefix", hashPrefix).Msg("RefreshTokenHandler: Refresh token not found in DB or already used/revoked")
			return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Invalid or expired refresh token"})
		}
		log.Ctx(ctx).Error().Err(err).Msg("RefreshTokenHandler: Failed to get refresh token from DB")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to process refresh token"})
	}

	// 4. Perform additional checks on the stored token data.
	if storedToken.UserID != claims.UserID {
		log.Ctx(ctx).Warn().
			Str("db_user_id", storedToken.UserID.String()).
			Str("claims_user_id", claims.UserID.String()).
			Msg("RefreshTokenHandler: UserID mismatch between DB record and token claims. Potential security issue.")
		// Immediately delete the compromised token from the DB.
		if delErr := s.Queries.DeleteRefreshTokenByTokenHash(ctx, hashedIncomingToken); delErr != nil {
			log.Ctx(ctx).Error().Err(delErr).Str("token_hash_prefix", hashedIncomingToken[:16]).Msg("RefreshTokenHandler: Failed to delete compromised refresh token after UserID mismatch.")
		}
		return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Invalid refresh token"})
	}

	if time.Now().After(storedToken.ExpiresAt) {
		log.Ctx(ctx).Warn().Time("expires_at", storedToken.ExpiresAt).Str("token_id", storedToken.ID.String()).Msg("RefreshTokenHandler: Refresh token is expired.")
		// Token is expired, delete it from DB to prevent reuse and clutter.
		if delErr := s.Queries.DeleteRefreshTokenByTokenHash(ctx, hashedIncomingToken); delErr != nil {
			log.Ctx(ctx).Error().Err(delErr).Str("token_hash_prefix", hashedIncomingToken[:16]).Msg("RefreshTokenHandler: Failed to delete expired refresh token.")
		}
		return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Refresh token expired"})
	}

	// 5. If all checks pass, delete the old refresh token (implementing one-time use).
	err = s.Queries.DeleteRefreshTokenByTokenHash(ctx, hashedIncomingToken)
	if err != nil {
		// If deletion fails, this is a problem. The old token might be reused.
		// Log critically and consider how to handle this. For now, we proceed but log it.
		log.Ctx(ctx).Error().Err(err).Str("token_hash_prefix", hashedIncomingToken[:16]).Msg("RefreshTokenHandler: CRITICAL - Failed to delete used refresh token. It might be possible to reuse it.")
		// Depending on policy, you might want to prevent new token issuance here.
		// For this implementation, we will proceed to issue new tokens but the old one might still be in DB.
		// A more robust solution might involve a transaction for delete + create of new token.
	}

	// 6. Generate a new access token.
	newAccessToken, err := token.GenerateAccessToken(ctx, s.Queries, claims.UserID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", claims.UserID.String()).Msg("RefreshTokenHandler: Failed to generate new access token")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate new tokens"})
	}

	// 7. Generate a new refresh token (this will also store its hash in the DB).
	newRefreshToken, err := token.GenerateRefreshToken(ctx, s.Queries, claims.UserID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", claims.UserID.String()).Msg("RefreshTokenHandler: Failed to generate new refresh token")
		// At this point, the old refresh token was deleted (or attempted to be deleted).
		// Failing to issue a new one leaves the user without a refresh token.
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate new tokens"})
	}

	log.Ctx(ctx).Info().Str("user_id", claims.UserID.String()).Msg("Refresh token successfully exchanged for a new token pair.")

	response := RefreshTokenResponse{
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
		TokenType:    "Bearer",
	}

	return c.JSON(http.StatusOK, response)
}

// LogoutHandler godoc
// @Summary Logout user
// @Description Invalidates the user's current refresh token.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body payloads.LogoutRequestPayload true "Refresh token to invalidate"
// @Success 204 "No Content - Logout successful"
// @Failure 400 {object} utils.ErrorResponse "Invalid request format or Invalid request data"
// @Failure 500 {object} utils.ErrorResponse "An error occurred during logout."
// @Router /authn/logout [post]
func (s *AuthnService) LogoutHandler(c echo.Context) error {
	ctx := c.Request().Context()
	logger := log.Ctx(ctx)

	var req payloads.LogoutRequestPayload

	// Step 1: Bind the request payload
	if err := c.Bind(&req); err != nil {
		logger.Warn().Err(err).Msg("LogoutHandler: Failed to bind request payload")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request format", err)
	}

	// Step 2: Validate the bound payload
	if err := c.Validate(&req); err != nil {
		logger.Warn().Err(err).Msg("LogoutHandler: Validation failed for request payload")
		// Providing the err directly to HandleError can expose detailed validation messages, which is often desired.
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request data", err)
	}

	// The JWTAuthMiddleware should have already validated the access token.
	// Optional: Extract UserID from access token claims for logging or audit if needed.
	// claims, ok := c.Get("user_claims").(*token.Claims) // Example: if middleware stores claims in context
	// if ok && claims != nil {
	// 	logger.Info().Str("userID", claims.UserID.String()).Msg("Logout requested by user")
	// } else {
	//  // This case should ideally not happen if JWTAuthMiddleware is strict.
	//  logger.Warn().Msg("LogoutHandler: User claims not found in context, though JWTAuthMiddleware should have run.")
	// }

	err := s.Logout(ctx, req.RefreshToken) // Calling the Logout method we defined on AuthnService
	if err != nil {
		// The s.Logout method already logs details and handles pgx.ErrNoRows gracefully.
		return utils.HandleError(c, http.StatusInternalServerError, "An error occurred during logout.", err)
	}

	logger.Info().Msg("LogoutHandler: User successfully logged out, refresh token invalidated.")
	return c.NoContent(http.StatusNoContent)
}
