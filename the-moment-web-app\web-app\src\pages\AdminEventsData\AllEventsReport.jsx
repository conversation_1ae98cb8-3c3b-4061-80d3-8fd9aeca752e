import React, { useState } from 'react';
import { Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import { PieChartOutlined, AppstoreOutlined } from '@ant-design/icons';
import EventsTabContent from './components/AdminEventsListTab';
import ReportsTabContent from './components/AllEventsReportsTab';

const AllEventsReportPage = () => {
    const [activeKey, setActiveKey] = useState('events');
    const { t } = useTranslation();

    const handleTabChange = (key) => {
        setActiveKey(key);
    };

    const items = [
        {
            key: 'events',
            icon: <AppstoreOutlined />,
            label: t('adminEvents.report.tabs.allEvents'),
            children: <EventsTabContent isActive={activeKey === 'events'} />
        },
        {
            key: 'reports',
            icon: <PieChartOutlined />,
            label: t('adminEvents.report.tabs.reports'),
            children: (
                <ReportsTabContent 
                    isActive={activeKey === 'reports'} 
                />
            )
        }
    ];

    return (
        <Tabs
            activeKey={activeKey}
            onChange={handleTabChange}
            items={items}
            style={{ padding: '10px 24px 42px' }}
        />
    );
};

export default AllEventsReportPage;
