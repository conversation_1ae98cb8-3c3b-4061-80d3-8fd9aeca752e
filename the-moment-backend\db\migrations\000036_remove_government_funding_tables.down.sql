-- Create the table for government funding types
CREATE TABLE government_funding_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    slug TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE government_funding_types IS 'Stores different types of government funding sources for events.';
COMMENT ON COLUMN government_funding_types.slug IS 'A unique, URL-friendly identifier for the funding type.';
COMMENT ON COLUMN government_funding_types.name IS 'The display name of the government funding type.';
COMMENT ON COLUMN government_funding_types.description IS 'Optional description of the funding type.';

-- Create the join table between events and government funding types
CREATE TABLE event_government_funding_types (
    event_id UUID NOT NULL,
    funding_type_id UUID NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (event_id, funding_type_id),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (funding_type_id) REFERENCES government_funding_types(id) ON DELETE CASCADE
);

COMMENT ON TABLE event_government_funding_types IS 'Join table linking events to their government funding sources.';

-- Seed the government funding types table
INSERT INTO government_funding_types (id, slug, name, description) VALUES
('f1f1f1f1-0001-4000-8000-000000000001', 'gfp-green', 'Government-funded programme (Green)', 'Placeholder for specific details about GFP Green'),
('f1f1f1f1-0002-4000-8000-000000000002', 'gfp-red', 'Government-funded programme (Red)', 'Placeholder for specific details about GFP Red'),
('f1f1f1f1-0003-4000-8000-000000000003', 'ydc', 'Youth Development Commission', 'Funding from the Youth Development Commission'),
('f1f1f1f1-0004-4000-8000-000000000004', 'govhkyouth', '中華人民共和國香港特別行政區政府民政及青年事務局', 'Funding from the Home and Youth Affairs Bureau HKSAR');

-- Remove the old boolean column from the events table
ALTER TABLE events DROP COLUMN is_government_funded; 