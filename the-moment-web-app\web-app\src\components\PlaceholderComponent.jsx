import React from 'react';
import { Result } from 'antd';
import { useTranslation } from 'react-i18next';

const PlaceholderComponent = ({ pageName }) => {
  const { t } = useTranslation();
  const featureName = pageName || t('common.thisPage', 'This page');
  return (
    <Result
      status="info"
      title={t('common.workInProgress', { feature: featureName })}
      subTitle={t('common.comeBackLater', 'Please check back later for updates.')}
    />
  );
};

export default PlaceholderComponent; 