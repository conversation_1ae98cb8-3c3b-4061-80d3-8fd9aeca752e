-- Add document_id to hk_youth_plus table, making it required
ALTER TABLE verification_hk_youth_plus
ADD COLUMN document_id UUID REFERENCES verification_documents(id) ON DELETE SET NULL;

-- Making it NOT NULL. If there's pre-existing data without a document_id (which shouldn't happen for new logic but could for old),
-- this migration might require a default value or an update strategy for those old rows first.
-- For now, assuming this is for new data or that old data is handled/migrated separately if necessary.
ALTER TABLE verification_hk_youth_plus
ALTER COLUMN document_id SET NOT NULL; 