-- name: Get<PERSON><PERSON><PERSON>yPhone :one
SELECT *
FROM users
WHERE phone = $1 LIMIT 1;

-- name: GetUserByEmail :one
SELECT *
FROM users
WHERE email = $1 LIMIT 1;

-- name: GetUserByID :one
SELECT *
FROM users
WHERE id = $1 LIMIT 1;

-- name: <PERSON><PERSON><PERSON><PERSON>WithPhone :one
INSERT INTO users (
    display_name,
    phone,
    phone_verified_at,
    -- email, -- Can be added later via profile update
    -- hashed_password, -- Not set during phone OTP registration
    interface_language,
    communication_language,
    enable_app_notifications,
    enable_whatsapp_notifications,
    enable_sms_notifications,
    enable_email_notifications,
    phone_otp_channel
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
)
RETURNING *;

-- name: CreateStaffUserWithEmailPassword :one
INSERT INTO users (
    display_name,
    email,
    email_verified_at, -- Assume verified for testing endpoint
    hashed_password,
    interface_language,
    communication_language,
    enable_app_notifications,
    enable_whatsapp_notifications,
    enable_sms_notifications,
    enable_email_notifications,
    phone_otp_channel, -- Can be default, not primary for this user type
    role
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11,
    'admin'
)
RETURNING *;

-- name: SetUserPhoneVerified :one
UPDATE users
SET phone_verified_at = $2, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdateUserDisplayName :one
UPDATE users
SET display_name = $2, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdateUserNotificationSettings :one
UPDATE users
SET 
    enable_app_notifications = $2,
    enable_whatsapp_notifications = $3,
    enable_sms_notifications = $4,
    enable_email_notifications = $5,
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdateUserLanguagePreferences :one
UPDATE users
SET 
    interface_language = $2,
    communication_language = $3,
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdateUserPhoneAndVerificationStatus :one
UPDATE users
SET 
    phone = $2,
    phone_verified_at = NULL, -- Needs re-verification
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdateUserPhoneAndMarkVerified :one
UPDATE users
SET 
    phone = $2,
    phone_verified_at = NOW(), -- Mark as verified
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdateUserEmailAndVerificationStatus :one
UPDATE users
SET
    email = $2,
    email_verified_at = $3, -- Assume verified for testing endpoint
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: GetUserRegistrationDate :one
SELECT created_at AS registration_date FROM users
WHERE id = $1;

-- name: GetUserTotalAttendedEvents :one
SELECT
    COUNT(er.id) AS total_attended_events
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
AND er.status = 'attended';

-- name: GetUserTotalVolunteerEvents :one
SELECT COUNT(*) AS total_volunteer_events
FROM event_volunteer_applications eva
WHERE eva.user_id = $1
AND eva.status = 'approved'; -- or whatever status indicates confirmed volunteering

-- name: GetMonthlyAttendedEvents :many
SELECT
    TO_CHAR(e.start_time, 'YYYY-MM') AS month,
    COUNT(er.id) AS count
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
AND er.status = 'attended'
AND e.start_time >= NOW() - INTERVAL '6 months'
GROUP BY month
ORDER BY month DESC;

-- name: GetTopAttendedEventTags :many
SELECT
    et.name_en,
    et.name_zh_hk,
    et.name_zh_cn,
    COUNT(er.id) AS count
FROM event_registrations er
JOIN events e ON er.event_id = e.id
JOIN event_event_tags eet ON e.id = eet.event_id
JOIN event_tags et ON eet.event_tag_id = et.id
WHERE er.user_id = $1
AND er.status = 'attended'
AND e.start_time >= NOW() - INTERVAL '6 months'
GROUP BY et.id, et.name_en, et.name_zh_hk, et.name_zh_cn
ORDER BY count DESC
LIMIT 5;

-- name: ListUsersFiltered :many
SELECT
    u.id,
    u.display_name,
    u.email,
    u.email_verified_at,
    u.phone,
    u.phone_verified_at,
    u.profile_picture_url AS avatar_url, -- Use schema name and alias
    u.role,
    u.created_at,
    u.updated_at,
    COUNT(*) OVER() AS total_count
FROM
    users u
WHERE
    (sqlc.narg(search_query)::TEXT IS NULL OR u.display_name ILIKE sqlc.narg(search_query)::TEXT OR u.email ILIKE sqlc.narg(search_query)::TEXT)
    AND (sqlc.narg(email)::TEXT IS NULL OR u.email = sqlc.narg(email)::TEXT)
    AND (sqlc.narg(role_filter)::TEXT IS NULL OR u.role = sqlc.narg(role_filter)::user_role)
    -- New condition for filtering admins by organizations owned by actor_org_owner_id
    AND (
        sqlc.narg(actor_org_owner_id)::UUID IS NULL OR -- If no owner ID, this condition is true
        sqlc.narg(role_filter)::TEXT != 'admin' OR   -- If not filtering for admins, this condition is true
        EXISTS (                                     -- Otherwise, check if the user 'u' is an admin in an org owned by actor_org_owner_id
            SELECT 1
            FROM user_organization_memberships uom_member
            JOIN user_organization_memberships uom_owner ON uom_member.organization_id = uom_owner.organization_id
            WHERE uom_member.user_id = u.id -- The user 'u' we are considering
            AND uom_owner.user_id = sqlc.narg(actor_org_owner_id)::UUID -- The actor who is an owner
            AND uom_owner.role = 'owner'
            AND uom_member.role = 'admin' -- Ensure the user 'u' is an admin in that org (redundant if role_filter is 'admin', but good for clarity)
        )
    )
ORDER BY
    CASE WHEN sqlc.narg(sort_by)::TEXT = 'display_name' AND sqlc.narg(sort_order)::TEXT = 'asc' THEN u.display_name END ASC,
    CASE WHEN sqlc.narg(sort_by)::TEXT = 'display_name' AND sqlc.narg(sort_order)::TEXT = 'desc' THEN u.display_name END DESC,
    CASE WHEN sqlc.narg(sort_by)::TEXT = 'email' AND sqlc.narg(sort_order)::TEXT = 'asc' THEN u.email END ASC,
    CASE WHEN sqlc.narg(sort_by)::TEXT = 'email' AND sqlc.narg(sort_order)::TEXT = 'desc' THEN u.email END DESC,
    CASE WHEN sqlc.narg(sort_by)::TEXT = 'created_at' AND sqlc.narg(sort_order)::TEXT = 'asc' THEN u.created_at END ASC,
    CASE WHEN sqlc.narg(sort_by)::TEXT = 'created_at' AND sqlc.narg(sort_order)::TEXT = 'desc' THEN u.created_at END DESC,
    u.created_at DESC -- Default sort
LIMIT sqlc.arg(page_size)
OFFSET sqlc.arg(page_offset);

-- Note: The CountUsers query is removed as ListUsersFiltered now includes COUNT(*) OVER()

-- name: DeleteUser :exec
DELETE FROM users
WHERE id = $1;