# Issues to Fix

## Startup Issues

1. **PDF Export Font Support**: Our pages require PDF/CSV/PNG export functionality with frontend-based export. The PDF library used is "jspdf-autotable": "^3.8.4" (`web-app/src/utils/exportUtils.js`), which lacks Chinese font support. We added Traditional Chinese support in `web-app/src/assets/fonts/notosanstc-bold.js`, but this appears to cause "maximum depth reached" errors on startup. The issue resolves after page refresh.

## API Related Modifications

1. **Home Page Issues** (`web-app/src/pages/Home.js`):
   
   1. **Image Loading Logic**: The image loading logic for posts/events is inconsistent. Please ensure the backend returns a unified cover image format (starting with http://..) and doesn't return unnecessary fields.
      - **Posts section**: Only requires cover image, author, and datetime
      - **Events section**: Only requires cover image, location, and datetime (start, end)
   
   2. **Upcoming Events Countdown Logic**: The countdown calculation for upcoming events needs clarification on whether to use start time or end time to define "upcoming", otherwise countdown display logic becomes confusing.

     <img src="./images/Screenshot.png" alt="Screenshot 2025-06-18 at 20.21.43" style="zoom:50%;" />

2. **Filter-Related Pages**:
   
   Events list - user view (`web-app/src/pages/EventsPage/EventList.js`) and admin view (`web-app/src/pages/AdminEventsData/components/AdminEventsListTab.js`) use the same event Filter component (`web-app/src/components/EventFilter.js`).

   1. **Admin Event List Filter Confusion**: In the admin view event list, the usage of event filter and table sort/filter is confusing. The EventFilter component performs backend filtering, while the Antd table filter/sort performs frontend filtering. This needs to be modified to a more user-friendly approach.
   
   2. **Similar Issues in Other Admin Lists**: The same issue applies to admin post list (`web-app/src/pages/AdminPostsData/AdminPostsListTab.js`) and admin resource list (`web-app/src/pages/AdminResourcesData/AdminResourcesListTab.js`).

3. **User Identity Document Approval Interface** (`web-app/src/pages/UserApproval/ProofDetails.jsx`):
   
   1. **Image Loading Issues**: This interface is used for reviewing user identity documents. There's an issue where switching tabs causes images to break (browser cannot cache too many blob files). Please ensure the backend only returns file paths.

4. **User Identity Information Retrieval - Three Interfaces**:
   
   1. **User Verification List Interface** (`web-app/src/pages/UserApproval/UserVerificationList.jsx`): This is the admin interface for reviewing all user information (switching between pending/approved/rejected states). The API usage for pending/approved/rejected states is inconsistent. Reference (`web-app/src/hooks/useVerificationQueries.js`) shows separate fetching for pending and approved states. The backend needs to provide consistent APIs before unified usage can be implemented.
   
   2. **Admin Participant Information Interface** (`web-app/src/pages/AdminEventsData/components/HistoricalDataTab.js`): This interface allows admins to view all participant information and verification history. The API fetching logic here is the most problematic. Due to the backend lacking a dedicated API for viewing individual user verification history, this is hard-coded: it first fetches all users' verification history (same as UserVerificationList), then maps the corresponding user ID for display. This is a critical issue.
   
   3. **User Management Interface** (`web-app/src/pages/UserManagement/UserManagementPage.js`): Uses the same problematic logic and needs to be fixed.