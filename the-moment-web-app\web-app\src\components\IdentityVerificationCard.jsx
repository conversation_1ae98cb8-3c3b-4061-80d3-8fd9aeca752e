import React from 'react';
import { Card, Typography, Space, Tag, Button, Alert, message } from 'antd';
import { CheckCircleFilled, ClockCircleFilled, CloseCircleFilled, ExclamationCircleFilled, RightOutlined, LockOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const { Text } = Typography;
const IdentityVerificationCard = ({ 
    title,
    status,
    comment,
    type = 'identity', // 'identity' or 'address'
    identityStatus = 'unverified', // Add this prop to check identity status for address verification
    verificationPath = null, // Custom path for verification if needed
    hideVerifyButton = false // New prop to hide verification button
}) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    
    // Handle null or undefined status as unverified
    const normalizedStatus = status === null || status === undefined ? 'unverified' : status;
    
    const getStatusTag = () => {
        switch (normalizedStatus) {
            case 'approved':
                return <Tag icon={<CheckCircleFilled />} color="success">
                    {t('identityVerificationCard.status.approved')}
                </Tag>;
            case 'pending':
                return <Tag icon={<ClockCircleFilled />} color="processing">
                    {t('identityVerificationCard.status.pending')}
                </Tag>;
            case 'rejected':
                return <Tag icon={<CloseCircleFilled />} color="error">
                    {t('identityVerificationCard.status.rejected')}
                </Tag>;
            case 'unverified':
                return <Tag icon={<ExclamationCircleFilled />} color="warning">
                    {t('identityVerificationCard.status.unverified', 'Not Applied')}
                </Tag>;
            default:
                return <Tag icon={<ExclamationCircleFilled />} color="warning">
                    {t('identityVerificationCard.status.error')}
                </Tag>;
        }
    };

    const getStatusMessage = () => {
        switch (normalizedStatus) {
            case 'approved':
                return (
                    <Text type="secondary">
                        {t('userSettings.identityVerification.messages.approved')}
                    </Text>
                );
            case 'pending':
                return (
                    <Text type="secondary">
                        {t('userSettings.identityVerification.messages.pending')}
                    </Text>
                );
            case 'unverified':
                return (
                    <Text type="secondary">
                        {t('userSettings.identityVerification.messages.unverified')}
                    </Text>
                );
            default:
                return null;
        }
    };

    const handleVerificationClick = () => {
        // Use custom path if provided, otherwise use default paths
        const path = verificationPath || (type === 'identity' ? '/register/identity-verification' : '/register/address-verification');
        navigate(path);
    };

    // Check if identity verification is required and available for address verification
    const canVerifyAddress = type === 'identity' || ['approved', 'pending'].includes(normalizedStatus);

    // Get appropriate alert type based on verification status
    const getAlertType = () => {
        switch (normalizedStatus) {
            case 'approved':
                return 'success';
            case 'pending':
                return 'info';
            case 'rejected':
                return 'error';
            default:
                return 'warning';
        }
    };

    return (
        <Card className="rounded-lg border border-gray-200">
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {/* Status Header */}
                <div className="flex items-center justify-between">
                    <Space size={8} align="center">
                        <Text strong style={{ fontSize: '16px', color: '#1f1f1f' }}>
                            {t('userSettings.identityVerification.labels.status')}：
                        </Text>
                        {getStatusTag()}
                    </Space>
                    
                    {/* Verification Button or Message */}
                    {!hideVerifyButton && (normalizedStatus === 'rejected' || normalizedStatus === 'unverified') && (
                        <>
                            {canVerifyAddress ? (
                                <Button
                                    type="text"
                                    onClick={handleVerificationClick}
                                    className="flex items-center text-primary hover:text-primary-dark"
                                >
                                    {t('userSettings.identityVerification.buttons.verifyNow')} <RightOutlined className="ml-1" />
                                </Button>
                            ) : (
                                <Button
                                    type="text"
                                    onClick={() => message.info(t('userSettings.identityVerification.messages.identityRequired'))}
                                    className="flex items-center text-gray-500 hover:text-gray-700"
                                >
                                    {t('userSettings.identityVerification.buttons.verifyNow')} <RightOutlined className="ml-1" />
                                </Button>
                            )}
                        </>
                    )}
                </div>

                <div>
                    {/* Status Message */}
                    {getStatusMessage()}
                    
                    {/* Admin Notes (Comment) - Display for any status if comment exists */}
                    {comment && (
                        <Alert
                            type={getAlertType()}
                            message={t('identityVerificationCard.messages.comment')}
                            description={comment}
                            className="mt-3"
                            showIcon
                        />
                    )}
                </div>
            </Space>
        </Card>
    );
};

export default IdentityVerificationCard;