package services

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/locales"
	_ "Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/twilio_service"
	_ "Membership-SAAS-System-Backend/internal/utils"
	"errors"

	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/jackc/pgx/v5"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// Notification type constants
const (
	NotificationTypeVerificationUpdate            = "verification_status_update"       // Existing
	NotificationTypeVerificationApproved          = "verification_approved"            // New
	NotificationTypeVolunteerApplicationApproved  = "volunteer_application_approved"   // New
	NotificationTypeEventRegistrationFromWaitlist = "event_registration_from_waitlist" // New
	NotificationTypeEventReminder1Day             = "event_reminder_1_day"             // New
	NotificationTypeEventAdminRegistrationUpdate  = "event_admin_registration_update"  // New
	// Add other existing constants if they are defined elsewhere, or define all here.
	// For example:
	// NotificationTypeGeneralMessage = "general_message"
	// NotificationTypePhoneChangeSuccess = "phone_change_success"
)

// Notification represents the structure of a notification message.
type Notification struct {
	Type    string      `json:"type"`    // e.g., "verification_update", "phone_change_success", "general_message"
	Payload interface{} `json:"payload"` // Data specific to the notification type
}

// wsClient represents a connected WebSocket client.
type wsClient struct {
	conn   *websocket.Conn
	userID uuid.UUID
	send   chan []byte // Buffered channel for outbound messages
}

// NotificationService handles real-time notifications via WebSockets.
type NotificationService interface {
	Register(conn *websocket.Conn, userID uuid.UUID)
	Unregister(conn *websocket.Conn)
	SendToUser(ctx context.Context, userID uuid.UUID, messageType string, payload map[string]interface{}) error
	ServeWs(c echo.Context) error
	// Event-related notifications
	SendEventPromotionNotification(ctx context.Context, user db.User, event db.Event) error
	SendEventCancellationConfirmation(ctx context.Context, user db.User, event db.Event) error
	SendRegistrationStatusUpdate(ctx context.Context, user db.User, event db.Event, newStatus string, adminNotes *string) error
}

// Hub maintains the set of active clients and broadcasts messages.
type Hub struct {
	// Registered clients. Maps user ID to a map of connections.
	clients map[uuid.UUID]map[*websocket.Conn]bool

	// Mutex to protect concurrent access to clients map.
	mu sync.Mutex
}

func NewHub() *Hub {
	return &Hub{
		clients: make(map[uuid.UUID]map[*websocket.Conn]bool),
	}
}

type notificationService struct {
	hub          *Hub
	twilioClient *twilio_service.TwilioService
	dbQueries    *db.Queries
}

// NewNotificationService creates a new NotificationService.
func NewNotificationService(twilioSvc *twilio_service.TwilioService, queries *db.Queries) NotificationService {
	return &notificationService{
		hub:          NewHub(),
		twilioClient: twilioSvc,
		dbQueries:    queries,
	}
}

// Register adds a new client connection to the hub.
func (h *Hub) Register(conn *websocket.Conn, userID uuid.UUID) {
	h.mu.Lock()
	defer h.mu.Unlock()
	if _, ok := h.clients[userID]; !ok {
		h.clients[userID] = make(map[*websocket.Conn]bool)
	}
	h.clients[userID][conn] = true
	log.Info().Str("userID", userID.String()).Msg("WebSocket client registered")
}

// Unregister removes a client connection from the hub.
func (h *Hub) Unregister(conn *websocket.Conn) {
	h.mu.Lock()
	defer h.mu.Unlock()
	// Find the user ID for this connection to remove it efficiently
	var userIDToRemove uuid.UUID
	var connToRemove *websocket.Conn

OuterLoop: // Label for breaking out of nested loops
	for userID, connections := range h.clients {
		if _, ok := connections[conn]; ok {
			userIDToRemove = userID
			connToRemove = conn
			break OuterLoop
		}
	}

	if connToRemove != nil {
		delete(h.clients[userIDToRemove], connToRemove)
		// If the user has no more connections, remove the user entry entirely
		if len(h.clients[userIDToRemove]) == 0 {
			delete(h.clients, userIDToRemove)
		}
		conn.Close() // Close the connection
		log.Info().Str("userID", userIDToRemove.String()).Msg("WebSocket client unregistered")
	}
}

// BroadcastToUser sends a message to all active WebSocket connections for a specific user.
// This method is intended to be called by the notificationService.
func (h *Hub) BroadcastToUser(userID uuid.UUID, message interface{}) error {
	h.mu.Lock()
	defer h.mu.Unlock()

	userConnections, ok := h.clients[userID]
	if !ok {
		log.Warn().Str("userID", userID.String()).Msg("No active WebSocket connections found for user to broadcast message")
		return nil // Not necessarily an error if user is offline
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal message to JSON for WebSocket broadcast")
		return err
	}

	log.Info().Str("userID", userID.String()).Int("connections", len(userConnections)).RawJSON("message", jsonData).Msg("Attempting to broadcast WebSocket message to user connections")

	failedConns := []*websocket.Conn{}
	for conn := range userConnections {
		if err := conn.WriteMessage(websocket.TextMessage, jsonData); err != nil {
			log.Warn().Err(err).Str("userID", userID.String()).Msg("Failed to write message to WebSocket connection, marking for removal")
			failedConns = append(failedConns, conn)
		}
	}

	// Clean up failed connections after sending attempts
	for _, conn := range failedConns {
		// Check if user still has connections before trying to delete from map
		if connsForUser, userExists := h.clients[userID]; userExists {
			delete(connsForUser, conn)
			if len(connsForUser) == 0 { // If last connection for this user was removed
				delete(h.clients, userID)
			}
		}
		conn.Close() // Close the failed connection
	}

	return nil
}

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow all connections for now, adjust in production
		return true
	},
}

// ServeWs godoc
// @Summary Upgrade connection to WebSocket for real-time notifications
// @Description Establishes a WebSocket connection. Requires authentication via token in query param or valid Bearer header initially.
// @Tags WebSocket, Real-time
// @Param token query string false "Authentication token (alternative to Bearer header for WS)"
// @Success 101 {string} string "Switching Protocols"
// @Failure 400 {object} _.ErrorResponse "Bad Request (e.g. missing auth or invalid token)"
// @Failure 401 {object} _.ErrorResponse "Unauthorized"
// @Failure 500 {object} _.ErrorResponse "Internal server error during upgrade"
// @Security ApiKeyAuth
// @Router /ws [get]
func (s *notificationService) ServeWs(c echo.Context) error {
	// Authentication is handled by the jwtAuth middleware before this handler is called.
	// We should have claims available in the context.
	claims, ok := c.Get("user").(*token.AppClaims)
	if !ok || claims == nil {
		log.Ctx(c.Request().Context()).Warn().Msg("WebSocket connection attempt without valid claims after middleware")
		return echo.NewHTTPError(http.StatusUnauthorized, "Invalid or missing authentication claims")
	}
	userID := claims.UserID

	ws, err := upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("userID", userID.String()).Msg("Failed to upgrade connection to WebSocket")
		// Don't return echo error here, Upgrade writes the response header on failure
		return nil
	}
	defer ws.Close() // Ensure connection is closed when function returns

	logger := log.Ctx(c.Request().Context()).With().Str("userID", userID.String()).Str("remoteAddr", ws.RemoteAddr().String()).Logger()
	logger.Info().Msg("WebSocket connection established")

	s.Register(ws, userID)
	defer s.Unregister(ws) // Ensure unregistration when function returns

	// Keep the connection alive and handle potential client messages (like pings)
	for {
		_, message, err := ws.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logger.Warn().Err(err).Msg("WebSocket closed unexpectedly")
			} else {
				logger.Info().Msg("WebSocket connection closed normally")
			}
			break // Exit loop on read error/close
		}
		logger.Debug().Str("message", string(message)).Msg("Received WebSocket message")
		// Handle client messages if necessary (e.g., pong response to ping)
		// if string(message) == "ping" {
		// 	if err := ws.WriteMessage(websocket.PongMessage, []byte{}); err != nil {
		// 		logger.Error().Err(err).Msg("Failed to send pong")
		// 		break
		// 	}
		// }
	}

	logger.Info().Msg("WebSocket connection handler finished")
	return nil
}

// Register adds a new client connection to the hub.
func (s *notificationService) Register(conn *websocket.Conn, userID uuid.UUID) {
	s.hub.Register(conn, userID)
}

// Unregister removes a client connection from the hub.
func (s *notificationService) Unregister(conn *websocket.Conn) {
	s.hub.Unregister(conn)
}

// SendToUser sends a message to all active connections for a specific user via WebSocket
// and also sends an SMS/WhatsApp notification if configured and applicable.
func (s *notificationService) SendToUser(ctx context.Context, userID uuid.UUID, messageType string, payload map[string]interface{}) error {
	logger := log.Ctx(ctx).With().Str("userID", userID.String()).Str("messageType", messageType).Logger()

	// Read the environment variable to force SMS notifications
	forceSMSEnv := os.Getenv("FORCE_NOTIFICATIONS_TO_SMS")
	forceSMS := forceSMSEnv == "true" // Or any other affirmative value like "1"

	// 0. Fetch user details including preferences
	user, err := s.dbQueries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Msg("User not found, cannot send notification.")
			return nil // Or return specific error
		}
		logger.Error().Err(err).Msg("Failed to fetch user for notification.")
		return err
	}

	preferredLang := user.CommunicationLanguage
	if preferredLang == "" {
		preferredLang = locales.LangEN // Default to English
		logger.Debug().Msg("User communication_language is empty, defaulting to English.")
	}
	logger = logger.With().Str("preferredLang", preferredLang).Logger()

	// 1. Send WebSocket notification
	// Get localized websocket payload. The payload for websocket might be the raw `payload` map,
	// or a specific string if `body_websocket` is defined for the message type.
	// For simplicity, let's assume the client-side can handle the raw payload along with the messageType.
	// If specific localized text is needed for websocket, it should be fetched here.

	// Example: Fetching a specific websocket body if defined
	websocketBody, err := locales.GetMessage(messageType, preferredLang, "body_websocket", payload)
	var wsPayloadToSend interface{}
	if err == nil && websocketBody != "" { // If a specific websocket body is found and not empty
		// If websocketBody is a simple string.
		// If it's a JSON string that needs to be unmarshalled, more handling is needed.
		// For now, let's assume it's a string to be put under a "message" key.
		wsPayloadToSend = map[string]interface{}{
			"message":          websocketBody, // Send the localized string
			"original_payload": payload,       // Optionally send original payload too
		}
	} else {
		// Fallback to sending the original payload if no specific websocket body or error
		wsPayloadToSend = payload
		if err != nil {
			logger.Warn().Err(err).Msg("Failed to get localized 'body_websocket', sending original payload to websocket.")
		}
	}

	wsMessage := Notification{
		Type:    messageType,
		Payload: wsPayloadToSend,
	}
	wsErr := s.hub.BroadcastToUser(userID, wsMessage)
	if wsErr != nil {
		logger.Error().Err(wsErr).Msg("Failed to send WebSocket notification")
		// Continue to attempt Twilio
	} else {
		logger.Info().Msg("WebSocket notification sent successfully.")
	}

	// 2. Twilio notifications (SMS and WhatsApp)
	if s.twilioClient == nil {
		logger.Warn().Msg("Twilio client is not initialized, skipping SMS/WhatsApp notifications.")
		return wsErr // Return WebSocket error if any, or nil
	}

	// Check if user has a phone number
	if user.Phone == nil || *user.Phone == "" {
		logger.Info().Msg("User does not have a phone number, skipping Twilio notifications.")
		return wsErr
	}
	toNumber := *user.Phone

	// Determine if this message is an OTP (these should not be forced to SMS if they are meant for WhatsApp)
	// PLEASE REPLACE "OTP_MESSAGE_TYPE_EXAMPLE_1" and "OTP_MESSAGE_TYPE_EXAMPLE_2" with actual OTP message types
	isOTP := messageType == "OTP_MESSAGE_TYPE_EXAMPLE_1" || messageType == "OTP_MESSAGE_TYPE_EXAMPLE_2"

	// WhatsApp Notification
	// Attempt WhatsApp only if SMS is not forced OR if it's an OTP message (OTP might have its own preferred channel)
	if !forceSMS || isOTP {
		if user.EnableWhatsappNotifications {
			logger.Debug().Msg("Attempting to send WhatsApp notification.")
			templateSID, found := locales.GetTwilioTemplateSID(messageType, preferredLang)
			if found {
				var templateParams map[string]string
				var errParams error

				switch messageType {
				case NotificationTypeVerificationApproved:
					templateParams, errParams = s.prepareTwilioParams(payload, []string{"UserName", "VerificationType", "DocumentDetails", "AdminNotes", "DetailsURL"})
				case NotificationTypeVolunteerApplicationApproved:
					templateParams, errParams = s.prepareTwilioParams(payload, []string{"UserName", "EventName", "EventDetailsURL"})
				case NotificationTypeEventRegistrationFromWaitlist:
					templateParams, errParams = s.prepareTwilioParams(payload, []string{"UserName", "EventName", "EventDetailsURL"})
				case NotificationTypeEventAdminRegistrationUpdate:
					templateParams, errParams = s.prepareTwilioParams(payload, []string{"UserName", "EventName", "NewStatus", "AdminNotes", "EventDetailsURL"})
				case NotificationTypeEventReminder1Day:
					templateParams, errParams = s.prepareTwilioParams(payload, []string{"EventName", "EventTime", "EventLocation", "EventDetailsURL", "UserName"})
				// START_TOKEN_IGNORE
				// IMPORTANT: Add cases for your OTP message types here if they use WhatsApp templates
				// and should not be overridden by FORCE_NOTIFICATIONS_TO_SMS.
				// Example:
				// case "OTP_MESSAGE_TYPE_EXAMPLE_1":
				//  templateParams, errParams = s.prepareTwilioParams(payload, []string{"OTPCode"})
				// END_TOKEN_IGNORE
				default:
					log.Ctx(ctx).Warn().Str("messageType", messageType).Msg("No specific Twilio template parameter mapping defined for this message type for WhatsApp.")
					templateParams = make(map[string]string)
				}

				if errParams != nil {
					logger.Error().Err(errParams).
						Str("messageType", messageType).
						Msg("Failed to prepare Twilio template parameters for WhatsApp")
				} else {
					logger.Info().
						Str("to", *user.Phone).
						Str("templateSID", templateSID).
						Interface("templateParams", templateParams).
						Msg("Attempting to send templated WhatsApp message via Twilio")
					err = s.twilioClient.SendTemplatedMessage(*user.Phone, templateSID, templateParams)
					if err != nil {
						logger.Error().Err(err).Msg("Failed to send templated WhatsApp message via Twilio")
					} else {
						logger.Info().Msg("Templated WhatsApp message sent successfully via Twilio.")
						// If WhatsApp was successful and SMS is not forced, we might not need to send SMS.
						// However, if SMS is forced, we will proceed to send SMS regardless.
						// If it's an OTP, and WhatsApp was successful, we likely don't send SMS.
						if isOTP { // If OTP sent via WhatsApp, skip SMS
							return wsErr // or nil if wsErr is nil
						}
					}
				}
			} else {
				logger.Warn().
					Str("messageType", messageType).
					Str("lang", preferredLang).
					Msg("Twilio Template SID not found for WhatsApp. Skipping WhatsApp notification.")
			}
		} else {
			logger.Info().Msg("WhatsApp notifications disabled for user.")
		}
	} else {
		logger.Info().Str("messageType", messageType).Msg("FORCE_NOTIFICATIONS_TO_SMS is true, and this is not an OTP, so WhatsApp attempt is skipped or will be followed by SMS.")
	}

	// SMS Notification
	// Send SMS if:
	// 1. FORCE_NOTIFICATIONS_TO_SMS is true AND it's not an OTP that was already successfully sent via WhatsApp.
	// OR
	// 2. FORCE_NOTIFICATIONS_TO_SMS is false, AND user has SMS notifications enabled (and WhatsApp was not sent or failed, or is not preferred).
	//    (The existing logic for user.EnableSmsNotifications handles the non-forced case)

	shouldSendSMS := false
	if forceSMS && !isOTP { // If SMS is forced and it's not an OTP
		if user.EnableSmsNotifications { // Still respect user's choice to disable SMS altogether
			logger.Info().Msg("FORCE_NOTIFICATIONS_TO_SMS is true. Attempting to send SMS.")
			shouldSendSMS = true
		} else {
			logger.Info().Msg("FORCE_NOTIFICATIONS_TO_SMS is true, but user has SMS notifications disabled. Skipping SMS.")
		}
	} else if !forceSMS && user.EnableSmsNotifications { // Standard SMS sending logic if not forced
		// This branch is for when SMS is not forced.
		// Original logic might have sent SMS if WhatsApp was disabled or failed.
		// For simplicity now, it just checks EnableSmsNotifications.
		// You might need more complex logic here if SMS should be a fallback to a failed WhatsApp when not forced.
		logger.Debug().Msg("Attempting to send SMS (standard, not forced).")
		shouldSendSMS = true
	} else if isOTP && user.EnableSmsNotifications { // If it IS an OTP, send SMS only if WhatsApp wasn't sent/failed (assuming OTP can go to SMS)
		// This part needs careful consideration: if an OTP is primarily WhatsApp, SMS is a fallback.
		// The current logic sends WhatsApp if enabled, then potentially SMS.
		// If `isOTP` is true and WhatsApp was attempted and failed (or not attempted because disabled), then SMS should be tried.
		// This is complex because the WhatsApp success isn't directly checked here for OTPs before deciding on SMS.
		// Let's assume for OTP, if EnableSmsNotifications is true, we attempt it,
		// relying on upstream logic (e.g. in an AuthService) to perhaps only call SendToUser for SMS for OTPs if WhatsApp is not viable.
		// Or, the OTP sending function might call SendToUser twice, once for whatsapp, once for sms.
		// For now, if it's an OTP and SMS is enabled, we'll try to send.
		logger.Debug().Msg("Attempting to send SMS for OTP.")
		shouldSendSMS = true
	}

	if shouldSendSMS {
		smsBody, err := locales.GetMessage(messageType, preferredLang, "body_sms", payload)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to get localized SMS body.")
		} else {
			logger.Info().Str("to", toNumber).Msg("Sending SMS.")
			err = s.twilioClient.SendMessage(toNumber, smsBody, "sms") // Assuming channel "sms"
			if err != nil {
				logger.Error().Err(err).Msg("Failed to send SMS message.")
			} else {
				logger.Info().Msg("SMS message sent successfully.")
			}
		}
	} else {
		if !(forceSMS && !user.EnableSmsNotifications) { // Avoid double logging if forceSMS was true but user disabled SMS
			logger.Info().Msg("SMS notification not sent (either not enabled, or forced SMS was off, or it was an OTP handled by WhatsApp).")
		}
	}

	// Return the first error encountered (prefer wsErr if both fail, or any other error)
	// A more sophisticated error aggregation might be needed if multiple errors are critical.
	if wsErr != nil {
		return wsErr
	}
	// If no wsErr, but other errors occurred, this function currently doesn't return them.
	// The last error from SMS/WhatsApp attempts would be lost if not handled.
	// For simplicity, returning wsErr for now as per original structure.
	// A robust implementation should collect and return all significant errors.
	return nil // If wsErr was nil and subsequent errors were logged.
}

// prepareTwilioParams converts a payload map and an ordered list of keys
// into Twilio's map[string]string format (e.g., {"1": "value1", "2": "value2"}).
func (s *notificationService) prepareTwilioParams(payload map[string]interface{}, orderedKeys []string) (map[string]string, error) {
	params := make(map[string]string)
	for i, key := range orderedKeys {
		if val, ok := payload[key]; ok {
			params[fmt.Sprintf("%d", i+1)] = fmt.Sprintf("%v", val)
		} else {
			// This key is expected by the template but missing in the payload.
			// This could be an error, or you could send an empty string or a placeholder.
			// For now, let's log and potentially return an error or skip this param.
			log.Warn().Str("key", key).Msg("Expected Twilio template parameter key missing in payload")
			// params[fmt.Sprintf("%d", i+1)] = "" // Or return error:
			return nil, fmt.Errorf("missing payload key '%s' for Twilio template", key)
		}
	}
	return params, nil
}

// --- Specific Notification Methods (Event-Related) ---

// SendEventPromotionNotification sends a notification when a user is promoted from the waitlist.
func (s *notificationService) SendEventPromotionNotification(ctx context.Context, user db.User, event db.Event) error {
	log.Info().Str("userID", user.ID.String()).Str("eventID", event.ID.String()).Msg("Triggering event promotion notification")
	// The SendToUser expects the actual data payload directly for the 'payload' argument
	// The Notification struct {Type, Payload} is constructed inside SendToUser for WebSockets.
	dataPayload := map[string]interface{}{
		"eventTitle": event.Title,
		"startTime":  event.StartTime.Format(time.RFC3339),
		"eventId":    event.ID.String(),
		// This message field is for the WebSocket. Twilio message is constructed by SendToUser.
		"message": fmt.Sprintf("Good news! You're off the waitlist for %s starting %s.", event.Title, event.StartTime.Format(time.RFC3339)),
	}
	return s.SendToUser(ctx, user.ID, "event_promotion", dataPayload)
}

// SendEventCancellationConfirmation sends a notification confirming registration cancellation.
func (s *notificationService) SendEventCancellationConfirmation(ctx context.Context, user db.User, event db.Event) error {
	log.Info().Str("userID", user.ID.String()).Str("eventID", event.ID.String()).Msg("Triggering event cancellation confirmation")
	dataPayload := map[string]interface{}{
		"eventTitle": event.Title,
		"eventId":    event.ID.String(),
		"message":    fmt.Sprintf("Your registration for event: %s has been cancelled.", event.Title),
	}
	return s.SendToUser(ctx, user.ID, "event_cancellation_confirmation", dataPayload)
}

// SendRegistrationStatusUpdate sends a notification about a change in registration status (e.g., by admin).
func (s *notificationService) SendRegistrationStatusUpdate(ctx context.Context, user db.User, event db.Event, newStatus string, adminNotes *string) error {
	log.Info().Str("userID", user.ID.String()).Str("eventID", event.ID.String()).Str("newStatus", newStatus).Msg("Triggering registration status update notification")

	notes := ""
	if adminNotes != nil {
		notes = *adminNotes
	}
	messageForWebSocket := fmt.Sprintf("Your registration status for event: %s has been updated to %s.", event.Title, newStatus)
	if notes != "" {
		messageForWebSocket += fmt.Sprintf(" Notes: %s", notes)
	}

	dataPayload := map[string]interface{}{
		"eventTitle": event.Title,
		"eventId":    event.ID.String(),
		"newStatus":  newStatus,
		"adminNotes": notes,
		"message":    messageForWebSocket, // This is for WebSocket, Twilio message is built in SendToUser
	}
	return s.SendToUser(ctx, user.ID, "registration_status_update", dataPayload)
}
