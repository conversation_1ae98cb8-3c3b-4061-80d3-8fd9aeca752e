/* EditManager.css */

/* Permission icon styling */
.permission-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-right: 8px;
  border-radius: 6px;
}

/* Permission item styling */
.permission-item {
  padding: 12px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

/* Grid-style permission item */
.permission-item-grid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  background-color: #f9fafb;
  border: 1px solid #f0f0f0;
}



/* Permission grid layout */
.permission-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 16px;
  padding: 8px 0;
}

@media (min-width: 640px) {
  .permission-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .permission-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Category header */
.category-header {
  display: flex;
  align-items: center;
}

/* Category icon */
.category-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 12px;
  border-radius: 8px;
}

/* Custom Collapse Styles */
.permissions-collapse {
  background: transparent;
}

.permissions-collapse .ant-collapse-header {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px !important;
}

.permissions-collapse .ant-collapse-content {
  border-top: none !important;
}

.permissions-collapse .ant-collapse-content-box {
  background-color: #fff;
  border-radius: 0 0 8px 8px;
}

.permissions-collapse .ant-collapse-item {
  border-radius: 8px !important;
  margin-bottom: 12px;
  border: none !important;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.permissions-collapse .ant-collapse-header-text {
  display: flex;
  align-items: center;
  flex: 1;
}

/* Icon background colors */
.icon-view { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
.icon-create { background-color: rgba(82, 196, 26, 0.15); color: #389e0d; }
.icon-edit { background-color: rgba(250, 173, 20, 0.15); color: #d48806; }
.icon-delete { background-color: rgba(245, 34, 45, 0.1); color: #f5222d; }
.icon-approve { background-color: rgba(114, 46, 209, 0.1); color: #722ed1; }
.icon-block { background-color: rgba(245, 34, 45, 0.1); color: #f5222d; }
.icon-assign { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
.icon-track { background-color: rgba(250, 173, 20, 0.15); color: #d48806; }
.icon-reports { background-color: rgba(82, 196, 26, 0.15); color: #389e0d; }
.icon-attendance { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
.icon-volunteerAttendance { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
.icon-process { background-color: rgba(82, 196, 26, 0.15); color: #389e0d; }
.icon-refund { background-color: rgba(250, 173, 20, 0.15); color: #d48806; }
.icon-publish { background-color: rgba(114, 46, 209, 0.1); color: #722ed1; }
.icon-send { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
.icon-backup { background-color: rgba(250, 173, 20, 0.15); color: #d48806; }
.icon-volunteerApprove { background-color: rgba(82, 196, 26, 0.15); color: #389e0d; }
.icon-eventApprove { background-color: rgba(114, 46, 209, 0.1); color: #722ed1; }

/* Category icon background colors */
.category-user { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
.category-volunteer { background-color: rgba(82, 196, 26, 0.15); color: #389e0d; }
.category-event { background-color: rgba(250, 173, 20, 0.15); color: #d48806; }
.category-payment { background-color: rgba(245, 34, 45, 0.1); color: #f5222d; }
.category-content { background-color: rgba(114, 46, 209, 0.1); color: #722ed1; }
.category-notification { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
.category-system { background-color: rgba(250, 173, 20, 0.15); color: #d48806; }

/* Manager header layout */
.manager-header {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .manager-header {
    grid-template-columns: 1fr;
  }
}

/* Manager select container */
.manager-select-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 150px; /* Fixed height */
}

/* Empty states styling */
.empty-manager-profile,
.empty-permissions-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  border: 1px dashed #e0e0e0;
}

.empty-permissions-container {
  min-height: 200px;
  margin-top: 20px;
}

/* Manager profile card */
.manager-profile {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  height: 150px; /* Fixed height */
}

.manager-profile .avatar-container {
  margin-right: 16px;
}

.manager-profile .info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.manager-profile .stats-container {
  background-color: #f7f9fc;
  padding: 12px 16px;
  border-radius: 8px;
  text-align: center;
}

/* Full-screen loading */
.full-screen-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
  width: 100%;
}