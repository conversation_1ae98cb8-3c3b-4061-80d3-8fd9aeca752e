import React, { useState, useEffect } from 'react';
import { useNavigate, /* useParams removed if only for current user */ } from 'react-router-dom';
import { Row, Col, Image, Typography, Descriptions, Button, Statistic, Skeleton, message, Upload, Modal, Form, Input, Empty } from 'antd';
import { CheckCircleOutlined, UserOutlined, EditOutlined, HeartOutlined, HistoryOutlined, ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// import { useAuth } from '../contexts/AuthContext'; // No longer directly needed if UserContext handles auth state for profile
import { useUser } from '../contexts/UserContext'; // Import useUser
import {
    LineChart, Line as RechartsLine, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
    BarChart, Bar, LabelList
} from 'recharts';
import { profileService } from '../services/profileService';
import ErrorPage from './ErrorPage';
import '../styles/UserProfile.css';
import moment from 'moment';
import { genderFormatter } from '../utils/genderFormatter';
import { formatHKPhoneNumber } from '../utils/phoneFormatter';

const { Title, Text } = Typography;

// Loading skeleton component
const ProfileSkeleton = () => (
    <div className="user-stat-container">
        <div className="border border-solid border-gray-300 rounded-xl p-6 mb-6 bg-white">
            <Row gutter={16} align="middle">
                <Col xs={24} sm={6} md={6} lg={4}>
                    <div className="image-container" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Skeleton.Avatar
                            active={false}
                            size={200}
                        />
                    </div>
                </Col>
                <Col xs={24} sm={24} md={18} lg={20}>
                    {/* Profile information placeholder without skeleton */}
                    <div style={{ height: '150px' }}></div>
                </Col>
            </Row>
        </div>
    </div>
);

const UserProfile = () => {
    const navigate = useNavigate();
    // const { userId } = useParams(); // Removed, assuming page is for current user via context
    // const { user } = useAuth(); // Replaced by useUser
    const { t } = useTranslation();

    // Use UserContext state
    const { currentUser: profileData, isLoading: loading, error, fetchUserProfile } = useUser();

    const [uploading, setUploading] = useState(false); // Keep local state for upload UI
    const [statsData, setStatsData] = useState(null);
    const [statsLoading, setStatsLoading] = useState(true);

    useEffect(() => {
        // If user is guest, redirect to login
        if (profileData && profileData.role === 'guest') {
            navigate('/login');
        }
    }, [profileData, navigate]);

    // Fetch user stats
    useEffect(() => {
        const fetchStats = async () => {
            try {
                setStatsLoading(true);
                const response = await profileService.getUserStats();
                setStatsData(response);
            } catch (error) {
                console.error('Error fetching user stats:', error);
            } finally {
                setStatsLoading(false);
            }
        };

        if (profileData?.id) {
            fetchStats();
        }
    }, [profileData?.id]);

    const items = [
        { key: '1', label: t('userProfile.info.name'), children: profileData?.display_name || '-' },
        { key: '2', label: t('userProfile.info.phone'), children: profileData?.phone ? formatHKPhoneNumber(profileData.phone) : '-' },
    ];

    const handleEditClick = () => {
        navigate('/settings');
    };

    const handleViewAllEvents = () => {
        navigate('/my-events?showCompleted=true');
    };

    // Format monthly data for the chart
    const formatMonthlyData = () => {
        if (!statsData?.monthlyAttendedEvents || statsData.monthlyAttendedEvents.length === 0) {
            // Generate past 12 months with zero values
            const emptyData = [];
            const today = new Date();

            for (let i = 11; i >= 0; i--) {
                const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
                const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

                emptyData.push({
                    month: monthStr,
                    monthLabel: date.toLocaleString('en-US', { month: 'short' }),
                    value: 0
                });
            }

            return emptyData;
        }

        return statsData.monthlyAttendedEvents.map(item => ({
            month: item.month,
            monthLabel: new Date(item.month + '-01').toLocaleString('en-US', { month: 'short' }),
            value: item.count
        }));
    };

    // Format event tags for the chart
    const formatEventTags = () => {
        if (!statsData?.topAttendedEventTags) return [];

        return statsData.topAttendedEventTags
            .map(item => ({
                type: item.name,
                value: item.count
            }))
            .sort((a, b) => b.value - a.value);
    };

    const monthlyParticipation = formatMonthlyData();
    const eventCategories = formatEventTags();

    const handleAvatarUpload = async (file) => {
        const isImage = file.type.startsWith('image/');
        if (!isImage) {
            message.error(t('userProfile.avatar.upload.validation.imageOnly'));
            return Upload.LIST_IGNORE;
        }

        const isLessThan2M = file.size / 1024 / 1024 < 2;
        if (!isLessThan2M) {
            message.error(t('userProfile.avatar.upload.validation.maxSize'));
            return Upload.LIST_IGNORE;
        }

        try {
            setUploading(true);
            // Update the API call to match field name if needed in profileService
            await profileService.uploadProfilePicture(file);

            // Instead of setProfileData locally, refresh the profile from UserContext
            await fetchUserProfile(); // This will update currentUser in UserContext

            message.success(t('userProfile.avatar.upload.success'));
        } catch (error) {
            message.error(t('userProfile.avatar.upload.error'));
            console.error('Avatar upload error:', error);
        } finally {
            setUploading(false);
        }

        return false; // Prevent default upload behavior if custom logic is handled
    };

    if (loading) {
        return <ProfileSkeleton />;
    }

    if (error) {
        return (
            <div className="user-stat-container">
                <ErrorPage
                    type={error}
                    showBackHome={false}
                />
            </div>
        );
    }

    if (!profileData || profileData.role === 'guest') { // Added second condition for safety, though useEffect should redirect
        // This state should ideally be handled by the useEffect redirecting to /login for guests.
        // If for some reason the redirect hasn't happened, show a message or minimal UI.
        return (
            <div className="p-5 text-center">
                <Title level={3}>{t('userProfile.accessDenied.title')}</Title>
                <Text>{t('userProfile.accessDenied.message')}</Text>
                <Button type="primary" onClick={() => navigate('/login')} className="mt-5">
                    {t('login.buttons.login')}
                </Button>
            </div>
        );
    }

    return (
        <div className="user-stat-container">
            {/* User Information Section */}
            <div className="border border-solid border-gray-300 rounded-xl p-6 mb-6 bg-white">
                <Row gutter={16} align="middle">
                    <Col xs={24} sm={6} md={6} lg={4}>
                        <div className="image-container relative group">
                            <Upload
                                name="avatar"
                                showUploadList={false}
                                beforeUpload={handleAvatarUpload}
                                disabled={uploading}
                            >
                                <div className="cursor-pointer w-full aspect-square relative">
                                    <Image
                                        src={profileData?.profile_picture_url || "https://api.dicebear.com/7.x/miniavs/svg?seed=1"}
                                        alt="avatar"
                                        className="w-full h-full object-cover rounded-full"
                                        preview={false}
                                    />
                                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-full">
                                        <div className="text-center">
                                            <UploadOutlined className="text-white text-2xl mb-2" />
                                            <p className="text-white text-sm">{t('userProfile.avatar.changeAvatar')}</p>
                                        </div>
                                    </div>
                                </div>
                            </Upload>
                        </div>
                    </Col>
                    <Col xs={24} sm={24} md={18} lg={20}>
                        <Row
                            justify="space-between"
                            align="middle"
                            className="profile-header"
                            style={{
                                flexDirection: { xs: 'column', sm: 'row' },
                                marginBottom: '16px'
                            }}
                        >
                            <div className="flex flex-wrap items-center gap-x-3 gap-y-1 flex-1">
                                <div className="min-w-[150px] max-w-[450px] flex-1">
                                    <Title level={3}>
                                        {profileData?.display_name ? `@ ${profileData.display_name}` : '-'}
                                    </Title>
                                </div>
                            </div>

                            {/* userId && (
                                <Button
                                    onClick={handleEditClick}
                                    icon={<EditOutlined />}
                                    type="text"
                                    ghost
                                    style={{ marginTop: { xs: '8px', sm: 0 } }}
                                >
                                    {t('userProfile.buttons.editProfile')}
                                </Button>
                            ) */}
                        </Row>
                        <Descriptions
                            className="descriptions"
                            column={{ xs: 1, sm: 1, md: 2, lg: 3 }}
                        >
                            {items.map((item) => (
                                <Descriptions.Item
                                    key={item.key}
                                    label={<strong>{item.label}</strong>}
                                    style={{ paddingBottom: '8px' }}
                                >
                                    {item.children}
                                </Descriptions.Item>
                            ))}
                        </Descriptions>
                    </Col>
                </Row>
            </div>

            {/* Statistics Section */}
            <div className="stats-section">
                <Row gutter={[0, 24]}>
                    <Col xs={24}>
                        <div className="stats-overview border-b border-solid border-gray-200 pb-6">
                            {statsLoading ? (
                                <Row gutter={[48, 24]}>
                                    <Col xs={24} sm={12} lg={6}><Skeleton.Button active size="large" style={{ width: 150, height: 80 }} /></Col>
                                    <Col xs={24} sm={12} lg={6}><Skeleton.Button active size="large" style={{ width: 150, height: 80 }} /></Col>
                                    <Col xs={24} sm={12} lg={6}><Skeleton.Button active size="large" style={{ width: 150, height: 80 }} /></Col>
                                    <Col xs={24} sm={12} lg={6}><Skeleton.Button active size="large" style={{ width: 150, height: 80 }} /></Col>
                                </Row>
                            ) : (
                                <Row gutter={[48, 24]} align="middle">
                                    <Col flex="auto">
                                        <Row gutter={[48, 24]}>
                                            <Col xs={24} sm={12} lg={6}>
                                                <Statistic
                                                    title={<Text strong className="stat-title">
                                                        {t('userProfile.statistics.totalEvents.title')}
                                                    </Text>}
                                                    value={statsData?.totalEvents || 0}
                                                    suffix={t('userProfile.statistics.totalEvents.suffix')}
                                                    prefix={<CheckCircleOutlined className="stats-icon blue" />}
                                                />
                                            </Col>
                                            <Col xs={24} sm={12} lg={6}>
                                                <Statistic
                                                    title={<Text strong className="stat-title">
                                                        {t('userProfile.statistics.participantHours.title')}
                                                    </Text>}
                                                    value={statsData?.volunteerEvents || 0}
                                                    suffix={t('userProfile.statistics.participantHours.suffix')}
                                                    prefix={<UserOutlined className="stats-icon green" />}
                                                />
                                            </Col>
                                            <Col xs={24} sm={12} lg={6}>
                                                <Statistic
                                                    title={<Text strong className="stat-title">
                                                        {t('userProfile.statistics.memberDays.title')}
                                                    </Text>}
                                                    value={statsData?.userJoinedAt ? Math.ceil(moment().diff(moment(statsData.userJoinedAt), 'days', true)) : 0}
                                                    suffix={
                                                        <div>
                                                            <span>
                                                                {t('userProfile.statistics.memberDays.suffix')}

                                                            </span>
                                                            <span className="ml-2 text-gray-500 text-base">
                                                                ({moment(statsData.userJoinedAt).format('YYYY-MM-DD')})
                                                            </span>
                                                        </div>
                                                    }
                                                    prefix={<HistoryOutlined className="stats-icon red" />}
                                                />
                                            </Col>

                                        </Row>
                                    </Col>
                                    {/* userId && (
                                        <Col>
                                            <Button
                                                type="default"
                                                size='large'
                                                icon={<HistoryOutlined />}
                                                onClick={handleViewAllEvents}
                                            >
                                                {t('userProfile.buttons.viewAllEvents')}
                                            </Button>
                                        </Col>
                                    ) */}
                                </Row>
                            )}
                        </div>
                    </Col>

                    {/* Charts Section */}
                    {!statsLoading && (
                        <>
                            <Col xs={24}>
                                <div className="mt-6">
                                    <div className="chart-header">
                                        <Title level={4}>{t('userProfile.charts.monthlyParticipation.title')}</Title>
                                        <Text type="secondary">{t('userProfile.charts.monthlyParticipation.subtitle')}</Text>
                                    </div>
                                    <div className="w-full h-[400px]">
                                        <ResponsiveContainer>
                                            <LineChart
                                                data={monthlyParticipation}
                                                margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis
                                                    dataKey="month"
                                                    tickFormatter={(value) => {
                                                        const item = monthlyParticipation.find(item => item.month === value);
                                                        return item ? item.monthLabel : '';
                                                    }}
                                                />
                                                <YAxis
                                                    allowDecimals={false}
                                                    domain={[0, 'auto']}
                                                />
                                                <Tooltip
                                                    content={({ active, payload, label }) => {
                                                        if (active && payload && payload.length) {
                                                            const item = monthlyParticipation.find(item => item.month === label);
                                                            return (
                                                                <div className="custom-tooltip">
                                                                    <p>{`${t('userProfile.charts.monthlyParticipation.tooltip.month')}: ${item ? item.month : ''}`}</p>
                                                                    <p>{`${t('userProfile.charts.monthlyParticipation.tooltip.events')}: ${payload[0].value}`}</p>
                                                                </div>
                                                            );
                                                        }
                                                        return null;
                                                    }}
                                                />
                                                <RechartsLine
                                                    type="monotone"
                                                    dataKey="value"
                                                    stroke="#1890ff"
                                                    strokeWidth={2}
                                                    dot={{ fill: '#fff', stroke: '#1890ff', strokeWidth: 2 }}
                                                    activeDot={{ r: 8 }}
                                                />
                                            </LineChart>
                                        </ResponsiveContainer>
                                    </div>
                                </div>
                            </Col>



                            <Col xs={24}>
                                <div className="mt-6">
                                    <Title level={4}>{t('userProfile.charts.eventCategories.title')}</Title>
                                    <div className="w-full h-[300px]">
                                        {eventCategories.length > 0 ? (
                                            <ResponsiveContainer>
                                                <BarChart
                                                    data={eventCategories}
                                                    layout="vertical"
                                                    margin={{ top: 20, right: 50, left: 20, bottom: 5 }}
                                                >
                                                    <CartesianGrid strokeDasharray="3 3" />
                                                    <XAxis type="number" allowDecimals={false} />
                                                    <YAxis dataKey="type" type="category" width={150} />
                                                    <Bar
                                                        dataKey="value"
                                                        fill="#1890ff"
                                                        barSize={30}
                                                    >
                                                        <LabelList
                                                            dataKey="value"
                                                            position="right"
                                                        />
                                                    </Bar>
                                                </BarChart>
                                            </ResponsiveContainer>
                                        ) : (
                                            <div className="flex items-center justify-center h-full">
                                                <Empty />
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </Col>
                        </>
                    )}
                </Row>
            </div>
        </div>
    );
};

export default UserProfile;
