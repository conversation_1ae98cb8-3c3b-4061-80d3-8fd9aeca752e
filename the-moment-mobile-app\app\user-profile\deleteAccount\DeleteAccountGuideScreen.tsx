import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Platform, TouchableOpacity, Modal, Dimensions, Animated, TouchableWithoutFeedback } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, ActivityIndicator } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import { CustomDialog } from '@/common_modules/CustomDialog';

export default function DeleteAccountGuideScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const storedTheme = appStyleStore(state => state.theme);
  const theme = storedTheme || createTheme('red');

  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const animationRef = useRef(new Animated.Value(0));

  // Handle modal visibility and animation
  useEffect(() => {
    if (showConfirmModal) {
      // Reset animation value to 0 and start animation
      animationRef.current.setValue(0);
      Animated.timing(animationRef.current, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    } else {
      // Animate closing
      Animated.timing(animationRef.current, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [showConfirmModal]);

  const handleContinue = () => {
    setShowConfirmModal(true);
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleCloseModal = () => {
    if (isDeleting) return;
    setShowConfirmModal(false);
  };

  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    
    try {
      // TODO: Replace with actual API call
      // await deleteUserAccount();
      
      // Simulate API delay for demo purposes
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Success: close modal and show success dialog
      setShowConfirmModal(false);
      setIsDeleting(false);
      setShowSuccessDialog(true);
    } catch (error) {
      // Handle API errors (network issues, server errors, etc.)
      console.error('Delete account error:', error);
      setShowConfirmModal(false);
      setIsDeleting(false);
      setShowErrorDialog(true);
    }
  };

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('profile.deleteAccount.title'),
      }} />

      <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.system.background }]} edges={['bottom']}>
        <ScrollView
          style={[styles.container, { backgroundColor: theme.system.background }]}
          contentContainerStyle={styles.contentContainer}
        >
          <View style={styles.header}>
            <View style={[styles.iconContainer, { borderColor: theme.colors.error }]}>
              <MaterialCommunityIcons name="delete-empty" size={54} color={theme.colors.error} />
            </View>
            <Text style={[styles.title, { color: theme.system.text }]}>
              {t('profile.deleteAccount.title')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.system.text }]}>
              {t('profile.deleteAccount.whatWillBeDeleted')}
            </Text>

            <View style={styles.stepContainer}>
              <View style={[styles.stepIcon, { backgroundColor: `${theme.colors.error}15` }]}>
                <Ionicons name="person" size={22} color={theme.colors.error} />
              </View>
              <View style={styles.stepContent}>
                <Text style={[styles.stepTitle, { color: theme.system.text }]}>
                  {t('profile.deleteAccount.step1.title')}
                </Text>
                <Text style={[styles.stepDescription, { color: theme.system.secondaryText }]}>
                  {t('profile.deleteAccount.step1.description')}
                </Text>
              </View>
            </View>

            <View style={styles.stepContainer}>
              <View style={[styles.stepIcon, { backgroundColor: `${theme.colors.error}15` }]}>
                <Ionicons name="calendar" size={22} color={theme.colors.error} />
              </View>
              <View style={styles.stepContent}>
                <Text style={[styles.stepTitle, { color: theme.system.text }]}>
                  {t('profile.deleteAccount.step2.title')}
                </Text>
                <Text style={[styles.stepDescription, { color: theme.system.secondaryText }]}>
                  {t('profile.deleteAccount.step2.description')}
                </Text>
              </View>
            </View>

            <View style={styles.stepContainer}>
              <View style={[styles.stepIcon, { backgroundColor: `${theme.colors.error}15` }]}>
                <Ionicons name="document-text" size={22} color={theme.colors.error} />
              </View>
              <View style={styles.stepContent}>
                <Text style={[styles.stepTitle, { color: theme.system.text }]}>
                  {t('profile.deleteAccount.step3.title')}
                </Text>
                <Text style={[styles.stepDescription, { color: theme.system.secondaryText }]}>
                  {t('profile.deleteAccount.step3.description')}
                </Text>
              </View>
            </View>
          </View>

          <View style={[styles.warningSection, {
            backgroundColor: `${theme.colors.error}12`,
            borderColor: `${theme.colors.error}30`
          }]}>
            <View style={styles.warningContent}>
              <Ionicons name="warning" size={20} color={theme.colors.error} style={styles.warningIcon} />
              <Text style={[styles.warningTitle, { color: theme.colors.error }]}>
                {t('profile.deleteAccount.warning.title')}
              </Text>
            </View>
            <Text style={[styles.warningDescription, { color: theme.system.secondaryText }]}>
              {t('profile.deleteAccount.warning.description')}
            </Text>
          </View>


        </ScrollView>

        <View style={[styles.footer, { borderTopColor: theme.system.border, backgroundColor: theme.system.background }]}>
          <View style={styles.footerButtons}>
            <Button
              mode="outlined"
              onPress={handleGoBack}
              style={[styles.backButton, { borderColor: theme.colors.primary }]}
              contentStyle={styles.buttonContent}
              labelStyle={[styles.buttonLabel, { color: theme.colors.primary }]}
            >
              {t('common.back')}
            </Button>
            <Button
              mode="contained"
              onPress={handleContinue}
              style={[styles.continueButton, { backgroundColor: theme.colors.error }]}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}
            >
              {t('profile.deleteAccount.continue')}
            </Button>
          </View>
        </View>
      </SafeAreaView>

      {/* Confirmation Modal */}
      <Modal
        visible={showConfirmModal}
        transparent
        animationType="none"
        onRequestClose={handleCloseModal}
      >
        <TouchableWithoutFeedback onPress={handleCloseModal}>
          <Animated.View style={[styles.overlay, { opacity: animationRef.current }]}>
            <TouchableWithoutFeedback>
              <Animated.View
                style={[
                  styles.modalContent,
                  {
                    transform: [{ 
                      translateY: animationRef.current.interpolate({
                        inputRange: [0, 1],
                        outputRange: [Dimensions.get('window').height, 0],
                      })
                    }],
                    backgroundColor: theme.colors.surface,
                  }
                ]}
              >
                <View style={styles.handle} />
                
                {/* Icon */}
                <View style={[styles.modalIconContainer, { backgroundColor: `${theme.colors.error}15` }]}>
                  <MaterialCommunityIcons name="delete-outline" size={32} color={theme.colors.error} />
                </View>
                
                <Text style={[styles.modalTitle, { color: theme.system.text }]}>
                  {t('profile.deleteAccount.confirmModal.title')}
                </Text>
                
                <Text style={[styles.modalMessage, { color: theme.system.secondaryText }]}>
                  {t('profile.deleteAccount.confirmModal.message')}
                </Text>
                
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    onPress={handleCloseModal}
                    disabled={isDeleting}
                    style={[styles.cancelButton, { backgroundColor: theme.system.border }]}
                  >
                    <Text style={[styles.cancelButtonText, { color: theme.system.text }]}>
                      {t('common.cancel')}
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    onPress={handleDeleteAccount}
                    disabled={isDeleting}
                    style={[styles.confirmButton, { backgroundColor: theme.colors.error }]}
                  >
                    <View style={styles.confirmButtonContent}>
                      {isDeleting && (
                        <ActivityIndicator size={16} color="#FFFFFF" style={styles.buttonLoader} />
                      )}
                      <Text style={styles.confirmButtonText}>
                        {isDeleting 
                          ? t('profile.deleteAccount.confirmModal.deleting')
                          : t('profile.deleteAccount.confirmModal.confirm')
                        }
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </Animated.View>
            </TouchableWithoutFeedback>
          </Animated.View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* Success Dialog */}
      <CustomDialog
        visible={showSuccessDialog}
        type="success"
        title={t('profile.deleteAccount.success.title')}
        message={t('profile.deleteAccount.success.message')}
        confirmText={t('common.ok')}
        onConfirm={() => {
          setShowSuccessDialog(false);
        }}
      />

      {/* Error Dialog */}
      <CustomDialog
        visible={showErrorDialog}
        type="error"
        title={t('profile.deleteAccount.error.title')}
        message={t('profile.deleteAccount.error.message')}
        confirmText={t('common.ok')}
        onConfirm={() => {
          setShowErrorDialog(false);
        }}
      />
    </>
  );
}

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
    },
    container: {
      flex: 1,
    },
    contentContainer: {
      padding: 24,
      paddingBottom: 24,
    },
    header: {
      alignItems: 'center',
      marginBottom: 24,
    },
    iconContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      borderWidth: 2,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 12,
    },
    title: {
      fontSize: 22,
      fontWeight: '600',
      marginTop: 16,
      textAlign: 'center',
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 17,
      fontWeight: '600',
      marginBottom: 12,
    },
    description: {
      fontSize: 15,
      lineHeight: 22,
      letterSpacing: 0.1,
    },
    stepContainer: {
      flexDirection: 'row',
      marginBottom: 16,
    },
    stepIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    stepContent: {
      flex: 1,
    },
    stepTitle: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 4,
    },
    stepDescription: {
      fontSize: 14,
      lineHeight: 20,
    },
    warningSection: {
      padding: 16,
      borderRadius: 12,
      marginBottom: 16,
      borderWidth: 1,
    },
    warningContent: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    warningIcon: {
      marginRight: 8,
    },
    warningTitle: {
      fontSize: 15,
      fontWeight: '600',
    },
    warningDescription: {
      fontSize: 14,
      lineHeight: 20,
    },
    footer: {
      paddingHorizontal: 24,
      paddingVertical: 16,
      borderTopWidth: 1,
    },
    footerButtons: {
      flexDirection: 'row',
      gap: 12,
    },
    backButton: {
      flex: 1,
      height: 48,
      justifyContent: 'center',
      borderRadius: 10,
      elevation: 0,
    },
    continueButton: {
      flex: 1,
      height: 48,
      justifyContent: 'center',
      borderRadius: 10,
      elevation: 0,
    },
    buttonContent: {
      height: 48,
    },
    buttonLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FFFFFF',
    },
    // Modal styles
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 16,
      paddingTop: 12,
      paddingBottom: Platform.OS === 'ios' ? 34 : 24,
    },
    handle: {
      width: 36,
      height: 4,
      backgroundColor: '#E0E0E0',
      borderRadius: 2,
      alignSelf: 'center',
      marginBottom: 20,
    },
    modalIconContainer: {
      width: 60,
      height: 60,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
      marginBottom: 16,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '600',
      marginBottom: 12,
      textAlign: 'center',
    },
    modalMessage: {
      fontSize: 16,
      lineHeight: 22,
      marginBottom: 24,
      textAlign: 'center',
    },
    modalButtons: {
      flexDirection: 'row',
      gap: 12,
    },
    cancelButton: {
      flex: 1,
      height: 48,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    confirmButton: {
      flex: 1,
      height: 48,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
    },
    confirmButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FFFFFF',
    },
    confirmButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonLoader: {
      marginRight: 8,
    },
  }); 