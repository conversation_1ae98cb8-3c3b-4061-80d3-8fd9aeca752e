-- name: CreatePostTag :one
INSERT INTO post_tags (
    name_en,
    name_zh_hk,
    name_zh_cn,
    description_en,
    description_zh_hk,
    description_zh_cn
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING *;

-- name: GetPostTagByID :one
SELECT * FROM post_tags
WHERE id = $1;

-- name: GetPostTagByName :one
SELECT * FROM post_tags
WHERE name_en = $1 OR name_zh_hk = $1 OR name_zh_cn = $1;

-- name: ListPostTags :many
SELECT * FROM post_tags
ORDER BY name_en;

-- name: UpdatePostTag :one
UPDATE post_tags
SET
    name_en = $2,
    name_zh_hk = $3,
    name_zh_cn = $4,
    description_en = $5,
    description_zh_hk = $6,
    description_zh_cn = $7,
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: DeletePostTag :exec
DELETE FROM post_tags
WHERE id = $1;

-- name: AddTagToPost :exec
INSERT INTO post_post_tags (
    post_id,
    tag_id
) VALUES (
    $1, $2
) ON CONFLICT (post_id, tag_id) DO NOTHING;

-- name: RemoveTagFromPost :exec
DELETE FROM post_post_tags
WHERE post_id = $1 AND tag_id = $2;

-- name: GetTagsForPost :many
SELECT pt.*
FROM post_tags pt
JOIN post_post_tags ppt ON pt.id = ppt.tag_id
WHERE ppt.post_id = $1
ORDER BY pt.name_en;

-- name: GetPostsForTag :many
SELECT p.*
FROM posts p
JOIN post_post_tags ppt ON p.id = ppt.post_id
WHERE ppt.tag_id = $1
ORDER BY p.created_at DESC;
