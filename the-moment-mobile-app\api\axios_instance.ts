import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import { API_BASE_URL, ApiConfig } from '@/api/api_config';
import { attemptTokenRefresh, performLogout, isAuthenticationError } from '@/utils/authUtils';
import { router } from 'expo-router';

const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
});

// Request queue for handling concurrent requests during token refresh
interface QueuedRequest {
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

let isRefreshing = false;
let failedQueue: QueuedRequest[] = [];

// Process queued requests after token refresh
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor - adds authorization header
axiosInstance.interceptors.request.use(
  (config) => {
    const { accessToken, isAuthenticated } = authenticationStore.getState();
    if (isAuthenticated && accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor - handles 401 errors and automatic token refresh
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

    // Check if it's a 401 error and not already retried, and not the refresh token endpoint itself
    if (
      isAuthenticationError(error) &&
      !originalRequest._retry &&
      originalRequest.url !== ApiConfig.authentication.access_token_refresh.endpoint
    ) {
      if (isRefreshing) {
        // If a refresh is already in progress, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
        .then((token) => {
          if (token && originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${token}`;
          }
          originalRequest._retry = true;
          return axiosInstance(originalRequest);
        })
        .catch((err) => {
          return Promise.reject(err);
        });
      }

      // Mark as refreshing and attempt token refresh
      originalRequest._retry = true;
      isRefreshing = true;

      try {
        console.log('[axios_instance] Detected 401 error, attempting token refresh');

        const refreshSuccess = await attemptTokenRefresh();

        if (refreshSuccess) {
          console.log('[axios_instance] Token refresh successful, retrying original request');

          // Get the new access token
          const { accessToken } = authenticationStore.getState();

          // Update the original request's authorization header
          if (originalRequest.headers && accessToken) {
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          }

          // Process the queue with the new token
          processQueue(null, accessToken);

          // Retry the original request
          return axiosInstance(originalRequest);
        } else {
          console.log('[axios_instance] Token refresh failed, performing logout');

          // Token refresh failed, perform logout
          performLogout();

          // Process the queue with error
          const logoutError = new Error('Session expired. Please login again.');
          processQueue(logoutError, null);

          // Redirect to dashboard (login will be shown due to authentication state)
          try {
            router.replace('/tabs/dashboard');
          } catch (routerError) {
            console.warn('[axios_instance] Router navigation failed:', routerError);
          }

          return Promise.reject(logoutError);
        }
      } catch (refreshError) {
        console.error('[axios_instance] Token refresh error:', refreshError);

        // Token refresh failed, perform logout
        performLogout();

        // Process the queue with error
        processQueue(refreshError, null);

        // Redirect to dashboard
        try {
          router.replace('/tabs/dashboard');
        } catch (routerError) {
          console.warn('[axios_instance] Router navigation failed:', routerError);
        }

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    } else if (
      isAuthenticationError(error) &&
      originalRequest.url === ApiConfig.authentication.access_token_refresh.endpoint
    ) {
      // This is a 401 from the refresh token endpoint itself
      console.warn('[axios_instance] Refresh token endpoint returned 401, clearing tokens');

      performLogout();

      // Redirect to dashboard
      try {
        router.replace('/tabs/dashboard');
      } catch (routerError) {
        console.warn('[axios_instance] Router navigation failed:', routerError);
      }

      return Promise.reject(error);
    }

    // For all other errors (non-401 or already retried), just reject
    return Promise.reject(error);
  }
);

export default axiosInstance;