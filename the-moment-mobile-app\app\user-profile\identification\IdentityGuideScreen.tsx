import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { appStyleStore } from 'stores/app_style_store';
import { VerificationTypeEnum } from 'types/enums';
import { createTheme } from 'theme/index';

interface GuideStep {
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
  title: string;
  description: string;
}

// Function to get dynamic guide steps
const getGuideSteps = (documentType: VerificationTypeEnum | undefined, t: TFunction): GuideStep[] => {
  const dt = documentType || 'default'; // Use a fallback key if documentType is undefined
  
  const stepsConfig = [
    { key: 'info', icon: 'account-check-outline' as keyof typeof MaterialCommunityIcons.glyphMap },
    { key: 'prepare', icon: 'file-document-outline' as keyof typeof MaterialCommunityIcons.glyphMap },
    { key: 'photo', icon: 'camera-outline' as keyof typeof MaterialCommunityIcons.glyphMap },
    { key: 'review', icon: 'clock-time-four-outline' as keyof typeof MaterialCommunityIcons.glyphMap },
  ];

  return stepsConfig.map(config => {
    const titleKey = `identity.guide.steps.${config.key}.title`;
    const descriptionKey = `identity.guide.steps.${config.key}.description`;
    
    // Attempt to get specific key first, then generic step key
    const specificTitle = t([`${titleKey}_${dt}`, titleKey]);
    const specificDescription = t([`${descriptionKey}_${dt}`, descriptionKey]);

    return {
      icon: config.icon,
      title: specificTitle,
      description: specificDescription,
    };
  });
  // Note: This i18n key structure means you'll need keys like:
  // 'identity.guide.steps.info.title_hkid', 'identity.guide.steps.info.description_hkid', etc.
  // And fallbacks like 'identity.guide.steps.info.title' if the specific one isn't found.
};

export default function IdentityGuideScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const router = useRouter();
  const params = useLocalSearchParams<{ documentType?: string }>();
  
  const documentType = params.documentType as VerificationTypeEnum | undefined;

  useEffect(() => {
    if (!documentType) {
      console.warn("IdentityGuideScreen: documentType param is missing or invalid.");
      Alert.alert(t('common.error'), t('identity.errors.noDocType'), 
        [{ text: t("common.ok"), onPress: () => router.canGoBack() ? router.back() : router.replace('/tabs/profile') }]
      );
    }
  }, [documentType, router, t]);

  // Get the dynamic steps
  const guideStepsToDisplay = getGuideSteps(documentType, t);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.system.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      paddingTop: 24,
      paddingBottom: 24,
    },
    sectionTitle: {
      fontSize: 22,
      fontWeight: 'bold',
      color: theme.system.text,
      marginBottom: 24,
      paddingHorizontal: 24,
      textAlign: 'center',
    },
    stepsContainer: {
      paddingHorizontal: 24,
    },
    step: {
      marginBottom: 30,
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    stepIconWrapper: {
        backgroundColor: theme.colors.primaryContainer,
        borderRadius: 22,
        padding: 10,
        marginRight: 16,
        alignItems: 'center',
        justifyContent: 'center',
        width: 44, 
        height: 44,
    },
    stepIcon: {
      // Removed marginRight and marginTop as it's handled by wrapper
    },
    stepContent: {
      flex: 1,
    },
    stepTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.system.text,
      marginBottom: 6,
    },
    stepDescription: {
      fontSize: 15,
      color: theme.system.secondaryText,
      lineHeight: 22,
    },
    divider: {
      height: 1,
      backgroundColor: theme.system.border,
      marginVertical: 24,
      marginHorizontal: 24,
    },
    footer: {
      paddingHorizontal: 24,
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.system.border,
      backgroundColor: theme.system.background,
    },
    startButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      paddingVertical: 16,
      borderRadius: 12,
      gap: 8,
    },
    startButtonDisabled: {
      backgroundColor: theme.colors.primaryDisabled || '#CCCCCC',
    },
    startButtonText: {
      fontSize: 17,
      fontWeight: '600',
      color: '#FFFFFF',
    },
  });

  const handleStart = () => {
    if (!documentType) {
        Alert.alert(t('common.error'), t('identity.errors.noDocType'));
        return;
    }
    const showTerms = documentType === VerificationTypeEnum.HomeVisit;
    router.push({
        pathname: '/user-profile/identification/IdentityInfoScreen',
        params: { 
            documentType: documentType,
            showTerms: showTerms.toString() 
        },
    });
  };

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('identity.guide.title'),
      }} />
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <StatusBar style="dark" />
        
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
          <View style={styles.stepsContainer}>
            {guideStepsToDisplay.map((step, index) => (
              <View key={index} style={styles.step}>
                  <View style={styles.stepIconWrapper}>
                      <MaterialCommunityIcons 
                          name={step.icon} 
                          size={24} 
                          color={theme.colors.primary}
                          style={styles.stepIcon} 
                      />
                  </View>
                <View style={styles.stepContent}>
                  <Text style={styles.stepTitle}>{step.title}</Text>
                  <Text style={styles.stepDescription}>{step.description}</Text>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
        <View style={styles.footer}>
          <TouchableOpacity 
            style={[styles.startButton, !documentType && styles.startButtonDisabled]} 
            onPress={handleStart} 
            disabled={!documentType}
          >
            <Text style={styles.startButtonText}>{t('identity.guide.startButton')}</Text>
            <MaterialCommunityIcons name="arrow-right" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </>
  );
}