-- Enum for volunteer application status
CREATE TYPE volunteer_application_status AS ENUM (
    'pending',
    'approved',
    'rejected'
);

-- Table for user volunteer applications
CREATE TABLE user_volunteer_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    application_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status volunteer_application_status NOT NULL DEFAULT 'pending',
    motivation TEXT, -- Optional: User can provide a short motivation
    reviewed_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Staff member who reviewed
    review_date TIMESTAMPTZ,
    admin_notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add a partial unique index for pending applications separately
CREATE UNIQUE INDEX idx_uq_user_org_pending_application
ON user_volunteer_applications (user_id, organization_id)
WHERE status = 'pending';

-- Table for user organization volunteer qualifications
CREATE TABLE user_organization_volunteer_qualifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    application_id UUID NOT NULL REFERENCES user_volunteer_applications(id) ON DELETE CASCADE, -- Link to the approved application
    qualification_date TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- When they were approved
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT uq_user_org_qualification UNIQUE (user_id, organization_id) -- A user is either qualified or not for an org
);

-- Triggers to update updated_at timestamps
CREATE TRIGGER set_timestamp_user_volunteer_applications
BEFORE UPDATE ON user_volunteer_applications
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamp();

CREATE TRIGGER set_timestamp_user_organization_volunteer_qualifications
BEFORE UPDATE ON user_organization_volunteer_qualifications
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamp();

-- Indexes for faster lookups
CREATE INDEX idx_user_volunteer_applications_user_id ON user_volunteer_applications(user_id);
CREATE INDEX idx_user_volunteer_applications_organization_id ON user_volunteer_applications(organization_id);
CREATE INDEX idx_user_volunteer_applications_status ON user_volunteer_applications(status);

CREATE INDEX idx_user_organization_volunteer_qualifications_user_id ON user_organization_volunteer_qualifications(user_id);
CREATE INDEX idx_user_organization_volunteer_qualifications_organization_id ON user_organization_volunteer_qualifications(organization_id); 