-- Revert seeding of post_tags with deterministic UUIDs

DELETE FROM "post_tags" WHERE "id" IN (
    -- Tag Group 1: Government News
    'a1a1a1a1-0001-4000-8000-000000000001',
    'a1a1a1a1-1001-4000-8000-000000000001',
    'a1a1a1a1-**************-000000000001',

    -- Tag Group 2: Event News
    'a1a1a1a1-0002-4000-8000-000000000002',
    'a1a1a1a1-1002-4000-8000-000000000002',
    'a1a1a1a1-**************-000000000002',

    -- Tag Group 3: National Education
    'a1a1a1a1-0003-4000-8000-000000000003',
    'a1a1a1a1-1003-4000-8000-000000000003',
    'a1a1a1a1-**************-000000000003',

    -- Tag Group 4: Office Information
    'a1a1a1a1-0004-4000-8000-000000000004',
    'a1a1a1a1-1004-4000-8000-000000000004',
    'a1a1a1a1-**************-000000000004',

    -- Tag Group 5: Community Update
    'a1a1a1a1-0005-4000-8000-000000000005',
    'a1a1a1a1-1005-4000-8000-000000000005',
    'a1a1a1a1-**************-000000000005',

    -- Tag Group 6: Event Highlight
    'a1a1a1a1-0006-4000-8000-000000000006',
    'a1a1a1a1-1006-4000-8000-000000000006',
    'a1a1a1a1-2006-4000-8000-000000000006',

    -- Tag Group 7: Member Story
    'a1a1a1a1-0007-4000-8000-000000000007',
    'a1a1a1a1-1007-4000-8000-000000000007',
    'a1a1a1a1-2007-4000-8000-000000000007',

    -- Tag Group 8: Official Announcement
    'a1a1a1a1-0008-4000-8000-000000000008',
    'a1a1a1a1-1008-4000-8000-000000000008',
    'a1a1a1a1-2008-4000-8000-000000000008'
); 