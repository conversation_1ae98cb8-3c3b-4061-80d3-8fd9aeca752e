import { useState, useEffect, useCallback } from 'react';
import { Typography, App, Spin, Tabs, Image, Empty, Skeleton } from 'antd';
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { verificationService } from '../../services/verificationService';
import ErrorPage from '../ErrorPage';
import ApprovalSidebar from '../../components/ApprovalSidebar';
import DocumentDetailsView from '../../components/DocumentDetailsView';
import { formatSimpleDateTime } from '../../utils/dateFormatter';
import approveIcon from '../../assets/images/approve.svg';
import { Icon } from '@iconify/react';
import fallbackImage from '../../assets/images/picture-loading-failed.svg';

const { Text } = Typography;

const ProofDetails = () => {
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(true);
  const [verificationData, setVerificationData] = useState(null);
  const [verificationDataCache, setVerificationDataCache] = useState({});
  const [error, setError] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [documentStates, setDocumentStates] = useState({});
  
  // State for multiple document handling
  const [documentRequests, setDocumentRequests] = useState([]);
  const [currentDocIndex, setCurrentDocIndex] = useState(0);
  
  // State for document images
  const [documentImages, setDocumentImages] = useState({});
  const [imageLoading, setImageLoading] = useState({});
  
  const { t } = useTranslation();
  const { id: singleReqId } = useParams();
  const { message } = App.useApp();
  const { user: adminUser } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Parse document data from location state or use single reqId
  useEffect(() => {
    const parseDocumentData = async () => {
      try {
        setInitializing(true);
        
        // First check if we have data in location state
        if (location.state?.documentData) {
          const documentData = location.state.documentData;
          if (Array.isArray(documentData) && documentData.length > 0) {
            setDocumentRequests(documentData);
            setInitializing(false);
            return;
          }
        }
        
        // Fallback to single reqId from params
        if (singleReqId) {
          setDocumentRequests([{ reqId: singleReqId, type: null }]);
        } else {
          setError(null);
        }
      } catch (err) {
        console.error('Error parsing document data:', err);
        setError(err);
      } finally {
        setInitializing(false);
      }
    };
    
    parseDocumentData();
  }, [location.state, singleReqId, t]);

  // Fetch verification details based on current document index
  const fetchVerificationDetails = useCallback(async (reqId) => {
    if (!reqId) {
      setVerificationData(null);
      setLoading(false);
      return;
    }
    
    // Check cache first
    if (verificationDataCache[reqId]) {
      setVerificationData(verificationDataCache[reqId]);
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const apiResponse = await verificationService.getAdminVerificationDetail(reqId);
      console.log('API Response for reqId:', reqId, apiResponse);

      const transformedData = {
        ...apiResponse,
        documentVerification: {
          [apiResponse.verification_type]: {
            status: apiResponse.status,
            submissionTime: apiResponse.submitted_at,
            details: {
              fullname: apiResponse.specifics?.english_name || '-',
              chineseName: apiResponse.specifics?.chinese_name || '-',
              gender: apiResponse.specifics?.sex || '-',
              idNumber: apiResponse.specifics?.hk_id_number || apiResponse.specifics?.passport_number || '-',
              dateOfBirth: apiResponse.specifics?.date_of_birth || '-',
              ...(apiResponse.verification_type === 'passport' && {
                passportNumber: apiResponse.specifics?.passport_number || '-',
                issuingCountry: apiResponse.specifics?.issuing_country || '-',
                issueDate: apiResponse.specifics?.issue_date || '-',
                expiryDate: apiResponse.specifics?.expiry_date || '-'
              })
            },
            documents: [{
              type: apiResponse.verification_type,
              id: apiResponse.document_id,
              file_name: apiResponse.file_name,
              mime_type: apiResponse.mime_type
            }]
          }
        },
        accountHistory: apiResponse.accountHistory || apiResponse.history || []
      };

      // Cache the data
      setVerificationDataCache(prev => ({
        ...prev,
        [reqId]: transformedData
      }));
      
      setVerificationData(transformedData);
      
      // Update document type in documentRequests if not already set (safer update)
      setDocumentRequests(prevRequests => {
        const requestIndex = prevRequests.findIndex(doc => doc.reqId === reqId);
        if (requestIndex !== -1 && prevRequests[requestIndex] && !prevRequests[requestIndex].type && apiResponse.verification_type) {
          const updatedRequests = [...prevRequests];
          updatedRequests[requestIndex] = {
            ...updatedRequests[requestIndex],
            type: apiResponse.verification_type,
          };
          return updatedRequests;
        }
        return prevRequests; // No change needed or found
      });
      
    } catch (fetchError) {
      console.error("Error fetching verification details for reqId " + reqId + ":", fetchError);
      setError(fetchError);
      setVerificationData(null); // Clear data on error
    } finally {
      setLoading(false);
    }
  }, [verificationDataCache, setLoading, setError, setVerificationData, setDocumentRequests]);

  // NEW Main data fetching useEffect
  useEffect(() => {
    let reqIdToFetch = null;
    if (documentRequests.length > 0 && currentDocIndex >= 0 && currentDocIndex < documentRequests.length) {
      reqIdToFetch = documentRequests[currentDocIndex]?.reqId;  
    }
    
    if (reqIdToFetch) {
      fetchVerificationDetails(reqIdToFetch);
    } else {
      // No valid reqId for current index, or no documents at all.
      setVerificationData(null);
      setLoading(false); // Ensure loading is reset if no fetch occurs.
    }
  // Dependencies: currentDocIndex, documentRequests array (to react to list changes), and the stable fetchVerificationDetails.
  }, [currentDocIndex, documentRequests, fetchVerificationDetails]);

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 768);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Function to update local verification status without fetching from server
  const updateLocalVerificationStatus = (documentType, status, comments) => {
    if (!verificationData) return;

    const newHistoryEntry = {
      timestamp: new Date().toISOString(),
      action: t(`documents.${documentType}`) + ' ' + t(`proofDetails.verificationStatus.${status}`),
      status: status.toLowerCase(),
      reviewer: adminUser?.display_name || t('common.admin'),
      comments: comments // This is the 'reason' or admin note.
    };

    const updatedVerificationData = {
      ...verificationData,
      status: status, 
      updated_at: new Date().toISOString(), 
      admin_notes: comments, // Align with your diff: use admin_notes for storing the review comments/reason
      ...(verificationData.documentVerification && {
        documentVerification: {
          ...verificationData.documentVerification,
          [documentType]: {
            ...verificationData.documentVerification[documentType],
            status: status
          }
        }
      }),
      accountHistory: [
        ...(verificationData.accountHistory || []),
        newHistoryEntry
      ]
    };

    // Update both current data and cache
    setVerificationData(updatedVerificationData);
    const reqId = documentRequests[currentDocIndex]?.reqId;
    if (reqId) {
      setVerificationDataCache(prev => ({
        ...prev,
        [reqId]: updatedVerificationData
      }));
    }
  };

  // Handle document tab change
  const handleDocumentTabChange = async (newIndex) => {
    if (newIndex === currentDocIndex) return;
    
    // Don't clear verification data, just switch to new index
    // The useEffect will handle loading data from cache or API
    setCurrentDocIndex(newIndex);
  };

  const handleStatusChange = async (newStatus) => {
    // Validation for rejected status - require a reason
    if (newStatus === 'rejected' && !reason) {
      message.warning(t('proofDetails.messages.reasonRequired'));
      return;
    }

    try {
      setSubmitting(true);
      const reqId = documentRequests[currentDocIndex].reqId;

      // API call to update status
      await verificationService.reviewVerification(reqId, {
        status: newStatus,
        adminNotes: reason
      });

      message.success(t(`proofDetails.messages.${newStatus}Success`));
      setReason('');

      // Update local state
      const docType = verificationData?.verification_type;
      updateLocalVerificationStatus(docType, newStatus, reason);
      
      // Update status in documentRequests
      const updatedRequests = [...documentRequests];
      updatedRequests[currentDocIndex].status = newStatus;
      setDocumentRequests(updatedRequests);
    } catch (error) {
      setError(error);
    } finally {
      setSubmitting(false);
      // navigate('/verification'); // Lawson：这个是否是typo？审批完不应该退回到列表页，而应该停留在当前页，等待下一个审批。
    }
  };

  const getAccountHistory = () => {
    const history = [];

    if (verificationData?.created_at || verificationData?.submitted_at) {
      const submissionTime = verificationData?.submitted_at || verificationData?.created_at;
      const docType = verificationData?.verification_type || '';

      history.push({
        timestamp: submissionTime,
        action: t(`documents.${docType}`) + ' ' + t('proofDetails.verificationStatus.pending'),
        status: 'pending',
        comments: ''
      });
    }

    if (verificationData?.status && verificationData?.status !== 'pending' && verificationData?.updated_at) {
      const docType = verificationData?.verification_type || '';

      history.push({
        timestamp: verificationData.updated_at,
        action: t(`documents.${docType}`) + ' ' + t(`proofDetails.verificationStatus.${verificationData.status}`),
        status: verificationData.status,
        reviewer: adminUser?.display_name || t('common.admin'),
        comments: verificationData.admin_notes || ''
      });
    }

    return history;
  };

  // Function to load document images
  const loadDocumentImage = useCallback(async (docId) => {
    if (!docId) return;
    
    try {
      setImageLoading(prev => ({ ...prev, [docId]: true }));
      const response = await verificationService.getAdminVerificationDocument(docId);
      
      // The response is already a Blob object, no need for response.data
      const imageUrl = URL.createObjectURL(response);
      
      setDocumentImages(prev => ({
        ...prev,
        [docId]: imageUrl
      }));
    } catch (err) {
      console.error(`Error loading document image for ${docId}:`, err);
      // Add the docId to a "failed" list so we don't try to load it again
      setDocumentImages(prev => ({
        ...prev,
        [docId]: null // Mark as failed
      }));
    } finally {
      setImageLoading(prev => ({ ...prev, [docId]: false }));
    }
  }, []); // Remove documentImages dependency to avoid recreating this function

  // Only clean up blob URLs for docs that are completely removed from documentRequests
  useEffect(() => {
    const allDocIds = documentRequests.flatMap(doc => {
      // Get cached data for this document
      const cachedData = verificationDataCache[doc.reqId];
      return [
        cachedData?.document_id,
        cachedData?.document_id_2
      ].filter(Boolean);
    });
    
    // Get doc IDs that we have blob URLs for but are no longer in any document
    const unusedDocIds = Object.keys(documentImages).filter(
      docId => !allDocIds.includes(docId) && documentImages[docId] !== null
    );
    
    // Only clean up if we have URLs to clean up and they're truly unused
    if (unusedDocIds.length > 0) {
      // Revoke the unused blob URLs
      unusedDocIds.forEach(docId => {
        if (documentImages[docId]) {
          URL.revokeObjectURL(documentImages[docId]);
        }
      });
      
      // Remove the unused URLs from state
      setDocumentImages(prev => {
        const newState = { ...prev };
        unusedDocIds.forEach(docId => {
          delete newState[docId];
        });
        return newState;
      });
    }
    
    // Clean up all blob URLs on component unmount
    return () => {
      Object.values(documentImages).forEach(url => {
        if (url) URL.revokeObjectURL(url);
      });
    };
  }, [documentRequests, verificationDataCache]); // Remove documentImages dependency to avoid loops, on purpose.

  // Function to check if a blob URL is still valid
  const isBlobUrlValid = useCallback((url) => {
    try {
      // Simple check: if URL starts with blob: and we can create an img element with it
      return url && url.startsWith('blob:');
    } catch {
      return false;
    }
  }, []);

  // Load document images when verification data changes
  useEffect(() => {
    if (verificationData) {
      // Load primary document if exists and not already loaded with valid URL
      const primaryDocId = verificationData.document_id;
      if (primaryDocId && 
          (!documentImages[primaryDocId] || !isBlobUrlValid(documentImages[primaryDocId])) && 
          !imageLoading[primaryDocId]) {
        loadDocumentImage(primaryDocId);
      }
      
      // Load secondary document if exists and not already loaded with valid URL
      const secondaryDocId = verificationData.document_id_2;
      if (secondaryDocId && 
          (!documentImages[secondaryDocId] || !isBlobUrlValid(documentImages[secondaryDocId])) && 
          !imageLoading[secondaryDocId]) {
        loadDocumentImage(secondaryDocId);
      }
    }
  }, [verificationData, loadDocumentImage, documentImages, imageLoading, isBlobUrlValid]);

  if (initializing) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (documentRequests.length === 0) {
    return <ErrorPage type={'404'} showBackHome={false} />;
  }

  const renderVerificationStatus = (status) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: <ClockCircleOutlined className="mr-2" /> },
      approved: { color: 'bg-green-100 text-green-800', icon: <CheckCircleOutlined className="mr-2" /> },
      rejected: { color: 'bg-red-100 text-red-800', icon: <CloseCircleOutlined className="mr-2" /> },
      withdrawn: { color: 'bg-purple-100 text-purple-800', icon: <ExclamationCircleOutlined className="mr-2" /> }
    };
    const config = statusConfig[status?.toLowerCase()] || statusConfig.pending;

    return (
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        {config.icon}
        {t(`proofDetails.verificationStatus.${status?.toLowerCase() || 'pending'}`)}
      </div>
    );
  };

  const renderDocumentViewer = (documents) => {
    if (!documents?.length) {
      return (
        <Empty />
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {documents.map((doc, index) => {
          // Remove isPDF check since no PDFs will be shown
          const isLoading = imageLoading[doc.id] || false;
          const imageUrl = documentImages[doc.id] || null;
          
          // Determine document side title
          const sideTitle = doc.side === 'front' 
            ? t('documents.frontSide', 'Front Side') 
            : doc.side === 'back' 
              ? t('documents.backSide', 'Back Side') 
              : '';
          
          // Remove _2 suffix from document type
          const displayType = doc.type?.replace(/_2$/, '');
          
          return (
            <div key={index} className="border rounded-lg overflow-hidden bg-white shadow-sm">
              <div className="p-4 border-b bg-gray-50 flex justify-between items-center">
                <Text strong>{t(`documents.${displayType}`)}</Text>
                {sideTitle && <Text className="text-gray-500">{sideTitle}</Text>}
              </div>
              <div className="p-4">
                <div className="aspect-w-16 aspect-h-9 bg-gray-100 rounded-lg flex items-center justify-center">
                  {isLoading ? (
                    <div className="w-full h-64 flex items-center justify-center">
                      <Skeleton.Image active className="!w-full !h-48" style={{ width: '100%', height: '280px' }} />
                    </div>
                  ) : imageUrl && (
                    <Image
                      src={imageUrl}
                      alt={`${sideTitle || ''} ${doc.file_name || t('proofDetails.documentViewer.image')}`}
                      className="object-contain w-full h-full"
                      fallback={fallbackImage}
                    />
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Render document details for the current document
  const renderDocumentDetails = () => {

    if (loading) {
      return (
        <div className="flex justify-center items-center py-20">
          <Spin size="large" />
        </div>
      );
    }


    if (!verificationData) {
      return (
          <Empty />
      );
    }


    const docType = verificationData.verification_type;
    
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center mx-4">
          <Text strong className="text-lg">
            {t(`documents.${docType}`) || docType || t('proofDetails.reviewTitle')}
          </Text>
          {renderVerificationStatus(verificationData.status)}
        </div>
        
        <div className="bg-gray-50 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Document information section */}
            <div>
              <Text strong className="block mb-4">{t('proofDetails.sections.documentReview.fields.documentInformation')}</Text>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <Text className="text-gray-500">{t('proofDetails.sections.documentReview.fields.documentType')}</Text>
                  <Text strong>{t(`documents.${docType}`) || docType || '-'}</Text>
                </div>
                <div className="flex justify-between">
                  <Text className="text-gray-500">{t('proofDetails.sections.userInfo.fields.submissionTime')}</Text>
                  <Text strong>{formatSimpleDateTime(verificationData.submitted_at || verificationData.created_at)}</Text>
                </div>
              </div>
            </div>

            {/* User information section */}
            <div>
              <Text strong className="block mb-4">{t('proofDetails.sections.userInfo.title')}</Text>
              {verificationData?.specifics && (
                <DocumentDetailsView 
                  documentType={docType}
                  specifics={verificationData.specifics}
                  userData={verificationData}
                />
              )}
            </div>
          </div>
        </div>
        
        {/* Document viewer */}
        <div className="mt-6">
          <Text strong className="block mb-4 ml-4">{t('proofDetails.sections.documentReview.title')}</Text>
          {renderDocumentViewer(
            [
              // Primary document (front side)
              ...(verificationData.document_id ? [{
                id: verificationData.document_id,
                type: docType,
                file_name: verificationData.file_name,
                mime_type: verificationData.mime_type,
                side: 'front'
              }] : []),
              // Secondary document (back side)
              ...(verificationData.document_id_2 ? [{
                id: verificationData.document_id_2,
                type: `${docType}_2`,
                file_name: verificationData.file_name_2,
                mime_type: verificationData.mime_type_2,
                side: 'back'
              }] : [])
            ]
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white">
      {/* Mobile Action Button */}
      {isMobile && (
        <div className="fixed bottom-4 right-4 z-50">
          <button
            onClick={() => setSidebarVisible(true)}
            className="w-14 h-14 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center shadow-lg hover:bg-blue-100 transition-colors"
          >
            <img src={approveIcon} alt="Approve" className="w-7 h-7" />
          </button>
        </div>
      )}

      <div className={`max-w-[1600px] mx-auto ${isMobile ? 'block' : 'flex'}`}>
        {/* Main Content */}
        <div className={`${isMobile ? 'w-full' : 'flex-1'} p-4 ${!isMobile && 'border-r border-gray-200'}`}>
          {/* Single tabs system for document requests */}
          {documentRequests.length > 1 ? (
            <Tabs
              activeKey={currentDocIndex.toString()}
              onChange={(key) => handleDocumentTabChange(parseInt(key, 10))}
              items={documentRequests.map((doc, index) => {
                // Create a friendly name for the tab
                const docType = doc.type || 'Document';
                const status = doc.status || 'pending';
                
                // Create icon based on document type
                let icon;
                if (docType === 'address_proof') {
                  icon = <Icon icon="hugeicons:address-book" className="mr-1" />;
                } else if (docType === 'student_id') {
                  icon = <Icon icon="ph:student" className="mr-1" />;
                } else {
                  icon = <Icon icon="la:id-card" className="mr-1" />;
                }
                
                return {
                  key: index.toString(),
                  label: (
                    <span className="flex items-center">
                      {icon}
                      {t(`documents.${docType}`) || docType.replace(/_/g, ' ')}
                      {status !== 'pending' && (
                        <span className="ml-2">
                          {status === 'approved' ? <CheckCircleOutlined className="text-green-500" /> : <CloseCircleOutlined className="text-red-500" />}
                        </span>
                      )}
                    </span>
                  ),
                  children: renderDocumentDetails()
                };
              })}
            />
          ) : (
            // For single document, just render the details without tabs
            renderDocumentDetails()
          )}
        </div>

        {/* Approval Sidebar Component */}
        {!loading && verificationData && (
          <ApprovalSidebar
            status={verificationData?.status || 'pending'}
            reason={reason}
            setReason={setReason}
            actionLoading={submitting}
            accountHistory={getAccountHistory()}
            isMobile={isMobile}
            sidebarVisible={sidebarVisible}
            setSidebarVisible={setSidebarVisible}
            onStatusChange={handleStatusChange}
            type="verification"
            title={verificationData?.verification_type ?
              (t(`documents.${verificationData.verification_type}`) || verificationData.verification_type.replace(/_/g, ' ')) :
              t('proofDetails.reviewTitle')}
          />
        )}
      </div>
    </div>
  );
};

export default ProofDetails;