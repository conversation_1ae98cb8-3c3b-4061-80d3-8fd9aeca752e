import { useState } from 'react';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';

/**
 * A custom hook for handling form submissions with standardized loading and error states
 * 
 * @param {Function} submitFunction - The function to call for form submission
 * @param {Object} options - Configuration options
 * @param {Function} options.onSuccess - Callback function to execute on successful submission
 * @param {Function} options.onError - Callback function to execute on submission error
 * @param {string} options.successMessage - Message to display on success
 * @param {string} options.errorMessage - Message to display on error
 * @param {boolean} options.showSuccessMessage - Whether to show a success message
 * @param {boolean} options.showErrorMessage - Whether to show an error message
 * @returns {Object} - { handleSubmit, isSubmitting, error }
 */
export const useFormSubmit = (submitFunction, {
  onSuccess,
  onError,
  successMessage: defaultSuccessMessage,
  errorMessage: defaultErrorMessage,
  showSuccessMessage = true,
  showErrorMessage = true
} = {}) => {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (values, options = {}) => {
    const { successMessage, errorMessage } = options;
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      const result = await submitFunction(values);
      
      if (showSuccessMessage) {
        message.success(successMessage || defaultSuccessMessage || t('common.form.submitSuccess'));
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (err) {
      setError(err);
      
      // Determine error message to display
      const displayErrorMessage = errorMessage || 
        defaultErrorMessage || 
        (err.data?.message || err.message || t('common.form.submitError'));
      
      if (showErrorMessage) {
        message.error(displayErrorMessage);
      }
      
      if (onError) {
        onError(err);
      }
      
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting,
    error,
    setError
  };
};

export default useFormSubmit; 