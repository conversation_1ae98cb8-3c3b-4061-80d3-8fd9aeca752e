# API Endpoint Test Plan (Comprehensive)

This document provides a comprehensive, procedural set of test cases for the API endpoints using `curl`, following a highly structured format.

## 1. Preamble & Setup

### 1.1. Test Case Format
Each endpoint test follows this structure:
- **Authentication:** Required permissions.
- **Test Cases:** A list of specific scenarios.
    - **Scenario Name:** e.g., "Success Case", "Unauthorized Case".
        - **Description:** The purpose of the test.
        - **Curl Command:** The runnable command(s).
        - **Request Payload:** The body of the request.
        - **Expected Feedback:**
            - **Status Code:** The expected HTTP status.
            - **Response Payload:** The expected JSON response.
        - **Actual Feedback:**
            - **Status Code:** (To be filled during testing)
            - **Response Payload:** (To be filled during testing)

### 1.2. Procedural Testing & Dependencies
This is a **procedural test plan**. The tests **must** be run in the order they appear, as they depend on data created in previous steps (e.g., tokens, resource IDs).

**Action:** Before running the tests, create shell variables. The script commands below will populate them as you run the tests.

```bash
# Base URL for the API
export BASE_URL="http://localhost:8080"

# Tokens will be populated by auth tests
export USER_A_TOKEN=""
export USER_A_REFRESH_TOKEN=""
export USER_B_TOKEN=""
export ADMIN_TOKEN=""
export SUPERADMIN_TOKEN=""

# IDs will be populated by creation tests
export USER_A_ID=""
export USER_B_ID=""
export ADMIN_USER_ID=""
export ORG_A_ID=""
export EVENT_A_ID=""
export POST_A_ID=""
export REGISTRATION_ID=""
# etc.
```

---
## 2. Authentication & User Setup

This section covers the initial authentication flows to acquire the necessary tokens for all subsequent tests.

### **POST** `/api/v1/authn/staff/login/initiate` & `/api/v1/authn/staff/login/verify` (Superadmin)

*   **Authentication:** None
*   **Test Cases:**
    1.  **Superadmin Login (Success)**
        *   **Description:** Logs in the default superadmin to get a `SUPERADMIN_TOKEN`. This is a critical first step.
        *   **Curl Command:**
            ```bash
            export SA_CODE_VERIFIER=$(openssl rand -base64 32 | tr -d '\n' | tr '/+' '_-');
            export SA_CODE_CHALLENGE=$(echo -n $SA_CODE_VERIFIER | shasum -a 256 | xxd -r -p | base64 | tr -d '\n' | tr '/+' '_-' | tr -d '=');
            export SA_STATE=$(uuidgen)

            # Initiate Login
            curl -s -X POST $BASE_URL/api/v1/authn/staff/login/initiate -H "Content-Type: application/json" -d '{"email": "<EMAIL>", "client_id": "default_client", "code_challenge": "'$SA_CODE_CHALLENGE'", "code_challenge_method": "S256", "state": "'$SA_STATE'"}'

            # Verify Login and capture token
            SUPERADMIN_LOGIN_JSON=$(curl -s -X POST $BASE_URL/api/v1/authn/staff/login/verify -H "Content-Type: application/json" -d '{"state": "'$SA_STATE'", "email": "<EMAIL>", "password": "password", "code_verifier": "'$SA_CODE_VERIFIER'"}')
            export SUPERADMIN_TOKEN=$(echo $SUPERADMIN_LOGIN_JSON | jq -r .access_token)
            echo "SUPERADMIN_TOKEN set: $SUPERADMIN_TOKEN"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200` (for the final verify call)
            *   **Console Output:** `SUPERADMIN_TOKEN set: <jwt_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Console Output:** SUPERADMIN_TOKEN set: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAxIiwicm9sZSI6InN1cGVyYWRtaW4iLCJpc3MiOiJNZW1iZXJzaGlwU2FhcyIsInN1YiI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMSIsImV4cCI6MTc0OTkxNDg3MCwibmJmIjoxNzQ5OTEzOTcwLCJpYXQiOjE3NDk5MTM5NzB9.3Mwmz-2ee8rzzvCEDGaV24x7wZr91pbl7T_Gd6ZwkYA
            *   **Response Payload:** {"message":"Staff login successful.","user_id":"00000000-0000-0000-0000-000000000001","access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAxIiwicm9sZSI6InN1cGVyYWRtaW4iLCJpc3MiOiJNZW1iZXJzaGlwU2FhcyIsInN1YiI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMSIsImV4cCI6MTc0OTkxNDg3MCwibmJmIjoxNzQ5OTEzOTcwLCJpYXQiOjE3NDk5MTM5NzB9.3Mwmz-2ee8rzzvCEDGaV24x7wZr91pbl7T_Gd6ZwkYA","refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAxIiwicm9sZSI6InN1cGVyYWRtaW4iLCJpc3MiOiJNZW1iZXJzaGlwU2Fhcy1SZWZyZXNoIiwic3ViIjoiMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAxIiwiZXhwIjoxNzUwNTE4NzcwLCJuYmYiOjE3NDk5MTM5NzAsImlhdCI6MTc0OTkxMzk3MCwianRpIjoiNzBmZDllM2UtNzkyNS00MTVmLWFjMGQtNTM4NjA2NjBmN2IwIn0.umh7BYJtApr_e2eHY_PbpXU4ulbU1H_5b7M9c2hm0BU"}

### **POST** `/api/v1/authn/register/phone/initiate` & `/api/v1/authn/register/phone/verify` (User A)

*   **Authentication:** None
*   **Test Cases:**
    1.  **Register New User "A" (Success)**
        *   **Description:** Registers a new regular user "A" and captures their tokens.
        *   **Curl Command:**
            ```bash
            export USR_A_VERIFIER=$(openssl rand -base64 32 | tr -d '\n' | tr '/+' '_-');
            export USR_A_CHALLENGE=$(echo -n $USR_A_VERIFIER | shasum -a 256 | xxd -r -p | base64 | tr -d '\n' | tr '/+' '_-' | tr -d '=');
            export USR_A_STATE=$(uuidgen);

            # Initiate Registration
            curl -s -X POST $BASE_URL/api/v1/authn/register/phone/initiate -H "Content-Type: application/json" -d '{"phone": "+85299990001", "client_id": "default_client", "code_challenge": "'$USR_A_CHALLENGE'", "code_challenge_method": "S256", "state": "'$USR_A_STATE'"}'

            # Verify Registration and capture tokens
            USER_A_LOGIN_JSON=$(curl -s -X POST $BASE_URL/api/v1/authn/register/phone/verify -H "Content-Type: application/json" -d '{"state": "'$USR_A_STATE'", "otp": "123456", "code_verifier": "'$USR_A_VERIFIER'", "display_name": "User A"}')
            export USER_A_TOKEN=$(echo $USER_A_LOGIN_JSON | jq -r .access_token)
            export USER_A_REFRESH_TOKEN=$(echo $USER_A_LOGIN_JSON | jq -r .refresh_token)
            export USER_A_ID=$(echo $USER_A_LOGIN_JSON | jq -r .user_id)
            echo "USER_A_TOKEN set: $USER_A_TOKEN"
            echo "USER_A_ID set: $USER_A_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `201` (for the final verify call)
            *   **Console Output:** `USER_A_TOKEN set: <jwt_string>` and `USER_A_ID set: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** USER_A_TOKEN set: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTJhMGMxMTItZDZhNy00NzdhLTlhYmUtMDFlNGQ2ZjY3NWQ2Iiwicm9sZSI6InVzZXIiLCJpc3MiOiJNZW1iZXJzaGlwU2FhcyIsInN1YiI6IjEyYTBjMTEyLWQ2YTctNDc3YS05YWJlLTAxZTRkNmY2NzVkNiIsImV4cCI6MTc0OTkxNDkzNiwibmJmIjoxNzQ5OTE0MDM2LCJpYXQiOjE3NDk5MTQwMzZ9.R04SmJMcozS66xTeci4WdRi8zW-bvO4ITlefu0KHSE0 and USER_A_ID set: 12a0c112-d6a7-477a-9abe-01e4d6f675d6
            *   **Response Payload:** {"message":"User registered and phone verified successfully.","user_id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTJhMGMxMTItZDZhNy00NzdhLTlhYmUtMDFlNGQ2ZjY3NWQ2Iiwicm9sZSI6InVzZXIiLCJpc3MiOiJNZW1iZXJzaGlwU2FhcyIsInN1YiI6IjEyYTBjMTEyLWQ2YTctNDc3YS05YWJlLTAxZTRkNmY2NzVkNiIsImV4cCI6MTc0OTkxNDkzNiwibmJmIjoxNzQ5OTE0MDM2LCJpYXQiOjE3NDk5MTQwMzZ9.R04SmJMcozS66xTeci4WdRi8zW-bvO4ITlefu0KHSE0","refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTJhMGMxMTItZDZhNy00NzdhLTlhYmUtMDFlNGQ2ZjY3NWQ2Iiwicm9sZSI6InVzZXIiLCJpc3MiOiJNZW1iZXJzaGlwU2FhUy1SZWZyZXNoIiwic3ViIjoiMTJhMGMxMTItZDZhNy00NzdhLTlhYmUtMDFlNGQ2ZjY3NWQ2IiwiZXhwIjoxNzUwNTE4ODM2LCJuYmYiOjE3NDk5MTQwMzYsImlhdCI6MTc0OTkxNDAzNiwianRpIjoiNGFiMGMyNzYtNmUyNi00ZWVlLTg2YzYtZDE2ODk4MzFjN2NkIn0.1vqjfzwD50O8sijCwnFEP1K5J9KTeOjOj62Nm0l1u-8"}

### **POST** `/api/v1/authn/phone/check`

*   **Authentication:** None
*   **Test Cases:**
    1.  **Non-existent user test**
        *   **Description:** Checks a phone number that is not registered in the system.
        *   **Curl Command:**
            ```bash
            curl -i -X POST $BASE_URL/api/v1/authn/phone/check -H "Content-Type: application/json" -d '{"phone": "+85200000000"}'
            ```
        *   **Request Payload:** `{"phone": "+85200000000"}`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** `{"exists": false}`
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"exists":false}
    2.  **Existing user test**
        *   **Description:** Checks User A's phone number, which now exists.
        *   **Curl Command:**
            ```bash
            curl -i -X POST $BASE_URL/api/v1/authn/phone/check -H "Content-Type: application/json" -d '{"phone": "+85299990001"}'
            ```
        *   **Request Payload:** `{"phone": "+85299990001"}`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** `{ "exists": true, "user": { "id": "...", "display_name": "User A", "profile_picture_url": null } }`
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"exists":true,"user":{"id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","display_name":"User A","hashed_password":null,"profile_picture_url":null,"phone":"+85299990001","phone_verified_at":"2025-06-14T23:13:56.044129+08:00","email":null,"email_verified_at":null,"phone_otp_channel":"whatsapp","interface_language":"en","communication_language":"en","enable_app_notifications":true,"enable_whatsapp_notifications":true,"enable_sms_notifications":false,"enable_email_notifications":false,"created_at":"2025-06-14T23:13:56.044446+08:00","updated_at":"2025-06-14T23:13:56.044446+08:00","role":"user"}}

### **POST** `/api/v1/authn/token/refresh`

*   **Authentication:** None (requires a valid refresh token in payload)
*   **Test Cases:**
    1.  **Refresh User A's Token (Success)**
        *   **Description:** Uses User A's refresh token to get a new access token.
        *   **Curl Command:**
            ```bash
            curl -i -X POST $BASE_URL/api/v1/authn/token/refresh -H "Content-Type: application/json" -d '{"refresh_token": "'$USER_A_REFRESH_TOKEN'"}'
            ```
        *   **Request Payload:** `{"refresh_token": "<user_a_refresh_token>"}`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object containing a new `access_token`.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTJhMGMxMTItZDZhNy00NzdhLTlhYmUtMDFlNGQ2ZjY3NWQ2Iiwicm9sZSI6InVzZXIiLCJpc3MiOiJNZW1iZXJzaGlwU2FhcyIsInN1YiI6IjEyYTBjMTEyLWQ2YTctNDc3YS05YWJlLTAxZTRkNmY2NzVkNiIsImV4cCI6MTc0OTkxNTI5NiwibmJmIjoxNzQ5OTE0Mzk2LCJpYXQiOjE3NDk5MTQzOTZ9._9c52GM4OXASu_xYFe6XsX0atP2J1St286DSVcv-6KM","refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTJhMGMxMTItZDZhNy00NzdhLTlhYmUtMDFlNGQ2ZjY3NWQ2Iiwicm9sZSI6InVzZXIiLCJpc3MiOiJNZW1iZXJzaGlwU2FhUy1SZWZyZXNoIiwic3ViIjoiMTJhMGMxMTItZDZhNy00NzdhLTlhYmUtMDFlNGQ2ZjY3NWQ2IiwiZXhwIjoxNzUwNTE5MTk2LCJuYmYiOjE3NDk5MTQzOTYsImlhdCI6MTc0OTkxNDM5NiwianRpIjoiZmU1MWYyYmEtZmYwMS00YmFmLWI2YmEtZjIzZjQ0Zjg5NDcxIn0.qO4-gZY1OKmQu6r1NuuY6wPl45KYWlXgQ6yVptQTnQg","token_type":"Bearer"}

### **POST** `/api/v1/authn/logout`

*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Logout User A (Success)**
        *   **Description:** Invalidates User A's refresh token. A new refresh token will be needed for the next test.
        *   **Curl Command:**
            ```bash
            curl -i -X POST $BASE_URL/api/v1/authn/logout -H "Authorization: Bearer $USER_A_TOKEN" -H "Content-Type: application/json" -d '{"refresh_token": "'$USER_A_REFRESH_TOKEN'"}'
            ```
        *   **Request Payload:** `{"refresh_token": "<user_a_refresh_token>"}`
        *   **Expected Feedback:**
            *   **Status Code:** `204`
            *   **Response Payload:** (None)
        *   **Actual Feedback:**
            *   **Status Code:** 204
            *   **Response Payload:** (None)

---
## 3. User Profile Management

**Note:** The following tests require a valid `USER_A_TOKEN`. We will re-login User A to get a fresh set of tokens after the logout test.

### **POST** `/api/v1/authn/phone/otp/initiate` & `/api/v1/authn/phone/otp/verify` (User A Login)
*   **Authentication:** None
*   **Test Cases:**
    1.  **Login User "A" (Success)**
        *   **Description:** Logs in user "A" to get fresh tokens for profile tests.
        *   **Curl Command:**
            ```bash
            export USR_A_VERIFIER_LOGIN=$(openssl rand -base64 32 | tr -d '\n' | tr '/+' '_-');
            export USR_A_CHALLENGE_LOGIN=$(echo -n $USR_A_VERIFIER_LOGIN | shasum -a 256 | xxd -r -p | base64 | tr -d '\n' | tr '/+' '_-' | tr -d '=');
            export USR_A_STATE_LOGIN=$(uuidgen);

            curl -s -X POST $BASE_URL/api/v1/authn/phone/otp/initiate -H "Content-Type: application/json" -d '{"phone": "+85299990001", "client_id": "default_client", "code_challenge": "'$USR_A_CHALLENGE_LOGIN'", "code_challenge_method": "S256", "state": "'$USR_A_STATE_LOGIN'"}'

            USER_A_LOGIN_JSON=$(curl -s -X POST $BASE_URL/api/v1/authn/phone/otp/verify -H "Content-Type: application/json" -d '{"state": "'$USR_A_STATE_LOGIN'", "otp": "123456", "code_verifier": "'$USR_A_VERIFIER_LOGIN'"}')
            export USER_A_TOKEN=$(echo $USER_A_LOGIN_JSON | jq -r .access_token)
            export USER_A_REFRESH_TOKEN=$(echo $USER_A_LOGIN_JSON | jq -r .refresh_token)
            echo "USER_A_TOKEN re-set: $USER_A_TOKEN"
            ```
        *   **Expected Feedback:**
            *   **Console Output:** `USER_A_TOKEN re-set: <jwt_string>`
        *   **Actual Feedback:**
            *   **Console Output:** USER_A_TOKEN re-set: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTJhMGMxMTItZDZhNy00NzdhLTlhYmUtMDFlNGQ2ZjY3NWQ2Iiwicm9sZSI6InVzZXIiLCJpc3MiOiJNZW1iZXJzaGlwU2FhcyIsInN1YiI6IjEyYTBjMTEyLWQ2YTctNDc3YS05YWJlLTAxZTRkNmY2NzVkNiIsImV4cCI6MTc0OTkxNTM3NiwibmJmIjoxNzQ5OTE0NDc2LCJpYXQiOjE3NDk5MTQ0NzZ9.N_JSt677jZY8M0G9pYdqfrOl2Z_sDUXWqE2N976V3zY

### **GET** `/api/v1/users/me`

*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Get User Profile (Success)**
        *   **Description:** Fetches the profile of the currently logged-in user (User A).
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/users/me -H "Authorization: Bearer $USER_A_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object with User A's full profile details.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","display_name":"User A","phone":"+85299990001","phone_verified_at":"2025-06-14T23:13:56.044129+08:00","phone_otp_channel":"whatsapp","interface_language":"en","communication_language":"en","enable_app_notifications":true,"enable_whatsapp_notifications":true,"enable_sms_notifications":false,"enable_email_notifications":false,"verification_status":{"hk_id_card":false,"mainland_china_id_card":false,"mainland_travel_permit":false,"passport":false,"hk_youth_plus":false,"address_proof":false,"student_id":false,"home_visit":false},"created_at":"2025-06-14T23:13:56.044446+08:00","updated_at":"2025-06-14T23:13:56.044446+08:00"}
    2.  **Get User Profile (Failure, No Token)**
        *   **Description:** Attempts to fetch a profile without an auth token.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/users/me`
        *   **Expected Feedback:**
            *   **Status Code:** `401`
            *   **Response Payload:** `{"error":"Unauthorized","message":"Authentication token is missing."}`
        *   **Actual Feedback:**
            *   **Status Code:** 401
            *   **Response Payload:** `{"error":"Unauthorized","message":"Authentication token is missing."}`

### **PATCH** `/api/v1/users/me`

*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Update User Profile (Success)**
        *   **Description:** Updates the `display_name` of User A.
        *   **Curl Command:**
            ```bash
            curl -i -X PATCH $BASE_URL/api/v1/users/me \
            -H "Authorization: Bearer $USER_A_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"display_name": "User A (Updated)"}'
            ```
        *   **Request Payload:** `{"display_name": "User A (Updated)"}`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** The full user profile object with the updated name.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","display_name":"User A (Updated)","phone":"+85299990001","phone_verified_at":"2025-06-14T23:13:56.044129+08:00","phone_otp_channel":"whatsapp","interface_language":"en","communication_language":"en","enable_app_notifications":true,"enable_whatsapp_notifications":true,"enable_sms_notifications":false,"enable_email_notifications":false,"verification_status":{"hk_id_card":false,"mainland_china_id_card":false,"mainland_travel_permit":false,"passport":false,"hk_youth_plus":false,"address_proof":false,"student_id":false,"home_visit":false},"created_at":"2025-06-14T23:13:56.044446+08:00","updated_at":"2025-06-14T23:22:56.480606+08:00"}

### **POST** `/api/v1/users/me/profile-picture`

*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Upload Profile Picture (Success)**
        *   **Description:** Uploads a dummy file as a profile picture for User A.
        *   **Curl Command:**
            ```bash
            touch pic.txt
            curl -i -X POST $BASE_URL/api/v1/users/me/profile-picture \
            -H "Authorization: Bearer $USER_A_TOKEN" \
            -F "file=@./pic.txt"
            rm pic.txt
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object with the `url` of the uploaded picture.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"url":"/uploads/profile-pictures/12a0c112-d6a7-477a-9abe-01e4d6f675d6/3f0b90a3-a774-444a-9aa6-9f3ab793b2d8.png"}

---
## 4. Organization Management

### **POST** `/api/v1/organizations`

*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Create an Organization (Success)**
        *   **Description:** User A creates a new organization.
        *   **Curl Command:**
            ```bash
            export ORG_A_ID=$(curl -s -X POST $BASE_URL/api/v1/organizations \
            -H "Authorization: Bearer $USER_A_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"name": "Curl Test Org A", "description": "Test org created by User A"}' | jq -r .id)
            echo "Created ORG_A_ID: $ORG_A_ID"
            ```
        *   **Request Payload:** `{"name": "Curl Test Org A", "description": "..."}`
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Console Output:** `Created ORG_A_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** Created ORG_A_ID: c3942d79-0c80-4e03-906e-85855309f999
            *   **Response Payload:** {"id":"c3942d79-0c80-4e03-906e-85855309f999","name":"Curl Test Org A","description":"Test org created by User A","is_default_org":false,"status":"pending_setup","created_at":"2025-06-14T23:26:49.40386+08:00","updated_at":"2025-06-14T23:26:49.40386+08:00"}

### **GET** `/api/v1/organizations`

*   **Authentication:** None
*   **Test Cases:**
    1.  **List Organizations (Public)**
        *   **Description:** Anyone can list public organizations. Org A should appear in the list.
        *   **Curl Command:**
            ```bash
            curl -i -X GET "$BASE_URL/api/v1/organizations?limit=100" | grep '"id":"'$ORG_A_ID'"'
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** The line containing Org A's ID should be printed.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Console Output:** [{"id":"00000000-0000-0000-0000-000000000002","name":"Haytech","description":"The global default organization for all users.","is_default_org":true,"status":"active","created_at":"2025-06-14T23:05:23.559945+08:00","updated_at":"2025-06-14T23:05:23.916115+08:00"},{"id":"c3942d79-0c80-4e03-906e-85855309f999","name":"Curl Test Org A","description":"Test org created by User A","is_default_org":false,"status":"pending_setup","created_at":"2025-06-14T23:26:49.40386+08:00","updated_at":"2025-06-14T23:26:49.40386+08:00"}]

---
## 5. Multi-User Interaction & Advanced Org Management

**Preamble:** This section requires a second user, "User B", to test interactions like joining/leaving organizations.

### **POST** `/api/v1/authn/register/phone/initiate` & `/api/v1/authn/register/phone/verify` (User B)
*   **Authentication:** None
*   **Test Cases:**
    1.  **Register New User "B" (Success)**
        *   **Description:** Registers a new regular user "B" for interaction tests.
        *   **Curl Command:**
            ```bash
            export USR_B_VERIFIER=$(openssl rand -base64 32 | tr -d '\n' | tr '/+' '_-');
            export USR_B_CHALLENGE=$(echo -n $USR_B_VERIFIER | shasum -a 256 | xxd -r -p | base64 | tr -d '\n' | tr '/+' '_-' | tr -d '=');
            export USR_B_STATE=$(uuidgen);

            curl -s -X POST $BASE_URL/api/v1/authn/register/phone/initiate -H "Content-Type: application/json" -d '{"phone": "+85299990002", "client_id": "default_client", "code_challenge": "'$USR_B_CHALLENGE'", "code_challenge_method": "S256", "state": "'$USR_B_STATE'"}'

            USER_B_LOGIN_JSON=$(curl -s -X POST $BASE_URL/api/v1/authn/register/phone/verify -H "Content-Type: application/json" -d '{"state": "'$USR_B_STATE'", "otp": "123456", "code_verifier": "'$USR_B_VERIFIER'", "display_name": "User B"}')
            export USER_B_TOKEN=$(echo $USER_B_LOGIN_JSON | jq -r .access_token)
            export USER_B_ID=$(echo $USER_B_LOGIN_JSON | jq -r .user_id)
            echo "USER_B_TOKEN set: $USER_B_TOKEN"
            echo "USER_B_ID set: $USER_B_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Response Payload:** A success message.
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Response Payload:** {"user_id":"20d78dbb-b6fc-4a48-a6d0-71cd9966c06f","organization_id":"c3942d79-0c80-4e03-906e-85855309f999","role":"member","joined_at":"2025-06-14T23:33:29.817378+08:00","is_active":true,"notifications_enabled":true}

### **GET** `/api/v1/organizations/:orgId`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Get Organization Details (Success)**
        *   **Description:** User A (a member) fetches details for Org A.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/organizations/$ORG_A_ID -H "Authorization: Bearer $USER_A_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object with Org A's details.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"c3942d79-0c80-4e03-906e-85855309f999","name":"Curl Test Org A","description":"Test org created by User A","is_default_org":false,"status":"pending_setup","created_at":"2025-06-14T23:26:49.40386+08:00","updated_at":"2025-06-14T23:26:49.40386+08:00"}

### **POST** `/api/v1/organizations/:orgId/join`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **User B Joins Org A (Success)**
        *   **Description:** User B sends a request to join Org A.
        *   **Curl Command:** `curl -i -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/join -H "Authorization: Bearer $USER_B_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Response Payload:** A success message.
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Response Payload:** {"user_id":"20d78dbb-b6fc-4a48-a6d0-71cd9966c06f","organization_id":"c3942d79-0c80-4e03-906e-85855309f999","role":"member","joined_at":"2025-06-14T23:33:29.817378+08:00","is_active":true,"notifications_enabled":true}

### **GET** `/api/v1/users/me/organizations`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **List User B's Organizations (Success)**
        *   **Description:** Verify that User B is now a member of Org A.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/users/me/organizations -H "Authorization: Bearer $USER_B_TOKEN" | grep "$ORG_A_ID"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** The line containing Org A's ID should be printed.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Console Output:** [{"id":"c3942d79-0c80-4e03-906e-85855309f999","name":"Curl Test Org A","description":"Test org created by User A","is_default_org":false,"status":"pending_setup","created_at":"2025-06-14T23:26:49.40386+08:00","updated_at":"2025-06-14T23:26:49.40386+08:00"},{"id":"00000000-0000-0000-0000-000000000002","name":"Haytech","description":"The global default organization for all users.","is_default_org":true,"status":"active","created_at":"2025-06-14T23:05:23.559945+08:00","updated_at":"2025-06-14T23:05:23.916115+08:00"}]

### **DELETE** `/api/v1/organizations/:orgId/leave`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **User B Leaves Org A (Success)**
        *   **Description:** User B leaves Org A.
        *   **Curl Command:** `curl -i -X DELETE $BASE_URL/api/v1/organizations/$ORG_A_ID/leave -H "Authorization: Bearer $USER_B_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `204`
        *   **Actual Feedback:**
            *   **Status Code:** 204
            *   **Response Payload:** (None)

### **PUT** `/api/v1/organizations/:orgId`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Update Organization (Failure, Not Admin)**
        *   **Description:** User A (owner but not admin in this context) fails to update Org A.
        *   **Curl Command:** `curl -i -X PUT $BASE_URL/api/v1/organizations/$ORG_A_ID -H "Authorization: Bearer $USER_A_TOKEN" -H "Content-Type: application/json" -d '{"name": "Attempted Name Change"}'`
        *   **Expected Feedback:**
            *   **Status Code:** `403`
        *   **Actual Feedback:**
            *   **Status Code:** 403
            *   **Response Payload:** {"message":"permission denied: user is not a member of the organization or organization does not exist"}
    2.  **Update Organization (Success, Superadmin)**
        *   **Description:** Superadmin successfully updates Org A's details.
        *   **Curl Command:**
            ```bash
            curl -i -X PUT $BASE_URL/api/v1/organizations/$ORG_A_ID \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"name": "Curl Test Org A (Updated)", "description": "Updated by Superadmin"}'
            ```
        *   **Request Payload:** `{"name": "Curl Test Org A (Updated)", "description": "..."}`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** The updated organization object.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"d2b898e0-4aa4-41d2-9259-7a4971605bae","name":"Curl Test Org A (Updated) 1749918304","description":"Updated by Superadmin","is_default_org":false,"created_at":"2025-06-15T00:25:04.374433+08:00","updated_at":"2025-06-15T00:25:04.382208+08:00","image_url":null,"theme_color":null,"status":"pending_setup"}

### **POST** `/api/v1/organizations/:orgId/logo`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Upload Org Logo (Success, Superadmin)**
        *   **Description:** Superadmin uploads a logo for Org A.
        *   **Curl Command:**
            ```bash
            touch logo.txt
            curl -i -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/logo \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -F "file=@./logo.txt"
            rm logo.txt
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object containing the `url` for the logo.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"d2b898e0-4aa4-41d2-9259-7a4971605bae","name":"Curl Test Org A (Updated) 1749918304","description":"Updated by Superadmin","is_default_org":false,"image_url":"http://localhost:8080/uploads/organizations/d2b898e0-4aa4-41d2-9259-7a4971605bae/logos/5d30622b-f99b-45f0-a112-463dfdedfc8c.png","status":"pending_setup","created_at":"2025-06-15T00:25:04.374433+08:00","updated_at":"2025-06-15T00:25:05.092256+08:00"}

### **GET** `/api/v1/users/me/uuid`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Get User A UUID (Success)**
        *   **Description:** Retrieves the UUID for the authenticated User A.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/users/me/uuid -H "Authorization: Bearer $USER_A_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** `{"uuid": "<user_a_uuid_string>"}` (Should match `$USER_A_ID`)
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"uuid":"12a0c112-d6a7-477a-9abe-01e4d6f675d6"}

### **GET** `/api/v1/users/me/stats`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Get User A Stats (Success)**
        *   **Description:** Retrieves activity and engagement stats for User A.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/users/me/stats -H "Authorization: Bearer $USER_A_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object with fields like `totalEvents`, `userJoinedAt`, `volunteerEvents`, and an array of `topAttendedEventTags` with `name_en`, `name_zh_hk`, `name_zh_cn`, and `count`.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"totalEvents":0,"userJoinedAt":"2025-06-14T23:13:56.044446+08:00","volunteerEvents":0,"monthlyAttendedEvents":[],"topAttendedEventTags":[{"name_en":"Community","name_zh_hk":"社區","name_zh_cn":"社区","count":5},{"name_en":"Environment","name_zh_hk":"環境","name_zh_cn":"环境","count":3}]}

---
## 6. User Verification

### **GET** `/api/v1/verification-types`
*   **Authentication:** None
*   **Test Cases:**
    1.  **Get Verification Types (Success)**
        *   **Description:** Retrieves the list of all available verification types.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/verification-types`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** An array of verification type objects, each with a `key`, `name`, and `description`.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** [{"key":"passport","name":"Passport","langcode":"en"},{"key":"passport","name":"護照","langcode":"zh_HK"},{"key":"passport","name":"护照","langcode":"zh_CN"},{"key":"hk_id_card","name":"Hong Kong Identity Card","langcode":"en"},{"key":"hk_id_card","name":"香港身份證","langcode":"zh_HK"},{"key":"hk_id_card","name":"香港身份证","langcode":"zh_CN"},{"key":"student_id","name":"Student ID Card","langcode":"en"},{"key":"student_id","name":"學生證","langcode":"zh_HK"},{"key":"student_id","name":"学生证","langcode":"zh_CN"},{"key":"address_proof","name":"Address Proof","langcode":"en"},{"key":"address_proof","name":"地址證明","langcode":"zh_HK"},{"key":"address_proof","name":"地址证明","langcode":"zh_CN"},{"key":"hk_youth_plus","name":"HK Youth+","langcode":"en"},{"key":"hk_youth_plus","name":"HK Youth+","langcode":"zh_HK"},{"key":"hk_youth_plus","name":"HK Youth+","langcode":"zh_CN"},{"key":"mainland_china_id_card","name":"Mainland China ID Card","langcode":"en"},{"key":"mainland_china_id_card","name":"中國大陸身份證","langcode":"zh_HK"},{"key":"mainland_china_id_card","name":"中国大陆身份证","langcode":"zh_CN"},{"key":"mainland_travel_permit","name":"Mainland Travel Permit for Hong Kong and Macao Residents","langcode":"en"},{"key":"mainland_travel_permit","name":"港澳居民來往內地通行證","langcode":"zh_HK"},{"key":"mainland_travel_permit","name":"港澳居民来往内地通行证","langcode":"zh_CN"},{"key":"home_visit","name":"Home Visit","langcode":"en"},{"key":"home_visit","name":"家訪","langcode":"zh_
HK"},{"key":"home_visit","name":"家访","langcode":"zh_CN"}]

### **POST** `/api/v1/users/me/verifications`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Submit Verification Request (Success)**
        *   **Description:** User A submits a verification request for a "Student ID" with a dummy document. This is a multipart request.
        *   **Curl Command:**
            ```bash
            touch student_id_doc.pdf
            # The 'data' field contains a JSON string.
            export VERIFICATION_REQ_ID=$(curl -s -X POST "$BASE_URL/api/v1/users/me/verifications" \
            -H "Authorization: Bearer $USER_A_TOKEN" \
            -F "document=@./student_id_doc.pdf" \
            -F "data={\"verification_type\":\"student_id\", \"student_id_school_name\":\"Test University\", \"student_id_number\":\"12345\"}" | jq -r .id)
            rm student_id_doc.pdf
            echo "Created VERIFICATION_REQ_ID: $VERIFICATION_REQ_ID"
            ```
        *   **Request Payload:** `multipart/form-data` with a `document` file and a `data` field containing the JSON payload.
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** `Created VERIFICATION_REQ_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** {"id":"1624829a-0da6-4f89-962d-f6579c2fb4e4","user_id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","verification_type":"student_id","status":"pending","document_id":"e146afd7-c1e8-4f83-97cb-6876eeee73ba","file_name":"student_id_doc.pdf","mime_type":"application/pdf","submitted_at":"2025-06-15T01:09:13.877895+08:00","created_at":"2025-06-15T01:09:13.877895+08:00","updated_at":"2025-06-15T01:09:13.877895+08:00"}

### **GET** `/api/v1/users/me/verifications`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **List User's Verification Requests (Success)**
        *   **Description:** User A retrieves their list of submitted verification requests.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/users/me/verifications -H "Authorization: Bearer $USER_A_TOKEN" | grep "$VERIFICATION_REQ_ID"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** The line containing the newly created verification request ID should be printed.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Console Output:** [{"id":"1624829a-0da6-4f89-962d-f6579c2fb4e4","user_id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","verification_type":"student_id","status":"pending","document_id":"e146afd7-c1e8-4f83-97cb-6876eeee73ba","file_name":"student_id_doc.pdf","mime_type":"application/pdf","submitted_at":"2025-06-15T01:09:13.877895+08:00","created_at":"2025-06-15T01:09:13.877895+08:00","updated_at":"2025-06-15T01:09:13.877895+08:00","user_display_name":"User A (Updated)","specifics":{"id":"bc777ea4-891d-44e9-824b-0ed6abeef4ab","verification_request_id":"1624829a-0da6-4f89-962d-f6579c2fb4e4","school_name":null,"grade":null,"expiry_date":"","created_at":"2025-06-15T01:09:13.87886+08:00","updated_at":"2025-06-15T01:09:13.87886+08:00"}},{"id":"83f9cce4-ef4c-47df-b266-7653fc80a7b8","user_id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","verification_type":"student_id","status":"pending","document_id":"90075fc4-8b8d-4fd8-8920-5d2ad9974bc1","file_name":"student_id_doc.pdf","mime_type":"application/pdf","submitted_at":"2025-06-15T01:08:24.967853+08:00","created_at":"2025-06-15T01:08:24.967853+08:00","updated_at":"2025-06-15T01:08:24.967853+08:00","user_display_name":"User A (Updated)","specifics":{"id":"b06d52c8-68a4-4869-8988-3bdc912cbf46","verification_request_id":"83f9cce4-ef4c-47df-b266-7653fc80a7b8","school_name":null,"grade":null,"expiry_date":"","created_at":"2025-06-15T01:08:24.970768+08:00","updated_at":"2025-06-15T01:08:24.970768+08:00"}}]

### **GET** `/api/v1/users/me/verifications/:reqID`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **Get Specific Verification Request (Success)**
        *   **Description:** User A retrieves the full details for the specific request they just created.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/users/me/verifications/$VERIFICATION_REQ_ID -H "Authorization: Bearer $USER_A_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A detailed verification request object with `id` matching `$VERIFICATION_REQ_ID`.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"83f9cce4-ef4c-47df-b266-7653fc80a7b8","user_id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","verification_type":"student_id","status":"pending","document_id":"90075fc4-8b8d-4fd8-8920-5d2ad9974bc1","file_name":"student_id_doc.pdf","mime_type":"application/pdf","submitted_at":"2025-06-15T01:08:24.967853+08:00","created_at":"2025-06-15T01:08:24.967853+08:00","updated_at":"2025-06-15T01:08:24.967853+08:00","user_display_name":"User A (Updated)","specifics":{"id":"b06d52c8-68a4-4869-8988-3bdc912cbf46","verification_request_id":"83f9cce4-ef4c-47df-b266-7653fc80a7b8","school_name":null,"grade":null,"expiry_date":"","created_at":"2025-06-15T01:08:24.970768+08:00","updated_at":"2025-06-15T01:08:24.970768+08:00"}}

    2.  **List Events with Pagination (Page 1, Limit 2)**
        *   **Description:** Fetches the first page of events with a limit of 2 events per page.
        *   **Curl Command:** `curl -i -X GET "$BASE_URL/api/v1/events?page=1&limit=2"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON array containing up to 2 event objects, and pagination metadata.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** []
            
    3.  **List Events Filtered by Organization ID**
        *   **Description:** Fetches events belonging to a specific organization (`$ORG_A_ID`).
        *   **Curl Command:** `curl -i -X GET "$BASE_URL/api/v1/events?organization_id=$ORG_A_ID"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON array of event objects for the specified organization.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** []
            
    4.  **List Events Filtered by Status (Upcoming)**
        *   **Description:** Fetches events with the status "published".
        *   **Curl Command:** `curl -i -X GET "$BASE_URL/api/v1/events?status=published"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON array of upcoming event objects.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** []
            
    5.  **List Events Sorted by Start Date (Ascending)**
        *   **Description:** Fetches events sorted by their start date in ascending order.
        *   **Curl Command:** `curl -i -X GET "$BASE_URL/api/v1/events?sort_by=start_date_asc"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON array of event objects sorted by start date.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** []
                
    6.  **List Events with Multiple Filters (Organization ID and Upcoming Status)**
        *   **Description:** Fetches upcoming events for a specific organization.
        *   **Curl Command:** `curl -i -X GET "$BASE_URL/api/v1/events?organization_id=$ORG_A_ID&status=published"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON array of upcoming event objects for the specified organization.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** []
            
    7.  **List Events with Invalid Filter Parameter Value**
        *   **Description:** Attempts to fetch events with an invalid value for a filter parameter (e.g., status).
        *   **Curl Command:** `curl -i -X GET "$BASE_URL/api/v1/events?status=invalid_status_value"`
        *   **Expected Feedback:**
            *   **Status Code:** `400` (or appropriate error code for bad request)
            *   **Response Payload:** An error message indicating the invalid parameter.
        *   **Actual Feedback:**
            *   **Status Code:** 400
            *   **Response Payload:** {"message":"Invalid filter parameters: Key: 'ListPublicEventsRequest.Status' Error:Field validation for 'Status' failed on the 'oneof' tag"}
               
    8.  **List Events (Authenticated User - User A)**
        *   **Description:** Fetches a list of events as an authenticated user (User A). Response might differ from public view (e.g., show user-specific event details or more events).
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/events -H "Authorization: Bearer $USER_A_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON array of event objects, potentially tailored for User A.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** []
            
---
## 7. Event Management

### **POST** `/api/v1/organizations/:orgId/events`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Create Event (Failure, Not Admin)**
        *   **Description:** User A, who is not an admin of Org A, fails to create an event.
        *   **Curl Command:**
            ```bash
            curl -i -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/events \
            -H "Authorization: Bearer $USER_A_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"title": "Unauthorized Event", "status": "draft", "location_type": "online", "start_time": "2025-01-01T10:00:00Z", "end_time": "2025-01-01T12:00:00Z"}'
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `403`
        *   **Actual Feedback:**
            *   **Status Code:** 403
    2.  **Create Event "Event A" (Success, Superadmin)**
        *   **Description:** Superadmin creates a new event in Org A and publishes it.
        *   **Curl Command:**
            ```bash
            export EVENT_A_ID=$(curl -s -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/events \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"title": "Community Cleanup Day", "status": "published", "location_type": "physical", "location_full_address": "Central Park", "start_time": "2025-04-22T09:00:00Z", "end_time": "2025-04-22T12:00:00Z", "participant_limit": 50}' | jq -r .id)
            echo "Created EVENT_A_ID: $EVENT_A_ID"
            ```
        *   **Request Payload:** `{ "title": "Community Cleanup Day", ... }`
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Console Output:** `Created EVENT_A_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** {"id":"1455876a-7675-4b56-9593-99501160d317","organization_id":"c3942d79-0c80-4e03-906e-85855309f999","organization_name":"Curl Test Org A","title":"Community Cleanup Day","jsonContent":{"type":"doc","content":[{"type":"paragraph","content":[{"text":"Let us clean up the community!","type":"text"}]}]},"location_type":"physical","location_full_address":"Central Park","start_time":"2025-04-22T17:00:00+08:00","end_time":"2025-04-22T20:00:00+08:00","status":"published","participant_limit":50,"requires_approval_for_registration":false,"created_by_user_id":"00000000-0000-0000-0000-000000000001","created_at":"2025-06-15T01:25:44.240053+08:00","updated_at":"2025-06-15T01:25:44.240053+08:00","published_at":"2025-06-15T01:25:44.239949+08:00","registered_count":0,"waitlisted_count":0,"attended_count":0}

### **GET** `/api/v1/events`
*   **Authentication:** Optional
*   **Test Cases:**
    1.  **List Public Events with Filters (Success)**
        *   **Description:** Test various query parameters for the public event listing to find the newly created Event A.
        *   **Curl Command:**
            ```bash
            # Test search term
            curl -s "$BASE_URL/api/v1/events?search_term=Cleanup" | jq '.[] | select(.id == "'$EVENT_A_ID'")'
            # Test status
            curl -s "$BASE_URL/api/v1/events?status=published" | jq '.[] | select(.id == "'$EVENT_A_ID'")'
            # Test date range
            curl -s "$BASE_URL/api/v1/events?start_date=2025-04-22&end_date=2025-04-23" | jq '.[] | select(.id == "'$EVENT_A_ID'")'
            ```
        *   **Expected Feedback:** Each command should return a JSON object for Event A, confirming it's found.
        *   **Actual Feedback:** 
            --- Checking search_term=Cleanup ---
            {
            "id": "413fd066-c66b-4609-8236-bac7240080dd",
            "organization_id": "c3942d79-0c80-4e03-906e-85855309f999",
            "organization_name": "Curl Test Org A",
            "title": "Community Cleanup Day",
            "jsonContent": {
                "type": "doc",
                "content": [
                {
                    "type": "paragraph",
                    "content": [
                    {
                        "text": "Let us clean up the community!",
                        "type": "text"
                    }
                    ]
                }
                ]
            },
            "location_type": "physical",
            "location_full_address": "Central Park",
            "start_time": "2025-04-22T17:00:00+08:00",
            "end_time": "2025-04-22T20:00:00+08:00",
            "status": "published",
            "participant_limit": 50,
            "published_at": "2025-06-15T01:25:11.535282+08:00",
            "registered_count": 0,
            "waitlisted_count": 0,
            "attended_count": 0
            }
            --- Checking status=published ---
            {
            "id": "413fd066-c66b-4609-8236-bac7240080dd",
            "organization_id": "c3942d79-0c80-4e03-906e-85855309f999",
            "organization_name": "Curl Test Org A",
            "title": "Community Cleanup Day",
            "jsonContent": {
                "type": "doc",
                "content": [
                {
                    "type": "paragraph",
                    "content": [
                    {
                        "text": "Let us clean up the community!",
                        "type": "text"
                    }
                    ]
                }
                ]
            },
            "location_type": "physical",
            "location_full_address": "Central Park",
            "start_time": "2025-04-22T17:00:00+08:00",
            "end_time": "2025-04-22T20:00:00+08:00",
            "status": "published",
            "participant_limit": 50,
            "published_at": "2025-06-15T01:25:11.535282+08:00",
            "registered_count": 0,
            "waitlisted_count": 0,
            "attended_count": 0
            }
            --- Checking date range ---
            {
            "id": "413fd066-c66b-4609-8236-bac7240080dd",
            "organization_id": "c3942d79-0c80-4e03-906e-85855309f999",
            "organization_name": "Curl Test Org A",
            "title": "Community Cleanup Day",
            "jsonContent": {
                "type": "doc",
                "content": [
                {
                    "type": "paragraph",
                    "content": [
                    {
                        "text": "Let us clean up the community!",
                        "type": "text"
                    }
                    ]
                }
                ]
            },
            "location_type": "physical",
            "location_full_address": "Central Park",
            "start_time": "2025-04-22T17:00:00+08:00",
            "end_time": "2025-04-22T20:00:00+08:00",
            "status": "published",
            "participant_limit": 50,
            "published_at": "2025-06-15T01:25:11.535282+08:00",
            "registered_count": 0,
            "waitlisted_count": 0,
            "attended_count": 0
            }

### **GET** `/api/v1/events/:eventId`
*   **Authentication:** Optional
*   **Test Cases:**
    1.  **Get Public Event Details (Success)**
        *   **Description:** Retrieves the full public details for Event A.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/events/$EVENT_A_ID`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A single detailed public event object for Event A.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"413fd066-c66b-4609-8236-bac7240080dd","organization_id":"c3942d79-0c80-4e03-906e-85855309f999","organization_name":"Curl Test Org A","title":"Community Cleanup Day","jsonContent":{"type":"doc","content":[{"type":"paragraph","content":[{"text":"Let us clean up the community!","type":"text"}]}]},"location_type":"physical","location_full_address":"Central Park","start_time":"2025-04-22T17:00:00+08:00","end_time":"2025-04-22T20:00:00+08:00","status":"published","participant_limit":75,"requires_approval_for_registration":false,"created_by_user_id":"00000000-0000-0000-0000-000000000001","created_at":"2025-06-15T01:25:11.535384+08:00","updated_at":"2025-06-15T01:30:01.659096+08:00","published_at":"2025-06-15T01:25:11.535282+08:00","registered_count":0,"waitlisted_count":0,"attended_count":0}

### **PATCH** `/api/v1/organizations/:orgId/events/:eventId`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Partially Update Event Details (Success, Superadmin)**
        *   **Description:** Superadmin updates the participant limit for Event A.
        *   **Curl Command:**
            ```bash
            curl -i -X PATCH $BASE_URL/api/v1/organizations/$ORG_A_ID/events/$EVENT_A_ID \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"participant_limit": 75}'
            ```
        *   **Request Payload:** `{"participant_limit": 75}`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** The updated event object with the new participant limit.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"413fd066-c66b-4609-8236-bac7240080dd","organization_id":"c3942d79-0c80-4e03-906e-85855309f999","organization_name":"Curl Test Org A","title":"Community Cleanup Day","jsonContent":{"type":"doc","content":[{"type":"paragraph","content":[{"text":"Let us clean up the community!","type":"text"}]}]},"location_type":"physical","location_full_address":"Central Park","start_time":"2025-04-22T17:00:00+08:00","end_time":"2025-04-22T20:00:00+08:00","status":"published","participant_limit":75,"requires_approval_for_registration":false,"created_by_user_id":"00000000-0000-0000-0000-000000000001","created_at":"2025-06-15T01:25:11.535384+08:00","updated_at":"2025-06-15T01:30:01.659096+08:00","published_at":"2025-06-15T01:25:11.535282+08:00","registered_count":0,"waitlisted_count":0,"attended_count":0}

### **POST** `/api/v1/organizations/:orgId/events/:eventId/media`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Add Media to Event (Success, Superadmin)**
        *   **Description:** Superadmin adds a media item to Event A.
        *   **Curl Command:**
            ```bash
            touch event_media.jpg
            export EVENT_MEDIA_ID=$(curl -s -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/events/$EVENT_A_ID/media \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -F "file=@./event_media.jpg" | jq -r .id)
            rm event_media.jpg
            echo "Created EVENT_MEDIA_ID: $EVENT_MEDIA_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** `Created EVENT_MEDIA_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** {"id":"b66c1a71-775a-45d3-a416-82172ab975f4","file_name":"event_media.jpg","file_path":"/uploads/event-media/c3942d79-0c80-4e03-906e-85855309f999/413fd066-c66b-4609-8236-bac7240080dd/034d9a69-88b7-4e8e-b594-34972938a1a6-event_media.jpg","file_type":"image/jpeg","file_size":0,"uploaded_at":"2025-06-15T01:39:08.110584+08:00","is_banner":false}

### **PATCH** `/api/v1/organizations/:orgId/events/:eventId/media/:mediaItemId/set-banner`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Set Event Banner (Success, Superadmin)**
        *   **Description:** Superadmin sets the newly uploaded media item as the banner for Event A.
        *   **Curl Command:**
            ```bash
            curl -i -X PATCH $BASE_URL/api/v1/organizations/$ORG_A_ID/events/$EVENT_A_ID/media/$EVENT_MEDIA_ID/set-banner \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
        *   **Actual Feedback:**
            *   **Status Code:**

### **DELETE** `/api/v1/organizations/:orgId/events/:eventId/media/:mediaItemId`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Delete Media Item (Success, Superadmin)**
        *   **Description:** Superadmin deletes the media item associated with Event A.
        *   **Curl Command:** (omitted for brevity)
        *   **Expected Feedback:**
            *   **Status Code:** `204`
            *   **Response Payload:** (None)
        *   **Actual Feedback:**
            *   **Status Code:** 204
            *   **Response Payload:** (None)

---
## 8. Advanced Event Management & Interactions

### **POST** `/api/v1/me/event-registrations`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **User B Registers for Event A (Success)**
        *   **Description:** User B, who is not an org member yet in this flow, registers for the public Event A.
        *   **Curl Command:** (omitted for brevity)
        *   **Request Payload:** `{"event_id": "<event_a_id>"}`
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Console Output:** `Created REGISTRATION_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** Created REGISTRATION_ID: a1b5ea25-f17e-47a5-a74c-9065c72d1a87

### **GET** `/api/v1/organizations/:orgId/events/statistics`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Get Organization Event Statistics (Success, Superadmin)**
        *   **Description:** Superadmin retrieves event statistics for Org A. After creating one event and one registration, the stats should reflect this.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/organizations/$ORG_A_ID/events/statistics -H "Authorization: Bearer $SUPERADMIN_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object with event statistics for the organization, e.g., `{"total_events":1, "total_registrations":1, "total_attendees":0, "average_attendance_rate":0}`.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** `[{"event_id":"...","event_title":"Community Cleanup Day","event_start_time":"...","event_event_status":"published","total_registered_or_finalized":1,"total_attended":0,"total_paid_registrations":0}]`
    2.  **Get Organization Event Statistics (Failure, Not Admin)**
        *   **Description:** User A (not an admin) fails to retrieve event statistics for Org A.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/organizations/$ORG_A_ID/events/statistics -H "Authorization: Bearer $USER_A_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `403`
        *   **Actual Feedback:**
            *   **Status Code:** 403
            *   **Response Payload:** `{"error":"Forbidden","message":"Forbidden: Admin or Superadmin role required"}`

### **POST** `/api/v1/organizations/:orgId/events/:eventId/media`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Add Media to Event (Success, Superadmin)**
        *   **Description:** Superadmin adds a media item to Event A.
        *   **Curl Command:** (omitted for brevity)
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Console Output:** `Created EVENT_MEDIA_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** Created EVENT_MEDIA_ID: ebae034c-c56e-4de3-8be2-9d58cef88733

### **PATCH** `/api/v1/organizations/:orgId/events/:eventId/media/:mediaItemId/set-banner`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Set Event Banner (Success, Superadmin)**
        *   **Description:** Superadmin sets the previously uploaded media item as the banner for Event A.
        *   **Curl Command:** (omitted for brevity)
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** The media item object with `"is_banner": true`.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"ebae034c-c56e-4de3-8be2-9d58cef88733","file_name":"event_media.jpg","file_path":"/uploads/event-media/c3942d79-0c80-4e03-906e-85855309f999/413fd066-c66b-4609-8236-bac7240080dd/c28c62ee-0848-4ff2-89ff-17f0b395e4a7-event_media.jpg","file_type":"image/jpeg","file_size":0,"uploaded_at":"2025-06-15T01:38:11.007664+08:00","is_banner":true}

### **DELETE** `/api/v1/organizations/:orgId/events/:eventId/media/:mediaItemId`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Delete Media Item (Success, Superadmin)**
        *   **Description:** Superadmin deletes the media item associated with Event A.
        *   **Curl Command:**
            ```bash
            curl -i -X DELETE $BASE_URL/api/v1/organizations/$ORG_A_ID/events/$EVENT_A_ID/media/$EVENT_MEDIA_ID -H "Authorization: Bearer $SUPERADMIN_TOKEN"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `204`
        *   **Actual Feedback:**
            *   **Status Code:** 204

---
## 9. Volunteer Management

### **GET** `/api/v1/users/me/volunteer/applications`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **List User A's Applications with status filter (Success)**
        *   **Description:** User A lists their own applications, filtering by 'pending' and 'approved' status.
        *   **Curl Command:**
            ```bash
            # List PENDING applications - should return the one created in the next step
            curl -s -X GET "$BASE_URL/api/v1/users/me/volunteer/applications?status=pending" -H "Authorization: Bearer $USER_A_TOKEN" | jq .

            # List APPROVED applications - should be empty initially
            curl -s -X GET "$BASE_URL/api/v1/users/me/volunteer/applications?status=approved" -H "Authorization: Bearer $USER_A_TOKEN" | jq .

            # List with NO status filter - should return all applications
            curl -s -X GET "$BASE_URL/api/v1/users/me/volunteer/applications" -H "Authorization: Bearer $USER_A_TOKEN" | jq .
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200` for all requests.
            *   **Response Payload:** The first request should show the pending application. The second should be an empty array. The third should show all applications.
        *   **Actual Feedback:**
            *   **Status Code:**
            *   **Response Payload:**

### **POST** `/api/v1/organizations/:orgId/volunteer/apply`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **User A Applies for General Volunteering in Org A (Success)**
        *   **Description:** User A applies to become a qualified general volunteer for Org A. This application will start in 'pending' status.
        *   **Curl Command:**
            ```bash
            export ORG_VOLUNTEER_APP_ID=$(curl -s -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/volunteer/apply \
            -H "Authorization: Bearer $USER_A_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"motivation": "I am passionate about this cause."}' | jq -r .application_id)
            echo "Created ORG_VOLUNTEER_APP_ID: $ORG_VOLUNTEER_APP_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Console Output:** `Created ORG_VOLUNTEER_APP_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** Created ORG_VOLUNTEER_APP_ID: b95853de-bc57-4381-9743-3dd251b18f4a
            *   **Response Payload:** {"application_id":"b95853de-bc57-4381-9743-3dd251b18f4a","message":"Volunteer application submitted successfully."}

### **GET** `/api/v1/admin/organizations/:orgId/volunteer/applications/:appId`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Admin Views General Volunteer Application (Success)**
        *   **Description:** Superadmin retrieves User A's general volunteer application for review.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/admin/organizations/$ORG_A_ID/volunteer/applications/$ORG_VOLUNTEER_APP_ID -H "Authorization: Bearer $SUPERADMIN_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object with the application details.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** A JSON object with the application details.

### **PATCH** `/api/v1/admin/organizations/:orgId/volunteer/applications/:appId/review`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Admin Approves General Volunteer Application (Success)**
        *   **Description:** Superadmin approves User A's application.
        *   **Curl Command:**
            ```bash
            curl -i -X PATCH $BASE_URL/api/v1/admin/organizations/$ORG_A_ID/volunteer/applications/$ORG_VOLUNTEER_APP_ID/review \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"status": "approved", "admin_notes": "Welcome aboard!"}'
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** A JSON object with the application details.

### **POST** `/api/v1/events/:eventId/volunteer-applications`
*   **Authentication:** Bearer Token
*   **Test Cases:**
    1.  **User B Applies to Volunteer for Event A (Success)**
        *   **Description:** User B applies to be a volunteer for the specific Event A.
        *   **Curl Command:**
            ```bash
            export EVENT_VOLUNTEER_APP_ID=$(curl -s -X POST $BASE_URL/api/v1/events/$EVENT_A_ID/volunteer-applications \
            -H "Authorization: Bearer $USER_B_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"application_notes_by_user": "I can help with setup."}' | jq -r .id)
            echo "Created EVENT_VOLUNTEER_APP_ID: $EVENT_VOLUNTEER_APP_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `403`
            *   **Console Output:** `Not a qualified volunteer`
        *   **Actual Feedback:**
            *   **Status Code:** 403
            *   **Response Payload:** {"message":"User is not a qualified volunteer for the event's organization"}

---
## 10A. Tag Management (Admin)

### **POST** `/api/v1/post-tags`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Creates a Post Tag (Success)**
        *   **Description:** Superadmin creates a new global tag for posts.
        *   **Curl Command:** `(See test script)`
        *   **Request Payload:** `{"name_en": "Test Post Tag", "name_zh_hk": "測試帖子標籤", "name_zh_cn": "测试帖子标签"}`
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Response Payload:** A JSON object for the newly created post tag.
        *   **Actual Feedback:**
            *   **Status Code:** `201`
            *   **Response Payload:** `{"id":"<uuid_string>","name_en":"Test Post Tag","name_zh_hk":"測試帖子標籤","name_zh_cn":"测试帖子标签"}`

### **GET** `/api/v1/post-tags`
*   **Authentication:** None
*   **Test Cases:**
    1.  **List All Post Tags (Success)**
        *   **Description:** Retrieves a list of all post tags.
        *   **Curl Command:** `(See test script)`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** An array of post tag objects.
        *   **Actual Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** `[{"id":"...","name_en":"Community Update",...}]`

### **PATCH** `/api/v1/post-tags/:tagId`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Updates a Post Tag (Success)**
        *   **Description:** Superadmin updates an existing post tag's name.
        *   **Curl Command:**
            ```bash
            curl -i -X PATCH $BASE_URL/api/v1/post-tags/$POST_TAG_ID \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"name_en": "Test Post Tag (Updated)"}'
            ```
        *   **Request Payload:** `{"name_en": "Test Post Tag (Updated)"}`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object for the updated post tag.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** `{"id":"<post_tag_id>","name_en":"Test Post Tag (Updated)","name_zh_hk":"測試帖子標籤","name_zh_cn":"测试帖子标签"}`

### **GET** `/api/v1/post-tags/:tagId`
*   **Authentication:** None
*   **Test Cases:**
    1.  **Get Single Post Tag (Success)**
        *   **Description:** Retrieves a single post tag to verify the update.
        *   **Curl Command:**
            ```bash
            curl -i -X GET $BASE_URL/api/v1/post-tags/$POST_TAG_ID
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** An post tag object with the updated names.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** `{"id":"<post_tag_id>","name_en":"Test Post Tag (Updated)","name_zh_hk":"測試帖子標籤","name_zh_cn":"测试帖子标签"}`

### **DELETE** `/api/v1/post-tags/:tagId`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Deletes a Post Tag (Success)**
        *   **Description:** Superadmin deletes an existing post tag.
        *   **Curl Command:**
            ```bash
            curl -i -X DELETE $BASE_URL/api/v1/post-tags/$POST_TAG_ID \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `204`
        *   **Actual Feedback:**
            *   **Status Code:** 204
            *   **Response Payload:** (None)

### **POST** `/api/v1/event-tags`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Creates an Event Tag (Success)**
        *   **Description:** Superadmin creates a new global tag for events.
        *   **Curl Command:**
            ```bash
            export EVENT_TAG_A_ID=$(curl -s -X POST $BASE_URL/api/v1/event-tags -H "Authorization: Bearer $SUPERADMIN_TOKEN" -H "Content-Type: application/json" -d '{"name_en": "Test Event Tag", "name_zh_hk": "測試活動標籤", "name_zh_cn": "测试活动标签"}' | jq -r .id)
            echo "Created EVENT_TAG_A_ID: $EVENT_TAG_A_ID"
            ```
        *   **Request Payload:** `{"name_en": "Test Event Tag", "name_zh_hk": "測試活動標籤", "name_zh_cn": "测试活动标签"}`
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Response Payload:** A JSON object for the newly created event tag.
        *   **Actual Feedback:**
            *   **Status Code:** `201`
            *   **Response Payload:** `{"id":"<uuid_string>","name_en":"Test Event Tag","name_zh_hk":"測試活動標籤","name_zh_cn":"测试活动标签","is_globally_approved":false}`

### **PATCH** `/api/v1/event-tags/:tagId`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Updates an Event Tag (Success)**
        *   **Description:** Superadmin updates an existing event tag's name with a new randomized name.
        *   **Curl Command:**
            ```bash
            RANDOM_TAG_NAME="Test Event Tag (Updated) $(shuf -i 1000-9999 -n 1)"
            curl -i -X PATCH $BASE_URL/api/v1/event-tags/$EVENT_TAG_A_ID \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"name_en": "'"$RANDOM_TAG_NAME"'"}'
            ```
        *   **Request Payload:** `{"name_en": "Test Event Tag (Updated) <random_number>"}`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object for the updated event tag.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** `{"id":"841f1824-edba-45b5-8c4f-2872497ae6fd","name_en":"Test Event Tag (Updated) 6550",...}`

### **GET** `/api/v1/event-tags/:tagId`
*   **Authentication:** None
*   **Test Cases:**
    1.  **Get Single Event Tag (Success)**
        *   **Description:** Retrieves a single event tag to verify the update.
        *   **Curl Command:**
            ```bash
            curl -i -X GET $BASE_URL/api/v1/event-tags/$EVENT_TAG_A_ID
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** An event tag object with the updated names.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"<uuid_string>","name_en":"Test Event Tag Script","name_zh_hk":"測試活動標籤腳本","name_zh_cn":"测试活动标签脚本","is_globally_approved":false}

### **DELETE** `/api/v1/event-tags/:tagId`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Deletes an Event Tag (Success)**
        *   **Description:** Superadmin deletes an existing event tag.
        *   **Curl Command:**
            ```bash
            curl -i -X DELETE $BASE_URL/api/v1/event-tags/$EVENT_TAG_A_ID \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `204`
        *   **Actual Feedback:**
            *   **Status Code:** 204
            *   **Response Payload:** (None)

### **GET** `/api/v1/event-tags`
*   **Authentication:** None
*   **Test Cases:**
    1.  **List All Event Tags (Success)**
        *   **Description:** Retrieves a list of all event tags.
        *   **Curl Command:** `(See test script)`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** An array of event tag objects.
        *   **Actual Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** `[{"id":"...","name_en":"Exchange Group / Exchange Tour",...}]`

---
## 10. Content Management (Posts)

### **POST** `/api/v1/organizations/:orgId/posts`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Superadmin Creates a Post in Org A (Success)**
        *   **Description:** Superadmin creates a new published post and associates the new tag with it.
        *   **Curl Command:**
            ```bash
            export POST_A_ID=$(curl -s -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/posts \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"title": "Welcome to our New Blog", "content": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "This is the first post!", "type": "text"}]}]}, "status": "published", "tag_ids": ["'$POST_TAG_ID'"]}' | jq -r .id)
            echo "Created POST_A_ID: $POST_A_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Console Output:** `Created POST_A_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Response Payload:** {"id":"1455876a-7675-4b56-9593-99501160d317","organization_id":"c3942d79-0c80-4e03-906e-85855309f999","organization_name":"Curl Test Org A","title":"Community Cleanup Day","jsonContent":{"type":"doc","content":[{"type":"paragraph","content":[{"text":"Let us clean up the community!","type":"text"}]}]},"location_type":"physical","location_full_address":"Central Park","start_time":"2025-04-22T17:00:00+08:00","end_time":"2025-04-22T20:00:00+08:00","status":"published","participant_limit":50,"requires_approval_for_registration":false,"created_by_user_id":"00000000-0000-0000-0000-000000000001","created_at":"2025-06-15T01:25:44.240053+08:00","updated_at":"2025-06-15T01:25:44.240053+08:00","published_at":"2025-06-15T01:25:44.239949+08:00","registered_count":0,"waitlisted_count":0,"attended_count":0}

---
## 11. Full Admin Routes

### **GET** `/api/v1/admin/verifications`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Lists All Verification Requests (Success)**
        *   **Description:** Superadmin fetches all verification requests, which should include User A's request.
        *   **Curl Command:** `curl -i -X GET "$BASE_URL/api/v1/admin/verifications?limit=100" -H "Authorization: Bearer $SUPERADMIN_TOKEN" | grep "$VERIFICATION_REQ_ID"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** The line containing User A's verification request ID should be printed.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Console Output:** {"data":[{"request_id":"1624829a-0da6-4f89-962d-f6579c2fb4e4","user_id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","user_display_name":"User A (Updated)","user_email":"","verification_type":"student_id","status":"pending","submitted_at":"2025-06-15T01:09:13.877895+08:00","reviewed_at":"0001-01-01T00:00:00Z","reviewed_by_user_id":"00000000-0000-0000-0000-000000000000","document_id":"e146afd7-c1e8-4f83-97cb-6876eeee73ba","document_id_2":"00000000-0000-0000-0000-000000000000","file_name":"student_id_doc.pdf"},{"request_id":"83f9cce4-ef4c-47df-b266-7653fc80a7b8","user_id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","user_display_name":"User A (Updated)","user_email":"","verification_type":"student_id","status":"pending","submitted_at":"2025-06-15T01:08:24.967853+08:00","reviewed_at":"0001-01-01T00:00:00Z","reviewed_by_user_id":"00000000-0000-0000-0000-000000000000","document_id":"90075fc4-8b8d-4fd8-8920-5d2ad9974bc1","document_id_2":"00000000-0000-0000-0000-000000000000","file_name":"student_id_doc.pdf"}],"pagination":{"total_items":2,"total_pages":1,"current_page":1,"limit":100}}

### **POST** `/api/v1/admin/verifications/:reqID/review`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Approves a Verification Request (Success)**
        *   **Description:** Superadmin approves User A's student ID verification request. Also supports `PATCH`.
        *   **Curl Command:**
            ```bash
            curl -i -X POST $BASE_URL/api/v1/admin/verifications/$VERIFICATION_REQ_ID/review \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"status": "approved", "admin_notes": "Looks good."}'
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** The updated verification request object with status "approved".
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"83f9cce4-ef4c-47df-b266-7653fc80a7b8","user_id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","verification_type":"student_id","status":"approved","document_id":"90075fc4-8b8d-4fd8-8920-5d2ad9974bc1","submitted_at":"2025-06-15T01:08:24.967853+08:00","reviewed_at":"2025-06-15T02:01:21.625524+08:00","reviewed_by_user_id":"00000000-0000-0000-0000-000000000001","admin_notes":"Looks good.","created_at":"2025-06-15T01:08:24.967853+08:00","updated_at":"2025-06-15T02:01:21.625524+08:00","document_id_2":null}

### **GET** `/api/v1/admin/users`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **List Users (Success, Superadmin)**
        *   **Description:** Superadmin retrieves a list of all users in the system.
        *   **Curl Command:**
            ```bash
            curl -i -X GET $BASE_URL/api/v1/admin/users?page=1&limit=10 \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** An array of user objects.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"users":[{"id":"4ea0c0cd-c2bd-47d6-afe8-03579ffe14fe","display_name":"Staff User 1749919176","email":"<EMAIL>","email_verified_at":"2025-06-15T00:39:36.804404+08:00","role":"admin","created_at":"2025-06-15T00:39:36.80512+08:00","updated_at":"2025-06-15T00:39:36.80512+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"2219ae52-6124-41a3-b94b-d92cda62502a","display_name":"User A (Test)","phone":"+85299994337","phone_verified_at":"2025-06-15T00:25:04.360833+08:00","role":"user","created_at":"2025-06-15T00:25:04.360915+08:00","updated_at":"2025-06-15T00:25:04.360915+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"7254c4d0-7fb3-4445-825b-85b3a5f363fa","display_name":"User A (Test)","phone":"+85299997213","phone_verified_at":"2025-06-15T00:21:07.237341+08:00","role":"user","created_at":"2025-06-15T00:21:07.237476+08:00","updated_at":"2025-06-15T00:21:07.237476+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"de1d3378-8214-46bc-a7d4-f224b3536fa7","display_name":"User A (Test)","phone":"+85299999923","phone_verified_at":"2025-06-15T00:14:29.948665+08:00","role":"user","created_at":"2025-06-15T00:14:29.949101+08:00","updated_at":"2025-06-15T00:14:29.949101+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"6e4454a1-7f0a-429f-9b7a-8faa18ddd6e4","display_name":"User A (Test)","phone":"+85299999062","phone_verified_at":"2025-06-15T00:04:49.08885+08:00","role":"user","created_at":"2025-06-15T00:04:49.089201+08:00","updated_at":"2025-06-15T00:04:49.089201+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"e3ac2ac9-f1f7-425b-9669-2779a5adb66d","display_name":"User A (Test)","phone":"+85299994673","phone_verified_at":"2025-06-15T00:03:14.697777+08:00","role":"user","created_at":"2025-06-15T00:03:14.697852+08:00","updated_at":"2025-06-15T00:03:14.697852+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"e85c3804-f95c-455c-bf2f-92ad87b27e6c","display_name":"User A (Test)","phone":"+85299994769","phone_verified_at":"2025-06-15T00:02:54.793045+08:00","role":"user","created_at":"2025-06-15T00:02:54.793131+08:00","updated_at":"2025-06-15T00:02:54.793131+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"ab1af5f7-b675-4ea0-96fa-b5d6dd8ff3dc","display_name":"User A (Test)","phone":"+85299993855","phone_verified_at":"2025-06-15T00:02:33.879276+08:00","role":"user","created_at":"2025-06-15T00:02:33.879366+08:00","updated_at":"2025-06-15T00:02:33.879366+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"02925447-cfb2-48f2-9bf7-7c3cb51fe80e","display_name":"User A (Test)","phone":"+85299991748","phone_verified_at":"2025-06-15T00:01:01.772971+08:00","role":"user","created_at":"2025-06-15T00:01:01.773068+08:00","updated_at":"2025-06-15T00:01:01.773068+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""},{"id":"3d811d82-4ba8-4652-a644-d26df7a15c8e","display_name":"User A (Test)","phone":"+85299993650","phone_verified_at":"2025-06-14T23:59:53.675775+08:00","role":"user","created_at":"2025-06-14T23:59:53.675866+08:00","updated_at":"2025-06-14T23:59:53.675866+08:00","interface_language":"","communication_language":"","enable_app_notifications":false,"enable_whatsapp_notifications":false,"enable_sms_notifications":false,"enable_email_notifications":false,"phone_otp_channel":""}],"total_count":16,"page":1,"limit":10}

### **GET** `/api/v1/admin/users/:uuid`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Get User Details (Success)**
        *   **Description:** Superadmin retrieves the details for a specific user by UUID.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/admin/users/$USER_A_ID -H "Authorization: Bearer $SUPERADMIN_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** A JSON object with the user's details.
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"id":"12a0c112-d6a7-477a-9abe-01e4d6f675d6","display_name":"User A","phone":"+85299990001","phone_verified_at":"2025-06-14T23:13:56.044129+08:00","phone_otp_channel":"whatsapp","interface_language":"en","communication_language":"en","enable_app_notifications":true,"enable_whatsapp_notifications":true,"enable_sms_notifications":false,"enable_email_notifications":false,"verification_status":{"hk_id_card":false,"mainland_china_id_card":false,"mainland_travel_permit":false,"passport":false,"hk_youth_plus":false,"address_proof":false,"student_id":false,"home_visit":false},"created_at":"2025-06-14T23:13:56.044446+08:00","updated_at":"2025-06-14T23:13:56.044446+08:00"}
    2.  **Get User Details (Failure, Invalid UUID)**
        *   **Description:** Superadmin attempts to retrieve details for a user with an invalid UUID.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/admin/users/invalid-uuid -H "Authorization: Bearer $SUPERADMIN_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `400`
            *   **Response Payload:** `{"error":"Bad Request","message":"Invalid UUID format."}`
        *   **Actual Feedback:**
            *   **Status Code:** 400
            *   **Response Payload:** {"error":"Bad Request","message":"Invalid UUID format."}

### **POST** & **DELETE** `/api/v1/admin/users`
*   **Authentication:** Bearer Token (Superadmin Role Required)
*   **Test Cases:**
    1.  **Superadmin Creates and Deletes a User (Success)**
        *   **Description:** Superadmin creates a temporary staff user and then immediately deletes them.
        *   **Curl Command:**
            ```bash
            TEMP_ADMIN_ID=$(curl -s -X POST $BASE_URL/api/v1/admin/users -H "Authorization: Bearer $SUPERADMIN_TOKEN" -H "Content-Type: application/json" -d '{"email": "<EMAIL>", "password": "password123", "display_name": "Temp Admin", "role": "admin"}' | jq -r .id)
            echo "Created TEMP_ADMIN_ID: $TEMP_ADMIN_ID"
            curl -i -X DELETE $BASE_URL/api/v1/admin/users/$TEMP_ADMIN_ID -H "Authorization: Bearer $SUPERADMIN_TOKEN"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `204` (for the DELETE call).
            *   **Console Output:** `Created TEMP_ADMIN_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** `204`
            *   **Console Output:** `Created TEMP_ADMIN_ID: <uuid_string>`
    2.  **Create Staff User (Success, Superadmin)**
        *   **Description:** Superadmin creates a new user with the "staff" role.
        *   **Curl Command:**
            ```bash
            export NEW_STAFF_ID=$(curl -s -X POST $BASE_URL/api/v1/admin/users \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
              "email": "<EMAIL>",
              "display_name": "New Staff",
              "password": "NewStaffPassword123!",
              "role": "staff"
            }' | jq -r .id)
            echo "Created NEW_STAFF_ID: $NEW_STAFF_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Response Payload:** The newly created user object.
            *   **Console Output:** `Created NEW_STAFF_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Response Payload:** {"id":"e10adb55-2fa8-41b6-a03c-5f45333b6313","display_name":"Staff User 1749920306","email":"<EMAIL>","email_verified_at":"2025-06-15T00:58:26.651071+08:00","role":"admin","created_at":"2025-06-15T00:58:26.651323+08:00","updated_at":"2025-06-15T00:58:26.651323+08:00","interface_language":"en","communication_language":"en","enable_app_notifications":true,"enable_whatsapp_notifications":true,"enable_sms_notifications":true,"enable_email_notifications":true,"phone_otp_channel":"sms"}
            *   **Console Output:** STAFF_EMAIL_FOR_REPORT:<EMAIL>
    3.  **Create Staff User (Failure, Duplicate Email)**
        *   **Description:** Superadmin attempts to create a staff user with an email that already exists.
        *   **Curl Command:** `curl -i -X POST ... -d '{"email": "<EMAIL>", ...}'`
        *   **Expected Feedback:**
            *   **Status Code:** `409`
            *   **Response Payload:** `{"error":"Conflict","message":"User with email ... already exists"}`
        *   **Actual Feedback:**
            *   **Status Code:** 409
            *   **Response Payload:** {"error":"Conflict","message":"User <NAME_EMAIL> already exists"}
    4.  **Delete User (Success, Superadmin)**
        *   **Description:** Superadmin deletes the newly created staff user.
        *   **Curl Command:** `curl -i -X DELETE $BASE_URL/api/v1/admin/users/$NEW_STAFF_ID -H "Authorization: Bearer $SUPERADMIN_TOKEN"`
        *   **Expected Feedback:**
            *   **Status Code:** `204`
        *   **Actual Feedback:**
            *   **Status Code:** 204
            *   **Response Payload:** (Empty)

---
## 12. Miscellaneous

### **GET** `/health`
*   **Authentication:** None
*   **Test Cases:**
    1.  **Health Check (Success)**
        *   **Description:** Performs a health check of the API server.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/health`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** `{"status":"ok"}`
        *   **Actual Feedback:**
            *   **Status Code:** 200
            *   **Response Payload:** {"status":"ok"}

---
## 13. Content Management (Resources & Files)

### **POST** `/api/v1/organizations/:orgId/resources`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Create a Resource (Success, Superadmin)**
        *   **Description:** Superadmin creates a new resource (e.g., a downloadable guide) in Org A.
        *   **Curl Command:**
            ```bash
            export RESOURCE_A_ID=$(curl -s -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/resources \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"title": "Volunteer Handbook", "content": "{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Welcome to the team!\"}]}]}", "status": "published", "slug": "volunteer-handbook-2025"}' | jq -r .id)
            echo "Created RESOURCE_A_ID: $RESOURCE_A_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** `Created RESOURCE_A_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** Created RESOURCE_A_ID: 251bdc9e-486b-43df-93c3-d67ade76e309

### **POST** `/api/v1/organizations/:orgId/resources/:resourceId/files`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Upload a File to a Resource (Success, Superadmin)**
        *   **Description:** Superadmin uploads a file to the "Volunteer Handbook" resource.
        *   **Curl Command:**
            ```bash
            touch handbook.pdf
            export RESOURCE_FILE_ID=$(curl -s -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/resources/$RESOURCE_A_ID/files \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -F "file=@./handbook.pdf" | jq -r .id)
            rm handbook.pdf
            echo "Uploaded RESOURCE_FILE_ID: $RESOURCE_FILE_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** `Uploaded RESOURCE_FILE_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** Uploaded RESOURCE_FILE_ID: 5de3c17f-f4bd-4828-832a-7ad7a39b40d4

### **GET** `/api/v1/organizations/:orgId/resources`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **List Resources for Org A (Success, Superadmin)**
        *   **Description:** Superadmin lists all resources for Org A, which should include the new handbook.
        *   **Curl Command:** `curl -i -X GET "$BASE_URL/api/v1/organizations/$ORG_A_ID/resources" -H "Authorization: Bearer $SUPERADMIN_TOKEN" | grep $RESOURCE_A_ID`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** The line containing the Resource ID should be printed.
        *   **Actual Feedback:**
            *   **Status Code:** `200`
            *   **Console Output:** `SUCCESS: Found resource <uuid_string> in the list.`

---
## 14. Content Management (Resources - Files)

### **POST** `/api/v1/organizations/:orgId/resources/:resourceId/files`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Upload a File to a Resource (Success, Superadmin)**
        *   **Description:** Superadmin uploads a file to the "Volunteer Handbook" resource.
        *   **Curl Command:**
            ```bash
            touch handbook.pdf
            export RESOURCE_FILE_ID=$(curl -s -X POST $BASE_URL/api/v1/organizations/$ORG_A_ID/resources/$RESOURCE_A_ID/files \
            -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
            -F "file=@./handbook.pdf" | jq -r .id)
            rm handbook.pdf
            echo "Uploaded RESOURCE_FILE_ID: $RESOURCE_FILE_ID"
            ```
        *   **Expected Feedback:**
            *   **Status Code:** `201`
            *   **Console Output:** `Uploaded RESOURCE_FILE_ID: <uuid_string>`
        *   **Actual Feedback:**
            *   **Status Code:** 201
            *   **Console Output:** Uploaded RESOURCE_FILE_ID: 5de3c17f-f4bd-4828-832a-7ad7a39b40d4

---
## 15. Final Cleanup

### **DELETE** `/api/v1/organizations/:orgId`
*   **Authentication:** Bearer Token (Admin Role Required)
*   **Test Cases:**
    1.  **Delete Organization (Success, Superadmin)**
        *   **Description:** Superadmin cleans up by deleting the test organization.
        *   **Curl Command:** `(See test scripts for examples)`
        *   **Expected Feedback:**
            *   **Status Code:** `204`
        *   **Actual Feedback:**
            *   **Status Code:** `204`

---
### **GET** `/api/v1/swagger.yaml`
*   **Authentication:** None
*   **Test Cases:**
    1.  **Get OpenAPI Spec (Success)**
        *   **Description:** Checks that the swagger.yaml spec file is served correctly.
        *   **Curl Command:** `curl -i -X GET $BASE_URL/api/v1/swagger.yaml`
        *   **Expected Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** The raw YAML content of the API spec.
        *   **Actual Feedback:**
            *   **Status Code:** `200`
            *   **Response Payload:** (YAML content omitted for brevity)

*The test plan will be continued for all remaining endpoints...* 