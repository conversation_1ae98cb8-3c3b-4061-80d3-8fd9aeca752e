import {
    TeamOutlined,
    ExperimentOutlined,
    AppstoreOutlined,
    CoffeeOutlined,
    GlobalOutlined,
    BookOutlined,
    RocketOutlined,
    CodeOutlined,
    BulbOutlined,
    FlagOutlined,
} from '@ant-design/icons';

export const eventTypes = {
    Conference: {
        color: 'blue',
        icon: TeamOutlined
    },
    Workshop: {
        color: 'purple',
        icon: ExperimentOutlined
    },
    Webinar: {
        color: 'purple',
        icon: AppstoreOutlined
    },
    Networking: {
        color: 'volcano',
        icon: CoffeeOutlined
    },
    Expo: {
        color: 'green',
        icon: GlobalOutlined
    },
    Seminar: {
        color: 'orange',
        icon: BookOutlined
    },
    Bootcamp: {
        color: 'geekblue',
        icon: RocketOutlined
    },
    Forum: {
        color: 'cyan',
        icon: BulbOutlined
    },
    Hackathon: {
        color: 'magenta',
        icon: CodeOutlined
    },
    Summit: {
        color: 'gold',
        icon: FlagOutlined
    },
    Symposium: {
        color: 'lime',
        icon: TeamOutlined
    }
};

// 新增獲取顏色的通用函數
export const getTypeColor = (type) => {
    return eventTypes[type]?.color || 'gray';
};

export const getTypeIcon = (type) => {
    const IconComponent = eventTypes[type]?.icon;
    return IconComponent ? <IconComponent /> : null;
}; 