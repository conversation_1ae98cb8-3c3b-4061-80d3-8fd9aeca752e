import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Platform, TouchableOpacity, SafeAreaView, Alert, Image, ActivityIndicator, Modal } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useTranslation } from 'react-i18next';
import Ionicons from '@expo/vector-icons/Ionicons';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { appStyleStore } from 'stores/app_style_store';
import { Stack, useRouter, useFocusEffect, useLocalSearchParams } from 'expo-router';
import { VerificationTypeEnum, VerificationStatusEnum } from 'types/enums';
import { useFetchUserVerifications, useFetchUserVerificationsByUserId } from '@/api/user_services';
import { API_BASE_URL } from '@/api/api_config';
import axiosInstance from '@/api/axios_instance';
import Constants from 'expo-constants';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { createTheme } from 'theme/index';
import { useVolunteerPermissions } from 'hooks/useVolunteerPermissions';
import { CameraView, useCameraPermissions, BarcodeScanningResult } from 'expo-camera';
import { VerificationModal } from '@/common_modules/VerificationModal';

type IconName = keyof typeof Ionicons.glyphMap | keyof typeof MaterialCommunityIcons.glyphMap;

const getStatusInfo = (status: VerificationStatusEnum) => {
  switch (status) {
    case VerificationStatusEnum.Approved:
      return {
        icon: 'checkmark-circle-outline' as const,
        color: '#4CAF50',
        textKey: 'applications.status.verified'
      };
    case VerificationStatusEnum.Pending:
      return {
        icon: 'time-outline' as const,
        color: '#757575',
        textKey: 'applications.status.pending'
      };
    case VerificationStatusEnum.Rejected:
      return {
        icon: 'close-circle-outline' as const,
        color: '#FF3B30',
        textKey: 'applications.status.rejected'
      };
    case VerificationStatusEnum.Unverified:
      return {
        icon: 'alert-circle-outline' as const,
        color: '#FF9800',
        textKey: 'applications.status.unverified'
      };
    default:
      return {
        icon: 'alert-circle-outline' as const,
        color: '#FF3B30',
        textKey: 'applications.status.error'
      };
  }
};

const ListItem = ({
  theme,
  icon,
  iconType = 'ionicons',
  title,
  status,
  adminNotes,
  onPress,
  disabled = false,
  subtitleText,
}: {
  theme: ReturnType<typeof createTheme>;
  icon: IconName | number;
  iconType?: 'ionicons' | 'material' | 'image';
  title: string;
  status: VerificationStatusEnum;
  adminNotes?: string | null;
  onPress?: () => void;
  disabled?: boolean;
  subtitleText?: string;
}) => {
  const { t } = useTranslation();
  const statusInfo = getStatusInfo(status);
  
  const itemIsDisabled = disabled || 
                         status === VerificationStatusEnum.Approved || 
                         status === VerificationStatusEnum.Pending;

  const isHomeVisitItem = title === t('applications.types.homeVisit');

  return (
    <TouchableOpacity
      onPress={itemIsDisabled ? undefined : onPress}
      disabled={itemIsDisabled}
      style={styles.listItem}
      activeOpacity={itemIsDisabled ? 1 : 0.7}
    >
      <View style={[styles.iconWrapper, { backgroundColor: theme.colors.primaryContainer }]}>
        {iconType === 'image' ? (
          <Image 
            source={icon as number}
            style={{ width: 22, height: 22 }}
            resizeMode="contain"
          />
        ) : iconType === 'ionicons' ? (
          <Ionicons
            name={icon as keyof typeof Ionicons.glyphMap}
            size={22}
            color={theme.colors.primary}
          />
        ) : (
          <MaterialCommunityIcons
            name={icon as keyof typeof MaterialCommunityIcons.glyphMap}
            size={22}
            color={theme.colors.primary}
          />
        )}
      </View>
      <View style={styles.listItemContent}>
        <Text style={styles.listItemTitle}>{title}</Text>
        {subtitleText && (
          <Text style={styles.listItemSubtitle}>{subtitleText}</Text>
        )}
        <View style={styles.statusContainer}>
          <Ionicons
            name={statusInfo.icon}
            size={16}
            color={statusInfo.color}
            style={styles.statusIcon}
          />
          <Text style={[styles.statusText, { color: statusInfo.color }]}>
            {t(statusInfo.textKey)}
          </Text>
          {adminNotes && (
            <Text style={styles.adminNotes}>{adminNotes}</Text>
          )}
        </View>
      </View>
      {!itemIsDisabled && (
        <Ionicons
          name="chevron-forward"
          size={20}
          color="#CCC"
        />
      )}
    </TouchableOpacity>
  );
};

export default function ApplicationStatusScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  
  // Check if we're assisting someone else
  const assistingMemberId = params.assistingMemberId as string;
  const isVolunteerAssisting = params.isVolunteerAssisting === 'true';
  
  // Fetch verifications based on whether we're assisting or viewing own data
  const { data: myVerificationsData, isLoading: myLoading, refetch: refetchMyVerifications } = useFetchUserVerifications();
  const { data: assistedVerificationsData, isLoading: assistedLoading, refetch: refetchAssistedVerifications } = useFetchUserVerificationsByUserId(
    assistingMemberId,
    isVolunteerAssisting && !!assistingMemberId
  );
  
  // Use appropriate data based on assistance mode
  const verificationsData = isVolunteerAssisting ? assistedVerificationsData : myVerificationsData;
  const loading = isVolunteerAssisting ? assistedLoading : myLoading;
  const refetchVerifications = isVolunteerAssisting ? refetchAssistedVerifications : refetchMyVerifications;
  
  const [showHkidInfoDialog, setShowHkidInfoDialog] = useState(false);
  const { hasVolunteerPermissions } = useVolunteerPermissions();

  // Camera scanner states
  const [permission, requestPermission] = useCameraPermissions();
  const [scannerVisible, setScannerVisible] = useState(false);
  const [scanned, setScanned] = useState(false);
  
  // Verification modal states (using VerificationModal component)
  const [verificationState, setVerificationState] = useState({
    visible: false,
    isValid: false,
    memberId: '',
    memberName: '',
    isProcessing: false,
    errorMessage: '',
  });

  useFocusEffect(
    React.useCallback(() => {
      refetchVerifications();
    }, [refetchVerifications])
  );

  const getVerificationStatus = (type: VerificationTypeEnum): VerificationStatusEnum => {
    if (!verificationsData || !verificationsData) {
      return VerificationStatusEnum.Unverified;
    }
    const verifications = verificationsData || [];
    const verification = verifications.find(v => v.verification_type === type.toString());
    return verification?.status as VerificationStatusEnum || VerificationStatusEnum.Unverified;
  };

  const getAdminNotes = (type: VerificationTypeEnum): string | null | undefined => {
    if (!verificationsData || !verificationsData) {
      return null;
    }
    return verificationsData.find(v => v.verification_type === type.toString())?.admin_notes;
  };

  const hkidStatus = getVerificationStatus(VerificationTypeEnum.HkIDCard);
  const isHkidApproved = hkidStatus === VerificationStatusEnum.Approved;

  const handleVerificationPress = (type: VerificationTypeEnum) => {
    if (!verificationsData || !verificationsData) return;
    const verifications = verificationsData || [];
    const verification = verifications.find(v => v.verification_type === type.toString());
    if (type === VerificationTypeEnum.HomeVisit) {
      if (!isHkidApproved) {
        setShowHkidInfoDialog(true);
        return;
      }
    }

    if (type === VerificationTypeEnum.HomeVisit) {
      router.push('/user-profile/identification/HomeVisitApplicationScreen');
    } else if (verification?.status !== VerificationStatusEnum.Approved) {
      router.push({
        pathname: '/user-profile/identification/IdentityGuideScreen',
        params: { 
          documentType: type,
          ...(isVolunteerAssisting && { 
            assistingMemberId,
            isVolunteerAssisting: 'true'
          })
        }
      });
    }
  };

  const homeVisitSubtitle = !isHkidApproved ? t('applications.prerequisites.hkidRequired.short') : undefined;

  // Handle scan button press for volunteers to assist others
  const handleScanPress = async () => {
    try {
      if (!permission?.granted) {
        const newPermission = await requestPermission();
        if (!newPermission.granted) {
                  Alert.alert(
          t('camera.permissionDenied.title'),
          t('camera.permissionDenied.message'),
          [{ text: t('common.ok') }]
        );
          return;
        }
      }
      setScannerVisible(true);
      setScanned(false);
    } catch (error) {
      console.error('Camera permission error:', error);
              Alert.alert(
          t('common.error'),
          t('camera.permissionDenied.message'),
          [{ text: t('common.ok') }]
        );
    }
  };

    // Handle QR code scan result
  const handleBarCodeScanned = ({ data }: BarcodeScanningResult) => {
    if (scanned) return;

    setScanned(true);
    setScannerVisible(false);

    // Check if it's a valid member QR code
    if (data.startsWith('The_Moment_Member-')) {
      const memberId = data.replace('The_Moment_Member-', '');
      setVerificationState({
        visible: true,
        isValid: true,
        memberId,
        memberName: '', // Will be populated if needed
        isProcessing: false,
        errorMessage: '',
      });
    } else {
      setVerificationState({
        visible: true,
        isValid: false,
        memberId: '',
        memberName: '',
        isProcessing: false,
        errorMessage: t('qrcode.verification.invalidMessage'),
      });
    }
  };

  // Handle verification modal close
  const handleVerificationClose = () => {
    setVerificationState(prev => ({
      ...prev,
      visible: false,
    }));
  };

  // Handle proceed with application
  const handleProceedWithApplication = async () => {
    const { memberId } = verificationState;
    
    // Set loading state
    setVerificationState(prev => ({
      ...prev,
      isProcessing: true,
      errorMessage: '',
    }));
    
    try {
      // Try to fetch user verifications to validate access
      const response = await axiosInstance.request({
        url: `${API_BASE_URL}/admin/verifications`,
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        params: {
          user_id: memberId,
        },
      });
      
      // If successful, close modal and navigate
      setVerificationState(prev => ({
        ...prev,
        visible: false,
        isProcessing: false,
      }));
      
      // Navigate to identity status screen to view member's verification status
      router.push({
        pathname: '/user-profile/identification/IdentifyStatusScreen',
        params: { 
          assistingMemberId: memberId,
          isVolunteerAssisting: 'true'
        }
      });
      
    } catch (error) {
      // Handle errors in the modal
      console.error('[handleProceedWithApplication] Error fetching user verifications:', error);
      
      let errorMessage = t('error.http.default');
      
      // Check for specific error types
      const status = (error as any)?.response?.status;
      if (status === 401) {
        errorMessage = t('error.http.401');
      } else if (status === 403) {
        errorMessage = t('error.http.403');
      } else if (status === 404) {
        errorMessage = t('error.http.404');
      } else if (status >= 500) {
        errorMessage = t('error.http.500');
      }
      
      setVerificationState(prev => ({
        ...prev,
        isProcessing: false,
        errorMessage,
      }));
    }
  };

  if (loading && !verificationsData) {
    return (
      <View style={[styles.container, styles.centeredLoading]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <>
      <Stack.Screen options={{
        headerTitle: isVolunteerAssisting 
          ? t('volunteer.assistingApplications.title')
          : t('applications.status.title'),
        headerRight: hasVolunteerPermissions && !isVolunteerAssisting ? () => (
          <TouchableOpacity
            onPress={handleScanPress}
          >
            <View style={styles.scanButtonContainer}>
              <Text style={[styles.scanButtonText, { color: theme.colors.onSurfaceVariant }]}>
                {t('volunteer.assistOthers')}
              </Text>
              <MaterialCommunityIcons
                name="qrcode-scan"
                size={16}
                color={theme.colors.onSurfaceVariant}
              />
            </View>
          </TouchableOpacity>
        ) : undefined,
      }} />
      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.contentContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {isVolunteerAssisting 
                ? t('volunteer.assistingApplications.title')
                : t('applications.status.title')
              }
            </Text>
            <Text style={styles.subtitle}>
              {isVolunteerAssisting 
                ? t('volunteer.assistingApplications.subtitle', { memberId: assistingMemberId })
                : t('applications.status.subtitle')
              }
            </Text>
          </View>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('applications.status.documents')}</Text>
            
            <ListItem
              theme={theme}
              icon="card-account-details"
              iconType="material"
              title={t('applications.types.hkid')}
              status={getVerificationStatus(VerificationTypeEnum.HkIDCard)}
              adminNotes={getAdminNotes(VerificationTypeEnum.HkIDCard)}
              onPress={() => handleVerificationPress(VerificationTypeEnum.HkIDCard)}
              disabled={loading}
            />
            
            <ListItem
              theme={theme}
              icon="card-account-details-outline"
              iconType="material"
              title={t('applications.types.mainlandId')}
              status={getVerificationStatus(VerificationTypeEnum.MainlandChinaIDCard)}
              adminNotes={getAdminNotes(VerificationTypeEnum.MainlandChinaIDCard)}
              onPress={() => handleVerificationPress(VerificationTypeEnum.MainlandChinaIDCard)}
              disabled={loading}
            />
            
            <ListItem
              theme={theme}
              icon="card-bulleted"
              iconType="material"
              title={t('applications.types.mainlandTravelPermit')}
              status={getVerificationStatus(VerificationTypeEnum.MainlandTravelPermit)}
              adminNotes={getAdminNotes(VerificationTypeEnum.MainlandTravelPermit)}
              onPress={() => handleVerificationPress(VerificationTypeEnum.MainlandTravelPermit)}
              disabled={loading}
            />
            
            <ListItem
              theme={theme}
              icon="passport"
              iconType="material"
              title={t('applications.types.passport')}
              status={getVerificationStatus(VerificationTypeEnum.Passport)}
              adminNotes={getAdminNotes(VerificationTypeEnum.Passport)}
              onPress={() => handleVerificationPress(VerificationTypeEnum.Passport)}
              disabled={loading}
            />
            
            <ListItem
              theme={theme}
              icon={require('@/assets/verification_logo/hkyouth.jpg')}
              iconType="image"
              title={t('applications.types.hkYouth')}
              status={getVerificationStatus(VerificationTypeEnum.HkYouthPlus)}
              adminNotes={getAdminNotes(VerificationTypeEnum.HkYouthPlus)}
              onPress={() => handleVerificationPress(VerificationTypeEnum.HkYouthPlus)}
              disabled={loading}
            />
            
            <ListItem
              theme={theme}
              icon="home"
              iconType="ionicons"
              title={t('applications.types.address')}
              status={getVerificationStatus(VerificationTypeEnum.AddressProof)}
              adminNotes={getAdminNotes(VerificationTypeEnum.AddressProof)}
              onPress={() => handleVerificationPress(VerificationTypeEnum.AddressProof)}
              disabled={loading}
            />
            
            <ListItem
              theme={theme}
              icon="school"
              iconType="ionicons"
              title={t('applications.types.studentId')}
              status={getVerificationStatus(VerificationTypeEnum.StudentID)}
              adminNotes={getAdminNotes(VerificationTypeEnum.StudentID)}
              onPress={() => handleVerificationPress(VerificationTypeEnum.StudentID)}
              disabled={loading}
            />

            <ListItem
              theme={theme}
              icon="shield-home"
              iconType="material"
              title={t('applications.types.homeVisit')}
              status={getVerificationStatus(VerificationTypeEnum.HomeVisit)}
              adminNotes={getAdminNotes(VerificationTypeEnum.HomeVisit)}
              onPress={() => handleVerificationPress(VerificationTypeEnum.HomeVisit)}
              disabled={loading}
              subtitleText={homeVisitSubtitle}
            />
          </View>
        </ScrollView>
        <CustomDialog
          visible={showHkidInfoDialog}
          title={t('applications.prerequisites.hkidRequired.title')}
          message={t('applications.prerequisites.hkidRequired.message')}
          confirmText={t('applications.prerequisites.hkidRequired.verifyNow')}
          cancelText={t('common.cancel')}
          onConfirm={() => {
            setShowHkidInfoDialog(false);
            router.push({
              pathname: '/user-profile/identification/IdentityGuideScreen',
              params: { documentType: VerificationTypeEnum.HkIDCard }
            });
          }}
          onCancel={() => setShowHkidInfoDialog(false)}
          type="warning"
        />

        {/* Camera Scanner Modal */}
        <Modal
          animationType="fade"
          transparent={false}
          visible={scannerVisible}
          onRequestClose={() => setScannerVisible(false)}
        >
          <View style={styles.scannerContainer}>
            {!permission ? (
              <Text style={styles.cameraPermissionText}>
                {t('camera.requestingPermission')}
              </Text>
            ) : !permission.granted ? (
              <Text style={styles.cameraPermissionText}>
                {t('camera.noAccess')}
              </Text>
            ) : (
              <View style={styles.cameraContainer}>
                <CameraView
                  style={StyleSheet.absoluteFillObject}
                  onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
                  barcodeScannerSettings={{
                    barcodeTypes: ['qr'],
                  }}
                />
                <View style={styles.scannerOverlay}>
                  <View style={styles.scannerTarget} />
                  <Text style={styles.scannerInstructions}>
                    {t('volunteer.scanInstructions')}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setScannerVisible(false)}
                >
                  <MaterialCommunityIcons name="close" size={30} color="#FFF" />
                </TouchableOpacity>
              </View>
            )}
          </View>
        </Modal>

        {/* Verification Modal */}
        <VerificationModal
          visible={verificationState.visible}
          onClose={handleVerificationClose}
          isValid={verificationState.isValid && !verificationState.errorMessage}
                  title={(!verificationState.isValid || verificationState.errorMessage)
          ? (!verificationState.isValid ? t('qrcode.verification.invalidTitle') : t('common.error'))
          : t('volunteer.memberFound.title')
        }
          errorMessage={verificationState.errorMessage}
          memberInfo={verificationState.isValid && !verificationState.errorMessage ? {
            memberId: verificationState.memberId,
          } : undefined}
          customContent={verificationState.isValid && !verificationState.errorMessage ? (
            <Text style={{ fontSize: 16, color: '#333', textAlign: 'center', marginTop: 16 }}>
              {t('volunteer.memberFound.message')}
            </Text>
          ) : undefined}
          actions={(!verificationState.isValid || verificationState.errorMessage) ? [
            {
              text: t('common.ok'),
              onPress: handleVerificationClose,
              variant: 'primary'
            }
          ] : [
            {
              text: t('common.cancel'),
              onPress: handleVerificationClose,
              variant: 'secondary'
            },
            {
              text: t('volunteer.proceedWithApplication'),
              onPress: handleProceedWithApplication,
              variant: 'primary',
              loading: verificationState.isProcessing
            }
          ]}
        />
      </View>
    </>
  );
}

const STATUSBAR_HEIGHT = Platform.OS === 'android' ? Constants.statusBarHeight : 0;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingTop: STATUSBAR_HEIGHT,
  },
  scanButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  scanButtonText: {
    fontSize: 14,
    fontWeight: '500'
  },
  centeredLoading: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    paddingBottom: 16,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#212121',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#757575',
    lineHeight: 22,
  },
  infoSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  infoBox: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 16,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212121',
    marginBottom: 4,
  },
  infoText: {
    fontSize: 14,
    color: '#616161',
    lineHeight: 20,
  },
  section: {
    paddingTop: 12,
    paddingBottom: 4,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
    marginLeft: 16,
    marginBottom: 4,
    lineHeight: 20,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    minHeight: 64,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  listItemContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    lineHeight: 20,
    marginBottom: 4,
  },
  listItemSubtitle: {
    fontSize: 13,
    color: '#FF9800',
    lineHeight: 18,
    marginBottom: 4,
  },
  iconWrapper: {
    width: 36,
    height: 36,
    borderRadius: 10,
    backgroundColor: '#FFF5E6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 14,
    lineHeight: 18,
  },
  adminNotes: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },
  // Camera scanner styles
  scannerContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
  },
  cameraContainer: {
    flex: 1,
  },
  cameraPermissionText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
  },
  scannerOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  scannerTarget: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#FFF',
    borderRadius: 12,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  scannerInstructions: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
    paddingHorizontal: 20,
  },
  closeButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 30,
    left: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 10,
  },

});
