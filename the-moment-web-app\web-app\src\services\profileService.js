import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

// Helper to map JS-style camelCase params to API-style snake_case for query params
const mapToApiQueryParams = (params, mapping) => {
  const apiParams = {};
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      const mappedKey = mapping[key] || key;
      apiParams[mappedKey] = params[key];
    }
  }
  return apiParams;
};

const listUserVerificationsParamsMapping = {
  verificationType: 'verification_type',
  // status can be used directly
};

const getMyEventStatisticsParamsMapping = {
  startDate: 'start_date',
  endDate: 'end_date',
  // limit, offset can be used directly
};

// Helper function to decode JWT and extract role
const parseJWTRole = (token) => {
  try {
    if (!token) return null;
    
    // JWT has 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    // Decode the payload (second part)
    const payload = JSON.parse(atob(parts[1]));
    return payload.role || null;
  } catch (error) {
    console.error('Error parsing JWT role:', error);
    return null;
  }
};

// Helper function to fetch user profile and determine role
const fetchAndDetermineRole = async () => {
  try {
    // Use the more direct API_ENDPOINTS.USERS.ME to get the base user profile
    const userProfileResponse = await api.get(API_ENDPOINTS.USERS.ME);

    // Get role from JWT token instead of inferring it
    let role = 'user'; // Default role

    // Try to get the token from localStorage and parse the role
    const accessToken = localStorage.getItem('access_token');
    if (accessToken) {
      const jwtRole = parseJWTRole(accessToken);
      if (jwtRole) {
        // Map backend role values to frontend expected values if needed
        switch (jwtRole) {
          case 'superadmin':
      role = 'super_admin';
            break;
          case 'admin':
      role = 'admin';
            break;
          case 'user':
          default:
            role = 'user';
            break;
        }
      }
    }

    return { ...userProfileResponse, role };
  } catch (error) {
    console.error('Error fetching user profile or determining role:', error);
    // Propagate the error so the calling function can decide how to handle it
    throw error; 
  }
};

export const profileService = {
  // Fetch current user's profile with role
  getUserProfile: async () => {
    try {
      // Now directly calls fetchAndDetermineRole to get profile with role
      const userWithRole = await fetchAndDetermineRole();
      return userWithRole;
    } catch (error) {
      console.error('Error fetching user profile with role:', error);
      throw error;
    }
  },

  // Update current user's profile
  updateUserProfile: async (updateData) => {
    // updateData should match UpdateUserProfileRequest payload from api.md
    // e.g., { display_name: "...", notification_settings: { enable_app_notifications: true, ... }, ... }
    // Caller must ensure updateData keys are snake_case as per JSON tags in api.md, especially for nested objects.
    try {
      const response = await api.patch(API_ENDPOINTS.USERS.UPDATE_ME, updateData);
      return response;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  // Upload profile picture for current user
  uploadProfilePicture: async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file); // 'file' is the assumed field name for multipart/form-data
      const response = await api.post(API_ENDPOINTS.USERS.UPLOAD_PROFILE_PICTURE, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    }
  },

  // Fetch current user's statistics
  getUserStats: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.USERS.MY_STATS);
      console.log("API response", response);
      return response;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  },

  // Initiate phone number change for current user
  initiatePhoneChange: async (newPhoneNumber, phoneOtpChannel = null, clientId, state) => {
    try {
      const payload = {
        new_phone_number: newPhoneNumber,
        client_id: clientId,
        state: state,
      };
      if (phoneOtpChannel) {
        payload.phone_otp_channel = phoneOtpChannel;
      }
      const response = await api.post(API_ENDPOINTS.USERS.INITIATE_PHONE_CHANGE, payload);
      return response;
    } catch (error) {
      console.error('Error initiating phone change:', error);
      throw error;
    }
  },

  // Verify phone number change for current user
  verifyPhoneChange: async (state, otp, newPhoneNumber) => {
    try {
      const payload = { state, otp, new_phone_number: newPhoneNumber };
      const response = await api.post(API_ENDPOINTS.USERS.VERIFY_PHONE_CHANGE, payload);
      return response;
    } catch (error) {
      console.error('Error verifying phone change:', error);
      throw error;
    }
  },

  // Fetch organizations for the current user
  getUserOrganizations: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.USERS.MY_ORGANIZATIONS);
      return response;
    } catch (error) {
      console.error('Error fetching user organizations:', error);
      throw error;
    }
  },

  // Fetch current user's UUID
  getUserUUID: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.USERS.MY_UUID);
      return response;
    } catch (error) {
      console.error('Error fetching user UUID:', error);
      throw error;
    }
  },

  // Submit a verification document for current user
  submitUserVerification: async (verificationType, file, specifics = {}) => {
    // API: POST /users/me/verifications - uses multipart/form-data
    // `verificationType` corresponds to `form:"verification_type"`
    // `specifics` keys should match `form:"..."` tags in specific payloads (e.g., HKIDCardPayload)
    // The primary file is sent as 'document' and an optional secondary file as 'document_2'
    try {
      const formData = new FormData();
      formData.append('verification_type', verificationType);
      if (file) {
        formData.append('document', file); // Changed 'file' to 'document'
      }
      Object.keys(specifics).forEach(key => {
        // Ensure specifics keys are already in the format expected by the form (e.g., chinese_name)
        formData.append(key, specifics[key]);
      });

      const response = await api.post(API_ENDPOINTS.USERS.SUBMIT_VERIFICATION, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    } catch (error) {
      console.error('Error submitting user verification:', error);
      throw error;
    }
  },

  // Fetch user verifications for current user (optionally with params)
  listUserVerifications: async (clientParams = {}) => {
    // clientParams: { status?, verificationType? }
    // API expects query params: { status?, verification_type? }
    try {
      const apiParams = mapToApiQueryParams(clientParams, listUserVerificationsParamsMapping);
      const response = await api.get(API_ENDPOINTS.USERS.LIST_MY_VERIFICATIONS, { params: apiParams });
      return response;
    } catch (error) {
      console.error('Error fetching user verifications:', error);
      throw error;
    }
  },

  // Get a specific verification request detail for the current user
  getVerificationDetail: async (reqId) => {
    try {
      const response = await api.get(API_ENDPOINTS.USERS.GET_MY_VERIFICATION_DETAIL(reqId));
      return response;
    } catch (error) {
      console.error('Error fetching verification detail:', error);
      throw error;
    }
  },

  // Delete a specific verification request for the current user
  deleteVerification: async (reqId) => {
    try {
      const response = await api.delete(API_ENDPOINTS.USERS.DELETE_MY_VERIFICATION(reqId));
      return response;
    } catch (error) {
      console.error('Error deleting verification:', error);
      throw error;
    }
  },

  // Get a specific verification document for the current user
  getVerificationDocument: async (docId) => {
    try {
      // This endpoint returns a file. The API client should handle it appropriately (e.g., as a blob).
      const response = await api.get(API_ENDPOINTS.USERS.GET_MY_VERIFICATION_DOCUMENT(docId), {
        responseType: 'blob', // Important for file downloads
      });
      return response;
    } catch (error) {
      console.error('Error fetching verification document:', error);
      throw error;
    }
  },

  // List volunteer applications for the current user
  listMyVolunteerApplications: async (pageRequest = {}) => {
    // pageRequest: { limit?, offset? } - API expects these directly as query params
    try {
      const response = await api.get(API_ENDPOINTS.USERS.LIST_MY_VOLUNTEER_APPLICATIONS, { params: pageRequest });
      return response;
    } catch (error) {
      console.error('Error listing my volunteer applications:', error);
      throw error;
    }
  },

  // Get details of a specific volunteer application for the current user
  getMyVolunteerApplicationDetail: async (appId) => {
    try {
      const response = await api.get(API_ENDPOINTS.USERS.GET_MY_VOLUNTEER_APPLICATION_DETAIL(appId));
      return response;
    } catch (error) {
      console.error('Error fetching my volunteer application detail:', error);
      throw error;
    }
  },

  // List volunteer qualifications for the current user
  listMyVolunteerQualifications: async (pageRequest = {}) => {
    // pageRequest: { limit?, offset? } - API expects these directly as query params
    try {
      const response = await api.get(API_ENDPOINTS.USERS.LIST_MY_VOLUNTEER_QUALIFICATIONS, { params: pageRequest });
      return response;
    } catch (error) {
      console.error('Error listing my volunteer qualifications:', error);
      throw error;
    }
  },
  
  // Get event statistics for the current user
  getMyEventStatistics: async (clientParams = {}) => {
    // clientParams: { startDate?, endDate?, limit?, offset? }
    // API expects query params: { start_date?, end_date?, limit?, offset? }
    try {
      const apiParams = mapToApiQueryParams(clientParams, getMyEventStatisticsParamsMapping);
      const response = await api.get(API_ENDPOINTS.USERS.MY_EVENT_STATISTICS, { params: apiParams });
      return response;
    } catch (error) {
      console.error('Error fetching my event statistics:', error);
      throw error;
    }
  }
}; 