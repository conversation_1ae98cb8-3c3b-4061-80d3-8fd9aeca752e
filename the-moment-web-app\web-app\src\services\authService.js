import api from './api';
import { API_ENDPOINTS } from './apiEndpoints'; // Ensure this path is correct
import { profileService } from './profileService'; // Import profileService

// Token management utilities
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token'
};

// Token storage utilities
const tokenStorage = {
  setTokens: (accessToken, refreshToken) => {
    localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, accessToken);
    localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, refreshToken);
    // Immediately update the API instance header after setting tokens
    if (api.instance && api.instance.defaults) {
      api.instance.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
    } else if (api.defaults) { // Fallback for older structure
      api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
    }
  },
  
  clearTokens: () => {
    localStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN);
    // Immediately clear the API instance header after clearing tokens
    if (api.instance && api.instance.defaults) {
      delete api.instance.defaults.headers.common['Authorization'];
    } else if (api.defaults) { // Fallback for older structure
      delete api.defaults.headers.common['Authorization'];
    }
  },
  
  getAccessToken: () => localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN),
  getRefreshToken: () => localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN)
};

// ALL_ORGANIZATION_ID and fetchAndDetermineRole are removed from here

export const authService = {
  // Add these for direct access, consistent with how AuthContext wants to use them
  getAccessToken: tokenStorage.getAccessToken,
  getRefreshToken: tokenStorage.getRefreshToken,
  clearTokens: tokenStorage.clearTokens,

  // Check if phone number exists
  checkPhone: async (phone) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.PHONE_CHECK, { phone });
      // Assuming response.data directly contains { exists: boolean, user?: object }
      return response;
    } catch (error) {
      // Rethrow or handle specific errors as needed
      console.error('Error in checkPhone:', error);
      throw error;
    }
  },

  // Initiate phone OTP for login
  initiatePhoneOTP: async (phone, clientId, codeChallenge, codeChallengeMethod, state, phoneOtpChannel = null) => {
    try {
      const payload = {
        phone,
        client_id: clientId,
        code_challenge: codeChallenge,
        code_challenge_method: codeChallengeMethod,
        state,
      };
      if (phoneOtpChannel) {
        payload.phone_otp_channel = phoneOtpChannel;
      }
      const response = await api.post(API_ENDPOINTS.AUTH.PHONE_OTP_INITIATE, payload);
      // Assuming response.data contains { state: string, message?: string }
      return response;
    } catch (error) {
      console.error('Error in initiatePhoneOTP:', error);
      throw error;
    }
  },

  // Verify phone OTP for login
  verifyPhoneOTP: async (state, otp, codeVerifier) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.PHONE_OTP_VERIFY, {
        state,
        otp,
        code_verifier: codeVerifier,
      });
      // Assuming response.data contains { message: string, user_id: uuid, access_token: string, refresh_token: string }
      const { access_token, refresh_token, user_id, message } = response; // Adjusted to match expected response structure from definition
      if (access_token && refresh_token) {
        tokenStorage.setTokens(access_token, refresh_token);
      }
      // Fetch user profile with role using profileService
      const userWithRole = await profileService.getUserProfile();
      return { userId: user_id, user: userWithRole, message, accessToken: access_token, refreshToken: refresh_token };
    } catch (error) {
      console.error('Error in verifyPhoneOTP:', error);
      // Handle specific error cases
      if (error.response) {
        switch (error.response.status) {
          case 401: // Unauthorized
            throw new Error('Invalid OTP or verification parameters.');
          // Add other specific error codes as per your API definition or backend behavior
          default:
            throw new Error('OTP verification failed.');
        }
      }
      throw error;
    }
  },

  // Initiate phone registration OTP
  initiatePhoneRegistration: async (phone, clientId, codeChallenge, codeChallengeMethod, state, phoneOtpChannel = null) => {
    try {
      const payload = {
        phone,
        client_id: clientId,
        code_challenge: codeChallenge,
        code_challenge_method: codeChallengeMethod,
        state,
      };
      if (phoneOtpChannel) {
        payload.phone_otp_channel = phoneOtpChannel;
      }
      const response = await api.post(API_ENDPOINTS.AUTH.REGISTER_PHONE_INITIATE, payload);
      // Assuming response.data contains { state: string, message?: string, flow_id?: string }
      return response;
    } catch (error) {
      console.error('Error in initiatePhoneRegistration:', error);
      throw error;
    }
  },

  // Verify phone registration OTP
  verifyPhoneRegistration: async (state, otp, codeVerifier, displayName, interfaceLanguage = null, communicationLanguage = null, phoneOtpChannel = null) => {
    try {
      const payload = {
        state,
        otp,
        code_verifier: codeVerifier,
        display_name: displayName,
      };
      if (interfaceLanguage) payload.interface_language = interfaceLanguage;
      if (communicationLanguage) payload.communication_language = communicationLanguage;
      if (phoneOtpChannel) payload.phone_otp_channel = phoneOtpChannel;

      const response = await api.post(API_ENDPOINTS.AUTH.REGISTER_PHONE_VERIFY, payload);
      const { access_token, refresh_token, message } = response;
      if (access_token && refresh_token) {
        tokenStorage.setTokens(access_token, refresh_token);
      }
      // Fetch user profile with role using profileService
      const userWithRole = await profileService.getUserProfile();
      return { user: userWithRole, message, accessToken: access_token, refreshToken: refresh_token };
    } catch (error) {
      console.error('Error in verifyPhoneRegistration:', error);
      if (error.response) {
        switch (error.response.status) {
          case 400: // Bad Request
            throw new Error(error.response.data?.message || 'Invalid registration data.');
          case 409: // Conflict
            throw new Error(error.response.data?.message || 'User already exists or flow invalid.');
          default:
            throw new Error('Registration verification failed.');
        }
      }
      throw error;
    }
  },

  // Check staff email
  checkStaffEmail: async (email) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.STAFF_EMAIL_CHECK, { email });
      // Assuming response.data: { exists: bool, role: string, user_hint?: string }
      return response;
    } catch (error) {
      console.error('Error in checkStaffEmail:', error);
      throw error;
    }
  },

  // Initiate staff login
  initiateStaffLogin: async (email, clientId, codeChallenge, codeChallengeMethod, state) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.STAFF_LOGIN_INITIATE, {
        email,
        client_id: clientId,
        code_challenge: codeChallenge,
        code_challenge_method: codeChallengeMethod,
        state,
      });
      // Assuming response.data: { state: string, flow_id?: string }
      return response;
    } catch (error) {
      console.error('Error in initiateStaffLogin:', error);
      throw error;
    }
  },

  // Verify staff login
  verifyStaffLogin: async (state, email, password, codeVerifier) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.STAFF_LOGIN_VERIFY, {
        state,
        email,
        password,
        code_verifier: codeVerifier,
      });
      const { access_token, refresh_token, message } = response;
      if (access_token && refresh_token) {
        tokenStorage.setTokens(access_token, refresh_token);
      }
      // Fetch user profile with role using profileService
      const userWithRole = await profileService.getUserProfile();
      return { user: userWithRole, message, accessToken: access_token, refreshToken: refresh_token };
    } catch (error) {
      console.log('Error in verifyStaffLogin:', error);
      if (error.response && error.response.status === 401) {
        throw new Error('Invalid staff password.');
      }
      throw error;
    }
  },

  // Refresh token
  refreshToken: async () => {
    try {
      const currentRefreshToken = tokenStorage.getRefreshToken();
      if (!currentRefreshToken) {
        // No refresh token, effectively logged out or session expired.
        authService.logout(); // Ensure clean state
        throw new Error('No refresh token available. Please login again.');
      }

      const response = await api.post(API_ENDPOINTS.AUTH.TOKEN_REFRESH, {
        refresh_token: currentRefreshToken
      });

      // Assuming response.data: { access_token: string, refresh_token: string, token_type: string }
      const { access_token, refresh_token } = response;
      tokenStorage.setTokens(access_token, refresh_token);
      
      // Fetch updated user profile with role using profileService
      const userWithRole = await profileService.getUserProfile();
      
      return {
        accessToken: access_token,
        refreshToken: refresh_token,
        user: userWithRole 
      };
    } catch (error) {
      console.error('Error refreshing token:', error);
      // If refresh fails, log out the user as the session is truly expired or invalid.
      authService.logout(); // This will clear tokens and any auth headers.
      throw new Error('Session expired. Please login again.');
    }
  },

  // Logout
  logout: async () => {
    const currentRefreshToken = tokenStorage.getRefreshToken();
    if (currentRefreshToken) {
      try {
        await api.post(API_ENDPOINTS.AUTH.LOGOUT, { refresh_token: currentRefreshToken });
      } catch (error) {
        // Log error but proceed with clearing tokens locally regardless of server response
        console.error('Logout API call failed:', error);
      }
    }
    tokenStorage.clearTokens();
    // Clearing api.defaults.headers.common['Authorization'] should be handled by the interceptor logic in api.js
    // or by ensuring that subsequent requests fail and trigger a login flow.
    // If api.js's RealApi instance is recreated or its interceptor handles missing tokens, this is fine.
  },

  // Get stored tokens
  getStoredTokens: () => ({
    accessToken: tokenStorage.getAccessToken(),
    refreshToken: tokenStorage.getRefreshToken()
  }),

  // Check if user is authenticated
  isAuthenticated: () => {
    const accessToken = tokenStorage.getAccessToken();
    return !!accessToken; // Basic check, could be enhanced with token expiration check
  },

  // Initialize auth state (e.g., on app load)
  initializeAuth: () => {
    // The request interceptor in api.js handles adding the token from localStorage
    // to requests, so direct manipulation of api.defaults here might be redundant
    // or conflict, depending on how api.js is structured.
    // If RealApi always reads from localStorage via its interceptor, this function
    // might only be needed if there's other auth-related setup.
    const accessToken = tokenStorage.getAccessToken();
    if (accessToken) {
      // This line might be unnecessary if the interceptor in api.js correctly sets
      // the Authorization header for every request using RealApi.
      // If api.js creates a single instance of RealApi, this could be okay.
      // Check api.js implementation for RealApi.
      // For MockApi, headers are handled differently, often on a per-call basis or not at all.
      // Ensuring header is set on init if token exists:
      if (api.instance && api.instance.defaults) { 
         api.instance.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
      } else if (api.defaults) { 
         api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
      }
    }
  }
}; 