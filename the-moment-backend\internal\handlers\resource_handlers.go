package handlers

import (
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services"
	utils "Membership-SAAS-System-Backend/internal/utils"
	"database/sql"
	"fmt"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// Placeholder for appBaseURL removed - should be accessed via config/context

const DefaultResourcesLimit = 10
const MaxResourcesLimit = 100
const DefaultOrgFilesLimit = 50
const MaxOrgFilesLimit = 200

type ResourceHandler struct {
	Service   *services.ResourceService
	Validator *utils.RequestValidator
	BaseURL   string
}

func NewResourceHandler(service *services.ResourceService, validator *utils.RequestValidator, baseURL string) *ResourceHandler {
	return &ResourceHandler{
		Service:   service,
		Validator: validator,
		BaseURL:   baseURL,
	}
}

// --- Resource Endpoints (like posts but for downloadable file collections) ---

// CreateResource godoc
// @Summary Create a new resource entry
// @Description Creates a new resource, which is a collection of downloadable files. Requires admin/staff privileges for the organization.
// @Tags Content Management (Resources - Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID) to associate the resource with"
// @Param body body payloads.CreateResourceRequest true "Details of the resource to create (title, description, visibility, status, etc.)"
// @Success 201 {object} payloads.ResourceResponse "Newly created resource with its files (if any were included in request, though typically files are added separately)"
// @Failure 400 {object} utils.ErrorResponse "Invalid request body or organization ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 500 {object} utils.ErrorResponse "Failed to create resource or retrieve details after creation"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/resources [post]
func (h *ResourceHandler) CreateResource(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	var req payloads.CreateResourceRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body", err)
	}
	req.OrganizationID = orgID

	if err := h.Validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	dbResource, err := h.Service.CreateResource(c.Request().Context(), req)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to create resource")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to create resource", err)
	}

	createdResourceResp, err := h.Service.GetResourceByID(c.Request().Context(), dbResource.ID)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to fetch newly created resource for response")
		return utils.HandleError(c, http.StatusInternalServerError, "Resource created but failed to retrieve full details", err)
	}

	// Prepend base URL to file paths
	if createdResourceResp != nil {
		for i := range createdResourceResp.Files {
			if !strings.HasPrefix(createdResourceResp.Files[i].FilePath, "http://") && !strings.HasPrefix(createdResourceResp.Files[i].FilePath, "https://") {
				createdResourceResp.Files[i].FilePath = h.BaseURL + "/" + strings.TrimPrefix(createdResourceResp.Files[i].FilePath, "./")
			}
		}
	}

	return c.JSON(http.StatusCreated, createdResourceResp)
}

// GetOrgResource godoc
// @Summary Get details of a specific resource (admin view)
// @Description Retrieves full details of a specific resource, including its associated files. Does not filter by status.
// @Tags Content Management (Resources - Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param resourceId path string true "Resource ID (UUID)" Format(uuid)
// @Success 200 {object} payloads.ResourceResponse "Resource details with associated files"
// @Failure 400 {object} utils.ErrorResponse "Invalid resource ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Resource not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve resource"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/resources/{resourceId} [get]
func (h *ResourceHandler) GetOrgResource(c echo.Context) error {
	resourceIDStr := c.Param("resourceId")
	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid resource ID format", err)
	}

	resourceResp, err := h.Service.GetResourceByID(c.Request().Context(), resourceID)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Resource not found", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to get resource by ID")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve resource", err)
	}

	// Prepend base URL to file paths
	if resourceResp != nil {
		for i := range resourceResp.Files {
			if !strings.HasPrefix(resourceResp.Files[i].FilePath, "http://") && !strings.HasPrefix(resourceResp.Files[i].FilePath, "https://") {
				resourceResp.Files[i].FilePath = h.BaseURL + "/" + strings.TrimPrefix(resourceResp.Files[i].FilePath, "./")
			}
		}
	}

	return c.JSON(http.StatusOK, resourceResp)
}

// UpdateResource godoc
// @Summary Update a resource entry
// @Description Updates an existing resource's details (title, description, visibility, status, etc.). Requires admin/staff privileges.
// @Tags Content Management (Resources - Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid) // Though not directly used by handler, it is part of the route for consistency
// @Param resourceId path string true "Resource ID (UUID) of the resource to update"
// @Param body body payloads.UpdateResourceRequest true "Resource details to update"
// @Success 200 {object} payloads.ResourceResponse "Updated resource details with files"
// @Failure 400 {object} utils.ErrorResponse "Invalid request body or resource ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Resource not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to update resource or retrieve details after update"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/resources/{resourceId} [put]
func (h *ResourceHandler) UpdateResource(c echo.Context) error {
	// Check if user is authenticated, as this is a modifying operation
	if c.Get("user") == nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Authentication required to update resource", nil)
	}

	resourceIDStr := c.Param("resourceId")
	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid resource ID format", err)
	}

	var req payloads.UpdateResourceRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body", err)
	}
	if err := h.Validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	_, err = h.Service.UpdateResource(c.Request().Context(), resourceID, req)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Resource not found for update", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to update resource")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to update resource", err)
	}

	updatedResourceResp, err := h.Service.GetResourceByID(c.Request().Context(), resourceID)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to fetch updated resource for response")
		return utils.HandleError(c, http.StatusInternalServerError, "Resource updated but failed to retrieve full details", err)
	}

	// Prepend base URL to file paths
	if updatedResourceResp != nil {
		for i := range updatedResourceResp.Files {
			if !strings.HasPrefix(updatedResourceResp.Files[i].FilePath, "http://") && !strings.HasPrefix(updatedResourceResp.Files[i].FilePath, "https://") {
				updatedResourceResp.Files[i].FilePath = h.BaseURL + "/" + strings.TrimPrefix(updatedResourceResp.Files[i].FilePath, "./")
			}
		}
	}

	return c.JSON(http.StatusOK, updatedResourceResp)
}

// DeleteResource godoc
// @Summary Delete a resource entry
// @Description Deletes a resource and its associated files. Requires admin/staff privileges.
// @Tags Content Management (Resources - Admin)
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param resourceId path string true "Resource ID (UUID) of the resource to delete"
// @Success 204 "No Content"
// @Failure 400 {object} utils.ErrorResponse "Invalid resource ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Resource not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to delete resource"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/resources/{resourceId} [delete]
func (h *ResourceHandler) DeleteResource(c echo.Context) error {
	// Check if user is authenticated, as this is a modifying operation
	if c.Get("user") == nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Authentication required to delete resource", nil)
	}

	resourceIDStr := c.Param("resourceId")
	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid resource ID format", err)
	}

	err = h.Service.DeleteResource(c.Request().Context(), resourceID)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Resource not found for deletion", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to delete resource")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to delete resource", err)
	}
	return c.NoContent(http.StatusNoContent)
}

// ListOrgResources godoc
// @Summary List resources for an organization (admin view, includes drafts)
// @Description Retrieves a list of resources belonging to a specific organization, including drafts and hidden ones. Supports pagination and status filtering.
// @Tags Content Management (Resources - Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param limit query int false "Limit number of results per page" default(10) minimum(1) maximum(100)
// @Param offset query int false "Offset for pagination" default(0) minimum(0)
// @Param status query string false "Filter by resource status (e.g., 'draft', 'published')" Enums(draft, published)
// @Param search_term query string false "Search term for resource title or description" maxlength(255)
// @Success 200 {array} payloads.ResourceResponse "List of resources belonging to the organization"
// @Header 200 {string} X-Total-Count "Total number of resources matching the query"
// @Failure 400 {object} utils.ErrorResponse "Invalid organization ID format or query parameters"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve resources"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/resources [get]
func (h *ResourceHandler) ListOrgResources(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	var listParams payloads.ListResourcesParams
	if err := c.Bind(&listParams); err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("ListOrgResources: Failed to bind query params, proceeding with defaults/manual validation.")
		// Even if bind fails, we might have partial data or want to apply defaults below.
		// So, we don't return immediately, but validator will catch issues.
	}

	// Set defaults if not provided or if outside valid range (also handles potential bind issues for these specific fields)
	if listParams.Limit <= 0 || listParams.Limit > MaxResourcesLimit {
		listParams.Limit = DefaultResourcesLimit
	}
	if listParams.Offset < 0 {
		listParams.Offset = 0
	}

	// Validate the parameters, including any bound values and defaults
	if err := h.Validator.ValidateStruct(listParams); err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Interface("params_after_bind_and_defaults", listParams).Msg("ListOrgResources: Validation failed for query parameters")
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed for query parameters", err)
	}

	resources, totalCount, err := h.Service.ListResourcesByOrganization(c.Request().Context(), orgID, listParams)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to list organization resources")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve resources for organization", err)
	}

	// Prepend base URL to file paths
	for idx := range resources {
		for i := range resources[idx].Files {
			if !strings.HasPrefix(resources[idx].Files[i].FilePath, "http://") && !strings.HasPrefix(resources[idx].Files[i].FilePath, "https://") {
				resources[idx].Files[i].FilePath = h.BaseURL + "/" + strings.TrimPrefix(resources[idx].Files[i].FilePath, "./")
			}
		}
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, resources)
}

// ListPublicOrgResources godoc
func (h *ResourceHandler) ListPublicOrgResources(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	limit := utils.ParseQueryInt(c, "limit", DefaultResourcesLimit)
	offset := utils.ParseQueryInt(c, "offset", 0)
	if limit <= 0 || limit > MaxResourcesLimit {
		limit = DefaultResourcesLimit
	}

	listParams := payloads.ListResourcesParams{
		Limit:  limit,
		Offset: offset,
		// Visibility filter could be added here if needed, but service method implies 'published'
	}

	resources, totalCount, err := h.Service.ListPublishedResourcesByOrganization(c.Request().Context(), orgID, listParams)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgID", orgIDStr).Msg("Failed to list published organization resources")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve resources for organization", err)
	}

	// Prepend base URL to file paths
	for idx := range resources {
		for i := range resources[idx].Files {
			if !strings.HasPrefix(resources[idx].Files[i].FilePath, "http://") && !strings.HasPrefix(resources[idx].Files[i].FilePath, "https://") {
				resources[idx].Files[i].FilePath = h.BaseURL + "/" + strings.TrimPrefix(resources[idx].Files[i].FilePath, "./")
			}
		}
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, resources)
}

// --- Public Resource Endpoints ---

// ListPublicResources godoc
// @Summary List all published resources
// @Description Retrieves a list of all publicly visible resources across all organizations. Supports pagination and filtering by organization ID, visibility, and search term.
// @Tags Content Management (Resources - Public)
// @Produce json
// @Param org_id query string false "Filter by Organization ID (UUID)"
// @Param org_id2 query string false "Filter by a second Organization ID (UUID)"
// @Param visibility query string false "Filter by visibility (e.g., public, org_only)" Enums(public, org_only)
// @Param limit query int false "Limit number of results per page" default(10) minimum(1) maximum(100)
// @Param offset query int false "Offset for pagination" default(0) minimum(0)
// @Param search_term query string false "Search term for resource title or description" maxlength(255)
// @Success 200 {array} payloads.ResourceResponse "List of published resources"
// @Header 200 {string} X-Total-Count "Total number of published resources matching the query"
// @Failure 400 {object} utils.ErrorResponse "Invalid query parameter format or validation error"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve published resources"
// @Router /resources [get]
func (h *ResourceHandler) ListPublicResources(c echo.Context) error {
	ctx := c.Request().Context()
	var listParams payloads.ListResourcesParams

	// Bind query parameters to listParams
	if err := c.Bind(&listParams); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("ListPublicResources: Failed to bind query params, proceeding with defaults/manual validation.")
		// Proceed to apply defaults and validate
	}

	// Apply defaults and constraints for Limit and Offset
	if listParams.Limit <= 0 || listParams.Limit > MaxResourcesLimit {
		listParams.Limit = DefaultResourcesLimit
	}
	if listParams.Offset < 0 {
		listParams.Offset = 0
	}

	// Validate all parameters in listParams after binding and applying defaults
	if err := h.Validator.ValidateStruct(listParams); err != nil {
		log.Ctx(ctx).Error().Err(err).Interface("params_after_bind_and_defaults", listParams).Msg("ListPublicResources: Validation failed for query parameters")
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed for query parameters", err)
	}

	// Note: OrganizationID, Visibility, and SearchTerm are already pointers in ListResourcesParams.
	// c.Bind will populate them if present in the query. If not, they remain nil.
	// The `validate` tags on ListResourcesParams (e.g., for `visibility`, `search_term`) will be checked by h.Validator.ValidateStruct.

	resources, totalCount, err := h.Service.ListPublishedResources(ctx, listParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list published resources")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve published resources", err)
	}

	// Prepend base URL to file paths
	for idx := range resources {
		for i := range resources[idx].Files {
			if !strings.HasPrefix(resources[idx].Files[i].FilePath, "http://") && !strings.HasPrefix(resources[idx].Files[i].FilePath, "https://") {
				resources[idx].Files[i].FilePath = h.BaseURL + "/" + strings.TrimPrefix(resources[idx].Files[i].FilePath, "./")
			}
		}
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, resources)
}

// GetPublicResourceBySlug godoc
// @Summary Get public details of a specific published resource by ID or slug
// @Description Retrieves the public details of a specific published resource, identified either by its UUID or its slug within the context of its organization.
// @Tags Content Management (Resources - Public)
// @Produce json
// @Param orgId path string true "Organization ID (UUID) (needed for slug lookup context)" Format(uuid)
// @Param resourceIdOrSlug path string true "Resource ID (UUID) or Slug"
// @Success 200 {object} payloads.ResourceResponse "Public resource details with associated files"
// @Failure 400 {object} utils.ErrorResponse "Invalid organization ID format"
// @Failure 404 {object} utils.ErrorResponse "Resource not found, not published, or not publicly visible in this organization context"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve resource"
// @Router /organizations/{orgId}/resources/{resourceIdOrSlug}/public [get]
func (h *ResourceHandler) GetPublicResourceBySlug(c echo.Context) error {
	orgIDStr := c.Param("orgId") // Assuming slug is unique per org, so orgId is needed
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format for resource slug lookup", err)
	}

	resourceIDOrSlug := c.Param("resourceIdOrSlug")
	var resourceResp *payloads.ResourceResponse

	// Try parsing as UUID first
	resourceUUID, parseErr := uuid.Parse(resourceIDOrSlug)
	if parseErr == nil {
		// It's a UUID, get by ID and ensure it's public and matches org (if needed)
		tempResp, getErr := h.Service.GetResourceByID(c.Request().Context(), resourceUUID)
		if getErr != nil {
			if getErr == utils.ErrNotFound {
				return utils.HandleError(c, http.StatusNotFound, "Resource not found by ID", getErr)
			}
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve resource by ID", getErr)
		}
		// Check if it's published and visible
		if tempResp.Status != "published" || (tempResp.Visibility != "public" && tempResp.OrganizationID != orgID) { // Simple check
			return utils.HandleError(c, http.StatusNotFound, "Resource not found, not published, or not publicly visible for this organization context", nil)
		}
		if tempResp.OrganizationID != orgID {
			// If slugs are globally unique, this check might be different or not needed here.
			// But if slug context is per org, this is important.
			return utils.HandleError(c, http.StatusNotFound, "Resource organization mismatch for public view by ID", nil)
		}
		resourceResp = tempResp
	} else {
		// Not a UUID, assume it's a slug for the given orgId
		resourceResp, err = h.Service.GetResourceByOrgAndSlug(c.Request().Context(), orgID, resourceIDOrSlug, true)
		if err != nil {
			if err == utils.ErrNotFound {
				return utils.HandleError(c, http.StatusNotFound, "Resource not found by slug for this organization, or not public/published", err)
			}
			log.Ctx(c.Request().Context()).Error().Err(err).Str("orgId", orgIDStr).Str("slug", resourceIDOrSlug).Msg("Failed to get resource by org and slug")
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve resource by slug", err)
		}
	}

	// Prepend base URL to file paths
	if resourceResp != nil {
		for i := range resourceResp.Files {
			if !strings.HasPrefix(resourceResp.Files[i].FilePath, "http://") && !strings.HasPrefix(resourceResp.Files[i].FilePath, "https://") {
				resourceResp.Files[i].FilePath = h.BaseURL + "/" + strings.TrimPrefix(resourceResp.Files[i].FilePath, "./")
			}
		}
	}

	return c.JSON(http.StatusOK, resourceResp)
}

// --- Resource File Endpoints ---

// UploadResourceFile godoc
// @Summary Upload a file to a resource
// @Description Uploads a file and associates it with a specific resource. Requires admin/staff privileges.
// @Tags Content Management (Resources - Admin)
// @Accept multipart/form-data
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param resourceId path string true "Resource ID (UUID) to attach the file to"
// @Param file formData file true "The file to upload"
// @Param description formData string false "Optional description for the file"
// @Success 201 {object} payloads.ResourceFileResponse "Details of the uploaded file"
// @Failure 400 {object} utils.ErrorResponse "Invalid input (e.g., missing file, resource ID format, file too large)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Resource not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to upload file or save its details"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/resources/{resourceId}/files [post]
func (h *ResourceHandler) UploadResourceFile(c echo.Context) error {
	resourceIDStr := c.Param("resourceId")
	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid resource ID format", err)
	}

	// Retrieve the file from the form data
	fileHeader, err := c.FormFile("file")
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "File upload failed: missing or invalid file field", err)
	}

	// Retrieve the optional description from form data
	description := c.FormValue("description")

	dbFile, err := h.Service.UploadResourceFile(c.Request().Context(), resourceID, fileHeader, description)
	if err != nil {
		if err == utils.ErrNotFound { // Service might return ErrNotFound if resourceID is invalid
			return utils.HandleError(c, http.StatusNotFound, "Resource not found to attach file", err)
		} else if strings.Contains(err.Error(), "exceeds maximum allowed size") {
			return utils.HandleError(c, http.StatusBadRequest, "File is too large", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to upload resource file")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to process file upload", err)
	}

	fileResp := payloads.ToResourceFileResponse(dbFile)

	// Prepend base URL to file path
	if !strings.HasPrefix(fileResp.FilePath, "http://") && !strings.HasPrefix(fileResp.FilePath, "https://") {
		fileResp.FilePath = h.BaseURL + "/" + strings.TrimPrefix(fileResp.FilePath, "./")
	}

	return c.JSON(http.StatusCreated, fileResp)
}

// DeleteResourceFile godoc
// @Summary Delete a file from a resource
// @Description Deletes a specific file associated with a resource. Requires admin/staff privileges.
// @Tags Content Management (Resources - Admin)
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param resourceId path string true "Resource ID (UUID)" Format(uuid)
// @Param fileId path string true "File ID (UUID) of the file to delete"
// @Success 204 "No Content"
// @Failure 400 {object} utils.ErrorResponse "Invalid file ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "File not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to delete resource file"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/resources/{resourceId}/files/{fileId} [delete]
func (h *ResourceHandler) DeleteResourceFile(c echo.Context) error {
	fileIDStr := c.Param("fileId")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid file ID format", err)
	}

	err = h.Service.DeleteResourceFile(c.Request().Context(), fileID)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "File not found for deletion", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to delete resource file")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to delete resource file", err)
	}
	return c.NoContent(http.StatusNoContent)
}

// DownloadResourceFile godoc
// @Summary Download a specific file from a resource (public)
// @Description Downloads a specific file associated with a public resource. Access checks are performed based on resource visibility before serving the file.
// @Tags Content Management (Resources - Public)
// @Produce octet-stream
// @Param orgId path string true "Organization ID (UUID) of the resource's organization"
// @Param resourceIdOrSlug path string true "Resource ID (UUID) or Slug"
// @Param fileIdOrName path string true "File ID (UUID) or exact File Name within the resource"
// @Success 200 {file} binary "The requested resource file"
// @Failure 400 {object} utils.ErrorResponse "Invalid ID format in path"
// @Failure 404 {object} utils.ErrorResponse "Resource or File not found, or not publicly accessible"
// @Failure 500 {object} utils.ErrorResponse "Error preparing file for download"
// @Router /resources/download/{orgId}/{resourceIdOrSlug}/{fileIdOrName} [get]
func (h *ResourceHandler) DownloadResourceFile(c echo.Context) error {
	// Developer notes on path definition:
	// The path used in @Router is /resources/download/{orgId}/{resourceIdOrSlug}/{fileIdOrName}
	// This implies that orgId, resource identifier (ID or slug), and file identifier (ID or name)
	// are all passed as path parameters.
	// Example considerations for alternative paths (kept for reference):
	// - /organizations/{orgId}/resources/{resourceSlug}/files/{fileName}/download
	// - /resources/{resourceId}/files/{fileId}/download
	// - /resources/slug/{orgSlug}/{resourceSlug}/files/{fileName}/download
	// The current handler logic attempts to resolve resource by ID/slug and file by ID/name from these params.

	orgIDStr := c.Param("orgId")
	resourceIdent := c.Param("resourceIdOrSlug") // This can be ID or Slug
	fileIDOrName := c.Param("fileIdOrName")      // This can be File ID or File Name

	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID for download path", err)
	}

	// 1. Get the Resource (ensuring it's public/published)
	var resourcePayload *payloads.ResourceResponse
	resourceUUID, parseErr := uuid.Parse(resourceIdent)
	if parseErr == nil {
		tempRes, getErr := h.Service.GetResourceByID(c.Request().Context(), resourceUUID)
		if getErr != nil || tempRes.Status != "published" || (tempRes.Visibility != "public" && tempRes.OrganizationID != orgID) {
			return utils.HandleError(c, http.StatusNotFound, "Resource not found or not publicly accessible by ID.", getErr)
		}
		if tempRes.OrganizationID != orgID {
			return utils.HandleError(c, http.StatusNotFound, "Resource organization mismatch for download by ID.", nil)
		}
		resourcePayload = tempRes
	} else {
		tempRes, getErr := h.Service.GetResourceByOrgAndSlug(c.Request().Context(), orgID, resourceIdent, true)
		if getErr != nil {
			return utils.HandleError(c, http.StatusNotFound, "Resource not found or not publicly accessible by slug.", getErr)
		}
		resourcePayload = tempRes // GetResourceByOrgAndSlug already checks public/published status
	}

	if resourcePayload == nil { // Should not happen if above logic is correct
		return utils.HandleError(c, http.StatusNotFound, "Resource not found for download.", nil)
	}

	// 2. Find the specific file within the resource's files
	var targetFile *payloads.ResourceFileResponse
	targetFileID, parseFileErr := uuid.Parse(fileIDOrName)

	for _, f := range resourcePayload.Files {
		if parseFileErr == nil && f.ID == targetFileID { // Matched by File ID
			targetFile = &f
			break
		}
		if parseFileErr != nil && f.FileName == fileIDOrName { // Matched by File Name (if fileIdOrName was not a UUID)
			// Potential issue: multiple files with same name? DB schema should prevent this for a single resource.
			targetFile = &f
			break
		}
	}

	if targetFile == nil {
		return utils.HandleError(c, http.StatusNotFound, "File not found within the resource.", nil)
	}

	// 3. Serve the file
	// Ensure targetFile.FilePath is a safe, absolute path or correctly relative to a secure base directory.
	// For this example, assuming FilePath stored in DB is the direct path from service constants like UploadDirResources.
	// It should be validated to prevent path traversal attacks if it comes from user input or is complexly derived.
	// Here, service layer constructs it, so it should be from a trusted base.
	return c.File(targetFile.FilePath)
}

// --- Organization File Management Endpoints (Direct file/folder manipulation) ---

// CreateOrganizationFolder godoc
// @Summary Create a new folder within an organization's file storage
// @Description Creates a new folder. A parent_folder_id can be provided to create a subfolder. Requires admin/staff privileges.
// @Tags Content Management (Organization Files - Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID) where the folder will be created"
// @Param body body payloads.CreateOrganizationFolderRequest true "Folder name and optional parent_folder_id"
// @Success 201 {object} payloads.OrganizationFileResponse "Details of the newly created folder"
// @Failure 400 {object} utils.ErrorResponse "Invalid request body or organization/parent_folder ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Parent folder not found (if specified)"
// @Failure 409 {object} utils.ErrorResponse "A folder or file with the same name already exists in the target location"
// @Failure 500 {object} utils.ErrorResponse "Failed to create folder"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/files/folder [post]
func (h *ResourceHandler) CreateOrganizationFolder(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	var req payloads.CreateOrganizationFolderRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body for folder creation", err)
	}
	if err := h.Validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed for folder creation", err)
	}

	dbFolder, err := h.Service.CreateOrganizationFolder(c.Request().Context(), orgID, req)
	if err != nil {
		if err == utils.ErrNotFound { // e.g. parent folder not found
			return utils.HandleError(c, http.StatusNotFound, err.Error(), err)
		} else if err == utils.ErrConflict { // e.g. folder name collision
			return utils.HandleError(c, http.StatusConflict, err.Error(), err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to create organization folder")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to create folder", err)
	}

	return c.JSON(http.StatusCreated, payloads.ToOrganizationFileResponse(dbFolder))
}

// UploadOrganizationFile godoc
// @Summary Upload a file directly to an organization's folder
// @Description Uploads a file to the organization's file storage. Can specify a parent_folder_id to upload into a specific folder, otherwise uploads to the root. Requires admin/staff privileges.
// @Tags Content Management (Organization Files - Admin)
// @Accept multipart/form-data
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param file formData file true "File to upload"
// @Param parent_folder_id formData string false "Parent Folder ID (UUID) to upload into (optional, defaults to root)"
// @Success 201 {object} payloads.OrganizationFileResponse "Details of the uploaded organization file"
// @Failure 400 {object} utils.ErrorResponse "Invalid ID format, file upload error, or file type/size issue"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Parent folder not found (if specified)"
// @Failure 409 {object} utils.ErrorResponse "File name conflict in the target folder"
// @Failure 500 {object} utils.ErrorResponse "Failed to process or save uploaded file"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/files/upload [post]
func (h *ResourceHandler) UploadOrganizationFile(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	fileHeader, err := c.FormFile("file")
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "File upload error: "+err.Error(), err)
	}

	parentFolderIDStr := c.FormValue("parent_folder_id")
	var parentFolderID uuid.UUID
	if parentFolderIDStr != "" {
		parentFolderID, err = uuid.Parse(parentFolderIDStr)
		if err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid parent_folder_id format", err)
		}
	}

	req := payloads.UploadOrganizationFileRequest{
		ParentFolderID: parentFolderID, // Will be uuid.Nil if not provided or parse failed before this point
	}
	// Note: We don't call h.Validator.ValidateStruct(req) here because `file` is not part of `req` for struct validation.
	// File validation (size, type) is typically handled in the service during processing.

	dbFile, err := h.Service.UploadOrganizationFile(c.Request().Context(), orgID, req, fileHeader)
	if err != nil {
		if err == utils.ErrNotFound { // e.g. parent folder not found
			return utils.HandleError(c, http.StatusNotFound, err.Error(), err)
		} else if err == utils.ErrConflict { // e.g. file name collision
			return utils.HandleError(c, http.StatusConflict, err.Error(), err)
		} else if strings.Contains(err.Error(), "exceeds max size") || strings.Contains(err.Error(), "failed to open") {
			return utils.HandleError(c, http.StatusBadRequest, err.Error(), err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to upload organization file")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to process or save uploaded organization file", err)
	}

	return c.JSON(http.StatusCreated, payloads.ToOrganizationFileResponse(dbFile))
}

// ListOrganizationFiles godoc
// @Summary List files and folders for an organization
// @Description Lists files and folders within an organization's storage. Use the `parent_folder_id` query parameter to list contents of a specific subfolder. Omitting `parent_folder_id` lists the root contents. Requires admin/staff privileges.
// @Tags Content Management (Organization Files - Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param parent_folder_id query string false "Parent Folder ID (UUID) to list contents of (optional, defaults to root)"
// @Param limit query int false "Limit number of results per page" default(50) minimum(1) maximum(200)
// @Param offset query int false "Offset for pagination" default(0) minimum(0)
// @Success 200 {array} payloads.OrganizationFileResponse "List of files and folders in the specified location"
// @Header 200 {string} X-Total-Count "Total number of items in the specified location"
// @Failure 400 {object} utils.ErrorResponse "Invalid ID format or query parameters"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Parent folder not found (if specified)"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve file list"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/files [get]
func (h *ResourceHandler) ListOrganizationFiles(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	limit := utils.ParseQueryInt(c, "limit", DefaultOrgFilesLimit)
	offset := utils.ParseQueryInt(c, "offset", 0)
	if limit <= 0 || limit > MaxOrgFilesLimit {
		limit = DefaultOrgFilesLimit
	}

	// Parse parent_folder_id from query param
	parentFolderIDStr := c.QueryParam("parent_folder_id")
	var parentFolderIDPtr *uuid.UUID
	if parentFolderIDStr != "" {
		parsedUUID, err := uuid.Parse(parentFolderIDStr)
		if err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid parent_folder_id format", err)
		}
		if parsedUUID != uuid.Nil {
			parentFolderIDPtr = &parsedUUID
		}
	}

	// TODO: Handle `path` query parameter for looking up parent_folder_id if provided
	path := c.QueryParam("path") // Currently unused by service, requires path resolution logic
	var pathPtr *string
	if path != "" {
		pathPtr = &path
	}

	listParams := payloads.ListOrgFilesParams{
		ParentFolderID: parentFolderIDPtr,
		Path:           pathPtr, // Pass along, though service might ignore it for now
		Limit:          limit,
		Offset:         offset,
	}

	// Call the service method
	files, totalCount, err := h.Service.ListOrganizationFiles(c.Request().Context(), orgID, listParams)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to list organization files")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve organization files", err)
	}

	// Prepend base URL to file paths
	for idx := range files {
		// Only prepend if FilePath is not empty and doesn't already have prefix
		if files[idx].FilePath != "" && !strings.HasPrefix(files[idx].FilePath, "http://") && !strings.HasPrefix(files[idx].FilePath, "https://") {
			// For OrganizationFileResponse, FilePath already holds logical path relative to root or physical name
			// We need the *physical* path stored in DB to construct download URL
			// This approach needs rethinking. Let's modify the Download handlers instead.
			// This List handler should perhaps return the logical path as intended.
			// files[idx].FilePath = appBaseURL + "/uploads/organization-files/" + orgIDStr + "/" + files[idx].FilePath // This is wrong, FilePath might be a folder path
		}
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, files)
}

// UpdateOrganizationFile godoc
// @Summary Rename a file or folder, or move it
// @Description Updates the name of a file/folder or moves it to a different parent folder. Provide `name` to rename, `parent_folder_id` to move. Both can be provided. Requires admin/staff privileges.
// @Tags Content Management (Organization Files - Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param fileOrFolderId path string true "File or Folder ID (UUID) to update"
// @Param body body payloads.UpdateOrganizationFileRequest true "New name and/or new parent_folder_id"
// @Success 200 {object} payloads.OrganizationFileResponse "Details of the updated file or folder"
// @Failure 400 {object} utils.ErrorResponse "Invalid request body or ID format, or invalid operation (e.g., cyclic move)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Operation not allowed (e.g., moving root)"
// @Failure 404 {object} utils.ErrorResponse "File/folder or target parent folder not found"
// @Failure 409 {object} utils.ErrorResponse "Name conflict in the target location"
// @Failure 500 {object} utils.ErrorResponse "Failed to update file/folder"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/files/{fileOrFolderId} [put]
func (h *ResourceHandler) UpdateOrganizationFile(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	fileOrFolderIDStr := c.Param("fileOrFolderId")
	fileOrFolderID, err := uuid.Parse(fileOrFolderIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid file/folder ID format", err)
	}

	var req payloads.UpdateOrganizationFileRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body", err)
	}
	if err := h.Validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	// Call the service method (signature confirmed to match)
	updatedFileDB, err := h.Service.UpdateOrganizationFile(c.Request().Context(), orgID, fileOrFolderID, req)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "File/folder not found", err)
		} else if err == utils.ErrConflict {
			return utils.HandleError(c, http.StatusConflict, "Name conflict in target location", err)
		} else if err == utils.ErrBadRequest {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid request (e.g., cyclic move, invalid parent)", err)
		} else if err == utils.ErrForbidden {
			return utils.HandleError(c, http.StatusForbidden, "Operation not allowed", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to update organization file/folder")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to update file/folder", err)
	}

	// Convert DB response to payload response
	updatedFileResp := payloads.ToOrganizationFileResponse(updatedFileDB)

	// Do NOT prepend base URL here. FilePath in response should represent logical structure or be omitted.

	return c.JSON(http.StatusOK, updatedFileResp)
}

// DeleteOrganizationFile godoc
// @Summary Delete a file or folder (recursively if folder)
// @Description Deletes a file or folder from the organization's storage. If a folder is deleted, its contents are deleted recursively. Requires admin/staff privileges.
// @Tags Content Management (Organization Files - Admin)
// @Param orgId path string true "Organization ID (UUID)"
// @Param fileOrFolderId path string true "File or Folder ID (UUID) to delete"
// @Success 204 "No Content"
// @Failure 400 {object} utils.ErrorResponse "Invalid ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "File/folder not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to delete file/folder"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/files/{fileOrFolderId} [delete]
func (h *ResourceHandler) DeleteOrganizationFile(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	fileIDStr := c.Param("fileOrFolderId")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid file/folder ID format", err)
	}

	err = h.Service.DeleteOrganizationFile(c.Request().Context(), orgID, fileID)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "File/folder not found for deletion", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to delete organization file/folder")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to delete file/folder", err)
	}

	return c.NoContent(http.StatusNoContent)
}

// DownloadOrganizationDirectFile godoc
// @Summary Download a file directly from organization storage
// @Description Downloads a specific file directly using its ID from the organization's file storage. Requires admin/staff privileges.
// @Tags Content Management (Organization Files - Admin)
// @Produce octet-stream
// @Param orgId path string true "Organization ID (UUID)"
// @Param fileId path string true "File ID (UUID) to download"
// @Success 200 {file} binary "The requested organization file"
// @Failure 400 {object} utils.ErrorResponse "Invalid ID format or trying to download a folder"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "File not found or does not belong to this organization"
// @Failure 500 {object} utils.ErrorResponse "Error preparing file for download"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/files/{fileId}/download [get]
func (h *ResourceHandler) DownloadOrganizationDirectFile(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	fileIDStr := c.Param("fileId")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid file ID format", err)
	}

	// For this direct download, we might fetch the OrganizationFile record directly
	// to get its stored FilePath (which should be the relative path from the uploads root).
	dbFile, err := h.Service.Querier.GetOrganizationFileByID(c.Request().Context(), fileID) // Direct store access for this simple case
	if err != nil {
		if err == sql.ErrNoRows { // Assuming sqlc returns sql.ErrNoRows
			return utils.HandleError(c, http.StatusNotFound, "File not found in organization storage", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Error fetching organization file DB entry for download")
		return utils.HandleError(c, http.StatusInternalServerError, "Error preparing file for download.", err)
	}

	// 2. Validate it belongs to the organization from the path and is not a folder
	if dbFile.OrganizationID != orgID {
		return utils.HandleError(c, http.StatusNotFound, "File not found under this organization (mismatch).", nil) // NotFound to avoid leaking info
	}
	if dbFile.IsFolder {
		return utils.HandleError(c, http.StatusBadRequest, "Cannot download a folder, only files.", nil)
	}

	// 3. Construct the full physical path and serve
	// dbFile.FilePath should be the logical path from the org's root file directory.
	// e.g., "folder1/subfolder2/actual-file-name-on-disk.txt"
	// Service constant UploadDirOrganizationFiles = "uploads/organization-files"
	physicalFilePath := filepath.Join(services.UploadDirOrganizationFiles, orgID.String(), dbFile.FilePath)

	// Sanitize and check path to prevent traversal. filepath.Clean is a good start.
	// Ensure it's still within the intended base directory.
	// physicalFilePath, err = filepath.Abs(physicalFilePath) // Make absolute
	// if err != nil { ... }
	// if !strings.HasPrefix(physicalFilePath, expectedBaseDirAbs) { ... return error ... }
	// For now, assume path stored in DB is safe and directly usable relative to org's base.

	return c.File(physicalFilePath)
}
