// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: resources.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const countAllPublishedResources = `-- name: CountAllPublishedResources :one
SELECT count(*) FROM resources
WHERE status = 'published'
`

func (q *Queries) CountAllPublishedResources(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, countAllPublishedResources)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countOrganizationFilesInFolder = `-- name: CountOrganizationFilesInFolder :one
SELECT count(*) FROM organization_files
WHERE organization_id = $1
AND parent_folder_id = $2
`

type CountOrganizationFilesInFolderParams struct {
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	ParentFolderID *uuid.UUID `db:"parent_folder_id" json:"parent_folder_id"`
}

func (q *Queries) CountOrganizationFilesInFolder(ctx context.Context, arg CountOrganizationFilesInFolderParams) (int64, error) {
	row := q.db.QueryRow(ctx, countOrganizationFilesInFolder, arg.OrganizationID, arg.ParentFolderID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countOrganizationFilesInRoot = `-- name: CountOrganizationFilesInRoot :one
SELECT count(*) FROM organization_files
WHERE organization_id = $1
AND parent_folder_id IS NULL
`

func (q *Queries) CountOrganizationFilesInRoot(ctx context.Context, organizationID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, countOrganizationFilesInRoot, organizationID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPublishedResources = `-- name: CountPublishedResources :one
SELECT COUNT(*) FROM resources
WHERE
    status = 'published'
    AND ($1::text IS NULL OR visibility = $1::text)
    AND (
        ($2::UUID IS NULL AND $3::UUID IS NULL) OR
        ($2::UUID IS NOT NULL AND organization_id = $2::UUID) OR
        ($3::UUID IS NOT NULL AND organization_id = $3::UUID)
    )
    AND ($4::TEXT IS NULL OR (title ILIKE '%' || $4::TEXT || '%' OR description ILIKE '%' || $4::TEXT || '%'))
`

type CountPublishedResourcesParams struct {
	VisibilityFilter      *string    `db:"visibility_filter" json:"visibility_filter"`
	OrganizationIDFilter  *uuid.UUID `db:"organization_id_filter" json:"organization_id_filter"`
	OrganizationId2Filter *uuid.UUID `db:"organization_id2_filter" json:"organization_id2_filter"`
	SearchTerm            *string    `db:"search_term" json:"search_term"`
}

func (q *Queries) CountPublishedResources(ctx context.Context, arg CountPublishedResourcesParams) (int64, error) {
	row := q.db.QueryRow(ctx, countPublishedResources,
		arg.VisibilityFilter,
		arg.OrganizationIDFilter,
		arg.OrganizationId2Filter,
		arg.SearchTerm,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPublishedResourcesByOrganization = `-- name: CountPublishedResourcesByOrganization :one
SELECT COUNT(*) FROM resources
WHERE organization_id = $1 AND status = 'published'
`

func (q *Queries) CountPublishedResourcesByOrganization(ctx context.Context, organizationID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, countPublishedResourcesByOrganization, organizationID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPublishedResourcesByVisibility = `-- name: CountPublishedResourcesByVisibility :one
SELECT count(*) FROM resources
WHERE status = 'published' AND visibility = $1
`

func (q *Queries) CountPublishedResourcesByVisibility(ctx context.Context, visibility string) (int64, error) {
	row := q.db.QueryRow(ctx, countPublishedResourcesByVisibility, visibility)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countResourcesByOrganization = `-- name: CountResourcesByOrganization :one
SELECT count(*) FROM resources
WHERE organization_id = $1
  AND ($2::TEXT IS NULL OR status = $2::TEXT)
  AND ($3::TEXT IS NULL OR (title ILIKE '%' || $3::TEXT || '%' OR description ILIKE '%' || $3::TEXT || '%'))
`

type CountResourcesByOrganizationParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Status         *string   `db:"status" json:"status"`
	SearchTerm     *string   `db:"search_term" json:"search_term"`
}

func (q *Queries) CountResourcesByOrganization(ctx context.Context, arg CountResourcesByOrganizationParams) (int64, error) {
	row := q.db.QueryRow(ctx, countResourcesByOrganization, arg.OrganizationID, arg.Status, arg.SearchTerm)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createOrganizationFile = `-- name: CreateOrganizationFile :one
INSERT INTO organization_files (
    organization_id,
    file_name,
    file_path,
    file_type,
    file_size,
    is_folder,
    parent_folder_id
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) RETURNING id, organization_id, file_name, file_path, file_type, file_size, is_folder, parent_folder_id, created_at, updated_at
`

type CreateOrganizationFileParams struct {
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	FileName       string     `db:"file_name" json:"file_name"`
	FilePath       string     `db:"file_path" json:"file_path"`
	FileType       *string    `db:"file_type" json:"file_type"`
	FileSize       *int64     `db:"file_size" json:"file_size"`
	IsFolder       bool       `db:"is_folder" json:"is_folder"`
	ParentFolderID *uuid.UUID `db:"parent_folder_id" json:"parent_folder_id"`
}

func (q *Queries) CreateOrganizationFile(ctx context.Context, arg CreateOrganizationFileParams) (OrganizationFile, error) {
	row := q.db.QueryRow(ctx, createOrganizationFile,
		arg.OrganizationID,
		arg.FileName,
		arg.FilePath,
		arg.FileType,
		arg.FileSize,
		arg.IsFolder,
		arg.ParentFolderID,
	)
	var i OrganizationFile
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.IsFolder,
		&i.ParentFolderID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createResource = `-- name: CreateResource :one
INSERT INTO resources (
    organization_id,
    title,
    slug,
    description,
    visibility,
    status,
    published_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) RETURNING id, organization_id, title, slug, description, visibility, status, published_at, created_at, updated_at
`

type CreateResourceParams struct {
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	Title          string     `db:"title" json:"title"`
	Slug           string     `db:"slug" json:"slug"`
	Description    *string    `db:"description" json:"description"`
	Visibility     string     `db:"visibility" json:"visibility"`
	Status         string     `db:"status" json:"status"`
	PublishedAt    *time.Time `db:"published_at" json:"published_at"`
}

func (q *Queries) CreateResource(ctx context.Context, arg CreateResourceParams) (Resource, error) {
	row := q.db.QueryRow(ctx, createResource,
		arg.OrganizationID,
		arg.Title,
		arg.Slug,
		arg.Description,
		arg.Visibility,
		arg.Status,
		arg.PublishedAt,
	)
	var i Resource
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Description,
		&i.Visibility,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createResourceFile = `-- name: CreateResourceFile :one
INSERT INTO resource_files (
    resource_id,
    file_name,
    file_path,
    file_type,
    file_size,
    description
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING id, resource_id, file_name, file_path, file_type, file_size, uploaded_at, description
`

type CreateResourceFileParams struct {
	ResourceID  uuid.UUID `db:"resource_id" json:"resource_id"`
	FileName    string    `db:"file_name" json:"file_name"`
	FilePath    string    `db:"file_path" json:"file_path"`
	FileType    string    `db:"file_type" json:"file_type"`
	FileSize    int64     `db:"file_size" json:"file_size"`
	Description *string   `db:"description" json:"description"`
}

func (q *Queries) CreateResourceFile(ctx context.Context, arg CreateResourceFileParams) (ResourceFile, error) {
	row := q.db.QueryRow(ctx, createResourceFile,
		arg.ResourceID,
		arg.FileName,
		arg.FilePath,
		arg.FileType,
		arg.FileSize,
		arg.Description,
	)
	var i ResourceFile
	err := row.Scan(
		&i.ID,
		&i.ResourceID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.UploadedAt,
		&i.Description,
	)
	return i, err
}

const deleteOrganizationFile = `-- name: DeleteOrganizationFile :exec
DELETE FROM organization_files
WHERE id = $1
`

func (q *Queries) DeleteOrganizationFile(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteOrganizationFile, id)
	return err
}

const deleteResource = `-- name: DeleteResource :exec
DELETE FROM resources
WHERE id = $1
`

func (q *Queries) DeleteResource(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteResource, id)
	return err
}

const deleteResourceFile = `-- name: DeleteResourceFile :exec
DELETE FROM resource_files
WHERE id = $1
`

func (q *Queries) DeleteResourceFile(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteResourceFile, id)
	return err
}

const getAllDescendantOrganizationItemsIncludingParent = `-- name: GetAllDescendantOrganizationItemsIncludingParent :many
WITH RECURSIVE descendants_cte AS ( 
    SELECT 
        id,
        organization_id,
        file_name,
        file_path,
        file_type,
        file_size,
        is_folder,
        parent_folder_id,
        created_at,
        updated_at
    FROM organization_files
    WHERE organization_files.id = $1 -- Explicitly qualify id in the anchor part

    UNION ALL

    SELECT
        f.id,
        f.organization_id,
        f.file_name,
        f.file_path,
        f.file_type,
        f.file_size,
        f.is_folder,
        f.parent_folder_id,
        f.created_at,
        f.updated_at
    FROM organization_files f
    INNER JOIN descendants_cte d_recursive_join ON f.parent_folder_id = d_recursive_join.id
)
SELECT 
    id, 
    organization_id,
    file_name,
    file_path,
    file_type,
    file_size,
    is_folder,
    parent_folder_id,
    created_at,
    updated_at
FROM descendants_cte
`

type GetAllDescendantOrganizationItemsIncludingParentRow struct {
	ID             uuid.UUID  `db:"id" json:"id"`
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	FileName       string     `db:"file_name" json:"file_name"`
	FilePath       string     `db:"file_path" json:"file_path"`
	FileType       *string    `db:"file_type" json:"file_type"`
	FileSize       *int64     `db:"file_size" json:"file_size"`
	IsFolder       bool       `db:"is_folder" json:"is_folder"`
	ParentFolderID *uuid.UUID `db:"parent_folder_id" json:"parent_folder_id"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}

func (q *Queries) GetAllDescendantOrganizationItemsIncludingParent(ctx context.Context, id uuid.UUID) ([]GetAllDescendantOrganizationItemsIncludingParentRow, error) {
	rows, err := q.db.Query(ctx, getAllDescendantOrganizationItemsIncludingParent, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetAllDescendantOrganizationItemsIncludingParentRow{}
	for rows.Next() {
		var i GetAllDescendantOrganizationItemsIncludingParentRow
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.IsFolder,
			&i.ParentFolderID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOrganizationFileByID = `-- name: GetOrganizationFileByID :one
SELECT id, organization_id, file_name, file_path, file_type, file_size, is_folder, parent_folder_id, created_at, updated_at FROM organization_files
WHERE id = $1 LIMIT 1
`

func (q *Queries) GetOrganizationFileByID(ctx context.Context, id uuid.UUID) (OrganizationFile, error) {
	row := q.db.QueryRow(ctx, getOrganizationFileByID, id)
	var i OrganizationFile
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.IsFolder,
		&i.ParentFolderID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getResourceByID = `-- name: GetResourceByID :one
SELECT id, organization_id, title, slug, description, visibility, status, published_at, created_at, updated_at FROM resources
WHERE id = $1 LIMIT 1
`

func (q *Queries) GetResourceByID(ctx context.Context, id uuid.UUID) (Resource, error) {
	row := q.db.QueryRow(ctx, getResourceByID, id)
	var i Resource
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Description,
		&i.Visibility,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getResourceByOrgAndSlug = `-- name: GetResourceByOrgAndSlug :one
SELECT id, organization_id, title, slug, description, visibility, status, published_at, created_at, updated_at FROM resources
WHERE organization_id = $1 AND slug = $2 LIMIT 1
`

type GetResourceByOrgAndSlugParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Slug           string    `db:"slug" json:"slug"`
}

func (q *Queries) GetResourceByOrgAndSlug(ctx context.Context, arg GetResourceByOrgAndSlugParams) (Resource, error) {
	row := q.db.QueryRow(ctx, getResourceByOrgAndSlug, arg.OrganizationID, arg.Slug)
	var i Resource
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Description,
		&i.Visibility,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getResourceByOrgAndSlugWithFiles = `-- name: GetResourceByOrgAndSlugWithFiles :many
SELECT
    r.id, r.organization_id, r.title, r.slug, r.description, r.visibility, r.status, r.published_at, r.created_at, r.updated_at,
    rf.id AS resource_file_id,
    rf.resource_id AS resource_file_resource_id,
    rf.file_name AS resource_file_file_name,
    rf.file_path AS resource_file_file_path,
    rf.file_type AS resource_file_file_type,
    rf.file_size AS resource_file_file_size,
    rf.description AS resource_file_description,
    rf.uploaded_at AS resource_file_uploaded_at
FROM
    resources r
LEFT JOIN
    resource_files rf ON r.id = rf.resource_id
WHERE
    r.organization_id = $1 AND r.slug = $2
`

type GetResourceByOrgAndSlugWithFilesParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Slug           string    `db:"slug" json:"slug"`
}

type GetResourceByOrgAndSlugWithFilesRow struct {
	Resource                Resource   `db:"resource" json:"resource"`
	ResourceFileID          *uuid.UUID `db:"resource_file_id" json:"resource_file_id"`
	ResourceFileResourceID  *uuid.UUID `db:"resource_file_resource_id" json:"resource_file_resource_id"`
	ResourceFileFileName    *string    `db:"resource_file_file_name" json:"resource_file_file_name"`
	ResourceFileFilePath    *string    `db:"resource_file_file_path" json:"resource_file_file_path"`
	ResourceFileFileType    *string    `db:"resource_file_file_type" json:"resource_file_file_type"`
	ResourceFileFileSize    *int64     `db:"resource_file_file_size" json:"resource_file_file_size"`
	ResourceFileDescription *string    `db:"resource_file_description" json:"resource_file_description"`
	ResourceFileUploadedAt  *time.Time `db:"resource_file_uploaded_at" json:"resource_file_uploaded_at"`
}

func (q *Queries) GetResourceByOrgAndSlugWithFiles(ctx context.Context, arg GetResourceByOrgAndSlugWithFilesParams) ([]GetResourceByOrgAndSlugWithFilesRow, error) {
	rows, err := q.db.Query(ctx, getResourceByOrgAndSlugWithFiles, arg.OrganizationID, arg.Slug)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetResourceByOrgAndSlugWithFilesRow{}
	for rows.Next() {
		var i GetResourceByOrgAndSlugWithFilesRow
		if err := rows.Scan(
			&i.Resource.ID,
			&i.Resource.OrganizationID,
			&i.Resource.Title,
			&i.Resource.Slug,
			&i.Resource.Description,
			&i.Resource.Visibility,
			&i.Resource.Status,
			&i.Resource.PublishedAt,
			&i.Resource.CreatedAt,
			&i.Resource.UpdatedAt,
			&i.ResourceFileID,
			&i.ResourceFileResourceID,
			&i.ResourceFileFileName,
			&i.ResourceFileFilePath,
			&i.ResourceFileFileType,
			&i.ResourceFileFileSize,
			&i.ResourceFileDescription,
			&i.ResourceFileUploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getResourceFileByID = `-- name: GetResourceFileByID :one
SELECT id, resource_id, file_name, file_path, file_type, file_size, uploaded_at, description FROM resource_files
WHERE id = $1 LIMIT 1
`

func (q *Queries) GetResourceFileByID(ctx context.Context, id uuid.UUID) (ResourceFile, error) {
	row := q.db.QueryRow(ctx, getResourceFileByID, id)
	var i ResourceFile
	err := row.Scan(
		&i.ID,
		&i.ResourceID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.UploadedAt,
		&i.Description,
	)
	return i, err
}

const getResourceWithFiles = `-- name: GetResourceWithFiles :many
SELECT
    -- Embed the main resource table
    r.id, r.organization_id, r.title, r.slug, r.description, r.visibility, r.status, r.published_at, r.created_at, r.updated_at,
    -- Explicitly select columns from resource_files for the LEFT JOIN
    -- SQLC should generate pointer types for these in the _Row struct
    -- due to the LEFT JOIN context.
    rf.id AS resource_file_id,
    rf.resource_id AS resource_file_resource_id,
    rf.file_name AS resource_file_file_name,
    rf.file_path AS resource_file_file_path,
    rf.file_type AS resource_file_file_type,
    rf.file_size AS resource_file_file_size,
    rf.description AS resource_file_description,
    rf.uploaded_at AS resource_file_uploaded_at
FROM
    resources r
LEFT JOIN
    resource_files rf ON r.id = rf.resource_id
WHERE
    r.id = $1
`

type GetResourceWithFilesRow struct {
	Resource                Resource   `db:"resource" json:"resource"`
	ResourceFileID          *uuid.UUID `db:"resource_file_id" json:"resource_file_id"`
	ResourceFileResourceID  *uuid.UUID `db:"resource_file_resource_id" json:"resource_file_resource_id"`
	ResourceFileFileName    *string    `db:"resource_file_file_name" json:"resource_file_file_name"`
	ResourceFileFilePath    *string    `db:"resource_file_file_path" json:"resource_file_file_path"`
	ResourceFileFileType    *string    `db:"resource_file_file_type" json:"resource_file_file_type"`
	ResourceFileFileSize    *int64     `db:"resource_file_file_size" json:"resource_file_file_size"`
	ResourceFileDescription *string    `db:"resource_file_description" json:"resource_file_description"`
	ResourceFileUploadedAt  *time.Time `db:"resource_file_uploaded_at" json:"resource_file_uploaded_at"`
}

func (q *Queries) GetResourceWithFiles(ctx context.Context, id uuid.UUID) ([]GetResourceWithFilesRow, error) {
	rows, err := q.db.Query(ctx, getResourceWithFiles, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetResourceWithFilesRow{}
	for rows.Next() {
		var i GetResourceWithFilesRow
		if err := rows.Scan(
			&i.Resource.ID,
			&i.Resource.OrganizationID,
			&i.Resource.Title,
			&i.Resource.Slug,
			&i.Resource.Description,
			&i.Resource.Visibility,
			&i.Resource.Status,
			&i.Resource.PublishedAt,
			&i.Resource.CreatedAt,
			&i.Resource.UpdatedAt,
			&i.ResourceFileID,
			&i.ResourceFileResourceID,
			&i.ResourceFileFileName,
			&i.ResourceFileFilePath,
			&i.ResourceFileFileType,
			&i.ResourceFileFileSize,
			&i.ResourceFileDescription,
			&i.ResourceFileUploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getScheduledResourcesToPublish = `-- name: GetScheduledResourcesToPublish :many
SELECT id FROM resources WHERE status = 'draft' AND published_at IS NOT NULL AND published_at <= NOW()
`

func (q *Queries) GetScheduledResourcesToPublish(ctx context.Context) ([]uuid.UUID, error) {
	rows, err := q.db.Query(ctx, getScheduledResourcesToPublish)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []uuid.UUID{}
	for rows.Next() {
		var id uuid.UUID
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		items = append(items, id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listFilesByResource = `-- name: ListFilesByResource :many
SELECT id, resource_id, file_name, file_path, file_type, file_size, description, uploaded_at
FROM resource_files
WHERE resource_id = $1
ORDER BY uploaded_at ASC
`

type ListFilesByResourceRow struct {
	ID          uuid.UUID `db:"id" json:"id"`
	ResourceID  uuid.UUID `db:"resource_id" json:"resource_id"`
	FileName    string    `db:"file_name" json:"file_name"`
	FilePath    string    `db:"file_path" json:"file_path"`
	FileType    string    `db:"file_type" json:"file_type"`
	FileSize    int64     `db:"file_size" json:"file_size"`
	Description *string   `db:"description" json:"description"`
	UploadedAt  time.Time `db:"uploaded_at" json:"uploaded_at"`
}

func (q *Queries) ListFilesByResource(ctx context.Context, resourceID uuid.UUID) ([]ListFilesByResourceRow, error) {
	rows, err := q.db.Query(ctx, listFilesByResource, resourceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListFilesByResourceRow{}
	for rows.Next() {
		var i ListFilesByResourceRow
		if err := rows.Scan(
			&i.ID,
			&i.ResourceID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.Description,
			&i.UploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOrganizationFilesInFolder = `-- name: ListOrganizationFilesInFolder :many
SELECT id, organization_id, parent_folder_id, file_name, file_path, file_type, file_size, is_folder, created_at, updated_at
FROM organization_files
WHERE organization_id = $1
AND parent_folder_id = $2
ORDER BY is_folder DESC, file_name ASC
LIMIT $3 OFFSET $4
`

type ListOrganizationFilesInFolderParams struct {
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	ParentFolderID *uuid.UUID `db:"parent_folder_id" json:"parent_folder_id"`
	Limit          int32      `db:"limit" json:"limit"`
	Offset         int32      `db:"offset" json:"offset"`
}

type ListOrganizationFilesInFolderRow struct {
	ID             uuid.UUID  `db:"id" json:"id"`
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	ParentFolderID *uuid.UUID `db:"parent_folder_id" json:"parent_folder_id"`
	FileName       string     `db:"file_name" json:"file_name"`
	FilePath       string     `db:"file_path" json:"file_path"`
	FileType       *string    `db:"file_type" json:"file_type"`
	FileSize       *int64     `db:"file_size" json:"file_size"`
	IsFolder       bool       `db:"is_folder" json:"is_folder"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}

func (q *Queries) ListOrganizationFilesInFolder(ctx context.Context, arg ListOrganizationFilesInFolderParams) ([]ListOrganizationFilesInFolderRow, error) {
	rows, err := q.db.Query(ctx, listOrganizationFilesInFolder,
		arg.OrganizationID,
		arg.ParentFolderID,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrganizationFilesInFolderRow{}
	for rows.Next() {
		var i ListOrganizationFilesInFolderRow
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.ParentFolderID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.IsFolder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOrganizationFilesInRoot = `-- name: ListOrganizationFilesInRoot :many
SELECT id, organization_id, parent_folder_id, file_name, file_path, file_type, file_size, is_folder, created_at, updated_at
FROM organization_files
WHERE organization_id = $1
AND parent_folder_id IS NULL
ORDER BY is_folder DESC, file_name ASC
LIMIT $2 OFFSET $3
`

type ListOrganizationFilesInRootParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Limit          int32     `db:"limit" json:"limit"`
	Offset         int32     `db:"offset" json:"offset"`
}

type ListOrganizationFilesInRootRow struct {
	ID             uuid.UUID  `db:"id" json:"id"`
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	ParentFolderID *uuid.UUID `db:"parent_folder_id" json:"parent_folder_id"`
	FileName       string     `db:"file_name" json:"file_name"`
	FilePath       string     `db:"file_path" json:"file_path"`
	FileType       *string    `db:"file_type" json:"file_type"`
	FileSize       *int64     `db:"file_size" json:"file_size"`
	IsFolder       bool       `db:"is_folder" json:"is_folder"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}

func (q *Queries) ListOrganizationFilesInRoot(ctx context.Context, arg ListOrganizationFilesInRootParams) ([]ListOrganizationFilesInRootRow, error) {
	rows, err := q.db.Query(ctx, listOrganizationFilesInRoot, arg.OrganizationID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrganizationFilesInRootRow{}
	for rows.Next() {
		var i ListOrganizationFilesInRootRow
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.ParentFolderID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.IsFolder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublishedResourcesByOrganizationWithFiles = `-- name: ListPublishedResourcesByOrganizationWithFiles :many

SELECT
    r.id, r.organization_id, r.title, r.slug, r.description, r.visibility, r.status, r.published_at, r.created_at, r.updated_at,
    rf.id AS resource_file_id,
    rf.resource_id AS resource_file_resource_id,
    rf.file_name AS resource_file_file_name,
    rf.file_path AS resource_file_file_path,
    rf.file_type AS resource_file_file_type,
    rf.file_size AS resource_file_file_size,
    rf.uploaded_at AS resource_file_uploaded_at
FROM
    resources r
LEFT JOIN
    resource_files rf ON r.id = rf.resource_id
WHERE
    r.organization_id = $1
    AND r.status = 'published'
ORDER BY
    r.published_at DESC NULLS LAST, r.created_at DESC, rf.uploaded_at ASC
LIMIT $2 OFFSET $3
`

type ListPublishedResourcesByOrganizationWithFilesParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Limit          int32     `db:"limit" json:"limit"`
	Offset         int32     `db:"offset" json:"offset"`
}

type ListPublishedResourcesByOrganizationWithFilesRow struct {
	Resource               Resource   `db:"resource" json:"resource"`
	ResourceFileID         *uuid.UUID `db:"resource_file_id" json:"resource_file_id"`
	ResourceFileResourceID *uuid.UUID `db:"resource_file_resource_id" json:"resource_file_resource_id"`
	ResourceFileFileName   *string    `db:"resource_file_file_name" json:"resource_file_file_name"`
	ResourceFileFilePath   *string    `db:"resource_file_file_path" json:"resource_file_file_path"`
	ResourceFileFileType   *string    `db:"resource_file_file_type" json:"resource_file_file_type"`
	ResourceFileFileSize   *int64     `db:"resource_file_file_size" json:"resource_file_file_size"`
	ResourceFileUploadedAt *time.Time `db:"resource_file_uploaded_at" json:"resource_file_uploaded_at"`
}

// Assuming public means published for the org context
func (q *Queries) ListPublishedResourcesByOrganizationWithFiles(ctx context.Context, arg ListPublishedResourcesByOrganizationWithFilesParams) ([]ListPublishedResourcesByOrganizationWithFilesRow, error) {
	rows, err := q.db.Query(ctx, listPublishedResourcesByOrganizationWithFiles, arg.OrganizationID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPublishedResourcesByOrganizationWithFilesRow{}
	for rows.Next() {
		var i ListPublishedResourcesByOrganizationWithFilesRow
		if err := rows.Scan(
			&i.Resource.ID,
			&i.Resource.OrganizationID,
			&i.Resource.Title,
			&i.Resource.Slug,
			&i.Resource.Description,
			&i.Resource.Visibility,
			&i.Resource.Status,
			&i.Resource.PublishedAt,
			&i.Resource.CreatedAt,
			&i.Resource.UpdatedAt,
			&i.ResourceFileID,
			&i.ResourceFileResourceID,
			&i.ResourceFileFileName,
			&i.ResourceFileFilePath,
			&i.ResourceFileFileType,
			&i.ResourceFileFileSize,
			&i.ResourceFileUploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublishedResourcesByVisibility = `-- name: ListPublishedResourcesByVisibility :many
SELECT id, organization_id, title, slug, description, visibility, status, published_at, created_at, updated_at FROM resources
WHERE status = 'published' AND visibility = $1
ORDER BY published_at DESC
LIMIT $2 OFFSET $3
`

type ListPublishedResourcesByVisibilityParams struct {
	Visibility string `db:"visibility" json:"visibility"`
	Limit      int32  `db:"limit" json:"limit"`
	Offset     int32  `db:"offset" json:"offset"`
}

func (q *Queries) ListPublishedResourcesByVisibility(ctx context.Context, arg ListPublishedResourcesByVisibilityParams) ([]Resource, error) {
	rows, err := q.db.Query(ctx, listPublishedResourcesByVisibility, arg.Visibility, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Resource{}
	for rows.Next() {
		var i Resource
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.Slug,
			&i.Description,
			&i.Visibility,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublishedResourcesWithFiles = `-- name: ListPublishedResourcesWithFiles :many
SELECT
    r.id, r.organization_id, r.title, r.slug, r.description, r.visibility, r.status, r.published_at, r.created_at, r.updated_at,
    rf.id AS resource_file_id,
    rf.resource_id AS resource_file_resource_id,
    rf.file_name AS resource_file_file_name,
    rf.file_path AS resource_file_file_path,
    rf.file_type AS resource_file_file_type,
    rf.file_size AS resource_file_file_size,
    rf.description AS resource_file_description,
    rf.uploaded_at AS resource_file_uploaded_at
FROM
    resources r
LEFT JOIN
    resource_files rf ON r.id = rf.resource_id
WHERE
    r.status = 'published'
    AND ($1::text IS NULL OR r.visibility = $1::text)
    AND (
        ($2::UUID IS NULL AND $3::UUID IS NULL) OR
        ($2::UUID IS NOT NULL AND r.organization_id = $2::UUID) OR
        ($3::UUID IS NOT NULL AND r.organization_id = $3::UUID)
    )
    AND ($4::TEXT IS NULL OR (r.title ILIKE '%' || $4::TEXT || '%' OR r.description ILIKE '%' || $4::TEXT || '%'))
ORDER BY
    r.published_at DESC NULLS LAST, r.created_at DESC, rf.uploaded_at ASC
LIMIT $6 OFFSET $5
`

type ListPublishedResourcesWithFilesParams struct {
	VisibilityFilter      *string    `db:"visibility_filter" json:"visibility_filter"`
	OrganizationIDFilter  *uuid.UUID `db:"organization_id_filter" json:"organization_id_filter"`
	OrganizationId2Filter *uuid.UUID `db:"organization_id2_filter" json:"organization_id2_filter"`
	SearchTerm            *string    `db:"search_term" json:"search_term"`
	OffsetVal             int32      `db:"offset_val" json:"offset_val"`
	LimitVal              int32      `db:"limit_val" json:"limit_val"`
}

type ListPublishedResourcesWithFilesRow struct {
	Resource                Resource   `db:"resource" json:"resource"`
	ResourceFileID          *uuid.UUID `db:"resource_file_id" json:"resource_file_id"`
	ResourceFileResourceID  *uuid.UUID `db:"resource_file_resource_id" json:"resource_file_resource_id"`
	ResourceFileFileName    *string    `db:"resource_file_file_name" json:"resource_file_file_name"`
	ResourceFileFilePath    *string    `db:"resource_file_file_path" json:"resource_file_file_path"`
	ResourceFileFileType    *string    `db:"resource_file_file_type" json:"resource_file_file_type"`
	ResourceFileFileSize    *int64     `db:"resource_file_file_size" json:"resource_file_file_size"`
	ResourceFileDescription *string    `db:"resource_file_description" json:"resource_file_description"`
	ResourceFileUploadedAt  *time.Time `db:"resource_file_uploaded_at" json:"resource_file_uploaded_at"`
}

func (q *Queries) ListPublishedResourcesWithFiles(ctx context.Context, arg ListPublishedResourcesWithFilesParams) ([]ListPublishedResourcesWithFilesRow, error) {
	rows, err := q.db.Query(ctx, listPublishedResourcesWithFiles,
		arg.VisibilityFilter,
		arg.OrganizationIDFilter,
		arg.OrganizationId2Filter,
		arg.SearchTerm,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPublishedResourcesWithFilesRow{}
	for rows.Next() {
		var i ListPublishedResourcesWithFilesRow
		if err := rows.Scan(
			&i.Resource.ID,
			&i.Resource.OrganizationID,
			&i.Resource.Title,
			&i.Resource.Slug,
			&i.Resource.Description,
			&i.Resource.Visibility,
			&i.Resource.Status,
			&i.Resource.PublishedAt,
			&i.Resource.CreatedAt,
			&i.Resource.UpdatedAt,
			&i.ResourceFileID,
			&i.ResourceFileResourceID,
			&i.ResourceFileFileName,
			&i.ResourceFileFilePath,
			&i.ResourceFileFileType,
			&i.ResourceFileFileSize,
			&i.ResourceFileDescription,
			&i.ResourceFileUploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublishedResources_NoFiles = `-- name: ListPublishedResources_NoFiles :many
SELECT id, organization_id, title, slug, description, visibility, status, published_at, created_at, updated_at
FROM resources r
WHERE r.status = 'published' AND r.visibility = 'public'
ORDER BY r.published_at DESC NULLS LAST, r.created_at DESC
LIMIT $1 OFFSET $2
`

type ListPublishedResources_NoFilesParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

func (q *Queries) ListPublishedResources_NoFiles(ctx context.Context, arg ListPublishedResources_NoFilesParams) ([]Resource, error) {
	rows, err := q.db.Query(ctx, listPublishedResources_NoFiles, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Resource{}
	for rows.Next() {
		var i Resource
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.Slug,
			&i.Description,
			&i.Visibility,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listResourcesByOrganization = `-- name: ListResourcesByOrganization :many
SELECT id, organization_id, title, slug, description, visibility, status, published_at, created_at, updated_at FROM resources
WHERE organization_id = $1
  AND ($2::TEXT IS NULL OR status = $2::TEXT)
  AND ($3::TEXT IS NULL OR (title ILIKE '%' || $3::TEXT || '%' OR description ILIKE '%' || $3::TEXT || '%'))
ORDER BY created_at DESC
LIMIT $5 OFFSET $4
`

type ListResourcesByOrganizationParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Status         *string   `db:"status" json:"status"`
	SearchTerm     *string   `db:"search_term" json:"search_term"`
	OffsetVal      int32     `db:"offset_val" json:"offset_val"`
	LimitVal       int32     `db:"limit_val" json:"limit_val"`
}

func (q *Queries) ListResourcesByOrganization(ctx context.Context, arg ListResourcesByOrganizationParams) ([]Resource, error) {
	rows, err := q.db.Query(ctx, listResourcesByOrganization,
		arg.OrganizationID,
		arg.Status,
		arg.SearchTerm,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Resource{}
	for rows.Next() {
		var i Resource
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.Slug,
			&i.Description,
			&i.Visibility,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const setResourceStatusToPublished = `-- name: SetResourceStatusToPublished :exec
UPDATE resources SET status = 'published', updated_at = NOW() WHERE id = $1
`

func (q *Queries) SetResourceStatusToPublished(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, setResourceStatusToPublished, id)
	return err
}

const updateOrganizationFile = `-- name: UpdateOrganizationFile :one
UPDATE organization_files
SET
    file_name = COALESCE($1, file_name),
    parent_folder_id = COALESCE($2, parent_folder_id), -- Allow moving files/folders
    file_path = COALESCE($3, file_path), -- Allow updating file path
    updated_at = now()
WHERE id = $4
RETURNING id, organization_id, file_name, file_path, file_type, file_size, is_folder, parent_folder_id, created_at, updated_at
`

type UpdateOrganizationFileParams struct {
	FileName       *string    `db:"file_name" json:"file_name"`
	ParentFolderID *uuid.UUID `db:"parent_folder_id" json:"parent_folder_id"`
	FilePath       *string    `db:"file_path" json:"file_path"`
	ID             uuid.UUID  `db:"id" json:"id"`
}

func (q *Queries) UpdateOrganizationFile(ctx context.Context, arg UpdateOrganizationFileParams) (OrganizationFile, error) {
	row := q.db.QueryRow(ctx, updateOrganizationFile,
		arg.FileName,
		arg.ParentFolderID,
		arg.FilePath,
		arg.ID,
	)
	var i OrganizationFile
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.IsFolder,
		&i.ParentFolderID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateResource = `-- name: UpdateResource :one
UPDATE resources
SET
    title = COALESCE($1, title),
    slug = COALESCE($2, slug),
    description = COALESCE($3, description),
    visibility = COALESCE($4, visibility),
    status = COALESCE($5, status),
    published_at = COALESCE($6, published_at),
    updated_at = now()
WHERE id = $7
RETURNING id, organization_id, title, slug, description, visibility, status, published_at, created_at, updated_at
`

type UpdateResourceParams struct {
	Title       *string    `db:"title" json:"title"`
	Slug        *string    `db:"slug" json:"slug"`
	Description *string    `db:"description" json:"description"`
	Visibility  *string    `db:"visibility" json:"visibility"`
	Status      *string    `db:"status" json:"status"`
	PublishedAt *time.Time `db:"published_at" json:"published_at"`
	ID          uuid.UUID  `db:"id" json:"id"`
}

func (q *Queries) UpdateResource(ctx context.Context, arg UpdateResourceParams) (Resource, error) {
	row := q.db.QueryRow(ctx, updateResource,
		arg.Title,
		arg.Slug,
		arg.Description,
		arg.Visibility,
		arg.Status,
		arg.PublishedAt,
		arg.ID,
	)
	var i Resource
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Description,
		&i.Visibility,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
