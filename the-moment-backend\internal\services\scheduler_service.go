package services

import (
	"Membership-SAAS-System-Backend/db"
	"context"

	"github.com/rs/zerolog/log"
)

type SchedulerService struct {
	queries *db.Queries
	// Potentially add a logger if not using global log
}

func NewSchedulerService(q *db.Queries) *SchedulerService {
	return &SchedulerService{
		queries: q,
	}
}

// PublishScheduledResourcesJob checks for resources that are due to be published and updates their status.
func (s *SchedulerService) PublishScheduledResourcesJob() {
	ctx := context.Background()
	// For logging purposes, let's note when the job runs
	log.Info().Msg("SchedulerService: Running PublishScheduledResourcesJob")

	resourcesToPublish, err := s.queries.GetScheduledResourcesToPublish(ctx)
	if err != nil {
		log.Error().Err(err).Msg("SchedulerService: Error fetching scheduled resources to publish")
		return
	}

	if len(resourcesToPublish) == 0 {
		log.Info().Msg("SchedulerService: No resources currently scheduled for publication.")
		return
	}

	log.Info().Int("count", len(resourcesToPublish)).Msgf("SchedulerService: Found %d resource(s) to publish", len(resourcesToPublish))

	var publishedCount int
	var errorCount int

	for _, resourceID := range resourcesToPublish {
		err := s.queries.SetResourceStatusToPublished(ctx, resourceID)
		if err != nil {
			log.Error().Err(err).Str("resource_id", resourceID.String()).Msg("SchedulerService: Error setting resource status to published")
			errorCount++
			continue
		}
		log.Info().Str("resource_id", resourceID.String()).Msg("SchedulerService: Successfully published resource")
		publishedCount++
	}

	log.Info().
		Int("published_count", publishedCount).
		Int("error_count", errorCount).
		Int("total_found", len(resourcesToPublish)).
		Msg("SchedulerService: PublishScheduledResourcesJob finished")

	// Optional: A more structured log entry at the end could be useful
	// Example:
	// log.Info().
	// 	Time("job_run_time", time.Now()).
	// 	Int("resources_found", len(resourcesToPublish)).
	// 	Int("successfully_published", publishedCount).
	// 	Int("failed_to_publish", errorCount).
	// 	Msg("Scheduled publishing job summary")
}

// PublishScheduledPostsJob checks for posts that are due to be published and updates their status.
func (s *SchedulerService) PublishScheduledPostsJob() {
	ctx := context.Background()
	log.Info().Msg("SchedulerService: Running PublishScheduledPostsJob")

	postsToPublish, err := s.queries.GetScheduledPostsToPublish(ctx)
	if err != nil {
		log.Error().Err(err).Msg("SchedulerService: Error fetching scheduled posts to publish")
		return
	}

	if len(postsToPublish) == 0 {
		log.Info().Msg("SchedulerService: No posts currently scheduled for publication.")
		return
	}

	log.Info().Int("count", len(postsToPublish)).Msgf("SchedulerService: Found %d post(s) to publish", len(postsToPublish))

	var publishedCount int
	var errorCount int

	for _, postID := range postsToPublish {
		err := s.queries.SetPostStatusToPublished(ctx, postID)
		if err != nil {
			log.Error().Err(err).Str("post_id", postID.String()).Msg("SchedulerService: Error setting post status to published")
			errorCount++
			continue
		}
		log.Info().Str("post_id", postID.String()).Msg("SchedulerService: Successfully published post")
		publishedCount++
	}

	log.Info().
		Int("published_count", publishedCount).
		Int("error_count", errorCount).
		Int("total_found", len(postsToPublish)).
		Msg("SchedulerService: PublishScheduledPostsJob finished")
}

// PublishScheduledEventsJob checks for events that are due to be published and updates their status.
func (s *SchedulerService) PublishScheduledEventsJob() {
	ctx := context.Background()
	log.Info().Msg("SchedulerService: Running PublishScheduledEventsJob")

	eventsToPublish, err := s.queries.GetScheduledEventsToPublish(ctx)
	if err != nil {
		log.Error().Err(err).Msg("SchedulerService: Error fetching scheduled events to publish")
		return
	}

	if len(eventsToPublish) == 0 {
		log.Info().Msg("SchedulerService: No events currently scheduled for publication.")
		return
	}

	log.Info().Int("count", len(eventsToPublish)).Msgf("SchedulerService: Found %d event(s) to publish", len(eventsToPublish))

	var publishedCount int
	var errorCount int

	for _, eventID := range eventsToPublish {
		err := s.queries.SetEventStatusToPublished(ctx, eventID)
		if err != nil {
			log.Error().Err(err).Str("event_id", eventID.String()).Msg("SchedulerService: Error setting event status to published")
			errorCount++
			continue
		}
		log.Info().Str("event_id", eventID.String()).Msg("SchedulerService: Successfully published event")
		publishedCount++
	}

	log.Info().
		Int("published_count", publishedCount).
		Int("error_count", errorCount).
		Int("total_found", len(eventsToPublish)).
		Msg("SchedulerService: PublishScheduledEventsJob finished")
}
