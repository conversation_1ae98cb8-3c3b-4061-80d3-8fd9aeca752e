import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { Text, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { withLayoutContext } from 'expo-router';
import { createMaterialTopTabNavigator, MaterialTopTabNavigationOptions } from '@react-navigation/material-top-tabs';
import { ParamListBase, TabNavigationState } from '@react-navigation/native';

const { Navigator } = createMaterialTopTabNavigator();

// Export the custom navigator that integrates with Expo Router
export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  {} // EventMap
>(Navigator);

// Options for the 'explore' route in the parent Tabs navigator
export const options = () => {
  const { t } = useTranslation();
  return {
    title: t('navigation.explore'),
    tabBarIcon: ({ color, size }: { color: string; size: number }) => (
      <MaterialCommunityIcons name="compass-outline" size={size} color={color} />
    ),
  };
};

const staticStyles = StyleSheet.create({
  tabLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    textTransform: 'none',
    padding: 0,
    margin: 0,
  },
});

// Memoized TabBarLabel component
const TabBarLabel = React.memo(({ color, children }: { color: string; children: string }) => (
  <Text style={[{ color }, staticStyles.tabLabel]}>
    {children}
  </Text>
));

export default function ExploreTabLayout() {
  const { t } = useTranslation();
  const activeTheme = appStyleStore(state => state.theme);

  const memoizedTabBarLabel = useCallback(({ focused, color, children }: { focused: boolean; color: string; children: string }) => (
    <TabBarLabel color={color}>
      {children}
    </TabBarLabel>
  ), []);

  const screenOptions = useMemo(() => ({
    tabBarActiveTintColor: activeTheme.colors.primary,
    tabBarInactiveTintColor: activeTheme.system.secondaryText,
    tabBarIndicatorStyle: {
      backgroundColor: activeTheme.colors.primary,
      height: 3,
    },
    tabBarStyle: {
      backgroundColor: activeTheme.colors.background,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 1,
      borderBottomColor: activeTheme.system.border,
    },
    tabBarLabel: memoizedTabBarLabel,
    tabBarPressColor: `${activeTheme.colors.primary}20`,
  }), [activeTheme, memoizedTabBarLabel]);

  const eventsOptions = useMemo(() => ({
    title: t('explore.tabs.events'),
  }), [t]);

  const postsOptions = useMemo(() => ({
    title: t('explore.tabs.posts'),
  }), [t]);

  const resourcesOptions = useMemo(() => ({
    title: t('explore.tabs.resources'),
  }), [t]);

  return (
    <MaterialTopTabs screenOptions={screenOptions}>
      <MaterialTopTabs.Screen name="events" options={eventsOptions} />
      <MaterialTopTabs.Screen name="posts" options={postsOptions} />
      <MaterialTopTabs.Screen name="resources" options={resourcesOptions} />
    </MaterialTopTabs>
  );
}
