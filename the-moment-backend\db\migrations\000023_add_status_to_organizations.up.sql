ALTER TABLE organizations
ADD COLUMN status VARCHAR(50) NOT NULL DEFAULT 'pending_setup';

COMMENT ON COLUMN organizations.status IS 'The current status of the organization (e.g., pending_setup, active, suspended).';

-- Update status for existing organizations that already have an image_url.
-- This ensures that organizations seeded with images (e.g., by 000021_...)
-- or existing orgs that had images before this status field was introduced
-- are correctly marked as 'active'.
UPDATE organizations
SET status = 'active'
WHERE image_url IS NOT NULL AND image_url != '';

-- Note: If existing orgs should be considered 'active', an UPDATE statement would follow.
-- For example, to set all existing orgs that previously had image_url and theme_color to active:
-- UPDATE organizations SET status = 'active' 
-- WHERE image_url IS NOT NULL AND image_url != '' 
-- AND theme_color IS NOT NULL AND theme_color != '';
-- Or to set all current organizations to 'active' regardless:
-- UPDATE organizations SET status = 'active';
-- For this migration, we will assume new orgs start as 'pending_setup'
-- and existing orgs will be handled by a subsequent data migration if necessary. 