-- Remove post_tags that are not in the specified list
DELETE FROM "post_tags"
WHERE "id" NOT IN (
    -- Tag Group 1: Government News
    'a1a1a1a1-0001-4000-8000-000000000001', 
    'a1a1a1a1-1001-4000-8000-000000000001', 
    'a1a1a1a1-2001-4000-8000-000000000001',
    -- Tag Group 2: Event News
    'a1a1a1a1-0002-4000-8000-000000000002', 
    'a1a1a1a1-1002-4000-8000-000000000002', 
    'a1a1a1a1-2002-4000-8000-000000000002',
    -- Tag Group 3: National Education
    'a1a1a1a1-0003-4000-8000-000000000003', 
    'a1a1a1a1-1003-4000-8000-000000000003', 
    'a1a1a1a1-2003-4000-8000-000000000003',
    -- Tag Group 4: Office Information
    'a1a1a1a1-0004-4000-8000-000000000004', 
    'a1a1a1a1-1004-4000-8000-000000000004', 
    'a1a1a1a1-2004-4000-8000-000000000004'
); 