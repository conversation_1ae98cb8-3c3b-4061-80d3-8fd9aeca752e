BEGIN;

-- Target user emails for deletion
-- '<EMAIL>' (<PERSON><PERSON>)
-- '<EMAIL>' (Manager ShaTin<PERSON>i)
-- '<EMAIL>' (Admin ShaTinSi)
-- '<EMAIL>' (Admin BlueOrg)
-- '<EMAIL>' (Admin PinkOrg)

-- Target user phones for deletion (Regular Users)
-- '+85299991001'
-- '+85299991002'
-- '+85299991003'

-- Target organization names for deletion
-- '同行明天'
-- '沙田西關愛隊'
-- '藍色主題組織'
-- '玫紅色主題組織'

-- 1. Delete user organization memberships
-- First, remove memberships of the specific users being deleted
DELETE FROM user_organization_memberships
WHERE user_id IN (
    SELECT id FROM users WHERE email IN (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ) OR phone IN (
        '+85299991001',
        '+85299991002',
        '+85299991003'
    )
);

-- Second, remove all memberships associated with the organizations being deleted
-- (this covers members who are not themselves being deleted but are in these orgs)
DELETE FROM user_organization_memberships
WHERE organization_id IN (
    SELECT id FROM organizations WHERE name IN (
        '同行明天',
        '沙田西關愛隊',
        '藍色主題組織',
        '玫紅色主題組織'
    )
);

-- 2. Delete the organizations
-- It's generally safer to delete organizations before users if users are owners
-- and there's no ON DELETE SET NULL/CASCADE on owner_user_id.
DELETE FROM organizations
WHERE name IN (
    '同行明天',
    '沙田西關愛隊',
    '藍色主題組織',
    '玫紅色主題組織'
);

-- 3. Delete the users
-- Delete staff users by email
DELETE FROM users
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
);

-- Delete regular users by phone (matching criteria from 000034 for robustness,
-- as these users were intended to be deleted by that migration if not already)
DELETE FROM users
WHERE phone IN (
    '+85299991001',
    '+85299991002',
    '+85299991003'
) AND email IS NULL AND is_staff = false;

COMMIT; 