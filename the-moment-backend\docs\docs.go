// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://example.com/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.example.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/event-applications/pending-review": {
            "get": {
                "security": [
                    {
                        "BearerAuth // Placeholder for system admin auth": []
                    }
                ],
                "description": "Retrieves a paginated list of all 'pending' volunteer applications across the system. For System Admin use.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin VolunteerApplications"
                ],
                "summary": "List all pending volunteer applications (System Admin)",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Page number for pagination",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 20,
                        "description": "Number of items per page",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully retrieved applications",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/db.ListAllPendingReviewEventVolunteerApplicationsRow"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/organizations/{orgId}/event-volunteer-applications": {
            "get": {
                "description": "Retrieves all event volunteer applications for a specific organization, with optional status and pagination. Requires staff privileges with 'admin', 'owner', or 'manager' role within the organization.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)",
                    "Event Volunteering (Admin)"
                ],
                "summary": "List all event volunteer applications for an organization (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Filter by application status (e.g., 'pending', 'approved', 'rejected')",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of event volunteer applications",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventVolunteerApplicationResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of applications matching criteria"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format or invalid status value or pagination params",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized (admin token missing or invalid)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (admin not staff in organization or lacks required role)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/organizations/{orgId}/volunteer/applications": {
            "get": {
                "description": "Retrieves all volunteer applications for a specific organization, with optional status filter, limit, and offset. Requires staff privileges with 'admin', 'owner', or 'manager' role within the organization.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (Admin)",
                    "Organizations"
                ],
                "summary": "List all volunteer applications for an organization (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Filter by application status (e.g., 'pending', 'approved', 'rejected')",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of applications to return per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination (number of items to skip)",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Paginated list of volunteer applications",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format or invalid status value",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized (admin token missing or invalid)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (admin not staff in organization or lacks required role)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/organizations/{orgId}/volunteer/applications/pending": {
            "get": {
                "description": "Retrieves all volunteer applications with status 'pending' for a specific organization. Requires staff privileges with 'admin', 'owner', or 'manager' role within the organization.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (Admin)",
                    "Organizations"
                ],
                "summary": "List pending volunteer applications for an organization (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of pending applications with status 'pending'",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized (admin token missing or invalid)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (admin not staff in organization or lacks required role)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/organizations/{orgId}/volunteer/applications/{appId}": {
            "get": {
                "description": "Retrieves full details of a specific volunteer application, including user info and associated verification documents, for admin review. Requires staff privileges within the organization.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (Admin)",
                    "Organizations"
                ],
                "summary": "Get details of a volunteer application for admin review",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Application ID (UUID)",
                        "name": "appId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Application details with user info and verifications",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_services.EnhancedVolunteerApplicationDetails"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not staff in organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or Application not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/organizations/{orgId}/volunteer/applications/{appId}/review": {
            "patch": {
                "description": "Allows an admin to review a volunteer application, updating its status (e.g., to 'approved' or 'rejected') and optionally providing feedback. Uses the consolidated application_status_enum for status values.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (Admin)",
                    "Organizations"
                ],
                "summary": "Review a volunteer application (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Volunteer Application ID (UUID)",
                        "name": "appId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Review details (new status using application_status_enum, and optional admin_feedback)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminReviewVolunteerApplicationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Application reviewed and status updated",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request (e.g., invalid ID format, invalid status value from enum, bad request body)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized (admin token missing or invalid)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (admin not staff in organization or lacks required role, or trying to review an application not belonging to the org)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Application not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict (e.g., application is not in a reviewable state like 'pending', or invalid status transition)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves a paginated list of all users. Admin only. Can filter by is_staff status.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Users"
                ],
                "summary": "List all users (Admin)",
                "parameters": [
                    {
                        "type": "boolean",
                        "description": "Filter by staff status",
                        "name": "is_staff",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Page number for pagination (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Number of items per page (default: 10, max: 100)",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of users with pagination info",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedUsersResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid query parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized (JWT missing or invalid)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (User is not an admin)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Creates a new user with is_staff set to true. Admin only.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Users"
                ],
                "summary": "Create a new staff user (Admin)",
                "parameters": [
                    {
                        "description": "Details of the staff user to create",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateStaffUserRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Newly created staff user details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or validation error (e.g., email exists, password too short)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict (e.g., email already exists)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error (e.g., failed to hash password or save user)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/users/email/{email}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves a specific user by their email address. Admin only.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Users"
                ],
                "summary": "Get user by email (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User Email",
                        "name": "email",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid email format in path",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/users/phone/{phone}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves a specific user by their phone number. Admin only.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Users"
                ],
                "summary": "Get user by phone (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User Phone Number (ensure URL encoding if it contains special characters like +)",
                        "name": "phone",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid phone format in path",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/users/{userId}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves a specific user by their ID. Admin only.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Users"
                ],
                "summary": "Get user by ID (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID (UUID)",
                        "name": "userId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid user ID format in path",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/verifications": {
            "get": {
                "description": "Retrieves a paginated list of user verification requests with filtering capabilities for admins.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin - User Verification"
                ],
                "summary": "List user verification requests (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by User ID (UUID)",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by Event ID (UUID)",
                        "name": "event_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by verification type (e.g., hk_id_card, student_id)",
                        "name": "verification_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by status (e.g., pending, approved, rejected)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by Organization ID (UUID)",
                        "name": "org_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number for pagination",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of items per page",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Paginated list of verification requests",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedUserVerificationRequestsResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid query parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/verifications/": {
            "get": {
                "description": "Retrieves all verification requests. For testing purposes. Requires staff privileges.\nCan be filtered by status (pending, approved, rejected) and/or organization ID.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Verification"
                ],
                "summary": "List all verification requests regardless of status (Admin - Testing Only)",
                "parameters": [
                    {
                        "enum": [
                            "pending",
                            "approved",
                            "rejected"
                        ],
                        "type": "string",
                        "description": "Filter by verification status (e.g., pending, approved, rejected)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Filter by organization ID (UUID)",
                        "name": "org_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of all verification requests",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.UserVerificationRequest"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad request (e.g., invalid status or org_id)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not staff)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/verifications/documents/{docID}": {
            "get": {
                "description": "Allows staff to download any submitted verification document.",
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "Admin Verification"
                ],
                "summary": "Download any verification document (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Document ID",
                        "name": "docID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Verification document file",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not staff)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Document not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/verifications/pending": {
            "get": {
                "description": "Retrieves all verification requests with status 'pending'. Requires staff privileges.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Verification"
                ],
                "summary": "List pending verification requests (Admin)",
                "responses": {
                    "200": {
                        "description": "List of pending verification requests",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.UserVerificationRequest"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not staff)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/verifications/{reqID}": {
            "get": {
                "description": "Retrieves full details of a specific verification request, including user info. Requires staff privileges.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Verification"
                ],
                "summary": "Get full details of a verification request (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Verification Request ID",
                        "name": "reqID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Full verification request details",
                        "schema": {
                            "$ref": "#/definitions/db.UserVerificationRequest"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not staff)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Request not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/verifications/{reqID}/review": {
            "patch": {
                "description": "Allows staff to approve or reject a pending verification request.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Verification"
                ],
                "summary": "Approve or reject a verification request (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Verification Request ID",
                        "name": "reqID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Review action (approve/reject) and comments",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminReviewVerificationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated verification request",
                        "schema": {
                            "$ref": "#/definitions/db.UserVerificationRequest"
                        }
                    },
                    "400": {
                        "description": "Invalid request or action",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not staff)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Request not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/government-funding-types": {
            "get": {
                "description": "Retrieves a list of all possible government funding types supported by the system, with names localized by language code.\nIt uses the 'lang_code' query parameter. If 'lang_code' is provided and a localization exists, it's returned.\nIf the specific 'lang_code' localization doesn't exist for a type, it falls back to English ('en').\nIf neither the specified 'lang_code' nor English is available for a type, that type is omitted.\nIf 'lang_code' is not provided, it defaults to English ('en') for all types.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public",
                    "Verification",
                    "GovernmentFunding"
                ],
                "summary": "List all supported government funding types with localizations",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Language code (e.g., 'en', 'zh_HK') to filter types. Defaults to 'en' if omitted or if specific localization not found.",
                        "name": "lang_code",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully retrieved localized government funding types",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/internal_handlers.GovernmentFundingTypeResponse"
                            }
                        }
                    }
                }
            }
        },
        "/api/v1/verification-types": {
            "get": {
                "description": "Retrieves a list of all possible verification types supported by the system, with names localized by language code. If the 'lang_code' query parameter is provided, the list is filtered to include only types matching that language code. Otherwise, all verification types with their available localizations are returned.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public",
                    "Verification"
                ],
                "summary": "List all supported verification types with localizations",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Language code (e.g., 'en', 'zh_HK') to filter verification types. If omitted, all types with all localizations are returned.",
                        "name": "lang_code",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully retrieved localized verification types",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/internal_handlers.VerificationTypeResponse"
                            }
                        }
                    }
                }
            }
        },
        "/authn/logout": {
            "post": {
                "description": "Invalidates the user's current refresh token.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Logout user",
                "parameters": [
                    {
                        "description": "Refresh token to invalidate",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.LogoutRequestPayload"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Logout successful"
                    },
                    "400": {
                        "description": "Invalid request format or Invalid request data",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "An error occurred during logout.",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/authn/phone/check": {
            "post": {
                "description": "Checks if a given phone number is already registered in the system.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Check if phone number exists",
                "parameters": [
                    {
                        "description": "Phone number to check",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.CheckPhoneRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Existence status",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.CheckPhoneResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or Phone number is required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to check phone number",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/authn/phone/otp/initiate": {
            "post": {
                "description": "Sends an OTP to the user's phone for login. Requires PKCE parameters.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Initiate OTP for existing user login",
                "parameters": [
                    {
                        "description": "Phone number and PKCE details",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.InitiatePhoneOTPRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "State and message",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.InitiatePhoneOTPResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or missing required fields",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "User with this phone number not found. Please register first.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to check phone number or Failed to initiate login flow or Failed to send OTP",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/authn/phone/otp/verify": {
            "post": {
                "description": "Verifies the OTP and PKCE challenge for phone login and issues tokens.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Verify OTP for login",
                "parameters": [
                    {
                        "description": "State, OTP, and PKCE verifier",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.VerifyPhoneOTPRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Message, UserID, AccessToken, RefreshToken",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.VerifyPhoneOTPResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or missing required fields or Invalid PKCE code verifier or Invalid OTP",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "403": {
                        "description": "Account locked due to too many failed OTP attempts or Too many failed OTP attempts. Account locked for a short period.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "Invalid or expired login state",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve login flow or Login flow integrity error or Failed to process OTP attempts or Unsupported code challenge method or OTP verification failed or User account inconsistency or Failed to retrieve user details or Failed to generate access token or Failed to generate refresh token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/authn/register/phone/initiate": {
            "post": {
                "description": "Starts the registration process for a new user with phone OTP and PKCE.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Initiate OTP for new user registration",
                "parameters": [
                    {
                        "description": "Phone number, client details, PKCE parameters, and OTP channel preference",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.InitiatePhoneRegistrationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "State, message and FlowID",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.InitiatePhoneRegistrationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or missing required fields",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to initiate registration flow or Failed to send OTP",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/authn/register/phone/verify": {
            "post": {
                "description": "Verifies OTP and PKCE, creates the user, and issues tokens.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Verify OTP for registration",
                "parameters": [
                    {
                        "description": "State, OTP, code verifier and user details",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.VerifyPhoneRegistrationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Message, UserID, AccessToken, RefreshToken",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.VerifyPhoneRegistrationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or missing required fields or Invalid PKCE code verifier or Invalid OTP",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "403": {
                        "description": "Account locked due to too many failed OTP attempts or Too many failed OTP attempts. Account locked for a short period.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "Invalid or expired registration state",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve registration flow or Registration flow integrity error or Failed to process OTP attempts or Unsupported code challenge method or OTP verification failed or Failed to create user account or Failed to generate access token or Failed to generate refresh token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/authn/staff/email/check": {
            "post": {
                "description": "Checks if a given email is registered for a staff member.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication",
                    "Staff"
                ],
                "summary": "Check if staff email exists",
                "parameters": [
                    {
                        "description": "Email to check",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.CheckStaffEmailRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Existence status and staff status",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.CheckStaffEmailResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or Email is required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to check staff email",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/authn/staff/login/initiate": {
            "post": {
                "description": "Initiates the login process for a staff member using email and password (first step).",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication",
                    "Staff"
                ],
                "summary": "Initiate staff login",
                "parameters": [
                    {
                        "description": "Staff email",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.InitiateStaffLoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Login initiated, includes state for verification step",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.InitiateStaffLoginResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or Email is required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "User is not authorized as staff or Account is locked",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "Staff user not found with this email",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to initiate staff login or Failed to process login attempts",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/authn/staff/login/verify": {
            "post": {
                "description": "Verifies the staff member's password against the initiated login flow and issues tokens.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication",
                    "Staff"
                ],
                "summary": "Verify staff login",
                "parameters": [
                    {
                        "description": "State from initiation and password",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.VerifyStaffLoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Login successful, includes access and refresh tokens",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.VerifyStaffLoginResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload, Missing state or password, or Invalid password",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "Account locked due to too many failed login attempts",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "Invalid or expired login state",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve login flow, Login flow integrity error, Failed to process login attempts, User account inconsistency, Failed to retrieve user details, Failed to generate access token, or Failed to generate refresh token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/authn/token/refresh": {
            "post": {
                "description": "Issues new access and refresh tokens using a valid refresh token.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Refresh JWT tokens",
                "parameters": [
                    {
                        "description": "Refresh token",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_authn.RefreshTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "New access token, new refresh token, and token type",
                        "schema": {
                            "$ref": "#/definitions/internal_authn.RefreshTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or Refresh token is required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "Invalid or expired refresh token or Refresh token expired",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Could not process token due to server configuration error or Failed to process refresh token or Failed to generate new tokens",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/event-registrations": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Registers the authenticated user for a specific event. Handles capacity, waitlist, eligibility, and time conflicts.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Registrations"
                ],
                "summary": "Register current user for an event",
                "parameters": [
                    {
                        "description": "Event ID to register for, e.g. {\\",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.RegisterForEventRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Registration successful (or waitlisted)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request, event full, not eligible, or time conflict",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/event-registrations/check-in": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "A volunteer or staff member (scanner) checks in a participant for an eligible event. The system identifies the relevant event for the participant (e.g., an active, published event they are registered for).\nThe scanner's user_id and staff status are taken from their JWT.\nThe participant's user_id is provided in the request body.\nAuthorization for Scanners:\n- Staff Scanners: Must be a member of the organization hosting the event with an appropriate role.\n- Volunteer Scanners: Must satisfy two conditions:\n1. Have an 'approved' ` + "`" + `user_volunteer_applications` + "`" + ` record for the organization hosting the event.\n2. Have an 'approved' ` + "`" + `event_volunteer_applications` + "`" + ` record for the specific event they are scanning for.\n(Successfully scanning also marks the volunteer as attended for that event).\nIMPORTANT NOTE: Currently, the API to create and approve ` + "`" + `event_volunteer_applications` + "`" + ` (event-specific volunteer roles) is not yet implemented. Therefore, volunteer scanning will result in a 403 error until this functionality is added.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Registrations"
                ],
                "summary": "Check in a participant for an event by a scanner (volunteer/staff)",
                "parameters": [
                    {
                        "description": "Participant's User ID to check in",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventCheckInRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Participant's registration details on successful check-in",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request (e.g., missing or invalid participant_user_id, malformed body)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized (scanner token invalid or missing)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (scanner not authorized: e.g., volunteer lacks approved org-level application, lacks approved event-specific application, or staff not in org/lacks role. See API description for details on volunteer authorization.)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found (e.g., participant not registered for any eligible event, or the specified participant user does not exist)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict (e.g., participant already checked in to the event)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/event-registrations/me": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Returns paginated list of the user's event registrations with extensive filtering options.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Registrations",
                    "User Profile"
                ],
                "summary": "List all event registrations for the current user",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by event start date (ISO 8601)",
                        "name": "start_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by event end date (ISO 8601)",
                        "name": "end_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by event status (published, archived, deleted, draft, hidden, cancelled)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Sort order (date_asc, date_desc)",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by user's role in event (attendee, volunteer)",
                        "name": "role",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by organization UUID",
                        "name": "organization_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of user's event registrations",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of registrations"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/event-registrations/{registrationId}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves details of a specific event registration owned by the user.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Registrations"
                ],
                "summary": "Get details of a specific registration",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Registration ID (UUID)",
                        "name": "registrationId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Details of the event registration",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not owner of registration)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Registration not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Allows the authenticated user to cancel their registration for an event.",
                "tags": [
                    "Event Registrations"
                ],
                "summary": "Cancel user's own registration",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Registration ID (UUID)",
                        "name": "registrationId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated registration details (status: cancelled)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not owner of registration)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Registration not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/event-registrations/{registrationId}/payment-status": {
            "patch": {
                "description": "Updates the payment status of a registration. This is a public endpoint potentially used by payment gateways or staff with a specific staff_id.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Registrations"
                ],
                "summary": "Update payment status for a specific registration (Public)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Registration ID (UUID)",
                        "name": "registrationId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "New payment status and staff ID, e.g. {\\",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateEventRegistrationPaymentRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated registration details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Registration not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/event-statistics": {
            "get": {
                "description": "Retrieves statistics about events, including counts per category (tag) and top N events by participant count.\nSupports optional startDate, endDate (RFC3339 format, e.g., \"2023-01-01T00:00:00Z\") and limit query parameters.\nDefaults to the last 6 months and top 10 events if parameters are not provided.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Statistics)"
                ],
                "summary": "Get event statistics",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Start date for filtering statistics (RFC3339 format)",
                        "name": "startDate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "End date for filtering statistics (RFC3339 format)",
                        "name": "endDate",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of top events to return",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Event statistics",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventStatisticsResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid query parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/event-tags": {
            "get": {
                "description": "Retrieves a list of event tags, potentially filtered by language code and approval status. Supports pagination.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Tags",
                    "Events (Public)"
                ],
                "summary": "List all available event tags",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by language code (e.g., 'en', 'zh_HK', 'zh_CN')",
                        "name": "langCode",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Filter by approval status (true or false)",
                        "name": "approved",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Limit number of results per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of event tags",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.EventTag"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid query parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/events": {
            "get": {
                "description": "Lists public events. Supports filtering by orgId, startDate, endDate, searchTerm, tagIds, status. Supports sorting by sortBy. Supports pagination.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Public)"
                ],
                "summary": "List public events",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by Organization UUID",
                        "name": "orgId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by start date (YYYY-MM-DDTHH:MM:SSZ)",
                        "name": "startDate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by end date (YYYY-MM-DDTHH:MM:SSZ)",
                        "name": "endDate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search term for title and location_full_address",
                        "name": "searchTerm",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Comma-separated list of tag IDs",
                        "name": "tagIds",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by status (e.g., published, archived, deleted, draft, hidden, cancelled)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by a specific government funding type key",
                        "name": "government_funding_type_key",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by a specific event verification type key",
                        "name": "event_verification_type_key",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Sort order (e.g., popularity_desc, start_time_asc)",
                        "name": "sortBy",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of public events",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PublicEventResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of events"
                            }
                        }
                    }
                }
            }
        },
        "/events-tags": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Allows authorized users to add new event tags to the system.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Tags",
                    "Admin"
                ],
                "summary": "Create a new event tag (admin/staff)",
                "parameters": [
                    {
                        "description": "Event tag details",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateEventTagRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Newly created event tag",
                        "schema": {
                            "$ref": "#/definitions/db.EventTag"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or validation failed",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/staff - check performed by middleware/route setup)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict - Tag already exists",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/events/{eventId}": {
            "get": {
                "description": "Retrieves public-facing details for a given event ID.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Public)"
                ],
                "summary": "Get public details of a specific event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Public event details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PublicEventResponse"
                        }
                    },
                    "404": {
                        "description": "Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/events/{event_id}/event-applications/pending-review": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves a paginated list of volunteer applications with 'pending' status for a specific event.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events VolunteerApplications"
                ],
                "summary": "List pending volunteer applications for an event",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Event ID (UUID format)",
                        "name": "event_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Page number for pagination",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 20,
                        "description": "Number of items per page",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully retrieved applications",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/db.ListPendingReviewEventVolunteerApplicationsForEventRow"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters (e.g., invalid UUID, non-positive page/page_size)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "Checks the health of the API.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "Health check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/organizations": {
            "get": {
                "description": "Lists all organizations. Visibility may depend on user role. Supports pagination. Response items include image_url, theme_color, and status.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "List all organizations",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of organizations",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of organizations"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized\" // If auth is strictly required",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Creates a new organization. The authenticated user becomes the owner. Name is required. Description, image_url, theme_color are optional. Status defaults to 'pending_setup'.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Create a new organization",
                "parameters": [
                    {
                        "description": "Organization details (name required, description, image_url, theme_color optional)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateOrganizationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Newly created organization (includes image_url, theme_color, status)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/debugjwt": {
            "get": {
                "description": "A temporary handler to debug JWT claims on organization routes.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Debug",
                    "Organizations"
                ],
                "summary": "Temporary JWT Debug Handler for Organizations",
                "responses": {
                    "200": {
                        "description": "UserID and debug message",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Error processing claims or token not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}": {
            "get": {
                "description": "Retrieves details for a given organization ID. Response includes image_url, theme_color, and status.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Get details of a specific organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Organization details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (if user not member and it's private)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "Updates details of an organization. Requires admin/owner role in that org. All fields in body are optional (name, description, image_url, theme_color, status).",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Update an organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Fields to update (name, description, image_url, theme_color, status - all optional)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateOrganizationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated organization details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/owner)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "Deletes an organization. Requires owner role in that org.",
                "tags": [
                    "Organizations"
                ],
                "summary": "Delete an organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Cannot delete default organization or invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not owner)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/event-registrations": {
            "get": {
                "description": "Admin view of all registrations for events within the organization, with filtering options. Supports pagination. Requires organization admin/manager permission.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Registrations (Organization Admin)"
                ],
                "summary": "List event registrations for an organization (Admin View)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by specific Event ID (UUID)",
                        "name": "event_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by event start date range (ISO 8601)",
                        "name": "start_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by event end date range (ISO 8601)",
                        "name": "end_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by registration status (e.g., confirmed, pending_approval, cancelled)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by specific User ID (UUID)",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search by user name",
                        "name": "search_name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by payment status (e.g., paid, unpaid, pending)",
                        "name": "payment_status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of event registrations for the organization",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of registrations"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin for this organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/event-registrations/{registrationId}/status": {
            "patch": {
                "description": "Admin action to update a specific registration's status (e.g., approve, reject, mark attendance). Requires organization admin/manager permission.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Event Registrations (Organization Admin)"
                ],
                "summary": "Update registration status (Admin Action)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Registration ID (UUID)",
                        "name": "registrationId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "New status and admin notes, e.g. {\\",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateOrgEventRegistrationStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated registration details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request or status transition",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin for this organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or Registration not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/event-statistics": {
            "get": {
                "description": "Retrieves statistics about events for a specific organization, including counts per category (tag) and top N events by participant count.\nSupports optional startDate, endDate (RFC3339 format, e.g., \"2023-01-01T00:00:00Z\") and limit query parameters.\nDefaults to the last 6 months and top 10 events if parameters are not provided.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization Statistics)"
                ],
                "summary": "Get event statistics for a specific organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Start date for filtering statistics (RFC3339 format)",
                        "name": "startDate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "End date for filtering statistics (RFC3339 format)",
                        "name": "endDate",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of top events to return",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Event statistics for the organization",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventStatisticsResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid path or query parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (user does not have permission to view stats for this organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events": {
            "get": {
                "description": "Lists events for an organization. Supports pagination and potential admin-only filters.\nSupports filtering by startDate, endDate, searchTerm, tagIds, status, government_funding_type_key, event_verification_type_key.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "List events managed by the organization (Admin View)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Filter by start date (YYYY-MM-DDTHH:MM:SSZ)",
                        "name": "startDate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by end date (YYYY-MM-DDTHH:MM:SSZ)",
                        "name": "endDate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search term for title and location_full_address",
                        "name": "searchTerm",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Comma-separated list of tag IDs",
                        "name": "tagIds",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by status (e.g., published, archived, deleted, draft, hidden, cancelled)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by government funding type key",
                        "name": "government_funding_type_key",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by event verification type key",
                        "name": "event_verification_type_key",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "default": false,
                        "description": "Flag for admin view to include more details",
                        "name": "isAdminView",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of events",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of events"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Creates an event. Payload includes details like title, JsonContent, location_type, location_full_address, location_online_url, start_time, end_time, price, participant_limit, waitlist_limit, requires_approval_for_registration, tag_ids, required_verification_type_keys, contact_email, contact_phone, mediaeventitem, government_funding_keys.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "Create a new event within an organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Event details",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateEventRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Newly created event",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (no permission in org)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/public": {
            "get": {
                "description": "Lists public events for a specific organization. Supports filtering by startDate, endDate, searchTerm, tagIds, status, government_funding_type_key, event_verification_type_key. Supports pagination.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Public)"
                ],
                "summary": "List public events for a specific organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Filter by start date (YYYY-MM-DDTHH:MM:SSZ)",
                        "name": "startDate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by end date (YYYY-MM-DDTHH:MM:SSZ)",
                        "name": "endDate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search term for title and location_full_address",
                        "name": "searchTerm",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Comma-separated list of tag IDs",
                        "name": "tagIds",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by status (e.g., published, archived, deleted, draft, hidden, cancelled)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by government funding type key",
                        "name": "government_funding_type_key",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by event verification type key",
                        "name": "event_verification_type_key",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of public events for the organization",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PublicEventResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of events"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}": {
            "get": {
                "description": "Retrieves detailed admin view of an event, including counts, tags, required verifications, and media items.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "Get detailed information about a specific event (Admin View)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Event details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "Updates an event. Payload can include title, JsonContent, location_type, location_full_address, location_online_url, start_time, end_time, price, participant_limit, waitlist_limit, requires_approval_for_registration, tag_ids, required_verification_type_keys, contact_email, contact_phone, mediaeventitem, government_funding_keys, status, published_at.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "Update details of a specific event (Admin)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID) to update",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Event details to update. Only provide fields that need to be changed.",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateEventRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated event details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request (e.g., bad format, validation error)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (user does not have permission to update events in this organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Event or Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "Deletes a specific event within an organization.",
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "Delete an event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/media": {
            "get": {
                "description": "Retrieves the list of media items associated with a specific event.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)",
                    "Event Media Management"
                ],
                "summary": "List media for an event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of media items",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized\" // Assuming auth is required",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden\" // Assuming auth is required",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Uploads image, video, or PDF for a specific post.",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)",
                    "Event Media Management"
                ],
                "summary": "Upload media for a post",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "Media file (image, video, or PDF)",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Uploaded post media item",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request or file type/size",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/manager for organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/media/{itemId}": {
            "delete": {
                "responses": {}
            }
        },
        "/organizations/{orgId}/events/{eventId}/registrations": {
            "get": {
                "description": "Admin view of registrations for a specific event within the organization, with filtering options. Supports pagination.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)",
                    "Event Registrations (Organization Admin)"
                ],
                "summary": "List registrations for a specific event (Admin View)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by registration status (e.g., confirmed, pending_approval, cancelled)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by specific User ID (UUID)",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search by user name",
                        "name": "search_name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by payment status (e.g., paid, unpaid, pending)",
                        "name": "payment_status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of event registrations",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of registrations"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/manager for organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/registrations/{registrationId}/payment": {
            "patch": {
                "description": "Admin action to update the payment status of a registration for an event.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)",
                    "Event Registrations (Organization Admin)"
                ],
                "summary": "Update payment status for a registration (Admin Action)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Registration ID (UUID)",
                        "name": "registrationId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "New payment status and staff ID, e.g. {\\",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateEventRegistrationPaymentRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated registration details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/manager for organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, Event, or Registration not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/registrations/{registrationId}/status": {
            "patch": {
                "description": "Admin action to update a specific registration's status (e.g., approve, reject, mark attendance) for an event.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)",
                    "Event Registrations (Organization Admin)"
                ],
                "summary": "Update registration status (Admin Action)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Registration ID (UUID)",
                        "name": "registrationId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "New status and admin notes, e.g. {\\",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateOrgEventRegistrationStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated registration details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request or status transition",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/manager for organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, Event, or Registration not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/tags": {
            "get": {
                "description": "Retrieves the list of tags associated with a specific event.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "List tags for an event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of event tags\" // Assuming payloads.TagResponse is the correct type",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.TagResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized\" // Assuming this endpoint requires auth based on context",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden\" // Assuming this endpoint requires auth based on context",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/tags/{tagId}": {
            "post": {
                "description": "Adds a tag to a specific event within an organization. Requires organization admin/event creator permission.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "Add tag to event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Tag ID (UUID)",
                        "name": "tagId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, Event, or Tag not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "Removes a tag from a specific event within an organization. Requires organization admin/event creator permission.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "Remove tag from event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Tag ID (UUID)",
                        "name": "tagId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, Event, or Tag not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/verification-types": {
            "get": {
                "description": "Retrieves the list of required verification types associated with a specific event.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "List required verification types for an event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of required verification type keys\" // Assuming string is the correct type for keys",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized\" // Assuming this endpoint requires auth",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden\" // Assuming this endpoint requires auth",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/verification-types/{typeKey}": {
            "post": {
                "description": "Adds a required verification type to a specific event. Requires organization admin/event creator permission.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "Add required verification type to event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Verification Type Key (e.g., hk_id)",
                        "name": "typeKey",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, Event, or Verification Type not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "Removes a required verification type from a specific event. Requires organization admin/event creator permission.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)"
                ],
                "summary": "Remove required verification type from event",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Verification Type Key (e.g., hk_id)",
                        "name": "typeKey",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, Event, or Verification Type not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/volunteer-applications": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Admin view of volunteer applications for a specific event within the organization. Supports pagination.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)",
                    "Event Volunteering (Admin)"
                ],
                "summary": "List volunteer applications for an event (Admin View)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of volunteer applications",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventVolunteerApplicationResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of applications"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/manager for organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or Event not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/events/{eventId}/volunteer-applications/{applicationId}/review": {
            "patch": {
                "description": "Admin action to review (approve or reject) a specific volunteer application for an event.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Events (Organization)",
                    "Event Volunteering (Admin)"
                ],
                "summary": "Review a volunteer application (Admin Action)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Event ID (UUID)",
                        "name": "eventId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Volunteer Application ID (UUID)",
                        "name": "applicationId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Review details (status, notes)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ReviewVolunteerApplicationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated volunteer application details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventVolunteerApplicationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request or review action",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/manager for organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, Event, or Application not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/files": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Lists files and folders within an organization's storage. Use the ` + "`" + `parent_folder_id` + "`" + ` query parameter to list contents of a specific subfolder. Omitting ` + "`" + `parent_folder_id` + "`" + ` lists the root contents. Requires admin/staff privileges.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Organization Files - Admin)"
                ],
                "summary": "List files and folders for an organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Parent Folder ID (UUID) to list contents of (optional, defaults to root)",
                        "name": "parent_folder_id",
                        "in": "query"
                    },
                    {
                        "maximum": 200,
                        "minimum": 1,
                        "type": "integer",
                        "default": 50,
                        "description": "Limit number of results per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of files and folders in the specified location",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of items in the specified location"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid ID format or query parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Parent folder not found (if specified)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve file list",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/files/folder": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Creates a new folder. A parent_folder_id can be provided to create a subfolder. Requires admin/staff privileges.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Organization Files - Admin)"
                ],
                "summary": "Create a new folder within an organization's file storage",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID) where the folder will be created",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Folder name and optional parent_folder_id",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateOrganizationFolderRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Details of the newly created folder",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or organization/parent_folder ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Parent folder not found (if specified)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "A folder or file with the same name already exists in the target location",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to create folder",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/files/upload": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Uploads a file to the organization's file storage. Can specify a parent_folder_id to upload into a specific folder, otherwise uploads to the root. Requires admin/staff privileges.",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Organization Files - Admin)"
                ],
                "summary": "Upload a file directly to an organization's folder",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "File to upload",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Parent Folder ID (UUID) to upload into (optional, defaults to root)",
                        "name": "parent_folder_id",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Details of the uploaded organization file",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid ID format, file upload error, or file type/size issue",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Parent folder not found (if specified)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "File name conflict in the target folder",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to process or save uploaded file",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/files/{fileId}/download": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Downloads a specific file directly using its ID from the organization's file storage. Requires admin/staff privileges.",
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "Content Management (Organization Files - Admin)"
                ],
                "summary": "Download a file directly from organization storage",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File ID (UUID) to download",
                        "name": "fileId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "The requested organization file",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "400": {
                        "description": "Invalid ID format or trying to download a folder",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "File not found or does not belong to this organization",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Error preparing file for download",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/files/{fileOrFolderId}": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Updates the name of a file/folder or moves it to a different parent folder. Provide ` + "`" + `name` + "`" + ` to rename, ` + "`" + `parent_folder_id` + "`" + ` to move. Both can be provided. Requires admin/staff privileges.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Organization Files - Admin)"
                ],
                "summary": "Rename a file or folder, or move it",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File or Folder ID (UUID) to update",
                        "name": "fileOrFolderId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "New name and/or new parent_folder_id",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateOrganizationFileRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Details of the updated file or folder",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or ID format, or invalid operation (e.g., cyclic move)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Operation not allowed (e.g., moving root)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "File/folder or target parent folder not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Name conflict in the target location",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to update file/folder",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Deletes a file or folder from the organization's storage. If a folder is deleted, its contents are deleted recursively. Requires admin/staff privileges.",
                "tags": [
                    "Content Management (Organization Files - Admin)"
                ],
                "summary": "Delete a file or folder (recursively if folder)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File or Folder ID (UUID) to delete",
                        "name": "fileOrFolderId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "File/folder not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to delete file/folder",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/join": {
            "post": {
                "description": "Allows the authenticated user to join a specific organization as a member.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Join an organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully joined organization\" // Or return membership details",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Already a member or invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/leave": {
            "delete": {
                "description": "Allows the authenticated user to leave a specific organization.",
                "tags": [
                    "Organizations"
                ],
                "summary": "Leave an organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Not a member or invalid request",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/logo": {
            "post": {
                "description": "Uploads a logo for the organization. This action updates the organization's ` + "`" + `image_url` + "`" + ` and sets its ` + "`" + `status` + "`" + ` to ` + "`" + `active` + "`" + `. Requires admin/owner permission.",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Upload organization logo",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "Logo image file",
                        "name": "logo",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated organization with new logo URL and active status",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request or file",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not admin/owner)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/posts": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Retrieves a list of posts belonging to a specific organization. Includes drafts and hidden posts. Supports pagination and filtering by tag IDs.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Posts - Admin)"
                ],
                "summary": "List posts for an organization (admin view, includes drafts)",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Comma-separated list of Tag IDs (UUIDs) to filter by",
                        "name": "tag_ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of posts belonging to the organization",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of posts matching the query"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format or query parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve posts",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Author ID is automatically set from the authenticated user's token. The status of the post can be 'draft', 'published', or 'hidden'. If media items are included and the first one is an image, its URL might be used as the post's cover_image_url by the service.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Posts - Admin)"
                ],
                "summary": "Create a new post within an organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Post content, status, tag IDs, and optional media items",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreatePostRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Newly created post with media items and cover image URL",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or organization ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - User ID not found in token",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to create post or retrieve full details after creation",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/posts/{postId}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Retrieves full details of a specific post belonging to an organization, including associated media items. Does not filter by status (shows drafts, hidden, etc.).",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Posts - Admin)"
                ],
                "summary": "Get details of a specific post (admin view, includes media)",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Post ID (UUID)",
                        "name": "postId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Post details with media items",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid post ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Post not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve post",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Updates the content, status, tags, or other attributes of an existing post. Requires user to be the author or have appropriate permissions.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Posts - Admin)"
                ],
                "summary": "Update an existing post",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Post ID (UUID)",
                        "name": "postId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Post content, status, tag IDs, etc. to update",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdatePostRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated post details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - User cannot update this post",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Post not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to update post or retrieve details after update",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Deletes a specific post. Requires user to be the author or have appropriate permissions.",
                "tags": [
                    "Content Management (Posts - Admin)"
                ],
                "summary": "Delete a post",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Post ID (UUID)",
                        "name": "postId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid post ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - User cannot delete this post",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Post not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to delete post",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/posts/{postId}/media": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Uploads an image, video, or PDF file and associates it with a specific post. Requires appropriate permissions.",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Posts - Admin)"
                ],
                "summary": "Upload media for a post",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Post ID (UUID)",
                        "name": "postId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "Media file to upload",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Details of the uploaded media item",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostMediaItemResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid post ID format, file upload error, or file type/size issue",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Post not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to process or save uploaded media",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/posts/{postId}/media/{mediaId}": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Deletes a specific media item associated with a post. Requires appropriate permissions.",
                "tags": [
                    "Content Management (Posts - Admin)"
                ],
                "summary": "Delete media from a post",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Post ID (UUID)",
                        "name": "postId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Media Item ID (UUID)",
                        "name": "mediaId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid media ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Media item not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to delete media item",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/resources": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Retrieves a list of resources belonging to a specific organization, including drafts and hidden ones. Supports pagination.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Resources - Admin)"
                ],
                "summary": "List resources for an organization (admin view, includes drafts)",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of resources belonging to the organization",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of resources matching the query"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format or query parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve resources",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Creates a new resource, which is a collection of downloadable files. Requires admin/staff privileges for the organization.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Resources - Admin)"
                ],
                "summary": "Create a new resource entry",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID) to associate the resource with",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Details of the resource to create (title, description, visibility, status, etc.)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateResourceRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Newly created resource with its files (if any were included in request, though typically files are added separately)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or organization ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to create resource or retrieve details after creation",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/resources/{resourceIdOrSlug}/public": {
            "get": {
                "description": "Retrieves the public details of a specific published resource, identified either by its UUID or its slug within the context of its organization.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Resources - Public)"
                ],
                "summary": "Get public details of a specific published resource by ID or slug",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID) (needed for slug lookup context)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Resource ID (UUID) or Slug",
                        "name": "resourceIdOrSlug",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Public resource details with associated files",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Resource not found, not published, or not publicly visible in this organization context",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve resource",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/resources/{resourceId}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Retrieves full details of a specific resource, including its associated files. Does not filter by status.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Resources - Admin)"
                ],
                "summary": "Get details of a specific resource (admin view)",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Resource ID (UUID)",
                        "name": "resourceId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Resource details with associated files",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid resource ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Resource not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve resource",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Updates an existing resource's details (title, description, visibility, status, etc.). Requires admin/staff privileges.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Resources - Admin)"
                ],
                "summary": "Update a resource entry",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Resource ID (UUID) of the resource to update",
                        "name": "resourceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Resource details to update",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateResourceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated resource details with files",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or resource ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Resource not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to update resource or retrieve details after update",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Deletes a resource and its associated files. Requires admin/staff privileges.",
                "tags": [
                    "Content Management (Resources - Admin)"
                ],
                "summary": "Delete a resource entry",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Resource ID (UUID) of the resource to delete",
                        "name": "resourceId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid resource ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Resource not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to delete resource",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/resources/{resourceId}/files": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Uploads a file and associates it with a specific resource. Requires admin/staff privileges.",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Resources - Admin)"
                ],
                "summary": "Upload a file to a resource",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Resource ID (UUID) to attach the file to",
                        "name": "resourceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "The file to upload",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Optional description for the file",
                        "name": "description",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Details of the uploaded file",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceFileResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input (e.g., missing file, resource ID format, file too large)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Resource not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to upload file or save its details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/resources/{resourceId}/files/{fileId}": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Deletes a specific file associated with a resource. Requires admin/staff privileges.",
                "tags": [
                    "Content Management (Resources - Admin)"
                ],
                "summary": "Delete a file from a resource",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Resource ID (UUID)",
                        "name": "resourceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File ID (UUID) of the file to delete",
                        "name": "fileId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid file ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "File not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to delete resource file",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/volunteer/apply": {
            "post": {
                "description": "Allows the authenticated user to submit an application to become a volunteer for an organization. This creates a user_volunteer_applications record with status 'pending'.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (User)",
                    "Organizations"
                ],
                "summary": "Apply for volunteer status in a specific organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Application details (e.g., motivation letter)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ApplyVolunteerRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Application submitted successfully (includes application_id). Status is 'pending'.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request (e.g., invalid orgId format, bad request body)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (e.g., user is not a member of the organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict (user already has an 'approved' application, or another 'pending' application exists for this organization)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{orgId}/volunteer/status": {
            "get": {
                "description": "Retrieves the user's volunteer application for the specified organization if its status is 'approved'. This indicates the user is qualified to volunteer for that organization.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (User)",
                    "Organizations"
                ],
                "summary": "Check volunteer qualification status for the user in a specific organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID)",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Approved volunteer application. A 404 is returned if no 'approved' application exists.",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "No approved volunteer application (qualification) found for this user in this organization.",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/event-applications/pending-review": {
            "get": {
                "security": [
                    {
                        "BearerAuth // Placeholder for org admin/manager auth": []
                    }
                ],
                "description": "Retrieves a paginated list of 'pending' volunteer applications for all events within a given organization.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations VolunteerApplications"
                ],
                "summary": "List pending volunteer applications for an organization",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID (UUID format)",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Page number for pagination",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 20,
                        "description": "Number of items per page",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully retrieved applications",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/db.ListPendingReviewEventVolunteerApplicationsForOrganizationRow"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found or no pending applications",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/posts": {
            "get": {
                "description": "Retrieves a list of all publicly visible posts across all organizations. Supports pagination and filtering by organization ID and tag IDs. The cover image URL is derived from the first media item if available.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Posts - Public)"
                ],
                "summary": "List all published posts",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Filter by Organization ID (UUID)",
                        "name": "org_id",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Comma-separated list of Tag IDs (UUIDs) to filter by",
                        "name": "tag_ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of published posts",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of published posts matching the query"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid query parameter format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve published posts",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/posts/{postIdOrSlug}": {
            "get": {
                "description": "Retrieves details of a specific publicly visible post. The post can be identified by its UUID or its unique slug. The cover image URL is derived from the first media item if available.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Posts - Public)"
                ],
                "summary": "Get details of a specific published post by ID or slug",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Post ID (UUID) or Slug",
                        "name": "postIdOrSlug",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Published post details with media items",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse"
                        }
                    },
                    "404": {
                        "description": "Post not found or not published",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve post",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/posts/{postId}/tags": {
            "get": {
                "description": "Retrieves a list of all tags associated with a given post UUID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Posts",
                    "Post Tags"
                ],
                "summary": "Get all tags for a specific post",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Post ID (UUID)",
                        "name": "postId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagsListResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid post ID format",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "500": {
                        "description": "Internal server error / Failed to get tags for post",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    }
                }
            }
        },
        "/posts/{postId}/tags/{tagId}": {
            "post": {
                "description": "Associates an existing post tag with an existing post.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Posts",
                    "Post Tags"
                ],
                "summary": "Add a tag to a post",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Post ID (UUID)",
                        "name": "postId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Post Tag ID (UUID)",
                        "name": "tagId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid post ID or tag ID format",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "500": {
                        "description": "Internal server error / Failed to add tag to post",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    }
                }
            },
            "delete": {
                "description": "Disassociates a post tag from a post.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Posts",
                    "Post Tags"
                ],
                "summary": "Remove a tag from a post",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Post ID (UUID)",
                        "name": "postId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Post Tag ID (UUID)",
                        "name": "tagId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid post ID or tag ID format",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "500": {
                        "description": "Internal server error / Failed to remove tag from post",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    }
                }
            }
        },
        "/resources": {
            "get": {
                "description": "Retrieves a list of all publicly visible resources across all organizations. Supports pagination and filtering by organization ID and visibility.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Content Management (Resources - Public)"
                ],
                "summary": "List all published resources",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Filter by Organization ID (UUID)",
                        "name": "organizationId",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "public",
                            "org_only"
                        ],
                        "type": "string",
                        "description": "Filter by visibility (e.g., public, org_only)",
                        "name": "visibility",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "description": "Limit number of results per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "default": 0,
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of published resources",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "string",
                                "description": "Total number of published resources matching the query"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid query parameter format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve published resources",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/resources/download/{orgId}/{resourceIdOrSlug}/{fileIdOrName}": {
            "get": {
                "description": "Downloads a specific file associated with a public resource. Access checks are performed based on resource visibility before serving the file.",
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "Content Management (Resources - Public)"
                ],
                "summary": "Download a specific file from a resource (public)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID (UUID) of the resource's organization",
                        "name": "orgId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Resource ID (UUID) or Slug",
                        "name": "resourceIdOrSlug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File ID (UUID) or exact File Name within the resource",
                        "name": "fileIdOrName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "The requested resource file",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "400": {
                        "description": "Invalid ID format in path",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Resource or File not found, or not publicly accessible",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Error preparing file for download",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/tags/posts": {
            "get": {
                "description": "Retrieves a list of all available post tags, ordered by tag name and language code.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Post Tags"
                ],
                "summary": "List all post tags",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagsListResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    }
                }
            },
            "post": {
                "description": "Creates a new post tag with name, language, and optional description.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Post Tags"
                ],
                "summary": "Create a new post tag",
                "parameters": [
                    {
                        "description": "Post Tag Create Payload",
                        "name": "tag",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreatePostTagRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation error",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    }
                }
            }
        },
        "/tags/posts/{tagId}": {
            "get": {
                "description": "Retrieves details of a post tag using its UUID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Post Tags"
                ],
                "summary": "Get a specific post tag by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Post Tag ID (UUID)",
                        "name": "tagId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid tag ID format",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "404": {
                        "description": "Tag not found",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    }
                }
            },
            "put": {
                "description": "Updates an existing post tag's name, language, or description.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Post Tags"
                ],
                "summary": "Update an existing post tag",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Post Tag ID (UUID)",
                        "name": "tagId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Post Tag Update Payload",
                        "name": "tag",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdatePostTagRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation error / Invalid tag ID",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "404": {
                        "description": "Tag not found",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    }
                }
            },
            "delete": {
                "description": "Deletes a post tag by its UUID. Also removes associations from posts.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Post Tags"
                ],
                "summary": "Delete a post tag",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Post Tag ID (UUID)",
                        "name": "tagId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Invalid tag ID format",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    },
                    "500": {
                        "description": "Internal server error / Failed to delete tag",
                        "schema": {
                            "$ref": "#/definitions/echo.Map"
                        }
                    }
                }
            }
        },
        "/users/me": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Fetches the profile information of the authenticated user.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Profile"
                ],
                "summary": "Retrieve current user's profile",
                "responses": {
                    "200": {
                        "description": "User profile data",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to retrieve user profile",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Updates user profile fields like display name, bio, language preferences, or user settings like notification preferences.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Profile"
                ],
                "summary": "Update current user's profile or settings",
                "parameters": [
                    {
                        "description": "Fields to update (include only fields to be changed)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateUserProfileRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated user profile data",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request payload or validation error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to update user profile",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/organizations": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves a list of organizations that the currently authenticated user is an active member of.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users",
                    "Organizations"
                ],
                "summary": "Get User's Organizations",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/phone/initiate-change": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Starts the process to change the user's registered phone number. Sends OTP to new number.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Profile"
                ],
                "summary": "Initiate phone number change",
                "parameters": [
                    {
                        "description": "New phone number, client ID, and state for phone change initiation",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.InitiatePhoneChangeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Details of the initiated phone change flow, including state and flow ID",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.InitiatePhoneChangeResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request or phone already in use",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict if the new phone number is already in use by another user",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to initiate phone number change",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/phone/verify-change": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Verifies the OTP sent to the new phone number to complete the change.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Profile"
                ],
                "summary": "Verify OTP for new phone number",
                "parameters": [
                    {
                        "description": "State, OTP, and new phone number for verification",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.VerifyPhoneChangeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Phone number changed successfully, returns updated user profile",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.VerifyPhoneChangeResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid OTP, state, or request format",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to verify phone number change",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/profile-picture": {
            "post": {
                "description": "Uploads a new profile picture for the current authenticated user.",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Profile"
                ],
                "summary": "Upload user profile picture",
                "parameters": [
                    {
                        "type": "file",
                        "description": "Image file (JPEG, PNG, GIF, WebP; max 5MB)",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated user profile with new profile_picture_url",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request or file type/size",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to upload profile picture",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves statistics for the currently authenticated user.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Get User Statistics",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.APIUserStats"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/uuid": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieves the UUID of the currently authenticated user.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Profile"
                ],
                "summary": "Get current user's UUID",
                "responses": {
                    "200": {
                        "description": "User UUID",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserUUIDResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/verifications": {
            "get": {
                "description": "Retrieves a list of verification requests submitted by the authenticated user.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Verification"
                ],
                "summary": "List all verification requests for the current user",
                "responses": {
                    "200": {
                        "description": "List of user's verification requests",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestResponse"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/verifications/documents/{docID}": {
            "get": {
                "description": "Retrieves a specific verification document submitted by the authenticated user.",
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "User Verification"
                ],
                "summary": "Get a user's verification document",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Verification Document ID",
                        "name": "docID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Verification document file",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not owner)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Document not found or access denied",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/verifications/{reqID}": {
            "get": {
                "description": "Retrieves details for a specific verification request owned by the user.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Verification"
                ],
                "summary": "Get details of a specific verification request",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Verification Request ID",
                        "name": "reqID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Verification request details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not owner)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Request not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "Allows user to request deletion of their verification data associated with a request.",
                "tags": [
                    "User Verification"
                ],
                "summary": "Request deletion of verification data",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Verification Request ID",
                        "name": "reqID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not owner)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Request not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/volunteer/applications": {
            "get": {
                "description": "Retrieves a list of volunteer applications submitted by the authenticated user across all organizations.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (User)",
                    "User Profile"
                ],
                "summary": "List current user's volunteer applications across all orgs",
                "responses": {
                    "200": {
                        "description": "List of user's volunteer applications",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/volunteer/applications/{appId}": {
            "get": {
                "description": "Retrieves details for a specific volunteer application submitted by the authenticated user.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (User)",
                    "User Profile"
                ],
                "summary": "Get details of a specific volunteer application owned by the user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Application ID (UUID)",
                        "name": "appId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Application details",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (not owner of application)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Application not found",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/me/volunteer/qualifications": {
            "get": {
                "description": "Retrieves a list of the user's volunteer applications that have a status of 'approved'. An approved application signifies the user is qualified to volunteer for that organization.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Volunteer Management (User)",
                    "User Profile"
                ],
                "summary": "List organizations the user is a qualified volunteer for",
                "responses": {
                    "200": {
                        "description": "List of approved volunteer applications (representing qualifications)",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/ws": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Establishes a WebSocket connection. Requires authentication via token in query param or valid Bearer header initially.",
                "tags": [
                    "WebSocket",
                    "Real-time"
                ],
                "summary": "Upgrade connection to WebSocket for real-time notifications",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authentication token (alternative to Bearer header for WS)",
                        "name": "token",
                        "in": "query"
                    }
                ],
                "responses": {
                    "101": {
                        "description": "Switching Protocols",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request (e.g. missing auth or invalid token)",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error during upgrade",
                        "schema": {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "Membership-SAAS-System-Backend_internal_payloads.APIUserStats": {
            "description": "Provides a summary of user activity, including event participation and engagement over time.",
            "type": "object",
            "properties": {
                "monthlyAttendedEvents": {
                    "description": "Statistics for events attended over the last 6 months.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.MonthlyAttendedEvent"
                    }
                },
                "topAttendedEventTags": {
                    "description": "Top 5 most frequently attended event tags over the last 6 months.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.TopAttendedEventTag"
                    }
                },
                "totalEvents": {
                    "description": "Total number of events the user has attended.",
                    "type": "integer",
                    "example": 50
                },
                "userJoinedAt": {
                    "description": "Timestamp when the user registered.",
                    "type": "string",
                    "example": "2023-01-15T10:00:00Z"
                },
                "volunteerEvents": {
                    "description": "Number of events the user has volunteered for.",
                    "type": "integer",
                    "example": 15
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.AdminReviewVerificationRequest": {
            "type": "object",
            "required": [
                "status"
            ],
            "properties": {
                "admin_notes": {
                    "type": "string"
                },
                "status": {
                    "description": "\"approved\" or \"rejected\"",
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.AdminReviewVolunteerApplicationRequest": {
            "type": "object",
            "required": [
                "status"
            ],
            "properties": {
                "admin_notes": {
                    "type": "string",
                    "example": "Looks good."
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "approved",
                        "rejected"
                    ],
                    "example": "approved"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse": {
            "type": "object",
            "properties": {
                "communication_language": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "display_name": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "email_verified_at": {
                    "type": "string"
                },
                "enable_app_notifications": {
                    "type": "boolean"
                },
                "enable_email_notifications": {
                    "type": "boolean"
                },
                "enable_sms_notifications": {
                    "type": "boolean"
                },
                "enable_whatsapp_notifications": {
                    "type": "boolean"
                },
                "id": {
                    "type": "string"
                },
                "interface_language": {
                    "type": "string"
                },
                "is_staff": {
                    "type": "boolean"
                },
                "phone": {
                    "type": "string"
                },
                "phone_otp_channel": {
                    "type": "string"
                },
                "phone_verified_at": {
                    "type": "string"
                },
                "profile_picture_url": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.ApplyVolunteerRequest": {
            "type": "object",
            "properties": {
                "motivation": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.CategoryStatItem": {
            "type": "object",
            "properties": {
                "name": {
                    "description": "Tag Name",
                    "type": "string"
                },
                "value": {
                    "description": "Count",
                    "type": "integer"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.CreateEventRequest": {
            "type": "object"
        },
        "Membership-SAAS-System-Backend_internal_payloads.CreateEventTagRequest": {
            "type": "object",
            "required": [
                "language_code",
                "tag_name"
            ],
            "properties": {
                "description": {
                    "type": "string"
                },
                "language_code": {
                    "type": "string",
                    "enum": [
                        "en",
                        "zh_HK",
                        "zh_CN"
                    ]
                },
                "tag_name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.CreateOrganizationFolderRequest": {
            "type": "object",
            "required": [
                "folder_name"
            ],
            "properties": {
                "folder_name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1
                },
                "parent_folder_id": {
                    "description": "Optional",
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.CreateOrganizationRequest": {
            "description": "Request body for creating a new organization.",
            "type": "object",
            "required": [
                "name",
                "owner_user_id"
            ],
            "properties": {
                "description": {
                    "description": "Optional description of the organization",
                    "type": "string",
                    "maxLength": 500,
                    "example": "We do great things!"
                },
                "image_url": {
                    "description": "Optional URL for the organization's image/logo",
                    "type": "string",
                    "maxLength": 512,
                    "example": "http://example.com/logo.png"
                },
                "is_default": {
                    "type": "boolean",
                    "example": false
                },
                "name": {
                    "description": "Name of the organization",
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 2,
                    "example": "My Awesome Org"
                },
                "owner_user_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "theme_color": {
                    "description": "Optional theme color for the organization (e.g., hex code)",
                    "type": "string",
                    "maxLength": 50,
                    "example": "#FF5733"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.CreatePostRequest": {
            "type": "object",
            "required": [
                "title"
            ],
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "published_at": {
                    "type": "string"
                },
                "slug": {
                    "type": "string",
                    "maxLength": 255
                },
                "status": {
                    "description": "Added 'hidden'",
                    "type": "string",
                    "enum": [
                        "draft",
                        "published",
                        "hidden"
                    ]
                },
                "title": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 3
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.CreatePostTagRequest": {
            "type": "object",
            "required": [
                "language_code",
                "tag_name"
            ],
            "properties": {
                "description": {
                    "type": "string"
                },
                "language_code": {
                    "type": "string",
                    "maxLength": 10,
                    "minLength": 2
                },
                "tag_name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.CreateResourceRequest": {
            "type": "object",
            "required": [
                "title"
            ],
            "properties": {
                "description": {
                    "type": "string"
                },
                "published_at": {
                    "type": "string"
                },
                "slug": {
                    "description": "Optional, can be auto-generated",
                    "type": "string",
                    "maxLength": 255
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "draft",
                        "published"
                    ]
                },
                "title": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 3
                },
                "visibility": {
                    "type": "string",
                    "enum": [
                        "public",
                        "org_only"
                    ]
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.CreateStaffUserRequest": {
            "type": "object",
            "required": [
                "display_name",
                "email",
                "password"
            ],
            "properties": {
                "communication_language": {
                    "type": "string"
                },
                "display_name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                },
                "email": {
                    "type": "string"
                },
                "interface_language": {
                    "type": "string"
                },
                "password": {
                    "description": "Password will be hashed by the service",
                    "type": "string",
                    "minLength": 8
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "description": "Optional more specific error",
                    "type": "string",
                    "example": "Bad Request"
                },
                "message": {
                    "type": "string",
                    "example": "Invalid input"
                },
                "statusCode": {
                    "type": "integer",
                    "example": 400
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.EventCheckInRequest": {
            "description": "Request body for marking a user's attendance at an event.",
            "type": "object",
            "required": [
                "user_id"
            ],
            "properties": {
                "user_id": {
                    "description": "The UUID of the user to check in.",
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse": {
            "type": "object",
            "properties": {
                "admin_notes_on_registration": {
                    "type": "string"
                },
                "attended_at": {
                    "type": "string"
                },
                "cancellation_reason_by_user": {
                    "type": "string"
                },
                "check_in_by_user_id": {
                    "description": "ID of the user (staff/volunteer) who performed the check-in for this registration (participant or volunteer).",
                    "type": "string"
                },
                "check_in_method": {
                    "description": "Method used for check-in (e.g., qr_scan, manual_staff, self_check_in).",
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "event_contact_email": {
                    "type": "string"
                },
                "event_contact_phone": {
                    "type": "string"
                },
                "event_description": {
                    "type": "string"
                },
                "event_end_time": {
                    "type": "string"
                },
                "event_id": {
                    "type": "string"
                },
                "event_location_full_address": {
                    "type": "string"
                },
                "event_location_online_url": {
                    "type": "string"
                },
                "event_location_type": {
                    "type": "string"
                },
                "event_organization_id": {
                    "type": "string"
                },
                "event_organization_name": {
                    "type": "string"
                },
                "event_price": {
                    "type": "string"
                },
                "event_start_time": {
                    "type": "string"
                },
                "event_status": {
                    "type": "string"
                },
                "event_title": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "payment_status": {
                    "$ref": "#/definitions/db.PaymentStatusType"
                },
                "registered_at": {
                    "type": "string"
                },
                "registered_count": {
                    "type": "integer"
                },
                "registration_role": {
                    "$ref": "#/definitions/db.EventRegistrationRoleType"
                },
                "status": {
                    "$ref": "#/definitions/db.EventRegistrationStatusType"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_display_name": {
                    "type": "string"
                },
                "user_email": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "user_phone": {
                    "type": "string"
                },
                "waitlist_priority": {
                    "type": "string"
                },
                "waitlisted_count": {
                    "type": "integer"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.EventResponse": {
            "type": "object",
            "properties": {
                "attended_count": {
                    "type": "integer"
                },
                "contact_email": {
                    "type": "string"
                },
                "contact_phone": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "created_by_user_id": {
                    "type": "string"
                },
                "current_user_registration_id": {
                    "type": "string"
                },
                "current_user_registration_status": {
                    "type": "string"
                },
                "current_user_volunteer_application_id": {
                    "type": "string"
                },
                "current_user_volunteer_status": {
                    "type": "string"
                },
                "end_time": {
                    "type": "string"
                },
                "event_verification_type_key": {
                    "type": "string"
                },
                "government_funding_keys": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "id": {
                    "type": "string"
                },
                "jsonContent": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "location_full_address": {
                    "description": "Consolidated address",
                    "type": "string"
                },
                "location_online_url": {
                    "type": "string"
                },
                "location_type": {
                    "type": "string"
                },
                "media_items": {
                    "description": "Added for generic media",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse"
                    }
                },
                "mediaeventitem": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "organization_id": {
                    "type": "string"
                },
                "organization_name": {
                    "type": "string"
                },
                "participant_limit": {
                    "type": "integer"
                },
                "price": {
                    "type": "string"
                },
                "published_at": {
                    "type": "string"
                },
                "registered_count": {
                    "type": "integer"
                },
                "required_verification_type_keys": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "requires_approval_for_registration": {
                    "type": "boolean"
                },
                "start_time": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.TagResponse"
                    }
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "waitlist_limit": {
                    "type": "integer"
                },
                "waitlisted_count": {
                    "type": "integer"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.EventStatisticsResponse": {
            "type": "object",
            "properties": {
                "categoryStats": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.CategoryStatItem"
                    }
                },
                "topEvents": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.TopEventItem"
                    }
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.EventVolunteerApplicationResponse": {
            "type": "object",
            "properties": {
                "admin_review_notes": {
                    "type": "string"
                },
                "application_notes_by_user": {
                    "type": "string"
                },
                "applied_at": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "event_id": {
                    "type": "string"
                },
                "event_start_time": {
                    "type": "string"
                },
                "event_title": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "organization_id": {
                    "type": "string"
                },
                "organization_name": {
                    "type": "string"
                },
                "reviewed_at": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "reviewer_display_name": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/db.ApplicationStatusEnum"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_display_name": {
                    "type": "string"
                },
                "user_email": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "user_phone": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.InitiatePhoneChangeRequest": {
            "type": "object",
            "required": [
                "client_id",
                "new_phone_number",
                "state"
            ],
            "properties": {
                "client_id": {
                    "description": "For PKCE flow, if AuthnService requires it for this flow type for consistency\nHowever, for an already authenticated user changing phone, PKCE might be optional for this specific sub-flow.\nThe current AuthnService.InitiatePhoneChangeOTP generates internal PKCE.\nClientID, State are good for flow correlation even without full client-side PKCE.",
                    "type": "string"
                },
                "new_phone_number": {
                    "type": "string"
                },
                "phone_otp_channel": {
                    "description": "Added: e.g., \"sms\", \"whatsapp\"",
                    "type": "string"
                },
                "state": {
                    "description": "Client generated state",
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.InitiatePhoneChangeResponse": {
            "type": "object",
            "properties": {
                "expires_in_sec": {
                    "description": "Added",
                    "type": "integer"
                },
                "flow_id": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "otp_channel": {
                    "description": "Added",
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.LogoutRequestPayload": {
            "type": "object",
            "required": [
                "refresh_token"
            ],
            "properties": {
                "refresh_token": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse": {
            "type": "object",
            "properties": {
                "file_name": {
                    "type": "string"
                },
                "file_path": {
                    "type": "string"
                },
                "file_size": {
                    "type": "integer"
                },
                "file_type": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "uploaded_at": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.MonthlyAttendedEvent": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer",
                    "example": 5
                },
                "month": {
                    "description": "YYYY-MM",
                    "type": "string",
                    "example": "2023-08"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.OrgSlimResponse": {
            "description": "Minimal details of an organization.",
            "type": "object",
            "properties": {
                "id": {
                    "description": "Unique identifier for the organization",
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "name": {
                    "description": "Name of the organization",
                    "type": "string",
                    "example": "Example Community"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "file_name": {
                    "type": "string"
                },
                "file_path": {
                    "description": "Public URL or identifier",
                    "type": "string"
                },
                "file_size": {
                    "type": "integer"
                },
                "file_type": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_folder": {
                    "type": "boolean"
                },
                "organization_id": {
                    "type": "string"
                },
                "parent_folder_id": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse": {
            "description": "Full details of an organization.",
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Timestamp when the organization was created",
                    "type": "string"
                },
                "description": {
                    "description": "Optional description of the organization",
                    "type": "string",
                    "example": "A community for developers."
                },
                "id": {
                    "description": "Unique identifier for the organization",
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "image_url": {
                    "description": "URL of the organization's logo or banner image",
                    "type": "string",
                    "example": "http://example.com/logo.png"
                },
                "is_default_org": {
                    "description": "Indicates if this is a default organization (e.g., for system use)",
                    "type": "boolean",
                    "example": false
                },
                "name": {
                    "description": "Name of the organization",
                    "type": "string",
                    "example": "Example Community"
                },
                "owner_user_id": {
                    "description": "ID of the user who owns the organization",
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "status": {
                    "description": "The current status of the organization (e.g., pending_setup, active, suspended).",
                    "type": "string",
                    "example": "active"
                },
                "theme_color": {
                    "description": "Primary theme color for the organization (e.g., 'red', 'blue', '#FF0000')",
                    "type": "string",
                    "example": "#007bff"
                },
                "updated_at": {
                    "description": "Timestamp when the organization was last updated",
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse": {
            "type": "object",
            "properties": {
                "data": {},
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total_items": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.PaginatedUserVerificationRequestsResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestAdminListResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.Pagination"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.PaginatedUsersResponse": {
            "type": "object",
            "properties": {
                "limit": {
                    "type": "integer"
                },
                "page": {
                    "type": "integer"
                },
                "total_count": {
                    "type": "integer"
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse"
                    }
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.Pagination": {
            "type": "object",
            "properties": {
                "current_page": {
                    "type": "integer"
                },
                "limit": {
                    "type": "integer"
                },
                "total_items": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.PostMediaItemResponse": {
            "type": "object",
            "properties": {
                "file_name": {
                    "type": "string"
                },
                "file_path": {
                    "description": "Public URL",
                    "type": "string"
                },
                "file_size": {
                    "type": "integer"
                },
                "file_type": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "post_id": {
                    "type": "string"
                },
                "uploaded_at": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.PostResponse": {
            "type": "object",
            "properties": {
                "author_display_name": {
                    "type": "string"
                },
                "content": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "media_items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostMediaItemResponse"
                    }
                },
                "organization_id": {
                    "type": "string"
                },
                "published_at": {
                    "type": "string"
                },
                "slug": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse"
                    }
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.PostTagResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "language_code": {
                    "type": "string"
                },
                "tag_name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.PostTagsListResponse": {
            "type": "object",
            "properties": {
                "tags": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse"
                    }
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.PublicEventResponse": {
            "type": "object",
            "properties": {
                "contact_email": {
                    "type": "string"
                },
                "contact_phone": {
                    "type": "string"
                },
                "current_user_registration_id": {
                    "type": "string"
                },
                "current_user_registration_status": {
                    "type": "string"
                },
                "current_user_volunteer_application_id": {
                    "type": "string"
                },
                "current_user_volunteer_status": {
                    "type": "string"
                },
                "end_time": {
                    "type": "string"
                },
                "event_verification_type_key": {
                    "type": "string"
                },
                "government_funding_keys": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "id": {
                    "type": "string"
                },
                "jsonContent": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "location_full_address": {
                    "description": "Consolidated address",
                    "type": "string"
                },
                "location_online_url": {
                    "type": "string"
                },
                "location_type": {
                    "type": "string"
                },
                "media_items": {
                    "description": "Added for generic media",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse"
                    }
                },
                "mediaeventitem": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "organization_id": {
                    "type": "string"
                },
                "organization_name": {
                    "type": "string"
                },
                "participant_limit": {
                    "type": "integer"
                },
                "price": {
                    "type": "string"
                },
                "published_at": {
                    "description": "Added for scheduled publishing",
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.TagResponse"
                    }
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.RegisterForEventRequest": {
            "description": "Request body for registering for an event.",
            "type": "object",
            "required": [
                "event_id"
            ],
            "properties": {
                "event_id": {
                    "description": "The UUID of the event to register for.",
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.ResourceFileResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "Optional description of the file.",
                    "type": "string",
                    "example": "This is a detailed description of the file."
                },
                "file_name": {
                    "type": "string"
                },
                "file_path": {
                    "description": "Public URL",
                    "type": "string"
                },
                "file_size": {
                    "type": "integer"
                },
                "file_type": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "resource_id": {
                    "type": "string"
                },
                "uploaded_at": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.ResourceResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "files": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceFileResponse"
                    }
                },
                "id": {
                    "type": "string"
                },
                "organization": {
                    "description": "For public listings",
                    "allOf": [
                        {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrgSlimResponse"
                        }
                    ]
                },
                "organization_id": {
                    "type": "string"
                },
                "published_at": {
                    "type": "string"
                },
                "slug": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "visibility": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.ReviewVolunteerApplicationRequest": {
            "type": "object",
            "required": [
                "new_status"
            ],
            "properties": {
                "admin_notes": {
                    "description": "Optional notes from the admin regarding the review",
                    "type": "string",
                    "example": "Applicant has relevant skills."
                },
                "new_status": {
                    "description": "The new status for the application ('approved' or 'rejected')",
                    "type": "string",
                    "enum": [
                        "approved",
                        "rejected"
                    ],
                    "example": "approved"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.TagResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_globally_approved": {
                    "type": "boolean"
                },
                "language_code": {
                    "type": "string"
                },
                "tag_name": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.TopAttendedEventTag": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer",
                    "example": 12
                },
                "name": {
                    "type": "string",
                    "example": "Community"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.TopEventItem": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "participants": {
                    "description": "Changed from int32 to int64 to match generated SQLC type",
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateEventRegistrationPaymentRequest": {
            "description": "Request body for updating the payment status of an event registration (Public or Staff endpoint).",
            "type": "object",
            "required": [
                "new_payment_status"
            ],
            "properties": {
                "new_payment_status": {
                    "description": "The new payment status for the registration.",
                    "type": "string",
                    "enum": [
                        "paid",
                        "unpaid",
                        "not_required",
                        "refunded"
                    ],
                    "example": "paid"
                },
                "staff_id": {
                    "description": "The UUID of the staff member performing the update (optional, for logging).",
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateEventRequest": {
            "type": "object"
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateLanguagePreferencesPayload": {
            "type": "object",
            "properties": {
                "communication_language": {
                    "type": "string",
                    "enum": [
                        "en",
                        "zh_HK",
                        "zh_CN"
                    ]
                },
                "interface_language": {
                    "type": "string",
                    "enum": [
                        "en",
                        "zh_HK",
                        "zh_CN"
                    ]
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateNotificationSettingsPayload": {
            "type": "object",
            "properties": {
                "enable_app_notifications": {
                    "type": "boolean"
                },
                "enable_email_notifications": {
                    "type": "boolean"
                },
                "enable_sms_notifications": {
                    "type": "boolean"
                },
                "enable_whatsapp_notifications": {
                    "type": "boolean"
                },
                "phone_otp_channel": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateOrgEventRegistrationStatusRequest": {
            "type": "object",
            "required": [
                "new_status"
            ],
            "properties": {
                "admin_notes": {
                    "description": "Optional notes from the admin regarding the status change",
                    "type": "string",
                    "example": "Approved after reviewing application."
                },
                "new_status": {
                    "description": "The new status for the registration (e.g., 'registered', 'cancelled_by_admin', 'rejected', 'waitlisted', 'attended')",
                    "type": "string",
                    "enum": [
                        "registered",
                        "cancelled_by_admin",
                        "rejected",
                        "waitlisted",
                        "attended"
                    ],
                    "example": "approved"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateOrganizationFileRequest": {
            "type": "object",
            "properties": {
                "new_name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1
                },
                "parent_folder_id": {
                    "description": "For moving",
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateOrganizationRequest": {
            "description": "Request body for updating an existing organization. All fields are optional.",
            "type": "object",
            "properties": {
                "description": {
                    "description": "New description for the organization",
                    "type": "string",
                    "maxLength": 500,
                    "example": "We updated our description!"
                },
                "image_url": {
                    "description": "New URL for the organization's image/logo",
                    "type": "string",
                    "maxLength": 512,
                    "example": "http://example.com/new_logo.jpg"
                },
                "name": {
                    "description": "New name for the organization",
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 2,
                    "example": "Updated Org Name"
                },
                "owner_user_id": {
                    "description": "Ownership change should be a separate, more controlled process.",
                    "type": "string"
                },
                "status": {
                    "description": "New status for the organization (pending_setup, active, suspended)",
                    "type": "string",
                    "enum": [
                        "pending_setup",
                        "active",
                        "suspended"
                    ],
                    "example": "active"
                },
                "theme_color": {
                    "description": "New theme color for the organization",
                    "type": "string",
                    "maxLength": 50,
                    "example": "#33FF57"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdatePostRequest": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "published_at": {
                    "type": "string"
                },
                "slug": {
                    "type": "string",
                    "maxLength": 255
                },
                "status": {
                    "description": "Added 'hidden'",
                    "type": "string",
                    "enum": [
                        "draft",
                        "published",
                        "hidden"
                    ]
                },
                "title": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 3
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdatePostTagRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "language_code": {
                    "type": "string",
                    "maxLength": 10,
                    "minLength": 2
                },
                "tag_name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateResourceRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "published_at": {
                    "type": "string"
                },
                "slug": {
                    "type": "string",
                    "maxLength": 255
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "draft",
                        "published"
                    ]
                },
                "title": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 3
                },
                "visibility": {
                    "type": "string",
                    "enum": [
                        "public",
                        "org_only"
                    ]
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UpdateUserProfileRequest": {
            "type": "object",
            "properties": {
                "display_name": {
                    "type": "string"
                },
                "language_preferences": {
                    "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateLanguagePreferencesPayload"
                },
                "notification_settings": {
                    "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateNotificationSettingsPayload"
                },
                "profile_picture_url": {
                    "description": "Basic for now, full upload is more complex",
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse": {
            "type": "object",
            "properties": {
                "communication_language": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "display_name": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "email_verified_at": {
                    "type": "string"
                },
                "enable_app_notifications": {
                    "type": "boolean"
                },
                "enable_email_notifications": {
                    "type": "boolean"
                },
                "enable_sms_notifications": {
                    "type": "boolean"
                },
                "enable_whatsapp_notifications": {
                    "type": "boolean"
                },
                "id": {
                    "type": "string"
                },
                "interface_language": {
                    "type": "string"
                },
                "is_staff": {
                    "type": "boolean"
                },
                "phone": {
                    "type": "string"
                },
                "phone_otp_channel": {
                    "description": "e.g., \"sms\", \"whatsapp\"",
                    "type": "string"
                },
                "phone_verified_at": {
                    "type": "string"
                },
                "profile_picture_url": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "verification_status": {
                    "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationStatusResponse"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UserUUIDResponse": {
            "type": "object",
            "properties": {
                "uuid": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestAdminListResponse": {
            "type": "object",
            "properties": {
                "admin_notes": {
                    "type": "string"
                },
                "document_id": {
                    "type": "string"
                },
                "document_id_2": {
                    "type": "string"
                },
                "file_name": {
                    "type": "string"
                },
                "request_id": {
                    "type": "string"
                },
                "reviewed_at": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "reviewer_display_name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "submitted_at": {
                    "type": "string"
                },
                "user_display_name": {
                    "type": "string"
                },
                "user_email": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "verification_type": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestResponse": {
            "type": "object",
            "properties": {
                "admin_notes": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "document_id": {
                    "type": "string"
                },
                "document_id_2": {
                    "type": "string"
                },
                "file_name": {
                    "type": "string"
                },
                "file_name_2": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "mime_type": {
                    "type": "string"
                },
                "mime_type_2": {
                    "type": "string"
                },
                "reviewed_at": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "reviewer_display_name": {
                    "type": "string"
                },
                "specifics": {
                    "description": "Specifics will hold the data for the given verification type.\nThis can be a map[string]interface{} or a more structured approach later."
                },
                "status": {
                    "$ref": "#/definitions/db.VerificationStatusEnum"
                },
                "submitted_at": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_display_name": {
                    "description": "Optional related info often included in responses",
                    "type": "string"
                },
                "user_email": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "verification_type": {
                    "$ref": "#/definitions/db.VerificationTypeEnum"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.UserVerificationStatusResponse": {
            "type": "object",
            "properties": {
                "address_proof": {
                    "type": "boolean"
                },
                "hk_id_card": {
                    "type": "boolean"
                },
                "hk_youth_plus": {
                    "type": "boolean"
                },
                "mainland_china_id_card": {
                    "type": "boolean"
                },
                "mainland_travel_permit": {
                    "type": "boolean"
                },
                "passport": {
                    "type": "boolean"
                },
                "student_id": {
                    "type": "boolean"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.VerifyPhoneChangeRequest": {
            "type": "object",
            "required": [
                "new_phone_number",
                "otp",
                "state"
            ],
            "properties": {
                "new_phone_number": {
                    "type": "string"
                },
                "otp": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.VerifyPhoneChangeResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                },
                "user": {
                    "description": "The updated user profile",
                    "allOf": [
                        {
                            "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse"
                        }
                    ]
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse": {
            "type": "object",
            "properties": {
                "admin_notes": {
                    "type": "string"
                },
                "applicant_display_name": {
                    "description": "Fields primarily for admin views of applications",
                    "type": "string"
                },
                "applicant_email": {
                    "type": "string"
                },
                "applicant_phone": {
                    "type": "string"
                },
                "application_date": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "motivation": {
                    "type": "string"
                },
                "organization_id": {
                    "type": "string"
                },
                "organization_name": {
                    "type": "string"
                },
                "review_date": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "reviewer_display_name": {
                    "description": "For admin views",
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "description": "Kept for consistency, though often redundant if it's /me/",
                    "type": "string"
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_services.EnhancedVolunteerApplicationDetails": {
            "type": "object",
            "properties": {
                "admin_notes": {
                    "type": "string"
                },
                "applicant_display_name": {
                    "description": "Fields primarily for admin views of applications",
                    "type": "string"
                },
                "applicant_email": {
                    "type": "string"
                },
                "applicant_phone": {
                    "type": "string"
                },
                "application_date": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "motivation": {
                    "type": "string"
                },
                "organization_id": {
                    "type": "string"
                },
                "organization_name": {
                    "type": "string"
                },
                "review_date": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "reviewer_display_name": {
                    "description": "For admin views",
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "description": "Kept for consistency, though often redundant if it's /me/",
                    "type": "string"
                },
                "verification_requests": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestResponse"
                    }
                }
            }
        },
        "Membership-SAAS-System-Backend_internal_utils.ErrorResponse": {
            "description": "Represents a standard error format returned by the API.",
            "type": "object",
            "properties": {
                "details": {
                    "description": "Optional technical details or underlying error string (usually omitted for 5xx errors).",
                    "type": "string",
                    "example": "error details here"
                },
                "error": {
                    "description": "A short, general error message or code (often corresponds to HTTP status text).",
                    "type": "string",
                    "example": "Not Found"
                },
                "message": {
                    "description": "A more detailed, human-readable message about the error.",
                    "type": "string",
                    "example": "Resource not found"
                }
            }
        },
        "db.ApplicationStatusEnum": {
            "type": "string",
            "enum": [
                "pending",
                "approved",
                "rejected"
            ],
            "x-enum-varnames": [
                "ApplicationStatusEnumPending",
                "ApplicationStatusEnumApproved",
                "ApplicationStatusEnumRejected"
            ]
        },
        "db.EventRegistrationRoleType": {
            "type": "string",
            "enum": [
                "participant",
                "volunteer"
            ],
            "x-enum-varnames": [
                "EventRegistrationRoleTypeParticipant",
                "EventRegistrationRoleTypeVolunteer"
            ]
        },
        "db.EventRegistrationStatusType": {
            "type": "string",
            "enum": [
                "pending_approval",
                "registered",
                "waitlisted",
                "rejected_approval",
                "cancelled_by_user",
                "attended",
                "absent"
            ],
            "x-enum-varnames": [
                "EventRegistrationStatusTypePendingApproval",
                "EventRegistrationStatusTypeRegistered",
                "EventRegistrationStatusTypeWaitlisted",
                "EventRegistrationStatusTypeRejectedApproval",
                "EventRegistrationStatusTypeCancelledByUser",
                "EventRegistrationStatusTypeAttended",
                "EventRegistrationStatusTypeAbsent"
            ]
        },
        "db.EventTag": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "created_by_user_id": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_globally_approved": {
                    "type": "boolean"
                },
                "language_code": {
                    "type": "string"
                },
                "tag_name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "db.ListAllPendingReviewEventVolunteerApplicationsRow": {
            "type": "object",
            "properties": {
                "admin_review_notes": {
                    "type": "string"
                },
                "applicant_display_name": {
                    "type": "string"
                },
                "applicant_email": {
                    "type": "string"
                },
                "applicant_phone": {
                    "type": "string"
                },
                "application_notes_by_user": {
                    "type": "string"
                },
                "applied_at": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "event_id": {
                    "type": "string"
                },
                "event_title": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "organization_id": {
                    "type": "string"
                },
                "organization_name": {
                    "type": "string"
                },
                "reviewed_at": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/db.ApplicationStatusEnum"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "db.ListPendingReviewEventVolunteerApplicationsForEventRow": {
            "type": "object",
            "properties": {
                "admin_review_notes": {
                    "type": "string"
                },
                "applicant_display_name": {
                    "type": "string"
                },
                "applicant_email": {
                    "type": "string"
                },
                "applicant_phone": {
                    "type": "string"
                },
                "application_notes_by_user": {
                    "type": "string"
                },
                "applied_at": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "event_id": {
                    "type": "string"
                },
                "event_title": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "organization_id": {
                    "type": "string"
                },
                "organization_name": {
                    "type": "string"
                },
                "reviewed_at": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/db.ApplicationStatusEnum"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "db.ListPendingReviewEventVolunteerApplicationsForOrganizationRow": {
            "type": "object",
            "properties": {
                "admin_review_notes": {
                    "type": "string"
                },
                "applicant_display_name": {
                    "type": "string"
                },
                "applicant_email": {
                    "type": "string"
                },
                "applicant_phone": {
                    "type": "string"
                },
                "application_notes_by_user": {
                    "type": "string"
                },
                "applied_at": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "event_id": {
                    "type": "string"
                },
                "event_title": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "organization_id": {
                    "type": "string"
                },
                "organization_name": {
                    "type": "string"
                },
                "reviewed_at": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/db.ApplicationStatusEnum"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "db.PaymentStatusType": {
            "type": "string",
            "enum": [
                "paid",
                "unpaid",
                "not_required",
                "refunded"
            ],
            "x-enum-varnames": [
                "PaymentStatusTypePaid",
                "PaymentStatusTypeUnpaid",
                "PaymentStatusTypeNotRequired",
                "PaymentStatusTypeRefunded"
            ]
        },
        "db.User": {
            "type": "object",
            "properties": {
                "communication_language": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "display_name": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "email_verified_at": {
                    "type": "string"
                },
                "enable_app_notifications": {
                    "type": "boolean"
                },
                "enable_email_notifications": {
                    "type": "boolean"
                },
                "enable_sms_notifications": {
                    "type": "boolean"
                },
                "enable_whatsapp_notifications": {
                    "type": "boolean"
                },
                "hashed_password": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "interface_language": {
                    "type": "string"
                },
                "is_staff": {
                    "type": "boolean"
                },
                "phone": {
                    "type": "string"
                },
                "phone_otp_channel": {
                    "type": "string"
                },
                "phone_verified_at": {
                    "type": "string"
                },
                "profile_picture_url": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "db.UserVerificationRequest": {
            "type": "object",
            "properties": {
                "admin_notes": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "document_id": {
                    "type": "string"
                },
                "document_id_2": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "reviewed_at": {
                    "type": "string"
                },
                "reviewed_by_user_id": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/db.VerificationStatusEnum"
                },
                "submitted_at": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "verification_type": {
                    "$ref": "#/definitions/db.VerificationTypeEnum"
                }
            }
        },
        "db.VerificationStatusEnum": {
            "type": "string",
            "enum": [
                "pending",
                "approved",
                "rejected",
                "data_deleted_by_user"
            ],
            "x-enum-varnames": [
                "VerificationStatusEnumPending",
                "VerificationStatusEnumApproved",
                "VerificationStatusEnumRejected",
                "VerificationStatusEnumDataDeletedByUser"
            ]
        },
        "db.VerificationTypeEnum": {
            "type": "string",
            "enum": [
                "hk_id_card",
                "mainland_china_id_card",
                "mainland_travel_permit",
                "passport",
                "hk_youth_plus",
                "address_proof",
                "student_id",
                "home_visit"
            ],
            "x-enum-varnames": [
                "VerificationTypeEnumHkIDCard",
                "VerificationTypeEnumMainlandChinaIDCard",
                "VerificationTypeEnumMainlandTravelPermit",
                "VerificationTypeEnumPassport",
                "VerificationTypeEnumHkYouthPlus",
                "VerificationTypeEnumAddressProof",
                "VerificationTypeEnumStudentID",
                "VerificationTypeEnumHomeVisit"
            ]
        },
        "echo.Map": {
            "type": "object",
            "additionalProperties": true
        },
        "internal_authn.CheckPhoneRequest": {
            "type": "object",
            "required": [
                "phone"
            ],
            "properties": {
                "phone": {
                    "type": "string"
                }
            }
        },
        "internal_authn.CheckPhoneResponse": {
            "type": "object",
            "properties": {
                "exists": {
                    "type": "boolean"
                },
                "user": {
                    "description": "Optionally return user details if exists",
                    "allOf": [
                        {
                            "$ref": "#/definitions/db.User"
                        }
                    ]
                }
            }
        },
        "internal_authn.CheckStaffEmailRequest": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string"
                }
            }
        },
        "internal_authn.CheckStaffEmailResponse": {
            "type": "object",
            "properties": {
                "exists": {
                    "type": "boolean"
                },
                "is_staff": {
                    "description": "To confirm the user is actually staff",
                    "type": "boolean"
                },
                "user_hint": {
                    "description": "e.g., if exists but not staff",
                    "type": "string"
                }
            }
        },
        "internal_authn.InitiatePhoneOTPRequest": {
            "type": "object",
            "required": [
                "client_id",
                "code_challenge",
                "code_challenge_method",
                "phone",
                "state"
            ],
            "properties": {
                "client_id": {
                    "type": "string"
                },
                "code_challenge": {
                    "type": "string"
                },
                "code_challenge_method": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "phone_otp_channel": {
                    "description": "e.g., 'whatsapp', 'sms'",
                    "type": "string"
                },
                "redirect_uri": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "internal_authn.InitiatePhoneOTPResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "description": "e.g., \"OTP initiated successfully\"",
                    "type": "string"
                },
                "state": {
                    "description": "Echo back the state for client verification",
                    "type": "string"
                }
            }
        },
        "internal_authn.InitiatePhoneRegistrationRequest": {
            "type": "object",
            "required": [
                "client_id",
                "code_challenge",
                "code_challenge_method",
                "phone",
                "state"
            ],
            "properties": {
                "client_id": {
                    "type": "string"
                },
                "code_challenge": {
                    "type": "string"
                },
                "code_challenge_method": {
                    "description": "e.g., \"S256\"",
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "phone_otp_channel": {
                    "description": "e.g., 'whatsapp', 'sms'",
                    "type": "string",
                    "enum": [
                        "whatsapp",
                        "sms"
                    ]
                },
                "redirect_uri": {
                    "description": "Optional, but good for consistency",
                    "type": "string"
                },
                "state": {
                    "description": "Opaque value for CSRF protection",
                    "type": "string"
                }
            }
        },
        "internal_authn.InitiatePhoneRegistrationResponse": {
            "type": "object",
            "properties": {
                "flow_id": {
                    "description": "The ID of the created auth_flow, useful for client tracking",
                    "type": "string"
                },
                "message": {
                    "description": "e.g., \"Registration OTP initiated successfully\"",
                    "type": "string"
                },
                "state": {
                    "description": "Echo back the state for client verification",
                    "type": "string"
                }
            }
        },
        "internal_authn.InitiateStaffLoginRequest": {
            "type": "object",
            "required": [
                "client_id",
                "code_challenge",
                "code_challenge_method",
                "email",
                "state"
            ],
            "properties": {
                "client_id": {
                    "type": "string"
                },
                "code_challenge": {
                    "type": "string"
                },
                "code_challenge_method": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "redirect_uri": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "internal_authn.InitiateStaffLoginResponse": {
            "type": "object",
            "properties": {
                "flow_id": {
                    "description": "ID of the created auth_flow",
                    "type": "string"
                },
                "state": {
                    "description": "Echo back state",
                    "type": "string"
                }
            }
        },
        "internal_authn.RefreshTokenRequest": {
            "type": "object",
            "required": [
                "refresh_token"
            ],
            "properties": {
                "refresh_token": {
                    "type": "string"
                }
            }
        },
        "internal_authn.RefreshTokenResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "refresh_token": {
                    "type": "string"
                },
                "token_type": {
                    "description": "Usually \"Bearer\"",
                    "type": "string"
                }
            }
        },
        "internal_authn.VerifyPhoneOTPRequest": {
            "type": "object",
            "required": [
                "code_verifier",
                "otp",
                "state"
            ],
            "properties": {
                "code_verifier": {
                    "description": "PKCE code verifier",
                    "type": "string"
                },
                "otp": {
                    "description": "The OTP received by the user",
                    "type": "string"
                },
                "state": {
                    "description": "The state value from the initiation step",
                    "type": "string"
                }
            }
        },
        "internal_authn.VerifyPhoneOTPResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "refresh_token": {
                    "type": "string"
                },
                "user_id": {
                    "description": "ID of the logged-in user",
                    "type": "string"
                }
            }
        },
        "internal_authn.VerifyPhoneRegistrationRequest": {
            "type": "object",
            "required": [
                "code_verifier",
                "display_name",
                "otp",
                "state"
            ],
            "properties": {
                "code_verifier": {
                    "description": "PKCE code verifier",
                    "type": "string"
                },
                "communication_language": {
                    "type": "string"
                },
                "display_name": {
                    "description": "User's chosen display name",
                    "type": "string"
                },
                "interface_language": {
                    "type": "string"
                },
                "otp": {
                    "description": "The OTP received by the user",
                    "type": "string"
                },
                "phone_otp_channel": {
                    "description": "e.g., 'whatsapp', 'sms'",
                    "type": "string",
                    "enum": [
                        "whatsapp",
                        "sms"
                    ]
                },
                "state": {
                    "description": "The state value from the initiation step",
                    "type": "string"
                }
            }
        },
        "internal_authn.VerifyPhoneRegistrationResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "refresh_token": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "internal_authn.VerifyStaffLoginRequest": {
            "type": "object",
            "required": [
                "code_verifier",
                "email",
                "password",
                "state"
            ],
            "properties": {
                "code_verifier": {
                    "type": "string"
                },
                "email": {
                    "description": "For verification against the flow",
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "internal_authn.VerifyStaffLoginResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "refresh_token": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "internal_handlers.GovernmentFundingTypeResponse": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string",
                    "example": "gov_funded_prog_green"
                },
                "langcode": {
                    "type": "string",
                    "example": "en"
                },
                "name": {
                    "type": "string",
                    "example": "Government-funded programme (Green)"
                }
            }
        },
        "internal_handlers.VerificationTypeResponse": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string",
                    "example": "passport"
                },
                "langcode": {
                    "type": "string",
                    "example": "en"
                },
                "name": {
                    "type": "string",
                    "example": "Passport"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{"http", "https"},
	Title:            "Membership SAAS System API",
	Description:      "This is the API for the Membership SAAS System backend.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
