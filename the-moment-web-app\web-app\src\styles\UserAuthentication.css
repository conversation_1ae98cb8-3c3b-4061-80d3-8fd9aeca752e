/* UserAuthentication.css */
.auth-container {
    display: flex;
    min-height: 100vh;
    background-color: #ffffff;
}

.auth-left {
    width: 40%;
    background-color: #f8f8f8;
    /* Light gray background for left content */
    padding: 60px 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-instructions {
    max-width: 450px;
}

.instruction-header {
    margin-bottom: 24px;
    /* Space between header and list */
}

.info-icon {
    font-size: 24px;
    color: rgb(0, 157, 0);
    /* Ant Design primary color */
    margin-right: 16px;
}

.instructions-title {
    color: #000000;
    margin: 0;
    /* Remove default margin */
    font-size: 24px;
    /* Increased font size */
}

.instructions-list {
    list-style-type: disc;
    padding-left: 24px;
    color: #595959;
    /* Gray font for instructions */
    margin-bottom: 16px;
    line-height: 1.8;
    font-size: 16px;
    /* Increased font size */
}

.instructions-list li {
    margin-bottom: 12px;
}

.auth-right {
    width: 60%;
    padding: 40px 60px;
    /* Adjusted padding for better spacing */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.auth-steps {
    margin-bottom: 40px;
    width: 80%;  
    min-width: 300px; 
    max-width: 800px; 
    margin-left: auto; 
    margin-right: auto;
}

.auth-steps .ant-steps-item {
    padding-right: 16px;
}


@media (max-width: 768px) {
    .auth-steps {
        width: 100%; 
        min-width: 250px;
    }
}

.auth-form-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.auth-form-container .ant-form {
    width: 100%;
    max-width: 700px;
}

.navigation-buttons {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    width: 100%;
}

.navigation-buttons .ant-btn {
    min-width: 120px;
    /* Increased button width */
    font-size: 16px;
    /* Increased button font size */
}


.auth-form-container .ant-form-item-label>label, 
.auth-form-container .ant-upload.ant-upload-picture-card-wrapper .ant-upload-text {
    font-size: 16px;
}

@media (max-width: 992px) {
    .auth-container {
        flex-direction: column;
    }

    .auth-left,
    .auth-right {
        width: 100%;
        padding: 40px 20px;
    }

    .auth-steps {
        margin-bottom: 20px;
    }

    .auth-form-container {
        justify-content: center;
        align-items: center;
    }

    .auth-form-container .ant-form {
        max-width: 100%;
    }

    .navigation-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .navigation-buttons .ant-btn {
        width: 100%;
        margin-bottom: 8px;
        min-width: unset;
        /* Remove min-width on small screens */
    }


    .auth-form-container .ant-form-item-label>label {
        font-size: 14px;
    }

    .auth-form-container .ant-input,
    .auth-form-container .ant-input-textarea,
    .auth-form-container .ant-picker,
    .auth-form-container .ant-radio-group,
    .auth-form-container .ant-upload-text {
        font-size: 14px;
    }

    .navigation-buttons .ant-btn {
        font-size: 14px;
    }
}

.review-details {
    margin-bottom: 24px;
}

.detail-item {
    margin-bottom: 12px;
}

.detail-label {
    color: #595959;
    font-size: 16px;
}

.detail-value {
    color: #000000;
    font-size: 16px;
    margin-left: 20px;
}

.requirements-list {
    list-style-type: disc;
    padding-left: 24px;
    color: #595959;
    margin-bottom: 16px;
    line-height: 1.8;
    font-size: 16px;
}

.requirements-list li {
    margin-bottom: 12px;
}

.verification-setup {
    margin-bottom: 20px;
}

.data-security {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

/* Address Verification */

/* Style for the Radio Group */
.address-proof-radio-group {
    display: flex;
    margin-bottom: 20px;
}

.address-descriptions {
    padding: 8px;
    margin-bottom: 24px;
}

.address-descriptions .ant-descriptions-item-label, .address-descriptions .ant-descriptions-item-content {
    font-size: 16px;
}

.verification-modal-content {
  padding: 16px 0;
}

.verification-steps {
  margin: 24px 0;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.step-item {
  display: flex;
  align-items: center;
  margin: 12px 0;
}

.step-icon {
  margin-right: 12px;
  font-size: 20px;
}

.step-icon.completed {
  color: #52c41a;
}

.step-icon.pending {
  color: #1890ff;
}

.verification-benefits {
  margin-top: 24px;
  color: #666;
}

.verification-benefits p {
  margin-bottom: 12px;
}
