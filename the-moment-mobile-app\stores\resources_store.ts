import { create } from 'zustand';
import { AxiosError } from 'axios';
import type {
    ResourceListPayload,
} from '@/api/api_config';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ResourceListState {
    resourceList: ResourceListPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setResourceList: (resources: ResourceListPayload[]) => void;
    setError: (error: AxiosError) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const resourceListStore = create<ResourceListState>()(persist<ResourceListState>(
    (set) => ({
        resourceList: [],
        isFetching: false,
        error: null,
        setResourceList: (resources: ResourceListPayload[]) => set({ resourceList: resources, isFetching: false, error: null }),
        setError: (error: AxiosError) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'resource-list-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

// State for Resource Details
interface ResourceDetailsState {
    resourceDetails: ResourceListPayload | null;
    isFetching: boolean;
    error: AxiosError | null;
    setResourceDetails: (resource: ResourceListPayload) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const resourceDetailsStore = create<ResourceDetailsState>()(persist<ResourceDetailsState>(
    (set) => ({
        resourceDetails: null ,
        isFetching: false,
        error: null,
        setResourceDetails: (resource: ResourceListPayload) => set({ resourceDetails: resource, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'resource-details-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));
