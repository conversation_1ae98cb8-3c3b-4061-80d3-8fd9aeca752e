package authn

import (
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/token"
	"errors"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	_ "Membership-SAAS-System-Backend/internal/utils"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// CreateOrganizationHandler godoc
// @Summary Create a new organization
// @Description Creates a new organization. The authenticated user becomes the owner. Name is required. Description, image_url, theme_color are optional. Status defaults to 'pending_setup'.
// @Tags Organizations
// @Accept json
// @Produce json
// @Param body body payloads.CreateOrganizationRequest true "Organization details (name required, description, image_url, theme_color optional)"
// @Success 201 {object} payloads.OrganizationResponse "Newly created organization (includes image_url, theme_color, status)"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations [post]
func (as *AuthnService) CreateOrganizationHandler(c echo.Context) error {
	ctx := c.Request().Context()
	log.Ctx(ctx).Info().Msg("CreateOrganizationHandler: Attempting to access JWT claims")

	retrievedToken, ok := c.Get("user").(*jwt.Token)
	if !ok || retrievedToken == nil {
		log.Ctx(ctx).Error().Msg("CreateOrganizationHandler: JWT token not found using 'user' key.")
		return c.JSON(http.StatusUnauthorized, map[string]string{"error": "CreateOrganizationHandler: User claims not found (user)"})
	}

	claims, ok := retrievedToken.Claims.(*token.AppClaims)
	if !ok || claims == nil {
		log.Ctx(ctx).Error().Msg("CreateOrganizationHandler: Failed to cast claims from token.")
		return echo.NewHTTPError(http.StatusInternalServerError, "CreateOrganizationHandler: Could not process user claims from token")
	}

	creatorUserID, err := uuid.Parse(claims.RegisteredClaims.Subject)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subject", claims.RegisteredClaims.Subject).Msg("CreateOrganizationHandler: Failed to parse creator user ID from claims")
		return echo.NewHTTPError(http.StatusInternalServerError, "CreateOrganizationHandler: Invalid creator user ID in claims")
	}

	var input payloads.CreateOrganizationRequest
	if err := c.Bind(&input); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to bind request for CreateOrganization")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request payload: "+err.Error())
	}

	if input.OwnerUserID == uuid.Nil {
		log.Ctx(ctx).Info().Str("creator_user_id", creatorUserID.String()).Msg("CreateOrganizationHandler: OwnerUserID not provided in request, defaulting to creatorUserID.")
		input.OwnerUserID = creatorUserID
	} else {
		log.Ctx(ctx).Info().Str("creator_user_id", creatorUserID.String()).Str("payload_owner_user_id", input.OwnerUserID.String()).Msg("CreateOrganizationHandler: OwnerUserID provided in request.")
	}

	if err := c.Validate(input); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Validation failed for CreateOrganization input")
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	if as.OrgManager == nil {
		log.Ctx(ctx).Error().Msg("OrganizationManager is not initialized in AuthnService")
		return echo.NewHTTPError(http.StatusInternalServerError, "Internal server error: Organization service not available.")
	}

	org, err := as.OrgManager.CreateOrganization(ctx, creatorUserID, input)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create organization")
		if errors.Is(err, payloads.ErrOrgNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Failed to create organization: Owner user not found.")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create organization: "+err.Error())
	}

	log.Ctx(ctx).Info().Str("org_id", org.ID.String()).Str("org_name", org.Name).Msg("Organization created successfully by CreateOrganizationHandler")
	return c.JSON(http.StatusCreated, payloads.ToOrganizationResponse(org))
}

// ListOrganizationsHandler godoc
// @Summary List all organizations
// @Description Lists all organizations. Visibility may depend on user role. Supports pagination. Response items include image_url, theme_color, and status.
// @Tags Organizations
// @Produce json
// @Param limit query int false "Limit number of results" default(10)
// @Param offset query int false "Offset for pagination" default(0)
// @Success 200 {array} payloads.OrganizationResponse "List of organizations"
// @Header 200 {string} X-Total-Count "Total number of organizations"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized" // If auth is strictly required
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations [get]
func (as *AuthnService) ListOrganizationsHandler(c echo.Context) error {
	ctx := c.Request().Context()

	// Parse limit and offset from query parameters
	limitStr := c.QueryParam("limit")
	offsetStr := c.QueryParam("offset")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10 // Default limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0 // Default offset
	}

	if as.OrgManager == nil {
		log.Ctx(ctx).Error().Msg("OrganizationManager is not initialized")
		return echo.NewHTTPError(http.StatusInternalServerError, "Internal server error: Organization service not available.")
	}

	orgs, totalCount, err := as.OrgManager.ListOrganizations(ctx, limit, offset)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list organizations")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to list organizations: "+err.Error())
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, payloads.ToOrganizationResponses(orgs))
}

// GetOrganizationByIDHandler godoc
// @Summary Get details of a specific organization
// @Description Retrieves details for a given organization ID. Response includes image_url, theme_color, and status.
// @Tags Organizations
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Success 200 {object} payloads.OrganizationResponse "Organization details"
// @Failure 400 {object} payloads.ErrorResponse "Invalid organization ID format"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (if user not member and it's private)"
// @Failure 404 {object} payloads.ErrorResponse "Organization not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations/{orgId} [get]
func (as *AuthnService) GetOrganizationByIDHandler(c echo.Context) error {
	ctx := c.Request().Context()
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("orgId_str", orgIDStr).Msg("Failed to parse orgId from path")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	if as.OrgManager == nil {
		log.Ctx(ctx).Error().Msg("OrganizationManager is not initialized")
		return echo.NewHTTPError(http.StatusInternalServerError, "Internal server error: Organization service not available.")
	}

	org, err := as.OrgManager.GetOrganizationByID(ctx, orgID)
	if err != nil {
		if errors.Is(err, payloads.ErrOrgNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		log.Ctx(ctx).Error().Err(err).Str("orgId", orgID.String()).Msg("Failed to get organization by ID")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve organization: "+err.Error())
	}
	return c.JSON(http.StatusOK, payloads.ToOrganizationResponse(org))
}

// UpdateOrganizationHandler godoc
// @Summary Update an organization
// @Description Updates details of an organization. Requires admin/owner role in that org. All fields in body are optional (name, description, image_url, theme_color, status).
// @Tags Organizations
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param body body payloads.UpdateOrganizationRequest true "Fields to update (name, description, image_url, theme_color, status - all optional)"
// @Success 200 {object} payloads.OrganizationResponse "Updated organization details"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not admin/owner)"
// @Failure 404 {object} payloads.ErrorResponse "Organization not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations/{orgId} [put]
func (as *AuthnService) UpdateOrganizationHandler(c echo.Context) error {
	ctx := c.Request().Context()
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	retrievedToken, ok := c.Get("user").(*jwt.Token)
	if !ok || retrievedToken == nil {
		log.Ctx(ctx).Error().Msg("UpdateOrganizationHandler: JWT token not found using 'user' key.")
		return echo.NewHTTPError(http.StatusUnauthorized, "User claims not found (user)")
	}
	claims, ok := retrievedToken.Claims.(*token.AppClaims)
	if !ok || claims == nil {
		log.Ctx(ctx).Error().Msg("UpdateOrganizationHandler: Failed to cast claims from token.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Could not process user claims from token")
	}
	actorUserID, err := uuid.Parse(claims.RegisteredClaims.Subject)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subject", claims.RegisteredClaims.Subject).Msg("UpdateOrganizationHandler: Failed to parse user ID.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Invalid user ID in token")
	}

	if as.OrgManager == nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Organization service not available.")
	}

	// Superadmin bypass
	if claims.Role != "superadmin" {
		roleOpts := payloads.CheckUserOrganizationRoleOptions{
			UserID:         actorUserID,
			OrganizationID: orgID,
			RequiredRoles:  []string{"owner", "admin"},
		}
		hasPermission, err := as.OrgManager.CheckUserOrganizationRole(ctx, roleOpts)
		if err != nil {
			if errors.Is(err, payloads.ErrNotMember) {
				return echo.NewHTTPError(http.StatusForbidden, "You are not a member of this organization.")
			}
			log.Ctx(ctx).Error().Err(err).Str("user_id", actorUserID.String()).Str("org_id", orgID.String()).Msg("Error checking user role for update organization")
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to verify permissions.")
		}
		if !hasPermission {
			return echo.NewHTTPError(http.StatusForbidden, "You do not have permission to update this organization.")
		}
	}

	var input payloads.UpdateOrganizationRequest
	if err := c.Bind(&input); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request payload: "+err.Error())
	}
	if err := c.Validate(input); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	updatedOrg, err := as.OrgManager.UpdateOrganization(ctx, orgID, input)
	if err != nil {
		if errors.Is(err, payloads.ErrOrgNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		log.Ctx(ctx).Error().Err(err).Str("orgId", orgID.String()).Msg("Failed to update organization")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update organization: "+err.Error())
	}
	return c.JSON(http.StatusOK, updatedOrg)
}

// DeleteOrganizationHandler godoc
// @Summary Delete an organization
// @Description Deletes an organization. Requires owner role in that org.
// @Tags Organizations
// @Param orgId path string true "Organization ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} payloads.ErrorResponse "Cannot delete default organization or invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not owner)"
// @Failure 404 {object} payloads.ErrorResponse "Organization not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations/{orgId} [delete]
func (as *AuthnService) DeleteOrganizationHandler(c echo.Context) error {
	ctx := c.Request().Context()
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	retrievedToken, ok := c.Get("user").(*jwt.Token)
	if !ok || retrievedToken == nil {
		log.Ctx(ctx).Error().Msg("DeleteOrganizationHandler: JWT token not found using 'user' key.")
		return echo.NewHTTPError(http.StatusUnauthorized, "User claims not found (user)")
	}
	claims, ok := retrievedToken.Claims.(*token.AppClaims)
	if !ok || claims == nil {
		log.Ctx(ctx).Error().Msg("DeleteOrganizationHandler: Failed to cast claims from token.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Could not process user claims from token")
	}
	actorUserID, err := uuid.Parse(claims.RegisteredClaims.Subject)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subject", claims.RegisteredClaims.Subject).Msg("DeleteOrganizationHandler: Failed to parse user ID.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Invalid user ID in token")
	}

	if as.OrgManager == nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Organization service not available.")
	}

	// Superadmin bypass
	if claims.Role != "superadmin" {
		roleOpts := payloads.CheckUserOrganizationRoleOptions{
			UserID:         actorUserID,
			OrganizationID: orgID,
			RequiredRoles:  []string{"owner"},
		}
		hasPermission, err := as.OrgManager.CheckUserOrganizationRole(ctx, roleOpts)
		if err != nil {
			if errors.Is(err, payloads.ErrNotMember) {
				return echo.NewHTTPError(http.StatusForbidden, "You are not a member of this organization.")
			}
			log.Ctx(ctx).Error().Err(err).Str("user_id", actorUserID.String()).Str("org_id", orgID.String()).Msg("Error checking user role for delete organization")
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to verify permissions.")
		}
		if !hasPermission {
			return echo.NewHTTPError(http.StatusForbidden, "You do not have permission to delete this organization (only owner can delete).")
		}
	}

	err = as.OrgManager.DeleteOrganization(ctx, orgID)
	if err != nil {
		if errors.Is(err, payloads.ErrOrgNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		if errors.Is(err, payloads.ErrCannotDeleteDefaultOrg) {
			return echo.NewHTTPError(http.StatusBadRequest, "The default organization cannot be deleted.")
		}
		log.Ctx(ctx).Error().Err(err).Str("orgId", orgID.String()).Msg("Failed to delete organization")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete organization: "+err.Error())
	}

	return c.NoContent(http.StatusNoContent)
}

// ListMyOrganizationsHandler godoc
// @Summary List organizations for current user
// @Description Retrieves a list of organizations the authenticated user is an active member of. Each organization includes id, name, description, owner_user_id, is_default_org, image_url, theme_color, status, created_at, and updated_at.
// @Tags User Profile, Organizations
// @Produce json
// @Success 200 {array} payloads.OrganizationResponse "List of user's organizations with detailed fields"
// @Failure 401 {object} payloads.ErrorResponse "User claims not found (user)"
// @Failure 500 {object} payloads.ErrorResponse "Could not process user claims from token or Invalid user ID in token or Internal server error: Organization service not available. or Failed to retrieve your organizations"
// @Router /users/me/organizations [get]
func (as *AuthnService) ListMyOrganizationsHandler(c echo.Context) error {
	ctx := c.Request().Context()

	retrievedToken, ok := c.Get("user").(*jwt.Token)
	if !ok || retrievedToken == nil {
		log.Ctx(ctx).Error().Msg("ListMyOrganizationsHandler: JWT token not found using 'user' key.")
		return echo.NewHTTPError(http.StatusUnauthorized, "User claims not found (user)")
	}
	claims, ok := retrievedToken.Claims.(*token.AppClaims)
	if !ok || claims == nil {
		log.Ctx(ctx).Error().Msg("ListMyOrganizationsHandler: Failed to cast claims from token.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Could not process user claims from token")
	}
	userID, err := uuid.Parse(claims.RegisteredClaims.Subject)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subject", claims.RegisteredClaims.Subject).Msg("ListMyOrganizationsHandler: Failed to parse user ID.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Invalid user ID in token")
	}

	if as.OrgManager == nil {
		log.Ctx(ctx).Error().Msg("OrganizationManager is not initialized in AuthnService for ListMyOrganizationsHandler")
		return echo.NewHTTPError(http.StatusInternalServerError, "Internal server error: Organization service not available.")
	}

	orgs, err := as.OrgManager.ListUserOrganizations(ctx, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Msg("Failed to list user's organizations")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve your organizations: "+err.Error())
	}

	return c.JSON(http.StatusOK, payloads.ToOrganizationResponses(orgs))
}

// @Summary Join an organization
// @Description Allows the authenticated user to join a specific organization as a member.
// @Tags Organizations
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Success 200 {object} map[string]string "Successfully joined organization" // Or return membership details
// @Failure 400 {object} payloads.ErrorResponse "Already a member or invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 404 {object} payloads.ErrorResponse "Organization not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/join [post]
func (as *AuthnService) JoinOrganizationHandler(c echo.Context) error {
	ctx := c.Request().Context()

	retrievedToken, ok := c.Get("user").(*jwt.Token)
	if !ok || retrievedToken == nil {
		log.Ctx(ctx).Error().Msg("JoinOrganizationHandler: JWT token not found using 'user' key.")
		return echo.NewHTTPError(http.StatusUnauthorized, "User claims not found (user)")
	}
	claims, ok := retrievedToken.Claims.(*token.AppClaims)
	if !ok || claims == nil {
		log.Ctx(ctx).Error().Msg("JoinOrganizationHandler: Failed to cast claims from token.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Could not process user claims from token")
	}
	userID, err := uuid.Parse(claims.RegisteredClaims.Subject)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subject", claims.RegisteredClaims.Subject).Msg("JoinOrganizationHandler: Failed to parse user ID.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Invalid user ID in token")
	}

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("orgId_str", orgIDStr).Msg("Failed to parse orgId from path for join")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	if as.OrgManager == nil {
		log.Ctx(ctx).Error().Msg("OrganizationManager is not initialized in AuthnService for JoinOrganizationHandler")
		return echo.NewHTTPError(http.StatusInternalServerError, "Internal server error: Organization service not available.")
	}

	membership, err := as.OrgManager.JoinOrganization(ctx, userID, orgID)
	if err != nil {
		if errors.Is(err, payloads.ErrOrgNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found.")
		}
		if errors.Is(err, payloads.ErrAlreadyMember) {
			return echo.NewHTTPError(http.StatusConflict, "You are already a member of this organization.")
		}
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Str("org_id", orgID.String()).Msg("Failed to join organization")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to join organization: "+err.Error())
	}

	return c.JSON(http.StatusCreated, membership)
}

// @Summary Leave an organization
// @Description Allows the authenticated user to leave a specific organization.
// @Tags Organizations
// @Param orgId path string true "Organization ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} payloads.ErrorResponse "Not a member or invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 404 {object} payloads.ErrorResponse "Organization not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/leave [delete]
func (as *AuthnService) LeaveOrganizationHandler(c echo.Context) error {
	ctx := c.Request().Context()

	retrievedToken, ok := c.Get("user").(*jwt.Token)
	if !ok || retrievedToken == nil {
		log.Ctx(ctx).Error().Msg("LeaveOrganizationHandler: JWT token not found using 'user' key.")
		return echo.NewHTTPError(http.StatusUnauthorized, "User claims not found (user)")
	}
	claims, ok := retrievedToken.Claims.(*token.AppClaims)
	if !ok || claims == nil {
		log.Ctx(ctx).Error().Msg("LeaveOrganizationHandler: Failed to cast claims from token.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Could not process user claims from token")
	}
	userID, err := uuid.Parse(claims.RegisteredClaims.Subject)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subject", claims.RegisteredClaims.Subject).Msg("LeaveOrganizationHandler: Failed to parse user ID.")
		return echo.NewHTTPError(http.StatusInternalServerError, "Invalid user ID in token")
	}

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("orgId_str", orgIDStr).Msg("Failed to parse orgId from path for leave")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	if as.OrgManager == nil {
		log.Ctx(ctx).Error().Msg("OrganizationManager is not initialized in AuthnService for LeaveOrganizationHandler")
		return echo.NewHTTPError(http.StatusInternalServerError, "Internal server error: Organization service not available.")
	}

	err = as.OrgManager.LeaveOrganization(ctx, userID, orgID)
	if err != nil {
		if errors.Is(err, payloads.ErrOrgNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found.")
		}
		if errors.Is(err, payloads.ErrNotMember) {
			return echo.NewHTTPError(http.StatusForbidden, "You are not a member of this organization.")
		}
		if errors.Is(err, payloads.ErrCannotRemoveOwner) {
			return echo.NewHTTPError(http.StatusForbidden, "The organization owner cannot leave the organization. Transfer ownership first.")
		}
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Str("org_id", orgID.String()).Msg("Failed to leave organization")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to leave organization: "+err.Error())
	}

	return c.NoContent(http.StatusNoContent)
}

// TempOrgDebugHandler godoc
// @Summary Temporary JWT Debug Handler for Organizations
// @Description A temporary handler to debug JWT claims on organization routes.
// @Tags Debug, Organizations
// @Produce json
// @Success 200 {object} map[string]interface{} "UserID and debug message"
// @Failure 500 {object} map[string]string "Error processing claims or token not found"
// @Router /organizations/debugjwt [get]
func (as *AuthnService) TempOrgDebugHandler(c echo.Context) error {
	ctx := c.Request().Context()
	userID := "not found"

	// Try to get the JWT token from the context
	userToken, ok := c.Get("user").(*jwt.Token) // Using "user" as configured in main.go
	if ok && userToken != nil {
		if claims, ok := userToken.Claims.(*token.AppClaims); ok && claims != nil {
			userID = claims.RegisteredClaims.Subject
			log.Ctx(ctx).Info().Str("UserIDFromToken", userID).Msg("TempOrgDebugHandler: UserID retrieved from 'user' context key.")
			return c.JSON(http.StatusOK, map[string]interface{}{"UserIDFromToken": userID, "Message": "Retrieved from 'user' key"})
		} else {
			log.Ctx(ctx).Warn().Msg("TempOrgDebugHandler: Could not cast claims to *token.AppClaims from 'user' key.")
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Could not process claims from 'user' key"})
		}
	} else {
		log.Ctx(ctx).Warn().Msg("TempOrgDebugHandler: JWT token not found in context with key 'user'.")
		// Optionally, try the old key for debugging if it was recently changed
		// userTokenOld, okOld := c.Get("final_jwt_user_token").(*jwt.Token)
		// if okOld && userTokenOld != nil {
		// 	if claimsOld, okClaimsOld := userTokenOld.Claims.(*token.AppClaims); okClaimsOld && claimsOld != nil {
		// 		userID = claimsOld.RegisteredClaims.Subject
		// 		log.Ctx(ctx).Info().Str("UserIDFromToken", userID).Msg("TempOrgDebugHandler: UserID retrieved from 'final_jwt_user_token' context key (fallback debug).")
		// 		return c.JSON(http.StatusOK, map[string]interface{}{"UserIDFromToken": userID, "Message": "Retrieved from 'final_jwt_user_token' key (fallback debug)"})
		// 	}
		// }
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Token not found with key 'user'"})
	}
}

// UploadOrganizationLogoHandler godoc
// @Summary Upload organization logo
// @Description Uploads a logo for the organization. This action updates the organization's `image_url` and sets its `status` to `active`. Requires admin/owner permission.
// @Tags Organizations
// @Accept multipart/form-data
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param logo formData file true "Logo image file"
// @Success 200 {object} payloads.OrganizationResponse "Updated organization with new logo URL and active status"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request or file"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not admin/owner)"
// @Failure 404 {object} payloads.ErrorResponse "Organization not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/logo [post]
func (as *AuthnService) UploadOrganizationLogoHandler(c echo.Context) error {
	ctx := c.Request().Context()
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	retrievedToken, ok := c.Get("user").(*jwt.Token)
	if !ok || retrievedToken == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "User claims not found")
	}
	claims, ok := retrievedToken.Claims.(*token.AppClaims)
	if !ok || claims == nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Could not process user claims")
	}
	actorUserID, err := uuid.Parse(claims.RegisteredClaims.Subject)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Invalid user ID in token")
	}

	if as.OrgManager == nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Organization service not available.")
	}

	// Superadmin bypass
	if claims.Role != "superadmin" {
		// Check permissions: user must be owner or admin of the org
		roleOpts := payloads.CheckUserOrganizationRoleOptions{
			UserID:         actorUserID,
			OrganizationID: orgID,
			RequiredRoles:  []string{"owner", "admin"},
		}
		hasPermission, err := as.OrgManager.CheckUserOrganizationRole(ctx, roleOpts)
		if err != nil {
			if errors.Is(err, payloads.ErrNotMember) {
				return echo.NewHTTPError(http.StatusForbidden, "You are not a member of this organization.")
			}
			log.Ctx(ctx).Error().Err(err).Str("user_id", actorUserID.String()).Str("org_id", orgID.String()).Msg("Error checking user role for uploading logo")
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to verify permissions.")
		}
		if !hasPermission {
			return echo.NewHTTPError(http.StatusForbidden, "You do not have permission to upload a logo for this organization.")
		}
	}

	// --- File Handling --- //
	file, err := c.FormFile("logo")
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get 'logo' file from form")
		return echo.NewHTTPError(http.StatusBadRequest, "Missing 'logo' file in request: "+err.Error())
	}

	// Basic validation (can be expanded)
	const maxFileSize = 5 * 1024 * 1024 // 5MB
	if file.Size > maxFileSize {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("File size exceeds limit of %dMB", maxFileSize/1024/1024))
	}

	// Check file type (example for common image types)
	// More robust checking would involve inspecting magic bytes
	src, err := file.Open()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to open uploaded file")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to process uploaded file.")
	}
	defer src.Close()

	// Detect content type
	buff := make([]byte, 512) // http.DetectContentType only needs first 512 bytes
	_, err = src.Read(buff)
	if err != nil && err != io.EOF {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read from uploaded file for content type detection")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to process uploaded file content type.")
	}
	contentType := http.DetectContentType(buff)
	log.Ctx(ctx).Info().Str("detected_mime_type", contentType).Str("original_filename", file.Filename).Msg("Attempting to validate file type for logo upload.")

	allowedTypes := map[string]bool{
		"image/png":  true,
		"image/jpeg": true,
		"image/gif":  true,
		"image/webp": true,
	}
	if !allowedTypes[contentType] {
		// Fallback to check file extension if content type detection is generic
		if contentType == "application/octet-stream" {
			extension := strings.ToLower(filepath.Ext(file.Filename))
			switch extension {
			case ".png":
				contentType = "image/png"
			case ".jpg", ".jpeg":
				contentType = "image/jpeg"
			case ".gif":
				contentType = "image/gif"
			case ".webp":
				contentType = "image/webp"
			}
			log.Ctx(ctx).Info().Str("fallback_mime_type", contentType).Str("file_extension", extension).Msg("Using fallback MIME type based on file extension.")
		}

		if !allowedTypes[contentType] {
			log.Ctx(ctx).Warn().Str("detected_type", contentType).Msg("Uploaded file type not allowed for logo")
			return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid file type: %s. Allowed types: PNG, JPG, GIF, WebP", contentType))
		}
	}

	// Rewind the file to the beginning before passing it to the service
	_, err = src.Seek(0, io.SeekStart)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to seek uploaded file stream")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to process uploaded file.")
	}

	updatedOrg, err := as.OrgManager.UploadOrganizationLogo(ctx, orgID, actorUserID, file)
	if err != nil {
		if errors.Is(err, payloads.ErrOrgNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found.")
		}
		// Other specific errors from service can be handled here
		log.Ctx(ctx).Error().Err(err).Str("orgId", orgID.String()).Msg("Failed to upload organization logo")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to upload logo: "+err.Error())
	}

	return c.JSON(http.StatusOK, payloads.ToOrganizationResponse(updatedOrg))
}
