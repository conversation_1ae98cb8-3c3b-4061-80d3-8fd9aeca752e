import React, { useState, useEffect, useMemo } from 'react';
import { Typography, Divider, Avatar, Skeleton, message, Space, Button, List, Tag, Popconfirm } from 'antd';
import {
    UserOutlined,
    ClockCircleOutlined,
    CalendarOutlined,
    EyeInvisibleOutlined,
    EditOutlined,
    DownloadOutlined,
    DeleteOutlined,
    Arrow<PERSON>eftOutlined,
    EyeOutlined
} from '@ant-design/icons';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { postService } from '../../services/postService';
import { organizationService } from '../../services/organizationService';
import '../../styles/PostPage.css';
import { formatDate } from '../../utils/dateFormatter';
import { useTranslation } from 'react-i18next';
import ErrorPage from '../ErrorPage';
import { generateSafeHTML } from '../../utils/htmlUtils';
import { getFileIcon } from '../../config/fileIconConfig';

const { Title, Text } = Typography;

// Helper function to truncate middle of long strings
const truncateMiddle = (str, startLen = 20, endLen = 8) => {
    if (str.length <= startLen + endLen) return str;
    return `${str.slice(0, startLen)}...${str.slice(-endLen)}`;
};

// Helper function to trigger file download from a blob
const triggerBlobDownload = (blobData, fileName) => {
    try {
        const url = window.URL.createObjectURL(blobData);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        link.parentNode.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error("Error in triggerBlobDownload:", error);
        message.error('Failed to initiate download process.');
    }
};

// Helper function to get tag name based on current language
const getTagName = (tag, language) => {
    if (!tag) return '';
    if (language.startsWith('zh')) {
        if (language.includes('HK')) {
            return tag.name_zh_hk || tag.name_en || '';
        } else {
            return tag.name_zh_cn || tag.name_en || '';
        }
    } else {
        return tag.name_en || '';
    }
};

const PostPage = () => {
    const [post, setPost] = useState(null);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 576);
    const { postId } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const { t, i18n } = useTranslation();
    const { user, isLoggedIn } = useAuth();
    

    const isAdmin = user?.role === 'admin' || user?.role === 'super_admin';

    // Determine the return path based on current route
    const getReturnToPath = () => {
        const searchParams = new URLSearchParams(location.search);
        const returnTo = searchParams.get('returnTo');
        
        // If returnTo parameter is present, use it
        if (returnTo === 'management') {
            return '/posts-management';
        } else if (returnTo === 'details') {
            return `/posts/${postId}`;
        }
        
        // Otherwise check the current path to determine the correct return path
        if (location.pathname.includes('/posts-management')) {
            return '/posts-management';
        }
        
        return '/posts'; // Default return path
    };

    useEffect(() => {
        const handleResize = () => {
            setIsSmallScreen(window.innerWidth < 640);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Memoize the generated HTML to avoid unnecessary regeneration
    const safeHtml = useMemo(() => {
        if (!post?.content) return '';
        return generateSafeHTML(post.content);
    }, [post?.content]);

    // Memoize reading time calculation
    const readingTime = useMemo(() => {
        if (!safeHtml) return 0;
        const text = safeHtml.replace(/<[^>]*>/g, ''); // Strip HTML tags
        const wordsPerMinute = 200;
        const words = text.trim().split(/\s+/).length;
        return Math.ceil(words / wordsPerMinute);
    }, [safeHtml]);

    useEffect(() => {
        const fetchPostDetails = async () => {
            try {
                setLoading(true);
                const postDataFromApi = await postService.getPublicPostDetail(postId);

                // Check if post is hidden and user is not admin
                if (postDataFromApi.status === 'hidden' && !isAdmin) {
                    setError('403');
                    setLoading(false); // Ensure loading is stopped
                    return;
                }
                if (!postDataFromApi.organization_id) {
                    console.error("Post data is missing organization_id, which is required for media operations.");
                    // Potentially set an error or handle gracefully
                }

                const attachments = (postDataFromApi.media_items || []).map(item => ({
                    id: item.id,
                    name: item.file_name,
                    fileName: item.file_name,
                    url: item.file_path,
                    type: item.file_type,
                    file_size: item.file_size // included if needed by getFileIcon or display
                }));

                const coverImageItem = (postDataFromApi.media_items || []).find(item => item.file_type?.startsWith('image/'));
                const coverImageUrl = coverImageItem ? coverImageItem.file_path : null;

                setPost({
                    ...postDataFromApi,
                    attachments: attachments,
                    coverImage: coverImageUrl
                });
            } catch (error) {
                message.error(t('messages.fetchError'));
                let errorType = 'general';
                if (error.response) {
                    switch (error.response.status) {
                        case 403:
                            errorType = '403';
                            break;
                        case 404:
                            errorType = '404';
                            break;
                        case 500:
                            errorType = '500';
                            break;
                        default:
                            break;
                    }
                }
                setError(errorType);
            } finally {
                setLoading(false);
            }
        };

        fetchPostDetails();
    }, [postId, navigate, isAdmin, t]);

    const handleDeleteAttachment = async (attachmentId) => {
        if (!post || !post.organization_id || !attachmentId) {
            message.error(t('postPage.errors.deleteAttachmentInvalidParams'));
            return;
        }
        try {
            await organizationService.deletePostMedia(post.organization_id, postId, attachmentId);
            message.success(t('postPage.messages.attachmentDeleteSuccess'));
            // Update local state to remove the attachment
            setPost(prevPost => ({
                ...prevPost,
                attachments: prevPost.attachments.filter(att => att.id !== attachmentId)
            }));
        } catch (error) {
            console.error("Error deleting attachment:", error);
            message.error(t('postPage.errors.attachmentDeleteError') + (error.response?.data?.message ? `: ${error.response.data.message}` : ''));
        }
    };

    // Handle file download using postService.downloadPostMedia
    const handleFileDownload = async (fileToDownload) => {
        if (!fileToDownload || !post || !post.organization_id) {
            message.error(t('resourceList.messages.downloadErrorMissingInfo'));
            return;
        }

        const fileName = fileToDownload.file_name || fileToDownload.fileName || 'downloadedFile';
        
        message.loading({ content: t('resourceList.messages.downloadStartingFor', { fileName: truncateMiddle(fileName) }), key: `download-${fileToDownload.id}`, duration: 0 });

        try {
            // Use postService.downloadPostMedia instead of resourceService
            console.log(`Downloading file: orgId=${post.organization_id}, postId=${postId}, fileId=${fileToDownload.id}`);
            const response = await postService.downloadPostMedia(
                post.organization_id,
                postId,
                fileToDownload.id
            );

            if (response && response.data) { // Assuming response is Axios-like with data as Blob
                triggerBlobDownload(response.data, fileName);
                message.success({ content: t('resourceList.messages.downloadSuccessFor', { fileName: truncateMiddle(fileName) }), key: `download-${fileToDownload.id}`, duration: 3 });
            } else if (response instanceof Blob) { // If service directly returns a Blob
                triggerBlobDownload(response, fileName);
                message.success({ content: t('resourceList.messages.downloadSuccessFor', { fileName: truncateMiddle(fileName) }), key: `download-${fileToDownload.id}`, duration: 3 });
            } else {
                throw new Error('Invalid response data for file download.');
            }
        } catch (error) {
            console.error(`Error downloading file ${fileName}:`, error);
            message.error({ content: t('resourceList.messages.downloadFailedFor', { fileName: truncateMiddle(fileName) }), key: `download-${fileToDownload.id}`, duration: 3 });
        }
    };

    if (loading) {
        return (
            <div className="max-w-4xl mx-auto p-6">
                <article>
                    {/* Simple title skeleton */}
                    <div className="mb-6">
                        <Skeleton.Input active size="large" style={{ width: '70%', height: '32px' }} />
                    </div>

                    {/* Simple author info skeleton */}
                    <div className="flex items-center mb-6">
                        <Skeleton.Avatar active size={40} />
                        <div className="ml-3 flex-1">
                            <Skeleton.Input active size="small" style={{ width: 120 }} />
                        </div>
                    </div>

                    <Divider />

                    {/* Content skeleton */}
                    <div className="mb-8">
                        <Skeleton active paragraph={{ rows: 10 }} />
                    </div>

                    {/* Simple attachment skeleton */}
                    <Divider />
                    <div>
                        <Skeleton.Input active size="small" style={{ width: 150, marginBottom: 16 }} />
                        <Skeleton active paragraph={{ rows: 2 }} />
                    </div>
                </article>
            </div>
        );
    }

    if (error) {
        return (
            <div style={{ maxWidth: '800px', margin: '0 auto', padding: '24px' }}>
                <ErrorPage
                    type={error}
                    showBackHome={false}
                />
            </div>
        );
    }

    return (
        <div className="max-w-4xl mx-auto p-6">
            {post.status === 'hidden' && (
                <div className="bg-gray-100 border-l-4 border-gray-400 p-3 rounded flex items-center mb-4 gap-4">
                    <EyeInvisibleOutlined className="text-gray-500 text-lg" />
                    <div className="flex flex-col">
                        <span className="text-gray-700 font-medium">{t('postList.hiddenPost')}</span>
                        <span className="text-gray-500 text-sm">{t('postList.hiddenPostDescription')}</span>
                    </div>
                </div>
            )}
            <article>
                {/* Post Title and Admin Edit Button */}
                <div className="flex justify-between items-start mb-4">
                    <div className="flex flex-col gap-3">
                        <h1 className="text-3xl font-bold text-gray-900">
                            {post.title}
                        </h1>

                    </div>
                    {isAdmin && (
                        <Button
                            type="dashed"
                            icon={<EditOutlined />}
                            onClick={() => {
                                navigate(`/posts/${postId}/edit?returnTo=details`);
                            }}
                            className="flex-shrink-0"
                        >
                            {!isSmallScreen && t('postPage.buttons.edit')}
                        </Button>
                    )}
                </div>

                {/* Author info and metadata */}
                <div className="flex items-start space-x-4 mb-6 text-gray-700">
                    <div className="flex flex-col">
                        <Text strong className="text-base">{post.author_display_name}</Text>
                        <div className="text-gray-500 text-sm mt-1">
                            <Space>
                                <span className="flex items-center">
                                    <CalendarOutlined className="mr-1" /> {formatDate(post.updated_at, i18n.language)}
                                </span>
                                <span className="flex items-center">
                                    <ClockCircleOutlined className="mr-1" /> {readingTime}{t('postPage.readingTime.minRead')}
                                </span>
                            </Space>
                        </div>
                    </div>
                </div>

                {/* Cover Image */}
                {post.coverImage && (
                    <div className="relative mb-8 rounded-lg overflow-hidden">
                        <img
                            src={post.coverImage}
                            alt={post.title}
                            className="w-full h-auto object-cover"
                            style={{ maxHeight: '400px' }}
                        />
                    </div>
                )}

                <Divider />

                {/* Article content */}
                <div className="post-content">
                    <div dangerouslySetInnerHTML={{ __html: safeHtml }} />
                </div>

            </article>

            {/* Tags Section */}
            {post.tags && post.tags.length > 0 && (
                <>
                    <Divider className="my-8" />
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                            {t('postPage.sections.tags', 'Post Tags')}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {post.tags.map((tag) => (
                                <div key={tag.id} className="flex items-start">
                                    <div className="w-1 bg-gray-300 h-auto self-stretch mr-3 rounded-full"></div>
                                    <div>
                                        <span className="font-medium text-gray-900 text-base">{getTagName(tag, i18n.language)}</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </>
            )}

            {/* Attachments section */}
            {post.attachments && post.attachments.length > 0 && (
                <>
                    <Divider className="my-8" />
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                            {t('postPage.sections.attachments')}
                        </h3>
                        <List
                            dataSource={post.attachments}
                            renderItem={(attachment) => (
                                <div className="flex items-center justify-between p-3 mb-2 bg-white rounded-lg transition-colors duration-150 ease-in-out">
                                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                                        <div className="flex-shrink-0">
                                            {getFileIcon(attachment.name || attachment.fileName || attachment.url, attachment.type)}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {attachment.name || attachment.fileName || t('postPage.attachments.unnamedFile')}
                                            </p>
                                        </div>
                                    </div>
                                    <Space>
                                        {/* Preview button - always visible */}
                                        <Button
                                            type="dashed"
                                            icon={<EyeOutlined />}
                                            onClick={() => window.open(attachment.url, '_blank')}
                                        >
                                            {!isSmallScreen && (
                                                <span>{t('postPage.buttons.preview')}</span>
                                            )}
                                        </Button>
                                        
                                        {/* Download button - only for logged-in users */}
                                        {isLoggedIn && (
                                            <Button
                                                type="dashed"
                                                icon={<DownloadOutlined />}
                                                onClick={() => handleFileDownload(attachment)}
                                            >
                                                {!isSmallScreen && (
                                                    <span>{t('postPage.buttons.download')}</span>
                                                )}
                                            </Button>
                                        )}
                                        
                                        {/* Delete button - only for admins */}
                                        {isAdmin && (
                                            <Popconfirm
                                                title={t('postPage.deleteConfirm.attachmentTitle')}
                                                onConfirm={() => handleDeleteAttachment(attachment.id)}
                                                okText={t('postPage.deleteConfirm.okText')}
                                                cancelText={t('postPage.deleteConfirm.cancelText')}
                                            >
                                                <Button
                                                    danger
                                                    icon={<DeleteOutlined />}
                                                    type="dashed"
                                                    size={isSmallScreen ? 'small' : 'middle'}
                                                >
                                                    {!isSmallScreen && (
                                                        <span>{t('postPage.buttons.delete')}</span>
                                                    )}
                                                </Button>
                                            </Popconfirm>
                                        )}
                                    </Space>
                                </div>
                            )}
                        />
                    </div>
                </>
            )}
        </div>
    );
};

export default PostPage;
