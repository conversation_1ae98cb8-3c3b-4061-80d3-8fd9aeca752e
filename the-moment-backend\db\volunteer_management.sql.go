// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: volunteer_management.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const countOrgVolunteerApplicationsWithFilters = `-- name: CountOrgVolunteerApplicationsWithFilters :one
SELECT COUNT(*)
FROM user_volunteer_applications app
WHERE app.organization_id = $1
AND ($2::varchar IS NULL OR app.status::text = $2::varchar)
`

type CountOrgVolunteerApplicationsWithFiltersParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Status         *string   `db:"status" json:"status"`
}

func (q *Queries) CountOrgVolunteerApplicationsWithFilters(ctx context.Context, arg CountOrgVolunteerApplicationsWithFiltersParams) (int64, error) {
	row := q.db.QueryRow(ctx, countOrgVolunteerApplicationsWithFilters, arg.OrganizationID, arg.Status)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createVolunteerApplication = `-- name: CreateVolunteerApplication :one
INSERT INTO user_volunteer_applications (
    user_id,
    organization_id,
    motivation
) VALUES (
    $1, $2, $3
)
RETURNING id, user_id, organization_id, application_date, motivation, reviewed_by_user_id, review_date, admin_notes, created_at, updated_at, status
`

type CreateVolunteerApplicationParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Motivation     *string   `db:"motivation" json:"motivation"`
}

func (q *Queries) CreateVolunteerApplication(ctx context.Context, arg CreateVolunteerApplicationParams) (UserVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, createVolunteerApplication, arg.UserID, arg.OrganizationID, arg.Motivation)
	var i UserVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationDate,
		&i.Motivation,
		&i.ReviewedByUserID,
		&i.ReviewDate,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
	)
	return i, err
}

const getApprovedEventVolunteerApplication = `-- name: GetApprovedEventVolunteerApplication :one
SELECT id, event_id, user_id, organization_id, application_notes_by_user, admin_review_notes, applied_at, reviewed_at, reviewed_by_user_id, created_at, updated_at, status, attended_at
FROM event_volunteer_applications
WHERE user_id = $1 AND event_id = $2 AND status = 'approved'
LIMIT 1
`

type GetApprovedEventVolunteerApplicationParams struct {
	UserID  uuid.UUID `db:"user_id" json:"user_id"`
	EventID uuid.UUID `db:"event_id" json:"event_id"`
}

func (q *Queries) GetApprovedEventVolunteerApplication(ctx context.Context, arg GetApprovedEventVolunteerApplicationParams) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, getApprovedEventVolunteerApplication, arg.UserID, arg.EventID)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}

const getEventVolunteerApplicationDetailsByID = `-- name: GetEventVolunteerApplicationDetailsByID :one
SELECT id, event_id, user_id, organization_id, application_notes_by_user, admin_review_notes, applied_at, reviewed_at, reviewed_by_user_id, created_at, updated_at, status, attended_at FROM event_volunteer_applications
WHERE id = $1
`

func (q *Queries) GetEventVolunteerApplicationDetailsByID(ctx context.Context, id uuid.UUID) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, getEventVolunteerApplicationDetailsByID, id)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}

const getUserVolunteerApplicationByID = `-- name: GetUserVolunteerApplicationByID :one
SELECT id, user_id, organization_id, application_date, motivation, reviewed_by_user_id, review_date, admin_notes, created_at, updated_at, status FROM user_volunteer_applications
WHERE id = $1 AND user_id = $2
`

type GetUserVolunteerApplicationByIDParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	UserID uuid.UUID `db:"user_id" json:"user_id"`
}

func (q *Queries) GetUserVolunteerApplicationByID(ctx context.Context, arg GetUserVolunteerApplicationByIDParams) (UserVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, getUserVolunteerApplicationByID, arg.ID, arg.UserID)
	var i UserVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationDate,
		&i.Motivation,
		&i.ReviewedByUserID,
		&i.ReviewDate,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
	)
	return i, err
}

const getUserVolunteerApplicationForOrganization = `-- name: GetUserVolunteerApplicationForOrganization :one
SELECT id, user_id, organization_id, application_date, motivation, reviewed_by_user_id, review_date, admin_notes, created_at, updated_at, status FROM user_volunteer_applications
WHERE user_id = $1 AND organization_id = $2
ORDER BY application_date DESC
LIMIT 1
`

type GetUserVolunteerApplicationForOrganizationParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
}

func (q *Queries) GetUserVolunteerApplicationForOrganization(ctx context.Context, arg GetUserVolunteerApplicationForOrganizationParams) (UserVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, getUserVolunteerApplicationForOrganization, arg.UserID, arg.OrganizationID)
	var i UserVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationDate,
		&i.Motivation,
		&i.ReviewedByUserID,
		&i.ReviewDate,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
	)
	return i, err
}

const getVolunteerApplicationDetailsForAdmin = `-- name: GetVolunteerApplicationDetailsForAdmin :one
SELECT 
    app.id, app.user_id, app.organization_id, app.application_date, app.motivation, app.reviewed_by_user_id, app.review_date, app.admin_notes, app.created_at, app.updated_at, app.status,
    u.display_name as applicant_display_name,
    u.email as applicant_email,
    u.phone as applicant_phone_number,
    reviewer.display_name as reviewer_display_name
FROM user_volunteer_applications app
JOIN users u ON app.user_id = u.id
LEFT JOIN users reviewer ON app.reviewed_by_user_id = reviewer.id
WHERE app.id = $1 AND app.organization_id = $2
`

type GetVolunteerApplicationDetailsForAdminParams struct {
	ID             uuid.UUID `db:"id" json:"id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
}

type GetVolunteerApplicationDetailsForAdminRow struct {
	ID                   uuid.UUID             `db:"id" json:"id"`
	UserID               uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID       uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationDate      time.Time             `db:"application_date" json:"application_date"`
	Motivation           *string               `db:"motivation" json:"motivation"`
	ReviewedByUserID     *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	ReviewDate           *time.Time            `db:"review_date" json:"review_date"`
	AdminNotes           *string               `db:"admin_notes" json:"admin_notes"`
	CreatedAt            time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt            time.Time             `db:"updated_at" json:"updated_at"`
	Status               ApplicationStatusEnum `db:"status" json:"status"`
	ApplicantDisplayName string                `db:"applicant_display_name" json:"applicant_display_name"`
	ApplicantEmail       *string               `db:"applicant_email" json:"applicant_email"`
	ApplicantPhoneNumber *string               `db:"applicant_phone_number" json:"applicant_phone_number"`
	ReviewerDisplayName  *string               `db:"reviewer_display_name" json:"reviewer_display_name"`
}

func (q *Queries) GetVolunteerApplicationDetailsForAdmin(ctx context.Context, arg GetVolunteerApplicationDetailsForAdminParams) (GetVolunteerApplicationDetailsForAdminRow, error) {
	row := q.db.QueryRow(ctx, getVolunteerApplicationDetailsForAdmin, arg.ID, arg.OrganizationID)
	var i GetVolunteerApplicationDetailsForAdminRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationDate,
		&i.Motivation,
		&i.ReviewedByUserID,
		&i.ReviewDate,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.ApplicantDisplayName,
		&i.ApplicantEmail,
		&i.ApplicantPhoneNumber,
		&i.ReviewerDisplayName,
	)
	return i, err
}

const listOrgVolunteerApplicationsWithFilters = `-- name: ListOrgVolunteerApplicationsWithFilters :many
SELECT
    app.id, app.user_id, app.organization_id, app.application_date, app.motivation, app.reviewed_by_user_id, app.review_date, app.admin_notes, app.created_at, app.updated_at, app.status,
    u.display_name as applicant_display_name,
    u.email as applicant_email,
    u.phone as applicant_phone_number,
    reviewer.display_name as reviewer_display_name
FROM user_volunteer_applications app
JOIN users u ON app.user_id = u.id
LEFT JOIN users reviewer ON app.reviewed_by_user_id = reviewer.id
WHERE app.organization_id = $1
AND ($2::varchar IS NULL OR app.status::text = $2::varchar)
ORDER BY app.application_date DESC
LIMIT $4
OFFSET $3
`

type ListOrgVolunteerApplicationsWithFiltersParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Status         *string   `db:"status" json:"status"`
	OffsetVal      int32     `db:"offset_val" json:"offset_val"`
	LimitVal       int32     `db:"limit_val" json:"limit_val"`
}

type ListOrgVolunteerApplicationsWithFiltersRow struct {
	ID                   uuid.UUID             `db:"id" json:"id"`
	UserID               uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID       uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationDate      time.Time             `db:"application_date" json:"application_date"`
	Motivation           *string               `db:"motivation" json:"motivation"`
	ReviewedByUserID     *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	ReviewDate           *time.Time            `db:"review_date" json:"review_date"`
	AdminNotes           *string               `db:"admin_notes" json:"admin_notes"`
	CreatedAt            time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt            time.Time             `db:"updated_at" json:"updated_at"`
	Status               ApplicationStatusEnum `db:"status" json:"status"`
	ApplicantDisplayName string                `db:"applicant_display_name" json:"applicant_display_name"`
	ApplicantEmail       *string               `db:"applicant_email" json:"applicant_email"`
	ApplicantPhoneNumber *string               `db:"applicant_phone_number" json:"applicant_phone_number"`
	ReviewerDisplayName  *string               `db:"reviewer_display_name" json:"reviewer_display_name"`
}

func (q *Queries) ListOrgVolunteerApplicationsWithFilters(ctx context.Context, arg ListOrgVolunteerApplicationsWithFiltersParams) ([]ListOrgVolunteerApplicationsWithFiltersRow, error) {
	rows, err := q.db.Query(ctx, listOrgVolunteerApplicationsWithFilters,
		arg.OrganizationID,
		arg.Status,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrgVolunteerApplicationsWithFiltersRow{}
	for rows.Next() {
		var i ListOrgVolunteerApplicationsWithFiltersRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationDate,
			&i.Motivation,
			&i.ReviewedByUserID,
			&i.ReviewDate,
			&i.AdminNotes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.ApplicantDisplayName,
			&i.ApplicantEmail,
			&i.ApplicantPhoneNumber,
			&i.ReviewerDisplayName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPendingVolunteerApplicationsForOrganization = `-- name: ListPendingVolunteerApplicationsForOrganization :many
SELECT 
    app.id, app.user_id, app.organization_id, app.application_date, app.motivation, app.reviewed_by_user_id, app.review_date, app.admin_notes, app.created_at, app.updated_at, app.status,
    u.display_name as applicant_display_name,
    u.email as applicant_email,
    u.phone as applicant_phone_number
FROM user_volunteer_applications app
JOIN users u ON app.user_id = u.id
WHERE app.organization_id = $1 AND app.status = 'pending'
ORDER BY app.application_date ASC
`

type ListPendingVolunteerApplicationsForOrganizationRow struct {
	ID                   uuid.UUID             `db:"id" json:"id"`
	UserID               uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID       uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationDate      time.Time             `db:"application_date" json:"application_date"`
	Motivation           *string               `db:"motivation" json:"motivation"`
	ReviewedByUserID     *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	ReviewDate           *time.Time            `db:"review_date" json:"review_date"`
	AdminNotes           *string               `db:"admin_notes" json:"admin_notes"`
	CreatedAt            time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt            time.Time             `db:"updated_at" json:"updated_at"`
	Status               ApplicationStatusEnum `db:"status" json:"status"`
	ApplicantDisplayName string                `db:"applicant_display_name" json:"applicant_display_name"`
	ApplicantEmail       *string               `db:"applicant_email" json:"applicant_email"`
	ApplicantPhoneNumber *string               `db:"applicant_phone_number" json:"applicant_phone_number"`
}

func (q *Queries) ListPendingVolunteerApplicationsForOrganization(ctx context.Context, organizationID uuid.UUID) ([]ListPendingVolunteerApplicationsForOrganizationRow, error) {
	rows, err := q.db.Query(ctx, listPendingVolunteerApplicationsForOrganization, organizationID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPendingVolunteerApplicationsForOrganizationRow{}
	for rows.Next() {
		var i ListPendingVolunteerApplicationsForOrganizationRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationDate,
			&i.Motivation,
			&i.ReviewedByUserID,
			&i.ReviewDate,
			&i.AdminNotes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.ApplicantDisplayName,
			&i.ApplicantEmail,
			&i.ApplicantPhoneNumber,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserVolunteerApplications = `-- name: ListUserVolunteerApplications :many
SELECT id, user_id, organization_id, application_date, motivation, reviewed_by_user_id, review_date, admin_notes, created_at, updated_at, status FROM user_volunteer_applications
WHERE user_id = $1
AND ($2::application_status_enum IS NULL OR status = $2)
ORDER BY application_date DESC
`

type ListUserVolunteerApplicationsParams struct {
	UserID uuid.UUID                 `db:"user_id" json:"user_id"`
	Status NullApplicationStatusEnum `db:"status" json:"status"`
}

func (q *Queries) ListUserVolunteerApplications(ctx context.Context, arg ListUserVolunteerApplicationsParams) ([]UserVolunteerApplication, error) {
	rows, err := q.db.Query(ctx, listUserVolunteerApplications, arg.UserID, arg.Status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []UserVolunteerApplication{}
	for rows.Next() {
		var i UserVolunteerApplication
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationDate,
			&i.Motivation,
			&i.ReviewedByUserID,
			&i.ReviewDate,
			&i.AdminNotes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const markEventVolunteerAttended = `-- name: MarkEventVolunteerAttended :exec
UPDATE event_volunteer_applications
SET attended_at = NOW(), updated_at = NOW()
WHERE user_id = $1 AND event_id = $2 AND status = 'approved'
`

type MarkEventVolunteerAttendedParams struct {
	UserID  uuid.UUID `db:"user_id" json:"user_id"`
	EventID uuid.UUID `db:"event_id" json:"event_id"`
}

func (q *Queries) MarkEventVolunteerAttended(ctx context.Context, arg MarkEventVolunteerAttendedParams) error {
	_, err := q.db.Exec(ctx, markEventVolunteerAttended, arg.UserID, arg.EventID)
	return err
}

const setUserVolunteerApplicationStatusToWithdrawnByUser = `-- name: SetUserVolunteerApplicationStatusToWithdrawnByUser :one
UPDATE user_volunteer_applications
SET
    status = 'withdrawn', -- Directly set to withdrawn
    updated_at = NOW(),
    -- Clear review fields if withdrawal means it's no longer under review
    reviewed_by_user_id = NULL,
    review_date = NULL,
    admin_notes = NULL
WHERE id = $1 AND user_id = $2 AND status IN ('pending', 'approved') -- Define withdrawable states
RETURNING id, user_id, organization_id, application_date, motivation, reviewed_by_user_id, review_date, admin_notes, created_at, updated_at, status
`

type SetUserVolunteerApplicationStatusToWithdrawnByUserParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	UserID uuid.UUID `db:"user_id" json:"user_id"`
}

func (q *Queries) SetUserVolunteerApplicationStatusToWithdrawnByUser(ctx context.Context, arg SetUserVolunteerApplicationStatusToWithdrawnByUserParams) (UserVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, setUserVolunteerApplicationStatusToWithdrawnByUser, arg.ID, arg.UserID)
	var i UserVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationDate,
		&i.Motivation,
		&i.ReviewedByUserID,
		&i.ReviewDate,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
	)
	return i, err
}

const updateVolunteerApplicationStatus = `-- name: UpdateVolunteerApplicationStatus :one
UPDATE user_volunteer_applications
SET 
    status = $1,
    reviewed_by_user_id = $2,
    review_date = NOW(),
    admin_notes = $3,
    updated_at = NOW()
WHERE id = $4 AND organization_id = $5
RETURNING id, user_id, organization_id, application_date, motivation, reviewed_by_user_id, review_date, admin_notes, created_at, updated_at, status
`

type UpdateVolunteerApplicationStatusParams struct {
	Status           ApplicationStatusEnum `db:"status" json:"status"`
	ReviewedByUserID *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes       *string               `db:"admin_notes" json:"admin_notes"`
	ID               uuid.UUID             `db:"id" json:"id"`
	OrganizationID   uuid.UUID             `db:"organization_id" json:"organization_id"`
}

func (q *Queries) UpdateVolunteerApplicationStatus(ctx context.Context, arg UpdateVolunteerApplicationStatusParams) (UserVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, updateVolunteerApplicationStatus,
		arg.Status,
		arg.ReviewedByUserID,
		arg.AdminNotes,
		arg.ID,
		arg.OrganizationID,
	)
	var i UserVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationDate,
		&i.Motivation,
		&i.ReviewedByUserID,
		&i.ReviewDate,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
	)
	return i, err
}
