ALTER TABLE "posts"
ADD COLUMN "cover_image_url" TEXT,
ADD COLUMN "is_hidden" BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN "is_for_banner" BOOLEAN NOT NULL DEFAULT FALSE;

COMMENT ON COLUMN "posts"."cover_image_url" IS 'Stores the URL for the post\'s cover image.';
COMMENT ON COLUMN "posts"."is_hidden" IS 'Controls the visibility of the post.';
COMMENT ON COLUMN "posts"."is_for_banner" IS 'Indicates if the post is intended to be used as a banner.';

COMMENT ON TABLE "posts" IS 'Reverted removal of cover_image_url, is_hidden, and is_for_banner.'; 