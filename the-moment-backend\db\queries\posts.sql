-- name: CreatePost :one
INSERT INTO posts (
    organization_id,
    author_id,
    title,
    slug,
    content,
    status,
    published_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) RETURNING *;

-- name: GetPostByID :one
SELECT * FROM posts
WHERE id = $1 LIMIT 1;

-- name: GetPostBySlug :one
SELECT * FROM posts
WHERE slug = $1 LIMIT 1;

-- name: ListPostsByOrganization :many
SELECT * FROM posts
WHERE organization_id = $1
ORDER BY created_at DESC
LIMIT $2 OFFSET $3;

-- name: CountPostsByOrganization :one
SELECT COUNT(*) FROM posts
WHERE organization_id = $1;

-- name: ListOrgPostsWithMedia :many
SELECT
    p.id,
    p.organization_id,
    p.author_id,
    p.title,
    p.slug,
    p.content,
    p.status,
    p.published_at,
    p.created_at,
    p.updated_at,
    u.display_name AS author_display_name,
    pm.id AS post_media_item_id,
    pm.post_id AS post_media_item_post_id,
    pm.file_name AS post_media_item_file_name,
    pm.file_path AS post_media_item_file_path,
    pm.file_type AS post_media_item_file_type,
    pm.file_size AS post_media_item_file_size,
    pm.uploaded_at AS post_media_item_uploaded_at
FROM
    posts p
JOIN
    users u ON p.author_id = u.id
LEFT JOIN
    post_media_items pm ON p.id = pm.post_id
WHERE
    p.organization_id = $1
ORDER BY
    p.created_at DESC, p.id ASC, pm.uploaded_at ASC
LIMIT $2 OFFSET $3;

-- name: ListPublishedPosts :many
SELECT * FROM posts
WHERE status = 'published'
ORDER BY published_at DESC
LIMIT $1 OFFSET $2;

-- name: CountPublishedPosts :one
SELECT COUNT(p.id)
FROM posts p
WHERE p.status = 'published'
  AND (
    (sqlc.narg(organization_id)::UUID IS NULL AND sqlc.narg(organization_id2)::UUID IS NULL) OR
    (sqlc.narg(organization_id)::UUID IS NOT NULL AND p.organization_id = sqlc.narg(organization_id)::UUID) OR
    (sqlc.narg(organization_id2)::UUID IS NOT NULL AND p.organization_id = sqlc.narg(organization_id2)::UUID)
  )
  AND (sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR p.published_at >= sqlc.narg(start_date)::TIMESTAMPTZ)
  AND (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL OR p.published_at <= sqlc.narg(end_date)::TIMESTAMPTZ)
  AND (sqlc.narg(search_term)::TEXT IS NULL OR p.title ILIKE '%' || sqlc.narg(search_term)::TEXT || '%')
  AND (cardinality(sqlc.narg(tag_ids)::UUID[]) = 0 OR
       EXISTS (SELECT 1 FROM post_post_tags e_ppt WHERE e_ppt.post_id = p.id AND e_ppt.tag_id = ANY(sqlc.narg(tag_ids)::UUID[]))
      );

-- name: ListPublishedPostsWithAuthor :many
SELECT
    p.id,
    p.organization_id,
    p.title,
    p.slug,
    p.status,
    p.published_at,
    p.created_at,
    p.updated_at,
    p.content,
    p.author_id,
    u.display_name AS author_display_name,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', pt.id, 'name_en', pt.name_en, 'name_zh_hk', pt.name_zh_hk, 'name_zh_cn', pt.name_zh_cn, 'description_en', pt.description_en, 'description_zh_hk', pt.description_zh_hk, 'description_zh_cn', pt.description_zh_cn)) FILTER (WHERE pt.id IS NOT NULL), '[]') AS tags
FROM posts p
JOIN users u ON p.author_id = u.id
LEFT JOIN post_post_tags ppt ON p.id = ppt.post_id
LEFT JOIN post_tags pt ON ppt.tag_id = pt.id
WHERE p.status = 'published'
  AND (
    (sqlc.narg(organization_id)::UUID IS NULL AND sqlc.narg(organization_id2)::UUID IS NULL) OR
    (sqlc.narg(organization_id)::UUID IS NOT NULL AND p.organization_id = sqlc.narg(organization_id)::UUID) OR
    (sqlc.narg(organization_id2)::UUID IS NOT NULL AND p.organization_id = sqlc.narg(organization_id2)::UUID)
  )
  AND (sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR p.published_at >= sqlc.narg(start_date)::TIMESTAMPTZ)
  AND (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL OR p.published_at <= sqlc.narg(end_date)::TIMESTAMPTZ)
  AND (sqlc.narg(search_term)::TEXT IS NULL OR p.title ILIKE '%' || sqlc.narg(search_term)::TEXT || '%')
  AND (cardinality(sqlc.narg(tag_ids)::UUID[]) = 0 OR
       EXISTS (SELECT 1 FROM post_post_tags e_ppt_filter WHERE e_ppt_filter.post_id = p.id AND e_ppt_filter.tag_id = ANY(sqlc.narg(tag_ids)::UUID[]))
      )
GROUP BY p.id, u.display_name
ORDER BY p.published_at DESC
LIMIT sqlc.arg(limit_val) OFFSET sqlc.arg(offset_val);

-- name: CountPublishedPostsByOrganization :one
SELECT COUNT(*) FROM posts
WHERE organization_id = $1 AND status = 'published';

-- name: ListPublishedPostsByOrganizationWithMedia :many
SELECT
    sqlc.embed(p),
    pm.id AS post_media_item_id,
    pm.post_id AS post_media_item_post_id,
    pm.file_name AS post_media_item_file_name,
    pm.file_path AS post_media_item_file_path,
    pm.file_type AS post_media_item_file_type,
    pm.file_size AS post_media_item_file_size,
    pm.uploaded_at AS post_media_item_uploaded_at
FROM
    posts p
LEFT JOIN
    post_media_items pm ON p.id = pm.post_id
WHERE
    p.organization_id = $1 AND p.status = 'published'
ORDER BY
    p.published_at DESC, p.id ASC, pm.uploaded_at ASC -- Ensuring stable sort for posts and their media
LIMIT $2 OFFSET $3;

-- name: UpdatePost :one
UPDATE posts
SET
    title = COALESCE(sqlc.narg('title'), title),
    slug = COALESCE(sqlc.narg('slug'), slug),
    content = COALESCE(sqlc.narg('content'), content),
    status = COALESCE(sqlc.narg('status'), status),
    published_at = COALESCE(sqlc.narg('published_at'), published_at),
    updated_by = sqlc.narg('updated_by'),
    updated_at = now()
WHERE id = sqlc.arg('id')
RETURNING *;

-- name: DeletePost :exec
DELETE FROM posts
WHERE id = $1;

-- name: CreatePostMediaItem :one
INSERT INTO post_media_items (
    post_id,
    file_name,
    file_path,
    file_type,
    file_size,
    is_banner
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING *;

-- name: GetPostMediaItemByID :one
SELECT * FROM post_media_items
WHERE id = $1 LIMIT 1;

-- name: ListMediaItemsByPost :many
SELECT * FROM post_media_items
WHERE post_id = $1
ORDER BY is_banner DESC, uploaded_at ASC;

-- name: DeletePostMediaItem :exec
DELETE FROM post_media_items
WHERE id = $1;

-- name: GetPostWithMedia :many
SELECT
    sqlc.embed(p),
    pm.id AS post_media_item_id,
    pm.post_id AS post_media_item_post_id,
    pm.file_name AS post_media_item_file_name,
    pm.file_path AS post_media_item_file_path,
    pm.file_type AS post_media_item_file_type,
    pm.file_size AS post_media_item_file_size,
    pm.uploaded_at AS post_media_item_uploaded_at
FROM
    posts p
LEFT JOIN
    post_media_items pm ON p.id = pm.post_id
WHERE
    p.id = $1;

-- name: ListPublishedPostsWithMedia :many
SELECT
    sqlc.embed(p),
    pm.id AS post_media_item_id,
    pm.post_id AS post_media_item_post_id,
    pm.file_name AS post_media_item_file_name,
    pm.file_path AS post_media_item_file_path,
    pm.file_type AS post_media_item_file_type,
    pm.file_size AS post_media_item_file_size,
    pm.uploaded_at AS post_media_item_uploaded_at
FROM
    posts p
LEFT JOIN
    post_media_items pm ON p.id = pm.post_id
WHERE
    p.status = 'published'
ORDER BY
    p.published_at DESC
LIMIT $1 OFFSET $2;

-- name: GetPostDetailsWithTags :one
SELECT
    p.*,
    u.display_name as author_display_name,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', pt.id, 'name_en', pt.name_en, 'name_zh_hk', pt.name_zh_hk, 'name_zh_cn', pt.name_zh_cn, 'description_en', pt.description_en, 'description_zh_hk', pt.description_zh_hk, 'description_zh_cn', pt.description_zh_cn)) FILTER (WHERE pt.id IS NOT NULL), '[]') AS tags
FROM posts p
JOIN users u ON p.author_id = u.id
LEFT JOIN post_post_tags ppt ON p.id = ppt.post_id
LEFT JOIN post_tags pt ON ppt.tag_id = pt.id
WHERE p.id = $1
GROUP BY p.id, u.display_name;

-- name: ListPostsByOrganizationWithTags :many
SELECT
    p.*,
    u.display_name as author_display_name,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', pt.id, 'name_en', pt.name_en, 'name_zh_hk', pt.name_zh_hk, 'name_zh_cn', pt.name_zh_cn, 'description_en', pt.description_en, 'description_zh_hk', pt.description_zh_hk, 'description_zh_cn', pt.description_zh_cn)) FILTER (WHERE pt.id IS NOT NULL), '[]') AS tags
FROM posts p
JOIN users u ON p.author_id = u.id
LEFT JOIN post_post_tags ppt ON p.id = ppt.post_id -- For collecting tags for json_agg
LEFT JOIN post_tags pt ON ppt.tag_id = pt.id     -- For collecting tags for json_agg
WHERE p.organization_id = $1
  AND (sqlc.narg(status)::TEXT IS NULL OR p.status = sqlc.narg(status)::TEXT) -- Status can still be narg
  AND (sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR p.created_at >= sqlc.narg(start_date)::TIMESTAMPTZ)
  AND (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL OR p.created_at <= sqlc.narg(end_date)::TIMESTAMPTZ)
  AND (sqlc.narg(search_term)::TEXT IS NULL OR (p.title ILIKE '%' || sqlc.narg(search_term)::TEXT || '%' OR p.content::TEXT ILIKE '%' || sqlc.narg(search_term)::TEXT || '%'))
  AND (
    cardinality(sqlc.arg(tag_ids)::UUID[]) = 0 OR -- Use sqlc.arg, rely on cardinality for empty check
    EXISTS (
      SELECT 1
      FROM post_post_tags e_ppt
      JOIN post_tags e_pt ON e_ppt.tag_id = e_pt.id
      WHERE e_ppt.post_id = p.id AND e_pt.id = ANY(sqlc.arg(tag_ids)::UUID[]) -- Use sqlc.arg
    )
  )
GROUP BY p.id, u.display_name
ORDER BY p.created_at DESC
LIMIT $2 OFFSET $3;

-- name: CountPostsByOrganizationWithTags :one
SELECT COUNT(DISTINCT p.id)
FROM posts p
LEFT JOIN post_post_tags ppt ON p.id = ppt.post_id -- Join for tag filtering
LEFT JOIN post_tags pt ON ppt.tag_id = pt.id     -- Join for tag filtering
WHERE p.organization_id = $1
  AND (sqlc.narg(status)::TEXT IS NULL OR p.status = sqlc.narg(status)::TEXT) -- Status can still be narg
  AND (sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR p.created_at >= sqlc.narg(start_date)::TIMESTAMPTZ)
  AND (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL OR p.created_at <= sqlc.narg(end_date)::TIMESTAMPTZ)
  AND (sqlc.narg(search_term)::TEXT IS NULL OR (p.title ILIKE '%' || sqlc.narg(search_term)::TEXT || '%' OR p.content::TEXT ILIKE '%' || sqlc.narg(search_term)::TEXT || '%'))
  AND (
    cardinality(sqlc.arg(tag_ids)::UUID[]) = 0 OR -- Use sqlc.arg, rely on cardinality for empty check
    EXISTS (
      SELECT 1
      FROM post_post_tags e_ppt
      JOIN post_tags e_pt ON e_ppt.tag_id = e_pt.id
      WHERE e_ppt.post_id = p.id AND e_pt.id = ANY(sqlc.arg(tag_ids)::UUID[]) -- Use sqlc.arg
    )
  );

-- name: GetScheduledPostsToPublish :many
SELECT id FROM posts WHERE status = 'draft' AND published_at IS NOT NULL AND published_at <= NOW();

-- name: SetPostStatusToPublished :exec
UPDATE posts SET status = 'published', updated_at = NOW() WHERE id = $1;

-- name: DeletePostMediaByPostID :exec
DELETE FROM post_media_items WHERE post_id = $1;

-- name: ListMediaItemsByPostIDs :many
SELECT * FROM post_media_items
WHERE post_id = ANY(sqlc.arg(post_ids)::UUID[])
ORDER BY post_id, is_banner DESC, uploaded_at ASC; -- Order by post_id for easier grouping, then by banner status and uploaded_at

-- name: UnsetBannerForPostMediaItems :exec
UPDATE post_media_items
SET is_banner = FALSE
WHERE post_id = $1 AND is_banner = TRUE;

-- name: SetBannerForPostMediaItem :one
UPDATE post_media_items
SET is_banner = TRUE
WHERE id = $1 AND post_id = $2
RETURNING *;