import { format } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { zhHK } from 'date-fns/locale';
import { enUS } from 'date-fns/locale';

const TIMEZONE = 'Asia/Hong_Kong';

export const formatDate = (date, language = 'en', timeOnly = false) => {
    if (!date) return '';
    
    const locale = language === 'zh-HK' ? zhHK : enUS;
    
    try {
        // If timeOnly is true, only format the time part
        if (timeOnly) {
            return formatInTimeZone(date, TIMEZONE, 'HH:mm', { locale });
        }
        
        // 根據語言使用不同的格式
        const formatString = language === 'zh-HK' 
            ? 'yyyy年MM月dd日 HH:mm' 
            : 'MMM d, yyyy HH:mm';
            
        return formatInTimeZone(date, TIMEZONE, formatString, { locale });
    } catch (error) {
        console.error('Date formatting error:', error);
        return '';
    }
};

export const formatSimpleDateTime = (date) => {
    if (!date) return '';
    
    try {
        return formatInTimeZone(date, TIMEZONE, 'yyyy-MM-dd HH:mm');
    } catch (error) {
        console.error('Date formatting error:', error);
        return '';
    }
};

// New function to format date with time range
export const formatDateTimeRange = (startDate, endDate, language = 'en') => {
    if (!startDate || !endDate) return '';
    
    const locale = language === 'zh-HK' ? zhHK : enUS;
    
    try {
        // Format the date part
        const datePart = language === 'zh-HK'
            ? formatInTimeZone(startDate, TIMEZONE, 'yyyy年MM月dd日', { locale })
            : formatInTimeZone(startDate, TIMEZONE, 'MMM d, yyyy', { locale });
        
        // Format the time range
        const startTime = formatInTimeZone(startDate, TIMEZONE, 'HH:mm');
        const endTime = formatInTimeZone(endDate, TIMEZONE, 'HH:mm');
        
        // Combine date and time range
        return `${datePart} ${startTime}-${endTime}`;
    } catch (error) {
        console.error('Date range formatting error:', error);
        return '';
    }
};

// Format only the date part
export const formatDateOnly = (date, language = 'en') => {
    if (!date) return '';
    
    const locale = language === 'zh-HK' ? zhHK : enUS;
    
    try {
        // Format based on language
        const formatString = language === 'zh-HK' 
            ? 'yyyy年MM月dd日' 
            : 'MMM d, yyyy';
            
        return formatInTimeZone(date, TIMEZONE, formatString, { locale });
    } catch (error) {
        console.error('Date formatting error:', error);
        return '';
    }
};

// Format only the time range
export const formatTimeRange = (startDate, endDate) => {
    if (!startDate || !endDate) return '';
    
    try {
        // Format the time range
        const startTime = formatInTimeZone(startDate, TIMEZONE, 'HH:mm');
        const endTime = formatInTimeZone(endDate, TIMEZONE, 'HH:mm');
        
        return `${startTime}-${endTime}`;
    } catch (error) {
        console.error('Time range formatting error:', error);
        return '';
    }
};

// Format date as "Jan 3" for English and localized format for Chinese
export const formatShortDate = (date, language = 'en') => {
    if (!date) return '';
    
    const locale = language === 'zh-HK' ? zhHK : enUS;
    
    try {
        // Format based on language
        const formatString = language === 'zh-HK' 
            ? 'M月d日' // Chinese format: "3月5日"
            : 'MMM d'; // English format: "Jan 3"
            
        return formatInTimeZone(date, TIMEZONE, formatString, { locale });
    } catch (error) {
        console.error('Date formatting error:', error);
        return '';
    }
};

// Format date and time as "Jan 3, HH:mm" for English and localized format for Chinese
export const formatShortDatetime = (date, language = 'en') => {
    if (!date) return '';
    
    const locale = language === 'zh-HK' ? zhHK : enUS;
    
    try {
        // Format based on language
        const dateFormatString = language === 'zh-HK' 
            ? 'M月d日' // Chinese format: "3月5日"
            : 'MMM d'; // English format: "Jan 3"
        
        const timeFormat = 'HH:mm';
        
        // Combine date and time
        const dateStr = formatInTimeZone(date, TIMEZONE, dateFormatString, { locale });
        const timeStr = formatInTimeZone(date, TIMEZONE, timeFormat);
        
        return language === 'zh-HK'
            ? `${dateStr} ${timeStr}` // Chinese: "3月5日 14:30"
            : `${dateStr}, ${timeStr}`; // English: "Jan 3, 14:30"
    } catch (error) {
        console.error('Date formatting error:', error);
        return '';
    }
};

export const calculateDuration = (startDate, endDate, language = 'en') => {
    if (!startDate || !endDate) return '';
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    const durationInMs = endDateObj - startDateObj;
    
    const totalHours = Math.floor(durationInMs / (1000 * 60 * 60));
    const totalMinutes = Math.floor(durationInMs / (1000 * 60));
    
    // If duration is more than 48 hours, show in days
    if (totalHours >= 48) {
        const days = Math.floor(totalHours / 24);
        const remainingHours = totalHours % 24;
        
        if (language === 'zh-HK') {
            if (remainingHours === 0) {
                return days === 1 ? '1天' : `${days}天`;
            } else {
                return `${days}天 ${remainingHours}小時`;
            }
        } else {
            if (remainingHours === 0) {
                return days === 1 ? '1 day' : `${days} days`;
            } else {
                return `${days} ${days === 1 ? 'day' : 'days'} ${remainingHours} ${remainingHours === 1 ? 'hour' : 'hours'}`;
            }
        }
    }
    
    // For durations less than 48 hours, show in hours and minutes
    const hours = Math.floor(durationInMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationInMs % (1000 * 60 * 60)) / (1000 * 60));

    // Format based on language
    if (language === 'zh-HK') {
        if (hours === 0) {
            return `${minutes}分鐘`;
        } else if (minutes === 0) {
            return `${hours}小時`;
        } else {
            return `${hours}小時 ${minutes}分鐘`;
        }
    } else {
        if (hours === 0) {
            return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
        } else if (minutes === 0) {
            return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
        } else {
            return `${hours} ${hours === 1 ? 'hour' : 'hours'} ${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
        }
    }
};

// New function to format date to YYYY-MM-DD string, respecting timezone
export const formatToYYYYMMDD = (date) => {
    if (!date) return '';
    try {
        return formatInTimeZone(date, TIMEZONE, 'yyyy-MM-dd');
    } catch (error) {
        console.error('Date formatting error (formatToYYYYMMDD):', error);
        return '';
    }
};

// New function to get short month name, respecting timezone and locale
export const formatToShortMonth = (date, language = 'en') => {
    if (!date) return '';
    const locale = language === 'zh-HK' ? zhHK : enUS;
    try {
        return formatInTimeZone(date, TIMEZONE, 'MMM', { locale });
    } catch (error) {
        console.error('Date formatting error (formatToShortMonth):', error);
        return '';
    }
};

// New function to get day of month, respecting timezone
export const formatToDayOfMonth = (date) => {
    if (!date) return '';
    try {
        return formatInTimeZone(date, TIMEZONE, 'd');
    } catch (error) {
        console.error('Date formatting error (formatToDayOfMonth):', error);
        return '';
    }
};

// New function to format date time range with full end time
export const formatDateTimeRangeWithEndTime = (startDate, endDate, language = 'en') => {
    if (!startDate || !endDate) return '';
    
    const locale = language === 'zh-HK' ? zhHK : enUS;
    
    try {
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        
        // Check if start and end dates are on the same day
        const isSameDay = 
            startDateObj.getFullYear() === endDateObj.getFullYear() &&
            startDateObj.getMonth() === endDateObj.getMonth() &&
            startDateObj.getDate() === endDateObj.getDate();
        
        if (isSameDay) {
            // If same day, format as "YYYY-MM-DD HH:MM - HH:MM"
            const datePart = language === 'zh-HK'
                ? formatInTimeZone(startDate, TIMEZONE, 'yyyy年MM月dd日', { locale })
                : formatInTimeZone(startDate, TIMEZONE, 'yyyy-MM-dd', { locale });
            
            const startTime = formatInTimeZone(startDate, TIMEZONE, 'HH:mm');
            const endTime = formatInTimeZone(endDate, TIMEZONE, 'HH:mm');
            
            return `${datePart} ${startTime} - ${endTime}`;
        } else {
            // If different days, format as "YYYY-MM-DD HH:MM - YYYY-MM-DD HH:MM"
            const startDatePart = language === 'zh-HK'
                ? formatInTimeZone(startDate, TIMEZONE, 'yyyy年MM月dd日', { locale })
                : formatInTimeZone(startDate, TIMEZONE, 'yyyy-MM-dd', { locale });
            
            const endDatePart = language === 'zh-HK'
                ? formatInTimeZone(endDate, TIMEZONE, 'yyyy年MM月dd日', { locale })
                : formatInTimeZone(endDate, TIMEZONE, 'yyyy-MM-dd', { locale });
            
            const startTime = formatInTimeZone(startDate, TIMEZONE, 'HH:mm');
            const endTime = formatInTimeZone(endDate, TIMEZONE, 'HH:mm');
            
            return `${startDatePart} ${startTime} - ${endDatePart} ${endTime}`;
        }
    } catch (error) {
        console.error('Date range formatting error:', error);
        return '';
    }
};

// New function for more compact format (for booking card)
export const formatCompactDateTimeRange = (startDate, endDate, language = 'en') => {
    if (!startDate || !endDate) return '';
    
    const locale = language === 'zh-HK' ? zhHK : enUS;
    
    try {
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        
        // Check if start and end dates are on the same day
        const isSameDay = 
            startDateObj.getFullYear() === endDateObj.getFullYear() &&
            startDateObj.getMonth() === endDateObj.getMonth() &&
            startDateObj.getDate() === endDateObj.getDate();
        
        if (isSameDay) {
            // If same day, show date once with time range
            const dateFormat = language === 'zh-HK' ? 'MM月dd日' : 'MMM d';
            const datePart = formatInTimeZone(startDate, TIMEZONE, dateFormat, { locale });
            const timeRange = formatTimeRange(startDate, endDate);
            
            return `${datePart}, ${timeRange}`;
        } else {
            // If different days, show compact range
            const dateFormat = language === 'zh-HK' ? 'MM月dd日' : 'MMM d';
            const startDatePart = formatInTimeZone(startDate, TIMEZONE, dateFormat, { locale });
            const endDatePart = formatInTimeZone(endDate, TIMEZONE, dateFormat, { locale });
            const startTime = formatInTimeZone(startDate, TIMEZONE, 'HH:mm');
            const endTime = formatInTimeZone(endDate, TIMEZONE, 'HH:mm');
            
            return `${startDatePart} ${startTime} - ${endDatePart} ${endTime}`;
        }
    } catch (error) {
        console.error('Compact date range formatting error:', error);
        return '';
    }
}; 