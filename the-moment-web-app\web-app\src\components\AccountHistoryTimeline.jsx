import React from 'react';
import { Typography, Timeline, Tag, Divider } from 'antd';
import { PaperClipOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { formatSimpleDateTime } from '../utils/dateFormatter';

const { Title, Text } = Typography;

const AccountHistoryTimeline = ({ accountHistory, showTitle = true }) => {
  const { t } = useTranslation();

  const getStatusColor = (status) => {
    if (!status) return 'blue';

    const statusColors = {
      completed: 'green',
      rejected: 'red',
      approved: 'green',
    };
    return statusColors[status] || 'default';
  };

  if (!accountHistory?.length) {
    return (
      <div className="account-history-empty">
        <p>{t('volunteerApproval.review.noHistory', 'No history available')}</p>
      </div>
    );
  }

  return (
    <div className="account-history-timeline">
      {showTitle && (
        <>
          <Divider />
          <Title level={5} className="flex items-center gap-2">
            <PaperClipOutlined />
            <span>{t('volunteerApproval.review.accountHistory', 'Account History')}</span>
          </Title>
        </>
      )}
      <Timeline
        className="mt-4"
        reverse
        items={accountHistory.map((history) => ({
          color: getStatusColor(history.status),
          children: (
            <>
              <div className="flex items-start gap-3 mb-3">
                <Text strong className="flex-1 text-gray-900 text-base">{history.action}</Text>
                {history.status && (
                  <Tag color={getStatusColor(history.status)} className="mt-0.5 !px-3 !py-0.5 leading-5 font-medium">
                    {t(`volunteerApproval.review.status.${history.status}`, history.status)}
                  </Tag>
                )}
              </div>
              <div className="p-4 bg-gray-50 rounded-lg shadow-sm hover:bg-gray-100 transition-colors">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span className="whitespace-nowrap">{formatSimpleDateTime(history.timestamp)}</span>
                  <span className="w-1 h-1 rounded-full bg-gray-300 shrink-0"></span>
                  <span className="whitespace-nowrap overflow-hidden">{history.reviewer}</span>
                </div>
                {history.comments && (
                  <div className="mt-3 text-sm leading-6 text-gray-800 font-normal">
                    {history.comments}
                  </div>
                )}
              </div>
            </>
          ),
        }))}
      />
    </div>
  );
};

export default AccountHistoryTimeline;
