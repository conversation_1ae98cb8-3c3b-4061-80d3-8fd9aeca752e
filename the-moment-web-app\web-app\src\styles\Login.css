/* Base layout */
.login-container {
  display: flex;
  min-height: 100vh;
  background: #fff;
}

.login-left {
  flex: 1;
  background: linear-gradient(135deg, #f5f7ff 0%, #ffffff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  position: relative;
  overflow: hidden;
}

.login-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.05) 0%, rgba(64, 150, 255, 0.05) 100%);
}

/* Brand content styles */
.brand-content {
  max-width: 480px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.image-carousel-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  height: 300px;
  margin: 0 auto 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.brand-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: opacity 0.5s ease;
  opacity: 0;
}

.brand-image.active {
  opacity: 1;
}

.brand-content h1 {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1e293b;
  letter-spacing: -0.5px;
}

.brand-description {
  font-size: 16px;
  line-height: 1.6;
  color: #64748b;
  max-width: 400px;
  margin: 0 auto;
}

/* 添加一些装饰性元素 */
.login-left::after {
  content: '';
  position: absolute;
  width: 1000px;
  height: 1000px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.03) 0%, rgba(64, 150, 255, 0.03) 100%);
  top: -400px;
  right: -400px;
  z-index: 0;
}

/* Right side layout */
.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form-wrapper {
  position: relative;
  min-height: 600px;
  width: 380px;
  margin: 0 auto;
}

/* Ensure forms are properly positioned */
.login-form-wrapper > div {
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}

.login-subtitle {
  color: rgba(0, 0, 0, 0.45);
}

.login-form .ant-form-item {
  margin-bottom: 24px;
}

/* Form styles */
.verification-code-container {
  display: flex;
  gap: 8px;
  height: auto;
}

/* WhatsApp Toggle Switch styles */
.login-bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* Custom Switch based on Tailwind */
.whatsapp-switch-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.whatsapp-switch-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.custom-switch {
  position: relative;
  display: inline-flex;
  height: 24px;
  width: 44px;
  flex-shrink: 0;
  cursor: pointer;
  border-radius: 9999px;
  border: 2px solid transparent;
  background-color: #e5e7eb;
  transition: background-color 0.2s ease-in-out;
  padding: 0;
  outline: none;
}

.custom-switch.switch-checked {
  background-color: #25D366;
}

.switch-handle {
  pointer-events: none;
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  transform: translateX(0);
  background-color: #fff;
  border-radius: 9999px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease-in-out;
}

.custom-switch.switch-checked .switch-handle {
  transform: translateX(20px);
}

.switch-icon-off,
.switch-icon-on {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.switch-icon-off.active,
.switch-icon-on.active {
  opacity: 1;
}

.switch-icon-off {
  color: #9ca3af;
}

.switch-icon-on {
  color: #25D366;
}

/* Back button */
.back-home-button {
  position: fixed;
  top: 32px;
  left: 32px;
  height: 40px;
  padding: 0 24px;
  font-size: 16px;
  border-radius: 12px;
  z-index: 100;
  display: flex;
  align-items: center;
}

/* Responsive styles */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-left {
    padding: 20px;
    min-height: auto;
    max-height: 400px;
  }

  .image-carousel-container {
    height: 200px;
    margin-bottom: 20px;
  }
  
  .login-right {
    padding: 40px 20px;
  }

  .back-home-button {
    top: 16px;
    left: 16px;
    height: 36px;
    padding: 0 16px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .login-left {
    max-height: 320px;
    padding: 16px;
  }

  .image-carousel-container {
    height: 160px;
    margin-bottom: 16px;
  }
  
  .login-form-wrapper {
    width: 100%;
  }
}

/* Override ant-design styles for consistency */
.ant-input-affix-wrapper {
  border-radius: 8px;
}

.ant-form-item-label > label {
  font-weight: 500;
}

.account-recovery-link {
  color: rgba(0, 0, 0, 0.45);
  text-decoration: underline;
  padding: 0;
  height: auto;
}

.account-recovery-link:hover {
  color: rgba(0, 0, 0, 0.45) !important;
  cursor: pointer;
}

/* Username setup specific styles */
.username-setup-content {
  padding: 2rem;
  max-width: 400px;
  margin: 0 auto;
}

.username-description {
  display: block;
  margin: 1rem 0 2rem;
  color: rgba(0, 0, 0, 0.45);
}

.username-form {
  margin-top: 2rem;
}