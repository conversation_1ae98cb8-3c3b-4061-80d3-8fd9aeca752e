package payloads

import (
	"Membership-SAAS-System-Backend/db"
	"time"

	"github.com/google/uuid"
)

// UserVerificationRequestResponse represents a user verification request for API output.
// It will include details specific to the verification type.
type UserVerificationRequestResponse struct {
	ID               string                    `json:"id"`
	UserID           string                    `json:"user_id"`
	VerificationType db.VerificationTypeEnum   `json:"verification_type"`
	Status           db.VerificationStatusEnum `json:"status"`
	DocumentID       *string                   `json:"document_id,omitempty"`
	FileName         *string                   `json:"file_name,omitempty"`
	MimeType         *string                   `json:"mime_type,omitempty"`
	DocumentID2      *string                   `json:"document_id_2,omitempty"`
	FileName2        *string                   `json:"file_name_2,omitempty"`
	MimeType2        *string                   `json:"mime_type_2,omitempty"`
	SubmittedAt      time.Time                 `json:"submitted_at"`
	ReviewedAt       *time.Time                `json:"reviewed_at,omitempty"`
	ReviewedByUserID *string                   `json:"reviewed_by_user_id,omitempty"`
	AdminNotes       *string                   `json:"admin_notes,omitempty"`
	CreatedAt        time.Time                 `json:"created_at"`
	UpdatedAt        time.Time                 `json:"updated_at"`

	// Optional related info often included in responses
	UserDisplayName     *string `json:"user_display_name,omitempty"`
	UserEmail           *string `json:"user_email,omitempty"`
	ReviewerDisplayName *string `json:"reviewer_display_name,omitempty"`

	// Specifics will hold the data for the given verification type.
	// This can be a map[string]interface{} or a more structured approach later.
	Specifics interface{} `json:"specifics,omitempty"`
}

// UserVerificationStatusResponse defines the structure for verification status.
type UserVerificationStatusResponse struct {
	HKIDCard             bool `json:"hk_id_card"`
	MainlandChinaIDCard  bool `json:"mainland_china_id_card"`
	MainlandTravelPermit bool `json:"mainland_travel_permit"`
	Passport             bool `json:"passport"`
	HKYouthPlus          bool `json:"hk_youth_plus"`
	AddressProof         bool `json:"address_proof"`
	StudentID            bool `json:"student_id"`
	HomeVisit            bool `json:"home_visit"`
}

// ListUserVerificationsParams defines parameters for listing user verification requests.
type ListUserVerificationsParams struct {
	Status           *string // e.g., "approved", "pending"
	VerificationType *string // e.g., "hk_id_card"
	// Add other potential filters like Page, PageSize later if needed
}

// SubmitUserVerificationRequest represents the payload for submitting a new verification request.
// This is a multipart form data request.
// Fields common to all types:
// - verification_type (string)
// - document (file, optional depending on type)
// Specific fields depend on verification_type.
type SubmitUserVerificationRequest struct {
	VerificationType string `form:"verification_type" validate:"required"`

	// HKIDCard specific
	HkIdCardFullNameEng    string `form:"hk_id_card_full_name_eng" validate:"required_if=VerificationType hk_id_card"`
	HkIdCardFullNameChn    string `form:"hk_id_card_full_name_chn" validate:"required_if=VerificationType hk_id_card"`
	HkIdCardDob            string `form:"hk_id_card_dob" validate:"required_if=VerificationType hk_id_card,datetime=2006-01-02"` // YYYY-MM-DD
	HkIdCardSex            string `form:"hk_id_card_sex" validate:"required_if=VerificationType hk_id_card,oneof=M F O"`
	HkIdCardNumber         string `form:"hk_id_card_number" validate:"required_if=VerificationType hk_id_card"`
	HkIdCardIsPermanent    *bool  `form:"hk_id_card_is_permanent" validate:"required_if=VerificationType hk_id_card"`
	HkIdCardCommercialCode string `form:"hk_id_card_commercial_code"`

	// MainlandChinaIDCard specific
	MainlandIdNameChn    string `form:"mainland_id_name_chn" validate:"required_if=VerificationType mainland_china_id_card"`
	MainlandIdSex        string `form:"mainland_id_sex" validate:"required_if=VerificationType mainland_china_id_card,oneof=M F"`
	MainlandIdDob        string `form:"mainland_id_dob" validate:"required_if=VerificationType mainland_china_id_card,datetime=2006-01-02"`
	MainlandIdNumber     string `form:"mainland_id_number" validate:"required_if=VerificationType mainland_china_id_card"`
	MainlandIdValidFrom  string `form:"mainland_id_valid_from" validate:"required_if=VerificationType mainland_china_id_card,datetime=2006-01-02"`
	MainlandIdValidUntil string `form:"mainland_id_valid_until" validate:"required_if=VerificationType mainland_china_id_card,datetime=2006-01-02"`

	// MainlandTravelPermit specific
	MainlandTravelPermitNumber     string `form:"mainland_travel_permit_number" validate:"required_if=VerificationType mainland_travel_permit"`
	MainlandTravelPermitIssueDate  string `form:"mainland_travel_permit_issue_date" validate:"required_if=VerificationType mainland_travel_permit,datetime=2006-01-02"`
	MainlandTravelPermitExpiryDate string `form:"mainland_travel_permit_expiry_date" validate:"required_if=VerificationType mainland_travel_permit,datetime=2006-01-02"`

	// Passport specific
	PassportNumber         string `form:"passport_number" validate:"required_if=VerificationType passport"`
	PassportIssuingCountry string `form:"passport_issuing_country" validate:"required_if=VerificationType passport"` // ISO 3166-1 alpha-2 code
	PassportIssueDate      string `form:"passport_issue_date" validate:"required_if=VerificationType passport,datetime=2006-01-02"`
	PassportExpiryDate     string `form:"passport_expiry_date" validate:"required_if=VerificationType passport,datetime=2006-01-02"`

	// HkYouthPlus specific
	HkYouthPlusMemberNumber string `form:"hk_youth_plus_member_number" validate:"required_if=VerificationType hk_youth_plus"`

	// AddressProof specific
	AddressProofFullAddress string `form:"address_proof_full_address" validate:"required_if=VerificationType address_proof"`

	// StudentID specific
	StudentIdSchoolName string `form:"student_id_school_name" validate:"required_if=VerificationType student_id"`
	StudentIdGrade      string `form:"student_id_grade" validate:"required_if=VerificationType student_id"`
	StudentIdExpiryDate string `form:"student_id_expiry_date" validate:"required_if=VerificationType student_id,datetime=2006-01-02"`

	// HomeVisit specific
	HomeVisitNotes *string `form:"home_visit_notes" validate:"omitempty"`
}

// AdminListVerificationFilters defines the query parameters for listing user verifications by an admin.
type AdminListVerificationFilters struct {
	UserID           *uuid.UUID `json:"user_id" form:"user_id"`
	EventID          *uuid.UUID `json:"event_id" form:"event_id"`
	VerificationType string     `json:"verification_type" form:"verification_type"`
	Status           string     `json:"status" form:"status"`
	OrgID            *uuid.UUID `json:"org_id" form:"org_id"`
	Page             int        `json:"page" form:"page"`
	Limit            int        `json:"limit" form:"limit"`
}

// UserVerificationRequestAdminListResponse defines the structure for a single verification request in the admin list.
// This mirrors the fields from db.AdminListFullVerificationRequestsRow but is tailored for the API response.
type UserVerificationRequestAdminListResponse struct {
	RequestID           uuid.UUID `json:"request_id"`
	UserID              uuid.UUID `json:"user_id"`
	UserDisplayName     string    `json:"user_display_name"`
	UserEmail           string    `json:"user_email"`
	VerificationType    string    `json:"verification_type"`
	Status              string    `json:"status"`
	SubmittedAt         time.Time `json:"submitted_at"`
	ReviewedAt          time.Time `json:"reviewed_at,omitempty"`
	ReviewedByUserID    uuid.UUID `json:"reviewed_by_user_id,omitempty"`
	ReviewerDisplayName string    `json:"reviewer_display_name,omitempty"`
	AdminNotes          string    `json:"admin_notes,omitempty"`
	DocumentID          uuid.UUID `json:"document_id,omitempty"`
	DocumentID2         uuid.UUID `json:"document_id_2,omitempty"`
	FileName            string    `json:"file_name,omitempty"`
}

type VolunteerSubmitVerificationRequest struct {
	UserID           uuid.UUID `json:"user_id" validate:"required"`
	VerificationType string    `json:"verification_type" validate:"required"`
	// Other fields are dynamic and will be in the map[string]string
}

type VolunteerGetUserVerificationRequestsPayload struct {
	UserID uuid.UUID `query:"user_id" validate:"required"`
}
