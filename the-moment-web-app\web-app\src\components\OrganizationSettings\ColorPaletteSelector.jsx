import React from 'react';
import { Radio, Space } from 'antd';
import { THEME_COLORS } from '../../config/themeConfig';

const ColorPaletteSelector = ({ value, onChange }) => {
    return (
        <Radio.Group
            value={value}
            onChange={e => onChange(e.target.value)}
            buttonStyle="solid"
        >
            <Space direction="horizontal">
                {THEME_COLORS.map(color => (
                    <Radio.Button
                        key={color.name}
                        value={color.name}
                        style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                            width: '50px',
                            height: '30px',
                            marginRight: '8px',
                            position: 'relative'
                        }}
                    >
                        {value === color.name && (
                            <div style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                color: '#fff',
                                fontWeight: 'bold'
                            }}>✓</div>
                        )}
                    </Radio.Button>
                ))}
            </Space>
        </Radio.Group>
    );
};

export default ColorPaletteSelector; 