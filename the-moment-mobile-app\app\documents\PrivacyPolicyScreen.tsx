import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { Stack } from 'expo-router';

interface Section {
  title: string;
  content: string;
}

export function PrivacyPolicyScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme);
  const styles = getThemedStyles(theme);

  const sections = t('privacyPolicy.sections', { returnObjects: true }) as Section[];

  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: t('profile.items.privacyPolicy.title'),
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Header Section */}
          <View style={styles.headerSection}>
            <Text style={styles.lastUpdated}>
              {t('privacyPolicy.lastUpdated')}
            </Text>
          </View>

          {/* Content Sections */}
          {sections.map((section: Section, index: number) => (
            <View key={index} style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionNumber}>
                  {section.title.split('.')[0]}
                </Text>
                <Text style={styles.sectionTitle}>
                  {section.title.split('.')[1]}
                </Text>
              </View>
              <View style={styles.divider} />
              <Text style={styles.sectionContent}>
                {section.content}
              </Text>
            </View>
          ))}

          {/* Footer Section */}
          <View style={styles.footerSection}>
            <Text style={styles.footer}>
              {t('privacyPolicy.footer')}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const getThemedStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 32,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  lastUpdated: {
    fontSize: 14,
    marginBottom: 8,
    color: theme.system.secondaryText,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionNumber: {
    fontSize: 24,
    fontWeight: '600',
    marginRight: 12,
    color: theme.colors.primary,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    color: theme.system.text,
  },
  divider: {
    height: 1,
    marginBottom: 12,
    backgroundColor: theme.system.border,
  },
  sectionContent: {
    fontSize: 15,
    lineHeight: 24,
    paddingLeft: 36,
    color: theme.system.text,
  },
  footerSection: {
    marginTop: 32,
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  footer: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    color: theme.system.secondaryText,
  },
});

export default PrivacyPolicyScreen;
