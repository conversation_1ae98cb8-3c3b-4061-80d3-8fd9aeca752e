-- name: CreateResource :one
INSERT INTO resources (
    organization_id,
    title,
    slug,
    description,
    visibility,
    status,
    published_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) RETURNING *;

-- name: GetResourceByID :one
SELECT * FROM resources
WHERE id = $1 LIMIT 1;

-- name: GetResourceByOrgAndSlug :one
SELECT * FROM resources
WHERE organization_id = $1 AND slug = $2 LIMIT 1;

-- name: ListResourcesByOrganization :many
SELECT * FROM resources
WHERE organization_id = @organization_id
  AND (sqlc.narg(status)::TEXT IS NULL OR status = sqlc.narg(status)::TEXT)
  AND (sqlc.narg(search_term)::TEXT IS NULL OR (title ILIKE '%' || sqlc.narg(search_term)::TEXT || '%' OR description ILIKE '%' || sqlc.narg(search_term)::TEXT || '%'))
ORDER BY created_at DESC
LIMIT @limit_val OFFSET @offset_val;

-- name: ListPublishedResources_NoFiles :many
SELECT *
FROM resources r
WHERE r.status = 'published' AND r.visibility = 'public'
ORDER BY r.published_at DESC NULLS LAST, r.created_at DESC
LIMIT $1 OFFSET $2;

-- name: ListPublishedResourcesWithFiles :many
SELECT
    sqlc.embed(r),
    rf.id AS resource_file_id,
    rf.resource_id AS resource_file_resource_id,
    rf.file_name AS resource_file_file_name,
    rf.file_path AS resource_file_file_path,
    rf.file_type AS resource_file_file_type,
    rf.file_size AS resource_file_file_size,
    rf.description AS resource_file_description,
    rf.uploaded_at AS resource_file_uploaded_at
FROM
    resources r
LEFT JOIN
    resource_files rf ON r.id = rf.resource_id
WHERE
    r.status = 'published'
    AND (sqlc.narg(visibility_filter)::text IS NULL OR r.visibility = sqlc.narg(visibility_filter)::text)
    AND (
        (sqlc.narg(organization_id_filter)::UUID IS NULL AND sqlc.narg(organization_id2_filter)::UUID IS NULL) OR
        (sqlc.narg(organization_id_filter)::UUID IS NOT NULL AND r.organization_id = sqlc.narg(organization_id_filter)::UUID) OR
        (sqlc.narg(organization_id2_filter)::UUID IS NOT NULL AND r.organization_id = sqlc.narg(organization_id2_filter)::UUID)
    )
    AND (sqlc.narg(search_term)::TEXT IS NULL OR (r.title ILIKE '%' || sqlc.narg(search_term)::TEXT || '%' OR r.description ILIKE '%' || sqlc.narg(search_term)::TEXT || '%'))
ORDER BY
    r.published_at DESC NULLS LAST, r.created_at DESC, rf.uploaded_at ASC
LIMIT @limit_val OFFSET @offset_val;

-- name: ListPublishedResourcesByVisibility :many
SELECT * FROM resources
WHERE status = 'published' AND visibility = $1
ORDER BY published_at DESC
LIMIT $2 OFFSET $3;

-- name: UpdateResource :one
UPDATE resources
SET
    title = COALESCE(sqlc.narg('title'), title),
    slug = COALESCE(sqlc.narg('slug'), slug),
    description = COALESCE(sqlc.narg('description'), description),
    visibility = COALESCE(sqlc.narg('visibility'), visibility),
    status = COALESCE(sqlc.narg('status'), status),
    published_at = COALESCE(sqlc.narg('published_at'), published_at),
    updated_at = now()
WHERE id = sqlc.arg('id')
RETURNING *;

-- name: DeleteResource :exec
DELETE FROM resources
WHERE id = $1;

-- name: CreateResourceFile :one
INSERT INTO resource_files (
    resource_id,
    file_name,
    file_path,
    file_type,
    file_size,
    description
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING *;

-- name: GetResourceFileByID :one
SELECT * FROM resource_files
WHERE id = $1 LIMIT 1;

-- name: ListFilesByResource :many
SELECT id, resource_id, file_name, file_path, file_type, file_size, description, uploaded_at
FROM resource_files
WHERE resource_id = $1
ORDER BY uploaded_at ASC;

-- name: DeleteResourceFile :exec
DELETE FROM resource_files
WHERE id = $1;

-- name: GetResourceWithFiles :many
SELECT
    -- Embed the main resource table
    sqlc.embed(r),
    -- Explicitly select columns from resource_files for the LEFT JOIN
    -- SQLC should generate pointer types for these in the _Row struct
    -- due to the LEFT JOIN context.
    rf.id AS resource_file_id,
    rf.resource_id AS resource_file_resource_id,
    rf.file_name AS resource_file_file_name,
    rf.file_path AS resource_file_file_path,
    rf.file_type AS resource_file_file_type,
    rf.file_size AS resource_file_file_size,
    rf.description AS resource_file_description,
    rf.uploaded_at AS resource_file_uploaded_at
FROM
    resources r
LEFT JOIN
    resource_files rf ON r.id = rf.resource_id
WHERE
    r.id = $1;

-- name: GetResourceByOrgAndSlugWithFiles :many
SELECT
    sqlc.embed(r),
    rf.id AS resource_file_id,
    rf.resource_id AS resource_file_resource_id,
    rf.file_name AS resource_file_file_name,
    rf.file_path AS resource_file_file_path,
    rf.file_type AS resource_file_file_type,
    rf.file_size AS resource_file_file_size,
    rf.description AS resource_file_description,
    rf.uploaded_at AS resource_file_uploaded_at
FROM
    resources r
LEFT JOIN
    resource_files rf ON r.id = rf.resource_id
WHERE
    r.organization_id = $1 AND r.slug = $2;


-- name: CreateOrganizationFile :one
INSERT INTO organization_files (
    organization_id,
    file_name,
    file_path,
    file_type,
    file_size,
    is_folder,
    parent_folder_id
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) RETURNING *;

-- name: GetOrganizationFileByID :one
SELECT * FROM organization_files
WHERE id = $1 LIMIT 1;

-- name: ListOrganizationFilesInRoot :many
SELECT id, organization_id, parent_folder_id, file_name, file_path, file_type, file_size, is_folder, created_at, updated_at
FROM organization_files
WHERE organization_id = $1
AND parent_folder_id IS NULL
ORDER BY is_folder DESC, file_name ASC
LIMIT $2 OFFSET $3;

-- name: ListOrganizationFilesInFolder :many
SELECT id, organization_id, parent_folder_id, file_name, file_path, file_type, file_size, is_folder, created_at, updated_at
FROM organization_files
WHERE organization_id = $1
AND parent_folder_id = $2
ORDER BY is_folder DESC, file_name ASC
LIMIT $3 OFFSET $4;

-- name: UpdateOrganizationFile :one
UPDATE organization_files
SET
    file_name = COALESCE(sqlc.narg('file_name'), file_name),
    parent_folder_id = COALESCE(sqlc.narg('parent_folder_id'), parent_folder_id), -- Allow moving files/folders
    file_path = COALESCE(sqlc.narg('file_path'), file_path), -- Allow updating file path
    updated_at = now()
WHERE id = sqlc.arg('id')
RETURNING *;

-- name: DeleteOrganizationFile :exec
DELETE FROM organization_files
WHERE id = $1;

-- name: GetAllDescendantOrganizationItemsIncludingParent :many
WITH RECURSIVE descendants_cte AS ( 
    SELECT 
        id,
        organization_id,
        file_name,
        file_path,
        file_type,
        file_size,
        is_folder,
        parent_folder_id,
        created_at,
        updated_at
    FROM organization_files
    WHERE organization_files.id = $1 -- Explicitly qualify id in the anchor part

    UNION ALL

    SELECT
        f.id,
        f.organization_id,
        f.file_name,
        f.file_path,
        f.file_type,
        f.file_size,
        f.is_folder,
        f.parent_folder_id,
        f.created_at,
        f.updated_at
    FROM organization_files f
    INNER JOIN descendants_cte d_recursive_join ON f.parent_folder_id = d_recursive_join.id
)
SELECT 
    id, 
    organization_id,
    file_name,
    file_path,
    file_type,
    file_size,
    is_folder,
    parent_folder_id,
    created_at,
    updated_at
FROM descendants_cte;

-- name: CountOrganizationFilesInRoot :one
SELECT count(*) FROM organization_files
WHERE organization_id = $1
AND parent_folder_id IS NULL;

-- name: CountOrganizationFilesInFolder :one
SELECT count(*) FROM organization_files
WHERE organization_id = $1
AND parent_folder_id = $2;

-- name: CountResourcesByOrganization :one
SELECT count(*) FROM resources
WHERE organization_id = @organization_id
  AND (sqlc.narg(status)::TEXT IS NULL OR status = sqlc.narg(status)::TEXT)
  AND (sqlc.narg(search_term)::TEXT IS NULL OR (title ILIKE '%' || sqlc.narg(search_term)::TEXT || '%' OR description ILIKE '%' || sqlc.narg(search_term)::TEXT || '%'));

-- name: CountPublishedResourcesByVisibility :one
SELECT count(*) FROM resources
WHERE status = 'published' AND visibility = $1;

-- name: CountAllPublishedResources :one
SELECT count(*) FROM resources
WHERE status = 'published';

-- name: CountPublishedResourcesByOrganization :one
SELECT COUNT(*) FROM resources
WHERE organization_id = $1 AND status = 'published'; -- Assuming public means published for the org context

-- name: ListPublishedResourcesByOrganizationWithFiles :many
SELECT
    sqlc.embed(r),
    rf.id AS resource_file_id,
    rf.resource_id AS resource_file_resource_id,
    rf.file_name AS resource_file_file_name,
    rf.file_path AS resource_file_file_path,
    rf.file_type AS resource_file_file_type,
    rf.file_size AS resource_file_file_size,
    rf.uploaded_at AS resource_file_uploaded_at
FROM
    resources r
LEFT JOIN
    resource_files rf ON r.id = rf.resource_id
WHERE
    r.organization_id = $1
    AND r.status = 'published'
ORDER BY
    r.published_at DESC NULLS LAST, r.created_at DESC, rf.uploaded_at ASC
LIMIT $2 OFFSET $3;

-- name: CountPublishedResources :one
SELECT COUNT(*) FROM resources
WHERE
    status = 'published'
    AND (sqlc.narg(visibility_filter)::text IS NULL OR visibility = sqlc.narg(visibility_filter)::text)
    AND (
        (sqlc.narg(organization_id_filter)::UUID IS NULL AND sqlc.narg(organization_id2_filter)::UUID IS NULL) OR
        (sqlc.narg(organization_id_filter)::UUID IS NOT NULL AND organization_id = sqlc.narg(organization_id_filter)::UUID) OR
        (sqlc.narg(organization_id2_filter)::UUID IS NOT NULL AND organization_id = sqlc.narg(organization_id2_filter)::UUID)
    )
    AND (sqlc.narg(search_term)::TEXT IS NULL OR (title ILIKE '%' || sqlc.narg(search_term)::TEXT || '%' OR description ILIKE '%' || sqlc.narg(search_term)::TEXT || '%'));

-- name: GetScheduledResourcesToPublish :many
SELECT id FROM resources WHERE status = 'draft' AND published_at IS NOT NULL AND published_at <= NOW();

-- name: SetResourceStatusToPublished :exec
UPDATE resources SET status = 'published', updated_at = NOW() WHERE id = $1; 