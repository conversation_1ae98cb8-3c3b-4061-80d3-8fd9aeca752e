-- 000024_update_event_schema.up.sql

-- Step 0: Drop the default constraint on the status column.
ALTER TABLE events ALTER COLUMN status DROP DEFAULT;

-- Step 1: Alter columns using event_status_type to TEXT.
ALTER TABLE events ALTER COLUMN status TYPE TEXT;

-- Step 2: Drop the old ENUM type if it exists.
DROP TYPE IF EXISTS event_status_type;

-- Step 3: Create the new ENUM type with the desired values.
CREATE TYPE event_status_type AS ENUM (
    'published',
    'archived',
    'deleted',
    'draft',
    'hidden',
    'cancelled'
);

-- Step 4: Alter columns back to the new ENUM type.
ALTER TABLE events ALTER COLUMN status TYPE event_status_type USING status::event_status_type;

-- Step 5: Reset the default value for the status column.
ALTER TABLE events ALTER COLUMN status SET DEFAULT 'draft';

-- Add new columns to the events table
ALTER TABLE events
ADD COLUMN IF NOT EXISTS price TEXT NULL,
ADD COLUMN IF NOT EXISTS contact_email VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS contact_phone VARCHAR(50) NULL;

COMMENT ON COLUMN events.price IS 'Price of the event, stored as text. E.g., "Free", "10.99", "Contact us".';
COMMENT ON COLUMN events.contact_email IS 'Contact email for event inquiries.';
COMMENT ON COLUMN events.contact_phone IS 'Contact phone for event inquiries.';
