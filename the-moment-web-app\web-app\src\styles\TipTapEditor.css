/* TipTapEditor.css */

.tiptap-editor {
    border: 1px solid #d9d9d9;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
  
.tiptap-toolbar {
    padding: 8px;
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid #f0f0f0;
}
  
.tiptap-toolbar .ant-btn {
    margin-right: 4px;
    margin-bottom: 4px;
}

.prose-container {
    padding: 4px 16px 16px 16px;
    max-height: 600px;
    overflow-y: auto;
}
  
.tiptap-editor-content {
    min-height: 500px;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
}

/* Override Tailwind Typography styles when needed */
.tiptap-editor-content.prose {
    max-width: none;
}

.tiptap-editor-content.prose ul {
    list-style-type: disc;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.tiptap-editor-content.prose ol {
    list-style-type: decimal;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.tiptap-editor-content.prose li {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
}

.tiptap-editor-content.prose li > p {
    margin: 0;
}

/* Text Alignment */
.tiptap-editor-content [style*='text-align: center'] {
    text-align: center;
}
  
.tiptap-editor-content [style*='text-align: right'] {
    text-align: right;
}
  
/* Placeholder styling */
.tiptap-editor-content.is-empty::before {
    content: attr(data-placeholder);
    color: #bfbfbf;
    position: absolute;
    pointer-events: none;
}
  
/* Beautify scrollbar */
.prose-container::-webkit-scrollbar {
    width: 8px;
}
  
.prose-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}
  
.prose-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}
  
.prose-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
  