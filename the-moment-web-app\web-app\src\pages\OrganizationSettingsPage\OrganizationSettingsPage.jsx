import React, { useState, useEffect, useCallback } from 'react';
import {
    Form,
    Input,
    Button,
    Upload,
    Typography,
    Divider,
    message,
    Space,
    Select,
    Switch,
    Row,
    Col,
    Spin,
    Alert
} from 'antd';
import { UploadOutlined, PlusOutlined, SaveOutlined, UserAddOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';
import { useOrganization } from '../../contexts/OrganizationContext';
import { organizationService } from '../../services/organizationService';
import { ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { THEME_COLORS } from '../../config/themeConfig';
import CreateAdminModal from '../../components/OrganizationSettings/CreateAdminModal';
import ColorPaletteSelector from '../../components/OrganizationSettings/ColorPaletteSelector';
import { createOrgLogoUploadProps } from '../../utils/orgSettingsUploadUtils';

const { Title, Text } = Typography;
const { Option } = Select;

const OrganizationSettingsPage = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { orgId } = useParams(); // Get orgId from route params
    const { currentUser, isLoading: isUserLoading } = useUser();
    const { fetchOrganizations } = useOrganization();

    const [updateForm] = Form.useForm();

    const [updateLoading, setUpdateLoading] = useState(false);
    const [pageLoading, setPageLoading] = useState(false);

    const [currentLogoUrl, setCurrentLogoUrl] = useState(null);
    const [updateOrgLogoFileList, setUpdateOrgLogoFileList] = useState([]);

    const [editingOrganization, setEditingOrganization] = useState(null);

    const [isCreateAdminModalVisible, setIsCreateAdminModalVisible] = useState(false);
    const [createAdminModalLoading, setCreateAdminModalLoading] = useState(false);

    const isSuperAdmin = currentUser?.role === 'super_admin';
    const canManageOrganizations = currentUser?.role === 'super_admin' || currentUser?.role === 'admin';

    // For editing, check if user can edit this specific organization
    const canEditThisOrg = isSuperAdmin || (currentUser?.role === 'admin' && editingOrganization && editingOrganization.id !== ALL_ORGANIZATION_ID);

    // Fetch organization details when in edit mode
    useEffect(() => {
        if (orgId && canManageOrganizations) {
            const fetchOrgDetails = async () => {
                setPageLoading(true);
                try {
                    const response = await organizationService.getOrganizationDetails(orgId);
                    const org = response?.data || response;
                    setEditingOrganization(org);
                    
                    // Set form values for editing
                    let formThemeName = THEME_COLORS.length > 0 ? THEME_COLORS[0].name : 'red';
                    if (org.theme_color) {
                        const themeExists = THEME_COLORS.find(tc => tc.name === org.theme_color);
                if (themeExists) {
                            formThemeName = org.theme_color;
                }
            }

            updateForm.setFieldsValue({
                        name: org.name,
                        description: org.description || '',
                theme_color: formThemeName,
                        status: org.status || 'active',
            });
                    setCurrentLogoUrl(org.image_url || null);
                    setUpdateOrgLogoFileList(org.image_url ? [{
                        uid: '-1',
                        name: 'logo.png',
                        status: 'done',
                        url: org.image_url
                    }] : []);
                } catch (error) {
                    console.error('Error fetching organization details:', error);
                    message.error(t('organizationSettings.errors.fetchOrgFailed'));
                    navigate('/organization-settings');
                } finally {
                    setPageLoading(false);
                }
            };
            fetchOrgDetails();
        }
    }, [orgId, canManageOrganizations, updateForm, navigate, t]);

    const handleLogoUpload = async (file, orgIdParam) => {
        if (!orgIdParam) {
            message.error(t('organizationSettings.errors.orgIdMissingForLogoUpload'));
            return null;
        }
        try {
            const response = await organizationService.uploadOrganizationLogo(orgIdParam, file);
            if (response?.image_url) {
                message.success(t('organizationSettings.messages.logoUploadedSuccessfully'));
                return response.image_url;
            } else {
                message.error(t('organizationSettings.errors.logoUploadFailed'));
                return null;
            }
        } catch (error) {
            console.error(t('organizationSettings.errors.logoUploadFailed'), error);
            message.error(t('organizationSettings.errors.logoUploadFailed') + `: ${error.message}`);
            return null;
        }
    };

    const onUpdateOrganization = async (values) => {
        if (!editingOrganization) {
            message.error(t('organizationSettings.errors.noOrganizationSelected'));
            return;
        }

        setUpdateLoading(true);
        try {
        let imageUrl = currentLogoUrl;

            // Handle logo upload/update logic
        if (updateOrgLogoFileList.length > 0 && updateOrgLogoFileList[0].originFileObj) {
                const uploadedUrl = await handleLogoUpload(updateOrgLogoFileList[0].originFileObj, editingOrganization.id);
            if (uploadedUrl) {
                imageUrl = uploadedUrl;
            }
        } else if (updateOrgLogoFileList.length === 0 && currentLogoUrl) {
                imageUrl = null;
        }

        const payload = { ...values, image_url: imageUrl };
        
        // For super organization, don't allow changing status
            if (editingOrganization.id === ALL_ORGANIZATION_ID) {
            delete payload.status;
        }
        
            await organizationService.updateOrganization(editingOrganization.id, payload);
            message.success(t('organizationSettings.messages.orgUpdatedSuccessfully'));
            
            // Refresh organizations and navigate back
            if (fetchOrganizations) {
                await fetchOrganizations();
            }
            navigate('/organization-settings');
        } catch (error) {
            console.error('Error updating organization:', error);
            message.error(t('organizationSettings.errors.orgUpdateFailed') + `: ${error.message || ''}`);
        } finally {
            setUpdateLoading(false);
        }
    };

    // 使用共用的文件上传配置
    const uploadProps = createOrgLogoUploadProps(updateOrgLogoFileList, setUpdateOrgLogoFileList, t);

    const showCreateAdminModal = () => setIsCreateAdminModalVisible(true);
    const handleCreateAdminModalCancel = () => setIsCreateAdminModalVisible(false);
    
    const handleCreateAdminSuccess = (successMessage) => {
        message.success(successMessage);
        setIsCreateAdminModalVisible(false);
        setCreateAdminModalLoading(false);
    };

    const handleCreateAdminError = (errorMessage) => {
        message.error(errorMessage);
        setCreateAdminModalLoading(false);
    };

    if (isUserLoading || pageLoading) {
        return <Spin size="large" style={{ display: 'block', marginTop: '50px' }} />;
    }

    if (!currentUser || !canManageOrganizations) {
        return <Alert message={t('unauthorized.title')} description={t('unauthorized.defaultMessage')} type="error" showIcon />;
    }

    // For edit mode, check if user can edit this specific organization
    if (!isSuperAdmin && !canEditThisOrg) {
        return <Alert message={t('unauthorized.title')} description={t('organizationSettings.errors.noPermission')} type="error" showIcon />;
    }

    return (
        <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
            {/* Organization Settings Title */}
                    <Divider orientation="left" style={{ borderColor: '#bdbdbd', marginTop: '30px' }}>
                {t('organizationSettings.updateOrg.title', { orgName: editingOrganization?.name || 'Organization' })}
                    </Divider>

            {/* Edit Organization Form - Only show in edit mode */}
            {editingOrganization && (
                    <div style={{ padding: '0 16px', marginBottom: '40px' }}>
                        <Form form={updateForm} layout="vertical" onFinish={onUpdateOrganization}>
                            <Row gutter={[24, 16]}>
                                <Col xs={24} sm={12}>
                                    <Form.Item
                                        name="name"
                                        label={t('organizationSettings.common.name')}
                                        rules={[{ required: true, message: t('organizationSettings.errors.nameRequired') }]}
                                    >
                                        <Input />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={12}>
                                {editingOrganization.id !== ALL_ORGANIZATION_ID && (
                                        <Form.Item
                                            name="status"
                                            label={t('organizationSettings.common.status')}
                                        rules={[{ required: true, message: t('common.requiredField') }]}
                                        >
                                            <Select>
                                                <Option value="active">{t('organizationSettings.status.active')}</Option>
                                                <Option value="inactive">{t('organizationSettings.status.inactive')}</Option>
                                                <Option value="pending_setup">{t('organizationSettings.status.pending_setup')}</Option>
                                            </Select>
                                        </Form.Item>
                                    )}
                                </Col>
                            </Row>

                            <Form.Item name="description" label={t('organizationSettings.common.description')}>
                                <Input.TextArea rows={3} />
                            </Form.Item>

                            <Row gutter={[24, 16]}>
                                <Col xs={24} sm={12}>
                                    <Form.Item name="logo" label={t('organizationSettings.common.logo')}>
                                        <Upload {...uploadProps}>
                                            <Button icon={<UploadOutlined />}>{t('organizationSettings.common.selectLogo')}</Button>
                                        </Upload>
                                        {currentLogoUrl && updateOrgLogoFileList.length === 0 && (
                                            <div style={{ marginTop: 8 }}>
                                                <Text type="secondary">{t('organizationSettings.common.currentLogo')}: </Text>
                                                <img src={currentLogoUrl} alt={t('organizationSettings.common.logo')} style={{ maxWidth: '100px', maxHeight: '100px', display: 'block', marginTop: '8px' }} />
                                            </div>
                                        )}
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={12}>
                                    <Form.Item name="theme_color" label={t('organizationSettings.common.themeColor')}>
                                        <ColorPaletteSelector />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Divider />
                            <Row justify="space-between" align="middle" style={{ marginTop: '20px' }}>
                                <Col>
                                {isSuperAdmin && (
                                    <Button 
                                        size="large"
                                        color="primary" 
                                        variant="dashed"
                                        icon={<UserAddOutlined />} 
                                        onClick={showCreateAdminModal}
                                    >
                                        {t('organizationSettings.createAdmin.button')}
                                    </Button>
                                )}
                                </Col>
                                <Col>
                                    <Button
                                        size="large"
                                        type="primary"
                                        htmlType="submit"
                                    loading={updateLoading}
                                    icon={<SaveOutlined />}
                                    style={{ minWidth: '120px' }}
                                    >
                                    {t('organizationSettings.updateOrg.submit')}
                                    </Button>
                                </Col>
                            </Row>
                        </Form>
                    </div>
            )}

            <CreateAdminModal
                visible={isCreateAdminModalVisible}
                loading={createAdminModalLoading}
                onCancel={handleCreateAdminModalCancel}
                onSuccess={handleCreateAdminSuccess}
                onError={handleCreateAdminError}
                onLoadingChange={setCreateAdminModalLoading}
                isSuperAdmin={isSuperAdmin}
                organizationId={editingOrganization?.id}
            />
        </div>
    );
};

export default OrganizationSettingsPage; 