import React from 'react';
import { Input } from 'antd';
import { PhoneOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const PhoneNumberInput = ({ value, onChange, ...props }) => {
    const { t } = useTranslation();

    const handlePhoneChange = (e) => {
        const newValue = e.target.value.replace(/\D/g, '');
        onChange(newValue.slice(0, 8));
    };

    return (
        <Input
            prefix={<PhoneOutlined style={{ marginRight: '8px' }} />}
            className="input-grouping"
            size="large"
            addonBefore="+852"
            placeholder={t('tools.phoneNumberInput.placeholder')}
            value={value}
            onChange={handlePhoneChange}
            maxLength={8}
            {...props}
        />
    );
};

export default PhoneNumberInput;
