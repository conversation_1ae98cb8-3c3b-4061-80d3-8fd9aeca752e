.volunteer-approval {
  padding: 16px;
  background-color: #ffffff;
  min-height: 100vh;
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.volunteer-approval .ant-typography {
  color: #1f1f1f;
  margin-bottom: 16px;
}

.volunteer-approval .search-bar {
  margin-bottom: 16px;
}

.volunteer-approval .ant-table-wrapper {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.volunteer-approval .ant-table {
  border: none;
}

.volunteer-approval .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #1f1f1f;
  text-align: left;
  font-size: 14px;
  padding: 10px 12px;
  border-bottom: 1px solid #e8e8e8;
}

.volunteer-approval .ant-table-tbody > tr > td {
  padding: 10px 12px;
  color: #595959;
  font-size: 14px;
}

.volunteer-approval .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
  cursor: pointer;
}

.volunteer-approval .ant-btn {
  border-radius: 4px;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .volunteer-approval {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .volunteer-approval {
    padding: 8px;
  }

  .volunteer-approval .ant-table-thead > tr > th,
  .volunteer-approval .ant-table-tbody > tr > td {
    padding: 8px 10px;
    font-size: 12px;
  }
}
