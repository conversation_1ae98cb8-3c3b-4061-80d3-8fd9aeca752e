-- 000024_update_event_schema.down.sql

-- Revert new columns from events table
ALTER TABLE events
DROP COLUMN IF EXISTS price,
DROP COLUMN IF EXISTS contact_email,
DROP COLUMN IF EXISTS contact_phone;

-- Step 0: Drop the default constraint from the new enum type.
ALTER TABLE events ALTER COLUMN status DROP DEFAULT;

-- Step 1: Alter columns using the current event_status_type to TEXT.
ALTER TABLE events ALTER COLUMN status TYPE TEXT;

-- Step 2: Drop the current ENUM type if it exists.
DROP TYPE IF EXISTS event_status_type;

-- Step 3: Create the old ENUM type with the original values.
CREATE TYPE event_status_type AS ENUM (
    'draft',
    'upcoming',
    'ongoing',
    'past',
    'cancelled'
);

-- Step 4: Alter columns back to the old ENUM type.
ALTER TABLE events ALTER COLUMN status TYPE event_status_type USING status::event_status_type;

-- Step 5: Reset the default value for the status column to its original default.
ALTER TABLE events ALTER COLUMN status SET DEFAULT 'draft'; 