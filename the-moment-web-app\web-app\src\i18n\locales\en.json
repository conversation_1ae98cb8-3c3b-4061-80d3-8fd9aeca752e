{"userSettings": {"tabs": {"infoChange": "Information Change", "preference": "Preferences", "identityVerification": "Identity Verification", "programApplications": "Program Applications"}, "personalInfo": {"title": "Update Personal Information", "subtitle": "Modify your personal information below.", "restrictedAlert": "To modify restricted fields (like name or identity documents), please <recoveryLink>submit an account recovery request</recoveryLink> to reset your verification status and complete a new verification process."}, "username": {"title": "Username", "subtitle": "Use a username to be more easily identified on the platform.", "usernameEdit": {"title": "Update Username", "subtitle": "Update your user display name.", "current": "Current username:", "change": "Change"}, "validation": {"required": "Please enter a username", "minLength": "Username must contain at least 4 characters", "maxLength": "<PERSON>rna<PERSON> cannot exceed 12 characters", "format": "Username can only contain letters, numbers, underscores, and hyphens"}, "messages": {"updateSuccess": "Username updated successfully", "updateError": "Username update failed, please try again later", "duplicateError": "This username is already in use", "tooFrequent": "Updates too frequent, please try again after 15 days"}}, "identityInfo": {"title": "Title", "userId": "User ID", "name": "Name", "chineseName": "Chinese Name", "gender": "Gender", "dateOfBirth": "Date of Birth", "phoneNumber": "Phone Number", "address": "Address"}, "formPlaceholders": {"selectTitle": "Please select a title", "enterName": "Please enter name", "enterEmail": "Please enter email", "enterPhone": "Please enter phone number", "enterIndustry": "Please enter industry", "enterCompany": "Please enter company name"}, "formValidation": {"titleRequired": "Please select a title!", "nameRequired": "Please enter your name!", "emailRequired": "Please enter a valid email!", "phoneRequired": "Please enter your phone number!", "industryRequired": "Please enter your industry!", "companyRequired": "Please enter your company name!"}, "buttons": {"saveChanges": "Save Changes", "savePreferences": "Save Preferences"}, "messages": {"updateSuccess": "Personal information updated successfully!", "updateError": "Failed to update information, please try again.", "preferencesSuccess": "Preferences updated successfully!", "languageSuccess": "Language settings updated successfully!", "languageError": "Language settings update failed"}, "preferences": {"title": "User Preferences", "subtitle": "Customize your user experience settings.", "language": {"title": "Language Settings", "subtitle": "Choose your preferred platform interface language.", "english": "English", "traditionalChinese": "Traditional Chinese", "simplifiedChinese": "Simplified Chinese", "messages": {"switchSuccess": "Language switched to {{lang}}"}}, "notifications": {"general": {"title": "General Notifications", "enableAll": "Enable All Notifications", "enableDescription": "Turn on or off all notifications."}, "channels": {"title": "Notification Channels", "subtitle": "Choose how you want to receive notifications", "inApp": "In-App Notifications", "inAppDescription": "Receive notifications directly within the app.", "email": "Email Notifications", "whatsapp": "WhatsApp Notifications", "whatsappDescription": "Receive notifications via WhatsApp messages.", "sms": "SMS Notifications", "smsDescription": "Receive notifications via SMS."}, "types": {"title": "Notification Types", "events": "Event Reminders", "eventsDescription": "Get notifications for upcoming events.", "posts": "Posts and Updates", "postsDescription": "Receive updates about new posts and content.", "promotional": "Promotional Notifications", "promotionalDescription": "Receive promotional offers and news."}}, "accessibility": {"title": "Accessibility Mode", "subtitle": "Adjust settings for better accessibility.", "normalMode": "Normal Mode", "seniorMode": "Senior-Friendly Mode", "seniorModeActiveNote": "Senior-friendly mode is now enabled, enhancing readability and ease of use.", "messages": {"switchSuccess": "Accessibility mode switched to {{mode}}"}}, "buttons": {"saveChanges": "Save Changes"}, "messages": {"updateSuccess": "Preferences updated successfully.", "updateError": "Failed to update preferences."}}, "identityVerification": {"title": "Identity Verification", "subtitle": "View and manage your identity verification status.", "hkidVerification": {"title": "Hong Kong ID Card Verification", "description": "Upload photos of the front and back of your Hong Kong ID card for verification.", "submitSuccess": "Hong Kong ID card verification submitted and under review", "submitError": "Failed to submit Hong Kong ID card verification"}, "mainlandPassVerification": {"title": "Mainland Travel Permit Verification", "description": "Please upload a photo of your valid mainland entry and exit document, including Hong Kong and Macau Residents' Mainland Travel Permit or Mainland Residents' Hong Kong and Macau Travel Permit.", "submitSuccess": "Mainland Travel Permit verification submitted and under review", "submitError": "Failed to submit Mainland Travel Permit verification"}, "passportVerification": {"title": "Passport Verification", "description": "Upload a photo of your passport personal information page for identity verification.", "submitSuccess": "Passport verification submitted and under review", "submitError": "Failed to submit passport verification"}, "hkyouthVerification": {"title": "HKYouth+ Verification", "description": "Upload your HKYouth+ photo for identity verification.", "submitSuccess": "HKYouth+ verification submitted and under review", "submitError": "Failed to submit HKYouth+ verification"}, "mainlandChinaIDVerification": {"title": "Mainland China ID Card Verification", "description": "Upload your Mainland China Resident ID Card for identity verification.", "submitSuccess": "Mainland China ID Card verification submitted and under review", "submitError": "Failed to submit Mainland China ID Card verification"}, "studentIDVerification": {"title": "Student ID Verification", "description": "Upload your valid student ID card for identity verification.", "submitSuccess": "Student ID verification submitted and under review", "submitError": "Failed to submit Student ID verification"}, "homeVisitVerification": {"title": "Home Visit Verification", "description": "Schedule a home visit for identity and address verification.", "submitSuccess": "Home Visit verification request submitted and under review", "submitError": "Failed to submit Home Visit verification request"}, "addressVerification": {"title": "Address Verification", "description": "Upload address proof documents issued within the last 3 months to verify your residential address.", "submitSuccess": "Address verification submitted and under review", "submitError": "Failed to submit address verification"}, "accountRecoveryAlert": "To modify verification documents, please <recoveryLink>submit an account recovery request</recoveryLink> to reset your verification status and complete a new verification process.", "messages": {"approved": "Your identity has been approved. You can participate in all activities and apply to become a volunteer. To update your verification information, please contact customer service to reset your verification status.", "unverified": "According to platform security requirements, participation in certain activities or becoming a volunteer requires identity and address verification. Please click the button below to start the verification process.", "pending": "Your verification application is being processed and is expected to take 3-5 working days. We will notify you of the result via email.", "rejected": "Your verification application has been rejected. Please make modifications according to the reason for rejection and resubmit your verification application.", "hkidVerificationSubmitted": "Hong Kong ID card verification application successfully submitted. We will process your application as soon as possible.", "mainlandPassVerificationSubmitted": "Mainland Travel Permit verification application successfully submitted. We will process your application as soon as possible.", "passportVerificationSubmitted": "Passport verification application successfully submitted. We will process your application as soon as possible.", "hkyouthVerificationSubmitted": "HKYouth+ verification application successfully submitted. We will process your application as soon as possible.", "mainlandChinaIDVerificationSubmitted": "Mainland China ID Card verification application successfully submitted. We will process your application as soon as possible.", "studentIDVerificationSubmitted": "Student ID verification application successfully submitted. We will process your application as soon as possible.", "homeVisitVerificationSubmitted": "Home Visit verification request successfully submitted. We will process your application as soon as possible.", "addressVerificationSubmitted": "Address verification application successfully submitted. We will process your application as soon as possible.", "hkidVerificationError": "Hong Kong ID card verification submission failed. Please try again later or contact customer service.", "mainlandPassVerificationError": "Mainland Travel Permit verification submission failed. Please try again later or contact customer service.", "passportVerificationError": "Passport verification submission failed. Please try again later or contact customer service.", "hkyouthVerificationError": "HKYouth+ verification submission failed. Please try again later or contact customer service.", "mainlandChinaIDVerificationError": "Mainland China ID Card verification submission failed. Please try again later or contact customer service.", "studentIDVerificationError": "Student ID verification submission failed. Please try again later or contact customer service.", "homeVisitVerificationError": "Home Visit verification request submission failed. Please try again later or contact customer service.", "addressVerificationError": "Address verification submission failed. Please try again later or contact customer service.", "hkidRejectionDescription": "The Hong Kong ID card document you provided is invalid or unclear. Please upload clear photos of the front and back of your ID card.", "mainlandPassRejectionDescription": "The Mainland Travel Permit document you provided is invalid or unclear. Please upload a clear photo of your travel permit.", "passportRejectionDescription": "The passport document you provided is invalid or unclear. Please upload a clear photo of your passport.", "hkyouthRejectionDescription": "The HKYouth+ document you provided is invalid or unclear. Please upload a clear photo of your youth card.", "mainlandChinaIDRejectionDescription": "The Mainland China ID Card document you provided is invalid or unclear. Please upload clear photos of your ID card.", "studentIDRejectionDescription": "The Student ID document you provided is invalid, expired, or unclear. Please upload a clear photo of your valid student ID card.", "homeVisitRejectionDescription": "Your home visit verification request has been rejected. Please ensure your address information is correct and reschedule the visit.", "addressRejectionDescription": "The address proof document you provided has expired. Please provide a valid document issued within the last 3 months.", "identityRequired": "You need to submit at least one identity document verification before proceeding with address verification."}, "labels": {"status": "Verification Status"}, "buttons": {"verifyNow": "Verify Now"}}, "contactVerification": {"title": "Phone Number", "subtitle": "Manage your phone number to ensure account security and receive notifications", "phone": {"title": "Phone Number", "description": "Your phone number is used for account security and receiving important notifications.", "current": "Current phone number:"}, "email": {"title": "Email Address", "description": "Your email is used for receiving notifications and account recovery.", "current": "Current email address:"}, "buttons": {"verify": "Verify", "change": "Change"}}, "programApplications": {"title": "Program Applications", "subtitle": "Apply for and check the status of various programs and services.", "labels": {"status": "Application Status"}, "status": {"approved": "Approved", "pending": "Under Review", "rejected": "Rejected", "unverified": "Not Applied"}, "messages": {"approved": "Your application has been approved. You can browse activities and apply to become a volunteer. To update your verification information, please contact customer service to reset your verification status.", "pending": "Your verification application is being processed and is expected to take 3-5 working days. We will notify you of the result via email.", "rejected": "Your application has been rejected. Please check the reason for rejection.", "unverified": "You have not applied for this program yet.", "volunteerSubmitSuccess": "Volunteer application successfully submitted!", "homeVisitSubmitSuccess": "Home visit program application successfully submitted!", "verificationRequired": "Identity and address verification required before applying", "verificationRequiredTitle": "Verification Required", "verificationRequiredDesc": "You must complete identity card and address verification before applying for the program."}, "buttons": {"applyNow": "Apply Now", "completeVerification": "Complete Verification"}, "volunteer": {"title": "Volunteer Program", "description": "Apply to become a volunteer and bring warmth and assistance to community events."}, "homeVisit": {"title": "Home Visit Program", "description": "Apply for our home visit program to receive care in your home."}, "modal": {"userInformation": "Your Information", "termsAndConditions": "Terms and Conditions", "agreeToTerms": "I have read and agree to the terms and conditions", "submit": "Submit Application", "messages": {"agreementRequired": "Please agree to the terms and conditions before submitting"}, "terms": {"responsibilities": "Responsibilities", "requirements": "Requirements", "benefits": "Benefits", "eligibility": "Eligibility Criteria", "services": "Services Provided", "frequency": "Visit Frequency", "policy": "Privacy Policy", "volunteerResponsibilities": "As a volunteer, you will assist with event management tasks, including helping users check in and maintaining event order.", "volunteerRequirements": "Volunteers must complete identity verification and comply with event management regulations.", "volunteerBenefits": "Volunteers will have the opportunity to serve the community and gain experience in event management.", "homeVisitEligibility": "To be eligible for home visits, you must be a registered member, have verified identity, and have documented assistance needs.", "homeVisitServices": "Our team can assist with basic household chores, provide necessities, and offer companionship services.", "homeVisitFrequency": "Visit frequency is determined based on individual needs, typically ranging from weekly to monthly.", "homeVisitPolicy": "All personal information shared during the application process and visits will be kept confidential."}}}}, "adminEvents": {"list": {"columns": {"coverImage": "Cover Image", "eventName": "Event Name", "date": "Date", "organization": "Organization", "location": "Location", "count": "Count", "status": "Status", "price": "Price", "type": "Type", "action": "Action"}, "buttons": {"edit": "Edit"}, "filters": {"searchEventName": "Search event name...", "physical": "Physical Location", "online": "Online Event", "hybrid": "Hybrid Mode", "free": "Free Events", "paid": "Paid <PERSON>"}}, "status": {"draft": "Draft", "published": "Published", "hidden": "Hidden"}, "report": {"tabs": {"allEvents": "All Events", "reports": "Reports"}, "empty": {"noTrendData": "No trend data for this filter"}, "statistics": {"totalEvents": "Total Events", "totalParticipants": "Total Participants", "averageParticipants": "Average Participants"}, "charts": {"participantsOverTime": "Participant Trends", "participantsDistribution": "Participant Distribution", "eventTypesDistribution": "Event Type Distribution", "lineChart": "Line Chart", "barChart": "Bar Chart", "topEvents": "Popular Events", "topCategories": "Event Types", "participants": "Participants", "events": "Events", "total": "Total", "showMore": "Show More", "participantsLegend": "Participants", "volunteersLegend": "Volunteers"}}, "singleEventReport": {"tabs": {"dashboard": "Dashboard", "attendanceRecords": "Attendance Records"}, "statistics": {"totalUsers": "Total Users", "totalEvents": "Total Events", "totalVolunteerHours": "Total Volunteer Hours", "participationRate": "Participation Rate", "participants": "Participants", "volunteers": "Volunteers", "registrationRate": "Registration Rate", "registrationProgress": "Registration Progress", "eventParticipation": "Event Participation"}, "charts": {"userParticipation": "User Participation Trends", "participationRate": "Participation Rate", "participationTrends": "Participation Trends", "dailyParticipants": "Daily Participants and Volunteers", "demographics": {"title": "Participant Demographics", "subtitle": "Participant distribution by age group and gender", "ageGroups": {"title": "By Age Group", "under18": "Under 18", "18to35": "18-35", "36to55": "36-55", "over55": "Over 55", "unknown": "Unknown Age"}, "gender": {"title": "By Gender", "male": "Male", "female": "Female", "unknown": "Unknown Gender"}, "amount": "Amount", "share": "Share"}}, "attendance": {"title": "Participant Data", "total": "Total participants", "columns": {"displayName": "Name", "applicationDate": "Application Date", "documentCount": "Documents Count", "actions": "Actions", "registrationRole": "Registration Role", "status": "Status", "userPhone": "User Phone", "paymentStatus": "Payment Status"}, "noDocuments": "No documents", "export": {"title": "Export", "formats": {"csv": "CSV", "pdf": "PDF", "png": "Image"}, "success": "Export successful", "error": "Export failed", "pdfNotImplemented": "PDF export is not fully implemented. Falling back to CSV.", "pngNotImplemented": "PNG export is not fully implemented. Please try CSV format."}, "verificationDetails": {"type": "Document Type", "status": "Status", "submittedAt": "Submitted At", "reviewedAt": "Reviewed At", "noRecords": "No verification records found"}}}}, "eventCalendar": {"eventList": {"price": {"prefix": "HK$", "free": "Free"}}, "modes": {"month": "Month", "year": "Year"}, "status": {"volunteer": "Volunteer", "completed": "Completed", "absent": "Absent"}, "timeUnits": {"day": "day", "days": "days", "dayAgo": "day ago", "daysAgo": "days ago"}, "tabs": {"upcomingEvents": "Upcoming Events", "pastEvents": "Past Events"}, "emptyStates": {"noUpcomingEvents": "No upcoming events", "noPastEvents": "No past events"}}, "eventQRCode": {"title": "Event Details and Check-in QR Code", "qrcode": {"title": "Check-in QR Code", "hint": "Please present this QR code at the event entrance"}, "userInfo": {"name": "Participant Name", "paymentStatus": "Payment Status", "statuses": {"paid": "Paid", "unpaid": "Unpaid"}}, "registrationType": {"participant": "Participant", "volunteer": "Volunteer"}, "buttons": {"cancelRegistration": "Cancel Registration", "viewDetails": "View Event Details"}, "cancelModal": {"title": "Cancel Event Registration", "content": "Are you sure you want to cancel your registration for this event? This action cannot be undone.", "confirm": "Yes, Cancel Registration", "cancel": "No, Keep Registration"}, "steps": {"title": "Event Participation Guide", "arrive": {"title": "Arrive on Time", "description": "Please arrive 15 minutes before the event starts"}, "present": {"title": "Check-in", "description": "Present your QR code to staff at the registration desk"}}, "mobileHint": "QR code can be quickly accessed via the bottom tab in the mobile app"}, "userProfile": {"buttons": {"editProfile": "Edit Profile", "viewAllEvents": "View All Past Events"}, "info": {"name": "Name", "chineseName": "Chinese Name", "phone": "Phone", "gender": "Gender", "dateOfBirth": "Date of Birth"}, "avatar": {"changeAvatar": "Change Avatar", "upload": {"success": "Avatar updated successfully", "error": "Avatar update failed", "validation": {"imageOnly": "Only image files can be uploaded", "maxSize": "Image size cannot exceed 2MB"}}}, "statistics": {"title": "Statistics Overview", "totalEvents": {"title": "Total Events", "suffix": ""}, "participantHours": {"title": "Activity Hours", "suffix": "hours"}, "volunteerHours": {"title": "Volunteer Activities", "suffix": "hours"}, "memberDays": {"title": "Member For", "suffix": "days"}, "memberSince": {"title": "Member Since"}}, "charts": {"monthlyParticipation": {"title": "Monthly Event Participation", "subtitle": "Showing data for the past 12 months", "tooltip": {"month": "Month", "events": "Events"}}, "eventCategories": {"title": "Event Type Distribution"}}}, "identityVerificationCard": {"status": {"approved": "Approved", "pending": "Under Review", "rejected": "Rejected", "unverified": "Unverified", "error": "Error"}, "messages": {"comment": "Verification Comment:", "pendingNotice": "Verification process takes 3-5 working days."}, "buttons": {"submit": "Submit Verification"}}, "home": {"sections": {"upcomingEvents": "Upcoming Events", "popularEvents": "Popular Events", "latestPosts": "Latest News"}, "buttons": {"viewAll": "View All"}, "messages": {"bannerError": "Failed to load banner, please try again later.", "upcomingError": "Failed to load upcoming events, please try again later.", "popularError": "Failed to load popular events, please try again later.", "latestPostsError": "Failed to load latest news, please try again later."}, "author": "Published by", "days": "days", "day": "day", "allOrganizationName": "Event Organization"}, "mainLayout": {"roles": {"super_admin": "Super Admin", "guest": "Guest", "admin": "Admin", "manager": "Manager", "user": "User"}, "header": {"searchPlaceholder": "Search...", "greeting": "Hello, {{name}}", "profile": "Profile", "settings": "Settings", "application": "Applications", "logout": "Logout"}, "menu": {"home": "Home", "organization": "Switch Organization", "allOrganizations": "All Organizations", "events": {"group": "Browse Events", "list": "Event List", "myEvents": "My Events", "managerAdmin": "Manager", "approveAttendee": "Approve Participants", "management": "Event Management"}, "userManagement": {"title": "User Management", "userManagement": "User Info Management", "managerAdmin": "Managers", "admin": "System Admins", "payments": "Payment Management", "verification": "User Verification", "editManagers": "Edit Manager Accounts", "approveVolunteers": "Volunteers"}, "news": {"groupTitle": "News", "publishedPosts": "Published News", "postManagement": "Post Management"}, "resources": {"groupTitle": "Resources", "publishedResources": "Published Resources", "resourceManagement": "Resource Management"}, "organizationSettings": "Organizations", "settings": "Settings", "application": "Applications"}, "breadcrumbs": {"home": "Home", "events": {"all": "All Events", "description": "Event Details", "management": "Event Management", "create": "Create New Event", "edit": "Edit Event", "reports": "Event Reports", "participants": "Participants", "participantVerification": "Verify Participants", "participantVerificationDetails": "Participant Verification Details"}, "myEvents": {"title": "My Events", "qrcode": "Event QR Code"}, "organizationSettings": {"title": "Organization Settings", "edit": "Edit Organization", "create": "Create Organization"}, "userManagement": {"title": "User Information Management", "approveVolunteers": "Approve Volunteers", "verification": "User Verification", "verificationDetails": "User Verification Details", "editManagers": "Edit Manager Accounts", "payment": "Payment Management", "volunteerDetails": "Volunteer Details", "volunteerQualificationDetails": "Volunteer Qualification Approval", "volunteerEventRegistrationDetails": "Volunteer Event Registration Approval", "management": "User Management"}, "news": {"posts": "News", "createPost": "Create News", "postDetails": "News Details", "editPost": "Edit News"}, "settings": "Account <PERSON><PERSON>", "application": "Applications", "userProfile": "User Profile", "resources": {"_": "Resources", "list": "Resources", "management": "Resource Management"}, "resourcesManagement": "Resource Management", "posts": {"management": "Posts Management", "create": "Create Post", "edit": "Edit Post", "details": "Post Details"}}, "userMenu": {"profile": "Profile", "logout": "Logout"}, "confirmLogout": {"title": "Are you sure you want to logout?", "buttons": {"signIn": "Sign In", "signUp": "Sign Up"}}, "footer": {"copyright": "© {{year}} Membership SAAS. All rights reserved."}, "expandSidebar": "Expand Sidebar", "collapseSidebar": "Collapse Sidebar", "sidebarLockedOnMobile": "Sidebar cannot be expanded in small screen mode"}, "events": {"hiddenEvent": "Event is hidden"}, "eventFilter": {"search": {"placeholder": "Search events..."}, "dateFilter": {"allDates": "All Dates", "thisWeek": "This Week", "nextWeek": "Next Week", "thisMonth": "This Month", "nextMonth": "Next Month", "next30Days": "Next 30 Days", "custom": "Custom Range"}, "eventTypes": {"placeholder": "Select Event Type", "options": {}}, "verificationTypes": {"placeholder": "Select Verification Type"}, "fundingTypes": {"placeholder": "Select Funding Type"}, "hotEvents": "Hot Events", "buttons": {"createEvent": "Create New Event"}}, "verificationModal": {"title": "Verification Required", "content": {"main": "This event requires identity verification. You need to complete the verification process before participating.", "requirements": "The verification process includes:", "items": {"hkid": "Hong Kong ID Card Verification", "address": "Address Proof Verification"}, "prompt": "Would you like to proceed with the verification process now?"}, "buttons": {"verify": "Go to Verification", "later": "Later"}}, "adminActions": {"reports": "View Reports", "edit": "Edit Event"}, "volunteerApproval": {"title": "Volunteer Approval", "search": {"placeholder": "Search by name"}, "applicationDetailsTitle": "Application Details", "tabs": {"pending": "Pending", "approved": "Approved", "identityApproval": "Volunteer Identity Approval", "eventVolunteerApproval": "Event Volunteer Applications"}, "table": {"columns": {"name": "English Name", "chineseName": "Chinese Name", "phoneNumber": "Phone Number", "submissionTime": "Submission Time", "applicationTime": "Application Time", "eventName": "Event Name", "verification_type": "Verification Type", "overallStatus": "Overall Status", "status": "Status", "documents": {"title": "Document Details", "type": "Document Type", "status": "Status", "submissionTime": "Submission Time", "reviewedAt": "Review Time", "reviewerName": "Reviewer"}, "actions": "Actions"}, "buttons": {"viewDetails": "View Details"}}, "buttons": {"userInfo": "User Info", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "approve": "Approve", "reject": "Reject", "withdraw": "Withdraw", "confirmRejected": "Confirm Rejection", "confirmApproved": "Confirm Approval", "confirmWithdrawn": "Confirm <PERSON>", "writeReason": "Please enter reason..."}, "review": {"qualificationDecision": "Volunteer Qualification Approval Decision", "eventRegistrationDecision": "Event Registration Approval Decision", "modifyDecision": "Modify Approval Decision", "accountHistory": "Account History", "by": "Reviewed by", "status": {"submitted": "Submitted", "completed": "Completed", "rejected": "Rejected", "in_progress": "In Progress", "withdrawn": "Withdrawn", "pending": "Pending", "approved": "Approved"}}, "personalInfo": {"username": "Username", "fullName": "English Name", "chineseName": "Chinese Name", "phoneNumber": "Phone", "gender": "Gender", "dateOfBirth": "Date of Birth"}, "statistics": {"totalHours": "Total Attendance Hours", "totalEvents": "Total Events Attended", "totalParticipantEvents": "Participant Events", "totalParticipantHours": "Participant Hours", "absentParticipantEvents": "Absent Events", "absentVolunteerEvents": "Absent Volunteer Events", "totalVolunteerEvents": "Volunteer Events", "totalVolunteerHours": "Volunteer Hours", "hoursDistribution": "Hours Distribution", "volunteerHours": "Volunteer Hours", "otherHours": "Other Hours", "eventParticipation": "Event Participation"}, "activityHistory": {"title": "Activity History", "status": {"completed": "Completed", "absent": "Absent"}}, "messages": {"approvedSuccess": "Volunteer application approved successfully", "rejectedSuccess": "Volunteer status successfully changed to rejected", "approvedError": "Failed to approve volunteer application", "rejectedError": "Failed to reject volunteer application", "reasonRequired": "Please provide a reason", "volunteerApproved": "Volunteer approved", "volunteerRejected": "Volunteer rejected", "volunteerApprovalPending": "Volunteer approval pending", "withdrawnSuccess": "Volunteer application withdrawn successfully", "withdrawnError": "Failed to withdraw volunteer application", "missingApplicationId": "Missing application ID for navigation."}, "eventDetails": {"title": "Event Details", "volunteers": "Volunteers", "required": "Required", "current": "Current", "maximum": "Maximum", "participants": "Participants", "eventInfo": "Event Information", "registrationStatus": "Registration Status", "participantRegistration": "Participant Registration", "estimatedVolunteers": "Estimated Volunteers", "volunteerEstimation": "Volunteer Count", "basedOnAttendees": "Based on 5% of total participants", "registered": "Registered", "waitingList": "Waiting List", "type": "Type", "price": "Price", "applicantStats": "Applicant Statistics", "previousEvents": "Previous Events", "missedEvents": "Missed Events", "absentParticipantEvents": "Absent Events", "description": "Description", "requirements": "Requirements"}}, "paymentManagement": {"title": "Payment and Registration Management", "tabs": {"pending": "Pending Requests", "processed": "Processed Requests"}, "statistics": {"pendingRegistrations": "Pending Registrations", "pendingRefunds": "Pending Refunds", "pendingAmount": "Pending Amount"}, "table": {"columns": {"event": "Event", "customer": "Customer", "type": "Type", "amount": "Amount", "date": "Date", "actions": "Actions"}, "types": {"registration": "Registration", "refund": "Refund"}, "buttons": {"approve": "Approve", "details": "Details"}}, "detailsModal": {"title": "Request Details", "fields": {"id": "ID", "type": "Type", "event": "Event", "amount": "Amount", "customer": "Customer", "email": "Email", "date": "Date", "status": "Status", "notes": "Additional Notes"}, "buttons": {"reject": "Reject", "approve": "Approve"}}}, "userVerificationAdmin": {"title": "User Verification Management", "table": {"columns": {"name": "English Name", "chineseName": "Chinese Name", "phoneNumber": "Phone Number", "submissionTime": "Submission Time", "verification_type": "Verification Type", "overallStatus": "Overall Status", "status": "Status", "documentCount": "Request Count", "multipleDocuments": "Multiple Documents", "documents": {"title": "Document Details", "type": "Document Type", "status": "Status", "submissionTime": "Submission Time", "reviewedAt": "Review Time", "reviewerName": "Reviewer"}, "actions": "Actions"}, "buttons": {"viewDetails": "View Details"}}, "buttons": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected"}}, "proofDetails": {"title": "User Verification Details", "reviewTitle": "Document Review Decision", "identityCardVerificationDecision": "Identity Document Approval Decision", "addressProofVerificationDecision": "Address Proof Approval Decision", "overallRequestReview": "Overall Request Review", "sections": {"userInfo": {"title": "User Information", "fields": {"name": "Name", "phoneNumber": "Phone", "chineseName": "Chinese Name", "identityDocType": "Verification Type", "passportNumber": "Passport Number", "issuingCountry": "Issuing Country", "issueDate": "Issue Date", "expiryDate": "Expiry Date", "permitNumber": "Permit Number", "mainlandIdNumber": "Mainland ID Number", "validUntil": "<PERSON>id <PERSON>", "memberNumber": "HK Youth+ Member Number", "schoolName": "School Name", "grade": "Grade", "status": "Status", "submissionTime": "Submission Time", "infoCategory": "Information Category", "displayName": "Display Name", "is_permanent": "Is Hong Kong Permanent Resident"}}, "verificationSpecifics": {"title": "{{type}} Verification Details"}, "documentReview": {"title": "Document Review", "fields": {"fullname": "Name", "chineseName": "Chinese Name", "phoneNumber": "Phone", "gender": "Gender", "idNumber": "ID Number", "dateOfBirth": "Date of Birth", "address": "Address", "documentNumber": "Document Number", "documentType": "Document Type", "flatNumber": "Unit Number", "floor": "Floor", "buildingName": "Building Name", "streetAddress": "Street Address", "district": "District", "region": "Region", "completeAddress": "Complete Address", "personalInformation": "Personal Information", "documentInformation": "Document Information", "addressDetails": "Address Details"}}, "verificationHistory": {"title": "Verification History", "noHistory": "No verification history records yet", "timeline": {"DocumentUploaded": "Document Uploaded", "Approved": "Approved", "Rejected": "Rejected", "AdminAction": "Admin Action"}}}, "tabs": {"identity": "Identity Information", "address": "Address Information"}, "verificationStatus": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "withdrawn": "Withdrawn"}, "documentViewer": {"documentType": {"idFront": "ID Front", "idBack": "ID Back", "hkid": "Hong Kong ID Card", "passport": "Passport", "mainlandTravelPermit": "Mainland Travel Permit", "hkyouth": "HKYouth+", "address": "Address Proof", "phoneBill": "Phone Bill", "bankStatement": "Bank Statement", "utilityBill": "Utility Bill", "others": "Other Document Types"}, "noDocuments": "No documents available", "loadingDocument": "Loading document...", "imageNotAvailable": "Image not available", "download": "Download", "viewPdf": "Click to view PDF", "fetchError": "Failed to fetch document"}, "rejectModal": {"title": "Select Rejection Reason", "reason": {"label": "Reason", "placeholder": "Please select a reason", "options": {"incomplete": "Incomplete Information", "invalid": "Invalid Document", "mismatch": "Information Mismatch", "expired": "Document Expired", "custom": "Custom Reason"}}, "customReason": {"label": "Custom Reason", "placeholder": "Please enter reason"}}, "buttons": {"reject": "Reject", "approve": "Approve"}, "messages": {"approvedSuccess": "Request approved", "rejectedSuccess": "Request rejected", "reasonRequired": "Please select a rejection reason", "notFound": "Verification details not found", "invalidData": "Invalid data", "updateError": "Failed to update user verification", "approvedError": "Failed to approve document", "rejectedError": "Failed to reject document", "allVerificationsComplete": "All verifications completed. Returning to list.", "pendingSuccess": "Document status set to pending", "pendingError": "Failed to set document status to pending", "withdrawnSuccess": "Document status set to withdrawn", "withdrawnError": "Failed to set document status to withdrawn"}}, "accountRecovery": {"title": "Account Recovery", "subtitle": "Please provide your contact information and details to help us recover your account.", "messages": {"success": "Recovery request successfully submitted. We will contact you as soon as possible.", "error": "Failed to submit recovery request. Please try again."}, "form": {"labels": {"phoneNumber": "Phone Number", "email": "Email Address", "details": "Additional Information", "documents": "Supporting Documents"}, "placeholders": {"email": "Please enter your email address", "details": "Please provide any additional information that may help us verify your identity..."}, "validation": {"phoneRequired": "Please enter your phone number!", "phoneFormat": "Please enter a valid 8-digit phone number!", "emailRequired": "Please enter your email address!", "emailFormat": "Please enter a valid email address!", "detailsRequired": "Please provide additional information!", "detailsLength": "Details must be at least 20 characters!", "documentsRequired": "Please upload at least one identity document!", "fileType": "You can only upload PDF, JPEG, PNG, or JPG files!", "fileSize": "File must be smaller than 10MB!"}, "hints": {"documents": "Upload identity documents you used for verification to help us review your request."}}, "buttons": {"back": "Back", "submit": "Submit Request", "upload": "Upload File"}}, "managerAccounts": {"title": "Edit Manager Accounts", "table": {"columns": {"name": "Manager Name", "status": "Status", "assignedEvents": "Assigned Events", "actions": "Actions"}, "status": {"assigned": "Assigned", "unassigned": "Unassigned"}, "buttons": {"assign": "Assign"}, "moreEvents": "{{count}} more"}, "assignModal": {"title": "Assign Events to {{name}}", "placeholder": "Assign New <PERSON>", "buttons": {"save": "Save"}}, "messages": {"updateSuccess": "Manager updated successfully", "updateError": "Error updating manager status", "managerNotFound": "Manager not found", "invalidData": "Invalid data"}}, "resourceList": {"title": "Resources", "files": "files", "noFilesAttached": "No files attached.", "noFiles": "No files", "actions": {"deleteFile": "Delete File"}, "search": {"placeholder": "Search resources"}, "login": {"required": "<PERSON><PERSON> Required", "message": "You need to be logged in to download resources. Please log in first.", "button": "Go to Login"}, "fileColumns": {"fileName": "File Name", "fileType": "File Type", "fileSize": "File Size", "uploadedAt": "Uploaded At", "actions": "Actions"}, "dateFilter": {"allDates": "All dates", "last7Days": "Last 7 days", "last30Days": "Last 30 days", "thisMonth": "This month", "lastMonth": "Last month"}, "statusFilter": {"placeholder": "Filter by status...", "all": "All Statuses", "published": "Published", "draft": "Draft", "hidden": "Hidden"}, "buttons": {"upload": "Upload", "download": "Download", "edit": "Edit", "delete": "Delete", "selectMore": "Select Multiple", "cancel": "Cancel"}, "batchDownload": {"showDetails": "Show Details", "hideDetails": "Hide Details", "total": "Total: {{count}} files, {{size}}", "fileCount": "{{count}} files, {{size}}", "noItemsSelected": "No items selected for batch download.", "noFilesSelected": "No downloadable files in selected items.", "startingFull": "Starting batch download of {{count}} files..."}, "messages": {"deleteConfirm": "Are you sure you want to delete this resource?", "deleteSuccess": "Resource deleted successfully", "deleteFailed": "Failed to delete resource", "deleteResourceConfirmTitle": "Delete '{{resourceName}}'?", "deleteResourceConfirmContent": "Are you sure you want to delete this resource? This action cannot be undone.", "deleteFileConfirmTitle": "Delete File?", "deleteFileConfirmContent": "Are you sure you want to delete the file '{{fileName}}'?", "deleteFileSuccess": "File '{{fileName}}' deleted successfully.", "deleteFileFailed": "Failed to delete file '{{fileName}}'.", "selectOrgForUpload": "Please select an organization first to upload resources.", "selectOrgForEdit": "Please select an organization to edit this resource.", "editContextWarning": "Editing operations will be performed in the context of the organization this resource belongs to.", "invalidOrgContext": "Invalid organization context.", "createFailedNoId": "Failed to create resource: server did not return an ID.", "fileRemoved": "File '{{fileName}}' has been removed.", "fileUploadSuccess": "File '{{fileName}}' uploaded successfully.", "fileUploadFailed": "File '{{fileName}}' upload failed.", "uploadSuccess": "Resource uploaded successfully", "updateSuccess": "Resource updated successfully", "uploadSuccessDraft": "Resource saved as draft.", "updateSuccessDraft": "Resource updated and saved as draft.", "uploadFailed": "Failed to upload resource", "updateFailed": "Failed to update resource", "downloadAllFilesStarting": "Starting download of {{count}} files...", "noFilesToDownload": "This resource has no files to download.", "alreadyInDownloadGroup": "This file is already in your download list", "addedToDownloadGroup": "{{fileName}} added to your download list", "downloadAll": "Start downloading current resource", "unauthorized": "You are not authorized to perform this action.", "downloadErrorMissingInfo": "Cannot download file: missing file or resource information.", "downloadStartingFor": "Starting download for '{{fileName}}'...", "downloadSuccessFor": "Successfully downloaded '{{fileName}}'.", "downloadFailedFor": "Failed to download '{{fileName}}'."}, "updatedAt": "Last updated:", "description": {"expand": "Expand", "collapse": "Collapse"}}, "postList": {"hiddenPost": "News is hidden", "hiddenPostDescription": "This news is hidden and only visible to administrators.", "title": "News List", "search": {"placeholder": "Search news"}, "filterByTagPlaceholder": "Filter by tag...", "statusFilter": {"all": "All", "published": "Published", "draft": "Draft", "hidden": "Hidden"}, "buttons": {"create": "Create News", "edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Are you sure you want to delete this news?", "okText": "Yes", "cancelText": "No"}, "tags": {"placeholder": "Select a tag"}, "dateFilter": {"allDates": "All dates", "thisWeek": "This week", "nextWeek": "Next week", "thisMonth": "This month", "nextMonth": "Next month"}, "noPosts": "No news found", "messages": {"deleteSuccess": "News deleted successfully", "deleteError": "Failed to delete news", "deleteUnauthorized": "You don't have permission to delete this news", "orgIdMissingForDelete": "Organization ID is missing for this news"}}, "uploadResourceModal": {"title": {"create": "Upload New Resource", "edit": "Edit Resource"}, "form": {"title": {"label": "Title", "placeholder": "Please enter resource title", "rules": {"required": "Please enter a title", "maxLength": "Title cannot exceed 100 characters"}}, "description": {"label": "General Introduction", "placeholder": "Please enter resource general introduction", "rules": {"maxLength": "General introduction cannot exceed 200 characters"}}, "file": {"label": "Files", "dragText": "Click or drag files to this area to upload", "hint": {"limits": "File size limit: {{size}}, maximum {{count}} files"}, "rules": {"required": "Please upload files"}, "placeholder": "Please enter file description...", "actions": {"clickToAdd": "Click here to add file description"}}, "visibility": {"label": "Visibility", "rules": {"required": "Please select visibility"}, "options": {"public": "Public", "org_only": "Organization Only"}}, "published_at": {"label": "Schedule Publication On", "placeholder": "Select publication date and time"}}, "buttons": {"saveAsDraft": "Save as Draft", "publish": "Publish", "upload": "Upload", "save": "Save"}, "validation": {"fileRequired": "Please upload a file", "fileType": "Unsupported file type. Please upload {{extensions}}", "fileSize": "File must be smaller than {{size}}", "fileCount": "Cannot upload more than {{count}} files"}, "confirmPublish": {"title": "Confirm Publication", "contentBase": "Are you sure you want to publish this resource?", "details": {"publicVisibility": "It will be publicly visible.", "orgVisibility": "It will only be visible to members of its designated organization.", "scheduled": "It is scheduled to be published on {{publishTime}}.", "immediatePublish": "It will be published immediately."}}}, "postPage": {"sections": {"tags": "Tags", "attachments": "Attachments"}, "attachments": {"image": "Image", "video": "Video", "document": "Document"}, "buttons": {"download": "Download Attachments", "edit": "Edit News", "preview": "Preview", "delete": "Delete"}, "readingTime": {"minRead": " min read"}}, "createPost": {"title": {"create": "Create News", "edit": "Edit News"}, "form": {"title": {"label": "Title", "placeholder": "Please enter news title", "required": "Please enter a title!", "minLength": "Title must be at least 5 characters", "maxLength": "Title cannot exceed 30 characters"}, "content": {"label": "Main Content", "placeholder": "Enter news content here...", "required": "Please enter news content!"}, "tags": {"label": "Post Tag", "placeholder": "Select a tag", "required": "Please select a tag for the post", "manageAll": "Manage Tags", "managerModal": {"title": "Tag Management", "existingTags": "Existing Tags", "createNew": "Create New Tag", "noTags": "No tags available", "english": "English", "traditionalChinese": "Traditional Chinese", "simplifiedChinese": "Simplified Chinese", "deleteConfirmTitle": "Confirm Delete Tag", "deleteConfirmContent": "Are you sure you want to delete this tag? This action cannot be undone."}, "createModal": {"title": "Create New Tag", "nameEn": "English Name", "nameEnRequired": "Please enter English name", "nameEnMaxLength": "English name cannot exceed 50 characters", "nameEnPlaceholder": "Please enter English tag name", "nameZhHk": "Traditional Chinese Name", "nameZhHkRequired": "Please enter Traditional Chinese name", "nameZhHkMaxLength": "Traditional Chinese name cannot exceed 50 characters", "nameZhHkPlaceholder": "Please enter Traditional Chinese tag name", "nameZhCn": "Simplified Chinese Name", "nameZhCnRequired": "Please enter Simplified Chinese name", "nameZhCnMaxLength": "Simplified Chinese name cannot exceed 50 characters", "nameZhCnPlaceholder": "Please enter Simplified Chinese tag name", "cancel": "Cancel", "reset": "Reset", "create": "Create Tag"}}, "hidden": {"label": "Hide Post", "checkboxLabel": "Hide from the public"}, "publishAt": {"label": "Schedule Publication", "tooltip": "Set a scheduled publication time, and the post will be automatically published at the specified time. All times are in Hong Kong timezone.", "placeholder": "Select publish date and time", "timezonehint": "All times are in %{timezone} timezone", "pastDateWarning": "Cannot select a time in the past."}, "attachments": {"label": "Attachments", "required": "Please upload at least one attachment!", "imageSection": "Images", "videoSection": "Videos", "documentSection": "Document Links", "uploadImage": "Upload Image", "dragVideo": "Drag video files here or click to upload", "imageTip": "Supports JPG, PNG, GIF, WEBP. Max 5MB per image.", "videoTip": "Supports MP4, MOV, AVI, WEBM. Max 100MB per video.", "documentTip": "Supports PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX. Max 20MB per document.", "addLink": "Add Document Link", "linkNamePlaceholder": "Enter link name (e.g., Report Q1)", "linkUrlPlaceholder": "Enter URL (e.g., http://example.com/doc.pdf)", "validation": {"imageType": "Only JPG, PNG, GIF, WEBP images are allowed!", "imageSize": "Image must be smaller than 5MB!", "videoType": "Only MP4, MOV, AVI, WEBM videos are allowed!", "videoSize": "Video must be smaller than 100MB!", "documentType": "Only PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX documents are allowed!", "documentSize": "Document must be smaller than 20MB!", "linkNameRequired": "Link name is required.", "linkUrlRequired": "Link URL is required.", "linkUrlInvalid": "Please enter a valid URL.", "fileType": "Only images, videos, and documents are allowed!"}}, "coverImage": {"label": "Cover Image", "upload": "Upload Cover Image", "tip": "Recommended: 16:9 aspect ratio, < 2MB. Supports JPG, PNG, GIF, WEBP.", "required": "Cover image is required.", "uploadError": "Cover image upload failed."}, "attachmentUpload": {"label": "Upload Attachments (Images, Videos, Documents)", "upload": "Click to Upload Attachments", "tip": "Max 10 files. Images (<10MB), Videos (<100MB), Documents (<20MB).", "maxError": "You can only upload up to 10 attachments."}}, "buttons": {"publish": "Publish", "update": "Update", "cancel": "Cancel", "saveDraft": "Save Draft"}, "messages": {"createSuccess": "News created successfully!", "updateSuccess": "News updated successfully!", "draftSuccess": "Draft saved successfully!", "scheduledAsDraft": "Post will be saved as draft and published automatically at the scheduled time.", "createError": "Failed to create news", "updateError": "Failed to update news", "draftError": "Failed to save draft", "noChanges": "No changes detected to save.", "missingOrgIdCreate": "Organization ID is missing. Cannot create post.", "missingOrgIdEdit": "Organization ID is missing. <PERSON><PERSON> edit post.", "tagCreatedSuccess": "Tag created successfully!", "tagDeletedSuccess": "Tag deleted successfully!"}, "errors": {"fetchTagsError": "Failed to load tags. Please try again.", "missingOrgIdEditMode": "Organization ID missing for post edit mode. Redirecting...", "deleteMediaError": "Failed to delete media: {{mediaId}}", "uploadCoverError": "Failed to upload cover image.", "uploadAttachmentError": "Failed to upload attachment: {{fileName}}", "missingOrgIdFromApi": "Organization ID not found. Unable to edit post.", "contentRequired": "Please enter post content!", "organizationMissing": "No valid organization found, please try again.", "missingInitialData": "Initial data is missing for edit operation.", "tagCreatePermission": "Only administrators can create new tags.", "tagCreateError": "Failed to create tag. Please try again.", "tagDeletePermission": "You do not have permission to delete this tag.", "tagDeleteError": "Failed to delete tag. Please try again."}}, "eventEdit": {"title": {"create": "Create New Event", "edit": "Edit Event"}, "form": {"title": {"label": "Event Title", "required": "Please enter event title!", "placeholder": "Please enter event title", "minLength": "Event title must be at least 5 characters", "maxLength": "Event title cannot exceed 30 characters"}, "type": {"label": "Event Type", "required": "Please select event type!", "placeholder": "Select event type", "manageAll": "Manage Event Tags"}, "info": {"label": "Event Information"}, "hidden": {"label": "Hide Event", "checkboxLabel": "Hide event from public listings"}, "location": {"label": "Event Location", "required": "Please enter event location!", "placeholder": "Enter location"}, "locationType": {"label": "Location Type", "required": "Please select location type", "options": {"physical": "Physical Location", "online": "Online Event", "hybrid": "Hybrid Mode"}}, "locationOnlineUrl": {"label": "Online Event URL", "required": "Please enter online event URL", "invalid": "Please enter a valid URL", "placeholder": "Enter online event URL (e.g., https://zoom.us/j/xxxx)"}, "dateRange": {"label": "Event Date/Time Range", "required": "Please select date and time range!", "presets": {"2hours": "Now ~ 2 hours later", "4hours": "Now ~ 4 hours later", "8hours": "Now ~ 8 hours later", "3days": "Now ~ 3 days later", "7days": "Now ~ 7 days later"}}, "maxAttendees": {"label": "Maximum Attendees", "required": "Invalid number of attendees!"}, "maxWaitingList": {"label": "Maximum Waiting List Size", "required": "Please enter a valid waiting list size!", "tip": "Maximum number of people who can join the waiting list when the event is full"}, "applicationFee": {"label": "Registration Fee ($ HKD)", "required": "Please enter registration fee!"}, "publishAt": {"label": "Schedule Publication", "placeholder": "Select publish date and time (optional)", "tooltip": "Set a scheduled publication time, and the event will be automatically published at the specified time. All times are in Hong Kong timezone.", "timezonehint": "All times are in %{timezone} timezone", "pastDateWarning": "Cannot select a time in the past."}, "description": {"label": "Event Description", "required": "Please enter event description!"}, "organization": {"label": "Event Organization"}, "attachments": {"label": "Event Media"}, "documents": {"label": "Required Verification Documents", "placeholder": "Select required verification documents", "tooltip": "Please select verification documents required for the event, such as Hong Kong ID card, passport, etc. Multiple selections allowed."}, "governmentFundingType": {"label": "Government Funding Type", "tooltip": "Select applicable government funding type (if any)", "placeholder": "Select funding type"}, "coverImage": {"label": "Event Cover Image", "upload": "Upload Cover Image", "tip": "Recommended to use an image with 16:9 aspect ratio, maximum file size 5MB", "required": "Please upload event cover image!"}, "media": {"image": {"label": "Image Upload (Drag & Drop)", "hint": "Click or drag images to this area to upload"}, "video": {"label": "Video Upload", "hint": "Click or drag videos to this area to upload"}}}, "buttons": {"saveAsDraft": "Save as Draft", "cancelEvent": "Cancel Event"}, "messages": {"updateSuccess": "Event details updated successfully!", "updateError": "Failed to update event details", "fetchTagsError": "Failed to fetch event tags", "fetchVerificationTypesError": "Failed to fetch verification types", "fetchGovernmentFundingTypesError": "Failed to fetch government funding types", "invalidDate": "Invalid date format", "organizationRequired": "Organization must be selected", "createSuccess": "Event created successfully!", "createError": "Failed to create event", "createErrorNoId": "Failed to create event: No ID returned from server.", "scheduledAsDraft": "Event will be saved as draft and published automatically at the scheduled time.", "mediaUploadFailed": "Some media files failed to upload. Please check and try again.", "maxMediaFilesError": "You can upload a maximum of 10 media files (excluding cover image).", "maxVideoFilesError": "You can upload a maximum of 5 video files.", "contentRequired": "Event content cannot be empty.", "bannerUploadFailed": "Cover image upload failed", "cancelSuccess": "Event cancelled successfully", "cancelError": "Failed to cancel event", "imageUploadError": "You can only upload image files!", "videoUploadError": "You can only upload MP4 or AVI format video files!"}, "confirmations": {"publish": {"title": "Confirm Publication", "content": "Are you sure you want to publish this event? It will be publicly visible.", "contentHidden": "Are you sure you want to publish this event? It will be marked as hidden and not visible in public listings."}, "cancelEvent": {"title": "Confirm Event Cancellation", "content": "Are you sure you want to cancel this event? This action cannot be undone and will remove it from public listings."}}}, "customizedDropdown": {"button": "Custom Fields"}, "login": {"title": "<PERSON><PERSON>", "welcome": "Welcome", "description": "Login to explore more exciting events and participate in volunteer services.", "form": {"hints": {"autoRegister": "If this number is not registered, the system will automatically create a new account."}, "labels": {"phoneNumber": "Phone Number", "verificationCode": "Verification Code", "userLogin": "User Login", "staffLogin": "Staff Login", "email": "Email", "password": "Password", "displayName": "Display Name"}, "placeholders": {"verificationCode": "Please enter verification code", "email": "Email", "password": "Password", "displayName": "Please enter your display name"}, "validation": {"phoneRequired": "Please enter phone number!", "phoneFormat": "Phone number must be 8 digits", "codeRequired": "Please enter verification code!", "emailRequired": "Please enter email address", "emailFormat": "Please enter a valid email address", "passwordRequired": "Please enter password", "displayNameRequired": "Please enter display name!", "verificationCodeLength": "Verification code must be 6 digits!"}, "options": {"useWhatsApp": "Send verification code using WhatsApp"}}, "buttons": {"backHome": "Back to Home", "continue": "Continue", "sendCode": "Send Verification Code", "resend": "Resend ({{seconds}}s)", "whatsapp": "Login with WhatsApp", "accountRecovery": "Can't access your account?", "staffLogin": "Staff Login", "skip": "<PERSON><PERSON>"}, "messages": {"codeSent": "Verification code sent!", "formError": "Please fix errors in the form.", "loginSuccess": "Login successful!", "loginError": "Invalid login credentials.", "agreementRequired": "You must agree to our Terms of Service and Privacy Policy", "redirectToRegister": "You are a new user! Redirecting to create account...", "sendCodeError": "Failed to send verification code. Please try again.", "registrationSuccess": "Registration completed successfully!", "registrationError": "Registration failed, please try again.", "displayNameUpdated": "Display name updated successfully", "displayNameUpdateFailed": "Display name has been updated, but final synchronization may take some time", "staffLoginSuccess": "Staff login successful!"}, "errors": {"pkceStateMissing": "Security state missing. Please try again.", "csrfMismatch": "Security verification failed. Please try again.", "userStatusUnknown": "Unable to determine account status. Please try again.", "staffNotFound": "Staff email not found.", "userNotStaff": "This email does not belong to a staff account.", "staffLoginError": "Staff login failed. Please try again."}, "usernameModal": {"title": "Set Display Name"}, "modal": {"newUser": {"title": "Complete Registration", "content": "You have not completed the registration process. Would you like to complete it now to use all features?", "confirm": "Yes, Complete Registration", "cancel": "Not Now"}}, "divider": "or"}, "register": {"title": "Create Account", "description": "After creating an account, you can register for various exciting events, become a volunteer, and receive event reminders.", "form": {"labels": {"phoneNumber": "Phone Number", "verificationCode": "Verification Code"}, "placeholders": {"verificationCode": "Please enter verification code"}, "validation": {"phoneRequired": "Please enter phone number!", "phoneFormat": "Phone number must be 8 digits", "codeRequired": "Please enter verification code!", "agreementRequired": "You must agree to our Terms of Service and Privacy Policy"}}, "agreement": {"text": "I agree to the <terms>Terms of Service</terms> and <privacy>Privacy Policy</privacy>", "terms": "Terms of Service", "privacy": "Privacy Policy"}, "buttons": {"backHome": "Back to Home", "continue": "Continue", "sendCode": "Send Code", "resend": "Resend ({{seconds}}s)", "whatsapp": "Register with WhatsApp"}, "messages": {"codeSent": "Verification code sent!", "formError": "Please fix errors in the form."}, "divider": "or", "steps": {"basicInfo": {"title": "Basic Information", "header": {"title": "Personal Information Requirements", "instructions": ["Please ensure the personal information you provide is accurate.", "We need your email address to notify you of approval results for event applications or personal requests.", "Some events may require identity verification, which may need additional documents. This step is optional."], "description": "Providing accurate information helps us better understand you and effectively manage the verification process for your event requests."}}, "emailVerification": {"title": "Email Verification", "header": {"title": "Verify Your Email", "instructions": ["Receive identity verification results", "Receive event application approval updates", "Stay informed about important account activities and updates"], "description": "Email verification helps ensure secure and effective communication for your account activities. Please ensure you can access this email address."}}, "identityVerification": {"title": "Identity Verification", "description": "(Optional)"}}, "modal": {"identityVerification": {"title": "Identity Document Verification Complete!", "content": {"description": "You have completed the first step of identity verification. To fully participate in all activities, you need to complete the address verification process.", "step1": "Identity Document Verification (Completed)", "step2": "Address Verification (Next Step)", "addressVerification": "Address verification helps ensure the safety of all participants. This step requires providing a valid proof of residence document."}, "buttons": {"proceed": "Continue to Address Verification", "skip": "Complete Later"}}}}, "privacyPolicy": {"title": "Privacy Policy", "updatedAt": "Last updated: January 16, 2025", "sections": {"dataCollection": {"title": "1. Data Collection", "content": "The personal data we collect includes but is not limited to: name, phone number, email address, Hong Kong ID card information (for identity verification only), and event participation records. This data is used to provide services, improve user experience, ensure event safety, and fulfill legal obligations."}, "dataUsage": {"title": "2. Data Usage", "content": "We use the collected data to: manage your account, process event registrations, send event reminders, perform identity verification, provide customer support, and ensure platform security. We will not use your personal data for other purposes without your consent."}, "dataProtection": {"title": "3. Data Protection", "content": "We employ industry-standard encryption technology and security measures to protect your personal data. Only authorized personnel can access this data, and only to the extent necessary to perform their job duties."}, "dataSharing": {"title": "4. Data Sharing", "content": "We will not share your personal data with third parties unless required by law or with your explicit consent. In certain circumstances, we may share necessary data with event partners, but only to the extent required for event organization."}, "yourRights": {"title": "5. Your Rights", "content": "You have the right to view and correct your personal data, and to request account deletion. To exercise these rights, please contact us through customer service. Please note that some data may not be deletable due to legal requirements."}}, "footer": "If you have any questions about our privacy policy, please contact our customer service department.", "buttons": {"back": "Back"}}, "termsOfService": {"title": "Terms of Service", "updatedAt": "Last updated: January 16, 2025", "introduction": "Please read the following terms carefully as they constitute a legally binding agreement between you and us.", "sections": {"serviceScope": {"title": "1. <PERSON>", "content": "We provide membership management, event registration, and identity verification services. You need to complete identity verification to use the full membership features, including event registration and becoming a volunteer. We reserve the right to modify, suspend, or terminate services at any time."}, "membershipEligibility": {"title": "2. Membership Eligibility", "content": "You must hold a valid Hong Kong ID card to register as a member. You should provide true, accurate, and complete personal information, and update it promptly when changes occur."}, "identityVerification": {"title": "3. Identity Verification", "content": "To ensure service quality and security, you need to complete an identity verification process. This includes uploading photos of your Hong Kong ID card and other necessary documents. We will review the information you submit and reserve the right to reject verification."}, "eventParticipation": {"title": "4. Event Participation", "content": "You can register for events published on the platform. After registration, you should follow the specific rules and requirements of the event. If you need to cancel, please notify the organizer in advance. For volunteer positions, you must complete identity verification before registration."}, "accountSecurity": {"title": "5. Account Security", "content": "You are responsible for maintaining the security of your account and password. Do not share your account with others. If you suspect your account has been compromised, please contact customer service immediately. You should change your password regularly and ensure your contact information (such as phone number) remains valid."}, "privacyProtection": {"title": "6. Privacy Protection", "content": "We value your privacy and protect your personal data according to our Privacy Policy. We will only use your data to the extent necessary and will not disclose your personal information to third parties without your consent."}, "disclaimer": {"title": "7. <PERSON><PERSON><PERSON>", "content": "We are not responsible for service interruptions due to network issues, system maintenance, force majeure, or other reasons. For accidents or losses that may occur during events, we recommend purchasing appropriate insurance."}}, "footer": "If you have any questions about these Terms of Service, please contact our customer service team. Phone: +852 2222 2234, Email: <EMAIL>", "buttons": {"back": "Back"}}, "registration": {"steps": {"basicInfo": {"title": "Basic Information", "header": {"title": "Personal Information Requirements", "instructions": ["Please ensure the personal information you provide is accurate.", "We need your email address to notify you of approval results for event applications or personal requests.", "Some events may require identity verification, which may need additional documents. This step is optional."], "description": "Providing accurate information helps us better understand you and effectively manage the verification process for your event requests."}}, "emailVerification": {"title": "Email Verification", "header": {"title": "Verify Your Email", "instructions": ["Receive identity verification results", "Receive event application approval updates", "Stay informed about important account activities and updates"], "description": "Email verification helps ensure secure and effective communication for your account activities. Please ensure you can access this email address."}}, "identityVerification": {"title": "Identity Verification", "description": "(Optional)"}}, "form": {"labels": {"title": "Title", "fullName": "Name", "firstName": "First Name", "lastName": "Last Name", "chineseName": "Chinese Name", "hkid": "Hong Kong ID Card", "passport": "Passport", "passportCountry": "Country or Region", "mainlandTravelPermit": "Mainland Travel Permit", "idDocumentType": "Identity Document Type", "idNumber": "Document Number", "dateOfBirth": "Date of Birth", "gender": "Gender", "industry": "Industry", "company": "Company Name", "email": "Email Address", "verificationCode": "Verification Code"}, "placeholders": {"title": "Please select a title", "firstName": "Please enter first name", "lastName": "Please enter last name", "chineseName": "Please enter your Chinese name (optional)", "hkid": "Please enter ID card number (e.g.: A123456(7))", "passport": "Please enter passport number", "dateOfBirth": "Please select date of birth", "gender": "Please select gender", "passportCountry": "Please select country or region", "mainlandTravelPermit": "Please enter mainland travel permit number", "idDocumentType": "Please select identity document type", "idNumber": "Please enter document number", "company": "Please enter company name", "email": "Please enter email address"}, "validation": {"title": "Please select a title", "firstName": "Please enter first name", "lastName": "Please enter last name", "hkid": "Please enter Hong Kong ID card number", "passport": "Please enter passport number", "dateOfBirth": "Please select date of birth", "gender": "Please select gender", "mainlandTravelPermit": "Please enter correct mainland travel permit number", "idDocumentType": "Please select identity document type", "passportCountry": "Please select country or region", "idNumber": "Please enter document number", "email": "Please enter a valid email address!", "emailFormat": "Please enter a valid email address!", "verificationCode": "Please enter verification code!", "verificationCodeLength": "Verification code must be 6 digits!"}, "tooltips": {"hkid": "Format: 1-2 letters followed by 6 digits and a check digit in parentheses, e.g.: A123456(7)", "passport": "Please enter the passport number exactly as it appears on your passport", "mainlandTravelPermit": "Valid documents include Hong Kong and Macau Residents' Mainland Travel Permit and Mainland Residents' Hong Kong and Macau Travel Permit."}, "idDocumentTypes": {"hkid": "Hong Kong ID Card", "passport": "Passport", "mainlandTravelPermit": "Mainland Travel Permit"}, "gender": {"male": "Male", "female": "Female"}}, "buttons": {"submitNext": "Submit and Continue", "submitComplete": "Submit and Complete", "sendCode": "Send Verification Code", "resend": "Resend ({{seconds}}s)"}, "messages": {"codeSent": "Verification code sent to your email!", "emailRequired": "Please enter email address", "emailInvalid": "Please enter a valid email address", "registrationSuccess": "Registration successful!", "registrationError": "Registration failed, please try again.", "networkError": "Network error, please try again. (Using random number to simulate error, please retry)"}, "modal": {"identityVerification": {"title": "Identity Document Verification Complete!", "content": {"description": "You have completed the first step of identity verification. To fully participate in all activities, you need to complete the address verification process.", "step1": "Identity Document Verification (Completed)", "step2": "Address Verification (Next Step)", "addressVerification": "Address verification helps ensure the safety of all participants. This step requires providing a valid proof of residence document."}, "buttons": {"proceed": "Continue to Address Verification", "skip": "Complete Later"}}}}, "tools": {"hkidValidator": {"messages": {"lengthError": "Hong Kong ID card number must be 8 or 9 characters.", "invalidLetters": "The letter portion of the Hong Kong ID card number is invalid.", "invalidNumbers": "The numeric portion of the Hong Kong ID card number is invalid.", "invalidCharacters": "The letter part contains invalid characters.", "invalidNumberCharacters": "The numeric part contains invalid characters.", "valid": "The provided Hong Kong ID card number is valid.", "invalid": "The provided Hong Kong ID card number is invalid.", "formatError": "Unexpected error during formatting process."}}, "phoneNumberInput": {"placeholder": "Phone Number", "prefix": "+852"}}, "verification": {"email": {"title": "Email Verification", "description": "A verification code will be sent to your email address. Please check your inbox and enter the verification code to verify your email.", "form": {"email": {"label": "Email Address", "placeholder": "Please enter your email address"}, "code": {"label": "Verification Code", "placeholder": "Please enter 6-digit verification code"}}}, "identity": {"title": "Identity Verification", "documentConfirmation": {"mustConfirm": "Your uploaded documents must confirm the following information", "title": "Identity Document Verification", "requirements": {"title": "Image Requirements", "list": ["Government-issued identity document.", "Colored, clear, and readable document.", "Document size not exceeding 3 MB.", "JPG, JPEG, or PNG format."]}}, "address": {"title": "Address Proof Verification", "description": "This document will be used to confirm the authenticity of the address associated with your account.", "requirements": ["Your name and current address must be clearly visible on the document and match the details on your Hong Kong ID card.", "The document must be issued within the last 6 months.", "The name of the issuing organization and the issue date must be visible."]}, "form": {"labels": {"district": "District", "region": "Region", "address": "Address", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "flatNumber": "Unit", "floor": "Floor", "buildingName": "Building Name", "streetAddress": "Street Address"}, "placeholders": {"selectDistrict": "Please select your district", "region": "Hong Kong Island", "addressLine1": "Please enter your street address", "addressLine2": "Additional address information", "flatNumber": "Please enter unit number", "floor": "Please enter floor", "buildingName": "Please enter building name", "streetAddress": "Please enter street address"}, "validation": {"district": "Please select your district!", "region": "Please enter your region!", "address": "Please enter your address!"}}, "regions": {"hkIsland": "Hong Kong Island", "kowloon": "Kowloon", "newTerritories": "New Territories"}, "buttons": {"previous": "Previous", "next": "Next", "complete": "Complete"}, "messages": {"success": "Identity verification completed!", "error": "Verification failed. Please check your input."}, "districts": {"hkIsland": {"central": "Central & Western", "eastern": "Eastern", "southern": "Southern", "wanChai": "<PERSON>"}, "kowloon": {"kowloonCity": "Kowloon City", "kwunTong": "<PERSON><PERSON><PERSON>", "shamShuiPo": "<PERSON>ham <PERSON>", "wongTaiSin": "<PERSON>", "yauTsimMong": "<PERSON><PERSON>"}, "newTerritories": {"islands": "Islands", "kwaiTsing": "<PERSON><PERSON>", "north": "North", "saiKung": "<PERSON>", "shaTin": "<PERSON><PERSON>", "taiPo": "Tai Po", "tsuenWan": "<PERSON><PERSON><PERSON>", "tuenMun": "<PERSON><PERSON>", "yuenLong": "<PERSON><PERSON>"}}}, "addressProof": {"type": {"label": "Select Address Proof Type", "required": "Please select address proof type!", "options": {"bankStatement": "Bank Statement", "utilityBill": "Utility Bill", "phoneBill": "Phone Bill", "others": "Others"}}, "upload": {"label": "Upload Address Proof", "required": "Please upload your address proof!", "dragText": "Drag files here or click to upload", "hint": "Supports JPG, JPEG, PNG, or PDF format. File size limit: 3MB"}}, "hkid": {"title": "Identity Document Verification", "setup": {"title": "Before Verification", "description": "To better serve you, we need you to provide original identity documents. This will ensure the security of your account during account recovery.", "instructions": {"edit": "Please update your government-issued identity document images. We will contact you after processing your changes.", "new": "We will contact you via email after this process is completed."}}, "security": "All data is stored with secure encryption.", "upload": {"frontSide": {"label": "Front Side", "editLabel": "Update Front Side", "required": "Please upload front side of identity document!"}, "backSide": {"label": "Back Side", "editLabel": "Update Back Side", "required": "Please upload back side of identity document!"}, "dragText": "Drag files here or click to upload", "hint": "Only JPG, JPEG, or PNG format supported. File size limit: 3MB"}}}, "notFound": {"title": "404", "subTitle": "Sorry, the page you visited does not exist.", "backHome": "Back to Home"}, "forbidden": {"title": "403", "subTitle": "Sorry, you are not authorized to access this page.", "backHome": "Back to Home"}, "serverError": {"title": "500", "subTitle": "Sorry, server error occurred.", "backHome": "Back to Home"}, "generalError": {"title": "Error", "subTitle": "Sorry, an unexpected error occurred.", "backHome": "Back to Home"}, "changeContact": {"phone": {"title": "Change Phone Number", "description": "Update your phone number through two simple steps. First verify your current number, then enter your new number.", "newNumber": "New Phone Number", "placeholder": "Please enter new phone number"}, "email": {"title": "Change Email Address", "description": "Update your email address through two simple steps. First verify your current email, then enter your new email.", "newAddress": "New Email Address", "placeholder": "Please enter new email address"}, "steps": {"verify": "Verify Current Contact Information", "update": "Update Contact Information"}, "currentContact": "Current contact information:", "verificationCode": "Verification Code", "codePlaceholder": "Please enter 6-digit verification code", "buttons": {"back": "Back to Settings", "sendCode": "Send Verification Code", "resend": "Resend ({{seconds}}s)", "verify": "Verify", "submit": "Submit", "change": "Change"}, "messages": {"codeSent": "Verification code sent successfully", "sendCodeError": "Failed to send verification code", "verificationError": "Verification failed", "updateSuccess": "Contact information updated successfully", "updateError": "Failed to update contact information"}, "validation": {"required": "This field is required", "emailFormat": "Please enter a valid email address", "codeLength": "Please enter 6-digit verification code", "verificationRequired": "Please verify current email first", "phoneFormat": "Please enter a valid 8-digit phone number"}}, "eventIntroduction": {"adminActions": {"reports": "View Reports", "edit": "Edit Event", "viewParticipants": "View Participant Data", "attachments": {"title": "Attachments", "download": "Download"}, "idVerification": {"hk_id_card": "HK ID Card", "passport": "Passport", "mainland_travel_permit": "Mainland Travel Permit", "hkyouth_plus": "HKYouth+", "address_proof": "Address Proof", "student_id": "Student ID"}}, "errors": {"fetchFundingTypesError": "Failed to load government funding options."}, "shareModal": {"title": "Share Event", "content": "Are you sure you want to share this event? This action cannot be undone.", "confirm": "Yes, Share Event", "cancel": "No, Cancel Sharing"}, "participation": {"loginRequired": {"message": "<PERSON><PERSON> required to participate in this event", "button": "Login to Participate"}, "success": "Registration successful!", "error": "Registration failed, please try again later", "eventFull": "Event is full", "alreadyJoined": "You have already registered for this event", "eventNotFound": "Event not found", "notAuthorized": "You are not authorized to participate in this event", "participant": {"description": "As a participant, you will have full access to all event content and resources."}, "volunteer": {"description": "Join the volunteer team to assist with organization and create a good experience for all participants."}, "waitingList": {"description": "Join the waiting list, and we will notify you when a spot becomes available.", "full": "Waiting list is full."}, "buttons": {"participant": "Register as Participant", "volunteer": "Apply as Volunteer", "joinWaitingList": "Join Waiting List", "waitingListFull": "Waiting List Full"}}, "verificationModal": {"title": "Identity Verification Required", "content": {"main": "This event requires identity verification. Please complete the following verification process on your mobile device:"}, "buttons": {"understood": "I Understand"}}, "sections": {"basicInfo": "Basic Information", "eventInfo": "Event Information", "thingsToKnow": "Things to Know", "governmentFunding": "Government Funding", "location": "Location", "aboutEvent": {"title": "About Event", "noContent": "Event details not yet provided."}, "video": "Event Video"}, "eventOrganizer": {"hostedBy": "Hosted By"}, "eventInfo": {"type": "Event Type", "locationType": {"title": "Event Participation Type", "physical": "Physical Event", "online": "Online Event", "hybrid": "Hybrid Event (Physical and Online)", "unknown": "Unknown Location"}, "price": "Price", "participants": "Participants", "duration": "Duration", "waitlistCount": "Current Waitlist Count", "tags": "Event Tags"}, "idVerification": {"required": "This event requires identity verification.", "notRequired": "This event does not require identity verification.", "allVerified": "All verification requirements met.", "typeRequired": "{{type}} required", "requiredTemplate": "{{type}} verification required"}, "governmentFunding": {"generic_key_1": "Government Funding Example 1", "generic_key_2": "Government Funding Example 2"}, "volunteerOpportunity": {"title": "Volunteer Opportunity"}, "location": {"mapPlaceholder": "Map Placeholder", "physicalEvent": "Event takes place in-person", "onlineEvent": "Event takes place online", "hybridEvent": "Event takes place both online and in-person", "toBeAnnounced": "Location to be announced", "toBeAnnouncedDesc": "Location details will be updated soon", "openMap": "Open Map", "copyUrl": "Copy URL", "urlCopied": "URL copied to clipboard"}, "bookingCard": {"pricePerPerson": "per person", "labels": {"date": "Date", "time": "Time", "location": "Location", "verification": "Required Verification"}, "verificationNeeded": {"title": "Required Verification"}, "governmentFunding": {"title": "Government Funding"}, "noCharge": "No charge will be made at this time", "charge": "Charge will be contacted by a staff later", "buttons": {"participant": "Register as Participant", "volunteer": "Apply as Volunteer", "joinWaitingList": "Join Waiting List", "waitingListFull": "Waiting List Full", "eventExpired": "Event Has Expired"}}, "showAllPhotos": "View All Photos", "common": {"free": "Free", "share": "Share", "shareLink": "Share this event link", "shareLinkDescription": "Share this event link with your friends and family", "shareWith": "Share with", "qrCode": {"downloadApp": "Scan to download mobile app", "description": "Access events anytime, anywhere"}, "statuses": {"upcoming": "Upcoming", "onHold": "On Hold", "finished": "Finished"}, "video": {"notSupported": "Event video format not supported", "noVideoAvailable": "No event video available", "openExternally": "Unable to play video? Click here to open in a new window"}}, "hiddenEvent": "Hidden Event", "hiddenEventDescription": "This event is currently hidden and only visible to administrators."}, "username": {"title": "<PERSON>rname", "description": "Registration complete! Now you can set a username to make it easier for other users to recognize you", "placeholder": "Enter username", "validation": {"required": "Please enter a username", "length": "Username must be between 2-20 characters"}, "button": {"continue": "Complete Setup", "skip": "<PERSON><PERSON>"}, "skipHint": "You can update your username in personal settings at any time"}, "managerManagement": {"selectManagerAndOrg": "Select Manager and Organization", "selectManagerAndOrgPlaceholder": "Please select a manager and organization", "selectManagerToEdit": "Please select a manager to edit permissions", "categories": {"userManagement": "User Management", "volunteerManagement": "Volunteer Certification Management", "eventManagement": "Event Management", "contentManagement": "News/Resources Management"}, "permissions": {"approve": "Approve", "edit": "Edit", "delete": "Delete", "create": "Create", "volunteerApprove": "Volunteer Qualification Approval", "eventApprove": "Event Qualification Approval", "attendance": "Attendance", "volunteerAttendance": "Volunteer Attendance"}, "permissionCount": "Enabled Permissions", "tooltips": {"volunteerApprove": "Authorize managers to review users' basic volunteer qualification applications, including verifying identity information, approving/rejecting qualification documents. After approval, users can apply for volunteer positions at various events", "eventApprove": "Authorize managers to approve users who already have volunteer qualifications to apply for volunteer service positions for specific events", "attendance": "Authorize managers to use scan code check-in function to verify the on-site attendance of regular event participants", "volunteerAttendance": "Authorize managers to use scan code check-in function to confirm and record volunteer service check-ins"}}, "common": {"search": "Search", "reset": "Reset", "noResults": "No results", "publish": "Publish", "goBack": "Go Back", "free": "Free", "save": "Save", "loading": "Loading...", "error": "Error loading data", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "decision": "Approval Decision", "cancel": "Cancel", "clear": "Clear", "male": "Male", "female": "Female", "totalItems": "Total {{total}} items", "submit": "Submit", "unknown": "Unknown", "exportDate": "Export date:", "actions": {"delete": "Delete", "publish": "Publish", "cancel": "Cancel", "edit": "Edit", "download": "Download"}, "yes": "Yes", "no": "No", "waiting": "Waiting", "viewAllDetails": "View All Details", "organization": "Organization", "workInProgress": "{{feature}} is currently under construction.", "comeBackLater": "Please check back later for updates.", "thisPage": "This page", "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "not_required": "Not Required", "cancelled": "Cancelled", "waitlisted": "Waitlisted", "registered": "Registered", "attended": "Attended", "unknown": "Unknown", "cancelled_by_user": "User Cancelled"}, "payment": {"unpaid": "Unpaid", "paid": "Paid", "refunded": "Refunded", "not_required": "Not Required", "unknown": "Unknown"}, "roles": {"volunteer": "Volunteer", "participant": "Participant", "unknown": "Unknown"}, "noImage": "No Image", "checkAll": "Check All"}, "documents": {"hkid": "Hong Kong ID Card", "passport": "Passport", "mainlandTravelPermit": "Mainland Travel Permit", "hkyouth": "HKYouth+", "address": "Address", "studentId": "Student ID", "homeVisit": "Home Visit", "mainlandIdCard": "Mainland China ID Card", "hk_id_card": "Hong Kong ID Card", "mainland_china_id_card": "Mainland China ID Card", "mainland_travel_permit": "Mainland Travel Permit", "address_proof": "Address Proof", "student_id": "Student ID", "home_visit": "Home Visit", "hk_youth_plus": "HKYouth+", "frontSide": "Front Side", "backSide": "Back Side"}, "districts": {"nt": "New Territories", "kln": "Kowloon", "hk": "Hong Kong Island"}, "tagManager": {"title": "Tag Management", "existingTags": "Existing Tags", "createNew": "Create New Tag", "noTags": "No tags available", "english": "English", "traditionalChinese": "Traditional Chinese", "simplifiedChinese": "Simplified Chinese", "deleteConfirmTitle": "Confirm Delete Tag", "deleteConfirmContent": "Are you sure you want to delete this tag? This action cannot be undone.", "form": {"nameEn": "English Name", "nameEnRequired": "Please enter English name", "nameEnMaxLength": "English name cannot exceed 50 characters", "nameEnPlaceholder": "Please enter English tag name", "nameZhHk": "Traditional Chinese Name", "nameZhHkRequired": "Please enter Traditional Chinese name", "nameZhHkMaxLength": "Traditional Chinese name cannot exceed 50 characters", "nameZhHkPlaceholder": "Please enter Traditional Chinese tag name", "nameZhCn": "Simplified Chinese Name", "nameZhCnRequired": "Please enter Simplified Chinese name", "nameZhCnMaxLength": "Simplified Chinese name cannot exceed 50 characters", "nameZhCnPlaceholder": "Please enter Simplified Chinese tag name", "create": "Create Tag", "reset": "Reset"}, "delete": "Delete", "messages": {"tagCreatedSuccess": "Tag created successfully!", "tagDeletedSuccess": "Tag deleted successfully!"}, "errors": {"fetchTagsError": "Failed to load tags. Please try again.", "tagCreatePermission": "Only administrators can create new tags.", "tagCreateError": "Failed to create tag. Please try again.", "tagDeletePermission": "You do not have permission to delete this tag.", "tagDeleteError": "Failed to delete tag. Please try again."}}, "eventTagManager": {"title": "Event Tag Management", "existingTags": "Existing Event Tags", "createNew": "Create New Event Tag", "noTags": "No event tags available"}, "organizationSettings": {"title": "Organization Settings", "description": "Manage and update your organization settings.", "updateOrg": {"title": "Update {{orgName}}", "submit": "Save Changes"}, "createOrg": {"title": "Create New Organization", "submit": "Create Organization", "isDefault": "Set as Default Organization", "ownerLabel": "Organization Owner", "selectOwnerPlaceholder": "Please select organization owner"}, "createAdmin": {"button": "Create Ad<PERSON> Account", "title": "Create New Admin User", "displayName": "Display Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "language": "Language (Optional)", "languagePlaceholder": "e.g.: en, zh-HK"}, "common": {"name": "Organization Name", "description": "Description", "themeColor": "Theme Color", "logo": "Organization Logo", "selectLogo": "Select Logo", "currentLogo": "Current Logo", "status": "Status"}, "status": {"active": "Active", "inactive": "Inactive", "pending_setup": "Pending Setup"}, "themeColors": {"red": "Red", "orange": "Orange", "blue": "Blue", "pink": "Pink", "green": "Green"}, "changePassword": {"button": "Change Password", "title": "Change Account Password", "oldPassword": "Old Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password"}, "selectOrgPrompt": {"title": "Select an Organization", "description": "Please select an organization from the switcher to manage its settings, or create a new one if you are a super admin."}, "messages": {"orgUpdatedSuccessfully": "Organization updated successfully!", "orgCreatedSuccessfully": "Organization created successfully!", "logoUploadedSuccessfully": "Logo uploaded successfully!", "adminUserCreatedSuccessfully": "Admin account created successfully.", "adminCreateNote": "The new admin will be created with the specified details. They can change their password after logging in.", "passwordChangedSuccess": "Password changed successfully.", "passwordChangeFunctionalityPlaceholder": "Password change functionality is currently a placeholder and doesn't actually change anything.", "passwordChangeNote": "Note: This will change the password for your user account, not specific to any organization."}, "warnings": {"themeColorNotFound": "Organization's theme_color name \"{{themeName}}\" not found in THEME_COLORS. Defaulting UI selector to {{defaultTheme}}."}, "errors": {"nameRequired": "Organization name is required.", "ownerRequired": "Organization owner must be selected.", "logoRequiredForCreate": "Logo upload is required for creating an organization.", "orgUpdateFailed": "Failed to update organization", "orgCreateFailed": "Failed to create organization", "orgCreateFailedNoId": "Failed to create organization: server did not return ID.", "logoUploadFailedOrgCreated": "Organization '{{orgName}}' was created, but logo upload failed. Please update the logo manually.", "logoUploadFailed": "Logo upload failed", "imageTypeInvalid": "You can only upload JPG/PNG files!", "imageSizeInvalid": "Image must be smaller than 2MB!", "orgIdMissingForLogoUpload": "Missing organization ID for logo upload.", "updateFailedLogoProblem": "Organization update failed due to logo issue. Please try again.", "oldPasswordRequired": "Old password is required.", "newPasswordRequired": "New password is required.", "confirmPasswordRequired": "Please confirm your new password.", "passwordTooShort": "Password must be at least 8 characters.", "passwordsDoNotMatch": "The two passwords you entered do not match!", "passwordsDoNotMatchNewAdmin": "The two passwords you entered do not match!", "passwordChangeFailed": "Failed to change password", "displayNameRequired": "Display name is required.", "displayNameTooShort": "Display name must be at least 2 characters.", "displayNameTooLong": "Display name cannot exceed 50 characters.", "emailRequired": "Email is required.", "emailInvalid": "Please enter a valid email address.", "adminUserCreationFailed": "Failed to create admin user.", "fetchAdminUsersFailed": "Failed to fetch admin user list.", "noOrganizationSelected": "No organization selected for update.", "cannotUpdateDefaultOrg": "The 'Super Organization' view cannot be modified. This is a read-only representation.", "orgUpdateNotPermitted": "You do not have permission to update this organization.", "failedToFetchDefaultLogo": "Failed to fetch default logo", "processingDefaultLogo": "Error processing default logo."}}, "organizationList": {"title": "Organization Management", "description": "View and manage all organizations in the system.", "columns": {"logo": "Logo", "name": "Organization Name", "description": "Description", "status": "Status", "themeColor": "Theme Color", "createdAt": "Created Date", "actions": "Actions"}, "buttons": {"create": "Create Organization"}, "messages": {"deleteSuccess": "Organization '{{orgName}}' has been deleted successfully."}, "errors": {"fetchFailed": "Failed to fetch organizations list.", "deleteFailed": "Failed to delete organization '{{orgName}}'", "deleteUnauthorized": "You don't have permission to delete organization '{{orgName}}'", "deleteNotFound": "Organization '{{orgName}}' not found", "deleteConflict": "Cannot delete organization '{{orgName}}' as it has dependencies"}, "deleteConfirm": {"title": "Delete Organization", "description": "Are you sure you want to delete '{{orgName}}'? This action cannot be undone."}}, "unauthorized": {"title": "Unauthorized Access", "defaultMessage": "You don't have permission to view this page or perform this action."}, "messages": {"fetchError": "Failed to fetch data. Please try again later.", "validationWarning": "Please check your form inputs and try again.", "notAvailableForAllOrgs": "This page is not available when viewing All Organizations. Please select a specific organization."}, "httpErrors": {"400": "Invalid request, please check your input.", "401": "You need to log in to perform this action.", "403": "You don't have permission to perform this action.", "404": "The requested resource was not found.", "500": "Server error occurred, please try again later.", "unknown": "An unknown error occurred, please try again later."}, "eventRegistrationStatus": {"registered": "Registered", "attended": "Attended", "cancelled": "Cancelled", "waitlisted": "Waitlisted", "pending": "Pending", "approved": "Approved"}, "participantList": {"status": {"unknown": "Unknown"}, "errors": {"fetchError": "An error occurred while fetching participant data."}, "columns": {"displayName": "Name", "email": "Email", "phone": "Phone", "registrationStatus": "Registration Status", "actions": "Actions"}, "actions": {"viewVerification": "View Verification Data"}, "filters": {"statusPlaceholder": "Filter by status", "status": {"registered": "Registered", "attended": "Attended", "cancelled": "Cancelled", "waitlisted": "Waitlisted"}}, "noParticipants": "No participants found for the selected criteria."}, "eventParticipantsPage": {"errors": {"eventIdMissing": "Event ID missing.", "failedToLoadEventDetails": "Failed to load event details."}, "loadingEventDetails": "Loading event details...", "eventDetailsNotFound": "Event details not found.", "title": "Event Participants", "eventTitleFallback": "Event ID: {eventId}"}, "participantVerification": {"errors": {"missingParams": "Event ID or user ID missing.", "noRegistrationDetails": "No registration details found for this participant in this event.", "fetchError": "An error occurred while fetching data.", "eventNotFound": "Event details not found.", "orgIdMissing": "Organization ID missing in event details.", "userNotFound": "User details not found.", "noDataAvailable": "User or event details not available.", "noRegistrationDetailsCard": "Registration details for this participant in this event are not available."}, "title": "Verification for {eventName}", "userDetails": {"title": "User Details", "displayName": "Display Name", "email": "Email", "phone": "Phone", "isStaff": "Is Staff"}, "registrationDetails": {"title": "Registration Details", "status": "Status", "role": "Role", "registeredAt": "Registered At", "checkedInAt": "Checked In At", "adminNotes": "Admin Notes", "userNotes": "User Notes"}, "verificationDocs": {"title": "Required Verification", "statusUnavailable": "User verification status not available."}}, "adminPosts": {"title": "Posts Management", "list": {"title": "Posts List", "columns": {"title": "Title", "organization": "Organization", "author": "Author", "status": "Status", "publishedAt": "Published At", "createdAt": "Created At", "tags": "Tags", "actions": "Actions"}, "filters": {"searchTitle": "Search title..."}, "actions": {"edit": "Edit", "delete": "Delete", "view": "View"}, "empty": "No posts found."}, "buttons": {"create": "Create Post"}, "filter": {"searchPlaceholder": "Search posts...", "tagsPlaceholder": "Filter by tags...", "statusPlaceholder": "Filter by status..."}, "status": {"published": "Published", "draft": "Draft", "hidden": "Hidden", "archived": "Archived"}, "messages": {"deleteConfirmTitle": "Confirm Deletion", "deleteConfirmText": "Are you sure you want to delete this post? This action cannot be undone.", "deleteSuccess": "Post deleted successfully.", "deleteError": "Failed to delete post."}}, "postFilter": {"placeholders": {"search": "Search by title or content...", "dateRange": "Select date range", "tags": "Select tags", "status": "Select status (Admin)"}}, "adminResources": {"title": "Resources Management", "list": {"title": "Resources List", "columns": {"title": "Title", "organization": "Organization", "author": "Author", "status": "Status", "publishedAt": "Published At", "createdAt": "Created At", "actions": "Actions", "fileCount": "File Count", "visibility": "Visibility", "updatedAt": "Updated At"}, "actions": {"edit": "Edit", "delete": "Delete", "view": "View", "download": "Download"}, "empty": "No resources found", "filters": {"searchTitle": "Search title..."}}, "buttons": {"create": "Upload New Resource", "upload": "Upload"}, "filter": {"searchPlaceholder": "Search resources", "statusPlaceholder": "Filter by status", "visibilityPlaceholder": "Filter by visibility"}, "status": {"published": "Published", "draft": "Draft", "hidden": "Hidden", "scheduled": "Scheduled"}, "visibility": {"public": "Public", "org_only": "Org Only", "private": "Private"}, "messages": {"deleteConfirmTitle": "Confirm Deletion", "deleteConfirmText": "Are you sure you want to delete this resource? This action cannot be undone.", "deleteSuccess": "Resource deleted successfully.", "deleteError": "Failed to delete resource.", "uploadSuccess": "Resource uploaded successfully.", "uploadError": "Failed to upload resource."}, "batchOperation": {"title": "Batch Operations", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "selectedCount": "{{count}} items selected"}}, "organizationEdit": {"createTitle": "Create New Organization", "editTitle": "Edit {{orgName}}", "createDescription": "Fill in the details to create a new organization.", "editDescription": "Update the organization details below.", "fields": {"name": "Organization Name", "description": "Description", "status": "Status", "owner": "Organization Owner", "logo": "Organization Logo", "themeColor": "Theme Color"}, "placeholders": {"selectOwner": "Select organization owner"}, "buttons": {"create": "Create Organization", "save": "Save Changes", "selectLogo": "Select Logo"}, "labels": {"currentLogo": "Current Logo"}, "messages": {"orgCreatedSuccessfully": "Organization created successfully!", "orgUpdatedSuccessfully": "Organization updated successfully!", "logoUploadedSuccessfully": "Logo uploaded successfully!"}, "errors": {"fetchFailed": "Failed to fetch organization details.", "saveFailed": "Failed to save organization", "nameRequired": "Organization name is required.", "ownerRequired": "Organization owner must be selected.", "logoRequiredForCreate": "Logo upload is required for creating an organization.", "logoUploadFailed": "Logo upload failed", "imageTypeInvalid": "You can only upload JPG/PNG files!", "imageSizeInvalid": "Image must be smaller than 2MB!", "orgIdMissingForLogoUpload": "Missing organization ID for logo upload.", "orgCreateFailedNoId": "Failed to create organization: server did not return ID.", "noPermission": "You do not have permission to manage this organization."}}, "userManagement": {"title": "User Management", "noVerifications": "No Verifications", "noVerificationRecords": "No verification records found", "noDocuments": "No Documents", "columns": {"displayName": "Display Name", "email": "Email", "phone": "Phone", "userRole": "User Role", "createdAt": "Created At", "verificationCount": "Verification Count", "userDisplayName": "User Name", "userEmail": "Email", "userPhone": "Phone", "registrationRole": "Role", "lastActivity": "Last Activity", "verificationStatus": "Verification Status", "documentCount": "Document Count", "actions": "Actions"}, "filters": {"admin": "Admin", "user": "User"}, "userRoles": {"admin": "Admin", "user": "User", "superadmin": "Super Admin"}, "verificationDetails": {"type": "Verification Type", "status": "Status", "submittedAt": "Submitted At", "reviewedAt": "Reviewed At"}, "export": {"title": "Export", "success": "Data exported successfully", "error": "Failed to export data"}}}