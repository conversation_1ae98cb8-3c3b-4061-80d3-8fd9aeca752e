// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: event_conflict_check.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const getEventTimeByID = `-- name: GetEventTimeByID :one
SELECT start_time, end_time
FROM events
WHERE id = $1
`

type GetEventTimeByIDRow struct {
	StartTime time.Time `db:"start_time" json:"start_time"`
	EndTime   time.Time `db:"end_time" json:"end_time"`
}

func (q *Queries) GetEventTimeByID(ctx context.Context, id uuid.UUID) (GetEventTimeByIDRow, error) {
	row := q.db.QueryRow(ctx, getEventTimeByID, id)
	var i GetEventTimeByIDRow
	err := row.Scan(&i.StartTime, &i.EndTime)
	return i, err
}

const getUserRegisteredEventTimes = `-- name: GetUserRegisteredEventTimes :many
SELECT e.start_time, e.end_time
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
  AND er.status NOT IN ('cancelled_by_user', 'attended', 'absent', 'rejected_approval')
`

type GetUserRegisteredEventTimesRow struct {
	StartTime time.Time `db:"start_time" json:"start_time"`
	EndTime   time.Time `db:"end_time" json:"end_time"`
}

func (q *Queries) GetUserRegisteredEventTimes(ctx context.Context, userID uuid.UUID) ([]GetUserRegisteredEventTimesRow, error) {
	rows, err := q.db.Query(ctx, getUserRegisteredEventTimes, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetUserRegisteredEventTimesRow{}
	for rows.Next() {
		var i GetUserRegisteredEventTimesRow
		if err := rows.Scan(&i.StartTime, &i.EndTime); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
