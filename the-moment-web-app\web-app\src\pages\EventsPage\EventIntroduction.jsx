import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Button, Avatar, Divider, Modal, Image, Skeleton, Typography, message, Tag, Space, Spin, Tooltip, Input, Popover, QRCode } from 'antd';
import { CalendarOutlined, ClockCircleOutlined, EnvironmentOutlined, ShareAltOutlined, TeamOutlined, DollarOutlined, InfoCircleOutlined, IdcardOutlined, UserAddOutlined, CameraOutlined, ExclamationCircleOutlined, UserOutlined, BarChartOutlined, EditOutlined, GlobalOutlined, SafetyOutlined, EyeInvisibleOutlined, SolutionOutlined, CopyOutlined, CheckOutlined, LinkOutlined, WhatsAppOutlined, FacebookOutlined, TwitterOutlined, TagOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { eventService } from '../../services/eventService';
import fallbackImage from '../../assets/images/picture-loading-failed.svg';
import ErrorPage from '../ErrorPage';
import { formatDateOnly, formatTimeRange, calculateDuration, formatDateTimeRangeWithEndTime, formatCompactDateTimeRange } from '../../utils/dateFormatter';
import EventButtonGroup from './components/EventButtonGroup';
import { generateSafeHTML } from '../../utils/htmlUtils';
import '../../styles/EventDetails.css';
import { Icon } from '@iconify/react';
import { ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import hkyouthImg from '../../assets/images/hkyouth.jpg';
// Import government funding logos
// import govFundedProgGreen from '../../assets/images/gov_logo/gov_funded_prog_green.png';
import govFundedProg from '../../assets/images/gov_logo/gov_funded_prog_red.png';
import hksarHyab from '../../assets/images/gov_logo/hksar_hyab.png';
import youthDevCommission from '../../assets/images/gov_logo/youth_dev_commission.png';
const { Paragraph, Text } = Typography;

// Government funding logo mapping
const govFundingLogoMap = {
  // gov_funded_prog_green: govFundedProgGreen,
  gov_funded_prog: govFundedProg,
  hksar_hyab: hksarHyab,
  youth_dev_commission: youthDevCommission,
};

// Mapping from erification_type_keys (string from API) to UI elements
const identityIconMap = {
  hk_id_card: <Icon icon="qlementine-icons:id-card-16" className="text-2xl" />, // Use mainland_travel_permit icon
  mainland_china_id_card: <Icon icon="qlementine-icons:id-card-16" className="text-2xl" />, // Use mainland_travel_permit icon
  mainland_travel_permit: <Icon icon="qlementine-icons:id-card-16" className="text-2xl" />,
  hk_youth_plus: <img src={hkyouthImg} alt="hkyouth" className="w-6 h-6" />,
  passport: <Icon icon="bi:passport" className="text-2xl" />,
  address_proof: <Icon icon="hugeicons:address-book" className="text-2xl" />,
  student_id: <Icon icon="ph:student" className="text-2xl" />
  // Add other keys as defined in your backend verification types
};

// Helper function to check if event has expired
const isEventExpired = (endTime) => {
  if (!endTime) return false;
  const now = new Date();
  const eventEndTime = new Date(endTime);
  return now > eventEndTime;
};

// Helper function to check if any identity verification is required
const hasIdentityRequirements = (requiredKeysArray) => {
  if (!requiredKeysArray) return false;
  return requiredKeysArray.length > 0;
};

// Helper function to check if user meets all required verifications
// user.verification_status is expected to be like UserVerificationStatusResponse
const checkVerificationRequirements = (user, requiredKeysArray) => {
  if (!user || !requiredKeysArray || !user.verification_status) return false;

  for (const docKey of requiredKeysArray) {
    if (!user.verification_status[docKey]) { // If the key for a required doc is false in user's status
      return false;
    }
  }
  return true;
};

// Helper function to get missing verifications
const getMissingVerifications = (user, requiredKeysArray) => {
  if (!user || !requiredKeysArray || !user.verification_status) return [];
  const missing = [];
  for (const docKey of requiredKeysArray) {
    if (!user.verification_status[docKey]) {
      missing.push(docKey);
    }
  }
  return missing;
};

// Helper function to detect if a file is a video based on its extension
const isVideoFile = (filePath) => {
  if (!filePath) return false;
  const videoExtensions = ['.mp4', '.webm', '.mov', '.avi', '.mkv', '.flv', '.wmv', '.m4v', '.3gp'];
  const lowerCasePath = filePath.toLowerCase();
  return videoExtensions.some(ext => lowerCasePath.endsWith(ext));
};

// Helper function to detect if a file is an image based on its extension
const isImageFile = (filePath) => {
  if (!filePath) return false;
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
  const lowerCasePath = filePath.toLowerCase();
  return imageExtensions.some(ext => lowerCasePath.endsWith(ext));
};

// Helper function to get tag name based on current language
const getTagName = (tag, language) => {
  if (!tag) return '';
  
  if (language.startsWith('zh')) {
    if (language.includes('HK')) {
      return tag.name_zh_hk || tag.name_en || '';
    } else {
      return tag.name_zh_cn || tag.name_en || '';
    }
  } else {
    return tag.name_en || '';
  }
};

const EventIntroduction = () => {
  const [isGalleryModalOpen, setIsGalleryModalOpen] = useState(false);
  const [displayedImagesCount, setDisplayedImagesCount] = useState(5);
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [imageLoadingStates, setImageLoadingStates] = useState({});
  const [joinLoading, setJoinLoading] = useState(false);
  const [verificationTypeDetails, setVerificationTypeDetails] = useState([]);
  const [governmentFundingTypes, setGovernmentFundingTypes] = useState([]);
  const [loadingFundingTypes, setLoadingFundingTypes] = useState(true);
  const [copied, setCopied] = useState(false);
  const [shareOpen, setShareOpen] = useState(false);
  const eventLinkRef = useRef(null);
  const videoRef = useRef(null);

  const navigate = useNavigate();
  const { eventId } = useParams();
  const { user, isLoggedIn } = useAuth();
  const { t, i18n } = useTranslation();
  const location = useLocation();

  // Get the current event URL for sharing
  const eventUrl = useMemo(() => {
    if (typeof window !== 'undefined') {
      return window.location.origin + location.pathname;
    }
    return '';
  }, [location.pathname]);

  // Handle copy to clipboard functionality
  const handleCopyLink = () => {
    if (navigator.clipboard && eventUrl) {
      navigator.clipboard.writeText(eventUrl);
      setCopied(true);

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    }
  };

  // Share handlers for different platforms
  const handleShareWhatsApp = () => {
    window.open(`https://api.whatsapp.com/send?text=${encodeURIComponent(event?.title + ' - ' + eventUrl)}`, '_blank');
    setShareOpen(false);
  };

  const handleShareFacebook = () => {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(eventUrl)}`, '_blank');
    setShareOpen(false);
  };

  const handleShareTwitter = () => {
    window.open(`https://x.com/intent/tweet?url=${encodeURIComponent(eventUrl)}&text=${encodeURIComponent(event?.title)}`, '_blank');
    setShareOpen(false);
  };

  useEffect(() => {
    const fetchEventDetails = async () => {
      setLoading(true);
      setError(null);
      setImageLoadingStates({});
      try {
        const data = await eventService.getPublicEventDetail(eventId);
        // Check if event is hidden (using status) and user is not admin/super_admin
        if ((data.status === 'hidden' || data.status === 'cancelled') &&
          (!user || (user.role !== 'admin' && user.role !== 'super_admin'))) {
          setError('403'); // Or a specific error for hidden events
          setLoading(false);
          return;
        }

        // Process media_items to extract images and videos based on file extensions
        if (data.media_items && Array.isArray(data.media_items)) {
          // Process images - only based on file extensions, ignore incorrect file_type
          const imageUrls = data.media_items
            .filter(item => item.file_path && isImageFile(item.file_path))
            .map(item => item.file_path);
          
          // Ensure images array exists and merge existing images
          data.images = [...(data.images || [])];
          
          // Add non-duplicate image URLs to images array
          imageUrls.forEach(url => {
            if (!data.images.includes(url)) {
              data.images.push(url);
            }
          });
        }

        setEvent(data);

        // Pre-load images and set their initial loading states from images
        if (data?.images && data.images.length > 0) {
          const initialLoadingStates = {};
          data.images.forEach((_, index) => {
            initialLoadingStates[index] = true;
          });
          setImageLoadingStates(initialLoadingStates);
        }
      } catch (error) {
        if (error.response) {
          switch (error.response.status) {
            case 403:
              setError('403');
              break;
            case 404:
              setError('404');
              break;
            case 500:
              setError('500');
              break;
            default:
              setError('general');
          }
        } else {
          setError('general');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchEventDetails();
  }, [eventId, user]);

  useEffect(() => {
    const fetchVerificationTypes = async () => {
      try {
        const lang_code = i18n.language.replace('-', '_');
        const response = await eventService.listVerificationTypes({ lang_code });
        const types = Array.isArray(response) ? response : (response.data || response.items || []);
        setVerificationTypeDetails(types.filter(type => type.key && type.name));
      } catch (error) {
        console.error("Failed to fetch verification types for display:", error);
        setVerificationTypeDetails([]);
      }
    };
    fetchVerificationTypes();
  }, [i18n.language]);

  useEffect(() => {
    const fetchGovFundingTypes = async () => {
      setLoadingFundingTypes(true);
      try {
        const lang_code = i18n.language.replace('-', '_');
        const response = await eventService.listGovernmentFundingTypes({ lang_code });
        // Ensure response structure is handled correctly, matching what EventEdit.js expects
        const types = Array.isArray(response) ? response : (response.data || response.items || []);
        // Assuming types have 'key' and 'name' properties as per typical API response structure
        setGovernmentFundingTypes(types.filter(type => type.key && type.name));
      } catch (error) {
        console.error("Failed to fetch government funding types:", error);
        message.error(t('eventIntroduction.errors.fetchFundingTypesError', "Failed to load funding types."));
        setGovernmentFundingTypes([]);
      } finally {
        setLoadingFundingTypes(false);
      }
    };
    fetchGovFundingTypes();
  }, [i18n.language, t]);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setDisplayedImagesCount(3);
      } else if (window.innerWidth < 768) {
        setDisplayedImagesCount(4);
      } else if (window.innerWidth < 1024) {
        setDisplayedImagesCount(5);
      }
    };

    handleResize(); // Set initial count based on current width
    window.addEventListener('resize', handleResize);

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const handleFullscreenChange = () => {
      const video = videoRef.current;
      if (!video) return;
      // Check if the video element is in fullscreen
      const isFullscreen = document.fullscreenElement === video;
      if (isFullscreen) {
        video.play();
      } else {
        video.pause();
      }
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const handleJoinEvent = async () => {
    if (!isLoggedIn) {
      message.info(t('eventIntroduction.participation.loginRequired.message'));
      return;
    }

    if (hasIdentityRequirements(event?.verification_type_keys)) {
      const meetsRequirements = checkVerificationRequirements(user, event.verification_type_keys);
      if (!meetsRequirements) {
        setShowVerificationModal(true);
        return;
      }
    }

    setJoinLoading(true);
    try {
      // 假设这里调用加入活动的API
      await eventService.registerForEvent(eventId);
      message.success(t('eventIntroduction.participation.success'));
    } catch (error) {
      if (error.response?.status === 409 || error.error === "Conflict") {
        // Check if it's specifically a time conflict error
        if (error.response?.data?.error_code === 'TIME_CONFLICT' || error.error === "Conflict") {
          message.error(t('eventIntroduction.participation.timeConflict', '您已經報名了同一時間段的其他活動，無法參加本活動'));
        } else {
          message.error(t('eventIntroduction.participation.alreadyJoined'));
        }
      } else {
        message.error(t('common.error'));
      }
    } finally {
      setJoinLoading(false);
    }
  };

  const handleVolunteerJoin = async () => {
    if (!isLoggedIn) {
      message.info(t('eventIntroduction.participation.loginRequired.message'));
      return;
    }

    if (hasIdentityRequirements(event?.verification_type_keys)) {
      const meetsRequirements = checkVerificationRequirements(user, event.verification_type_keys);
      if (!meetsRequirements) {
        setShowVerificationModal(true);
        return;
      }
    }

    setJoinLoading(true);
    try {
      await eventService.joinEventAsVolunteer(eventId);
      message.success(t('eventIntroduction.participation.success'));

    } catch (error) {
      if (error.response?.status === 409 || error.error === "Conflict") {
        // Check if it's specifically a time conflict error
        if (error.response?.data?.error_code === 'TIME_CONFLICT' || error.error === "Conflict") {
          message.error(t('eventIntroduction.participation.timeConflict', '您已經報名了同一時間段的其他活動，無法參加本活動'));
        } else {
          message.error(t('eventIntroduction.participation.alreadyJoined'));
        }
      } else {
        message.error(t('common.error'));
      }
    } finally {
      setJoinLoading(false);
    }
  };

  const handleJoinWaitingList = async () => {
    if (!isLoggedIn) {
      message.info(t('eventIntroduction.participation.loginRequired.message'));
      return;
    }

    if (hasIdentityRequirements(event?.verification_type_keys)) {
      const meetsRequirements = checkVerificationRequirements(user, event.verification_type_keys);
      if (!meetsRequirements) {
        setShowVerificationModal(true);
        return;
      }
    }

    setJoinLoading(true);
    try {
      await eventService.registerForEvent(eventId);
      message.success(t('eventIntroduction.participation.success'));

    } catch (error) {
      if (error.response?.status === 409 || error.error === "Conflict") {
        // Check if it's specifically a time conflict error
        if (error.response?.data?.error_code === 'TIME_CONFLICT' || error.error === "Conflict") {
          message.error(t('eventIntroduction.participation.timeConflict', '您已經報名了同一時間段的其他活動，無法參加本活動'));
        } else {
          message.error(t('eventIntroduction.participation.alreadyJoined'));
        }
      } else {
        message.error(t('common.error'));
      }
    } finally {
      setJoinLoading(false);
    }
  };

  const renderParticipationSection = () => {
    // Check if event has expired
    if (event?.end_time && isEventExpired(event.end_time)) {
      return (
        <>
          <Button
            type="primary"
            size="large"
            block
            disabled
            className="join-button"
          >
            {t('eventIntroduction.bookingCard.buttons.eventExpired')}
          </Button>
        </>
      );
    }

    if (!isLoggedIn) {
      return (
        <>
          <Paragraph type="danger">
            <ExclamationCircleOutlined style={{ marginRight: '8px' }} />
            {t('eventIntroduction.participation.loginRequired.message')}
          </Paragraph>
          <Button
            type="primary"
            size="large"
            block
            onClick={() => navigate(`/login?returnTo=${encodeURIComponent(location.pathname)}`)}
            className="join-button"
          >
            {t('eventIntroduction.participation.loginRequired.button')}
          </Button>
        </>
      );
    }

    const isApprovedVolunteer = user?.applications?.volunteer?.status === 'approved';
    const isFull = event?.registered_count >= event?.participant_limit;
    const isWaitingListFull = isFull && (!event?.waitlist_limit || (event?.registered_count - event?.participant_limit >= event?.waitlist_limit));

    return (
      <EventButtonGroup
        isApprovedVolunteer={isApprovedVolunteer}
        isFull={isFull}
        isWaitingListFull={isWaitingListFull}
        isLoading={joinLoading}
        onJoinEvent={handleJoinEvent}
        onJoinWaitingList={handleJoinWaitingList}
        onVolunteerJoin={handleVolunteerJoin}
        userRole={user?.role}
      />
    );
  };

  const formatPrice = (priceString) => {
    // Backend sends price as string format number
    if (priceString === null || priceString === undefined) return t('common.tbd');
    
    if (priceString === '0' || priceString === '') {
      return t('common.free');
    }
    
    const numericPrice = parseFloat(priceString);
    if (!isNaN(numericPrice)) {
      if (numericPrice === 0) return t('common.free');
      return `${t('eventCalendar.eventList.price.prefix')} ${numericPrice.toFixed(2)}`;
    }
    
    return priceString; // Fallback
  };

  // Move duration calculation here and wrap it in useMemo
  const duration = useMemo(() => {
    if (!event?.start_time || !event?.end_time) return '';
    return calculateDuration(event.start_time, event.end_time, i18n.language);
  }, [event?.start_time, event?.end_time, i18n.language]);

  // Memoize the generated HTML to avoid unnecessary regeneration
  const safeHtml = useMemo(() => {
    if (!event?.jsonContent || !event.jsonContent.content || event.jsonContent.content.length === 0) return t('eventIntroduction.sections.aboutEvent.noContent'); // Use jsonContent
    return generateSafeHTML(event.jsonContent);
  }, [event?.jsonContent, t]);

  if (error) {
    return <ErrorPage type={error} />;
  }

  if (!event && !loading) {
    return null;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 lg:px-8 py-8">
      {loading ? (
        <div className="flex justify-center items-center py-16">
          <Spin size="large" />
        </div>
      ) : (
        <>
          {/* Display based on event.status */}
          {(event?.status === 'hidden' || event?.status === 'cancelled') && (user?.role === 'admin' || user?.role === 'super_admin') && (
            <div className="bg-gray-100 border-l-4 border-gray-400 p-3 rounded flex items-center mb-4 gap-4">
              <EyeInvisibleOutlined className="text-gray-500 text-lg" />
              <div className="flex flex-col">
                <span className="text-gray-700 font-medium">
                  {event.status === 'hidden' ? t('eventIntroduction.hiddenEvent') : t('eventIntroduction.cancelledEvent')}
                </span>
                <span className="text-gray-500 text-sm">
                  {event.status === 'hidden' ? t('eventIntroduction.hiddenEventDescription') : t('eventIntroduction.cancelledEventDescription')}
                </span>
              </div>
            </div>
          )}

          <h1 className="text-3xl font-bold text-gray-900 mb-2">{event?.title}</h1>
          <div className="flex flex-wrap items-center justify-between mb-6">
            <div className="flex items-center flex-wrap gap-4 text-gray-600">
              <div className="flex items-center">
                <CalendarOutlined className="text-gray-500 mr-2" />
                <span>{formatDateTimeRangeWithEndTime(event?.start_time, event?.end_time, i18n.language)}</span>
              </div>
              <div className="flex items-center">
                <EnvironmentOutlined className="text-gray-500 mr-2" />
                {/* Display location based on type */}
                <span>
                  {event?.location_type === 'physical' && event?.location_full_address}
                  {event?.location_type === 'online' && event?.location_online_url}
                  {event?.location_type === 'hybrid' && `${event?.location_full_address || ''} (${event?.location_online_url || ''})`}
                  {(!event?.location_type ||
                    (event?.location_type === 'physical' && !event?.location_full_address) ||
                    (event?.location_type === 'online' && !event?.location_online_url) ||
                    (event?.location_type === 'hybrid' && !event?.location_full_address && !event?.location_online_url)
                  ) && t('home.tbdLocation')}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {(user?.role === 'admin' || user?.role === 'super_admin' || user?.role === 'staff') && 
                // Hide admin actions for non-super_admin when event belongs to ALL_ORGANIZATION_ID
                !(event?.organization_id === ALL_ORGANIZATION_ID && user?.role !== 'super_admin') && (
                <>
                  <Button
                    onClick={() => navigate(`/events/${event.id}/event-reports`)}
                    icon={<BarChartOutlined />}
                    type="text"
                    className="font-medium text-gray-600 hover:text-gray-900"
                  >
                    {t('eventIntroduction.adminActions.reports')}
                  </Button>
                  <Button
                    icon={<EditOutlined />}
                    type='text'
                    onClick={() => navigate(`/events/${event.id}/edit?returnTo=${encodeURIComponent(location.pathname)}`)}
                    className="font-medium text-gray-600 hover:text-gray-900"
                  >
                    {t('eventIntroduction.adminActions.edit')}
                  </Button>

                  <Button
                    icon={<SolutionOutlined />}
                    type='text'
                    onClick={() => navigate(`/events/${eventId}/event-reports?tab=historical`)}
                    className="font-medium text-gray-600 hover:text-gray-900"
                  >
                    {t('eventIntroduction.adminActions.viewParticipants')}
                  </Button>
                </>
              )}

              {/* Share button with Popover */}
              <Popover
                content={
                  <div className="w-72">
                    {/* Share Link */}
                    <div className="p-2">
                      <label htmlFor="event-url" className="text-base font-medium text-gray-700 mb-3 block">
                        {t('eventIntroduction.common.shareLink', 'Share this event link')}:
                      </label>
                      <div className="relative mb-5">
                        <input
                          id="event-url"
                          type="text"
                          ref={eventLinkRef}
                          className="bg-gray-50 border border-gray-300 text-gray-700 text-base rounded-lg block w-full pr-10 p-3"
                          value={eventUrl}
                          readOnly
                        />
                        <button
                          onClick={handleCopyLink}
                          className="absolute end-2 top-1/2 -translate-y-1/2 text-gray-600 hover:bg-gray-100 rounded-lg p-2 inline-flex items-center justify-center"
                        >
                          {copied ? (
                            <CheckOutlined className="text-green-500 text-lg" />
                          ) : (
                            <CopyOutlined className="text-lg" />
                          )}
                        </button>
                      </div>

                      {/* QR Code for Mobile App */}
                      <div className="mb-5">
                        <div className="flex flex-col items-center p-4 bg-gray-50 rounded-lg border">
                          <QRCode
                            value="https://apps.apple.com/app/your-app-id" // Replace with actual app store URL
                            size={100}
                            type="canvas"
                            bordered={false}
                            bgColor="#f9fafb"
                            style={{ backgroundColor: 'transparent' }}
                          />
                          <div className="mt-3 text-center">
                            <p className="text-sm font-medium text-gray-700 mb-1">{t('eventIntroduction.common.qrCode.downloadApp')}</p>
                            <p className="text-xs text-gray-500">{t('eventIntroduction.common.qrCode.description')}</p>
                          </div>
                        </div>
                      </div>

                      {/* Social Media Links */}
                      <div>
                        <h5 className="text-base font-medium text-gray-700 mb-3">
                          {t('eventIntroduction.common.shareWith', 'Share with')}:
                        </h5>
                        <div className="space-y-2">
                          {/* WhatsApp */}
                          <Button
                            type="text"
                            onClick={handleShareWhatsApp}
                            className="w-full flex items-center justify-start text-base px-2 h-10"
                            icon={<WhatsAppOutlined className="text-green-500 text-lg" />}
                          >
                            <span className="ml-2 text-gray-700">WhatsApp</span>
                          </Button>

                          {/* Facebook */}
                          <Button
                            type="text"
                            onClick={handleShareFacebook}
                            className="w-full flex items-center justify-start text-base px-2 h-10"
                            icon={<FacebookOutlined className="text-blue-600 text-lg" />}
                          >
                            <span className="ml-2 text-gray-700">Facebook</span>
                          </Button>

                          {/* Twitter/X */}
                          <Button
                            type="text"
                            onClick={handleShareTwitter}
                            className="w-full flex items-center justify-start text-base px-2 h-10"
                            icon={<TwitterOutlined className="text-lg" />}
                          >
                            <span className="ml-2">X (Formerly Twitter)</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                }
                trigger="hover"
                arrow={false}
                placement="bottomRight"
                open={shareOpen}
                onOpenChange={setShareOpen}
              >
                <Button
                  type="text"
                  icon={<ShareAltOutlined />}
                  className="font-medium text-gray-600 hover:text-gray-900"
                >
                  {t('eventIntroduction.common.share')}
                </Button>
              </Popover>
            </div>
          </div>

          {/* Image Gallery */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-8 rounded-lg overflow-hidden h-auto relative">
            {/* Main Image */}
            <div className="col-span-1 md:col-span-2 md:row-span-2 relative rounded-lg overflow-hidden h-[400px]">
              {event?.images && event.images.some(img => isImageFile(img)) && (
                <>
                  <img
                    src={event.images.find(img => isImageFile(img))}
                    alt="Event main"
                    className="w-full h-full object-cover rounded-lg"
                  />
                </>
              )}
            </div>

            {/* Thumbnails */}
                          {(event?.images || [])
                .filter(img => isImageFile(img)) // Only display actual image files
                .slice(1, Math.min(displayedImagesCount, 5))
              .map((img, index) => {
                const actualIndex = index + 1;
                return (
                  <div key={index} className="relative rounded-lg overflow-hidden">
                    <img
                      src={img}
                      alt={`Event thumbnail ${actualIndex}`}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  </div>
                );
              })}

            {event?.images && (
              <button
                onClick={() => setIsGalleryModalOpen(true)}
                className="absolute bottom-4 right-4 bg-white text-gray-800 px-4 py-2 rounded-lg shadow-md flex items-center z-10 hover:bg-gray-100 transition duration-200"
              >
                <CameraOutlined className="mr-2" />
                {t('eventIntroduction.showAllPhotos')}
              </button>
            )}
          </div>

          {/* Image Gallery Modal */}
          <Modal
            title={t('eventIntroduction.showAllPhotos')}
            open={isGalleryModalOpen}
            onCancel={() => setIsGalleryModalOpen(false)}
            footer={null}
            width={1000}
            styles={{
              body: { padding: '24px' }
            }}
          >
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <Image.PreviewGroup>
                {(event?.images || [])
                  .filter(img => isImageFile(img)) // Only display actual image files
                  .map((img, index) => (
                    <Image
                      key={index}
                      src={img}
                      loading='lazy'
                      alt={`Event gallery ${index + 1}`}
                      className="w-full h-auto rounded-lg"
                      style={{ objectFit: 'cover' }}
                      placeholder={
                        <div className="w-full h-32 bg-gray-200 animate-pulse rounded-lg"></div>
                      }
                      fallback={fallbackImage}
                    />
                  ))}
              </Image.PreviewGroup>
            </div>
          </Modal>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column: Event Details */}
            <div className="lg:col-span-2">
              <div className="flex justify-between items-start mb-6">
                <div className="flex items-center gap-4">
                  <Avatar
                    src={event?.organization_logo_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(event?.organization_name || 'O')}&background=random`}
                    alt={event?.organization_name}
                    size={48}
                    icon={<GlobalOutlined />} // Fallback icon
                  />
                  <div className="flex flex-col">
                    <Text className="text-lg font-semibold">
                      {t('eventIntroduction.eventOrganizer.hostedBy')}
                    </Text>
                    <Text strong>
                      {event?.organization_name || t('common.unknown')}
                    </Text>
                  </div>
                </div>
              </div>

              {/* Government Funding Section - Main Section */}
              {!loadingFundingTypes && event?.government_funding_keys && event.government_funding_keys.length > 0 && (
                <>
                  <Divider className="mb-8 section-divider" orientation="left"><span className="text-xl text-gray-800">{t('eventIntroduction.sections.governmentFunding')}</span></Divider>
                  <div className="mb-8">
                    <div className="space-y-6">
                      {(event.government_funding_keys || []).map((key) => {
                        const fundingDetail = governmentFundingTypes.find(type => type.key === key);
                        const fundingName = fundingDetail ? fundingDetail.name : key.replace(/_/g, ' ');
                        // Get logo from mapping, or use default if not found
                        const logoSrc = govFundingLogoMap[key];

                        return (
                          <div key={key} className="flex items-center gap-6">
                            <img
                              src={logoSrc}
                              alt={fundingName}
                              className="h-auto"
                              style={{ width: '4cm', maxWidth: '100%' }}
                            />
                            <p className="font-semibold text-lg text-gray-800">
                              {fundingName}
                            </p>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </>
              )}

              <Divider className="mb-8 section-divider" orientation="left">
                <span className="text-xl text-gray-800">{t('eventIntroduction.sections.basicInfo')}</span>
              </Divider>

              {/* Event Info */}
              <div className="mb-8">
                <p className="text-base font-medium text-gray-600 mb-2">{t('eventIntroduction.sections.eventInfo')}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-start gap-4">
                    <div className="rounded-full p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                      <InfoCircleOutlined className="text-xl text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-base">{t('eventIntroduction.eventInfo.locationType.title')}</p>
                      <p className="text-gray-700 mt-1 text-base">
                        {event?.location_type
                          ? t(`eventIntroduction.eventInfo.locationType.${event.location_type}`, event.location_type)
                          : t('eventIntroduction.eventInfo.locationType.unknown')
                        }
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="rounded-full p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                      <DollarOutlined className="text-xl text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-base">{t('eventIntroduction.eventInfo.price')}</p>
                      <p className="text-gray-700 mt-1 text-base">{formatPrice(event?.price)}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="rounded-full p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                      <TeamOutlined className="text-xl text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-base">{t('eventIntroduction.eventInfo.participants')}</p>
                      <p className="text-gray-700 mt-1 text-base">
                        {event?.registered_count >= event?.participant_limit
                          ? (
                            <span className="flex items-center gap-2">
                              {`${event?.participant_limit} / ${event?.participant_limit}`}
                              <Tag className="m-0" color="error">{t('eventIntroduction.participation.eventFull')}</Tag>
                            </span>
                          )
                          : `${event?.registered_count || 0} / ${event?.participant_limit}`
                        }
                      </p>
                    </div>
                  </div>
                  {event?.registered_count >= event?.participant_limit && event?.waitlist_limit > 0 && (
                    <div className="flex items-start gap-4">
                      <div className="rounded-full p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                        <UserAddOutlined className="text-xl text-gray-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 text-base">{t('eventIntroduction.eventInfo.waitlistCount')}</p>
                        <p className="text-gray-600 mt-1 text-base">
                          {event?.waitlisted_count !== undefined ? `${event.waitlisted_count} / ${event.waitlist_limit}` : `N/A / ${event.waitlist_limit}`}
                        </p>
                      </div>
                    </div>
                  )}
                  <div className="flex items-start gap-4">
                    <div className="rounded-full p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                      <ClockCircleOutlined className="text-xl text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-base">{t('eventIntroduction.eventInfo.duration')}</p>
                      <p className="text-gray-600 mt-1 text-base">{duration}</p>
                    </div>
                  </div>
                  {/* Event Tags */}
                  {event?.tags && event.tags.length > 0 && (
                    <div className="flex items-start gap-4">
                      <div className="rounded-full p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                        <TagOutlined className="text-xl text-gray-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 text-base">{t('eventIntroduction.eventInfo.tags', 'Event Tags')}</p>
                        <div className="mt-1">
                          {event.tags.map((tag, index) => (
                            <span key={tag.id} className="text-gray-700 text-base">
                              {getTagName(tag, i18n.language)}
                              {index < event.tags.length - 1 && ', '}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>



              {/* Things to Know */}
              <div className="mb-8">
                <p className="text-base font-medium text-gray-600 mb-2">{t('eventIntroduction.sections.thingsToKnow')}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {hasIdentityRequirements(event?.verification_type_keys) ? (
                    (event.verification_type_keys || []).map((key) => {
                      const typeDetail = verificationTypeDetails.find(detail => detail.key === key);
                      const typeName = typeDetail ? typeDetail.name : key; // Use fetched name, fallback to key
                      return (
                        <div key={key} className="flex items-center gap-4">
                          <div className="rounded-lg p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                            <span className="text-gray-600">{identityIconMap[key] || <IdcardOutlined />}</span>
                          </div>
                          <p className="font-medium text-gray-900 text-base">
                            {t('eventIntroduction.idVerification.requiredTemplate', { type: typeName })}
                          </p>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex items-center gap-4">
                      <div className="rounded-lg p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                        <IdcardOutlined className="text-xl text-gray-600" />
                      </div>
                      <p className="font-medium text-gray-900 text-base">{t('eventIntroduction.idVerification.notRequired')}</p>
                    </div>
                  )}
                  {/* <div className="flex items-center gap-4">
                    <div className="rounded-lg p-2.5 bg-gray-50 h-11 w-11 flex items-center justify-center">
                      <UserAddOutlined className="text-xl text-pink-500" />
                    </div>
                    <p className="font-medium text-gray-900 text-base">{t('eventIntroduction.volunteerOpportunity.title')}</p>
                  </div> */}
                </div>
              </div>
              {/* Location Section */}
              <div className="mb-8">
                <p className="text-base font-medium text-gray-600 mb-2">{t('eventIntroduction.sections.location', 'Location')}</p>
                <div className="grid grid-cols-1 gap-6">
                  {/* Physical Address (if available) */}
                  {(event?.location_type === 'physical' || event?.location_type === 'hybrid') && event?.location_full_address && (
                    <div className="flex items-start justify-between">
                      <div className="flex items-start flex-1">
                        <div className="w-1 bg-gray-300 h-auto self-stretch mr-3 rounded-full"></div>
                        <div>
                          <span className="font-medium text-gray-900 text-base">{event.location_full_address}</span>
                          <span className="text-gray-600 block mt-1">
                            {t('eventIntroduction.location.physicalEvent', 'Event takes place in-person')}
                          </span>
                        </div>
                      </div>
                      <Button
                        type="text"
                        icon={<Icon icon="tabler:external-link" />}
                        onClick={() => window.open(`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(event.location_full_address)}`, '_blank')}
                      >
                        {t('eventIntroduction.location.openMap', 'Open Map')}
                      </Button>
                    </div>
                  )}

                  {/* Online URL (if available) */}
                  {(event?.location_type === 'online' || event?.location_type === 'hybrid') && event?.location_online_url && (
                    <div className="flex items-start justify-between">
                      <div className="flex items-start flex-1">
                        <div className="w-1 bg-gray-300 h-auto self-stretch mr-3 rounded-full"></div>
                        <div>
                          <span className="font-medium text-gray-900 text-base">{event.location_online_url}</span>
                          <span className="text-gray-600 block mt-1">
                            {event?.location_type === 'hybrid' 
                              ? t('eventIntroduction.location.hybridEvent', 'Event takes place both online and in-person')
                              : t('eventIntroduction.location.onlineEvent', 'Event takes place online')
                            }
                          </span>
                        </div>
                      </div>
                      <Button
                        type="text"
                        icon={<CopyOutlined />}
                        onClick={() => {
                          navigator.clipboard.writeText(event.location_online_url);
                          message.success(t('eventIntroduction.location.urlCopied', 'URL copied to clipboard'));
                        }}
                      >
                        {t('eventIntroduction.location.copyUrl', 'Copy URL')}
                      </Button>
                    </div>
                  )}

                  {/* No location provided */}
                  {(!event?.location_type ||
                    (event?.location_type === 'physical' && !event?.location_full_address) ||
                    (event?.location_type === 'online' && !event?.location_online_url) ||
                    (event?.location_type === 'hybrid' && !event?.location_full_address && !event?.location_online_url)) && (
                      <div className="flex items-start">
                        <div className="w-1 bg-gray-300 h-auto self-stretch mr-3 rounded-full"></div>
                        <div>
                          <span className="font-medium text-gray-900 text-base">
                            {t('eventIntroduction.location.toBeAnnounced', 'Location to be announced')}
                          </span>
                          <span className="text-gray-600 block mt-1">
                            {t('eventIntroduction.location.toBeAnnouncedDesc', 'Location details will be updated soon')}
                          </span>
                        </div>
                      </div>
                    )}
                </div>
              </div>

              <Divider className="mb-8 section-divider" orientation="left">
                <span className="text-xl text-gray-800">{t('eventIntroduction.sections.aboutEvent.title')}</span>
              </Divider>

              {/* Event Description */}
              <div className="mb-8">
                <div
                  className="event-description"
                  dangerouslySetInnerHTML={{ __html: safeHtml }}
                />
              </div>


              {/* Video Section - Check media_items for video */}
              {(() => {
                const videoItem = event?.media_items?.find(item => item.file_path && isVideoFile(item.file_path));
                
                if (videoItem) {
                  return (
                    <div className="mb-8">
                      <Divider className="mb-8 section-divider" orientation="left"><span className="text-xl text-gray-800">{t('eventIntroduction.sections.video')}</span></Divider>
                      <video
                        ref={videoRef}
                        controls
                        className="w-full rounded-lg"
                        style={{ maxHeight: 400, objectFit: 'contain', background: '#000' }}
                      >
                        <source 
                          src={videoItem.file_path} 
                          type={`video/${videoItem.file_path.split('.').pop().toLowerCase()}`} 
                        />
                        {t('eventIntroduction.common.video.notSupported')}
                      </video>
                      {/* Fallback link if video cannot be played */}
                      <div className="mt-2 text-center">
                        <a 
                          href={videoItem.file_path} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:text-blue-700"
                        >
                          {t('eventIntroduction.common.video.openExternally')}
                        </a>
                      </div>
                    </div>
                  );
                }
                return null;
              })()}
            </div>

            {/* Right Column: Booking Card */}
            <div className="lg:col-span-1">
              <div className="sticky top-[88px] bg-white rounded-xl shadow-lg p-4 border border-gray-200 transition-all duration-200">
                <div className="mb-6">
                  <p className="text-2xl font-bold">
                    {formatPrice(event.price)}
                    <span className="text-base font-normal text-gray-600 ml-2">
                      {t('eventIntroduction.bookingCard.pricePerPerson')}
                    </span>
                  </p>
                </div>

                <div className="border border-gray-300 rounded-lg mb-2">
                  <div className="p-4 border-b">
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <p className="text-xs font-medium text-gray-500 mb-1">
                          {t('eventIntroduction.bookingCard.labels.date')}
                        </p>
                        <p className="text-sm font-medium">
                          {formatCompactDateTimeRange(event?.start_time, event?.end_time, i18n.language)}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-xs font-medium text-gray-500 mb-1">
                      {t('eventIntroduction.bookingCard.labels.location')}
                    </p>
                    <p className="text-sm font-medium flex items-center">
                      <EnvironmentOutlined className="mr-2 text-gray-500" />
                      <span>
                        {event?.location_type === 'physical' && event?.location_full_address}
                        {event?.location_type === 'online' && event?.location_online_url}
                        {event?.location_type === 'hybrid' && `${event?.location_full_address || ''} (${event?.location_online_url || ''})`}
                        {(!event?.location_type ||
                          (event?.location_type === 'physical' && !event?.location_full_address) ||
                          (event?.location_type === 'online' && !event?.location_online_url) ||
                          (event?.location_type === 'hybrid' && !event?.location_full_address && !event?.location_online_url)
                        ) && t('home.tbdLocation')}
                      </span>
                    </p>
                  </div>
                </div>

                {/* Verification Needed in Booking Card*/}

                {getMissingVerifications(user, event.verification_type_keys).length > 0 && (
                  <div className="p-4 border-t">
                    <p className="text-xs font-medium text-gray-500 mb-2">
                      {t('eventIntroduction.bookingCard.verificationNeeded.title')}
                    </p>
                    <ul className="space-y-2">
                      {getMissingVerifications(user, event.verification_type_keys).map((key) => {
                        const typeDetail = verificationTypeDetails.find(detail => detail.key === key);
                        const typeName = typeDetail ? typeDetail.name : key; // Use fetched name, fallback to key
                        return (
                          <li key={key} className="flex items-center text-sm">
                            <span className="text-gray-500 mr-2">
                              {identityIconMap[key] || <IdcardOutlined />}
                            </span>
                            <span className="font-medium text-gray-700 text-base">
                              {t('eventIntroduction.idVerification.typeRequired', { type: typeName })}
                            </span>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}

                {/* Government Funding Needed in Booking Card*/}
                {!loadingFundingTypes && event?.government_funding_keys && event.government_funding_keys.length > 0 && (
                  <div className="p-4 border-t">
                    <p className="text-xs font-medium text-gray-500 mb-3">
                      {t('eventIntroduction.bookingCard.governmentFunding.title')}
                    </p>
                    <ul className="space-y-2">
                      {(event.government_funding_keys || []).map((key) => {
                        const fundingDetail = governmentFundingTypes.find(type => type.key === key);
                        const fundingName = fundingDetail ? fundingDetail.name : key.replace(/_/g, ' ');

                        return (
                          <li key={key} className="text-sm">
                            <span className="font-medium text-gray-700 text-base">
                              {fundingName}
                            </span>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}

                <Divider style={{ margin: '16px 0' }} />

                {renderParticipationSection()}

                {event.price === '0' ? (
                  <div className="text-center text-gray-500 text-sm mt-2">
                    {t('eventIntroduction.bookingCard.noCharge')}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 text-sm mt-2">
                    {t('eventIntroduction.bookingCard.charge')}
                  </div>
                )}

              </div>
            </div>
          </div>
        </>
      )}

      <Modal
        title={t('eventIntroduction.verificationModal.title')}
        open={showVerificationModal}
        onOk={() => setShowVerificationModal(false)}
        onCancel={() => setShowVerificationModal(false)}
        okText={t('eventIntroduction.verificationModal.buttons.understood')}
      >
        <div>
          <Paragraph className="mb-4">
            {t('eventIntroduction.verificationModal.content.main')}
          </Paragraph>
          <ul className="space-y-3">
            {event?.verification_type_keys && getMissingVerifications(user, event.verification_type_keys).map(docKey => {
              const typeDetail = verificationTypeDetails.find(detail => detail.key === docKey);
              const typeName = typeDetail ? typeDetail.name : docKey;
              return (
                <li key={docKey} className="text-gray-700 flex items-center">
                  <span className="text-gray-400 mr-3">•</span>
                  {typeName}
                </li>
              );
            })}
          </ul>
        </div>
      </Modal>
    </div>
  );
};

export default EventIntroduction;
