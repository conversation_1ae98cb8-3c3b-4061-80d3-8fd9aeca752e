import React, { useState, useEffect } from 'react';
import { Typography, Form, Divider, Alert, message, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Trans } from 'react-i18next';
import IdentityVerificationCard from '../../components/IdentityVerificationCard';
import { profileService } from '../../services/profileService';

const { Title, Text } = Typography;

// Matches VerificationTypeEnum from api_update.md
const VerificationTypeEnum = {
    HkIDCard: "hk_id_card",
    MainlandChinaIDCard: "mainland_china_id_card",
    MainlandTravelPermit: "mainland_travel_permit",
    Passport: "passport",
    HkYouthPlus: "hk_youth_plus",
    AddressProof: "address_proof",
    StudentID: "student_id",
    HomeVisit: "home_visit",
};

const IdentityVerificationTab = () => {
    const { t } = useTranslation();
    const [verifications, setVerifications] = useState([]);
    const [loading, setLoading] = useState(true);
    
    // Fetch verifications when tab is activated
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                
                // Fetch verification data
                const verificationsData = await profileService.listUserVerifications();
                console.log("Checking verificationsData", verificationsData);
                setVerifications(verificationsData || []);
            } catch (error) {
                console.error('Error fetching verification data:', error);
                message.error(t('userSettings.identityVerification.fetchError'));
            } finally {
                setLoading(false);
            }
        };
        
        fetchData();
    }, [t]);
    
    // Map verifications array to document status objects
    const documentVerification = React.useMemo(() => {
        const docMap = {};
        
        if (!Array.isArray(verifications)) return docMap;
        
        verifications.forEach(verification => {
            const type = verification.verification_type;
            
            switch(type) {
                case VerificationTypeEnum.HkIDCard:
                    docMap.hkIdCard = { 
                        status: verification.status || 'unverified',
                        comment: verification.admin_notes || ''
                    };
                    break;
                case VerificationTypeEnum.MainlandTravelPermit:
                    docMap.mainlandTravelPermit = { 
                        status: verification.status || 'unverified',
                        comment: verification.admin_notes || ''
                    };
                    break;
                case VerificationTypeEnum.Passport:
                    docMap.passport = { 
                        status: verification.status || 'unverified',
                        comment: verification.admin_notes || ''
                    };
                    break;
                case VerificationTypeEnum.HkYouthPlus:
                    docMap.hkYouthPlus = { 
                        status: verification.status || 'unverified',
                        comment: verification.admin_notes || ''
                    };
                    break;
                case VerificationTypeEnum.AddressProof:
                    docMap.addressProof = { 
                        status: verification.status || 'unverified',
                        comment: verification.admin_notes || ''
                    };
                    break;
                case VerificationTypeEnum.MainlandChinaIDCard:
                    docMap.mainlandChinaIdCard = { 
                        status: verification.status || 'unverified',
                        comment: verification.admin_notes || ''
                    };
                    break;
                case VerificationTypeEnum.StudentID:
                    docMap.studentId = { 
                        status: verification.status || 'unverified',
                        comment: verification.admin_notes || ''
                    };
                    break;
                case VerificationTypeEnum.HomeVisit:
                    docMap.homeVisit = { 
                        status: verification.status || 'unverified',
                        comment: verification.admin_notes || ''
                    };
                    break;
                default:
                    break;
            }
        });
        
        return docMap;
    }, [verifications]);
    
    // Check if any document is verified
    const hasVerifiedDocument = documentVerification &&
        Object.values(documentVerification).some(doc => 
            doc.status === 'approved'
        );

    return (
        <>
            <Title level={3}>{t('userSettings.identityVerification.title')}</Title>
            <Text type="secondary">{t('userSettings.identityVerification.subtitle')}</Text>

            <Divider />

            {/* Account Recovery Alert - Temporarily hidden 
            {
                hasVerifiedDocument && (
                    <Alert
                        description={
                            <Trans
                                i18nKey="userSettings.identityVerification.accountRecoveryAlert"
                                components={{
                                    recoveryLink: <Link className="text-blue-500" to="/account-recovery" />
                                }}
                            />
                        }
                        type="warning"
                        showIcon
                        closable
                        style={{ marginBottom: '24px' }}
                    />
                )
            }
            */}

            {loading ? (
                <div className="text-center p-4">
                    <Spin />
                </div>
            ) : (
                <>
                    {/* HKID Verification Row */}
                    <div className="settings-tab-content" style={{ marginBottom: '32px' }}>
                        <div className="settings-tab-sidebar">
                            <div className="settings-section-header">
                                <span className="settings-section-title">{t('userSettings.identityVerification.hkidVerification.title')}</span>
                                <span className="settings-section-subtitle">{t('userSettings.identityVerification.hkidVerification.description')}</span>
                            </div>
                        </div>
                        <div className="settings-tab-main">
                            <IdentityVerificationCard
                                title={t('userSettings.identityVerification.hkidVerification.title')}
                                status={documentVerification?.hkIdCard?.status || 'unverified'}
                                comment={documentVerification?.hkIdCard?.comment}
                                type="identity"
                                verificationPath={`/register/identity-verification?docType=${VerificationTypeEnum.HkIDCard}`}
                                hideVerifyButton={true}
                            />
                        </div>
                    </div>

                    <Divider />

                    {/* Mainland China ID Card Verification Row */}
                    <div className="settings-tab-content" style={{ marginBottom: '32px' }}>
                        <div className="settings-tab-sidebar">
                            <div className="settings-section-header">
                                <span className="settings-section-title">{t('userSettings.identityVerification.mainlandChinaIDVerification.title', 'Mainland China ID Card Verification')}</span>
                                <span className="settings-section-subtitle">{t('userSettings.identityVerification.mainlandChinaIDVerification.description', 'Upload your Mainland China Resident ID Card for identity verification.')}</span>
                            </div>
                        </div>
                        <div className="settings-tab-main">
                            <IdentityVerificationCard
                                title={t('userSettings.identityVerification.mainlandChinaIDVerification.title', 'Mainland China ID Card Verification')}
                                status={documentVerification?.mainlandChinaIdCard?.status || 'unverified'}
                                comment={documentVerification?.mainlandChinaIdCard?.comment}
                                type="identity"
                                verificationPath={`/register/identity-verification?docType=${VerificationTypeEnum.MainlandChinaIDCard}`}
                                hideVerifyButton={true}
                            />
                        </div>
                    </div>

                    <Divider />

                    {/* Mainland Travel Permit Verification Row */}
                    <div className="settings-tab-content" style={{ marginBottom: '32px' }}>
                        <div className="settings-tab-sidebar">
                            <div className="settings-section-header">
                                <span className="settings-section-title">{t('userSettings.identityVerification.mainlandPassVerification.title')}</span>
                                <span className="settings-section-subtitle">{t('userSettings.identityVerification.mainlandPassVerification.description')}</span>
                            </div>
                        </div>
                        <div className="settings-tab-main">
                            <IdentityVerificationCard
                                title={t('userSettings.identityVerification.mainlandPassVerification.title')}
                                status={documentVerification?.mainlandTravelPermit?.status || 'unverified'}
                                comment={documentVerification?.mainlandTravelPermit?.comment}
                                type="identity"
                                verificationPath={`/register/identity-verification?docType=${VerificationTypeEnum.MainlandTravelPermit}`}
                                hideVerifyButton={true}
                            />
                        </div>
                    </div>

                    <Divider />

                    {/* Passport Verification Row */}
                    <div className="settings-tab-content" style={{ marginBottom: '32px' }}>
                        <div className="settings-tab-sidebar">
                            <div className="settings-section-header">
                                <span className="settings-section-title">{t('userSettings.identityVerification.passportVerification.title')}</span>
                                <span className="settings-section-subtitle">{t('userSettings.identityVerification.passportVerification.description')}</span>
                            </div>
                        </div>
                        <div className="settings-tab-main">
                            <IdentityVerificationCard
                                title={t('userSettings.identityVerification.passportVerification.title')}
                                status={documentVerification?.passport?.status || 'unverified'}
                                comment={documentVerification?.passport?.comment}
                                type="identity"
                                verificationPath={`/register/identity-verification?docType=${VerificationTypeEnum.Passport}`}
                                hideVerifyButton={true}
                            />
                        </div>
                    </div>

                    <Divider />

                    {/* HK Youth Card Verification Row */}
                    <div className="settings-tab-content" style={{ marginBottom: '32px' }}>
                        <div className="settings-tab-sidebar">
                            <div className="settings-section-header">
                                <span className="settings-section-title">{t('userSettings.identityVerification.hkyouthVerification.title')}</span>
                                <span className="settings-section-subtitle">{t('userSettings.identityVerification.hkyouthVerification.description')}</span>
                            </div>
                        </div>
                        <div className="settings-tab-main">
                            <IdentityVerificationCard
                                title={t('userSettings.identityVerification.hkyouthVerification.title')}
                                status={documentVerification?.hkYouthPlus?.status || 'unverified'}
                                comment={documentVerification?.hkYouthPlus?.comment}
                                type="identity"
                                verificationPath={`/register/identity-verification?docType=${VerificationTypeEnum.HkYouthPlus}`}
                                hideVerifyButton={true}
                            />
                        </div>
                    </div>

                    <Divider />

                    {/* Student ID Verification Row */}
                    <div className="settings-tab-content" style={{ marginBottom: '32px' }}>
                        <div className="settings-tab-sidebar">
                            <div className="settings-section-header">
                                <span className="settings-section-title">{t('userSettings.identityVerification.studentIDVerification.title', 'Student ID Verification')}</span>
                                <span className="settings-section-subtitle">{t('userSettings.identityVerification.studentIDVerification.description', 'Upload your valid student ID card for identity verification.')}</span>
                            </div>
                        </div>
                        <div className="settings-tab-main">
                            <IdentityVerificationCard
                                title={t('userSettings.identityVerification.studentIDVerification.title', 'Student ID Verification')}
                                status={documentVerification?.studentId?.status || 'unverified'}
                                comment={documentVerification?.studentId?.comment}
                                type="identity"
                                verificationPath={`/register/identity-verification?docType=${VerificationTypeEnum.StudentID}`}
                                hideVerifyButton={true}
                            />
                        </div>
                    </div>

                    <Divider />

                    {/* Home Visit Verification Row */}
                    <div className="settings-tab-content" style={{ marginBottom: '32px' }}>
                        <div className="settings-tab-sidebar">
                            <div className="settings-section-header">
                                <span className="settings-section-title">{t('userSettings.identityVerification.homeVisitVerification.title', 'Home Visit Verification')}</span>
                                <span className="settings-section-subtitle">{t('userSettings.identityVerification.homeVisitVerification.description', 'Schedule a home visit for identity and address verification.')}</span>
                            </div>
                        </div>
                        <div className="settings-tab-main">
                            <IdentityVerificationCard
                                title={t('userSettings.identityVerification.homeVisitVerification.title', 'Home Visit Verification')}
                                status={documentVerification?.homeVisit?.status || 'unverified'}
                                comment={documentVerification?.homeVisit?.comment}
                                type="identity"
                                verificationPath={`/register/identity-verification?docType=${VerificationTypeEnum.HomeVisit}`}
                                hideVerifyButton={true}
                            />
                        </div>
                    </div>

                    <Divider />

                    {/* Address Verification Row */}
                    <div className="settings-tab-content">
                        <div className="settings-tab-sidebar">
                            <div className="settings-section-header">
                                <span className="settings-section-title">{t('userSettings.identityVerification.addressVerification.title')}</span>
                                <span className="settings-section-subtitle">{t('userSettings.identityVerification.addressVerification.description')}</span>
                            </div>
                        </div>
                        <div className="settings-tab-main">
                            <IdentityVerificationCard
                                title={t('userSettings.identityVerification.addressVerification.title')}
                                status={documentVerification?.addressProof?.status || 'unverified'}
                                comment={documentVerification?.addressProof?.comment}
                                type="address"
                                identityStatus={hasVerifiedDocument ? 'approved' : 'unverified'}
                                verificationPath={`/register/address-verification?docType=${VerificationTypeEnum.AddressProof}`}
                                hideVerifyButton={true}
                            />
                        </div>
                    </div>
                    <Divider />
                </>
            )}
        </>
    );
};

export default IdentityVerificationTab;