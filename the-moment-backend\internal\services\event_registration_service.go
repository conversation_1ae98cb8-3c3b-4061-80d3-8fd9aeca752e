package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/utils"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
	// No pgtype import needed if WaitlistPriority is *time.Time
)

// Declare the error variables used by event registration service
var (
	ErrNoEligibleEventForCheckIn = errors.New("no eligible event for check-in")
	ErrAlreadyCheckedIn          = errors.New("already checked-in to this event")
	ErrScannerNotAuthorized      = errors.New("scanner not authorized for this action")
	ErrParticipantNotRegistered  = errors.New("participant not registered for an eligible event")
	ErrUserNotFound              = errors.New("user not found")
	// Shared errors (<PERSON>rr<PERSON><PERSON><PERSON><PERSON><PERSON>aitlisted, Err<PERSON>lreadyRegistered, ErrRegistrationClosed,
	// ErrEventCapacityReached, Err<PERSON>aitlistFull, ErrEventNotFound,
	// ErrUserNotVerifiedForRequiredTypes) are defined in event_service.go

	ErrRegistrationAlreadyCancelled = errors.New("registration is already cancelled")
	ErrRegistrationNotCancellable   = errors.New("registration cannot be cancelled at this time")
	ErrRegistrationPendingApproval  = errors.New("registration is pending approval")
)

const (
	defaultCheckInWindowMinutes    = 30
	defaultCancellationCutoffHours = 24

	checkInMethodQRScanVolunteer = "qr_scan_volunteer"
	// Removed JobType string constants. Use direct string literals from job_service.go
)

// localConflictingEventDetails holds information about a conflicting event time.
// Defined locally as payloads.ConflictingEventDetails was not definitively located.
type localConflictingEventDetails struct {
	ConflictingEventStartTime *time.Time
	ConflictingEventEndTime   *time.Time
}

// errEventTimeConflictWithDetails is a local error type for event time conflicts within eventRegistrationService.
// This is used because the exact structure of EventTimeConflictError from event_service.go
// and its dependency on payloads.ConflictingEventDetails could not be fully resolved without further package structure analysis.
type errEventTimeConflictWithDetails struct {
	Details localConflictingEventDetails
	message string
}

func (e *errEventTimeConflictWithDetails) Error() string {
	if e.message != "" {
		return e.message
	}
	return "event time conflict detected"
}

// NotificationType represents the type of a notification.
type NotificationType string

// Notification type constants, mirroring those in notification_service.go for now
// TODO: Consolidate these with notification_service.go
const (
	NotificationTypeEventRegistrationSuccess NotificationType = "event_registration_success"
	NotificationTypeEventWaitlisted          NotificationType = "event_waitlisted"
	NotificationTypeEventRegistrationPending NotificationType = "event_registration_pending"
	// Add other relevant types if used by this service directly
)

// EventRegistrationService defines the service methods for event registrations.
type EventRegistrationService interface {
	// CheckInUserForClosestEvent checks in the user for the closest upcoming event they're registered for
	CheckInUserForClosestEvent(ctx context.Context, userID uuid.UUID) (*payloads.EventRegistrationResponse, error)

	// RegisterForEvent registers the user for an event
	RegisterForEvent(ctx context.Context, eventID, userID uuid.UUID) (*payloads.EventRegistrationResponse, error)

	// ListUserRegistrations lists all event registrations for a user with various filters
	ListUserRegistrations(ctx context.Context, userID uuid.UUID, params payloads.ListUserRegistrationsRequest) ([]payloads.EventRegistrationResponse, int64, error)

	// GetUserEventRegistration gets a specific event registration by ID for a user
	GetUserEventRegistration(ctx context.Context, userID, registrationID uuid.UUID) (*payloads.EventRegistrationResponse, error)

	// CancelEventRegistration cancels a user's event registration
	CancelEventRegistration(ctx context.Context, userID, registrationID uuid.UUID) (*payloads.EventRegistrationResponse, error)

	// UpdatePaymentStatus updates the payment status of an event registration
	UpdatePaymentStatus(ctx context.Context, registrationID uuid.UUID, newPaymentStatus string, staffID uuid.UUID) (*payloads.EventRegistrationResponse, error)

	// ListOrganizationEventRegistrations lists event registrations for an organization with various filters
	ListOrganizationEventRegistrations(ctx context.Context, orgID uuid.UUID, requesterUserID uuid.UUID, params payloads.ListOrgEventRegistrationsRequest) ([]payloads.EventRegistrationResponse, int64, error)

	// UpdateRegistrationStatus updates the status of a registration by an organization admin
	UpdateRegistrationStatus(ctx context.Context, registrationID, orgID, requesterUserID uuid.UUID, newStatusStr string, adminNotesStr string) (*payloads.EventRegistrationResponse, error)

	// CheckInParticipantByScanner handles the logic for a scanner (staff/volunteer) to check in a participant.
	CheckInParticipantByScanner(ctx context.Context, scannerUserID uuid.UUID, participantUserID uuid.UUID) (*payloads.EventRegistrationResponse, error)

	// ListParticipantUserIDsByEventAndStatus retrieves a list of user IDs for participants of a given event with a specific registration status.
	ListParticipantUserIDsByEventAndStatus(ctx context.Context, eventID uuid.UUID, status *db.EventRegistrationStatusType) ([]uuid.UUID, error)
}

// eventRegistrationService implements EventRegistrationService
type eventRegistrationService struct {
	queries                 *db.Queries
	pool                    *pgxpool.Pool
	notificationService     NotificationService
	jobService              JobService
	eventService            EventService
	orgService              OrganizationService
	baseURL                 string
	checkInWindowMinutes    int
	cancellationCutoffHours int
}

// NewEventRegistrationService creates a new EventRegistrationService
func NewEventRegistrationService(
	queries *db.Queries,
	pool *pgxpool.Pool,
	notifySvc NotificationService,
	jobSvc JobService,
	eventService EventService, // Add event service parameter
	baseURL string,
	checkInWindowMinutesVal int,
	cancellationCutoffHoursVal int,
) (EventRegistrationService, error) {
	if queries == nil {
		return nil, errors.New("db queries is required")
	}
	if pool == nil {
		return nil, errors.New("transaction manager is required")
	}
	if notifySvc == nil {
		return nil, errors.New("notification service is required")
	}
	if jobSvc == nil {
		return nil, errors.New("job service is required")
	}
	if eventService == nil {
		return nil, errors.New("event service is required")
	}

	if checkInWindowMinutesVal <= 0 {
		log.Warn().Msgf("checkInWindowMinutesVal is invalid (%d), using default: %d", checkInWindowMinutesVal, defaultCheckInWindowMinutes)
		checkInWindowMinutesVal = defaultCheckInWindowMinutes
	}
	if cancellationCutoffHoursVal <= 0 {
		log.Warn().Msgf("cancellationCutoffHoursVal is invalid (%d), using default: %d", cancellationCutoffHoursVal, defaultCancellationCutoffHours)
		cancellationCutoffHoursVal = defaultCancellationCutoffHours
	}

	return &eventRegistrationService{
		queries:                 queries,
		pool:                    pool,
		notificationService:     notifySvc,
		jobService:              jobSvc,
		eventService:            eventService,
		baseURL:                 baseURL,
		checkInWindowMinutes:    checkInWindowMinutesVal,
		cancellationCutoffHours: cancellationCutoffHoursVal,
	}, nil
}

// Helper struct to hold merged registration and event data for sorting
type eligibleCheckIn struct {
	db.GetEligibleEventRegistrationsForUserCheckInRow
	timeDifference time.Duration
}

// CheckInUserForClosestEvent attempts to check in the user for the closest eligible event.
func (s *eventRegistrationService) CheckInUserForClosestEvent(ctx context.Context, userID uuid.UUID) (*payloads.EventRegistrationResponse, error) {
	now := time.Now()
	windowStart := now.Add(-time.Duration(s.checkInWindowMinutes) * time.Minute)
	windowEnd := now.Add(time.Duration(s.checkInWindowMinutes) * time.Minute)

	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Time("check_in_attempt_time", now).Logger()
	logger.Info().Time("window_start", windowStart).Time("window_end", windowEnd).Msg("Searching for eligible events for check-in")

	// 1. Find all eligible event registrations for the user within the window
	dbEligibleRows, err := s.queries.GetEligibleEventRegistrationsForUserCheckIn(ctx, db.GetEligibleEventRegistrationsForUserCheckInParams{
		UserID:      userID,
		StartTime:   windowStart,
		StartTime_2: windowEnd, // Corrected field name to StartTime_2
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Info().Msg("No event registrations found for user at all (pre-filtering)")
			return nil, ErrNoEligibleEventForCheckIn
		}
		logger.Error().Err(err).Msg("Failed to get eligible event registrations from DB")
		return nil, fmt.Errorf("database error while searching for eligible events: %w", err)
	}

	if len(dbEligibleRows) == 0 {
		logger.Info().Msg("No eligible event registrations found within the defined time window and status.")
		return nil, ErrNoEligibleEventForCheckIn
	}

	logger.Info().Int("found_potential_registrations", len(dbEligibleRows)).Msg("Found potential registrations, proceeding to filter and select closest.")

	// 2. Filter further and select the closest event in time.
	var candidates []eligibleCheckIn
	for _, row := range dbEligibleRows {
		// Event start time must be within the window (already done by SQL)
		// Also consider events that have just started or are about to start.
		// The plan: "event start time is within now - 30 minutes and now + 30 minutes"
		// Our SQL query `e.start_time BETWEEN $2 AND $3` where $2 = now-30 and $3 = now+30 handles this.

		// Check if already attended this specific registration
		if row.RegistrationStatus == db.EventRegistrationStatusTypeAttended {
			logger.Info().Str("event_id", row.EventID.String()).Str("registration_id", row.RegistrationID.String()).Msg("Skipping event: already marked as attended.")
			continue // Already attended this one, skip
		}

		eventStartTime := row.EventStartTime // This is TIMESTAMPTZ from DB
		diff := eventStartTime.Sub(now)
		candidates = append(candidates, eligibleCheckIn{
			GetEligibleEventRegistrationsForUserCheckInRow: row,
			timeDifference: diff,
		})
	}

	if len(candidates) == 0 {
		logger.Info().Msg("No eligible (non-attended) registrations found after filtering.")
		return nil, ErrNoEligibleEventForCheckIn // All found were already attended or filtered out
	}

	// Sort candidates: closest to now (smallest absolute difference).
	// If tied, prefer future events. If still tied (same absolute diff, one past, one future), prefer future.
	// If still tied (e.g. two future events with same start time), explicit rule: earliest start time (already handled by diff).
	sort.Slice(candidates, func(i, j int) bool {
		absDiffI := time.Duration(math.Abs(float64(candidates[i].timeDifference)))
		absDiffJ := time.Duration(math.Abs(float64(candidates[j].timeDifference)))

		if absDiffI != absDiffJ {
			return absDiffI < absDiffJ // Sort by smallest absolute difference
		}
		// Tie-breaking: prefer future events (positive or zero difference)
		if (candidates[i].timeDifference >= 0) && (candidates[j].timeDifference < 0) {
			return true
		}
		if (candidates[i].timeDifference < 0) && (candidates[j].timeDifference >= 0) {
			return false
		}
		// If both are future or both are past with same absolute diff, prefer earlier start time (smaller original diff for future, larger for past)
		return candidates[i].EventStartTime.Before(candidates[j].EventStartTime)
	})

	selectedCandidate := candidates[0]
	logger.Info().Str("selected_event_id", selectedCandidate.EventID.String()).Str("selected_registration_id", selectedCandidate.RegistrationID.String()).Time("selected_event_start_time", selectedCandidate.EventStartTime).Msg("Selected closest eligible event for check-in")

	// 3. Handle specific cases (already handled during candidate selection for 'attended')
	// If selectedCandidate.RegistrationStatus == db.EventRegistrationStatusTypeAttended - this check is now effectively done

	// 4. Update the registration
	updatedReg, err := s.queries.UpdateEventRegistrationToCheckIn(ctx, db.UpdateEventRegistrationToCheckInParams{
		ID:     selectedCandidate.RegistrationID,
		UserID: userID, // Safeguard: ensure we only update if the user matches
	})
	if err != nil {
		// Check if the error is because the user_id in the DB didn't match ( safeguard triggered)
		// or if the registration was somehow deleted between selection and update.
		if errors.Is(err, pgx.ErrNoRows) { // Means WHERE clause (id + user_id) did not match
			logger.Error().Err(err).Str("registration_id", selectedCandidate.RegistrationID.String()).Msg("Failed to update event registration: registration not found for this user or already deleted.")
			return nil, fmt.Errorf("failed to check-in: registration update failed, possibly modified concurrently: %w", ErrNoEligibleEventForCheckIn)
		}
		logger.Error().Err(err).Str("registration_id", selectedCandidate.RegistrationID.String()).Msg("Failed to update event registration to attended in DB")
		return nil, fmt.Errorf("database error while updating registration: %w", err)
	}

	logEvent := logger.Info().Str("registration_id", updatedReg.ID.String()).Str("new_status", string(updatedReg.Status))
	if updatedReg.AttendedAt != nil { // Corrected: Check for nil pointer
		logEvent.Time("attended_at", *updatedReg.AttendedAt) // Corrected: Dereference pointer
	}
	logEvent.Msg("Event registration successfully updated to attended.")

	// 5. Return the updated registration details.
	// Fetch the full registration details to pass to the formatter
	fullRegistration, err := s.queries.GetEventRegistrationByID(ctx, updatedReg.ID)
	if err != nil {
		logger.Error().Err(err).Str("registration_id", updatedReg.ID.String()).Msg("Failed to fetch full registration details after update for response.")
		// Even if we can't format fully, the check-in succeeded. Return a minimal response or error.
		// For now, let's return an error indicating the check-in was successful but response generation failed.
		return nil, fmt.Errorf("check-in successful, but failed to retrieve full registration details for response: %w", err)
	}
	return s.formatEventRegistrationResponse(ctx, fullRegistration)
}

// IsUserEventTimeConflict checks if registering for newEventID would cause a time conflict
// with any of the user's existing active registrations.
// Returns true if conflict exists, details of the first conflicting event, and nil error.
// Returns false if no conflict, nil details, and nil error.
// Returns false, nil details, and an error if a database or other unexpected error occurs.
func (s *eventRegistrationService) IsUserEventTimeConflict(ctx context.Context, userID uuid.UUID, newEventID uuid.UUID) (bool, *localConflictingEventDetails, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Str("new_event_id", newEventID.String()).Logger()

	// 1. Get the start and end time of the new event
	newEventTime, err := s.queries.GetEventTimeByID(ctx, newEventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Msg("New event not found when checking for time conflict.")
			return false, nil, ErrEventNotFound // Use ErrEventNotFound from services package
		}
		logger.Error().Err(err).Msg("Failed to get new event time for conflict check.")
		return false, nil, fmt.Errorf("failed to get event details: %w", err)
	}

	// 2. Get start and end times of all existing active registrations for the user
	// This query fetches events the user is registered for or waitlisted on.
	existingEventTimes, err := s.queries.GetUserRegisteredEventTimes(ctx, userID)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		logger.Error().Err(err).Msg("Failed to get user's existing registered event times.")
		return false, nil, fmt.Errorf("failed to get existing registrations: %w", err)
	}

	// If pgx.ErrNoRows, existingEventTimes will be an empty slice, which is fine.

	// 3. Compare times
	for _, existingEvent := range existingEventTimes {
		// Check for overlap: (ExistingStart < NewEnd) AND (ExistingEnd > NewStart)
		eStartTime := existingEvent.StartTime // Assuming this is time.Time directly based on linter feedback
		eEndTime := existingEvent.EndTime     // Assuming this is time.Time directly
		nStartTime := newEventTime.StartTime  // Assuming this is time.Time directly
		nEndTime := newEventTime.EndTime      // Assuming this is time.Time directly

		if eStartTime.Before(nEndTime) && eEndTime.After(nStartTime) {
			logger.Info().Time("existing_start", eStartTime).Time("existing_end", eEndTime).Msg("Time conflict detected.")
			return true, &localConflictingEventDetails{
				ConflictingEventStartTime: &eStartTime,
				ConflictingEventEndTime:   &eEndTime,
			}, nil
		}
	}

	logger.Info().Msg("No time conflicts found for the user.")
	return false, nil, nil
}

// RegisterForEvent handles the logic for a user to register for an event.
// It includes checks for event status, existing registrations, eligibility, capacity, and waitlists.
func (s *eventRegistrationService) RegisterForEvent(ctx context.Context, userID uuid.UUID, eventID uuid.UUID) (resp *payloads.EventRegistrationResponse, err error) {
	logger := log.Ctx(ctx).With().Str("userID", userID.String()).Str("eventID", eventID.String()).Logger()
	logger.Info().Msg("Attempting to register user for event")

	tx, err := s.pool.Begin(ctx)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to begin transaction")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			rbErr := tx.Rollback(ctx)
			if rbErr != nil {
				logger.Error().Err(rbErr).Msg("Failed to rollback transaction")
			}
		}
	}()

	qtx := s.queries.WithTx(tx)

	// 1. Fetch Event Details
	// Use GetEventWithOrganizationByID to get all necessary event fields including RegistrationDeadline
	eventRow, err := qtx.GetEventWithOrganizationByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Msg("Event not found")
			return nil, ErrEventNotFound
		}
		logger.Error().Err(err).Msg("Failed to get event details")
		return nil, fmt.Errorf("failed to get event details: %w", err)
	}

	// Map eventRow to a simple db.Event for easier use, or access fields directly from eventRow
	event := db.Event{
		ID:                              eventRow.ID,
		Title:                           eventRow.Title,
		Status:                          eventRow.Status,
		ParticipantLimit:                eventRow.ParticipantLimit,
		WaitlistLimit:                   eventRow.WaitlistLimit,
		RequiresApprovalForRegistration: eventRow.RequiresApprovalForRegistration,
		StartTime:                       eventRow.StartTime,
		EndTime:                         eventRow.EndTime,
		OrganizationID:                  eventRow.OrganizationID,
	}

	// 2. Check Event Status and Registration Period
	// now := time.Now() // Removed as it became unused after RegistrationDeadline logic removal

	// Only allow registration for Published events.
	// db.EventStatusTypeUpcoming was not found in db/models.go for EventStatusType enum.
	if event.Status != db.EventStatusTypePublished {
		logger.Warn().Str("event_status", string(event.Status)).Msg("Event is not published, registration not allowed. If 'upcoming' is a valid state for registration, ensure it is added to EventStatusType enum.")
		return nil, ErrRegistrationClosed
	}

	// CRITICAL TODO: Registration deadline check is removed due to missing RegistrationDeadline field.
	// The 'registration_deadline' column might be missing from the 'events' table schema,
	// or not included in the 'GetEventWithOrganizationByIDRow' struct generated by sqlc.
	// This functionality MUST be restored by fixing the schema or query and then re-adding the check.
	logger.Warn().Msg("CRITICAL: Registration deadline check is currently disabled due to missing field data.")
	// if eventRow.RegistrationDeadline.Valid && now.After(eventRow.RegistrationDeadline.Time) { // Intended logic
	// 	logger.Warn().Time("registration_deadline", eventRow.RegistrationDeadline.Time).Msg("Registration deadline has passed")
	// 	return nil, ErrRegistrationClosed
	// }

	// Check for existing registration
	existingReg, err := qtx.GetEventRegistrationByUserAndEvent(ctx, db.GetEventRegistrationByUserAndEventParams{
		UserID:  userID,
		EventID: eventID,
	})

	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		logger.Error().Err(err).Msg("Failed to check for existing registration")
		return nil, fmt.Errorf("failed to check for existing registration: %w", err)
	}

	if err == nil { // Existing registration found
		logger.Info().Str("existing_registration_id", existingReg.ID.String()).Str("existing_status", string(existingReg.Status)).Msg("Existing registration found for user and event")
		switch existingReg.Status {
		case db.EventRegistrationStatusTypeRegistered:
			return nil, ErrAlreadyRegistered
		case db.EventRegistrationStatusTypeWaitlisted:
			return nil, ErrAlreadyWaitlisted
		case db.EventRegistrationStatusTypePendingApproval:
			return nil, ErrRegistrationPendingApproval
		case db.EventRegistrationStatusTypeAttended, db.EventRegistrationStatusTypeAbsent:
			return nil, fmt.Errorf("user has already %s this event", strings.ToLower(string(existingReg.Status)))
		case db.EventRegistrationStatusTypeCancelledByUser, db.EventRegistrationStatusTypeRejectedApproval:
			logger.Info().Msg("User has a previously cancelled/rejected registration. Proceeding with re-registration attempt.")
		default:
			// Log a warning if we encounter unexpected "cancelled" statuses that might be new/unhandled
			if strings.Contains(string(existingReg.Status), "cancel") || strings.Contains(string(existingReg.Status), "Cancel") {
				logger.Warn().Str("status", string(existingReg.Status)).Msg("Encountered an unhandled 'cancelled' status type. Review if new enum values (e.g., CancelledByAdmin, CancelledBySystem) were added to schema AND if they need specific handling here.")
			}
			logger.Warn().Str("unhandled_status", string(existingReg.Status)).Msg("Existing registration found with unhandled status")
			return nil, fmt.Errorf("existing registration has an unhandled status: %s", existingReg.Status)
		}
	}

	// 3. Check for Time Conflicts (if not re-registering a cancelled one)
	if existingReg.ID == uuid.Nil ||
		(existingReg.Status != db.EventRegistrationStatusTypeCancelledByUser &&
			existingReg.Status != db.EventRegistrationStatusTypeRejectedApproval) {
		conflict, conflictDetails, conflictErr := s.IsUserEventTimeConflict(ctx, userID, eventID)
		if conflictErr != nil {
			logger.Error().Err(conflictErr).Msg("Failed to check for event time conflicts")
			return nil, fmt.Errorf("failed to check for event time conflicts: %w", conflictErr)
		}
		if conflict {
			logger.Warn().Interface("conflicting_event", conflictDetails).Msg("Event time conflict detected")
			// Return the locally defined error type
			return nil, &errEventTimeConflictWithDetails{Details: *conflictDetails, message: "event time conflict detected"}
		}
	}

	// 4. Check User Verifications
	requiredVerificationKeys, listReqVerErr := qtx.ListRequiredVerificationTypesForEvent(ctx, eventID)
	if listReqVerErr != nil && !errors.Is(listReqVerErr, pgx.ErrNoRows) {
		logger.Error().Err(listReqVerErr).Msg("Failed to list required verification types for event")
		return nil, fmt.Errorf("failed to list required verification types: %w", listReqVerErr)
	}

	if len(requiredVerificationKeys) > 0 {
		userVerifications, listUserVerErr := s.queries.ListUserVerificationRequestsByUserID(ctx, userID)
		if listUserVerErr != nil && !errors.Is(listUserVerErr, pgx.ErrNoRows) {
			logger.Error().Err(listUserVerErr).Msg("Failed to retrieve user verifications")
			return nil, fmt.Errorf("failed to retrieve user verifications: %w", listUserVerErr)
		}

		approvedKeys := make(map[string]bool)
		for _, verification := range userVerifications {
			if verification.UserVerificationRequest.Status == db.VerificationStatusEnumApproved {
				approvedKeys[string(verification.UserVerificationRequest.VerificationType)] = true
			}
		}

		missingKeys := []string{}
		for _, requiredType := range requiredVerificationKeys {
			if !approvedKeys[requiredType] {
				missingKeys = append(missingKeys, requiredType)
			}
		}

		if len(missingKeys) > 0 {
			logger.Warn().Strs("missing_keys", missingKeys).Msg("User missing required verifications")
			return nil, fmt.Errorf("%w: missing %v", ErrUserNotVerifiedForRequiredTypes, missingKeys)
		}
	}

	// 5. Determine New Status & Check Capacity
	registeredCount, countRegErr := qtx.GetRegisteredCountForEvent(ctx, eventID)
	if countRegErr != nil {
		logger.Error().Err(countRegErr).Msg("Failed to get registered count for event")
		return nil, fmt.Errorf("failed to get registered count: %w", countRegErr)
	}

	var newStatus db.EventRegistrationStatusType
	var waitlistPriorityPayload *time.Time

	participantLimitVal := int64(0)
	if event.ParticipantLimit != nil {
		participantLimitVal = int64(*event.ParticipantLimit)
	}

	if participantLimitVal <= 0 || registeredCount < participantLimitVal ||
		(existingReg.ID != uuid.Nil && existingReg.Status == db.EventRegistrationStatusTypeRegistered) {
		if event.RequiresApprovalForRegistration {
			newStatus = db.EventRegistrationStatusTypePendingApproval
		} else {
			newStatus = db.EventRegistrationStatusTypeRegistered
		}
	} else {
		waitlistedCount, countWaitErr := qtx.GetWaitlistedCountForEvent(ctx, eventID)
		if countWaitErr != nil {
			logger.Error().Err(countWaitErr).Msg("Failed to get waitlisted count for event")
			return nil, fmt.Errorf("failed to get waitlisted count: %w", countWaitErr)
		}

		waitlistLimitVal := int64(0)
		if event.WaitlistLimit != nil {
			waitlistLimitVal = int64(*event.WaitlistLimit)
		}

		if waitlistLimitVal <= 0 || waitlistedCount < waitlistLimitVal ||
			(existingReg.ID != uuid.Nil && existingReg.Status == db.EventRegistrationStatusTypeWaitlisted) {
			newStatus = db.EventRegistrationStatusTypeWaitlisted
			nowT := time.Now()
			waitlistPriorityPayload = &nowT
		} else {
			logger.Warn().Msg("Event waitlist is full")
			return nil, ErrWaitlistFull
		}
	}

	var finalRegRow db.EventRegistration
	var notificationType NotificationType // Use defined NotificationType

	if existingReg.ID != uuid.Nil &&
		(existingReg.Status == db.EventRegistrationStatusTypeCancelledByUser ||
			existingReg.Status == db.EventRegistrationStatusTypeRejectedApproval) {
		logger.Info().Str("existing_registration_id", existingReg.ID.String()).Str("new_status", string(newStatus)).Msg("Updating existing cancelled/rejected registration")

		nowForUpdate := time.Now() // This is time.Time

		updateParams := db.UpdateEventRegistrationStatusAndDetailsParams{
			ID:               existingReg.ID,
			Status:           newStatus,
			PaymentStatus:    db.PaymentStatusTypeNotRequired,
			WaitlistPriority: waitlistPriorityPayload, // This is *time.Time
			RegisteredAt:     nowForUpdate,            // This should be time.Time based on db.EventRegistration.RegisteredAt and previous linter error
		}
		updatedReg, updateErr := qtx.UpdateEventRegistrationStatusAndDetails(ctx, updateParams)
		if updateErr != nil {
			if strings.Contains(updateErr.Error(), "event_registrations_event_id_user_id_key") {
				logger.Error().Err(updateErr).Msg("Duplicate key error on update, implies concurrent conflicting registration")
				return nil, ErrAlreadyRegistered
			}
			logger.Error().Err(updateErr).Msg("Failed to update existing event registration")
			err = fmt.Errorf("failed to update existing event registration: %w", updateErr)
			return nil, err
		}
		finalRegRow = updatedReg
		logger.Info().Str("registration_id", finalRegRow.ID.String()).Str("status", string(finalRegRow.Status)).Msg("Successfully updated (re-activated) event registration")

	} else {
		logger.Info().Str("new_status", string(newStatus)).Msg("Creating new event registration")
		createParams := db.CreateEventRegistrationParams{
			EventID:          eventID,
			UserID:           userID,
			Status:           newStatus,
			PaymentStatus:    db.PaymentStatusTypeNotRequired,
			RegistrationRole: db.EventRegistrationRoleTypeParticipant,
			WaitlistPriority: waitlistPriorityPayload,
		}
		createdRegRowInternal, createErr := qtx.CreateEventRegistration(ctx, createParams)
		if createErr != nil {
			if strings.Contains(createErr.Error(), "event_registrations_event_id_user_id_key") {
				logger.Error().Err(createErr).Msg("Duplicate key error on create, implies existing registration missed or race condition")
				return nil, ErrAlreadyRegistered
			}
			logger.Error().Err(createErr).Msg("Failed to create event registration")
			err = fmt.Errorf("failed to create event registration: %w", createErr)
			return nil, err
		}
		finalRegRow = db.EventRegistration{
			ID:                       createdRegRowInternal.ID,
			EventID:                  createdRegRowInternal.EventID,
			UserID:                   createdRegRowInternal.UserID,
			Status:                   createdRegRowInternal.Status,
			PaymentStatus:            createdRegRowInternal.PaymentStatus,
			RegistrationRole:         createdRegRowInternal.RegistrationRole,
			RegisteredAt:             createdRegRowInternal.RegisteredAt,
			AttendedAt:               createdRegRowInternal.AttendedAt,
			CancellationReasonByUser: createdRegRowInternal.CancellationReasonByUser,
			AdminNotesOnRegistration: createdRegRowInternal.AdminNotesOnRegistration,
			WaitlistPriority:         createdRegRowInternal.WaitlistPriority,
			CreatedAt:                createdRegRowInternal.CreatedAt,
			UpdatedAt:                createdRegRowInternal.UpdatedAt,
		}
		logger.Info().Str("registration_id", finalRegRow.ID.String()).Str("status", string(finalRegRow.Status)).Msg("Successfully created new event registration")
	}

	switch finalRegRow.Status {
	case db.EventRegistrationStatusTypeRegistered:
		notificationType = NotificationTypeEventRegistrationSuccess
	case db.EventRegistrationStatusTypeWaitlisted:
		notificationType = NotificationTypeEventWaitlisted
	case db.EventRegistrationStatusTypePendingApproval:
		notificationType = NotificationTypeEventRegistrationPending
	default:
		notificationType = "" // Or handle as an error/unknown notification
	}

	// 6. Commit Transaction
	commitErr := tx.Commit(ctx)
	if commitErr != nil {
		logger.Error().Err(commitErr).Msg("Failed to commit transaction")
		err = fmt.Errorf("failed to commit transaction: %w", commitErr)
		return nil, err
	}
	err = nil

	// 7. Enqueue Notifications
	if notificationType != "" {
		// userDetails, userErr := s.queries.GetUserByID(ctx, userID) // Commented out as userDetails is not used by the current jobPayload
		// if userErr != nil {
		// 	logger.Error().Err(userErr).Msg("Failed to get user details for notification")
		// } else
		if s.jobService != nil { // Check s.jobService directly
			// TODO: Define or import JobPayloadNotifyRegStatusUpdate and its fields (UserName, EventName, EventDetailsURL)
			// For now, only sending essential fields.
			jobPayload := struct { // Ad-hoc struct for now
				UserID    uuid.UUID
				EventID   uuid.UUID
				NewStatus string
			}{
				UserID:    userID,
				EventID:   eventID,
				NewStatus: string(finalRegRow.Status),
			}
			// jobPayload := JobPayloadNotifyRegStatusUpdate{
			// 	UserID:    userID,
			// 	EventID:   eventID,
			// 	NewStatus: string(finalRegRow.Status),
			// 	// UserName:        userDetails.DisplayName, // Commented out: Field potentially not in JobPayloadNotifyRegStatusUpdate
			// 	// EventName:       event.Title,             // Commented out: Field potentially not in JobPayloadNotifyRegStatusUpdate
			// 	// EventDetailsURL: fmt.Sprintf("/events/%s", eventID.String()), // Commented out: Field potentially not in JobPayloadNotifyRegStatusUpdate
			// }
			_, enqueueErr := s.jobService.EnqueueJob(ctx, string(notificationType), jobPayload, DefaultNotificationJobDelay, nil) // Use defined NotificationType
			if enqueueErr != nil {
				logger.Error().Err(enqueueErr).Msg("Failed to enqueue registration notification job")
			} else {
				logger.Info().Str("notification_type", string(notificationType)).Msg("Registration notification job enqueued")
			}
		} else {
			logger.Warn().Msg("JobService is nil, cannot enqueue registration notifications.")
		}
	}

	// 8. Format and return response
	return s.formatEventRegistrationResponse(ctx, finalRegRow)
}

// ListUserRegistrations implements EventRegistrationService
func (s *eventRegistrationService) ListUserRegistrations(ctx context.Context, userID uuid.UUID, params payloads.ListUserRegistrationsRequest) ([]payloads.EventRegistrationResponse, int64, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Logger()

	limit := int32(20) // DefaultPageSize
	if params.Limit > 0 {
		limit = int32(params.Limit)
		if limit > 100 { // MaxPageSize
			limit = 100 // MaxPageSize
		}
	}
	offset := int32(params.Offset)

	var dbStatusType db.NullEventStatusType
	if params.Status != nil && *params.Status != "" {
		valid := false
		for _, sVal := range db.AllEventStatusTypeValues() {
			if string(sVal) == *params.Status {
				dbStatusType = db.NullEventStatusType{EventStatusType: sVal, Valid: true}
				valid = true
				break
			}
		}
		if !valid {
			logger.Warn().Str("invalid_status_filter", *params.Status).Msg("Invalid event status filter value for ListUserRegistrations")
		}
	}

	var dbRoleType db.NullEventRegistrationRoleType
	if params.Role != nil && *params.Role != "" {
		valid := false
		for _, rVal := range db.AllEventRegistrationRoleTypeValues() {
			if string(rVal) == *params.Role {
				dbRoleType = db.NullEventRegistrationRoleType{EventRegistrationRoleType: rVal, Valid: true}
				valid = true
				break
			}
		}
		if !valid {
			logger.Warn().Str("invalid_role_filter", *params.Role).Msg("Invalid role filter value for ListUserRegistrations")
		}
	}

	countDbParams := db.CountUserRegistrationsWithFiltersParams{
		UserID:            userID,
		StartDate:         params.StartDate,
		EndDate:           params.EndDate,
		EventFilterStatus: dbStatusType,
		Role:              dbRoleType,
		OrganizationID:    params.OrganizationID,
	}

	totalCount, err := s.queries.CountUserRegistrationsWithFilters(ctx, countDbParams)
	if err != nil {
		logger.Error().Err(err).Msg("failed to count user registrations with filters")
		return nil, 0, fmt.Errorf("failed to count user registrations: %w", err)
	}

	if totalCount == 0 {
		return []payloads.EventRegistrationResponse{}, 0, nil
	}

	listDbParams := db.ListUserRegistrationsWithFiltersParams{
		UserID:            userID,
		StartDate:         params.StartDate,
		EndDate:           params.EndDate,
		EventFilterStatus: dbStatusType,
		Role:              dbRoleType,
		OrganizationID:    params.OrganizationID,
		SortOrder:         params.Sort,
		LimitVal:          limit,
		OffsetVal:         offset,
	}

	rows, err := s.queries.ListUserRegistrationsWithFilters(ctx, listDbParams)
	if err != nil {
		logger.Error().Err(err).Msg("failed to list user registrations with filters")
		return nil, 0, fmt.Errorf("failed to list user registrations: %w", err)
	}

	if len(rows) == 0 {
		return []payloads.EventRegistrationResponse{}, totalCount, nil
	}

	user, userErr := s.queries.GetUserByID(ctx, userID)
	if userErr != nil {
		logger.Warn().Err(userErr).Str("user_id_for_details", userID.String()).Msg("failed to fetch user details for ListUserRegistrations response; user info will be missing")
	}

	responses := make([]payloads.EventRegistrationResponse, 0, len(rows))
	for _, row := range rows {
		registration := db.EventRegistration{
			ID:                       row.ID,
			EventID:                  row.EventID,
			UserID:                   row.UserID,
			Status:                   row.Status,
			PaymentStatus:            row.PaymentStatus,
			RegistrationRole:         row.RegistrationRole,
			RegisteredAt:             row.RegisteredAt,
			AttendedAt:               row.AttendedAt,
			CancellationReasonByUser: row.CancellationReasonByUser,
			AdminNotesOnRegistration: row.AdminNotesOnRegistration,
			WaitlistPriority:         row.WaitlistPriority,
			CreatedAt:                row.CreatedAt,
			UpdatedAt:                row.UpdatedAt,
		}

		var mediaItems []payloads.MediaItemResponse
		if row.MediaItems != nil {
			// Check if row.MediaItems is already []byte (common for JSON from DB with sqlc)
			mediaItemsBytes, ok := row.MediaItems.([]byte)
			if !ok {
				// If not []byte, try to marshal it to JSON then unmarshal. This handles cases where it might be a string or other type.
				logger.Warn().Str("event_id", row.EventID.String()).Interface("media_items_type", fmt.Sprintf("%T", row.MediaItems)).Msg("MediaItems is not []byte, attempting intermediate marshal")
				jsonBytes, marshalErr := json.Marshal(row.MediaItems)
				if marshalErr != nil {
					logger.Error().Err(marshalErr).Str("event_id", row.EventID.String()).Msg("failed to marshal media_items to JSON before unmarshal")
					mediaItems = []payloads.MediaItemResponse{}
				} else {
					mediaItemsBytes = jsonBytes
				}
			}

			if len(mediaItemsBytes) > 0 && string(mediaItemsBytes) != "null" { // Check for actual content beyond just "null"
				if err := json.Unmarshal(mediaItemsBytes, &mediaItems); err != nil {
					logger.Error().Err(err).Str("event_id", row.EventID.String()).Str("media_items_raw", string(mediaItemsBytes)).Msg("failed to unmarshal media items for event")
					mediaItems = []payloads.MediaItemResponse{}
				} else {
					for i := range mediaItems {
						// Prepend base URL if FilePath is not an absolute URL
						if mediaItems[i].FilePath != "" && !strings.HasPrefix(mediaItems[i].FilePath, "http") {
							mediaItems[i].FilePath = s.baseURL + mediaItems[i].FilePath
						}
					}
				}
			} else {
				mediaItems = []payloads.MediaItemResponse{} // Initialize to empty slice if no meaningful data
			}
		} else {
			mediaItems = []payloads.MediaItemResponse{} // Initialize to empty slice if nil
		}

		responseItem := payloads.EventRegistrationResponse{
			EventRegistration:        registration,
			EventTitle:               row.EventTitle,
			EventStartTime:           row.EventStartTime,
			EventEndTime:             row.EventEndTime,
			EventLocationType:        string(row.EventLocationType),
			EventLocationFullAddress: row.EventLocationFullAddress,
			EventLocationOnlineURL:   row.EventLocationOnlineUrl,
			EventStatus:              string(row.EventStatus),
			EventOrganizationID:      &row.EventOrganizationID,
			EventPrice:               row.EventPrice,
			EventContactEmail:        row.EventContactEmail,
			EventContactPhone:        row.EventContactPhone,
			MediaItems:               mediaItems,
		}

		if row.EventOrganizationName != "" {
			orgName := row.EventOrganizationName
			responseItem.EventOrganizationName = &orgName
		}

		if userErr == nil && user.ID != uuid.Nil {
			responseItem.UserDisplayName = user.DisplayName
			responseItem.UserEmail = user.Email
			responseItem.UserPhone = user.Phone
		}

		responses = append(responses, responseItem)
	}

	return responses, totalCount, nil
}

// GetUserEventRegistration implements EventRegistrationService
func (s *eventRegistrationService) GetUserEventRegistration(ctx context.Context, userID, registrationID uuid.UUID) (*payloads.EventRegistrationResponse, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Str("registration_id", registrationID.String()).Logger()
	logger.Info().Msg("Getting user event registration details")

	// 1. Get the core registration details
	// Using a query that joins event and user might be more efficient if direct ownership check is not paramount here.
	// For now, use GetEventRegistrationByID and then verify ownership.
	// dbReg, err := s.queries.GetEventRegistrationByID(ctx, registrationID)
	// Need a query that also fetches event details for the response or make a subsequent call.
	// The existing `formatEventRegistrationResponse` fetches event and user details.

	// Let's use a more direct query that also checks user ownership implicitly or explicitly.
	// Assuming a query like `GetEventRegistrationForUser(ctx, userID, registrationID)` exists or can be added.
	// For now, let's use a combination:
	registration, err := s.queries.GetEventRegistrationByID(ctx, registrationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Msg("Event registration not found")
			return nil, ErrRegistrationNotFound
		}
		logger.Error().Err(err).Msg("Failed to get event registration by ID")
		return nil, fmt.Errorf("database error while getting registration: %w", err)
	}

	// 2. Verify ownership
	if registration.UserID != userID {
		logger.Warn().Msg("User does not own this registration")
		// This error implies the registration exists but doesn't belong to the user.
		// ErrRegistrationNotFound might be misleading. Consider utils.ErrForbidden.
		return nil, utils.ErrForbidden // More appropriate than ErrRegistrationNotFound
	}

	// 3. Format and return
	return s.formatEventRegistrationResponse(ctx, registration)
}

// CancelEventRegistration implements EventRegistrationService
func (s *eventRegistrationService) CancelEventRegistration(ctx context.Context, userID, registrationID uuid.UUID) (*payloads.EventRegistrationResponse, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Str("registration_id", registrationID.String()).Logger()
	logger.Info().Msg("CancelEventRegistration called in eventRegistrationService")

	// Simplified placeholder logic for cancellation - this should ideally involve database operations and proper checks.
	// This is NOT a complete implementation.

	reg, err := s.queries.GetEventRegistrationByID(ctx, registrationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrRegistrationNotFound
		}
		return nil, fmt.Errorf("failed to get registration for cancellation: %w", err)
	}

	if reg.UserID != userID {
		return nil, utils.ErrForbidden // Cannot cancel registration for another user
	}

	// Check if already cancelled or if event has passed, etc. (Simplified)
	if reg.Status == db.EventRegistrationStatusTypeCancelledByUser {
		return s.formatEventRegistrationResponse(ctx, reg) // Already cancelled, return current state
	}

	// Simulate update
	updatedRegParams := db.UpdateEventRegistrationStatusByAdminParams{
		RegistrationID: registrationID,
		NewStatus:      db.EventRegistrationStatusTypeCancelledByUser,
		UpdatedAt:      time.Now(),
	}

	updatedReg, err := s.queries.UpdateEventRegistrationStatusByAdmin(ctx, updatedRegParams)
	if err != nil {
		return nil, fmt.Errorf("failed to update registration status to cancelled: %w", err)
	}

	return s.formatEventRegistrationResponse(ctx, updatedReg)
}

// UpdatePaymentStatus implements EventRegistrationService
func (s *eventRegistrationService) UpdatePaymentStatus(ctx context.Context, registrationID uuid.UUID, newPaymentStatusStr string, staffID uuid.UUID) (*payloads.EventRegistrationResponse, error) {
	logger := log.Ctx(ctx).With().
		Str("registrationID", registrationID.String()).
		Str("newPaymentStatus", newPaymentStatusStr).
		Str("staffID", staffID.String()).
		Logger()
	logger.Info().Msg("Attempting to update payment status for event registration")

	// 1. Validate newPaymentStatus string
	var dbPaymentStatus db.PaymentStatusType
	if err := dbPaymentStatus.Scan(newPaymentStatusStr); err != nil || !dbPaymentStatus.Valid() {
		logger.Warn().Err(err).Msg("Invalid new payment status value")
		return nil, fmt.Errorf("invalid payment status: %s. %w", newPaymentStatusStr, utils.ErrBadRequest)
	}

	var updatedReg db.EventRegistration
	var err error // Declare err for the defer func

	tx, err := s.pool.Begin(ctx)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to begin transaction")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if p := recover(); p != nil {
			_ = tx.Rollback(ctx)
			panic(p) // re-panic after rollback
		} else if err != nil {
			if !errors.Is(err, pgx.ErrTxCommitRollback) && (err.Error() != "failed to commit transaction" && !strings.Contains(err.Error(), "failed to commit transaction:")) {
				rbErr := tx.Rollback(ctx)
				if rbErr != nil {
					logger.Error().Err(rbErr).Msg("Failed to rollback transaction")
				}
			}
		}
	}()

	qtx := s.queries.WithTx(tx)

	// --- Authorization Check ---
	currentReg, regErr := qtx.GetEventRegistrationByID(ctx, registrationID)
	if regErr != nil {
		if errors.Is(regErr, pgx.ErrNoRows) {
			logger.Warn().Msg("Event registration not found for authorization check")
			err = utils.ErrNotFound
			return nil, err
		}
		logger.Error().Err(regErr).Msg("Failed to get event registration for authorization check")
		err = fmt.Errorf("failed to get registration details for auth: %w", regErr)
		return nil, err
	}

	eventOrgID, eventOrgErr := qtx.GetEventOrganizationID(ctx, currentReg.EventID)
	if eventOrgErr != nil {
		if errors.Is(eventOrgErr, pgx.ErrNoRows) {
			logger.Error().Err(eventOrgErr).Str("eventID", currentReg.EventID.String()).Msg("Organization not found for event associated with registration")
			err = fmt.Errorf("organization not found for event: %w", eventOrgErr)
			return nil, err
		}
		logger.Error().Err(eventOrgErr).Str("eventID", currentReg.EventID.String()).Msg("Failed to get event organization ID for authorization")
		err = fmt.Errorf("failed to get event organization ID for auth: %w", eventOrgErr)
		return nil, err
	}

	isAuthorized, authErr := s.orgService.CheckUserOrganizationRole(ctx, payloads.CheckUserOrganizationRoleOptions{
		UserID:         staffID,
		OrganizationID: eventOrgID,
		RequiredRoles:  []string{"admin", "manager", "owner"},
	})
	if authErr != nil {
		logger.Error().Err(authErr).Str("staffID", staffID.String()).Str("organizationID", eventOrgID.String()).Msg("Error checking staff user role in organization")
		err = fmt.Errorf("failed to verify staff authorization: %w", authErr)
		return nil, err
	}
	if !isAuthorized {
		logger.Warn().Str("staffID", staffID.String()).Str("organizationID", eventOrgID.String()).Msg("Staff user not authorized to update payment status for this organization's event registration")
		err = utils.ErrForbidden
		return nil, err
	}
	// --- End Authorization Check ---

	// 2. Update payment status in DB
	updatedRegRow, txErr := qtx.UpdateEventRegistrationPaymentStatus(ctx, db.UpdateEventRegistrationPaymentStatusParams{
		ID:            registrationID,
		PaymentStatus: dbPaymentStatus,
	})
	if txErr != nil {
		if errors.Is(txErr, pgx.ErrNoRows) {
			logger.Warn().Msg("Event registration not found for payment status update after auth") // Added "after auth" for clarity
			err = utils.ErrNotFound
			return nil, err
		}
		logger.Error().Err(txErr).Msg("Failed to update event registration payment status in DB")
		err = fmt.Errorf("database error while updating payment status: %w", txErr)
		return nil, err
	}

	updatedReg = db.EventRegistration{
		ID:                       updatedRegRow.ID,
		EventID:                  updatedRegRow.EventID,
		UserID:                   updatedRegRow.UserID,
		Status:                   updatedRegRow.Status,
		PaymentStatus:            updatedRegRow.PaymentStatus,
		RegistrationRole:         updatedRegRow.RegistrationRole,
		RegisteredAt:             updatedRegRow.RegisteredAt,
		AttendedAt:               updatedRegRow.AttendedAt,
		CancellationReasonByUser: updatedRegRow.CancellationReasonByUser,
		AdminNotesOnRegistration: updatedRegRow.AdminNotesOnRegistration,
		WaitlistPriority:         updatedRegRow.WaitlistPriority,
		CreatedAt:                updatedRegRow.CreatedAt,
		UpdatedAt:                updatedRegRow.UpdatedAt,
	}

	// 3. Commit Transaction
	commitErr := tx.Commit(ctx)
	if commitErr != nil {
		logger.Error().Err(commitErr).Msg("Failed to commit transaction for payment status update")
		err = fmt.Errorf("failed to commit transaction: %w", commitErr)
		return nil, err
	}
	err = nil // Clear err after successful commit

	// 4. Enqueue notifications (outside transaction)
	if s.jobService != nil {
		jobPayload := JobPayloadNotifyRegStatusUpdate{
			UserID:    updatedReg.UserID,
			EventID:   updatedReg.EventID,
			NewStatus: string(updatedReg.Status),
		}
		_, enqueueErr := s.jobService.EnqueueJob(ctx, JobTypeNotifyRegStatusUpdate, jobPayload, DefaultNotificationJobDelay, nil)
		if enqueueErr != nil {
			logger.Error().Err(enqueueErr).Msg("Failed to enqueue payment status notification job")
			// Do not fail the whole operation for a notification error
		} else {
			logger.Info().Msg("Payment status notification job enqueued")
		}
	} else {
		logger.Warn().Msg("JobService is nil, cannot enqueue payment status notifications.")
	}

	// 8. Construct and return response
	return s.formatEventRegistrationResponse(ctx, updatedReg)
}

// ListOrganizationEventRegistrations lists event registrations for an organization with various filters
func (s *eventRegistrationService) ListOrganizationEventRegistrations(ctx context.Context, orgID uuid.UUID, requesterUserID uuid.UUID, params payloads.ListOrgEventRegistrationsRequest) ([]payloads.EventRegistrationResponse, int64, error) {
	logger := log.Ctx(ctx).With().Str("org_id", orgID.String()).Str("requester_user_id", requesterUserID.String()).Interface("params", params).Logger()
	logger.Info().Msg("Listing organization event registrations")

	// Validate string enum filters
	if params.Status != nil && *params.Status != "" {
		isValidStatus := false
		for _, validDbStatus := range db.AllEventRegistrationStatusTypeValues() {
			if string(validDbStatus) == *params.Status {
				isValidStatus = true
				break
			}
		}
		if !isValidStatus {
			logger.Warn().Str("status_filter", *params.Status).Msg("Invalid registration status filter value")
			return nil, 0, fmt.Errorf("%w: invalid registration status filter '%s'", utils.ErrBadRequest, *params.Status)
		}
	}

	if params.Role != nil && *params.Role != "" {
		isValidRole := false
		for _, validDbRole := range db.AllEventRegistrationRoleTypeValues() {
			if string(validDbRole) == *params.Role {
				isValidRole = true
				break
			}
		}
		if !isValidRole {
			logger.Warn().Str("role_filter", *params.Role).Msg("Invalid registration role filter value")
			return nil, 0, fmt.Errorf("%w: invalid registration role filter '%s'", utils.ErrBadRequest, *params.Role)
		}
	}

	if params.PaymentStatus != nil && *params.PaymentStatus != "" {
		isValidPaymentStatus := false
		for _, validDbPStatus := range db.AllPaymentStatusTypeValues() {
			if string(validDbPStatus) == *params.PaymentStatus {
				isValidPaymentStatus = true
				break
			}
		}
		if !isValidPaymentStatus {
			logger.Warn().Str("payment_status_filter", *params.PaymentStatus).Msg("Invalid payment status filter value")
			return nil, 0, fmt.Errorf("%w: invalid payment status filter '%s'", utils.ErrBadRequest, *params.PaymentStatus)
		}
	}

	listDbParams := db.ListOrganizationEventRegistrationsWithFiltersParams{
		OrganizationID:           orgID,
		Lim:                      int32(params.Limit),
		Offs:                     int32(params.Offset),
		EventID:                  params.EventID,
		FilterStartDate:          params.StartDate,
		FilterEndDate:            params.EndDate,
		FilterRegistrationStatus: params.Status,
		FilterParticipantUserID:  params.UserID,
		FilterRegistrationRole:   params.Role,
		FilterUserNameSearch:     params.SearchName,
		FilterPaymentStatus:      params.PaymentStatus,
	}

	dbRegRows, err := s.queries.ListOrganizationEventRegistrationsWithFilters(ctx, listDbParams)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Info().Msg("No event registrations found for the given filters")
			return []payloads.EventRegistrationResponse{}, 0, nil
		}
		logger.Error().Err(err).Msg("Failed to list organization event registrations from DB")
		return nil, 0, fmt.Errorf("database error while listing event registrations: %w", err)
	}

	countDbParams := db.CountOrganizationEventRegistrationsWithFiltersParams{
		OrganizationID:           orgID,
		EventID:                  params.EventID,
		FilterStartDate:          params.StartDate,
		FilterEndDate:            params.EndDate,
		FilterRegistrationStatus: params.Status,
		FilterParticipantUserID:  params.UserID,
		FilterRegistrationRole:   params.Role,
		FilterUserNameSearch:     params.SearchName,
		FilterPaymentStatus:      params.PaymentStatus,
	}
	totalCount, err := s.queries.CountOrganizationEventRegistrationsWithFilters(ctx, countDbParams)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to count organization event registrations from DB")
		return nil, 0, fmt.Errorf("database error while counting event registrations: %w", err)
	}

	var responses []payloads.EventRegistrationResponse
	for _, row := range dbRegRows {
		var eventDescriptionStr *string
		if row.EventDescription != nil && len(row.EventDescription) > 0 {
			desc := string(row.EventDescription)
			eventDescriptionStr = &desc
		}

		orgName := row.EventOrganizationName   // string to *string for payload
		userDisplayName := row.UserDisplayName // string to (direct) string for payload

		apiResponse := payloads.EventRegistrationResponse{
			EventRegistration: db.EventRegistration{
				ID:                       row.ID,
				EventID:                  row.EventID,
				UserID:                   row.UserID,
				Status:                   row.Status,
				PaymentStatus:            row.PaymentStatus,
				RegistrationRole:         row.RegistrationRole,
				RegisteredAt:             row.RegisteredAt,
				CancellationReasonByUser: row.CancellationReasonByUser,
				AttendedAt:               row.AttendedAt,
				CheckInByUserID:          row.CheckInByUserID,
				CheckInMethod:            row.CheckInMethod,
				AdminNotesOnRegistration: row.AdminNotesOnRegistration,
				CreatedAt:                row.CreatedAt,
				UpdatedAt:                row.UpdatedAt,
				WaitlistPriority:         row.WaitlistPriority,
			},
			EventTitle:               row.EventTitle,
			EventStartTime:           row.EventStartTime,
			EventEndTime:             row.EventEndTime,
			EventDescription:         eventDescriptionStr,
			EventLocationType:        string(row.EventLocationType),
			EventLocationFullAddress: row.EventLocationFullAddress,
			EventLocationOnlineURL:   row.EventLocationOnlineUrl,
			EventStatus:              string(row.EventStatus),
			EventOrganizationID:      &row.EventOrganizationID,
			EventOrganizationName:    &orgName,
			EventPrice:               row.EventPrice,
			EventContactEmail:        row.EventContactEmail,
			EventContactPhone:        row.EventContactPhone,
			UserDisplayName:          userDisplayName,
			UserEmail:                row.UserEmail,
			UserPhone:                row.UserPhone,
		}
		responses = append(responses, apiResponse)
	}

	logger.Info().Int("count", len(responses)).Int64("total_count", totalCount).Msg("Successfully listed organization event registrations")
	return responses, totalCount, nil
}

// UpdateRegistrationStatus updates the status of a registration by an organization admin
func (s *eventRegistrationService) UpdateRegistrationStatus(ctx context.Context, registrationID, orgID, requesterUserID uuid.UUID, newStatusStr string, adminNotesStr string) (*payloads.EventRegistrationResponse, error) {
	logger := log.Ctx(ctx).With().
		Str("registration_id", registrationID.String()).
		Str("org_id", orgID.String()).
		Str("requester_user_id", requesterUserID.String()).
		Str("new_status", newStatusStr).
		Logger()
	logger.Info().Msg("Updating event registration status")

	var newStatusType db.EventRegistrationStatusType
	isValidStatus := false
	for _, validDbStatus := range db.AllEventRegistrationStatusTypeValues() {
		if string(validDbStatus) == newStatusStr {
			newStatusType = validDbStatus
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		logger.Warn().Str("invalid_status", newStatusStr).Msg("Invalid new status for registration")
		return nil, fmt.Errorf("%w: invalid status '%s'", utils.ErrBadRequest, newStatusStr)
	}

	var adminNotesPtr *string
	if adminNotesStr != "" {
		adminNotesPtr = &adminNotesStr
	}

	updatedReg, err := s.queries.UpdateEventRegistrationStatusByAdmin(ctx, db.UpdateEventRegistrationStatusByAdminParams{
		RegistrationID: registrationID,
		NewStatus:      newStatusType,
		AdminNotes:     adminNotesPtr,
		UpdatedAt:      time.Now(),
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Msg("Event registration not found for status update")
			return nil, ErrRegistrationNotFound
		}
		logger.Error().Err(err).Msg("Failed to update event registration status in DB")
		return nil, fmt.Errorf("database error while updating registration status: %w", err)
	}

	response, err := s.formatEventRegistrationResponse(ctx, updatedReg)
	if err != nil {
		return nil, fmt.Errorf("failed to format registration response after update: %w", err)
	}

	logger.Info().Msg("Successfully updated event registration status")
	return response, nil
}

// CheckInParticipantByScanner handles the logic for a scanner (staff/volunteer) to check in a participant.
func (s *eventRegistrationService) CheckInParticipantByScanner(ctx context.Context, scannerUserID uuid.UUID, participantUserID uuid.UUID) (*payloads.EventRegistrationResponse, error) {
	logger := log.Ctx(ctx).With().
		Str("scanner_user_id", scannerUserID.String()).
		Str("participant_user_id", participantUserID.String()).
		Logger()
	logger.Info().Msg("Attempting to check in participant by scanner")

	// Constants for check-in window
	checkInWindowPast := time.Duration(s.checkInWindowMinutes) * time.Minute
	checkInWindowFuture := time.Duration(s.checkInWindowMinutes) * time.Minute
	now := time.Now()
	earliestCheckInTime := now.Add(-checkInWindowPast)
	latestCheckInTime := now.Add(checkInWindowFuture)

	// Step 1: Find Eligible Event Registration for Participant
	logger.Debug().Time("earliest_check_in", earliestCheckInTime).Time("latest_check_in", latestCheckInTime).Msg("Calculated check-in window")
	eligibleRegistrations, err := s.queries.GetEligibleEventRegistrationsForParticipantCheckIn(ctx, db.GetEligibleEventRegistrationsForParticipantCheckInParams{
		ParticipantUserID:   participantUserID,
		EarliestCheckInTime: earliestCheckInTime,
		LatestCheckInTime:   latestCheckInTime,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Msg("No eligible event registrations found for participant in the given window")
			return nil, ErrNoEligibleEventForCheckIn
		}
		logger.Error().Err(err).Msg("Failed to get eligible event registrations for participant")
		return nil, fmt.Errorf("database error finding eligible registration: %w", err)
	}

	if len(eligibleRegistrations) == 0 {
		logger.Warn().Msg("No eligible event registrations found for participant (empty slice)")
		return nil, ErrNoEligibleEventForCheckIn
	}

	selectedRegRow := eligibleRegistrations[0]
	logger.Info().Str("selected_registration_id", selectedRegRow.RegistrationID.String()).Str("selected_event_id", selectedRegRow.EventID.String()).Msg("Selected eligible registration")

	// Step 2: Authorize Scanner (Volunteer)
	eventID := selectedRegRow.EventID
	organizationID := selectedRegRow.OrganizationID

	orgVolunteerApp, err := s.queries.GetUserVolunteerApplicationForOrganization(ctx, db.GetUserVolunteerApplicationForOrganizationParams{
		OrganizationID: organizationID,
		UserID:         scannerUserID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Str("org_id", organizationID.String()).Msg("Scanner not found as an organization volunteer")
			return nil, ErrScannerNotAuthorized
		}
		logger.Error().Err(err).Str("org_id", organizationID.String()).Msg("Failed to get user volunteer application for organization")
		return nil, fmt.Errorf("database error checking org volunteer status: %w", err)
	}
	if orgVolunteerApp.Status != db.ApplicationStatusEnumApproved {
		logger.Warn().Str("org_id", organizationID.String()).Str("scanner_status", string(orgVolunteerApp.Status)).Msg("Scanner is not an approved organization volunteer")
		return nil, ErrScannerNotAuthorized
	}
	logger.Info().Str("org_id", organizationID.String()).Msg("Scanner is an approved organization volunteer")

	_, err = s.queries.GetApprovedEventVolunteerApplication(ctx, db.GetApprovedEventVolunteerApplicationParams{
		UserID:  scannerUserID,
		EventID: eventID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Str("event_id", eventID.String()).Msg("Scanner not found as an approved event-specific volunteer")
			return nil, ErrScannerNotAuthorized
		}
		logger.Error().Err(err).Str("event_id", eventID.String()).Msg("Failed to get approved event volunteer application")
		return nil, fmt.Errorf("database error checking event volunteer status: %w", err)
	}
	logger.Info().Str("event_id", eventID.String()).Msg("Scanner is an approved event-specific volunteer")

	// Step 3: Perform Check-in
	if selectedRegRow.RegistrationStatus == db.EventRegistrationStatusTypeAttended {
		logger.Info().Str("registration_id", selectedRegRow.RegistrationID.String()).Msg("Participant already marked as attended for this registration")
		return nil, ErrAlreadyCheckedIn
	}

	checkInMethodString := checkInMethodQRScanVolunteer
	var checkInByUserIDPtr *uuid.UUID
	if scannerUserID != uuid.Nil {
		checkInByUserIDPtr = &scannerUserID
	}
	var checkInMethodPtr *string
	if checkInMethodString != "" {
		checkInMethodPtr = &checkInMethodString
	}

	updatedReg, err := s.queries.UpdateEventRegistrationToCheckInByScanner(ctx, db.UpdateEventRegistrationToCheckInByScannerParams{
		RegistrationID:  selectedRegRow.RegistrationID,
		CheckInByUserID: checkInByUserIDPtr,
		CheckInMethod:   checkInMethodPtr,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Error().Err(err).Str("registration_id", selectedRegRow.RegistrationID.String()).Msg("Failed to update registration: No rows affected, registration might have been deleted or status changed concurrently.")
			return nil, fmt.Errorf("failed to check-in: registration update failed, possibly modified concurrently: %w", ErrNoEligibleEventForCheckIn)
		}
		logger.Error().Err(err).Str("registration_id", selectedRegRow.RegistrationID.String()).Msg("Failed to update event registration to attended by scanner")
		return nil, fmt.Errorf("database error while updating registration by scanner: %w", err)
	}
	logger.Info().Str("registration_id", updatedReg.ID.String()).Msg("Event registration successfully updated by scanner")

	// Step 4: Mark Scanner Volunteer as Attended
	err = s.queries.MarkEventVolunteerAttended(ctx, db.MarkEventVolunteerAttendedParams{
		UserID:  scannerUserID,
		EventID: eventID,
	})
	if err != nil {
		logger.Error().Err(err).Str("scanner_user_id", scannerUserID.String()).Str("event_id", eventID.String()).Msg("Failed to mark scanner volunteer as attended, but participant check-in succeeded.")
	} else {
		logger.Info().Str("scanner_user_id", scannerUserID.String()).Str("event_id", eventID.String()).Msg("Successfully marked scanner volunteer as attended.")
	}

	// Step 5: Construct and Return payloads.EventRegistrationResponse
	return s.formatEventRegistrationResponse(ctx, updatedReg)
}

// formatEventRegistrationResponse helper function
func (s *eventRegistrationService) formatEventRegistrationResponse(ctx context.Context, registration db.EventRegistration) (*payloads.EventRegistrationResponse, error) {
	logger := log.Ctx(ctx).With().Str("registration_id", registration.ID.String()).Logger()

	eventDetails, err := s.queries.GetEventWithOrganizationByID(ctx, registration.EventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Error().Err(err).Str("event_id", registration.EventID.String()).Msg("Event not found for registration")
			return nil, fmt.Errorf("%w: event %s not found for registration %s", ErrEventNotFound, registration.EventID, registration.ID)
		}
		logger.Error().Err(err).Str("event_id", registration.EventID.String()).Msg("Failed to get event details for registration")
		return nil, fmt.Errorf("failed to get event details: %w", err)
	}

	userDetails, err := s.queries.GetUserByID(ctx, registration.UserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Error().Err(err).Str("user_id", registration.UserID.String()).Msg("User not found for registration")
			return nil, fmt.Errorf("%w: user %s not found for registration %s", ErrUserNotFound, registration.UserID, registration.ID)
		}
		logger.Error().Err(err).Str("user_id", registration.UserID.String()).Msg("Failed to get user details for registration")
		return nil, fmt.Errorf("failed to get user details: %w", err)
	}

	var eventDescriptionStr *string
	if eventDetails.DescriptionContent != nil && len(eventDetails.DescriptionContent) > 0 {
		desc := string(eventDetails.DescriptionContent)
		eventDescriptionStr = &desc
	}

	orgName := eventDetails.OrganizationName
	userDisplayName := userDetails.DisplayName

	response := &payloads.EventRegistrationResponse{
		EventRegistration:        registration,
		EventTitle:               eventDetails.Title,
		EventStartTime:           eventDetails.StartTime,
		EventEndTime:             eventDetails.EndTime,
		EventDescription:         eventDescriptionStr,
		EventLocationType:        string(eventDetails.LocationType),
		EventLocationFullAddress: eventDetails.LocationFullAddress,
		EventLocationOnlineURL:   eventDetails.LocationOnlineUrl,
		EventStatus:              string(eventDetails.Status),
		EventOrganizationID:      &eventDetails.OrganizationID,
		EventOrganizationName:    &orgName,
		EventPrice:               eventDetails.Price,
		EventContactEmail:        eventDetails.ContactEmail,
		EventContactPhone:        eventDetails.ContactPhone,
		UserDisplayName:          userDisplayName,
		UserEmail:                userDetails.Email,
		UserPhone:                userDetails.Phone,
	}

	return response, nil
}

// ListParticipantUserIDsByEventAndStatus retrieves a list of user IDs for participants of a given event with a specific registration status.
// If status is nil, it fetches for all statuses. Otherwise, it filters by the provided status.
func (s *eventRegistrationService) ListParticipantUserIDsByEventAndStatus(ctx context.Context, eventID uuid.UUID, status *db.EventRegistrationStatusType) ([]uuid.UUID, error) {
	var statusLogStr string = "all"
	var statusFilterSQL string // This will be a plain string

	if status != nil {
		statusFilterSQL = string(*status)
		statusLogStr = statusFilterSQL
	}
	// If status (input) is nil, statusFilterSQL remains "" (empty string).
	// The SQL query needs to be written to interpret an empty string for StatusFilter as "match all".
	// Example SQL: WHERE event_id = $1 AND ($2 = '' OR status = $2)

	userIDs, err := s.queries.ListParticipantUserIDsByEventAndStatus(ctx, db.ListParticipantUserIDsByEventAndStatusParams{
		EventID: eventID,
		Column2: statusFilterSQL, // sqlc generated this as Column2 for the second param which is the status filter
	})

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return []uuid.UUID{}, nil // Return empty slice if no rows found
		}
		log.Ctx(ctx).Error().Err(err).Str("event_id", eventID.String()).Str("status_filter", statusLogStr).Msg("Failed to list participant user IDs by event and status")
		return nil, fmt.Errorf("failed to list participant user IDs: %w", err)
	}
	return userIDs, nil
}
