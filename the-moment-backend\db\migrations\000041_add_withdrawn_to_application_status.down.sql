-- Attempt to revert adding 'withdrawn' to application_status_enum.
-- PostgreSQL does not have a direct command to remove a value from an ENUM type once added,
-- especially if the value might be in use or if there are dependent objects.
--
-- To truly revert this, you would typically need to:
-- 1. Ensure no data uses the 'withdrawn' status. This might involve updating existing rows:
--    UPDATE user_volunteer_applications SET status = 'pending' WHERE status = 'withdrawn'; -- Or another appropriate status
--    UPDATE event_volunteer_applications SET status = 'pending' WHERE status = 'withdrawn'; -- Or another appropriate status
-- 2. Recreate the enum type without the 'withdrawn' value. This is a multi-step process:
--    a. Rename the current enum type (e.g., to application_status_enum_old).
--    b. Create a new enum type application_status_enum with the original values ('pending', 'approved', 'rejected').
--    c. Alter tables (user_volunteer_applications, event_volunteer_applications) to use the new enum type,
--       casting the values (e.g., ALTER COLUMN status TYPE application_status_enum USING status::text::application_status_enum).
--    d. Drop the old enum type (application_status_enum_old).
--
-- This down migration does not perform these complex steps automatically.
-- Manual intervention is required if a full rollback of this enum value is needed.
RAISE NOTICE 'Manual steps required to fully revert the addition of ''withdrawn'' to application_status_enum. See migration file for details.'; 