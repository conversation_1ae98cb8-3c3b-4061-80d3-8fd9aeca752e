package payloads

import (
	"math"
)

// Pagination defines the structure for pagination details in a response.
type Pagination struct {
	TotalItems  int `json:"total_items"`
	TotalPages  int `json:"total_pages"`
	CurrentPage int `json:"current_page"`
	Limit       int `json:"limit"`
}

// PaginatedResponse is a generic struct for responses that include pagination details.
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Page       int32       `json:"page"`
	PageSize   int32       `json:"page_size"`
	TotalItems int64       `json:"total_items"`
	TotalPages int64       `json:"total_pages"`
}

// NewPaginatedResponse creates a new PaginatedResponse.
// It calculates TotalPages based on totalItems and pageSize.
func NewPaginatedResponse(data interface{}, pageID int32, pageSize int32, totalItems int64) PaginatedResponse {
	if pageSize <= 0 {
		pageSize = 20 // Default page size if invalid, consider using a shared constant
	}
	totalPages := int64(math.Ceil(float64(totalItems) / float64(pageSize)))
	if totalItems == 0 { // if totalItems is 0, totalPages should be 0 not 1 after ceil
		totalPages = 0
	}

	return PaginatedResponse{
		Data:       data,
		Page:       pageID,
		PageSize:   pageSize,
		TotalItems: totalItems,
		TotalPages: totalPages,
	}
}

// PaginatedUserVerificationRequestsResponse defines the paginated response for user verification requests.
type PaginatedUserVerificationRequestsResponse struct {
	Data       []UserVerificationRequestAdminListResponse `json:"data"`
	Pagination Pagination                                 `json:"pagination"`
}
