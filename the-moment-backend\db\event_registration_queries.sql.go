// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: event_registration_queries.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const countOrganizationEventRegistrationsWithFilters = `-- name: CountOrganizationEventRegistrationsWithFilters :one
SELECT COUNT(*)
FROM event_registrations er
JOIN events e ON er.event_id = e.id
JOIN users u ON er.user_id = u.id
JOIN organizations o ON e.organization_id = o.id
WHERE e.organization_id = $1
  AND ($2::UUID IS NULL OR er.event_id = $2::UUID)
  AND ($3::TIMESTAMPTZ IS NULL OR e.start_time >= $3::TIMESTAMPTZ)
  AND ($4::TIMESTAMPTZ IS NULL OR e.start_time <= $4::TIMESTAMPTZ)
  AND ($5::TEXT IS NULL OR er.status = $5::event_registration_status_type)
  AND ($6::UUID IS NULL OR er.user_id = $6::UUID)
  AND ($7::TEXT IS NULL OR er.registration_role = $7::event_registration_role_type)
  AND ($8::TEXT IS NULL OR er.payment_status = $8::payment_status_type)
  AND ($9::TEXT IS NULL OR (u.display_name ILIKE '%' || $9::TEXT || '%' OR u.email ILIKE '%' || $9::TEXT || '%'))
`

type CountOrganizationEventRegistrationsWithFiltersParams struct {
	OrganizationID           uuid.UUID  `db:"organization_id" json:"organization_id"`
	EventID                  *uuid.UUID `db:"event_id" json:"event_id"`
	FilterStartDate          *time.Time `db:"filter_start_date" json:"filter_start_date"`
	FilterEndDate            *time.Time `db:"filter_end_date" json:"filter_end_date"`
	FilterRegistrationStatus *string    `db:"filter_registration_status" json:"filter_registration_status"`
	FilterParticipantUserID  *uuid.UUID `db:"filter_participant_user_id" json:"filter_participant_user_id"`
	FilterRegistrationRole   *string    `db:"filter_registration_role" json:"filter_registration_role"`
	FilterPaymentStatus      *string    `db:"filter_payment_status" json:"filter_payment_status"`
	FilterUserNameSearch     *string    `db:"filter_user_name_search" json:"filter_user_name_search"`
}

func (q *Queries) CountOrganizationEventRegistrationsWithFilters(ctx context.Context, arg CountOrganizationEventRegistrationsWithFiltersParams) (int64, error) {
	row := q.db.QueryRow(ctx, countOrganizationEventRegistrationsWithFilters,
		arg.OrganizationID,
		arg.EventID,
		arg.FilterStartDate,
		arg.FilterEndDate,
		arg.FilterRegistrationStatus,
		arg.FilterParticipantUserID,
		arg.FilterRegistrationRole,
		arg.FilterPaymentStatus,
		arg.FilterUserNameSearch,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countUserRegistrationsWithFilters = `-- name: CountUserRegistrationsWithFilters :one
SELECT COUNT(*)
FROM
    event_registrations er
JOIN
    events e ON er.event_id = e.id
WHERE
    er.user_id = $1
    AND (
        $2::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            ($3::TIMESTAMPTZ IS NOT NULL AND e.start_time >= $2::TIMESTAMPTZ AND e.start_time <= $3::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            ($3::TIMESTAMPTZ IS NULL AND e.start_time >= $2::TIMESTAMPTZ)
        )
    )
    AND ($4::event_status_type IS NULL OR e.status = $4::event_status_type)
    AND ($5::event_registration_role_type IS NULL OR er.registration_role = $5::event_registration_role_type)
    AND ($6::UUID IS NULL OR e.organization_id = $6::UUID)
`

type CountUserRegistrationsWithFiltersParams struct {
	UserID            uuid.UUID                     `db:"user_id" json:"user_id"`
	StartDate         *time.Time                    `db:"start_date" json:"start_date"`
	EndDate           *time.Time                    `db:"end_date" json:"end_date"`
	EventFilterStatus NullEventStatusType           `db:"event_filter_status" json:"event_filter_status"`
	Role              NullEventRegistrationRoleType `db:"role" json:"role"`
	OrganizationID    *uuid.UUID                    `db:"organization_id" json:"organization_id"`
}

func (q *Queries) CountUserRegistrationsWithFilters(ctx context.Context, arg CountUserRegistrationsWithFiltersParams) (int64, error) {
	row := q.db.QueryRow(ctx, countUserRegistrationsWithFilters,
		arg.UserID,
		arg.StartDate,
		arg.EndDate,
		arg.EventFilterStatus,
		arg.Role,
		arg.OrganizationID,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getEligibleEventRegistrationsForParticipantCheckIn = `-- name: GetEligibleEventRegistrationsForParticipantCheckIn :many
SELECT
    er.id AS registration_id,
    er.event_id,
    e.organization_id,
    er.user_id,
    er.status AS registration_status,
    e.title AS event_title,
    e.start_time AS event_start_time,
    e.end_time AS event_end_time,
    e.status AS event_status
FROM
    event_registrations er
JOIN
    events e ON er.event_id = e.id
WHERE
    er.user_id = $1
    AND er.status = 'registered'
    AND e.status = 'published'
    AND e.start_time >= $2 -- Parameter will be current_time - check_in_window_past
    AND e.start_time <= $3   -- Parameter will be current_time + check_in_window_future
    -- Consider event end_time as well if check-in is allowed throughout the event. This might be better handled by latest_check_in_time calculation logic.
    -- e.g., latest_check_in_time = MIN(current_time + check_in_window_future, event_end_time)
ORDER BY
    e.start_time ASC
`

type GetEligibleEventRegistrationsForParticipantCheckInParams struct {
	ParticipantUserID   uuid.UUID `db:"participant_user_id" json:"participant_user_id"`
	EarliestCheckInTime time.Time `db:"earliest_check_in_time" json:"earliest_check_in_time"`
	LatestCheckInTime   time.Time `db:"latest_check_in_time" json:"latest_check_in_time"`
}

type GetEligibleEventRegistrationsForParticipantCheckInRow struct {
	RegistrationID     uuid.UUID                   `db:"registration_id" json:"registration_id"`
	EventID            uuid.UUID                   `db:"event_id" json:"event_id"`
	OrganizationID     uuid.UUID                   `db:"organization_id" json:"organization_id"`
	UserID             uuid.UUID                   `db:"user_id" json:"user_id"`
	RegistrationStatus EventRegistrationStatusType `db:"registration_status" json:"registration_status"`
	EventTitle         string                      `db:"event_title" json:"event_title"`
	EventStartTime     time.Time                   `db:"event_start_time" json:"event_start_time"`
	EventEndTime       time.Time                   `db:"event_end_time" json:"event_end_time"`
	EventStatus        EventStatusType             `db:"event_status" json:"event_status"`
}

func (q *Queries) GetEligibleEventRegistrationsForParticipantCheckIn(ctx context.Context, arg GetEligibleEventRegistrationsForParticipantCheckInParams) ([]GetEligibleEventRegistrationsForParticipantCheckInRow, error) {
	rows, err := q.db.Query(ctx, getEligibleEventRegistrationsForParticipantCheckIn, arg.ParticipantUserID, arg.EarliestCheckInTime, arg.LatestCheckInTime)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEligibleEventRegistrationsForParticipantCheckInRow{}
	for rows.Next() {
		var i GetEligibleEventRegistrationsForParticipantCheckInRow
		if err := rows.Scan(
			&i.RegistrationID,
			&i.EventID,
			&i.OrganizationID,
			&i.UserID,
			&i.RegistrationStatus,
			&i.EventTitle,
			&i.EventStartTime,
			&i.EventEndTime,
			&i.EventStatus,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEligibleEventRegistrationsForUserCheckIn = `-- name: GetEligibleEventRegistrationsForUserCheckIn :many
SELECT
    er.id AS registration_id,
    er.event_id,
    er.user_id,
    er.status AS registration_status,
    e.title AS event_title,
    e.start_time AS event_start_time,
    e.end_time AS event_end_time,
    e.status AS event_status
FROM
    event_registrations er
JOIN
    events e ON er.event_id = e.id
WHERE
    er.user_id = $1
    AND er.status != 'cancelled_by_user' -- Assuming other non-attended statuses are eligible initially
    AND e.status = 'published' -- Use 'published' status based on enum
    AND e.start_time BETWEEN $2 AND $3
`

type GetEligibleEventRegistrationsForUserCheckInParams struct {
	UserID      uuid.UUID `db:"user_id" json:"user_id"`
	StartTime   time.Time `db:"start_time" json:"start_time"`
	StartTime_2 time.Time `db:"start_time_2" json:"start_time_2"`
}

type GetEligibleEventRegistrationsForUserCheckInRow struct {
	RegistrationID     uuid.UUID                   `db:"registration_id" json:"registration_id"`
	EventID            uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID             uuid.UUID                   `db:"user_id" json:"user_id"`
	RegistrationStatus EventRegistrationStatusType `db:"registration_status" json:"registration_status"`
	EventTitle         string                      `db:"event_title" json:"event_title"`
	EventStartTime     time.Time                   `db:"event_start_time" json:"event_start_time"`
	EventEndTime       time.Time                   `db:"event_end_time" json:"event_end_time"`
	EventStatus        EventStatusType             `db:"event_status" json:"event_status"`
}

func (q *Queries) GetEligibleEventRegistrationsForUserCheckIn(ctx context.Context, arg GetEligibleEventRegistrationsForUserCheckInParams) ([]GetEligibleEventRegistrationsForUserCheckInRow, error) {
	rows, err := q.db.Query(ctx, getEligibleEventRegistrationsForUserCheckIn, arg.UserID, arg.StartTime, arg.StartTime_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEligibleEventRegistrationsForUserCheckInRow{}
	for rows.Next() {
		var i GetEligibleEventRegistrationsForUserCheckInRow
		if err := rows.Scan(
			&i.RegistrationID,
			&i.EventID,
			&i.UserID,
			&i.RegistrationStatus,
			&i.EventTitle,
			&i.EventStartTime,
			&i.EventEndTime,
			&i.EventStatus,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventRegistrationByID = `-- name: GetEventRegistrationByID :one
SELECT id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method
FROM event_registrations
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetEventRegistrationByID(ctx context.Context, id uuid.UUID) (EventRegistration, error) {
	row := q.db.QueryRow(ctx, getEventRegistrationByID, id)
	var i EventRegistration
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CheckInByUserID,
		&i.CheckInMethod,
	)
	return i, err
}

const getEventRegistrationByUserAndEventAndRole = `-- name: GetEventRegistrationByUserAndEventAndRole :one
SELECT id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method
FROM event_registrations
WHERE user_id = $1 AND event_id = $2 AND registration_role = $3
LIMIT 1
`

type GetEventRegistrationByUserAndEventAndRoleParams struct {
	UserID           uuid.UUID                 `db:"user_id" json:"user_id"`
	EventID          uuid.UUID                 `db:"event_id" json:"event_id"`
	RegistrationRole EventRegistrationRoleType `db:"registration_role" json:"registration_role"`
}

func (q *Queries) GetEventRegistrationByUserAndEventAndRole(ctx context.Context, arg GetEventRegistrationByUserAndEventAndRoleParams) (EventRegistration, error) {
	row := q.db.QueryRow(ctx, getEventRegistrationByUserAndEventAndRole, arg.UserID, arg.EventID, arg.RegistrationRole)
	var i EventRegistration
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CheckInByUserID,
		&i.CheckInMethod,
	)
	return i, err
}

const listOrganizationEventRegistrationsWithFilters = `-- name: ListOrganizationEventRegistrationsWithFilters :many
SELECT
    er.id, er.event_id, er.user_id, er.status, er.payment_status, er.registration_role, er.registered_at, er.attended_at, er.cancellation_reason_by_user, er.admin_notes_on_registration, er.waitlist_priority, er.created_at, er.updated_at, er.check_in_by_user_id, er.check_in_method, -- all columns from event_registrations
    e.title AS event_title,
    e.start_time AS event_start_time,
    e.end_time AS event_end_time,
    e.description_content AS event_description, -- Assuming description is stored in description_content
    e.location_type AS event_location_type,
    e.location_full_address AS event_location_full_address,
    e.location_online_url AS event_location_online_url,
    e.status AS event_status,
    e.organization_id AS event_organization_id,
    o.name AS event_organization_name, -- Join with organizations table for name
    e.price AS event_price,
    e.contact_email AS event_contact_email,
    e.contact_phone AS event_contact_phone,
    u.display_name AS user_display_name,
    u.email AS user_email,
    u.phone AS user_phone
FROM event_registrations er
JOIN events e ON er.event_id = e.id
JOIN users u ON er.user_id = u.id
JOIN organizations o ON e.organization_id = o.id
WHERE e.organization_id = $1
  AND ($2::UUID IS NULL OR er.event_id = $2::UUID)
  AND ($3::TIMESTAMPTZ IS NULL OR e.start_time >= $3::TIMESTAMPTZ)
  AND ($4::TIMESTAMPTZ IS NULL OR e.start_time <= $4::TIMESTAMPTZ)
  AND ($5::TEXT IS NULL OR er.status = $5::event_registration_status_type)
  AND ($6::UUID IS NULL OR er.user_id = $6::UUID)
  AND ($7::TEXT IS NULL OR er.registration_role = $7::event_registration_role_type)
  AND ($8::TEXT IS NULL OR er.payment_status = $8::payment_status_type)
  AND ($9::TEXT IS NULL OR (u.display_name ILIKE '%' || $9::TEXT || '%' OR u.email ILIKE '%' || $9::TEXT || '%'))
ORDER BY e.start_time DESC, er.registered_at DESC -- Or other sensible default sort
LIMIT $11
OFFSET $10
`

type ListOrganizationEventRegistrationsWithFiltersParams struct {
	OrganizationID           uuid.UUID  `db:"organization_id" json:"organization_id"`
	EventID                  *uuid.UUID `db:"event_id" json:"event_id"`
	FilterStartDate          *time.Time `db:"filter_start_date" json:"filter_start_date"`
	FilterEndDate            *time.Time `db:"filter_end_date" json:"filter_end_date"`
	FilterRegistrationStatus *string    `db:"filter_registration_status" json:"filter_registration_status"`
	FilterParticipantUserID  *uuid.UUID `db:"filter_participant_user_id" json:"filter_participant_user_id"`
	FilterRegistrationRole   *string    `db:"filter_registration_role" json:"filter_registration_role"`
	FilterPaymentStatus      *string    `db:"filter_payment_status" json:"filter_payment_status"`
	FilterUserNameSearch     *string    `db:"filter_user_name_search" json:"filter_user_name_search"`
	Offs                     int32      `db:"offs" json:"offs"`
	Lim                      int32      `db:"lim" json:"lim"`
}

type ListOrganizationEventRegistrationsWithFiltersRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
	CheckInByUserID          *uuid.UUID                  `db:"check_in_by_user_id" json:"check_in_by_user_id"`
	CheckInMethod            *string                     `db:"check_in_method" json:"check_in_method"`
	EventTitle               string                      `db:"event_title" json:"event_title"`
	EventStartTime           time.Time                   `db:"event_start_time" json:"event_start_time"`
	EventEndTime             time.Time                   `db:"event_end_time" json:"event_end_time"`
	EventDescription         []byte                      `db:"event_description" json:"event_description"`
	EventLocationType        EventLocationType           `db:"event_location_type" json:"event_location_type"`
	EventLocationFullAddress *string                     `db:"event_location_full_address" json:"event_location_full_address"`
	EventLocationOnlineUrl   *string                     `db:"event_location_online_url" json:"event_location_online_url"`
	EventStatus              EventStatusType             `db:"event_status" json:"event_status"`
	EventOrganizationID      uuid.UUID                   `db:"event_organization_id" json:"event_organization_id"`
	EventOrganizationName    string                      `db:"event_organization_name" json:"event_organization_name"`
	EventPrice               *string                     `db:"event_price" json:"event_price"`
	EventContactEmail        *string                     `db:"event_contact_email" json:"event_contact_email"`
	EventContactPhone        *string                     `db:"event_contact_phone" json:"event_contact_phone"`
	UserDisplayName          string                      `db:"user_display_name" json:"user_display_name"`
	UserEmail                *string                     `db:"user_email" json:"user_email"`
	UserPhone                *string                     `db:"user_phone" json:"user_phone"`
}

func (q *Queries) ListOrganizationEventRegistrationsWithFilters(ctx context.Context, arg ListOrganizationEventRegistrationsWithFiltersParams) ([]ListOrganizationEventRegistrationsWithFiltersRow, error) {
	rows, err := q.db.Query(ctx, listOrganizationEventRegistrationsWithFilters,
		arg.OrganizationID,
		arg.EventID,
		arg.FilterStartDate,
		arg.FilterEndDate,
		arg.FilterRegistrationStatus,
		arg.FilterParticipantUserID,
		arg.FilterRegistrationRole,
		arg.FilterPaymentStatus,
		arg.FilterUserNameSearch,
		arg.Offs,
		arg.Lim,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrganizationEventRegistrationsWithFiltersRow{}
	for rows.Next() {
		var i ListOrganizationEventRegistrationsWithFiltersRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.Status,
			&i.PaymentStatus,
			&i.RegistrationRole,
			&i.RegisteredAt,
			&i.AttendedAt,
			&i.CancellationReasonByUser,
			&i.AdminNotesOnRegistration,
			&i.WaitlistPriority,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CheckInByUserID,
			&i.CheckInMethod,
			&i.EventTitle,
			&i.EventStartTime,
			&i.EventEndTime,
			&i.EventDescription,
			&i.EventLocationType,
			&i.EventLocationFullAddress,
			&i.EventLocationOnlineUrl,
			&i.EventStatus,
			&i.EventOrganizationID,
			&i.EventOrganizationName,
			&i.EventPrice,
			&i.EventContactEmail,
			&i.EventContactPhone,
			&i.UserDisplayName,
			&i.UserEmail,
			&i.UserPhone,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserRegistrationsWithFilters = `-- name: ListUserRegistrationsWithFilters :many
SELECT
    er.id,
    er.event_id,
    er.user_id,
    er.status,
    er.payment_status,
    er.registration_role,
    er.registered_at,
    er.attended_at,
    er.cancellation_reason_by_user,
    er.admin_notes_on_registration,
    er.waitlist_priority,
    er.created_at,
    er.updated_at,
    e.title AS event_title,
    e.start_time AS event_start_time,
    e.end_time AS event_end_time,
    -- e.description_content AS event_description_content, -- Consider if full content needed
    e.location_type AS event_location_type,
    e.location_full_address AS event_location_full_address,
    e.location_online_url AS event_location_online_url,
    e.status AS event_status,
    e.organization_id AS event_organization_id,
    (SELECT o.name FROM organizations o WHERE o.id = e.organization_id) AS event_organization_name,
    e.price AS event_price,
    e.contact_email AS event_contact_email,
    e.contact_phone AS event_contact_phone,
    COALESCE(
        (SELECT JSON_AGG(JSON_BUILD_OBJECT(
            'id', emi.id,
            'file_name', emi.file_name,
            'file_path', emi.file_path,
            'file_type', emi.file_type,
            'file_size', emi.file_size,
            'uploaded_at', emi.uploaded_at,
            'is_banner', emi.is_banner
        ))
        FROM event_media_items emi
        WHERE emi.event_id = e.id),
        '[]'::JSON
    ) AS media_items
FROM
    event_registrations er
JOIN
    events e ON er.event_id = e.id
WHERE
    er.user_id = $1
    AND (
        $2::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            ($3::TIMESTAMPTZ IS NOT NULL AND e.start_time >= $2::TIMESTAMPTZ AND e.start_time <= $3::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            ($3::TIMESTAMPTZ IS NULL AND e.start_time >= $2::TIMESTAMPTZ)
        )
    )
    AND ($4::event_status_type IS NULL OR e.status = $4::event_status_type)
    AND ($5::event_registration_role_type IS NULL OR er.registration_role = $5::event_registration_role_type)
    AND ($6::UUID IS NULL OR e.organization_id = $6::UUID)
GROUP BY
    er.id, e.id -- Group by event_registrations.id and events.id to ensure correct aggregation context for media_items
ORDER BY
    CASE WHEN $7::text = 'date_asc' THEN e.start_time END ASC,
    CASE WHEN $7::text = 'date_desc' THEN e.start_time END DESC,
    e.start_time DESC -- Default sort if no sort_order or invalid
LIMIT $9 OFFSET $8
`

type ListUserRegistrationsWithFiltersParams struct {
	UserID            uuid.UUID                     `db:"user_id" json:"user_id"`
	StartDate         *time.Time                    `db:"start_date" json:"start_date"`
	EndDate           *time.Time                    `db:"end_date" json:"end_date"`
	EventFilterStatus NullEventStatusType           `db:"event_filter_status" json:"event_filter_status"`
	Role              NullEventRegistrationRoleType `db:"role" json:"role"`
	OrganizationID    *uuid.UUID                    `db:"organization_id" json:"organization_id"`
	SortOrder         *string                       `db:"sort_order" json:"sort_order"`
	OffsetVal         int32                         `db:"offset_val" json:"offset_val"`
	LimitVal          int32                         `db:"limit_val" json:"limit_val"`
}

type ListUserRegistrationsWithFiltersRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
	EventTitle               string                      `db:"event_title" json:"event_title"`
	EventStartTime           time.Time                   `db:"event_start_time" json:"event_start_time"`
	EventEndTime             time.Time                   `db:"event_end_time" json:"event_end_time"`
	EventLocationType        EventLocationType           `db:"event_location_type" json:"event_location_type"`
	EventLocationFullAddress *string                     `db:"event_location_full_address" json:"event_location_full_address"`
	EventLocationOnlineUrl   *string                     `db:"event_location_online_url" json:"event_location_online_url"`
	EventStatus              EventStatusType             `db:"event_status" json:"event_status"`
	EventOrganizationID      uuid.UUID                   `db:"event_organization_id" json:"event_organization_id"`
	EventOrganizationName    string                      `db:"event_organization_name" json:"event_organization_name"`
	EventPrice               *string                     `db:"event_price" json:"event_price"`
	EventContactEmail        *string                     `db:"event_contact_email" json:"event_contact_email"`
	EventContactPhone        *string                     `db:"event_contact_phone" json:"event_contact_phone"`
	MediaItems               interface{}                 `db:"media_items" json:"media_items"`
}

func (q *Queries) ListUserRegistrationsWithFilters(ctx context.Context, arg ListUserRegistrationsWithFiltersParams) ([]ListUserRegistrationsWithFiltersRow, error) {
	rows, err := q.db.Query(ctx, listUserRegistrationsWithFilters,
		arg.UserID,
		arg.StartDate,
		arg.EndDate,
		arg.EventFilterStatus,
		arg.Role,
		arg.OrganizationID,
		arg.SortOrder,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListUserRegistrationsWithFiltersRow{}
	for rows.Next() {
		var i ListUserRegistrationsWithFiltersRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.Status,
			&i.PaymentStatus,
			&i.RegistrationRole,
			&i.RegisteredAt,
			&i.AttendedAt,
			&i.CancellationReasonByUser,
			&i.AdminNotesOnRegistration,
			&i.WaitlistPriority,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.EventTitle,
			&i.EventStartTime,
			&i.EventEndTime,
			&i.EventLocationType,
			&i.EventLocationFullAddress,
			&i.EventLocationOnlineUrl,
			&i.EventStatus,
			&i.EventOrganizationID,
			&i.EventOrganizationName,
			&i.EventPrice,
			&i.EventContactEmail,
			&i.EventContactPhone,
			&i.MediaItems,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateEventRegistrationStatusByAdmin = `-- name: UpdateEventRegistrationStatusByAdmin :one
UPDATE event_registrations
SET
    status = $1::event_registration_status_type,
    admin_notes_on_registration = $2,
    updated_at = $3
WHERE id = $4
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method
`

type UpdateEventRegistrationStatusByAdminParams struct {
	NewStatus      EventRegistrationStatusType `db:"new_status" json:"new_status"`
	AdminNotes     *string                     `db:"admin_notes" json:"admin_notes"`
	UpdatedAt      time.Time                   `db:"updated_at" json:"updated_at"`
	RegistrationID uuid.UUID                   `db:"registration_id" json:"registration_id"`
}

func (q *Queries) UpdateEventRegistrationStatusByAdmin(ctx context.Context, arg UpdateEventRegistrationStatusByAdminParams) (EventRegistration, error) {
	row := q.db.QueryRow(ctx, updateEventRegistrationStatusByAdmin,
		arg.NewStatus,
		arg.AdminNotes,
		arg.UpdatedAt,
		arg.RegistrationID,
	)
	var i EventRegistration
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CheckInByUserID,
		&i.CheckInMethod,
	)
	return i, err
}

const updateEventRegistrationToCheckIn = `-- name: UpdateEventRegistrationToCheckIn :one

UPDATE event_registrations
SET
    status = 'attended',      -- Referencing db.EventRegistrationStatusTypeAttended
    attended_at = NOW(),
    updated_at = NOW()
WHERE
    id = $1
    AND user_id = $2          -- Ensure we are updating for the correct user as a safeguard
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at
`

type UpdateEventRegistrationToCheckInParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	UserID uuid.UUID `db:"user_id" json:"user_id"`
}

type UpdateEventRegistrationToCheckInRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
}

// Time window: now - X minutes AND now + Y minutes
func (q *Queries) UpdateEventRegistrationToCheckIn(ctx context.Context, arg UpdateEventRegistrationToCheckInParams) (UpdateEventRegistrationToCheckInRow, error) {
	row := q.db.QueryRow(ctx, updateEventRegistrationToCheckIn, arg.ID, arg.UserID)
	var i UpdateEventRegistrationToCheckInRow
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateEventRegistrationToCheckInByScanner = `-- name: UpdateEventRegistrationToCheckInByScanner :one
UPDATE event_registrations
SET
    status = 'attended',
    attended_at = NOW(),
    check_in_by_user_id = $1,
    check_in_method = $2,
    updated_at = NOW()
WHERE
    id = $3 -- The ID of the event_registration record for the participant
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method
`

type UpdateEventRegistrationToCheckInByScannerParams struct {
	CheckInByUserID *uuid.UUID `db:"check_in_by_user_id" json:"check_in_by_user_id"`
	CheckInMethod   *string    `db:"check_in_method" json:"check_in_method"`
	RegistrationID  uuid.UUID  `db:"registration_id" json:"registration_id"`
}

func (q *Queries) UpdateEventRegistrationToCheckInByScanner(ctx context.Context, arg UpdateEventRegistrationToCheckInByScannerParams) (EventRegistration, error) {
	row := q.db.QueryRow(ctx, updateEventRegistrationToCheckInByScanner, arg.CheckInByUserID, arg.CheckInMethod, arg.RegistrationID)
	var i EventRegistration
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CheckInByUserID,
		&i.CheckInMethod,
	)
	return i, err
}
