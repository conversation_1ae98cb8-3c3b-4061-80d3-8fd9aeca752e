import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Platform, TouchableOpacity, SafeAreaView, Image } from 'react-native';
import { useTranslation } from 'react-i18next';
import Ionicons from '@expo/vector-icons/Ionicons';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Stack, useRouter } from 'expo-router';
import Constants from 'expo-constants';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { useFocusEffect } from '@react-navigation/native';

import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import { useFetchUserOrganizations, useFetchUserVolunteerQualifications } from '@/api/user_services';
import { userOrganizationsStore, userVolunteerQualificationsStore } from 'stores/user_store';
import type { OrganizationListPayload, VolunteerQualificationPayload } from '@/api/api_config';

// Define a type for volunteer status based on the API response type
type VolunteerStatusType = VolunteerQualificationPayload['status'];

// Create an enum to match the API response values plus our custom 'not_applied' value
enum VolunteerStatusEnum {
  Approved = 'approved',
  Pending = 'pending',
  Rejected = 'rejected',
  NotApplied = 'not_applied'
}

const getStatusInfo = (status: VolunteerStatusType | VolunteerStatusEnum, theme: ReturnType<typeof createTheme>) => {
  switch (status) {
    case VolunteerStatusEnum.Approved:
    case 'approved':
      return {
        icon: 'checkmark-circle-outline' as const,
        color: theme.system.success,
        textKey: 'applications.volunteer.status.approved'
      };
    case VolunteerStatusEnum.Pending:
    case 'pending':
      return {
        icon: 'time-outline' as const,
        color: theme.system.info,
        textKey: 'applications.volunteer.status.pending'
      };
    case VolunteerStatusEnum.Rejected:
    case 'rejected':
      return {
        icon: 'close-circle-outline' as const,
        color: theme.system.error,
        textKey: 'applications.volunteer.status.rejected'
      };
    case VolunteerStatusEnum.NotApplied:
      return {
        icon: 'alert-circle-outline' as const,
        color: theme.system.warning,
        textKey: 'applications.volunteer.status.notApplied'
      };
    default:
      return {
        icon: 'alert-circle-outline' as const,
        color: theme.system.error,
        textKey: 'applications.volunteer.status.error'
      };
  }
};

type IconName = keyof typeof Ionicons.glyphMap | keyof typeof MaterialCommunityIcons.glyphMap;

const ListItem = ({
  title,
  status,
  adminNotes,
  onPress,
  disabled = false,
  imageUrl,
}: {
  title: string;
  status: VolunteerStatusType | VolunteerStatusEnum;
  adminNotes?: string | null;
  onPress?: () => void;
  disabled?: boolean;
  imageUrl?: string | null;
}) => {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const statusInfo = getStatusInfo(status, theme);
  
  const itemIsDisabled = disabled || 
                         status === VolunteerStatusEnum.Approved || status === 'approved' ||
                         status === VolunteerStatusEnum.Pending || status === 'pending';

  return (
    <TouchableOpacity
      onPress={itemIsDisabled ? undefined : onPress}
      disabled={itemIsDisabled}
      style={[styles.listItem, { borderBottomColor: theme.system.border}] }
      activeOpacity={itemIsDisabled ? 1 : 0.7}
    >
      <View style={[styles.logoWrapper]}>
        {imageUrl ? (
          <Image 
            source={{ uri: imageUrl }} 
            style={styles.logo}
            resizeMode="contain"
          />
        ) : (
          <Image 
            source={require('@/assets/default-images/default-logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        )}
      </View>
      <View style={styles.listItemContent}>
        <Text style={[styles.listItemTitle, { color: theme.system.text }]}>{title}</Text>
        <View style={styles.statusContainer}>
          <Ionicons
            name={statusInfo.icon}
            size={16}
            color={statusInfo.color}
            style={styles.statusIcon}
          />
          <Text style={[styles.statusText, { color: statusInfo.color }]}>
            {t(statusInfo.textKey)}
          </Text>
          {adminNotes && (
            <Text style={[styles.adminNotes, { color: theme.system.secondaryText }]}>{adminNotes}</Text>
          )}
        </View>
      </View>
      {!itemIsDisabled && (
        <Ionicons
          name="chevron-forward"
          size={20}
          color={theme.system.border} 
        />
      )}
    </TouchableOpacity>
  );
};

export default function VolunteerStatusScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  
  const { 
    data: userOrgsData,
    refetch: refetchUserOrganizations,
    isLoading: isLoadingUserOrgs,
    error: userOrgsError 
  } = useFetchUserOrganizations();

  // Temporarily disabled due to backend API endpoint being unavailable (table removed in migration 000033)
  const { 
    data: volunteerQualificationsData, 
    refetch: refetchVolunteerQualifications,
    isLoading: isLoadingVolunteerQualifications,
    error: volunteerQualificationsError 
  } = useFetchUserVolunteerQualifications(undefined, false); // Completely disabled - API endpoint unavailable

  const storedUserOrganizations = userOrganizationsStore(state => state.userOrganizations);
  const storedVolunteerQualifications = userVolunteerQualificationsStore(state => state.volunteerQualifications);

  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  const [applicationDisplayData, setApplicationDisplayData] = useState<Array<{
    id: string;
    name: string;
    status: VolunteerStatusType;
    admin_notes?: string | null;
    image_url?: string | null;
  }>>([]);
  
  const [availableOrganizationsToApply, setAvailableOrganizationsToApply] = useState<Array<OrganizationListPayload>>([]);

  useEffect(() => {
    refetchUserOrganizations();
    // refetchVolunteerQualifications(); // Disabled - API endpoint unavailable
  }, [refetchUserOrganizations, refetchVolunteerQualifications]);

  useEffect(() => {
    if (storedUserOrganizations && storedVolunteerQualifications) {
      const qualificationsMap = new Map(storedVolunteerQualifications.map(q => [q.organization_id, q]));

      const currentApplications = storedVolunteerQualifications.map(qual => {
        const org = storedUserOrganizations.find(o => o.id === qual.organization_id);
        return {
          id: qual.organization_id,
          name: qual.organization_name || org?.name || 'Unknown Organization',
          status: qual.status as VolunteerStatusType,
          admin_notes: qual.admin_notes,
          image_url: org?.image_url,
        };
      });
      setApplicationDisplayData(currentApplications);

      const appliedOrgIds = new Set(storedVolunteerQualifications.map(app => app.organization_id));
      const orgsToApplyTo = storedUserOrganizations.filter(org => org.id && !appliedOrgIds.has(org.id));
      setAvailableOrganizationsToApply(orgsToApplyTo);
    }
  }, [storedUserOrganizations, storedVolunteerQualifications]);

  useEffect(() => {
    const err = userOrgsError || volunteerQualificationsError;
    if (err) {
      setErrorMessage(err.message || t('common.unknown'));
      setShowErrorDialog(true);
    }
  }, [userOrgsError, volunteerQualificationsError, t]);

  const handleApplicationPress = (org: OrganizationListPayload) => {
    router.push({
      pathname: '/user-profile/volunteer-application/VolunteerApplicationScreen',
      params: { 
        organizationId: org.id,
        organizationName: org.name,
        organizationImage: org.image_url || '',
        organizationDescription: org.description || ''
      }
    });
  };

  useFocusEffect(
    React.useCallback(() => {
      refetchUserOrganizations();
      refetchVolunteerQualifications();
      return () => {}; 
    }, [refetchUserOrganizations, refetchVolunteerQualifications])
  );

  const isLoading = isLoadingUserOrgs || isLoadingVolunteerQualifications;

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('applications.volunteer.status.title'),
      }} />
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ScrollView contentContainerStyle={styles.contentContainer}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.system.text }]}>
              {t('applications.volunteer.status.title')}
            </Text>
            <Text style={[styles.subtitle, { color: theme.system.secondaryText }]}>
              {t('applications.volunteer.status.subtitle')}
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.system.secondaryText }]}>
              {t('applications.volunteer.status.applications')}
            </Text>
            
            {isLoading ? (
              <Text style={[styles.loadingText, { color: theme.system.secondaryText }]}>
                {t('common.loading')}...
              </Text>
            ) : applicationDisplayData.length > 0 ? (
              applicationDisplayData.map(app => (
                <ListItem
                  key={app.id}
                  title={app.name}
                  status={app.status}
                  adminNotes={app.admin_notes}
                  imageUrl={app.image_url}
                  disabled={true} 
                />
              ))
            ) : (
              <Text style={[styles.noDataText, { color: theme.system.secondaryText }]}>
                {t('applications.volunteer.status.noApplications')}
              </Text>
            )}
          </View>
          
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.system.secondaryText }]}>
              {t('applications.volunteer.status.availableOrganizations')}
            </Text>
            
            {isLoading ? (
              <Text style={[styles.loadingText, { color: theme.system.secondaryText }]}>
                {t('common.loading')}...
              </Text>
            ) : availableOrganizationsToApply.length > 0 ? (
              availableOrganizationsToApply.map(org => (
                <ListItem
                  key={org.id}
                  title={org.name}
                  status={VolunteerStatusEnum.NotApplied}
                  imageUrl={org.image_url}
                  onPress={() => handleApplicationPress(org)}
                />
              ))
            ) : (
              <Text style={[styles.noDataText, { color: theme.system.secondaryText }]}>
                {t('applications.volunteer.status.noOrganizationsAvailable')}
              </Text>
            )}
          </View>
        </ScrollView>
        
        <CustomDialog
          visible={showErrorDialog}
          title={t('common.error')}
          message={errorMessage || t('common.unknown')}
          confirmText={t('common.ok')}
          onConfirm={() => setShowErrorDialog(false)}
          type="error"
        />
      </View>
    </>
  );
}

const STATUSBAR_HEIGHT = Platform.OS === 'android' ? Constants.statusBarHeight : 0;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 16,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  section: {
    paddingTop: 12,
    paddingBottom: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 16,
    marginBottom: 4,
    lineHeight: 20,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: 64,
    borderBottomWidth: 1,
  },
  listItemContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 20,
    marginBottom: 4,
  },
  logoWrapper: {
    width: 40,
    height: 40,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  logo: {
    width: '100%',
    height: '100%',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 14,
    lineHeight: 18,
  },
  adminNotes: {
    fontSize: 14,
    marginLeft: 8,
  },
  loadingText: {
    padding: 16,
    fontSize: 14,
    textAlign: 'center',
  },
  noDataText: {
    padding: 16,
    fontSize: 14,
    textAlign: 'center',
  }
}); 