import React, { useState, useEffect } from 'react';
import {
    Modal,
    Form,
    Input,
    Button,
    Divider,
    Row,
    Col,
    Space,
    Card,
    Popconfirm,
    Spin,
    Empty,
    message,
} from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { postService } from '../services/postService';
import { eventService } from '../services/eventService';

const TagManagerModal = ({ 
    visible, 
    onCancel, 
    onTagCreated, 
    onTagDeleted,
    getTagName,
    type = 'post' // 'post' or 'event'
}) => {
    const { t, i18n } = useTranslation();
    const [createTagForm] = Form.useForm();
    const [allTags, setAllTags] = useState([]);
    const [loadingAllTags, setLoadingAllTags] = useState(false);
    const [creatingTag, setCreatingTag] = useState(false);
    const [deletingTagId, setDeletingTagId] = useState(null);

    // Character limits (English allows more characters than Chinese)
    const EN_MAX_CHARS = 15;
    const ZH_MAX_CHARS = 8;

    // Get the appropriate service based on type
    const getService = () => {
        return type === 'event' ? eventService : postService;
    };

    // Fetch all tags when modal opens
    useEffect(() => {
        if (visible) {
            fetchAllTags();
        }
    }, [visible, type]);

    const fetchAllTags = async () => {
        setLoadingAllTags(true);
        try {
            const service = getService();
            const response = type === 'event' 
                ? await service.listEventTags()
                : await service.listPostTags();
            setAllTags(response || []);
        } catch (error) {
            console.error('Error fetching all tags:', error);
            message.error(t(`tagManager.errors.fetchTagsError`));
        } finally {
            setLoadingAllTags(false);
        }
    };

    const handleCreateTag = async (values) => {
        setCreatingTag(true);
        try {
            const service = getService();
            const response = type === 'event'
                ? await service.createEventTag({
                    name_en: values.name_en,
                    name_zh_hk: values.name_zh_hk,
                    name_zh_cn: values.name_zh_cn
                })
                : await service.createPostTag({
                    name_en: values.name_en,
                    name_zh_hk: values.name_zh_hk,
                    name_zh_cn: values.name_zh_cn
                });
            
            // Update local tags list - add new tag at the beginning
            setAllTags(prev => [response, ...prev]);
            
            // Notify parent component
            if (onTagCreated) {
                onTagCreated(response);
            }
            
            message.success(t('tagManager.messages.tagCreatedSuccess'));
            createTagForm.resetFields();
        } catch (error) {
            console.error('Error creating tag:', error);
            if (error.response?.status === 403) {
                message.error(t('tagManager.errors.tagCreatePermission'));
            } else {
                message.error(t('tagManager.errors.tagCreateError'));
            }
        } finally {
            setCreatingTag(false);
        }
    };

    const handleDeleteTag = async (tagId) => {
        setDeletingTagId(tagId);
        try {
            const service = getService();
            if (type === 'event') {
                await service.deleteEventTag(tagId);
            } else {
                await service.deletePostTag(tagId);
            }
            
            // Remove from local tags list
            setAllTags(prev => prev.filter(tag => tag.id !== tagId));
            
            // Notify parent component
            if (onTagDeleted) {
                onTagDeleted(tagId);
            }
            
            message.success(t('tagManager.messages.tagDeletedSuccess'));
        } catch (error) {
            console.error('Error deleting tag:', error);
            if (error.response?.status === 403) {
                message.error(t('tagManager.errors.tagDeletePermission'));
            } else {
                message.error(t('tagManager.errors.tagDeleteError'));
            }
        } finally {
            setDeletingTagId(null);
        }
    };

    const handleCancel = () => {
        createTagForm.resetFields();
        onCancel();
    };

    const getModalTitle = () => {
        return type === 'event'
            ? t('eventTagManager.title')
            : t('tagManager.title');
    };

    const getExistingTagsTitle = () => {
        return type === 'event'
            ? t('eventTagManager.existingTags')
            : t('tagManager.existingTags');
    };

    const getCreateNewTitle = () => {
        return type === 'event'
            ? t('eventTagManager.createNew')
            : t('tagManager.createNew');
    };

    const getNoTagsMessage = () => {
        return type === 'event'
            ? t('eventTagManager.noTags')
            : t('tagManager.noTags');
    };

    return (
        <Modal
            title={getModalTitle()}
            open={visible}
            onCancel={handleCancel}
            footer={null}
            destroyOnClose
            width={1400}
            style={{ top: 20, minHeight: '85vh' }}
        >
            <Row gutter={24} style={{ height: 650 }}>
                {/* Left: Tags List */}
                <Col span={16}>
                    <h4 style={{ marginBottom: 16, color: '#666', fontSize: '16px' }}>
                        {getExistingTagsTitle()}
                    </h4>
                    <div style={{ height: 600, overflowY: 'auto', padding: '0 8px' }}>
                        {loadingAllTags ? (
                            <div style={{ textAlign: 'center', padding: 60 }}>
                                <Spin size="large" />
                            </div>
                        ) : allTags.length === 0 ? (
                            <Empty 
                                description={getNoTagsMessage()}
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                            />
                        ) : (
                            <div className="space-y-3">
                                {allTags.map(tag => (
                                    <div key={tag.id} className="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors">
                                        <div className="flex justify-between items-start">
                                            <div className="flex-1">
                                                <div className="grid grid-cols-3 gap-4">
                                                    <div>
                                                        <span className="text-xs text-gray-500 uppercase font-medium">
                                                            {t('tagManager.english')}
                                                        </span>
                                                        <div className="text-sm text-gray-900 mt-1 font-medium">
                                                            {tag.name_en || '-'}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs text-gray-500 uppercase font-medium">
                                                            {t('tagManager.traditionalChinese')}
                                                        </span>
                                                        <div className="text-sm text-gray-900 mt-1 font-medium">
                                                            {tag.name_zh_hk || '-'}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs text-gray-500 uppercase font-medium">
                                                            {t('tagManager.simplifiedChinese')}
                                                        </span>
                                                        <div className="text-sm text-gray-900 mt-1 font-medium">
                                                            {tag.name_zh_cn || '-'}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <Popconfirm
                                                title={t('tagManager.deleteConfirmTitle')}
                                                description={t('tagManager.deleteConfirmContent')}
                                                onConfirm={() => handleDeleteTag(tag.id)}
                                                cancelText={t('common.cancel')}
                                            >
                                                <Button
                                                    type="text"
                                                    danger
                                                    size="small"
                                                    icon={<DeleteOutlined />}
                                                    loading={deletingTagId === tag.id}
                                                    style={{ marginLeft: 12 }}
                                                >
                                                    {t('tagManager.delete')}
                                                </Button>
                                            </Popconfirm>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </Col>

                {/* Divider */}
                <Col span={1}>
                    <Divider type="vertical" style={{ height: '100%', margin: 0 }} />
                </Col>

                {/* Right: Create New Tag Form */}
                <Col span={7}>
                    <h4 style={{ marginBottom: 16, color: '#666', fontSize: '16px' }}>
                        {getCreateNewTitle()}
                    </h4>
                    <Form
                        form={createTagForm}
                        layout="vertical"
                    >
                        <Form.Item
                            label={t('tagManager.form.nameEn')}
                            name="name_en"
                            rules={[
                                { required: true, message: t('tagManager.form.nameEnRequired') },
                                { max: EN_MAX_CHARS, message: `英文标签名不能超过${EN_MAX_CHARS}个字符` }
                            ]}
                        >
                            <Input 
                                placeholder={t('tagManager.form.nameEnPlaceholder')} 
                                size="large"
                                showCount
                                maxLength={EN_MAX_CHARS}
                            />
                        </Form.Item>

                        <Form.Item
                            label={t('tagManager.form.nameZhHk')}
                            name="name_zh_hk"
                            rules={[
                                { required: true, message: t('tagManager.form.nameZhHkRequired') },
                                { max: ZH_MAX_CHARS, message: `繁体中文标签名不能超过${ZH_MAX_CHARS}个字符` }
                            ]}
                        >
                            <Input 
                                placeholder={t('tagManager.form.nameZhHkPlaceholder')} 
                                size="large"
                                showCount
                                maxLength={ZH_MAX_CHARS}
                            />
                        </Form.Item>

                        <Form.Item
                            label={t('tagManager.form.nameZhCn')}
                            name="name_zh_cn"
                            rules={[
                                { required: true, message: t('tagManager.form.nameZhCnRequired') },
                                { max: ZH_MAX_CHARS, message: `简体中文标签名不能超过${ZH_MAX_CHARS}个字符` }
                            ]}
                        >
                            <Input 
                                placeholder={t('tagManager.form.nameZhCnPlaceholder')} 
                                size="large"
                                showCount
                                maxLength={ZH_MAX_CHARS}
                            />
                        </Form.Item>

                        <Form.Item style={{ marginBottom: 0, textAlign: 'right', marginTop: 24 }}>
                            <Space size="middle">
                                <Button 
                                    size="large"
                                    onClick={() => {
                                        createTagForm.resetFields();
                                    }}
                                >
                                    {t('tagManager.form.reset')}
                                </Button>
                                <Button 
                                    type="primary" 
                                    size="large"
                                    loading={creatingTag}
                                    onClick={() => {
                                        createTagForm.validateFields()
                                            .then(values => {
                                                handleCreateTag(values);
                                            })
                                            .catch(info => {
                                                console.log('Validate Failed:', info);
                                            });
                                    }}
                                >
                                    {t('tagManager.form.create')}
                                </Button>
                            </Space>
                        </Form.Item>
                    </Form>
                </Col>
            </Row>
        </Modal>
    );
};

export default TagManagerModal; 