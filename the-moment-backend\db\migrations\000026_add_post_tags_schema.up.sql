CREATE TABLE "post_tags" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "name_en" VARCHAR UNIQUE NOT NULL,
  "name_zh" VARCHAR UNIQUE NOT NULL,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT (now())
);

CREATE TABLE "post_post_tags" (
  "post_id" uuid NOT NULL,
  "tag_id" uuid NOT NULL,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  PRIMARY KEY ("post_id", "tag_id")
);

ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("post_id") REFERENCES "posts" ("id") ON DELETE CASCADE;
ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("tag_id") REFERENCES "post_tags" ("id") ON DELETE CASCADE;

-- Seed initial post tags
INSERT INTO "post_tags" (name_en, name_zh) VALUES
('Government News', '政府新聞'),
('Community Update', '社區動態'),
('Event Highlight', '活動亮點'),
('Member Story', '會員故事'),
('Official Announcement', '官方公告');
