import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import {
  fetchIntervalMs,
  ApiConfig,
  type VolunteerOrganizationQualificationsApplyRequest, // orgId in path, no body? Check API
  type VolunteerOrganizationQualificationsWithdrawRequest, // appId in path, no body?
  type EventVolunteerApplyRequest, // eventId in path, motivation in body
  type EventVolunteerWithdrawRequest, // appId in path, no body?
  type UserEventVolunteerApplicationsListPullRequest,
  type UserEventVolunteerApplicationDetailsPullRequest,
  type VolunteerOrganizationQualificationsApplyResponse,
  type VolunteerQualificationPayload,
  type EventVolunteerApplyResponse,
  type EventVolunteerApplicationPayload,
} from '@/api/api_config';
import {
  userEventVolunteerApplicationsListStore,
  userEventVolunteerApplicationDetailsStore,
  // Stores for mutation responses usually not needed, rely on cache invalidation
  // volunteerOrgQualificationStore, // This might be part of userStore or a dedicated one if details are fetched separately
} from 'stores/volunteer_store'; // Assuming you will create this
import { useEffect } from 'react';



// Apply for Volunteer Qualification in an Organization
export const useApplyVolunteerOrgQualification = () => {
  const queryClient = useQueryClient();
  return useMutation<VolunteerOrganizationQualificationsApplyResponse, Error, VolunteerOrganizationQualificationsApplyRequest & { orgId: string }>({
    mutationFn: async (payload: VolunteerOrganizationQualificationsApplyRequest & { orgId: string }): Promise<VolunteerOrganizationQualificationsApplyResponse> => {
      const { orgId, ...body } = payload; // Assuming body might be empty or contain other fields if API evolves
      try {
        const response = await axiosInstance.request<VolunteerOrganizationQualificationsApplyResponse>({
          url: ApiConfig.volunteer.volunteer_organization_qualifications_apply.endpoint.replace('{orgId}', orgId),
          method: ApiConfig.volunteer.volunteer_organization_qualifications_apply.method,
          data: body, // Send empty object or actual body if API expects one
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: VolunteerOrganizationQualificationsApplyResponse, variables) => {
      console.log(`[volunteer_services] useApplyVolunteerOrgQualification successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['userVolunteerQualifications'] }); // User's qualifications list
      // Potentially invalidate general organization volunteer list if one exists
    },
    onError: (error: Error, variables: VolunteerOrganizationQualificationsApplyRequest & { orgId: string }) => {
      console.error(`[volunteer_services] useApplyVolunteerOrgQualification error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    },
  });
};

// Withdraw Volunteer Qualification Application for an Organization
export const useWithdrawVolunteerOrgQualification = () => {
  const queryClient = useQueryClient();
  return useMutation<VolunteerQualificationPayload, Error, VolunteerOrganizationQualificationsWithdrawRequest & { appId: string }>({
    mutationFn: async (payload: VolunteerOrganizationQualificationsWithdrawRequest & { appId: string }): Promise<VolunteerQualificationPayload> => {
      const { appId, ...body } = payload;
      try {
        const response = await axiosInstance.request<VolunteerQualificationPayload>({
          url: ApiConfig.volunteer.volunteer_organization_qualifications_withdraw.endpoint.replace('{appId}', appId),
          method: ApiConfig.volunteer.volunteer_organization_qualifications_withdraw.method,
          data: body, // Send empty object or actual body if API expects one
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: VolunteerQualificationPayload, variables) => {
      console.log(`[volunteer_services] useWithdrawVolunteerOrgQualification successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['userVolunteerQualifications'] });
      // May need to invalidate a specific qualification detail if such a query exists
    },
    onError: (error: Error, variables: VolunteerOrganizationQualificationsWithdrawRequest & { appId: string }) => {
      console.error(`[volunteer_services] useWithdrawVolunteerOrgQualification error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    },
  });
};

// Apply to be a Volunteer for an Event
export const useApplyEventVolunteer = () => {
  const queryClient = useQueryClient();
  return useMutation<EventVolunteerApplyResponse, Error, EventVolunteerApplyRequest & { eventId: string }>({
    mutationFn: async (payload: EventVolunteerApplyRequest & { eventId: string }): Promise<EventVolunteerApplyResponse> => {
      const { eventId, ...body } = payload;
      try {
        const response = await axiosInstance.request<EventVolunteerApplyResponse>({
          url: ApiConfig.volunteer.event_volunteer_apply.endpoint.replace('{eventId}', eventId),
          method: ApiConfig.volunteer.event_volunteer_apply.method,
          data: body, // Contains motivation
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: EventVolunteerApplyResponse, variables) => {
      console.log(`[volunteer_services] useApplyEventVolunteer successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['userEventVolunteerApplicationsList'] });
      queryClient.invalidateQueries({ queryKey: ['eventDetails', variables.eventId] }); // Invalidate public event details for status change
    },
    onError: (error: Error, variables: EventVolunteerApplyRequest & { eventId: string }) => {
      console.error(`[volunteer_services] useApplyEventVolunteer error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    },
  });
};

// Withdraw Event Volunteer Application
export const useWithdrawEventVolunteer = () => {
  const queryClient = useQueryClient();
  return useMutation<EventVolunteerApplicationPayload, Error, EventVolunteerWithdrawRequest & { appId: string; eventId?: string }>({
    mutationFn: async (payload: EventVolunteerWithdrawRequest & { appId: string }): Promise<EventVolunteerApplicationPayload> => {
      const { appId, ...body } = payload;
      try {
        const response = await axiosInstance.request<EventVolunteerApplicationPayload>({
          url: ApiConfig.volunteer.event_volunteer_withdraw.endpoint.replace('{appId}', appId),
          method: ApiConfig.volunteer.event_volunteer_withdraw.method,
          // data: body, // PATCH, might have a body if API supports it
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: EventVolunteerApplicationPayload, variables) => {
      console.log(`[volunteer_services] useWithdrawEventVolunteer successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['userEventVolunteerApplicationsList'] });
      queryClient.invalidateQueries({ queryKey: ['userEventVolunteerApplicationDetails', variables.appId] });
      if (variables.eventId) { // If eventId is passed along, invalidate its details too
        queryClient.invalidateQueries({ queryKey: ['eventDetails', variables.eventId] });
      }
    },
    onError: (error: Error, variables: EventVolunteerWithdrawRequest & { appId: string; eventId?: string }) => {
      console.error(`[volunteer_services] useWithdrawEventVolunteer error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    },
  });
};

// Fetch User's Event Volunteer Applications List
export const useFetchUserEventVolunteerApplicationsList = (requestParams?: UserEventVolunteerApplicationsListPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['userEventVolunteerApplicationsList', requestParams] as const;
  
  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<EventVolunteerApplicationPayload[], Error, EventVolunteerApplicationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<EventVolunteerApplicationPayload[]>({
          url: ApiConfig.volunteer.user_event_volunteer_applications_list_pull.endpoint,
          method: ApiConfig.volunteer.user_event_volunteer_applications_list_pull.method,
          params: requestParams,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated, // Only fetch when user is authenticated
  });

  useEffect(() => {
    const store = userEventVolunteerApplicationsListStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userEventVolunteerApplicationsListStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setApplicationsList(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

// Fetch User's Event Volunteer Application Details
export const useFetchUserEventVolunteerApplicationDetails = (requestParams: UserEventVolunteerApplicationDetailsPullRequest) => {
  const queryClient = useQueryClient();
  const { appId } = requestParams;
  const queryKeyConst = ['userEventVolunteerApplicationDetails', appId] as const;

  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<EventVolunteerApplicationPayload, Error, EventVolunteerApplicationPayload, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      if (!appId) throw new Error('[useFetchUserEventVolunteerApplicationDetails] appId is required.');
      try {
        const response = await axiosInstance.request<EventVolunteerApplicationPayload>({
          url: ApiConfig.volunteer.user_event_volunteer_application_details_pull.endpoint.replace('{appId}', appId),
          method: ApiConfig.volunteer.user_event_volunteer_application_details_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!appId && isAuthenticated, // Only fetch when appId exists and user is authenticated
  });

  useEffect(() => {
    const store = userEventVolunteerApplicationDetailsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userEventVolunteerApplicationDetailsStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setApplicationDetails(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, appId, queryClient, requestParams]);

  return queryResult;
};
