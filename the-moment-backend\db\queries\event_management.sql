-- name: CreateEvent :one
INSERT INTO events (
    organization_id, title, description_content, location_type, 
    location_full_address,
    location_online_url, start_time, end_time, status,
    participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at,
    price, contact_email, contact_phone, government_funding_keys
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
    $16, $17, $18
) RETURNING *;

-- name: GetEventByID :one
SELECT * FROM events WHERE id = $1 LIMIT 1;

-- name: GetEventWithOrganizationByID :one
SELECT e.*, o.name as organization_name
FROM events e
JOIN organizations o ON e.organization_id = o.id
WHERE e.id = $1;

-- name: ListEventsByOrganization :many
SELECT * FROM events e
WHERE e.organization_id = $1
    AND (sqlc.narg(statuses)::text[] IS NULL OR e.status::text = ANY(sqlc.narg(statuses)::text[]))
    AND (
        sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NOT NULL AND e.end_time >= sqlc.narg(start_date)::TIMESTAMPTZ AND e.end_time <= sqlc.narg(end_date)::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL AND e.end_time >= sqlc.narg(start_date)::TIMESTAMPTZ)
        )
    )
    AND (sqlc.narg(search_term)::text IS NULL OR (
        e.title ILIKE '%' || sqlc.narg(search_term)::text || '%'
        OR e.location_full_address ILIKE '%' || sqlc.narg(search_term)::text || '%'
    ))
    AND (sqlc.narg(government_funding_keys)::text[] IS NULL OR e.government_funding_keys && sqlc.narg(government_funding_keys)::text[])
    AND (
        sqlc.narg(event_verification_type_keys)::text[] IS NULL
        OR array_length(sqlc.narg(event_verification_type_keys)::text[], 1) IS NULL
        OR EXISTS (
            SELECT 1
            FROM event_required_verification_types ervt
            WHERE ervt.event_id = e.id
            AND ervt.verification_type_key = ANY(sqlc.narg(event_verification_type_keys)::text[])
        )
    )
    AND (
        sqlc.narg(tag_ids)::uuid[] IS NULL
        OR array_length(sqlc.narg(tag_ids)::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY(sqlc.narg(tag_ids)::uuid[])
        )
    )
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3;

-- name: CountEventsByOrganization :one
SELECT COUNT(*) FROM events e
WHERE e.organization_id = $1
    AND (sqlc.narg(statuses)::text[] IS NULL OR e.status::text = ANY(sqlc.narg(statuses)::text[]))
    AND e.end_time > COALESCE(sqlc.narg(start_date)::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE(sqlc.narg(end_date)::TIMESTAMPTZ, 'infinity')
    AND (sqlc.narg(search_term)::text IS NULL OR (
        e.title ILIKE '%' || sqlc.narg(search_term)::text || '%'
        OR e.location_full_address ILIKE '%' || sqlc.narg(search_term)::text || '%'
    ))
    AND (sqlc.narg(government_funding_keys)::text[] IS NULL OR e.government_funding_keys && sqlc.narg(government_funding_keys)::text[])
    AND (
        sqlc.narg(event_verification_type_keys)::text[] IS NULL
        OR array_length(sqlc.narg(event_verification_type_keys)::text[], 1) IS NULL
        OR EXISTS (
            SELECT 1
            FROM event_required_verification_types ervt
            WHERE ervt.event_id = e.id
            AND ervt.verification_type_key = ANY(sqlc.narg(event_verification_type_keys)::text[])
        )
    )
    AND (
        sqlc.narg(tag_ids)::uuid[] IS NULL
        OR array_length(sqlc.narg(tag_ids)::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY(sqlc.narg(tag_ids)::uuid[])
        )
    );

-- name: ListPublicEvents :many
SELECT *
FROM events e
WHERE
    e.status::text = ANY(sqlc.arg(statuses)::text[]) -- Cast e.status to text
    AND e.end_time > COALESCE(sqlc.narg(start_date)::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE(sqlc.narg(end_date)::TIMESTAMPTZ, 'infinity')
    AND (sqlc.narg(search_term)::text IS NULL OR (
        e.title ILIKE '%' || sqlc.narg(search_term)::text || '%'
        OR e.location_full_address ILIKE '%' || sqlc.narg(search_term)::text || '%'
    ))
    AND (array_length(sqlc.arg(government_funding_keys)::text[], 1) IS NULL OR e.government_funding_keys && sqlc.arg(government_funding_keys)::text[])
    AND (
        (sqlc.narg(organization_id)::uuid IS NULL AND sqlc.narg(organization_id2)::uuid IS NULL) OR
        (sqlc.narg(organization_id)::uuid IS NOT NULL AND e.organization_id = sqlc.narg(organization_id)::uuid) OR
        (sqlc.narg(organization_id2)::uuid IS NOT NULL AND e.organization_id = sqlc.narg(organization_id2)::uuid)
    )
    AND ( -- Tag filter logic
        sqlc.narg(tag_ids)::uuid[] IS NULL
        OR array_length(sqlc.narg(tag_ids)::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY(sqlc.narg(tag_ids)::uuid[])
        )
    )
    AND (sqlc.narg(event_verification_type_key)::text IS NULL OR EXISTS (
        SELECT 1
        FROM event_required_verification_types ervt
        WHERE ervt.event_id = e.id
        AND ervt.verification_type_key = sqlc.narg(event_verification_type_key)::text
    ))
ORDER BY e.start_time ASC
LIMIT $1 OFFSET $2;

-- name: CountPublicEvents :one
SELECT COUNT(*)
FROM events e
WHERE
    e.status::text = ANY(sqlc.arg(statuses)::text[]) -- Cast e.status to text
    AND e.end_time > COALESCE(sqlc.narg(start_date)::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE(sqlc.narg(end_date)::TIMESTAMPTZ, 'infinity')
    AND (sqlc.narg(search_term)::text IS NULL OR (
        e.title ILIKE ('%' || sqlc.narg(search_term)::text || '%')
        OR e.location_full_address ILIKE ('%' || sqlc.narg(search_term)::text || '%')
    ))
    AND ( -- Alternative tag filter logic
        sqlc.narg(tag_ids)::uuid[] IS NULL
        OR array_length(sqlc.narg(tag_ids)::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY(sqlc.narg(tag_ids)::uuid[])
        )
    )
    AND (array_length(sqlc.arg(government_funding_keys)::text[], 1) IS NULL OR e.government_funding_keys && sqlc.arg(government_funding_keys)::text[])
    AND (sqlc.narg(event_verification_type_key)::text IS NULL OR EXISTS (
        SELECT 1
        FROM event_required_verification_types ervt
        WHERE ervt.event_id = e.id
        AND ervt.verification_type_key = sqlc.narg(event_verification_type_key)::text
    ))
    AND (
        (sqlc.narg(organization_id)::uuid IS NULL AND sqlc.narg(organization_id2)::uuid IS NULL) OR
        (sqlc.narg(organization_id)::uuid IS NOT NULL AND e.organization_id = sqlc.narg(organization_id)::uuid) OR
        (sqlc.narg(organization_id2)::uuid IS NOT NULL AND e.organization_id = sqlc.narg(organization_id2)::uuid)
    )
;

-- name: CountPublicEventsByOrganization :one
SELECT COUNT(*)
FROM events e
WHERE
    e.organization_id = $1
    AND e.status::text = ANY(sqlc.arg(statuses)::text[]) -- Cast e.status to text
    AND e.end_time > COALESCE(sqlc.narg(start_date)::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE(sqlc.narg(end_date)::TIMESTAMPTZ, 'infinity')
    AND (sqlc.narg(search_term)::text IS NULL OR (
        e.title ILIKE '%' || sqlc.narg(search_term)::text || '%'
        OR e.location_full_address ILIKE '%' || sqlc.narg(search_term)::text || '%'
    ))
    AND (
        COALESCE(array_length(sqlc.narg(tag_ids)::uuid[], 1), 0) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY(sqlc.narg(tag_ids)::uuid[])
        )
    )
    AND (array_length(sqlc.arg(government_funding_keys)::text[], 1) IS NULL OR e.government_funding_keys && sqlc.arg(government_funding_keys)::text[])
;

-- name: ListPublicEventsByOrganization :many
SELECT sqlc.embed(e) -- Add other required fields if needed (org name, tags etc) via JOINs
FROM events e
-- LEFT JOIN event_event_tags eet ON e.id = eet.event_id -- Add joins if tag filtering etc. is needed
-- LEFT JOIN event_tags et ON eet.tag_id = et.id
WHERE
    e.organization_id = $1
    AND e.status::text = ANY(sqlc.arg(statuses)::text[]) -- Cast e.status to text
    AND e.end_time > COALESCE(sqlc.narg(start_date)::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE(sqlc.narg(end_date)::TIMESTAMPTZ, 'infinity')
    AND (sqlc.narg(search_term)::text IS NULL OR (
        e.title ILIKE '%' || sqlc.narg(search_term)::text || '%'
        OR e.location_full_address ILIKE '%' || sqlc.narg(search_term)::text || '%'
    ))
    AND ( -- Alternative tag filter logic
        sqlc.narg(tag_ids)::uuid[] IS NULL
        OR array_length(sqlc.narg(tag_ids)::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY(sqlc.narg(tag_ids)::uuid[])
        )
    )
    AND (array_length(sqlc.arg(government_funding_keys)::text[], 1) IS NULL OR e.government_funding_keys && sqlc.arg(government_funding_keys)::text[])
GROUP BY e.id
ORDER BY e.start_time ASC
LIMIT $2 OFFSET $3;

-- name: UpdateEventDetails :one
UPDATE events
SET 
    title = COALESCE(sqlc.narg(title), title),
    description_content = COALESCE(sqlc.narg(description_content), description_content),
    location_type = COALESCE(sqlc.narg(location_type), location_type),
    location_full_address = COALESCE(sqlc.narg(location_full_address), location_full_address),
    location_online_url = COALESCE(sqlc.narg(location_online_url), location_online_url),
    start_time = COALESCE(sqlc.narg(start_time), start_time),
    end_time = COALESCE(sqlc.narg(end_time), end_time),
    price = COALESCE(sqlc.narg(price), price),
    contact_email = COALESCE(sqlc.narg(contact_email), contact_email),
    contact_phone = COALESCE(sqlc.narg(contact_phone), contact_phone),
    participant_limit = COALESCE(sqlc.narg(participant_limit), participant_limit),
    waitlist_limit = COALESCE(sqlc.narg(waitlist_limit), waitlist_limit),
    requires_approval_for_registration = COALESCE(sqlc.narg(requires_approval_for_registration), requires_approval_for_registration),
    status = COALESCE(sqlc.narg(status), status),
    published_at = COALESCE(sqlc.narg(published_at), published_at),
    government_funding_keys = COALESCE(sqlc.narg(government_funding_keys), government_funding_keys),
    updated_at = NOW()
WHERE id = sqlc.arg(id)
RETURNING *;

-- name: UpdateEventStatus :one
UPDATE events
SET status = $2,
    published_at = CASE 
                       WHEN $2 = 'published'::event_status_type AND published_at IS NULL THEN CURRENT_TIMESTAMP 
                       ELSE published_at 
                   END
WHERE id = $1
RETURNING *;

-- name: DeleteEvent :exec
DELETE FROM events WHERE id = $1;

-- name: CreateEventMediaItem :one
INSERT INTO event_media_items (
    event_id, file_name, file_path, file_type, file_size, is_banner
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING *;

-- name: ListEventMediaItems :many
SELECT * FROM event_media_items
WHERE event_id = $1
ORDER BY is_banner DESC, uploaded_at ASC;

-- name: GetEventMediaItemByID :one
SELECT * FROM event_media_items
WHERE id = $1;

-- name: DeleteEventMediaItem :exec
DELETE FROM event_media_items WHERE id = $1;

-- name: DeleteMediaItemsForEvent :exec
DELETE FROM event_media_items WHERE event_id = $1;

-- name: UnsetBannerForEventMediaItems :exec
UPDATE event_media_items
SET is_banner = FALSE
WHERE event_id = $1 AND is_banner = TRUE;

-- name: SetBannerForEventMediaItem :one
UPDATE event_media_items
SET is_banner = TRUE
WHERE id = $1 AND event_id = $2
RETURNING *;

-- name: ListMediaItemsByEventIDs :many
SELECT * FROM event_media_items
WHERE event_id = ANY(sqlc.arg(event_ids)::UUID[])
ORDER BY event_id, is_banner DESC, uploaded_at ASC;

-- name: CreateEventTag :one
INSERT INTO event_tags (
    name_en, name_zh_hk, name_zh_cn, is_globally_approved, created_by_user_id
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING *;

-- name: UpdateEventTag :one
UPDATE event_tags
SET
    name_en = COALESCE(sqlc.narg('name_en'), name_en),
    name_zh_hk = COALESCE(sqlc.narg('name_zh_hk'), name_zh_hk),
    name_zh_cn = COALESCE(sqlc.narg('name_zh_cn'), name_zh_cn),
    is_globally_approved = COALESCE(sqlc.narg('is_globally_approved'), is_globally_approved)
WHERE id = @id
RETURNING *;

-- name: ListEventTags :many
SELECT * FROM event_tags ORDER BY name_en;

-- name: ListGloballyApprovedEventTags :many
SELECT * FROM event_tags
WHERE is_globally_approved = TRUE
ORDER BY name_en;

-- name: GetEventTag :one
SELECT * FROM event_tags
WHERE id = @id;

-- name: DeleteEventTag :exec
DELETE FROM event_tags
WHERE id = @id;

-- name: AddTagToEvent :exec
INSERT INTO event_event_tags (
    event_id,
    event_tag_id
) VALUES (
    $1, $2
) ON CONFLICT (event_id, event_tag_id) DO NOTHING;

-- name: ListTagsForEvent :many
SELECT et.*
FROM event_tags et
JOIN event_event_tags eet ON et.id = eet.event_tag_id
WHERE eet.event_id = $1
ORDER BY et.name_en;

-- name: RemoveTagFromEvent :exec
DELETE FROM event_event_tags WHERE event_id = $1 AND event_tag_id = $2;

-- name: RemoveAllTagsFromEvent :exec
DELETE FROM event_event_tags WHERE event_id = $1;

-- name: GetEventsForTag :many
SELECT e.*
FROM events e
JOIN event_event_tags eet ON e.id = eet.event_id
WHERE eet.event_tag_id = $1
ORDER BY e.start_time DESC;

-- name: AddEventRequiredVerificationType :exec
INSERT INTO event_required_verification_types (
    event_id, verification_type_key
) VALUES (
    $1, $2
) ON CONFLICT (event_id, verification_type_key) DO NOTHING;

-- name: RemoveEventRequiredVerificationType :exec
DELETE FROM event_required_verification_types
WHERE event_id = $1 AND verification_type_key = $2;

-- name: ListRequiredVerificationTypesForEvent :many
SELECT verification_type_key 
FROM event_required_verification_types
WHERE event_id = $1;

-- name: RemoveAllRequiredVerificationsFromEvent :exec
DELETE FROM event_required_verification_types WHERE event_id = $1;

-- name: ListTagsByEventIDs :many
SELECT et.id, et.name_en, et.name_zh_hk, et.name_zh_cn, et.description_en, et.description_zh_hk, et.description_zh_cn, et.is_globally_approved, eet.event_id
FROM event_tags et
JOIN event_event_tags eet ON et.id = eet.event_tag_id
WHERE eet.event_id = ANY(sqlc.arg(event_ids)::uuid[]);

-- name: CreateEventRegistration :one
INSERT INTO event_registrations (
    event_id, user_id, status, payment_status, registration_role, waitlist_priority
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at;

-- name: GetEventRegistrationByUserAndEvent :one
SELECT id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at FROM event_registrations
WHERE user_id = $1 AND event_id = $2;

-- name: ListRegistrationsForEvent :many
SELECT r.id, r.event_id, r.user_id, r.status, r.payment_status, r.registration_role, r.registered_at, r.attended_at, r.cancellation_reason_by_user, r.admin_notes_on_registration, r.waitlist_priority, r.created_at, r.updated_at, u.display_name as user_display_name, u.email as user_email, u.phone as user_phone
FROM event_registrations r
JOIN users u ON r.user_id = u.id
WHERE r.event_id = $1
ORDER BY r.registered_at ASC
LIMIT $2 OFFSET $3;

-- name: ListRegistrationsForUser :many
SELECT r.id, r.event_id, r.user_id, r.status, r.payment_status, r.registration_role, r.registered_at, r.attended_at, r.cancellation_reason_by_user, r.admin_notes_on_registration, r.waitlist_priority, r.created_at, r.updated_at, e.title as event_title, e.start_time as event_start_time, e.status as event_status
FROM event_registrations r
JOIN events e ON r.event_id = e.id
WHERE r.user_id = $1
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3;

-- name: UpdateEventRegistrationStatus :one
UPDATE event_registrations
SET status = $2,
    admin_notes_on_registration = COALESCE($3, admin_notes_on_registration),
    waitlist_priority = CASE 
        WHEN $2 = 'waitlisted' THEN $4 -- Use provided priority for waitlisted
        ELSE waitlist_priority -- Keep existing priority otherwise
    END,
    updated_at = NOW()
WHERE id = $1
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method;

-- name: UpdateEventRegistrationPaymentStatus :one
UPDATE event_registrations
SET payment_status = $2
WHERE id = $1
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at;

-- name: CancelEventRegistrationByUser :one
UPDATE event_registrations
SET status = 'cancelled_by_user',
    cancellation_reason_by_user = $2
WHERE id = $1 AND user_id = $3 -- Ensure user can only cancel their own
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method;

-- name: GetOldestWaitlistedRegistration :one
SELECT id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at FROM event_registrations
WHERE event_id = $1 AND status = 'waitlisted'
ORDER BY waitlist_priority ASC, registered_at ASC
LIMIT 1
FOR UPDATE; -- For atomicity if promoting

-- name: GetRegisteredAndApprovedCountForEvent :one
SELECT COUNT(*) FROM event_registrations
WHERE event_id = $1 AND (status = 'registered' OR status = 'pending_approval');

-- name: GetRegisteredCountForEvent :one
SELECT COUNT(*) FROM event_registrations
WHERE event_id = $1 AND status IN ('registered', 'pending_approval'); -- Removed 'confirmed'

-- name: GetWaitlistedCountForEvent :one
SELECT COUNT(*) FROM event_registrations
WHERE event_id = $1 AND status = 'waitlisted';

-- name: CreateEventVolunteerApplication :one
INSERT INTO event_volunteer_applications (
    event_id, user_id, organization_id, status, application_notes_by_user
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING *;

-- name: GetEventVolunteerApplicationByID :one
SELECT * FROM event_volunteer_applications
WHERE id = $1;

-- name: GetEventVolunteerApplicationByUserAndEvent :one
SELECT * FROM event_volunteer_applications
WHERE user_id = $1 AND event_id = $2;

-- name: ListEventVolunteerApplicationsForEvent :many
SELECT va.*, u.display_name as user_display_name, u.email as user_email, u.phone as user_phone
FROM event_volunteer_applications va
JOIN users u ON va.user_id = u.id
WHERE va.event_id = $1
ORDER BY va.applied_at ASC
LIMIT $2 OFFSET $3;

-- name: ListEventVolunteerApplicationsForUser :many
SELECT
    va.*,
    e.title as event_title,
    e.start_time as event_start_time,
    e.end_time as event_end_time,
    e.description_content as event_description,
    e.location_type as event_location_type,
    e.location_full_address as event_location_full_address,
    e.location_online_url as event_location_online_url,
    e.status as event_status,
    e.organization_id as event_organization_id, -- This is the event's direct organization ID
    o.name as organization_name, -- Fetched via join for consistency
    e.price as event_price,
    e.contact_email as event_contact_email,
    e.contact_phone as event_contact_phone,
    COALESCE(
        (SELECT JSON_AGG(JSON_BUILD_OBJECT(
            'id', emi.id,
            'file_name', emi.file_name,
            'file_path', emi.file_path,
            'file_type', emi.file_type,
            'file_size', emi.file_size,
            'uploaded_at', emi.uploaded_at,
            'is_banner', emi.is_banner
        ))
        FROM event_media_items emi
        WHERE emi.event_id = e.id),
        '[]'::JSON
    ) AS media_items
FROM event_volunteer_applications va
JOIN events e ON va.event_id = e.id
LEFT JOIN organizations o ON e.organization_id = o.id -- Join to get organization name from event's perspective
WHERE va.user_id = $1
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3;

-- name: UpdateEventVolunteerApplicationStatus :one
UPDATE event_volunteer_applications
SET status = $2,
    admin_review_notes = $3,
    reviewed_at = CURRENT_TIMESTAMP,
    reviewed_by_user_id = $4
WHERE id = $1
RETURNING *;

-- name: GetUserEventStatistics :many
SELECT 
    e.id as event_id,
    e.title as event_title,
    e.start_time as event_start_time,
    e.status as event_status,
    er.status as registration_status,
    er.payment_status as registration_payment_status,
    er.attended_at IS NOT NULL as attended
FROM events e
JOIN event_registrations er ON e.id = er.event_id
WHERE er.user_id = $1
  AND e.status = 'past'
  AND e.end_time > COALESCE(sqlc.narg(start_date_filter)::TIMESTAMPTZ, '-infinity')
  AND e.start_time < COALESCE(sqlc.narg(end_date_filter)::TIMESTAMPTZ, 'infinity')
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3;

-- name: GetOrganizationEventStatistics :many
SELECT 
    e.id as event_id,
    e.title as event_title,
    e.start_time as event_start_time,
    e.status as event_event_status, -- aliased to avoid conflict with registration status
    (SELECT COUNT(*) FROM event_registrations er_total WHERE er_total.event_id = e.id AND (er_total.status='registered' OR er_total.status='attended' OR er_total.status='absent')) as total_registered_or_finalized,
    (SELECT COUNT(*) FROM event_registrations er_attended WHERE er_attended.event_id = e.id AND er_attended.status = 'attended') as total_attended,
    (SELECT COUNT(*) FROM event_registrations er_paid WHERE er_paid.event_id = e.id AND er_paid.payment_status = 'paid') as total_paid_registrations
FROM events e
WHERE e.organization_id = $1
  AND e.end_time < NOW()
  AND e.end_time > COALESCE(sqlc.narg(start_date_filter)::TIMESTAMPTZ, '-infinity')
  AND e.start_time < COALESCE(sqlc.narg(end_date_filter)::TIMESTAMPTZ, 'infinity')
  -- TODO: Add event type/tag filter by joining with event_event_tags and event_tags
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3;

-- name: GetEventDetailsWithCounts :one
SELECT 
    e.*,
    o.name as organization_name,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'registered') as registered_count,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'waitlisted') as waitlisted_count,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'attended') as attended_count
FROM events e
JOIN organizations o ON e.organization_id = o.id
WHERE e.id = $1;

-- name: ListUpcomingEvents :many
SELECT
    id, title, start_time
FROM events
WHERE
    status = 'published' 
    AND start_time > NOW()
ORDER BY start_time ASC
LIMIT $1;

-- name: ListUpcomingEventsByOrganization :many
SELECT
    id, title, start_time
FROM events
WHERE
    organization_id = $1
    AND status = 'published' 
    AND start_time > NOW()
ORDER BY start_time ASC
LIMIT $2;

-- name: ListPopularEvents :many
SELECT 
    e.id, 
    e.title, 
    e.start_time, 
    e.location_full_address,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status IN ('registered', 'pending_approval')) as registered_count
FROM events e
WHERE
    e.status = 'published' -- Use 'published' status based on enum
ORDER BY registered_count DESC, start_time ASC -- Sort by popularity, then time
LIMIT $1;

-- name: ListPopularEventsByOrganization :many
SELECT 
    e.id, 
    e.title, 
    e.start_time, 
    e.location_full_address,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status IN ('registered', 'pending_approval')) as registered_count
FROM events e
WHERE
    e.organization_id = $1
    AND e.status = 'published' -- Use 'published' status based on enum
ORDER BY registered_count DESC, start_time ASC -- Sort by popularity, then time
LIMIT $2;

-- Query to get Event's Organization ID --

-- name: GetEventOrganizationID :one
SELECT organization_id FROM events
WHERE id = $1;

-- name: ListPendingReviewEventVolunteerApplicationsForEvent :many
SELECT
    eva.*,
    u.display_name AS applicant_display_name,
    u.email AS applicant_email,
    u.phone AS applicant_phone,
    e.title AS event_title,
    o.name AS organization_name
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
JOIN events e ON eva.event_id = e.id
JOIN organizations o ON eva.organization_id = o.id
WHERE eva.event_id = $1 AND eva.status = 'pending'
ORDER BY eva.applied_at ASC
LIMIT $2 OFFSET $3;

-- name: CountPendingReviewEventVolunteerApplicationsForEvent :one
SELECT COUNT(*)
FROM event_volunteer_applications eva
WHERE eva.event_id = $1 AND eva.status = 'pending';

-- name: ListPendingReviewEventVolunteerApplicationsForOrganization :many
SELECT
    eva.*,
    u.display_name AS applicant_display_name,
    u.email AS applicant_email,
    u.phone AS applicant_phone,
    e.title AS event_title,
    o.name AS organization_name
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
JOIN events e ON eva.event_id = e.id
JOIN organizations o ON eva.organization_id = o.id
WHERE eva.organization_id = $1 AND eva.status = 'pending'
ORDER BY e.start_time ASC, eva.applied_at ASC
LIMIT $2 OFFSET $3;

-- name: CountPendingReviewEventVolunteerApplicationsForOrganization :one
SELECT COUNT(*)
FROM event_volunteer_applications eva
WHERE eva.organization_id = $1 AND eva.status = 'pending';

-- name: ListAllPendingReviewEventVolunteerApplications :many
SELECT
    eva.*,
    u.display_name AS applicant_display_name,
    u.email AS applicant_email,
    u.phone AS applicant_phone,
    e.title AS event_title,
    o.name AS organization_name
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
JOIN events e ON eva.event_id = e.id
JOIN organizations o ON eva.organization_id = o.id
WHERE eva.status = 'pending'
ORDER BY o.name ASC, e.start_time ASC, eva.applied_at ASC
LIMIT $1 OFFSET $2;

-- name: CountAllPendingReviewEventVolunteerApplications :one
SELECT COUNT(*)
FROM event_volunteer_applications eva
WHERE eva.status = 'pending';

-- name: ListOrgEventVolunteerApplicationsWithFilters :many
SELECT
    eva.*,
    u.display_name AS applicant_display_name,
    u.email AS applicant_email,
    u.phone AS applicant_phone_number,
    e.title AS event_title,
    o.name AS organization_name,
    reviewer.display_name as reviewer_display_name
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
JOIN events e ON eva.event_id = e.id
JOIN organizations o ON eva.organization_id = o.id
LEFT JOIN users reviewer ON eva.reviewed_by_user_id = reviewer.id
WHERE eva.organization_id = $1
AND eva.status = COALESCE(sqlc.narg('status')::application_status_enum, eva.status)
ORDER BY e.start_time DESC, eva.applied_at DESC
LIMIT $2 OFFSET $3;

-- name: CountOrgEventVolunteerApplicationsWithFilters :one
SELECT COUNT(*)
FROM event_volunteer_applications eva
WHERE eva.organization_id = $1
AND eva.status = COALESCE(sqlc.narg('status')::application_status_enum, eva.status);

-- name: GetVolunteerApplicationsForEvent :many
SELECT sqlc.embed(eva), sqlc.embed(u)
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
WHERE eva.event_id = $1
ORDER BY eva.applied_at ASC
LIMIT $2 OFFSET $3;

-- name: CountVolunteerApplicationsForEvent :one
SELECT COUNT(*) 
FROM event_volunteer_applications
WHERE event_id = $1;

-- name: GetScheduledEventsToPublish :many
SELECT id FROM events WHERE status = 'draft' AND published_at IS NOT NULL AND published_at <= NOW();

-- name: SetEventStatusToPublished :exec
UPDATE events SET status = 'published', updated_at = NOW() WHERE id = $1;

-- name: GetVolunteerApplicationByUserAndEventAndOrg :one
SELECT eva.*
FROM event_volunteer_applications eva
WHERE eva.user_id = $1 
  AND eva.event_id = $2
  AND eva.organization_id = $3;

-- END: Additional Event Volunteer Application Queries 

-- name: GetEventWithConfigurationForRegistration :one
SELECT 
    e.id as event_id,
    e.title as event_title,
    e.start_time as event_start_time,
    e.end_time as event_end_time,
    e.status as event_status,
    e.participant_limit as event_participant_limit,
    e.waitlist_limit as event_waitlist_limit,
    e.requires_approval_for_registration as event_requires_approval,
    e.published_at as event_published_at,
    e.government_funding_keys as event_government_funding_keys,
    o.id as organization_id,
    o.name as organization_name,
    o.image_url as organization_logo_url,
    o.theme_color as organization_theme_color
FROM
    events e
JOIN
    organizations o ON e.organization_id = o.id
WHERE
    e.id = $1;

-- name: ListPublicEventsWithCounts :many
SELECT
    e.id,
    e.organization_id,
    e.title,
    e.description_content, -- Map to JsonContent in payload
    e.location_type,
    e.location_full_address,
    e.location_online_url,
    e.start_time,
    e.end_time,
    e.price,
    e.government_funding_keys,
    e.status,
    e.participant_limit,
    e.waitlist_limit, -- Already in events table
    e.requires_approval_for_registration,
    e.created_by_user_id,
    e.published_at,
    e.contact_email,
    e.contact_phone,
    e.is_government_funded, -- Keep if needed, or remove if not in PublicEventResponse
    o.name AS organization_name,
    COALESCE(counts.registered_count, 0) AS registered_count,
    COALESCE(counts.waitlisted_count, 0) AS waitlisted_count,
    COALESCE(counts.attended_count, 0) AS attended_count
FROM events e
JOIN organizations o ON e.organization_id = o.id
LEFT JOIN (
    SELECT
        event_id,
        COUNT(CASE WHEN status = 'registered' THEN 1 END) AS registered_count,
        COUNT(CASE WHEN status = 'waitlisted' THEN 1 END) AS waitlisted_count,
        COUNT(CASE WHEN status = 'attended' THEN 1 END) AS attended_count
    FROM event_registrations
    GROUP BY event_id
) counts ON e.id = counts.event_id
WHERE
    e.status::text = ANY(sqlc.arg(statuses)::text[])
    AND e.end_time > COALESCE(sqlc.narg(start_date)::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE(sqlc.narg(end_date)::TIMESTAMPTZ, 'infinity')
    AND (sqlc.narg(search_term)::text IS NULL OR (
        e.title ILIKE '%' || sqlc.narg(search_term)::text || '%'
        OR e.location_full_address ILIKE '%' || sqlc.narg(search_term)::text || '%'
    ))
    AND ( -- Alternative tag filter logic
        sqlc.narg(tag_ids)::uuid[] IS NULL
        OR array_length(sqlc.narg(tag_ids)::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY(sqlc.narg(tag_ids)::uuid[])
        )
    )
    AND (array_length(sqlc.arg(government_funding_keys)::text[], 1) IS NULL OR e.government_funding_keys && sqlc.arg(government_funding_keys)::text[])
    AND (sqlc.narg(event_verification_type_key)::text IS NULL OR EXISTS (
        SELECT 1
        FROM event_required_verification_types ervt
        WHERE ervt.event_id = e.id
        AND ervt.verification_type_key = sqlc.narg(event_verification_type_key)::text
    ))
    AND (
        (sqlc.narg(organization_id)::uuid IS NULL AND sqlc.narg(organization_id2)::uuid IS NULL) OR
        (sqlc.narg(organization_id)::uuid IS NOT NULL AND e.organization_id = sqlc.narg(organization_id)::uuid) OR
        (sqlc.narg(organization_id2)::uuid IS NOT NULL AND e.organization_id = sqlc.narg(organization_id2)::uuid)
    )
ORDER BY e.start_time ASC -- Or based on parameters
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetUpcomingEventsForUserReminders :many
SELECT
    id, title, start_time
FROM events
WHERE
    status = 'published' 
    AND start_time > NOW()
ORDER BY start_time ASC
LIMIT $1;

-- name: SetEventVolunteerApplicationStatusToWithdrawnByUser :one
UPDATE event_volunteer_applications
SET
    status = 'withdrawn',
    updated_at = NOW(),
    -- Clear review fields if necessary
    reviewed_by_user_id = NULL,
    reviewed_at = NULL,
    admin_review_notes = NULL
WHERE id = $1 AND user_id = $2 AND status IN ('pending', 'approved') -- Define withdrawable states
RETURNING *;

-- name: ListUserRegistrationsForEvents :many
SELECT
    er.event_id,
    er.id AS registration_id,
    er.status AS registration_status,
    eva.id AS volunteer_application_id,
    eva.status AS volunteer_status
FROM events e
LEFT JOIN event_registrations er ON e.id = er.event_id AND er.user_id = sqlc.arg(user_id)
LEFT JOIN event_volunteer_applications eva ON e.id = eva.event_id AND eva.user_id = sqlc.arg(user_id)
WHERE e.id = ANY(sqlc.arg(event_ids)::uuid[]);

-- name: DeleteEventMediaItemsByEventID :exec
DELETE FROM event_media_items
WHERE event_id = $1;

-- name: GetEventRegistrationsForUserByEventIDs :many
SELECT er.*, e.title AS event_title
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
AND er.event_id = ANY(@event_ids::UUID[])
ORDER BY e.start_time DESC;

-- name: ReactivateEventVolunteerApplication :one
UPDATE event_volunteer_applications
SET
    status = 'pending',
    application_notes_by_user = $3,
    admin_review_notes = NULL,
    reviewed_by_user_id = NULL,
    updated_at = NOW()
WHERE event_id = $1 AND user_id = $2
RETURNING *;

-- name: GetUserEventVolunteerApplicationByID :one
SELECT
    eva.id, eva.event_id, eva.user_id, eva.organization_id, eva.application_notes_by_user, eva.admin_review_notes, eva.applied_at, eva.reviewed_at, eva.reviewed_by_user_id, eva.created_at, eva.updated_at, eva.status, eva.attended_at,
    e.title as event_title,
    e.start_time as event_start_time,
    e.end_time as event_end_time,
    e.description_content as event_description,
    e.location_type as event_location_type,
    e.location_full_address as event_location_full_address,
    e.location_online_url as event_location_online_url,
    e.status as event_status,
    e.organization_id as event_organization_id,
    o.name as organization_name,
    o.image_url as organization_logo_url,
    e.price as event_price,
    e.contact_email as event_contact_email,
    e.contact_phone as event_contact_phone,
    COALESCE(
        (SELECT JSON_AGG(JSON_BUILD_OBJECT(
            'id', emi.id,
            'file_name', emi.file_name,
            'file_path', emi.file_path,
            'file_type', emi.file_type,
            'file_size', emi.file_size,
            'uploaded_at', emi.uploaded_at,
            'is_banner', emi.is_banner
        ))
        FROM event_media_items emi
        WHERE emi.event_id = e.id),
        '[]'::JSON
    ) AS media_items
FROM event_volunteer_applications eva
JOIN events e ON eva.event_id = e.id
LEFT JOIN organizations o ON e.organization_id = o.id
WHERE eva.id = $1 AND eva.user_id = $2;

-- name: DeleteEventRegistration :exec
DELETE FROM event_registrations
WHERE id = $1;

-- name: ListMediaItemsForEvents :many
SELECT * FROM event_media_items
WHERE event_id = ANY(sqlc.arg(event_ids)::uuid[]);