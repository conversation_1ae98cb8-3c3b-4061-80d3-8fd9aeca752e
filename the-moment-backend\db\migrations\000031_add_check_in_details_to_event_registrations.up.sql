ALTER TABLE event_registrations
ADD COLUMN check_in_by_user_id UUID NULL REFERENCES users(id) ON DELETE SET NULL,
ADD COLUMN check_in_method VARCHAR(50) NULL;

COMMENT ON COLUMN event_registrations.check_in_by_user_id IS 'ID of the user (staff/volunteer) who performed the check-in for this registration (participant or volunteer).';
COMMENT ON COLUMN event_registrations.check_in_method IS 'Method used for check-in (e.g., qr_scan, manual_staff, self_check_in).'; 