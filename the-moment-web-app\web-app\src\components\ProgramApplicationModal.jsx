import React, { useState } from 'react';
import { Modal, Typo<PERSON>, Divider, Space, Button, Row, Col, Descriptions, Checkbox, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { genderFormatter } from '../utils/genderFormatter';

const { Title, Text, Paragraph } = Typography;

const ProgramApplicationModal = ({ 
    visible, 
    onClose, 
    programType, // 'volunteer' or 'homeVisit'
    onSubmit 
}) => {
    const { t } = useTranslation();
    const { user } = useAuth();
    const [agreed, setAgreed] = useState(false);
    
    // Determine program title and content based on type
    const getProgramTitle = () => {
        return programType === 'volunteer' 
            ? t('userSettings.programApplications.volunteer.title')
            : t('userSettings.programApplications.homeVisit.title');
    };
    
    const getTermsAndConditions = () => {
        if (programType === 'volunteer') {
            return [
                {
                    title: t('userSettings.programApplications.modal.terms.responsibilities'),
                    content: t('userSettings.programApplications.modal.terms.volunteerResponsibilities')
                },
                {
                    title: t('userSettings.programApplications.modal.terms.requirements'),
                    content: t('userSettings.programApplications.modal.terms.volunteerRequirements')
                },
                {
                    title: t('userSettings.programApplications.modal.terms.benefits'),
                    content: t('userSettings.programApplications.modal.terms.volunteerBenefits')
                }
            ];
        } else {
            return [
                {
                    title: t('userSettings.programApplications.modal.terms.eligibility'),
                    content: t('userSettings.programApplications.modal.terms.homeVisitEligibility')
                },
                {
                    title: t('userSettings.programApplications.modal.terms.services'),
                    content: t('userSettings.programApplications.modal.terms.homeVisitServices')
                },
                {
                    title: t('userSettings.programApplications.modal.terms.frequency'),
                    content: t('userSettings.programApplications.modal.terms.homeVisitFrequency')
                },
                {
                    title: t('userSettings.programApplications.modal.terms.policy'),
                    content: t('userSettings.programApplications.modal.terms.homeVisitPolicy')
                }
            ];
        }
    };
    
    const handleSubmit = () => {
        if (!agreed) {
            message.error(t('userSettings.programApplications.modal.messages.agreementRequired'));
            return;
        }
        
        onSubmit();
        onClose();
    };

    return (
        <Modal
            open={visible}
            onCancel={onClose}
            footer={null}
            width={800}
            style={{ maxHeight: '80vh', overflow: 'auto', padding: '24px 32px' }}
        >
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {/* Header */}
                <div>
                    <Title level={3}>{getProgramTitle()}</Title>
                    <Text type="secondary">
                        {programType === 'volunteer' 
                            ? t('userSettings.programApplications.volunteer.description') 
                            : t('userSettings.programApplications.homeVisit.description')}
                    </Text>
                </div>
                
                <Divider />
                
                {/* User Information */}
                <div>
                    <Title level={4}>{t('userSettings.programApplications.modal.userInformation')}</Title>
                    <Descriptions column={2}>
                        <Descriptions.Item label={t('userSettings.identityInfo.name')}>
                            {user?.lastName + ' ' + user?.firstName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={t('userSettings.identityInfo.userId')}>
                            {user?.id || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={t('userSettings.identityInfo.phoneNumber')}>
                            {user?.phoneNumber || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={t('userSettings.identityInfo.dateOfBirth')}>
                            {user?.dateOfBirth || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={t('userSettings.identityInfo.gender')}>
                            {genderFormatter(user?.gender)}
                        </Descriptions.Item>
                    </Descriptions>
                </div>

                <Divider />
                
                {/* Terms and Conditions */}
                <div>
                    <Title level={4}>{t('userSettings.programApplications.modal.termsAndConditions')}</Title>
                    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                        {getTermsAndConditions().map((term, index) => (
                            <div key={index}>
                                <Text strong>{term.title}</Text>
                                <Paragraph style={{ marginTop: '8px' }}>
                                    {term.content}
                                </Paragraph>
                            </div>
                        ))}
                    </Space>
                </div>
                
                <Divider />
                
                {/* Agreement */}
                <div>
                    <Checkbox 
                        checked={agreed} 
                        onChange={(e) => setAgreed(e.target.checked)}
                    >
                        <Text>{t('userSettings.programApplications.modal.agreeToTerms')}</Text>
                    </Checkbox>
                </div>
                
                {/* Actions */}
                <Row justify="end" gutter={16}>
                    <Col>
                        <Button onClick={onClose}>
                            {t('common.cancel')}
                        </Button>
                    </Col>
                    <Col>
                        <Button 
                            type="primary" 
                            onClick={handleSubmit}
                            disabled={!agreed}
                        >
                            {t('userSettings.programApplications.modal.submit')}
                        </Button>
                    </Col>
                </Row>
            </Space>
        </Modal>
    );
};

export default ProgramApplicationModal;
