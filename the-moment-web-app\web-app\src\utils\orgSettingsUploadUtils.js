import { message } from 'antd';

/**
 * 通用文件上传配置生成器
 * @param {Array} fileList - 当前文件列表
 * @param {Function} setFileList - 设置文件列表的函数
 * @param {Object} options - 配置选项
 * @param {Array} options.acceptedTypes - 接受的文件类型 (默认: ['image/jpeg', 'image/png'])
 * @param {number} options.maxSize - 最大文件大小(MB) (默认: 2)
 * @param {number} options.maxCount - 最大文件数量 (默认: 1)
 * @param {string} options.listType - 列表类型 (默认: 'picture')
 * @param {Function} options.t - 翻译函数
 * @returns {Object} Upload组件的props配置
 */
export const createUploadProps = (fileList, setFileList, options = {}) => {
    const {
        acceptedTypes = ['image/jpeg', 'image/png'],
        maxSize = 2,
        maxCount = 1,
        listType = 'picture',
        t = (key, defaultValue = key) => defaultValue
    } = options;

    return {
        listType,
        maxCount,
        fileList,
        beforeUpload: (file) => {
            const isAcceptedType = acceptedTypes.includes(file.type);
            if (!isAcceptedType) {
                message.error(t('organizationSettings.errors.imageTypeInvalid', 'You can only upload JPG/PNG files!'));
            }
            
            const isValidSize = file.size / 1024 / 1024 < maxSize;
            if (!isValidSize) {
                message.error(t('organizationSettings.errors.imageSizeInvalid', `Image must be smaller than ${maxSize}MB!`));
            }
            
            if (isAcceptedType && isValidSize) {
                setFileList([file]);
            } else {
                setFileList(fileList.filter(f => f.uid !== file.uid));
            }
            return false;
        },
        onChange: ({ file, fileList: newFileList }) => {
            if (file.status === 'removed') {
                setFileList([]);
            } else {
                const isAcceptedType = acceptedTypes.includes(file.type);
                const isValidSize = file.size / 1024 / 1024 < maxSize;
                
                if (isAcceptedType && isValidSize) {
                    setFileList(newFileList.slice(-maxCount));
                } else if (newFileList.length > 0 && !(isAcceptedType && isValidSize)) {
                    setFileList(newFileList.filter(f => f.uid !== file.uid));
                }
            }
        },
        onRemove: () => {
            setFileList([]);
        }
    };
};

/**
 * 组织Logo上传的专用配置
 * @param {Array} fileList - 当前文件列表
 * @param {Function} setFileList - 设置文件列表的函数
 * @param {Function} t - 翻译函数
 * @returns {Object} Upload组件的props配置
 */
export const createOrgLogoUploadProps = (fileList, setFileList, t) => {
    return createUploadProps(fileList, setFileList, {
        acceptedTypes: ['image/jpeg', 'image/png'],
        maxSize: 2,
        maxCount: 1,
        listType: 'picture',
        t
    });
}; 