package payloads

import (
	"time"

	"github.com/google/uuid"
)

// AdminUserResponse defines the user structure for admin-facing endpoints
type AdminUserResponse struct {
	ID                uuid.UUID  `json:"id"`
	DisplayName       string     `json:"display_name"`
	Email             *string    `json:"email,omitempty"`
	EmailVerifiedAt   *time.Time `json:"email_verified_at,omitempty"`
	Phone             *string    `json:"phone,omitempty"`
	PhoneVerifiedAt   *time.Time `json:"phone_verified_at,omitempty"`
	ProfilePictureURL *string    `json:"profile_picture_url,omitempty"`

	Role                        string    `json:"role"`
	CreatedAt                   time.Time `json:"created_at"`
	UpdatedAt                   time.Time `json:"updated_at"`
	InterfaceLanguage           string    `json:"interface_language"`
	CommunicationLanguage       string    `json:"communication_language"`
	EnableAppNotifications      bool      `json:"enable_app_notifications"`
	EnableWhatsappNotifications bool      `json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool      `json:"enable_sms_notifications"`
	EnableEmailNotifications    bool      `json:"enable_email_notifications"`
	PhoneOtpChannel             string    `json:"phone_otp_channel"`
}

// PaginatedUsersResponse for listing users
type PaginatedUsersResponse struct {
	Users      []AdminUserResponse `json:"users"`
	TotalCount int64               `json:"total_count"`
	Page       int                 `json:"page"`
	Limit      int                 `json:"limit"`
}

// CreateStaffUserRequest defines the payload for creating a staff user.
type CreateStaffUserRequest struct {
	DisplayName           string  `json:"display_name" validate:"required,min=2,max=50"`
	Email                 string  `json:"email" validate:"required,email"`
	Password              string  `json:"password" validate:"required,min=8"` // Password will be hashed by the service
	InterfaceLanguage     *string `json:"interface_language,omitempty"`
	CommunicationLanguage *string `json:"communication_language,omitempty"`
}

// OrganizationManagerRequest defines the payload for assigning or removing an organization manager.
// Deprecated: Use AssignManagerRequest or RemoveManagerRequest instead.
type OrganizationManagerRequest struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

// AssignManagerRequest defines the payload for assigning an organization manager.
type AssignManagerRequest struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

// RemoveManagerRequest defines the payload for removing an organization manager.
type RemoveManagerRequest struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
}
