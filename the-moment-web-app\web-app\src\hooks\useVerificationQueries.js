import { useQuery } from '@tanstack/react-query';
import { verificationService } from '../services/verificationService';
import { ALL_ORGANIZATION_ID } from '../contexts/OrganizationContext'; // ALL_ORGANIZATION_ID might not be relevant for event-specific registrations

// Function for fetching event registrations (previously verifications)
export const fetchEventRegistrationsList = async (eventId, status, page = 1, pageSize = 10, queryParams = {}) => {
  if (!eventId) {
    console.error('fetchEventRegistrationsList requires an eventId');
    // Or throw an error, or return an empty state, depending on desired handling
    return { registrations: [], total: 0 }; 
  }
  const params = {
    eventId: eventId, // Will be used for the path, not as a query param here
    status: status,
    limit: pageSize,
    offset: (page - 1) * pageSize,
    ...queryParams, // Allows passing search_name, user_id, payment_status etc.
  };

  // orgId is not a parameter for the new endpoint /events/{eventId}/registrations
  // if (orgId && orgId !== ALL_ORGANIZATION_ID) {
  //   params.orgId = orgId;
  // }

  const response = await verificationService.listEventRegistrationsAdmin(params);
  
  // Service now returns { registrations, total }
  return { 
    registrations: response.registrations || [], 
    total: response.total || 0 
  };
};

// New function to fetch pending verifications for an organization
export const fetchPendingVerificationsForOrg = async (orgId, page = 1, pageSize = 10) => {
  const params = {
    status: 'pending', // Ensure 'pending' status is always requested
    limit: pageSize,
    offset: (page - 1) * pageSize,
  };
  if (orgId) { // Assuming listPendingVerifications can take orgId
    params.orgId = orgId;
  }

  // Service now returns { verifications, total }
  const response = await verificationService.listPendingVerifications(params);
  
  return { 
    verifications: response.verifications || [], 
    total: response.total || 0
  };
};

export const fetchApprovedVerificationsForOrg = async (orgId, page = 1, pageSize = 10) => {
  const params = {
    status: 'approved', // Ensure 'pending' status is always requested
    limit: pageSize,
    offset: (page - 1) * pageSize,
  };
  if (orgId) { // Assuming listPendingVerifications can take orgId
    params.orgId = orgId;
  }

  // Service now returns { verifications, total }
  const response = await verificationService.listApprovedVerifications(params);
  
  return { 
    verifications: response.verifications || [], 
    total: response.total || 0
  };
};

// Function for fetching verifications with status filtering
export const fetchVerificationsList = async (orgId, status, page = 1, pageSize = 10) => {
  const params = {
    status: status,
    limit: pageSize,
    offset: (page - 1) * pageSize,
  };

  // if (orgId && orgId !== ALL_ORGANIZATION_ID) {
  //   params.orgId = orgId;
  // }

  // Service now returns { verifications, total }
  const response = await verificationService.listAllVerifications(params);
  
  return { 
    verifications: response.verifications || [], 
    total: response.total || 0 
  };
};

// Hook for fetching verification details
export const useVerificationDetails = (userId) => {
  return useQuery({
    queryKey: ['verificationDetails', userId],
    queryFn: async () => {
      const { data } = await verificationService.getVerificationDetails(userId);
      return data;
    },
    enabled: !!userId, // Only run the query if userId is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
