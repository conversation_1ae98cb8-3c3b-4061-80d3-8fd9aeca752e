import { Platform } from 'react-native';

export default {
  common: {
    apply: '確定',
    cancel: '取消',
    confirm: '確認',
    back: '返回',
    continue: '繼續',
    or: '或',
    error: '錯誤',
    save: '儲存',
    ok: '確定',
    home: '主頁',
    goBack: '返回',
    imageLoadError: '圖片載入失敗',
    step: '步驟',
    loading: '載入中...',
    photo: '照片',
    submit: '提交',
    success: '成功',
    askMeLater: '稍後詢問',
    dialog: {
      warning: '提示',
      info: '提示',
      error: '錯誤',
      success: '成功',
    },
    viewAll: '查看全部',
    locationUnavailable: '地點不詳',
    noDate: '未知日期',
    noAuthor: '未知作者',
    yes: '是',
    no: '否',
    edit: '編輯',
    delete: '刪除',
    create: '創建',
    notification: '通知',
    searchPlaceholder: '搜尋',
    noData: '沒有找到數據',
    done: '完成',
    complete: '完成',
    uploading: '上傳中',
    uploaded: '已上傳',
    close: '關閉',
    fileOpenError: '無法打開檔案',
    unknown: '未知',
    thisOrganization: '該組織',
    skipAndContinue: '跳過並繼續',
    characters: '字符',
  },
  documents: {
    mainlandIdCard: "中國居民身份證",
    hk_id_card: "香港身份證",
    mainland_china_id_card: "中國居民身份證",
    mainland_travel_permit: "內地出入境證件",
    address_proof: "地址證明",
    student_id: "學生證",
    home_visit: "家訪",
    passport: "護照",
    hk_youth_plus: "HKYouth+"
  },
  error: {
    retry: '重試',
    title: '出錯了',
    message: '抱歉，我們找不到相關內容，請稍後重試。',
    phoneVerification: {
      RATE_LIMIT_EXCEEDED: '發送過於頻繁，請稍後再試',
      SYSTEM_ERROR: '系統錯誤，請稍後再試',
      INVALID_CODE: '驗證碼不正確',
      CODE_EXPIRED: '驗證碼已過期',
      PHONE_EXISTS: '手機號碼已存在',
      INVALID_REQUEST: '無效的請求',
      UNAUTHORIZED: '未授權',
      DISPLAY_NAME_REQUIRED: '請輸入顯示名稱',
      STATE_MISSING: '無效的驗證流程，請重新開始',
      RESEND_FAILED: '重新發送驗證碼失敗',
      CONNECTION_ERROR: '連接伺服器失敗，請檢查網絡連接',
      RESEND_FAILED_NO_STATE: '重新發送驗證碼失敗，請重新開始驗證',
      SYSTEM_ERROR_RESEND: '重新發送驗證碼失敗，請稍後再試',
      ACCOUNT_LOCKED: '因嘗試次數過多，帳戶暫時鎖定'
    },
    sendVerificationCode: {
      RATE_LIMIT_EXCEEDED: '發送過於頻繁，請稍後再試',
      SYSTEM_ERROR: '系統錯誤，請稍後再試',
      PHONE_REQUIRED: '請輸入手機號碼',
      INVALID_REQUEST: '請求無效，請重試',
      USER_NOT_FOUND: '此號碼未註冊，將為您創建新帳戶',
      PHONE_CHECK_FAILED: '手機號碼驗證失敗，請重試',
      LOGIN_INITIATE_FAILED: '登入初始化失敗，請重試',
      REGISTRATION_INITIATE_FAILED: '註冊初始化失敗，請重試',
      OTP_SEND_FAILED: '驗證碼發送失敗，請檢查手機號碼',
      SERVICE_UNAVAILABLE: '服務暫時不可用，請稍後重試',
      INVALID_CHANNEL: '驗證方式無效'
    },
    systemError: '系統錯誤，請稍後再試',
    failed_to_load_registered_events: '無法載入已報名活動',
    failed_to_load_volunteer_applications: '無法載入義工申請',
    failed_to_load_events: '無法載入活動',
    // HTTP error codes
    http: {
      401: '無權限訪問，請確認您有相應權限',
      403: '權限不足，無法訪問此資源',
      404: '找不到所請求的資源',
      500: '伺服器錯誤，請稍後再試',
      default: '網絡錯誤，請檢查連接後重試'
    },
  },
  navigation: {
    dashboard: '主頁',
    explore: '探索',
    myEvents: '我的活動',
    profile: '我的',
    login: '登入',
    settings: '設定',
    qrcode: '二維碼',
  },
  auth: {
    appName: '會員系統',
    slogan: '連結 • 分享 • 啟發',
    logout: '登出',
    loginRequired: '需要登入',
    loginRequiredMessage: '請登入以使用此功能',
    loginButton: '登入',
    logoutConfirm: '確定要登出嗎？',
    logoutError: '登出失敗，請重試。',
    verifiedAccount: '已驗證帳戶',
    unverifiedAccount: '身份未驗證',
    underReviewAccount: '審核中',
    loginWithPhone: '以手機號碼登入',
    signUp: '註冊',
    login: '登入',
    continueWithWhatsApp: 'WhatsApp 登入',
    whatsAppVerificationTitle: 'WhatsApp 驗證',
    whatsAppVerificationDesc: "我們將透過 WhatsApp 發送驗證碼至您的手機",
    registrationInfo: '如果此號碼未註冊，系統將自動創建新帳戶。',
    registrationInfoWhatsApp: '如果此號碼未註冊，系統將自動創建新帳戶。',
    termsAgreement: {
      prefix: '繼續即表示您同意我們的',
      terms: '服務條款',
      and: '和',
      privacy: '私隱政策'
    },
    termsOfService: '服務條款',
    privacyPolicy: '私隱政策',
    smsVerification: '短訊驗證',
    smsVerificationDesc: '我們將發送 6 位數字驗證碼至您的手機',
    codeVerificationDesc: '請輸入發送至您手機的 6 位數字驗證碼',
    phonePrefix: '+852',
    phonePlaceholder: '1234 5678',
    phoneNumber: '電話號碼',
    sendCodeError: '發送驗證碼失敗，請重試。',
    verificationError: '驗證碼無效，請重試。',
    resendCode: '重新發送驗證碼',
    resendCodeTimer: '{{seconds}}秒後可重新發送',
    verify: '驗證',
    modal: {
      title: '需要登入',
      description: '登入後即可使用更多功能，體驗完整的平台服務',
      loginButton: '立即登入',
      cancelButton: '取消',
    },
    forgotAccount: '無法訪問您的帳戶？',
    enterDisplayName: '請輸入顯示名稱',
    displayNamePlaceholder: '用戶名稱',
    blockedTitle: '帳戶暫時鎖定',
    blockedMessage: '錯誤嘗試次數過多，請在{{minutes}}分鐘後重試。',
    blockedCountdown: '帳戶已鎖定，請在{{minutes}}分鐘後重試。',
  },
  identity: {
    title: '文件認證',
    subtitle: '查看和管理您的文件驗證狀態。',
    documentVerification: {
      hkid: '香港身份證',
      mainland_id: '內地身份證',
      mainland_travel_permit: '回鄉證或內地通行證',
      passport: '護照',
      hkyouth: 'HK Youth+',
      address: '地址驗證',
      student_card: '學生證',
    },
    verification: {
      title: '文件驗證',
      subtitle: '請驗證您的文件以使用所有功能',
      documentsSection: '文件驗證',
      addressSection: '地址驗證',
      requirementsTitle: '驗證要求',
      statusLabels: {
        verified: '已驗證',
        pending: '審核中',
        rejected: '已拒絕',
        unverified: '未驗證'
      },
      photo: {
        title: '上傳清晰照片',
        description: '在光線充足的環境下拍攝清晰的照片，確保所有文字清晰可見。',
      },
    },
    info: {
      title: '個人資料',
      defaultTitle: '請輸入資料',
      englishName: '英文姓名',
      title_hkid: '香港身份證資料',
      title_mainland_id: '內地身份證資料',
      title_mainland_travel_permit: '回鄉證/內地通行證資料',
      title_passport: '護照資料',
      title_hk_youth: 'HK Youth+資料',
      title_address: '地址證明資料',
      title_student_id: '學生證資料',
      softCopy: '電子副本',
      hkidChineseName: '中文姓名（選填）',
      hkidChineseNamePlaceholder: '請輸入中文姓名',
      hkidEnglishName: '英文姓名',
      hkidEnglishNamePlaceholder: '請輸入英文姓名',
      hkidNumber: '香港身份證號碼',
      hkidPrefixPlaceholder: 'A111111',
      hkidCheckDigitPlaceholder: '9',
      hkidDob: '出生日期',
      dobPlaceholder: 'DD-MM-YYYY',
      hkidGender: '性別',
      hkidPermanentResident: '永久性居民',
      hkidChineseCommercialCode: '中文電碼',
      hkidChineseCommercialCodePlaceholder: '選填，例如：1234',
      mainlandIdChineseName: '中文姓名',
      mainlandIdChineseNamePlaceholder: '例如：李曉明',
      mainlandIdNumber: '內地身份證號碼',
      mainlandIdNumberPlaceholder: '18位數字，最後一位可能是X',
      mainlandIdDob: '出生日期',
      mainlandIdGender: '性別',
      mainlandIdValidFrom: '簽發日期',
      mainlandIdExpiryDate: '有效期限',
      expiryDatePlaceholder: 'DD-MM-YYYY',
      mainlandTravelPermitNumber: '回鄉證/內地通行證號碼',
      mainlandTravelPermitNumberPlaceholder: '例如：H1234567890',
      mainlandTravelPermitIssueDate: '簽發日期',
      issueDatePlaceholder: 'DD-MM-YYYY',
      mainlandTravelPermitExpiryDate: '有效期限',
      passportNumber: '護照號碼',
      passportNumberPlaceholder: '例如：*********',
      passportIssuingCountry: '護照所屬地',
      selectCountry: '選擇國家',
      countryPlaceholder: '請選擇護照所屬國家',
      searchCountry: '搜尋國家',
      passportIssueDate: '發出日期',
      passportExpiryDate: '到期日期',
      hkYouthMembershipNumber: '會員號碼',
      hkYouthMembershipNumberPlaceholder: '請輸入您的會員號碼',
      addressUnit: '單位',
      addressUnitPlaceholder: '例如：Flat A / Unit A',
      addressFloor: '樓層',
      addressFloorPlaceholder: '例如：10/F',
      addressBuilding: '大廈名稱/門牌號碼',
      addressBuildingPlaceholder: "例如：幸福大廈 / 幸福花園一期 / 幸福村 / 10號",
      addressStreet: "街道",
      addressStreetPlaceholder: "例如：皇后大道中1號",
      addressDistrict: '地區',
      addressDistrictPlaceholder: "例如：中西區 / 油尖旺區",
      addressRegion: '區域',
      addressRegionPlaceholder: '選擇區域',
      regionNewTerritories: '新界',
      regionKowloon: '九龍',
      regionHongKongIsland: '香港島',
      studentIdSchoolName: '學校名稱',
      studentIdSchoolNamePlaceholder: '例如：XX中學 / XX大學',
      studentIdGrade: '年級',
      studentIdGradePlaceholder: '例如：中四 / 大一',
      studentIdExpiryDate: '到期日期',
      male: '男',
      female: '女',
      next: '下一步',
      validation: {
        chineseNameRequired: '請輸入中文姓名',
        englishNameRequired: '請輸入英文姓名',
        idCardNumberRequired: '請輸入證件號碼',
        idCardNumberInvalid: '身份證號碼格式不正確',
        idCardNumberInvalidChecksum: '無效的香港身份證號碼，請檢查後重試。',
        mainlandTravelPermitInvalid: '通行證號碼格式不正確',
        mainlandIdInvalid: '內地身份證號碼格式不正確',
        passportInvalid: '護照號碼格式不正確',
        countryRequired: '請選擇國家/地區',
        dateOfBirthRequired: '請輸入出生日期',
        dateOfBirthInvalid: '日期格式不正確，請使用DD-MM-YYYY格式',
        dateOfBirthFuture: '出生日期不能是將來日期',
        expiryDateRequired: 'Expiry date is required',
        expiryDateInvalid: '無效的到期日期格式，請使用DD-MM-YYYY格式',
        expiryDatePast: '到期日期不能是過去日期',
        issueDateRequired: '發出日期是必填的',
        issueDateInvalid: '無效的發出日期格式，請使用DD-MM-YYYY格式',
        issueDateFuture: '發出日期不能是將來日期',
        regionRequired: '區域是必填的',
        genericRequired: '{{fieldName}}是必填的',
        termsRequired: '請同意服務條款',
        hkidInvalid: '無效的香港身份證格式。',
      },
    },
    guide: {
      title: '身份認證',
      subtitle: '按照以下步驟快速安全地完成身份認證。',
      selectDocument: '選擇證件類型',
      stepsTitle: '認證流程',
      steps: {
        info: {
          title: '驗證個人資料',
          description: '在上傳身份證照片前，請先填寫並核實您的個人資料。',
          title_hk_id_card: '驗證香港身份證資料',
          description_hk_id_card: "請準備好您的香港身份證，確保證件乾淨無損。",
          title_mainland_china_id_card: '驗證內地身份證資料',
          description_mainland_china_id_card: "請準備好您的內地身份證，確保證件乾淨無損。",
          title_mainland_travel_permit: '驗證通行證資料',
          description_mainland_travel_permit: "請準備好您的通行證，確保證件乾淨無損。",
          title_passport: '驗證護照資料',
          description_passport: "請準備好您的護照，確保證件乾淨無損。",
          title_hk_youth_plus: '驗證HK Youth+資料',
          description_hk_youth_plus: "請準備好您的HK Youth+會員卡或相關證明文件。",
          title_address_proof: '輸入地址資料',
          description_address_proof: "請準備好您的地址證明文件，確保證件乾淨無損。",
          title_student_id: '驗證學生證資料',
          description_student_id: "請準備好您的學生證，確保證件乾淨無損。",
          title_home_visit: '輸入家訪申請資料',
          description_home_visit: "請確認您的家訪申請資料。",
          title_volunteer: '輸入義工申請資料',
          description_volunteer: "請確認您的義工申請資料。",
        },
        prepare: {
          title: '準備身份證',
          description: '請準備好您的香港身份證，確保證件乾淨無損。',
          title_hk_id_card: '準備香港身份證',
          description_hk_id_card: "請準備好您的香港身份證，確保證件乾淨無損。",
          title_mainland_china_id_card: '準備內地身份證',
          description_mainland_china_id_card: "請準備好您的內地身份證，確保證件乾淨無損。",
          title_mainland_travel_permit: '準備通行證',
          description_mainland_travel_permit: "請準備好您的通行證，確保證件乾淨無損。",
          title_passport: '準備護照',
          description_passport: "請準備好您的護照，確保證件乾淨無損。",
          title_hk_youth_plus: '準備HK Youth+證明',
          description_hk_youth_plus: "如果適用，請準備您的HK Youth+會員卡或相關證明文件。",
          title_address_proof: '準備地址證明',
          description_address_proof: "請準備好您的地址證明文件，確保證件乾淨無損。",
          title_student_id: '準備學生證',
          description_student_id: "請準備好您的學生證，確保證件乾淨無損。",
          title_home_visit: '準備家訪申請',
          description_home_visit: "確保所有必要信息已準備就緒。",
          title_volunteer: '準備義工申請',
          description_volunteer: "確保所有必要信息已準備就緒。",
        },
        photo: {
          title: '上傳清晰照片',
          description: '在光線充足的環境下拍攝清晰的照片，確保所有文字清晰可見。',
          title_hk_id_card: '上傳香港身份證照片',
          description_hk_id_card: "上傳香港身份證正反面照片。確保照片清晰、完整，無反光。",
          title_mainland_china_id_card: '上傳內地身份證照片',
          description_mainland_china_id_card: "上傳內地身份證正反面照片。確保照片清晰、完整，無反光。",
          title_mainland_travel_permit: '上傳回鄉證/內地通行證照片',
          description_mainland_travel_permit: "上傳回鄉證/內地通行證個人資料頁照片。確保照片清晰、完整，無反光。",
          title_passport: '上傳護照照片',
          description_passport: "上傳護照個人資料頁照片。確保照片清晰、完整，無反光。",
          title_hk_youth_plus: '上傳HK Youth+證明照片',
          description_hk_youth_plus: "上傳HK Youth+會員卡或相關證明文件。確保照片清晰。",
          title_address_proof: '上傳地址證明照片',
          description_address_proof: "上傳地址證明文件的完整頁面。確保姓名、地址、發出日期清晰可見。",
          title_student_id: '上傳學生證照片',
          description_student_id: "上傳學生證正面照片。如有背面資訊，請一併拍攝。",
          title_home_visit: '上傳家訪申請文件',
          description_home_visit: "上傳家訪申請所需的任何支持文件。",
          title_volunteer: '上傳義工文件',
          description_volunteer: "上傳義工申請所需的任何支持文件。",
        },
        review: {
          title: '等待審核',
          description: '提交後，我們將在3-5個工作天內完成身份驗證。審核完成後，您將收到通知。',
          title_hk_id_card: '等待香港身份證審核',
          description_hk_id_card: "我們很快就會審核您的香港身份證訊息，通常在 3-5 個工作天內。",
          title_mainland_china_id_card: '等待內地身份證審核',
          description_mainland_china_id_card: "我們將盡快審核您的內地身份證訊息，一般在3-5個工作天內。",
          title_mainland_travel_permit: '等待通行證審核',
          description_mainland_travel_permit: "我們將盡快審核您的通行證訊息，一般在3-5個工作天內。",
          title_passport: '等待護照審核',
          description_passport: "我們將盡快審核您的護照訊息，一般在3-5個工作天內。",
          title_hk_youth_plus: '等待HK Youth+審核',
          description_hk_youth_plus: "我們將盡快審核您的HK Youth+訊息，一般在3-5個工作天內。",
          title_address_proof: '等待地址證明審核',
          description_address_proof: "我們將盡快審核您的地址證明訊息，一般在3-5個工作天內。",
          title_student_id: '等待學生證審核',
          description_student_id: "我們將盡快審核您的學生證訊息，一般在3-5個工作天內。",
          title_home_visit: '等待家訪申請審核',
          description_home_visit: "您的家訪申請將由我們的團隊審核。",
          title_volunteer: '等待義工申請審核',
          description_volunteer: "您的義工申請將由我們的團隊審核。",
        }
      },
      startButton: '開始認證',
    },
    steps: {
      button: '提交認證文件',
      front: {
        title: '證件正面照片',
        description: '請上傳清晰正面照片，確保所有文字清晰可見。',
        hk_id_card: {
          title: '請上傳香港身份證正面照片',
          sample: '示例：香港身份證正面',
        },
        mainland_china_id_card: {
          title: '請上傳內地身份證正面照片',
          sample: '示例：內地身份證正面'
        },
        mainland_travel_permit: {
          title: '請上傳回鄉證/內地通行證正面照片',
          sample: '示例：回鄉證/內地通行證正面'
        },
        passport: {
          title: '請上傳護照照片',
          sample: '示例：護照資料頁'
        },
        hk_youth_plus: {
          title: '上傳HK Youth+照片',
          sample: '示例：HK Youth+照片'
        },
        address_proof: {
          title: '請上傳地址證明照片',
          sample: '示例：地址證明'
        },
        student_id: {
          title: '請上傳學生證正面照片',
          sample: '示例：學生證正面'
        },
        default: {
          title: '請上傳證明文件正面照片',
          sample: '示例：正面照片'
        },
        error: {
          noImage: '請上傳正面照片',
          missingImage: '需要正面照片'
        },
        success: {
          title: '正面照片已上傳',
          message: '正面照片已成功上傳，請繼續上傳背面照片。',
        },
      },
      back: {
        title: '證件背面照片',
        description: '請上傳清晰背面照片，確保所有文字清晰可見。',
        hk_id_card: {
          title: '請上傳香港身份證背面照片',
          sample: '示例：香港身份證背面',
        },
        mainland_china_id_card: {
          title: '請上傳內地身份證背面照片',
          sample: '示例：內地身份證背面'
        },
        mainland_travel_permit: {
          title: "請上傳回鄉證/內地通行證背面照片",
          sample: "示例：回鄉證/內地通行證背面",
        },
        student_id: {
          title: '請上傳學生證背面照片',
          sample: '示例：學生證背面'
        },
        default: {
          title: '請上傳證明文件背面照片',
          sample: '示例：背面照片'
        },
        error: {
          missingImage: '需要背面照片'
        },
        success: {
          title: '成功',
          message: '您的文件驗證已提交',
          confirm: '確定'
        }
      },
    },
    upload: {
      title: '選擇上傳方式',
      camera: '拍攝照片',
      gallery: '從相簿選擇',
      button: '上傳照片',
    },
    permissions: {
      gallery: '請允許存取相簿以上傳照片。',
      camera: '請允許使用相機以拍攝照片。',
    },
    errors: {
      upload: '上傳照片失敗，請重試。',
      incomplete: '請上傳身份證正反兩面照片後再提交。',
      incompletePassport: '請上傳護照資料頁的照片。',
      capture: '拍攝照片失敗，請重試。',
      invalidImage: '照片不符合要求，請重新上傳。',
      fileTooLarge: '圖片大小不能超過10MB',
      noDocType: "文件類型未指定。請返回並重試。",
      submissionFailed: "提交失敗。請稍後再試。",
      noFieldsConfigured: "沒有為文件類型指定信息字段：{{docType}}。請檢查配置。",
    },
    success: {
      title: '成功',
      message: '您的身份驗證已提交',
      confirm: '確定',
      homeVisit: "家訪計劃申請成功提交！",
      volunteer: "義工計劃申請成功提交！",
    },
    terms: {
      default: {
        title: "服務條款",
        content: "請閱讀並同意相關的服務條款。",
      },
      home_visit: {
        title: "家訪計劃服務條款",
        content: "家訪計劃的具體條款...",
      },
      volunteer: {
        title: "義工計劃服務條款",
        content: "義工計劃的具體條款...",
      },
      agreeToTerms: "我已閱讀並同意上述服務條款。",
    }
  },
  profile: {
    sections: {
      accountSettings: '帳戶設定',
      security: '安全',
      preferences: '偏好設定',
      supportAndAbout: '支援與關於',
      verification: '認證與申請',
    },
    errors: {
      settingsLoadFailed: '載入用戶設定失敗，請稍後重試',
    },
    avatar: {
      title: '更換頭像',
      success: '頭像更新成功',
      error: '頭像更新失敗',
      errors: {
        FILE_TOO_LARGE: '圖片大小不能超過5MB',
        INVALID_FORMAT: '圖片格式不正確',
        SYSTEM_ERROR: '系統錯誤，請稍後重試'
      }
    },
    organization: {
      tapToChange: '點擊切換組織',
      tapToManage: '點擊管理您的組織成員資格',
      manageOrganizations: '管理組織',
      selectToJoin: '選擇您想要加入或離開的組織',
      alreadyJoined: '已加入',
      updateSettings: '更新組織設置',
      updateSuccess: {
        title: '設置已更新',
        message: '您的組織設置已成功更新。'
      },
      updateError: {
        title: '更新失敗',
        message: '無法更新組織設置。請再試一次。'
      },
      noOrganizations: {
        title: '暫無可選組織',
        message: '目前沒有可以加入的組織。'
      },
    },
    guest: '訪客',
    logoutErrorDesc: '登出失敗，請稍後重試',
    logoutConfirmTitle: '登出確認',
    logoutConfirmMessage: '確定要登出嗎？',
    identityVerification: '身份認證',
    applicationStatus: '申請計劃狀態',
    latestApplication: '最新申請',
    editProfile: '身份資料',
    notifications: '通知',
    myStats: '我的數據',
    helpCenter: '幫助中心',
    terms: '服務條款',
    privacy: '私隱政策',
    editUsernameTitle: '編輯用戶名',
    usernameLabel: '用戶名',
    usernameRequired: '用戶名不能為空',
    usernameUpdateError: '更新用戶名失敗',
    language: '語言設定',
    logout: '登出',
    deleteAccount: {
      title: '刪除我的帳戶',
      whatWillBeDeleted: '將被刪除的內容',
      step1: {
        title: '個人資料',
        description: '您的姓名、電話號碼、電郵地址及所有身份驗證文件將被永久刪除。',
      },
      step2: {
        title: '活動紀錄',
        description: '您所有的活動報名、參與紀錄及義工申請將被移除。',
      },
      step3: {
        title: '帳戶資料',
        description: '您的登入憑證、偏好設定及所有相關帳戶資料將被永久刪除。',
      },
      warning: {
        title: '此操作無法復原',
        description: '一旦刪除帳戶，您將無法恢復您的資料。請確保在繼續前已下載任何重要資訊。',
      },
      continue: '確認刪除',
      confirmModal: {
        title: '刪除帳戶',
        message: '此操作無法復原。您確定要永久刪除您的帳戶嗎？',
        confirm: '刪除帳戶',
        deleting: '刪除中...',
      },
      success: {
        title: '帳戶已刪除',
        message: '您的帳戶已成功刪除。',
      },
      error: {
        title: '刪除失敗',
        message: '刪除帳戶失敗，請檢查網絡連接後重試。',
      },
    },
    appStatus: {
      noApplications: '未申請任何計劃',
      volunteerPending: '義工申請審核中',
      volunteerApproved: '義工申請已獲批准',
      someRejected: '申請被拒絕',
      summary: {
        someRejected: '部分申請/驗證被拒絕',
        somePending: '部分申請/驗證審核中',
        allApproved: '所有申請/驗證已獲批准',
        someApproved: '部分申請/驗證已獲批准',
        allUnverifiedOrNotSubmitted: '未申請計劃/未提交驗證',
      }
    },
    idStatus: {
      allVerified: '所有文件已驗證',
      somePending: '驗證進行中',
      someRejected: '驗證被拒絕',
      hkidVerifiedOnly: '身份證已驗證，地址待驗證',
      addressVerifiedOnly: '地址已驗證，身份證待驗證',
      noneVerified: '未驗證任何文件'
    },
    username: {
      edit: '編輯',
      title: '編輯用戶名',
      placeholder: '請輸入用戶名',
      save: '保存',
      success: '用戶名更新成功',
      error: '用戶名更新失敗',
      editRules: '用戶名長度必須在4-12個字符之間，修改後15天內不能再次修改',
      errors: {
        INVALID_USERNAME: '無效的用戶名格式',
        SYSTEM_ERROR: '系統錯誤，請稍後再試'
      }
    },
    changePhone: {
      title: '更改手機號碼',
      smsVerification: '手機號碼驗證',
      smsVerificationDesc: '請輸入新的手機號碼',
      codeVerificationDesc: '請輸入發送至新手機號碼的6位數字驗證碼',
      success: {
        title: '手機號碼已更改',
        message: '您的手機號碼已成功更新。',
      },
      verifyCurrentPhone: '驗證當前手機號碼',
      verifyCurrentPhoneDesc: '請選擇驗證方式以繼續更改手機號碼',
      verificationInfo: '我們將向您的手機發送驗證碼',
      selectMethod: '選擇驗證方式',
      useWhatsApp: '使用 WhatsApp 驗證',
      useSMS: '使用短訊驗證',
      smsDesc: '我們將向您的手機發送SMS驗證碼',
      whatsappDesc: '通過WhatsApp發送6位數字驗證碼',
      verificationCodeDesc: '請輸入發送至您手機的6位數字驗證碼',
      verifyNewPhoneCode: '驗證新號碼',
      verifyNewPhoneCodeDesc: '請輸入發送至您新號碼的6位數字驗證碼',
    },
    changePhoneGuide: {
      header: '更改手機號碼指南',
      title: '更改手機號碼',
      important: '重要信息',
      description: '更改手機號碼將影響您的帳戶登入信息。更改後，您需要使用新的手機號碼進行登入。',
      howItWorks: '操作流程',
      step1: {
        title: '驗證舊號碼',
        description: '您需要通過短信驗證碼來驗證舊的手機號碼。'
      },
      step2: {
        title: '驗證新號碼及帳戶轉移',
        description: '一旦新號碼驗證通過，您的帳戶將安全地轉移到新手機號碼。此後，您需要使用新號碼進行所有登入和帳戶恢復操作。'
      },
      trouble: {
        title: '無法使用您當前的手機號碼？',
        description: '如果您無法再使用當前手機號碼，請發送電郵聯絡我們。'
      },
      continue: '繼續更改手機號碼',
      needHelp: '我需要帳戶幫助'
    },
    edit: {
      title: '身份資料',
      notice: {
        title: '重要提示',
        message: '此資料將用於身份認證。\n一旦認證通過，如需修改請聯絡客戶服務。',
      },
      save: '儲存',
      success: {
        title: '身份資料已更新',
        message: '您的身份資料已成功更新。',
      },
      error: {
        title: '更新失敗',
        message: '更新身份資料失敗，請重試。',
      },
    },
    stats: {
      title: '我的數據',
      joinedDays: '加入天數',
      overview: {
        totalEvents: '參與活動',
        totalHours: '活動時長',
        volunteerEvents: '志願活動',
      },
      chart: {
        title: '活動統計',
        hint: '僅顯示近六個月數據',
        noData: '暫無數據',
        hours: '小時',
        events: '活動',
      },
      details: {
        monthly: {
          title: '每月活動',
          sixMonthsNote: '僅顯示近六個月數據',
        },
        categories: {
          title: '活動類型',
          viewAll: '查看所有活動',
          noData: '暫無過往活動',
        },
      },
    },
    items: {
      editProfile: {
        title: '身份資料',
        description: '管理身份認證資料',
      },
      phoneNumber: {
        title: '更改手機號碼',
        description: '更新您的手機號碼',
      },
      identity: {
        title: '身份證件認證',
        unverified: '未認證，請進行認證',
        pending: '審核中',
        verified: '已認證',
        rejected: '認證被拒絕',
      },
      address: {
        title: '地址認證',
        unverified: '未認證',
        pending: '審核中',
        verified: '已認證',
        rejected: '認證被拒絕',
        requiresIdCard: {
          title: '需要完成認證',
          message: '請先完成身份證件認證，才可進行地址認證。',
          button: '進行身份證件認證'
        }
      },
      email: {
        title: '電郵地址',
        description: '添加接收通知的電郵',
      },
      twoFactor: {
        title: '雙重認證',
        description: '增加帳戶安全性',
      },
      loginDevices: {
        title: '登入裝置',
        description: '管理已連接的裝置',
      },
      notifications: {
        title: '通知',
        description: '管理推送通知',
      },
      language: {
        title: '語言',
        description: '繁體中文',
        choose: '選擇語言',
      },
      theme: {
        title: '主題',
        description: {
          light: '淺色模式',
          dark: '深色模式',
        },
        choose: '選擇主題',
        options: {
          light: '淺色模式',
          dark: '深色模式',
        },
      },
      myStats: {
        title: '我的數據',
        description: '查看我的活動統計',
      },
      helpCenter: {
        title: '幫助中心',
        description: '獲取支援和常見問題',
      },
      privacyPolicy: {
        title: '私隱政策',
      },
      termsOfService: {
        title: '服務條款',
      },
    },
    accountRecovery: {
      title: '帳戶找回',
      description: '如果您無法訪問您的帳戶，請填寫以下表格。我們的客服團隊將在1-2個工作日內審核您的請求並與您聯繫。',
      phoneNumber: '手機號碼',
      email: '聯絡電郵',
      reason: '找回原因',
      submit: '提交申請',
      emailError: '請輸入有效的電郵地址',
      documentsTitle: '身份證明文件',
      documentsDescription: '請上傳最多2張身份證明文件的圖片 (例如：身份證、護照)。',
      addDocumentImage: '添加文件',
      uploadOptions: {
        title: '上傳文件',
        camera: '拍攝照片',
        gallery: '從相冊選擇'
      },
      errors: {
        fileTooLarge: '圖片大小不能超過10MB',
        invalidImage: '圖片格式不正確',
        capture: '拍攝照片失敗，請重試。',
        upload: '上傳圖片失敗，請重試。'
      },
      success: {
        title: '申請已提交',
        message: '您的帳戶找回申請已成功提交。我們將在1-2個工作日內通過電郵與您聯繫。',
      },
      error: {
        title: '提交失敗',
        message: '提交申請失敗，請稍後重試或聯繫客服。',
      },
    },
    version: '版本 {{version}}',
  },
  dashboard: {
    title: '主頁',
    welcome: {
      subtitle: '探索活動，連繫社群',
      greeting: '歡迎回來',
    },
    commonFeatures: {
      title: '常用功能',
      searchEvents: '搜尋活動',
      myStats: '我的統計',
      eventHistory: '活動記錄',
      helpCenter: '幫助中心',
    },
    posts: {
      title: '最新消息',
      empty: '暫無最新消息',
    },
    upcomingEvents: {
      title: '即將舉行的活動',
      empty: '暫無即將舉行的活動',
    },
    selectOrganization: '選擇組織',
    switchOrganization: '切換組織',
    allOrganizations: '所有組織',
  },
  posts: {
    detail: {
      title: '新聞詳情',
      notFound: '找不到新聞',
      lastUpdated: '最後更新：',
      postsNotFoundDesc: '抱歉，您要查看的新聞不存在或已被移除。',
      attachments: '附件',
      preview: '預覽',
      download: '下載',
      previewError: '無法預覽文件，請稍後重試。',
      downloadError: '無法下載文件，請稍後重試。',
    },
    share: {
      title: '新聞詳情',
      author: '作者',
      readMore: '閱讀更多',
      appNotInstalled: '下載我們的應用程式',
      appStoreLink: 'https://apps.apple.com/app/your-app-id',
      playStoreLink: 'https://play.google.com/store/apps/details?id=your.app.id',
    }
  },
  explore: {
    title: '探索',
    searchPlaceholder: {
      events: '搜尋活動...',
      posts: '搜尋新聞...',
      resources: '搜尋資源...',
    },
    tabs: {
      events: '活動',
      posts: '新聞',
      resources: '資源',
    },
    categories: {
      all: '全部',
      business: '商業',
      technology: '科技',
      design: '設計',
      marketing: '營銷',
    },
    free: '免費',
    attendees: '{{count}}人參加',
    loadingMore: '載入更多...',
    filters: {
      title: '篩選',
      reset: '重設',
      allEventTypes: '所有活動類型',
      allPostTypes: '所有新聞類型',
      eventTypes: '活動類型',
      postTypes: '新聞類型',
      date: {
        title: '日期',
        all: '所有日期',
        today: '今天',
        tomorrow: '明天',
        next7Days: '未來7天內',
        next30Days: '未來30天內',
        latest: '最新',
        last7Days: '最近7天',
        last30Days: '最近30天',
      },
      organization: '機構',
      selectOrganization: '選擇機構',
      price: {
        title: '價格',
        free: '免費',
        paid: '付費',
        under100: 'HK$100以下',
        under500: 'HK$500以下',
      },
    },
    noEventsTitle: '暫無活動',
    noEventsText: '未找到任何結果，請檢查您的搜尋關鍵字或篩選條件。',
    noPostsTitle: '暫無新聞',
    noPostsText: '未找到任何結果，請檢查您的搜尋關鍵字或篩選條件。',
    noResourcesTitle: '暫無資源',
    noResourcesText: '未找到任何結果，請檢查您的搜尋關鍵字或篩選條件。',

  },
  events: {
    detail: {
      title: '活動詳情',
      notFound: '找不到活動',
      eventNotFoundDesc: '抱歉，您要查看的活動不存在或已被移除。',
      description: '活動簡介',
      noDescription: '暫無活動簡介',
      participate: '參加活動',
      volunteer: '成為義工',
      eventFull: '活動已滿',
      joinWaitingList: '加入候補名單',
      free: '免費',
      governmentFunding: '政府資助項目',
      identityRequired: {
        hk_id_card: '需要香港身份證驗證',
        passport: '需要護照驗證',
        mainland_travel_permit: '需要內地出入境證件（回鄉證/通行證）驗證',
        hk_youth_plus: '需要HKYouth+驗證',
        address_proof: '需要地址證明驗證',
        student_id: '需要學生證驗證',
        home_visit: '需要家訪驗證',
        mainland_china_id_card: '需要中國居民身份證驗證',
      },
      share: '分享',
      save: '收藏',
      saved: '已收藏',
      organizer: '主辦方',
      attendees: '已報名人數',
      duration: '活動時長',
      location: '地點',
      viewDetails: '查看活動詳情',
      dateAndTime: '日期及時間',
      startDateTime: '開始時間',
      endDateTime: '結束時間',
      price: '費用',
      maxWaitingList: '最大候補名額',
      people: '人',
      idRequired: '需要身份驗證',
      video: '影片',
      attachments: '附件',
      watchVideo: '觀看影片',
      thingsToKnow: '報名須知',
      whatToExpect: '活動內容',
      eventEnded: '活動已結束',
      openMap: '打開地圖',
      chooseMapApp: '選擇地圖應用程式',
      openToMapApp: '按此打開地圖應用程式',
      addToCalendar: '按此加入至日曆',
      addToCalendarDesc: '是否要將此活動加入至日曆？',
      addToCalendarConfirm: '加入活動',
      addToCalendarSuccess: '已加入至日曆',
      addToCalendarSuccessDesc: '活動已成功加入至您的日曆',
      addToCalendarError: '加入日曆失敗',
      addToCalendarErrorDesc: '無法將活動加入至日曆，請稍後重試',
      icsDownloaded: 'ICS 檔案已下載',
      shareIcsDialogTitle: '分享日曆活動',
      shareActionTitle: '分享已啟動',
      shareActionMessage: '您的活動已準備好分享',
      calendarPermissionTitle: '日曆權限',
      calendarPermissionMessage: '此應用程式需要日曆權限才能添加活動',
      calendarPermissionDenied: '日曆權限被拒絕',
      calendarPermissionDeniedMessage: '需要日曆權限才能將活動添加到您的日曆中。請在設備設定中啟用權限。',
      calendarReadOnly: Platform.select({
        ios: '無法寫入日曆，請檢查是否已啟用 iCloud 日曆或選擇其他日曆',
        android: '無法寫入日曆，請確保已選擇 Google 日曆作為預設日曆',
      }),
      permissionRequired: '需要日曆權限',
      permissionDeniedIOS: '請前往 設定 > 隱私權與安全性 > 日曆，允許應用程式存取日曆',
      permissionDeniedAndroid: '請前往 設定 > 應用程式 > 權限，允許應用程式存取日曆',
      openSettings: '前往設定',
      invalidDateFormat: '日期格式無效，請聯絡客服',
      eventAlreadyAdded: '活動已加入',
      eventAlreadyAddedDesc: '此活動已在您的日曆中',
      verificationRequired: '需要驗證',
      verification: {
        pending: {
          title: '驗證審核中',
          message: '您的驗證目前正在審核中。完成後我們會通知您。',
        }
      },
      verificationGuide: '為確保活動的安全和質素，您仍需要完成以下驗證：\n{{items}}\n是否現在完成驗證？',
      goToVerification: '立即驗證',
      alreadyRegistered: '已報名',
      alreadyRegisteredMessage: '您已經報名參加此活動。',
      registrationSuccess: '報名成功',
      participantSuccessGuide: '您已成功報名參加此活動。您可以在「我的活動」中查看活動詳情。',
      volunteerSuccessGuide: '您的義工申請已提交。請注意：\n\n• 工作人員將審核您的申請\n• 審核通過後您將收到通知\n• 在「我的活動」中查看申請狀態',
      confirmRegistration: '確認報名',
      participantConfirmGuide: '您即將報名參加此活動，請確保您已閱讀並理解所有活動要求。',
      volunteerConfirmGuide: '您即將報名成為此活動的義工。請注意，您的申請需要經過工作人員的審核，通常需要1-2個工作日。請確保您已閱讀並理解所有義工的要求。',
      registrationFailed: '報名失敗',
      registrationFailedParticipant: '您已經報名參加此活動。',
      registrationFailedVolunteer: '您已經報名成為此活動的義工。',
      registrationFailedUnknown: '報名失敗，請稍後再試',
      duplicateRegistration: '您已經報名參加此活動。',
      duplicateVolunteerApplication: '您已經申請成為此活動的義工。',
      cancelParticipation: '取消參與活動',
      cancelParticipationDesc: '您確定要取消參與此活動嗎？',
      cancelParticipationConfirm: '確認取消',
      cancelSuccess: '已成功取消',
      cancelSuccessMessage: '您的報名已被取消',
      volunteerOpportunity: '義工機會',
      missingVerificationsError: '您需要完成以下身份驗證: {{verifications}}',
      pendingVerificationsWarning: '您有以下身份驗證正在審核中: {{verifications}}',
      verificationRequiredTitle: '需要身份驗證',
      waitlistedSuccess: '您已成功加入候補名單',
      volunteerApplicationSuccess: '義工申請提交成功',
      volunteerApplicationPending: '義工申請審核中',
      volunteerApplicationFailed: '義工申請提交失敗',
      volunteerWithdrawSuccess: '已成功取消義工申請',
      withdrawVolunteerSuccess: '義工申請已成功撤回',
      registrationError: '報名失敗',
      cancelError: '取消報名失敗',
      volunteerApplicationError: '義工申請失敗',
      volunteerJoinOrgTitle: '需要先成為組織義工',
      volunteerJoinOrgMessage: '您需要先成為「{{organizationName}}」的義工，才能為此活動申請義工職位',
      joinParticipant: '參加活動',
      cancelWaitlistSpot: '取消候補名額',
      statusPendingApproval: '等待審核',
      statusAttended: '已參加',
      registrationClosed: '報名已關閉',
      joinWaitlist: '加入候補名單',
      cancelVolunteerRole: '取消義工職位',
      volunteerConfirmed: '已確認為義工',
      applicationsClosed: '申請已關閉',
      eventCancelled: '活動已取消',
      cancelConfirmTitle: '確認取消',
      cancelParticipantConfirmMessage: '您確定要取消參加此活動嗎？',
      cancelVolunteerConfirmMessage: '您確定要取消義工申請嗎？',
      // New keys
      cancelRegistrationTitle: '取消報名',
      withdrawVolunteerTitle: '撤回義工申請',
      cancelRegistrationMessage: '您確定要取消此活動的報名嗎？',
      withdrawVolunteerMessage: '您確定要撤回此活動的義工申請嗎？',
      withdrawVolunteerError: '無法撤回義工申請',
      imageDownload: {
        downloading: '下載中...',
        permissionRequired: '需要權限',
        permissionMessage: '請允許存取相片庫以儲存圖片。',
        success: '成功',
        successMessage: '圖片已儲存至相片庫！',
        error: '錯誤',
        errorMessage: '儲存圖片失敗，請重試。',
        imageNotFound: '找不到圖片。',
      },
      cancelReasonTitle: '取消原因',
      cancelReasonPlaceholder: '請提供取消的原因（可選）',
      loadingStatus: '正在加載狀態...',
      eventNotLoaded: '活動詳情未載入，請重試。',
      loadingVolunteerStatus: '正在載入義工狀態...',
      appleMapApp: 'Apple 地圖',
      googleMapApp: 'Google 地圖',
    },
    share: {
      title: '活動詳情',
      joinUs: '一起參加這個活動吧！',
      type: '活動類型',
      participants: '參與人數',
      role: '您的角色',
      status: '狀態',
      openInApp: '在應用程式中打開',
      appNotInstalled: '下載我們的應用程式',
      appStoreLink: 'https://apps.apple.com/app/your-app-id',
      playStoreLink: 'https://play.google.com/store/apps/details?id=your.app.id',
      free: '免費',
      viewOnMap: '在Google地圖中查看',
      openInWebapp: '在網頁版中打開',
    },
  },
  my_events: {
    title: '我的活動',
    filters: {
      all: '所有日期',
      participant: '參與者',
      volunteer: '義工',
      today: '今日',
      tomorrow: '明日',
      next7days: '7日內',
      next30days: '1個月內',
      past: '過往活動',
      all_types: '所有類型',
      all_past: '所有過往活動', // New key
      select_date_range: '選擇日期範圍', // New key
      select_event_type: '選擇活動類型', // New key
      date: {
        all: '所有日期',
        today: '今日',
        tomorrow: '明日',
        next7Days: '7日內',
        next30Days: '1個月内',
      },
    },
    roleTag: {
      participant: '參與者',
      volunteer: '義工',
    },
    role_registered: '參與者',
    role_volunteering: '義工',
    status: {
      participant: '參與者',
      volunteer: '義工',
      upcoming: '即將開始',
      completed: '已完成',
      cancelled: '已取消',
      notAttended: '未出席',
      unknown: '未知狀態',
      registered: '已報名',
      waitlisted: '候補名單',
      pending_approval: '等待審批',
      pending: '等待審批',
      approved: '已批准',
      attended: '已參加',
      rejected: '已拒絕',
      withdrawn: '已撤回',
      absent: '缺席',
      volunteerPending: '等待審批',
      volunteerApproved: '已批准',
      volunteerRejected: '已拒絕',
      // Additional statuses found in EventDetailsScreen
      cancelled_by_user: '您已取消',
      cancelled_by_admin: '主辦方取消',
      rejected_approval: '審批被拒',
      pending_payment: '待付款',
      confirmed: '已確認',
      applied: '已申請',
      withdrawn_by_user: '您已撤回',
      rejected_by_host: '主辦方拒絕',
      // Additional volunteer-specific statuses
      confirmed_volunteer: '已確認為義工',
      attended_volunteer: '已參與志願服務',
      absent_volunteer: '義工缺席',
      cancelled_volunteer: '已取消義工角色',
      withdrawn_volunteer: '已撤回義工申請',
    },
    calendar: {
      title: '我的日曆',
      empty: '暫無活動安排',
      switchView: '切換視圖',
    },
    empty: {
      title: '暫無活動',
      description: '開始探索並參與活動吧！',
    },
    past: {
      title: '過往活動',
      empty: '暫無過往活動',
    },
    noEvents: "暫無活動",
    noUpcomingEvents: "暫無即將舉行的活動",
    noPastEvents: "暫無過往活動",
    noDate: '未知日期',
    loading_title: '載入中...',
    cancelRegistration: '取消報名',
    cancelConfirmTitle: '確認取消',
    cancelConfirmMessage: '確定要取消此活動報名嗎？',
    cancelConfirmTitle_registered: '確認取消',
    cancelConfirmMessage_registered: '確定要取消此活動報名嗎？',
    cancelConfirmTitle_volunteer: '確認取消志願服務',
    cancelConfirmMessage_volunteer: '您確定要取消此活動的志願服務嗎？',
    cancelSuccess: '報名已成功取消。',
    cancelSuccess_registered: '活動報名已成功取消。',
    cancelSuccess_volunteer: '義工申請已成功取消。',
    cancelError: '無法取消報名，請重試。',
    paymentStatusLabel: '付款狀態',
    paymentStatus: {
      not_required: '無需付款',
      pending: '待付款',
      completed: '已完成',
      failed: '失敗',
      refunded: '已退款',
    },
    cancelRegistrationButton: '取消報名',
    withdrawApplicationButton: '撤回申請',
    registered: '已報名活動',
    volunteering: '志願服務活動',
    filter_by_date: '按日期篩選',
    filter_by_type: '按類型篩選',
    select_organization_loading: '正在載入機構...',
    no_organization_selected: '未選擇機構',
  },
  resources: {
    title: '資源詳情',
    files: '個附件',
    file: '個附件',
    attachment: '附件',
    lastUpdated: '最後更新：',
    description: '描述',
    download: '下載',
    noResourcesTitle: '暫無資源',
    noResourcesText: '目前沒有可用的資源。請稍後再查看。',
    resourceNotFound: '找不到資源',
    errorLoadingResource: '載入資源時出錯',
    invalidResourceId: '無效的資源ID',
    invalidOrgId: '無效的組織ID',
    downloadError: "下載時發生錯誤"
  },
  qrcode: {
    title: '會員二維碼',
    subtitle: '在活動場地出示此二維碼以簽到',
    username: '用戶名',
    memberId: '會員編號',
    defaultUsername: '未知用戶',
    loginRequired: '請先登入以查看二維碼',
    scanHint: '請在活動場地出示此二維碼',
    error: '生成二維碼失敗',
    retry: '重新嘗試',
    tips: {
      title: '重要提示',
      content: '為保護您的私隱，請勿與工作人員以外的任何人分享您的二維碼。如有任何疑問，請聯絡我們。'
    },
    verification: {
      validTitle: '有效二維碼',
      invalidTitle: '無效二維碼',
      checkedInTitle: '簽到成功',
      memberFound: '已找到會員ID：',
      approvePrompt: '是否為此會員進行簽到？',
      checkedInPrompt: '會員已成功簽到！',
      invalidMessage: '掃描的二維碼不是有效的會員碼。',
      loading: '正在加載活動詳情...',
      checkIn: '活動簽到',
      memberId: '會員ID',
      memberName: '姓名',
      username: '用戶名',
      phone: '電話號碼',
      eventId: '活動ID',
      eventName: '活動名稱',
      eventDate: '日期和時間',
      eventLocation: '地點',
      role: '角色',
      status: '狀態'
    }
  },
  eventManagement: {
    title: '活動管理',
    howToJoin: '如何參加',
    steps: {
      arrival: '到達活動地點',
      showQRCode: '向工作人員或義工出示二維碼',
    },
    showQRCode: '顯示二維碼',
    openQRCode: '打開二維碼',
    qrCodeHint: '你也可以從底部狀態欄快速打開二維碼',
    registrationInfo: '報名資料',
    registrationId: '報名編號',
    registrationDate: '報名日期',
    contactPerson: '聯絡人',
    contactPhone: '聯絡電話',
    contactEmail: '聯絡電郵',
    specialNotes: '特別注意事項',
    clickToAddCalendar: '點擊下方卡片加入至日曆',
    status: {
      title: "活動狀態"
    },
    registrationStatusTitle: "您的報名狀態",
    error: {
      notFound: '找不到報名詳細資料。',
      loadFailed: '無法載入報名詳細資料。',
      noId: '缺少報名編號。',
      locationOrTitleMissing: '缺少活動地點或標題，無法提供地圖導航。'
    },
    cancelSuccessTitle: "取消成功",
    cancelSuccessMessage: "您的報名已成功取消。",
    cancelErrorTitle: "取消失敗",
    cancelErrorMessage: "無法取消您的報名，請重試。",
    mapError: "無法打開地圖",
    mapErrorDesc: "無法打開地圖應用程式，請確保您已安裝地圖應用程式。",
    mapErrorDescSystem: "系統錯誤：無法構建有效的地圖鏈接。",
    addToCalendar: "加入日曆",
    getDirections: "獲取路線",
    viewEventDetails: "查看活動詳情",
    cancelParticipation: "取消參與",
    dateTime: "日期與時間",
    location: "地點",
    locationNotAvailable: "地點資料不可用",
    cancelButton: "取消報名",
    withdrawButton: "撤回申請",
    cancelConfirm: {
      title: "確認取消",
      message: "確定要取消此活動的報名嗎？"
    },
    withdrawConfirm: {
      title: "確認撤回",
      message: "確定要撤回此活動的義工申請嗎？"
    },
    cancelReason: {
      title: "取消原因",
      placeholder: "請提供取消原因（可選）"
    },
    cancelSuccess: {
      title: "取消成功",
      message: "您的報名已成功取消。"
    },
    cancelError: {
      title: "取消失敗",
      message: "無法取消您的報名，請重試。"
    },
    withdrawSuccess: {
      title: "撤回成功",
      message: "您的義工申請已成功撤回。"
    },
    withdrawError: {
      title: "撤回失敗",
      message: "無法撤回您的申請，請重試。"
    }
  },
  helpCenter: {
    searchPlaceholder: '搜尋常見問題...',
    faq: {
      categories: {
        accountSecurity: '帳戶安全',
        identityVerification: '身份認證',
        qrCode: '二維碼使用',
        eventManagement: '活動管理',
        notifications: '通知設置'
      },
      items: {
        changePhone: {
          question: '如何更改手機號碼？',
          answer: '在「我的」頁面中點擊「帳戶設置」，選擇「更改手機號碼」選項。按照提示完成驗證後即可更改。\n如果您在更改過程中遇到問題，請聯繫客服熱線 +852 2222 2234。'
        },
        smsIssue: {
          question: '無法使用原有手機號碼登入怎麼辦？',
          answer: '如果您的手機號碼已無法使用，導致無法接收驗證碼或無法登入帳戶，請立即聯繫客服熱線 +852 2222 2234 尋求協助。我們的客服人員會核實您的身份並協助您重新獲得帳戶的訪問權限。'
        },
        accountStolen: {
          question: '發現帳戶被盜用怎麼辦？',
          answer: '如果您仍能登入帳戶：\n1. 立即更改密碼\n2. 聯繫客服熱線 +852 2222 2234 報告情況\n\n如果您無法登入帳戶：\n請直接聯繫客服熱線 +852 2222 2234，我們會立即凍結您的帳戶並協助您重新取得帳戶控制權。'
        },
        identityVerification: {
          question: '如何完成身份認證？',
          answer: '身份認證包括身份證件認證和地址認證兩個部分：\n\n1. 身份證件認證：\n• 上傳香港身份證正反面照片\n• 等待審核（1-2個工作天）\n\n2. 地址認證：\n• 上傳任一地址證明文件\n• 等待審核（1-2個工作天）\n\n注意：兩項認證都完成後，整體身份認證即通過。'
        },
        verificationStatus: {
          question: '如何查看認證狀態？',
          answer: '在「我的」頁面可查看：\n\n1. 身份證件認證：未認證/審核中/已認證\n2. 地址認證：未認證/審核中/已認證\n\n整體身份認證狀態：\n• 未認證：任一項未完成\n• 已認證：兩項都已通過'
        },
        addressVerification: {
          question: '如何完成地址認證？',
          answer: '1. 在「我的」頁面點擊「地址認證」\n2. 上傳以下其中一種文件：\n   • 銀行月結單\n   • 公共服務賬單\n   • 手機賬單\n   • 其他文件\n\n文件要求：\n• 清晰顯示您的姓名和地址\n• 使用中文或英文'
        },
        volunteerVerification: {
          question: '如何成為義工？',
          answer: '要成為義工，您需要：\n1. 首先完成身份認證\n2. 在活動詳情頁面點擊「成為義工」\n3. 等待義工資格審核（約1-3個工作天）\n\n審核通過後，您就可以報名參加義工活動了。如有任何問題，請聯繫客服熱線 +852 2222 2234。'
        },
        qrCodeInfo: {
          question: '什麼是會員二維碼？',
          answer: '會員二維碼是您在活動現場的電子身份證明。每位會員都有獨特的二維碼，用於向工作人員證明您的身份。請注意，這不是活動票據，您可以通過點擊底部導航欄中間的二維碼圖標快速顯示。'
        },
        qrCodeUsage: {
          question: '活動現場如何使用二維碼？',
          answer: '到達活動現場後，點擊底部導航欄中間的二維碼圖標，向工作人員出示您的會員二維碼即可完成身份驗證。\n請注意：二維碼僅用於身份驗證，不是活動票據。'
        },
        checkRegistered: {
          question: '如何查看我已報名的活動？',
          answer: '在底部導航欄點擊「我的活動」，您可以看到所有已報名的活動，包括作為參加者和義工的活動。'
        },
        cancelRegistration: {
          question: '如何取消活動報名？',
          answer: '進入「我的活動」，點擊要取消的活動，在活動管理頁面底部可以找到取消報名的選項。建議您提前至少24小時取消，以便其他人報名參加。'
        },
        addCalendar: {
          question: '如何將活動加入日曆？',
          answer: '在活動管理頁面中，點擊日期部分即可將活動加入到您的手機日曆中，方便及時獲得活動提醒。'
        },
        notifications: {
          question: '如何管理推送通知？',
          answer: '在「我的」頁面中，點擊「偏好設置」下的「通知」選項，您可以自定義接收哪些類型的通知，包括活動提醒、審核通知等。'
        }
      }
    }
  },
  privacyPolicy: {
    lastUpdated: '最後更新：2025年1月16日',
    sections: [
      {
        title: '1. 資料收集',
        content: '我們收集的個人資料包括但不限於：姓名、電話號碼、電子郵件地址、香港身份證資料（僅用於身份驗證）、活動參與記錄等。這些資料用於提供服務、改善用戶體驗、確保活動安全，以及履行我們的法律義務。'
      },
      {
        title: '2. 資料使用',
        content: '我們使用收集的資料來：管理您的帳戶、處理活動報名、發送活動提醒、進行身份驗證、提供客戶支援，以及確保平台安全。未經您的同意，我們不會將您的個人資料用於其他目的。'
      },
      {
        title: '3. 資料保護',
        content: '我們採用業界標準的加密技術和安全措施來保護您的個人資料。只有經授權的工作人員才能訪問這些資料，且僅限於執行其工作職責所需的範圍。'
      },
      {
        title: '4. 資料分享',
        content: '除非法律要求或獲得您的明確同意，我們不會與第三方分享您的個人資料。在某些情況下，我們可能會與活動合作夥伴分享必要的資料，但僅限於舉辦活動所需的範圍。'
      },
      {
        title: '5. 您的權利',
        content: '您有權查看、更正您的個人資料，以及要求刪除您的帳戶。如需行使這些權利，請通過客戶服務聯繫我們。請注意，某些資料可能因法律要求而無法刪除。'
      }
    ],
    footer: '如果您對我們的私隱政策有任何疑問，請聯繫我們的客戶服務部門。'
  },
  termsOfService: {
    lastUpdated: '最後更新日期：2025年1月16日',
    introduction: '請仔細閱讀以下條款，這些條款構成您與我們之間具有法律約束力的協議。',
    sections: [
      {
        title: '1.服務範圍',
        content: '我們提供會員管理、活動報名、身份認證等服務。您需要完成身份認證才能使用完整的會員功能，包括報名參加活動和成為義工。我們保留隨時修改、暫停或終止服務的權利。'
      },
      {
        title: '2.會員資格',
        content: '您必須持有有效的香港身份證才能註冊成為會員。您應當提供真實、準確、完整的個人資料，並在資料發生變更時及時更新。'
      },
      {
        title: '3.身份認證',
        content: '為確保服務質量和安全，您需要完成身份認證程序。這包括上傳您的香港身份證照片和其他必要文件。我們將對您提供的資料進行審核，並保留拒絕認證的權利。'
      },
      {
        title: '4.活動參與',
        content: '您可以報名參加平台上發佈的活動。一旦報名成功，您應當遵守活動的具體規則和要求。如需取消參與，請提前通知主辦方。對於義工崗位，您必須完成身份認證才能報名。'
      },
      {
        title: '5.帳號安全',
        content: '您有責任保護您的帳號和密碼安全，不得將帳號借給他人使用。如發現帳號被盜用，請立即聯繫客服處理。您應當定期更改密碼，並確保手機號碼等聯繫方式的有效性。'
      },
      {
        title: '6.隱私保護',
        content: '我們重視您的隱私，並按照隱私政策保護您的個人資料。我們只會在必要的範圍內使用您的資料，未經您的同意，不會向第三方披露您的個人資料。'
      },
      {
        title: '7.免責聲明',
        content: '我們不對因網絡問題、系統維護、不可抗力等原因導致的服務中斷承擔責任。對於活動中可能發生的意外或損失，我們建議您購買相應的保險。'
      }
    ],
    footer: '如您對本服務條款有任何疑問，請聯繫我們的客服團隊。電話：+852 2222 2234，電郵：<EMAIL>'
  },
  category: {
    title: '活動類型',
    community: '社區服務',
    technology: '科技創新',
    education: '教育發展',
    culture: '文化藝術',
    environment: '環境保護',
    health: '健康醫療',
    business: '商業活動',
    design: '設計創作',
    marketing: '市場營銷',
    infrastructure: '基礎建設',
    youth: '青年發展',
  },
  notifications: {
    title: '通知設置',
    channels: {
      title: '通知渠道',
      app: '應用程式通知',
      appDescription: '透過應用程式接收通知',
      whatsapp: 'WhatsApp 通知',
      whatsappDescription: '透過 WhatsApp 接收通知',
    },
    types: {
      title: '通知類型',
      events: '活動提醒',
      eventsDescription: '接收活動提醒和變更通知',
      posts: '新聞更新',
      postsDescription: '接收最新新聞和公告',
      promotional: '推廣訊息',
      promotionalDescription: '接收推廣優惠和消息'
    }
  },
  elderlyMode: {
    title: '老友記模式',
    description: '開啟大字體及其他無障礙功能',
    fontSize: '字體大小',
    fontSizeDescription: '選擇適合您的字體大小，讓內容更容易閱讀',
    note: '註：字體大小的更改將在重新啟動應用後生效',
    options: {
      standard: '標準',
      larger: '較大',
      large: '大',
      extraLarge: '特大',
    },
  },
  elderlySettings: {
    title: '顯示設定',
    selectMode: '請選擇使用模式',
    modes: {
      normal: {
        label: '標準模式',
        desc: '標準字體與操作界面'
      },
      senior: {
        label: '老友記',
        desc: '放大字體與簡化界面'
      }
    },
    preview: {
      title: '顯示效果預覽',
      fontSize: '字號：{{size}}pt',
      event: {
        free: '免費活動',
        attendees: '{{count}}人已報名'
      }
    }
  },
  applications: {
    status: {
      header: '申請狀態概覽',
      title: '申請狀態',
      subtitle: '申請參與並檢查您的各種計劃和服務。',
      info: {
        title: '重要資訊',
        text: '申請通常需要1-3個工作日進行審核。當您的申請狀態有更新時，您將會收到通知。'
      },
      verified: '已驗證',
      pending: '審核中',
      rejected: '已拒絕',
      unverified: '尚未申請',
      error: '錯誤',
      documents: '所需文件',
      reason: '原因',
    },
    prerequisites: {
      hkidRequired: {
        title: '香港身份證驗證要求',
        message: '要申請此服務，您的香港身份證必須先經過驗證。請完成香港身份證驗證。',
        short: '需要香港身份證驗證',
        verifyNow: '立即驗證'
      }
    },
    types: {
      hkid: '香港身份證',
      mainlandId: '內地身份證',
      mainlandTravelPermit: '回鄉證或內地通行證',
      passport: '護照',
      hkYouth: 'HK Youth+',
      address: '地址驗證',
      studentId: '學生證',
      homeVisit: '家訪計劃',
      volunteer: '義工計劃',
    },
    requirements: {
      identityDocument: '身份文件認證',
      addressVerification: '地址認證'
    },
    homeVisit: {
      title: '家訪計劃',
      description: '申請獲得日常任務的協助及義工的定期家訪'
    },
    volunteer: {
      title: '義工計劃',
      description: '申請成為義工，幫助社區中的其他人',
      status: {
        title: '義工申請',
        subtitle: '查看您在不同組織的義工申請狀態',
        organizations: '組織機構',
        applications: '當前申請',
        availableOrganizations: '可申請的組織',
        approved: '已批准',
        pending: '審核中',
        rejected: '已拒絕',
        notApplied: '未申請',
        error: '錯誤',
        noOrganizations: '目前沒有可用的組織',
        noApplications: '尚未提交義工申請',
        noOrganizationsAvailable: '暫無更多可申請的組織',
        unknownOrganization: '未知組織'
      }
    },
    forms: {
      common: {
        softCopy: '電子副本',
        chineseName: '中文姓名',
        chineseCommercialCode: '中文電碼',
        englishName: '英文姓名',
        gender: '性別',
        dateOfBirth: '出生日期',
        expiryDate: '有效期限',
        issueDate: '簽發日期',
        documentNumber: '證件號碼',
        permanentResident: '永久性居民',
        yes: '是',
        no: '否',
        male: '男',
        female: '女',
      },
      hkid: {
        title: '香港身份證認證',
        idNumber: '身份證號碼',
      },
      mainlandId: {
        title: '內地身份證認證',
        idNumber: '身份證號碼',
      },
      mainlandTravelPermit: {
        title: '回鄉證/內地通行證認證',
        permitNumber: '證件號碼',
      },
      passport: {
        title: '護照認證',
        passportNumber: '護照號碼',
        issuingCountry: '護照所屬地',
      },
      hkYouth: {
        title: 'HK Youth+ 認證',
        membershipNumber: '會員號碼',
      },
      address: {
        title: '地址認證',
        unit: '單位',
        floor: '樓層',
        building: '大廈名稱/門牌號',
        street: '街道地址',
        district: '地區',
        region: '區域',
      },
      studentId: {
        title: '學生證認證',
        schoolName: '學校名稱',
        grade: '年級',
        expiryDate: '到期日期',
      },
    },
  },
  programApplications: {
    title: "申請計劃",
    subtitle: "申請參與並檢查您的各種計劃和服務。",
    labels: {
      status: "申請狀態"
    },
    status: {
      approved: "已批准",
      pending: "審核中",
      rejected: "已拒絕",
      unverified: "未申請",
      error: "錯誤"
    },
    messages: {
      approved: "您的申請已獲批准。您可以瀏覽活動並申請成為義工。如需更新驗證資料，請聯絡客戶服務部重設驗證狀態。",
      pending: "您的驗證申請正在處理中，預計需時3-5個工作天。我們將透過電郵通知您審核結果。",
      rejected: "您的申請已被拒絕。請查看拒絕原因。",
      unverified: "您尚未申請此計劃。",
      volunteerSubmitSuccess: "義工申請已成功提交！",
      homeVisitSubmitSuccess: "家訪計劃申請已成功提交！",
      verificationRequired: "申請前需完成身份及地址驗證",
      verificationRequiredTitle: "需要完成驗證",
      verificationRequiredDesc: "您必須完成身份證及地址驗證才能申請計劃。",
      duplicateVolunteerOrgApplication: "您已經申請成為此組織的義工。",
      submitError: "申請提交失敗，請稍後再試。"
    },
    buttons: {
      applyNow: "立即申請",
      completeVerification: "完成驗證"
    },
    volunteer: {
      title: "申請義工計劃",
      organization: "當前申請組織",
      description: "申請成為義工，為社區活動帶來溫暖與幫助。"
    },
    homeVisit: {
      title: "家訪計劃",
      description: "申請我們的家訪計劃，讓關懷到您家中。"
    },
    modal: {
      userInformation: "您的資料",
      termsAndConditions: "條款和條件",
      agreeToTerms: "我已閱讀並同意條款和條件",
      submit: "提交申請",
      messages: {
        agreementRequired: "提交前請同意條款和條件"
      },
      terms: {
        responsibilities: "責任",
        requirements: "要求",
        benefits: "福利",
        eligibility: "資格標準",
        services: "提供的服務",
        frequency: "訪問頻率",
        policy: "隱私政策",
        volunteerResponsibilities: "作為義工，您將協助活動管理工作，包括協助用戶簽到、維持活動秩序等。",
        volunteerRequirements: "義工必須完成身份驗證，並遵守活動管理規定。",
        volunteerBenefits: "義工將獲得服務社區的機會，累積活動管理經驗。",
        homeVisitEligibility: "要獲得家訪資格，您必須是已註冊會員，已驗證身份，並有記錄的協助需求。",
        homeVisitServices: "我們的團隊可以協助基本家務，提供必需品，並提供陪伴服務。",
        homeVisitFrequency: "訪問頻率根據個人需求確定，通常每週到每月不等。",
        homeVisitPolicy: "在申請過程和訪問中共享的所有個人信息都將保密。"
      }
    }
  },
  verificationTypes: {
    hk_id_card: "香港身份證",
    student_id: "學生證",
    mainland_china_id_card: "中國內地居民身份證",
    mainland_travel_permit: "港澳居民來往內地通行證(回鄉證)",
    passport: "護照",
    hk_youth_plus: "HK Youth+",
    address_proof: "地址證明",
    home_visit: "家訪"
    // Add other verification type friendly names as they are defined in mapEventVerificationKeyToEnum
  },
  sharing: {
    shareFailedError: '嘗試分享活動時發生錯誤'
  },
  verification: {
    pending: {
      title: '驗證審核中',
      message: '您的驗證目前正在審核中。完成後我們會通知您。',
    }
  },
  camera: {
    permissionDenied: {
      title: '相機權限被拒絕',
      message: '請在設置中允許應用程式使用相機以掃描二維碼'
    },
    requestingPermission: '正在請求相機權限...',
    noAccess: '無法使用相機'
  },
  volunteer: {
    assistOthers: '為他人验证',
    scanInstructions: '請將攝像頭對準會員的二維碼來協助他們申請身份驗證',
    proceedWithApplication: '繼續申請',
    proceedWithVolunteerApplication: '繼續義工申請',
    memberFound: {
      title: '找到會員',
      message: '是否要為此會員繼續申請身份驗證？',
      volunteerMessage: '是否要為此會員繼續義工申請協助？',
      memberId: '會員ID'
    },

    assistingApplications: {
      title: '協助申請身份驗證',
      subtitle: '正在為會員 {{memberId}} 查看身份驗證狀態',
      volunteer: {
        title: '協助義工申請',
        subtitle: '正在為會員 {{memberId}} 查看義工申請狀態'
      }
    },
  }
};