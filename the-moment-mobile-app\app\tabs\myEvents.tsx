import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Platform,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { Card, MD3Theme } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { parseISO, isToday, isTomorrow, addDays, startOfDay, isAfter, isBefore, format as formatDateFns, Locale } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import DropDownPicker from 'react-native-dropdown-picker';
import { useRouter, Stack, useFocusEffect, useLocalSearchParams } from 'expo-router';
import { useIsFocused } from '@react-navigation/native';
import axios from 'axios';
import { toZonedTime } from 'date-fns-tz';

import { appStyleStore } from 'stores/app_style_store';
import { organizationStore } from 'stores/organization_store';
import { registeredEventsListStore } from 'stores/user_events_store';
import { userEventVolunteerApplicationsListStore } from 'stores/volunteer_store';
import {
  EventRegistrationPayload,
  MediaItemPayload,
  EventListPayloadDetails,
  EventVolunteerApplicationPayload,
  RegisteredEventsListPullRequest,
  UserEventVolunteerApplicationsListPullRequest,
} from '@/api/api_config';
import { useFetchRegisteredEventsList, useCancelEventRegistration } from '@/api/user_events_services';
import { useFetchUserEventVolunteerApplicationsList, useWithdrawEventVolunteer } from '@/api/volunteer_services';
import { CustomDialog } from '@/common_modules/CustomDialog';

type DateFilter = 'all' | 'today' | 'tomorrow' | 'next7days' | 'next30Days' | 'past';

const getValidImageUrl = (filePath: string | undefined): string | undefined => {
  if (!filePath) return undefined;
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }
  return filePath;
};

// Add locale and date formatting functions
const getLocale = (language: string): Locale => {
  switch (language.toLowerCase().split('-')[0]) {
    case 'zh':
      return zhCN;
    default:
      return enUS;
  }
};

const formatEventDate = (dateString: string | undefined, language: string): string => {
  if (!dateString) return '';
  try {
    const timeZone = 'Asia/Hong_Kong';
    const parsedDate = parseISO(dateString);
    const zonedDate = toZonedTime(parsedDate, timeZone);
    return formatDateFns(zonedDate, 'yyyy-MM-dd HH:mm', { locale: getLocale(language) });
  } catch (error) {
    console.warn('Error formatting date:', error);
    return '';
  }
};

// Status color constants
const STATUS_COLORS = {
  // Success/Approved - Deep green (readable)
  SUCCESS: '#0D7E2A',        // Dark forest green
  // Participation - Blue  
  PARTICIPATION: '#1890FF',  // Professional blue
  // Waiting/Pending - Orange (not yellow to avoid eye strain)
  WAITING: '#D46B08',        // Dark orange
  // Rejected/Absent - Red
  REJECTED: '#CF1322',       // Strong red
  // Cancelled/Withdrawn - Gray (dull meaning)
  CANCELLED: '#595959',      // Medium gray
  // Default
  DEFAULT: '#8C8C8C'         // Light gray
};

const STATUS_CATEGORIES: { [key: string]: 'success' | 'participation' | 'waiting' | 'rejected' | 'cancelled' } = {
  // Statuses from EventRegistrationStatusType (for participants and some volunteers)
  'attended': 'success',
  'confirmed': 'success',
  'approved': 'success', // Primarily for volunteers, but maps to success
  'completed': 'success', // General completion status

  'registered': 'participation',
  
  'waitlisted': 'waiting',
  'pending_approval': 'waiting', // For participants
  'pending': 'waiting',          // For volunteers
  'applied': 'waiting',          // Initial volunteer status

  'absent': 'rejected',
  'rejected_approval': 'rejected', // For participants
  'rejected': 'rejected',          // For volunteers
  'rejected_by_host': 'rejected',  // For volunteers

  'cancelled_by_user': 'cancelled',
  'cancelled_by_admin': 'cancelled',
  'cancelled_by_system': 'cancelled',
  'withdrawn_by_user': 'cancelled',   // For volunteers
};

const getStatusColorAndCategory = (status: string) => {
  const category = STATUS_CATEGORIES[status];

  switch (category) {
    case 'success':
      return { color: STATUS_COLORS.SUCCESS, category: 'success' };
    case 'participation':
      return { color: STATUS_COLORS.PARTICIPATION, category: 'participation' };
    case 'waiting':
      return { color: STATUS_COLORS.WAITING, category: 'waiting' };
    case 'rejected':
      return { color: STATUS_COLORS.REJECTED, category: 'rejected' };
    case 'cancelled':
      return { color: STATUS_COLORS.CANCELLED, category: 'cancelled' };
    default:
      if (status?.startsWith('cancel') || status?.startsWith('withdrawn')) {
        return { color: STATUS_COLORS.CANCELLED, category: 'cancelled' };
      }
      return { color: STATUS_COLORS.DEFAULT, category: 'default' };
  }
};

const getStatusBackgroundColor = (statusColor: string): string => {
  switch (statusColor) {
    case STATUS_COLORS.SUCCESS: return '#F6FFED';      // Light green background
    case STATUS_COLORS.PARTICIPATION: return '#E6F4FF'; // Light blue background  
    case STATUS_COLORS.WAITING: return '#FFF7E6';       // Light orange background
    case STATUS_COLORS.REJECTED: return '#FFF2F0';      // Light red background
    case STATUS_COLORS.CANCELLED: return '#F5F5F5';     // Light gray background
    default: return '#F5F5F5';                          // Default light gray
  }
};

const MyEventsScreen = () => {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<{ dateFilter?: string }>();
  const isFocused = useIsFocused();
  const theme = appStyleStore(state => state.theme) as MD3Theme;
  const insets = useSafeAreaInsets();
  const styles = createStyles(theme, insets);
  
  const selectedOrgId = organizationStore((state) => state.selectedOrganization?.id);

  const storeRegisteredEventsData = registeredEventsListStore(state => state.registeredEventsList);
  const isLoadingRegisteredEventsFromStore = registeredEventsListStore(state => state.isFetching);
  const registeredEventsErrorFromStore = registeredEventsListStore(state => state.error);

  const storeVolunteerApplicationsListData = userEventVolunteerApplicationsListStore(state => state.applicationsList);
  const isLoadingVolunteerApplicationsFromStore = userEventVolunteerApplicationsListStore(state => state.isFetching);
  const volunteerApplicationsErrorFromStore = userEventVolunteerApplicationsListStore(state => state.error);

  const [registeredEvents, setRegisteredEvents] = useState<(EventRegistrationPayload & { image_url?: string })[]>([]);
  const [volunteerApplications, setVolunteerApplications] = useState<(EventVolunteerApplicationPayload & { event_image_url?: string })[]>([]);

  // Initialize date filter from route params if available
  const [selectedDateFilter, setSelectedDateFilter] = useState<DateFilter>(() => {
    const paramDateFilter = params.dateFilter as DateFilter;
    return paramDateFilter && ['all', 'today', 'tomorrow', 'next7days', 'past'].includes(paramDateFilter) 
      ? paramDateFilter 
      : 'all';
  });
  
  // Dropdown open state
  const [dateDropdownOpen, setDateDropdownOpen] = useState(false);

  const [refreshing, setRefreshing] = useState(false);

  // Separate dialog states for registered events and volunteer applications
  const [isCancelRegistrationDialogVisible, setIsCancelRegistrationDialogVisible] = useState(false);
  const [registrationToCancel, setRegistrationToCancel] = useState<string | null>(null);
  const [isCancellingRegistration, setIsCancellingRegistration] = useState(false);
  
  const [isCancelVolunteerDialogVisible, setIsCancelVolunteerDialogVisible] = useState(false);
  const [volunteerApplicationToCancel, setVolunteerApplicationToCancel] = useState<string | null>(null);
  
  const [messageDialogVisible, setMessageDialogVisible] = useState(false);
  const [messageDialogType, setMessageDialogType] = useState<'success' | 'error'>('success');
  const [messageDialogTitle, setMessageDialogTitle] = useState('');
  const [messageDialogContent, setMessageDialogContent] = useState('');

  const showMessage = (title: string, message: string, type: 'success' | 'error') => {
    setMessageDialogTitle(title);
    setMessageDialogContent(message);
    setMessageDialogType(type);
    setMessageDialogVisible(true);
  };

  const { refetch: refetchRegisteredEvents } = useFetchRegisteredEventsList({ limit: 100, offset: 0 });
  const { refetch: refetchVolunteerApplications } = useFetchUserEventVolunteerApplicationsList({ limit: 100, offset: 0 });
  
  const { mutate: cancelEventRegistration } = useCancelEventRegistration();
  const { mutateAsync: withdrawVolunteerApplicationMutation, isPending: isWithdrawingVolunteerApp } = useWithdrawEventVolunteer();

  // Update filters when route params change
  useEffect(() => {
    if (params.dateFilter) {
      const paramDateFilter = params.dateFilter as DateFilter;
      if (['all', 'today', 'tomorrow', 'next7days', 'past'].includes(paramDateFilter)) {
        setSelectedDateFilter(paramDateFilter);
      }
    }
  }, [params.dateFilter]);

  useEffect(() => {
    if (isFocused && selectedOrgId) {
      if (!isLoadingRegisteredEventsFromStore && (!storeRegisteredEventsData || storeRegisteredEventsData.length === 0)) {
        refetchRegisteredEvents?.();
      }
      if (!isLoadingVolunteerApplicationsFromStore && (!storeVolunteerApplicationsListData || storeVolunteerApplicationsListData.length === 0)) {
        refetchVolunteerApplications?.();
      }
    } else if (!selectedOrgId && isFocused) {
      setRegisteredEvents([]);
      setVolunteerApplications([]);
    }
  }, [isFocused, selectedOrgId, refetchRegisteredEvents, refetchVolunteerApplications, isLoadingRegisteredEventsFromStore, isLoadingVolunteerApplicationsFromStore, storeRegisteredEventsData, storeVolunteerApplicationsListData]);

  useEffect(() => {
    if (selectedOrgId && storeRegisteredEventsData) {
      const filtered = storeRegisteredEventsData.filter((event: EventRegistrationPayload) => event.event_organization_id === selectedOrgId);
      const processedEvents = filtered.map((event: EventRegistrationPayload) => {
        const bannerItem = event.media_items?.find((m: MediaItemPayload) => m.is_banner);
        const imageUrl = bannerItem?.file_path || event.media_items?.[0]?.file_path || undefined;
        return {
          ...event,
          image_url: getValidImageUrl(imageUrl)
        };
      });
      setRegisteredEvents(processedEvents);
    } else if (!isLoadingRegisteredEventsFromStore || !selectedOrgId) {
      setRegisteredEvents([]);
    }
  }, [storeRegisteredEventsData, isLoadingRegisteredEventsFromStore, selectedOrgId]);

  useEffect(() => {
    if (selectedOrgId && storeVolunteerApplicationsListData) {
      const filtered = storeVolunteerApplicationsListData.filter((app: EventVolunteerApplicationPayload) => app.event_organization_id === selectedOrgId);
      const processedApplications = filtered.map((app: EventVolunteerApplicationPayload) => {
        const bannerItem = app.media_items?.find((m: MediaItemPayload) => m.is_banner);
        const imageUrl = bannerItem?.file_path || app.media_items?.[0]?.file_path || undefined;
        return {
          ...app,
          event_image_url: getValidImageUrl(imageUrl)
        };
      });
      setVolunteerApplications(processedApplications);
    } else if (!isLoadingVolunteerApplicationsFromStore || !selectedOrgId) {
      setVolunteerApplications([]);
    }
  }, [storeVolunteerApplicationsListData, isLoadingVolunteerApplicationsFromStore, selectedOrgId]);
  
  useFocusEffect(
    useCallback(() => {
      if (selectedOrgId) {
        refetchRegisteredEvents?.();
        refetchVolunteerApplications?.();
      }
    }, [selectedOrgId, refetchRegisteredEvents, refetchVolunteerApplications])
  );
  
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    if (isFocused && selectedOrgId) {
      const combinedEventsForPolling = [
        ...registeredEvents.map(e => ({ ...e, type: 'registered' as const })),
        ...volunteerApplications.map(v => ({ ...v, type: 'volunteering' as const }))
      ];
      const hasWaitlistedItems = combinedEventsForPolling.some(item => item.status === 'waitlisted' || item.status === 'pending_approval');

      if (hasWaitlistedItems) {
        intervalId = setInterval(() => {
          refetchRegisteredEvents?.();
          refetchVolunteerApplications?.();
        }, 60000);
      }
    }
    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isFocused, selectedOrgId, registeredEvents, volunteerApplications, refetchRegisteredEvents, refetchVolunteerApplications]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    if (selectedOrgId) {
      try {
        await refetchRegisteredEvents?.();
        await refetchVolunteerApplications?.();
      } catch (error) {
        console.error("Error during refresh:", error);
      }
    }
    setRefreshing(false);
  }, [selectedOrgId, refetchRegisteredEvents, refetchVolunteerApplications]);

  const dateOptions = [
    { label: t('my_events.filters.all'), value: 'all' },
    { label: t('my_events.filters.past'), value: 'past' },
    { label: t('my_events.filters.today'), value: 'today' },
    { label: t('my_events.filters.tomorrow'), value: 'tomorrow' },
    { label: t('my_events.filters.next7days'), value: 'next7days' },
  ];

  const getCombinedAndFilteredData = () => {
    // Combine both registered events and volunteer applications
    const regTyped = registeredEvents.map(e => ({ ...e, type: 'registered' as const, uniqueId: e.id || e.event_id + '_reg' }));
    const volTyped = volunteerApplications.map(v => ({ ...v, type: 'volunteering' as const, uniqueId: v.id || v.event_id + '_vol' }));
    const combinedList = [...regTyped, ...volTyped];
    
    if (!combinedList || combinedList.length === 0) return [];

    const now = startOfDay(new Date());
    return combinedList.filter(item => {
      const startDateStr = item.event_start_time;
      if (!startDateStr) return selectedDateFilter === 'all';

      try {
        const startDate = parseISO(startDateStr);
        switch (selectedDateFilter) {
          case 'today': return isToday(startDate);
          case 'tomorrow': return isTomorrow(startDate);
          case 'next7days': return isAfter(startDate, now) && isBefore(startDate, addDays(now, 8));
          case 'past': return isBefore(startDate, now) && !isToday(startDate);
          case 'all': return isAfter(startDate, addDays(now, -1));
          default: return true;
        }
      } catch (e) {
        console.error("Error parsing date for filter:", startDateStr, e);
        return true;
      }
    });
  };
  
  const finalFilteredData = getCombinedAndFilteredData();

  const handleCancelWithdraw = (item: (EventRegistrationPayload | EventVolunteerApplicationPayload) & { type: 'registered' | 'volunteering' }) => {
    if (item.type === 'registered') {
      setRegistrationToCancel(item.id);
    } else {
      setVolunteerApplicationToCancel(item.id);
    }
    if (item.type === 'registered') {
      setIsCancelRegistrationDialogVisible(true);
    } else {
      setIsCancelVolunteerDialogVisible(true);
    }
  };

  const confirmCancelRegistration = async () => {
    if (!registrationToCancel) return;
    setIsCancellingRegistration(true);
    try {
      await cancelEventRegistration({ registrationId: registrationToCancel }); 
      showMessage(t('common.success'), t('my_events.cancelSuccess_registered'), 'success');
      refetchRegisteredEvents?.();
      refetchVolunteerApplications?.();
    } catch (err: any) {
      const errorMessage = err.message || t('my_events.cancelError');
      showMessage(t('common.error'), errorMessage, 'error');
    }
    setIsCancellingRegistration(false);
    setIsCancelRegistrationDialogVisible(false);
    setRegistrationToCancel(null);
  };

  const confirmCancelVolunteerApplication = async () => {
    if (!volunteerApplicationToCancel) return;
    try {
      // Find the event ID for the volunteer application
      const volunteerApp = volunteerApplications.find(app => app.id === volunteerApplicationToCancel);
      const eventId = volunteerApp?.event_id;
      
      if (!eventId) {
        throw new Error('Event ID not found for volunteer application');
      }
      
      await withdrawVolunteerApplicationMutation({ appId: volunteerApplicationToCancel, eventId });
      showMessage(t('common.success'), t('my_events.cancelSuccess_volunteer'), 'success');
      refetchRegisteredEvents?.();
      refetchVolunteerApplications?.();
    } catch (err: any) {
      const errorMessage = err.message || t('my_events.cancelError');
      showMessage(t('common.error'), errorMessage, 'error');
    }
    setIsCancelVolunteerDialogVisible(false);
    setVolunteerApplicationToCancel(null);
  };

  const EventCard = ({ item }: { item: (EventRegistrationPayload & { image_url?: string; type: 'registered' }) | (EventVolunteerApplicationPayload & { event_image_url?: string; type: 'volunteering' }) }) => {
    const eventId = item.event_id;
    const title = item.event_title;
    const imageUrl = item.type === 'registered' ? item.image_url : item.event_image_url;
    const startDate = item.event_start_time;

    const canCancelItem = (item.type === 'registered' && ['registered', 'waitlisted', 'pending_approval'].includes(item.status)) ||
                          (item.type === 'volunteering' && ['pending', 'approved', 'waitlisted'].includes(item.status));

    const handleCardPress = () => {
      const orgId = item.type === 'registered' ? item.event_organization_id : item.event_organization_id;
      router.push({ pathname: '/explore/events/EventDetailsScreen', params: { eventId: eventId, orgId: orgId || selectedOrgId } });
    };
    
    // Use the new color system
    const statusInfo = getStatusColorAndCategory(item.status);
    let statusText = `${t(`my_events.role_${item.type}`)}: ${t(`my_events.status.${item.status}`, item.status)}`;
    let statusColor = statusInfo.color;

    return (
      <TouchableOpacity onPress={handleCardPress} style={styles.cardWrapper}>
        <Card style={styles.card}>
          <View style={styles.cardImageContainer}>
            {imageUrl ? (
              <Image source={{ uri: imageUrl }} style={styles.cardImage} resizeMode="cover" onError={() => console.warn("Failed to load image for EventCard:", imageUrl)} />
            ) : (
              <View style={[styles.cardImage, { backgroundColor: '#F5F5F5', justifyContent: 'center', alignItems: 'center' }]}>
                <MaterialCommunityIcons name="image-off" size={48} color="#999999" />
              </View>
            )}
          </View>
          <Card.Content style={styles.cardContent}>
            <View style={styles.dateStatusRow}>
              <View style={styles.iconTextContainer}>
                <MaterialCommunityIcons name="calendar-blank-outline" size={16} color="#8E8E93" />
                <Text style={styles.dateText}>
                  {startDate ? formatEventDate(startDate, i18n.language) : t('my_events.noDate')}
                </Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: getStatusBackgroundColor(statusColor) }]}>
                <Text style={[styles.statusText, { color: statusColor }]}>{statusText}</Text>
              </View>
            </View>
            <Text style={styles.cardTitle} numberOfLines={2}>
              {title || t('my_events.loading_title')}
            </Text>
            {item.type === 'registered' && item.payment_status && item.payment_status !== 'not_required' && (
                <View style={styles.paymentContainer}>
                  <MaterialCommunityIcons name="credit-card-outline" size={16} color="#8E8E93" />
                  <Text style={styles.paymentText}>
                    {t(`my_events.paymentStatus.${item.payment_status}`)}
                  </Text>
                </View>
              )}
          </Card.Content>
          {canCancelItem && (
            <View style={styles.actionsContainer}>
              <TouchableOpacity
                style={[styles.actionButton, { borderColor: '#FF3B30' }]}
                onPress={() => handleCancelWithdraw(item)}
              >
                <Text style={[styles.actionButtonText, { color: '#FF3B30' }]}>
                  {item.type === 'registered' ? t('my_events.cancelRegistrationButton') : t('my_events.withdrawApplicationButton')}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </Card>
      </TouchableOpacity>
    );
  };

  const isLoadingDisplay = isLoadingRegisteredEventsFromStore || isLoadingVolunteerApplicationsFromStore;
  
  const currentErrorToDisplay = registeredEventsErrorFromStore || volunteerApplicationsErrorFromStore;

  if (!theme) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center', paddingTop: insets.top }]}>
          <ActivityIndicator size="large" />
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { paddingTop: Platform.OS === 'android' ? insets.top : 0 }]}>
      <Stack.Screen options={{ title: t('my_events.title'), headerTitleAlign: 'center' }} />
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{t('my_events.title')}</Text>
      </View>

      <View style={styles.filterContainer}>
        <View style={[styles.dropdownContainer, { zIndex: dateDropdownOpen ? 1000 : 100 }]}>
          <DropDownPicker
            open={dateDropdownOpen}
            value={selectedDateFilter}
            items={dateOptions}
            setOpen={setDateDropdownOpen}
            setValue={setSelectedDateFilter}
            style={{
              borderColor: '#D1D5DB',
            }}
            dropDownContainerStyle={{
              borderColor: '#D1D5DB',
            }}
            selectedItemLabelStyle={{
              color: theme.colors.primary,
            }}
            showTickIcon={true}
            TickIconComponent={() => (
              <MaterialCommunityIcons 
                name="check" 
                size={16} 
                color={theme.colors.primary} 
              />
            )}
            showArrowIcon={true}
            ArrowDownIconComponent={() => (
              <MaterialCommunityIcons 
                name="chevron-down" 
                size={16} 
                color="#9CA3AF" 
              />
            )}
            ArrowUpIconComponent={() => (
              <MaterialCommunityIcons 
                name="chevron-up" 
                size={16} 
                color="#9CA3AF" 
              />
            )}
            placeholder={t('my_events.filters.all')}
          />
        </View>
      </View>
      
      {isLoadingDisplay && !refreshing && <View style={styles.loadingContainer}><ActivityIndicator size="large" color={theme.colors.primary} /></View>}
      
      {currentErrorToDisplay && !refreshing && (
        <View style={styles.emptyStateContainer}>
            <MaterialCommunityIcons name="alert-circle-outline" size={64} color="#FF3B30" />
            <Text style={[styles.noEventsTitle, {color: '#FF3B30'}]}>
                {t('errors.failed_to_load_events')}
            </Text>
        </View>
      )}

      {!isLoadingDisplay && !currentErrorToDisplay && (
        <ScrollView
          contentContainerStyle={[styles.content, { paddingBottom: insets.bottom + 20 }]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
              progressBackgroundColor="#F5F5F5"
            />
          }
        >
          {finalFilteredData.length > 0 ? (
            finalFilteredData.map((item) => (
              <EventCard key={item.type === 'registered' ? item.id || item.event_id : item.id || item.event_id + '_vol'} item={item} />
            ))
          ) : (
            <View style={styles.emptyStateContainer}>
              <MaterialCommunityIcons name="calendar-remove" size={64} color="#666666" />
              <Text style={[styles.noEventsTitle]}>{t('my_events.empty.title')}</Text>
              <Text style={[styles.noEventsText]}>{t('my_events.empty.description')}</Text>
            </View>
          )}
        </ScrollView>
      )}
      
      <CustomDialog
        visible={messageDialogVisible}
        title={messageDialogTitle}
        message={messageDialogContent}
        confirmText={t('common.ok')}
        onConfirm={() => {
          setMessageDialogVisible(false);
        }}
        type={messageDialogType === 'success' ? 'success' : 'error'}
      />

      <CustomDialog
        visible={isCancelRegistrationDialogVisible}
        title={t('my_events.cancelConfirmTitle_registered')}
        message={t('my_events.cancelConfirmMessage_registered')}
        confirmText={t('common.confirm')}
        cancelText={t('common.cancel')}
        onConfirm={confirmCancelRegistration}
        onCancel={() => {
          setIsCancelRegistrationDialogVisible(false);
          setRegistrationToCancel(null);
        }}
        type="warning"
        confirmLoading={isCancellingRegistration}
      />

      <CustomDialog
        visible={isCancelVolunteerDialogVisible}
        title={t('my_events.cancelConfirmTitle_volunteer')}
        message={t('my_events.cancelConfirmMessage_volunteer')}
        confirmText={t('common.confirm')}
        cancelText={t('common.cancel')}
        onConfirm={confirmCancelVolunteerApplication}
        onCancel={() => {
          setIsCancelVolunteerDialogVisible(false);
          setVolunteerApplicationToCancel(null);
        }}
        type="warning"
        confirmLoading={isWithdrawingVolunteerApp}
      />
    </View>
  );
};

const createStyles = (theme: MD3Theme, insets: { top: number; right: number; bottom: number; left: number }) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
  },
  dropdownContainer: {
    width: '60%',
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 16,
    gap: 16,
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: '#FFFFFF',
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
    minHeight: 200,
  },
  noEventsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  noEventsText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
  cardWrapper: {
    marginBottom: 0,
  },
  card: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
    overflow: 'visible',
    elevation: 0,
    shadowOpacity: 0,
    shadowRadius: 0,
    shadowOffset: { width: 0, height: 0 },
  },
  cardImageContainer: {
    overflow: 'hidden',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  cardImage: {
    height: 160,
    width: '100%',
    backgroundColor: '#F5F5F5',
  },
  cardContent: {
    padding: 16,
  },
  dateStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  iconTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateText: {
    fontSize: 14,
    color: '#8E8E93',
    marginLeft: 8,
    fontWeight: '500',
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1C1C1E',
    lineHeight: 26,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 8,
  },
  paymentText: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  actionsContainer: {
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  actionButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MyEventsScreen;

