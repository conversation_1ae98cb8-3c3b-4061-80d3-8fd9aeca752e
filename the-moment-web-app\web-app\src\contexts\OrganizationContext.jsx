import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { profileService } from '../services/profileService';
import { organizationService } from '../services/organizationService';
import { useAuth } from './AuthContext';
import { THEME_COLORS } from '../config/themeConfig';
import defaultLogo from '../assets/logo/default-logo.png';
import { useTranslation } from 'react-i18next';

export const ALL_ORGANIZATION_ID = "00000000-0000-0000-0000-000000000002";

const DEFAULT_THEME_NAME = 'red';
const defaultThemeFromList = THEME_COLORS.find(tc => tc.name === DEFAULT_THEME_NAME) || (THEME_COLORS.length > 0 ? THEME_COLORS[0] : null);
const initialDefaultColorValue = defaultThemeFromList ? defaultThemeFromList.value : '#f44336'; // Fallback to original if THEME_COLORS is empty or 'red' not found

const OrganizationContext = createContext();

export const useOrganization = () => {
    const context = useContext(OrganizationContext);
    if (!context) {
        throw new Error('useOrganization must be used within an OrganizationProvider');
    }
    return context;
};

export const OrganizationProvider = ({ children }) => {
    const [organizations, setOrganizations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [volunteerPendingCounts, setVolunteerPendingCounts] = useState({}); // { orgId: count }
    const [verificationsPendingCount, setVerificationsPendingCount] = useState(0); // Global verifications count for super org
    const auth = useAuth();
    const { t } = useTranslation();

    // 获取组织显示名称的函数 - 只在显示时转换名称，不修改原始数据
    const getOrganizationDisplayName = (organization) => {
        if (!organization) return '';
        
        // 如果是ALL_ORGANIZATION_ID且不是超级管理员，显示为"所有组织"
        if (organization.id === ALL_ORGANIZATION_ID && auth.user?.role !== 'super_admin') {
            return t('mainLayout.menu.allOrganizations', 'All Organizations');
        }
        
        // 其他情况显示原始名称
        return organization.name;
    };

    const [currentOrganization, setCurrentOrganization] = useState(() => {
        const cachedOrgId = localStorage.getItem('currentOrganizationId');
        const cachedOrgObject = localStorage.getItem('currentOrganizationObject');
        
        if (cachedOrgObject) {
            try {
                const parsedOrg = JSON.parse(cachedOrgObject);
                return parsedOrg;
            } catch (e) {
                console.error("Error parsing cached organization object:", e);
                localStorage.removeItem('currentOrganizationId');
                localStorage.removeItem('currentOrganizationObject');
                return null;
            }
        }
        return null;
    });

    // Extract fetchOrganizations as a reusable function
    const fetchOrganizations = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            let result;
            if (auth.isLoggedIn && auth.user) {
                // Fetch organizations the current user is part of
                result = await profileService.getUserOrganizations();
            } else {
                // Fetch all public organizations for non-logged-in users
                const publicOrgsResponse = await organizationService.listOrganizations({ limit: 100 }); // Add limit for safety
                result = publicOrgsResponse.data || publicOrgsResponse; // Adjust based on actual API response
            }
            
            // 确保result是个数组并且有元素
            if (!result || !Array.isArray(result) || result.length === 0) {
                setOrganizations([]);
                setCurrentOrganization(null);
                localStorage.removeItem('currentOrganizationId');
                localStorage.removeItem('currentOrganizationObject');
                setLoading(false);
                return;
            }
            
            // 设置默认logo
            result = result.map(org => {
                if (!org.image_url) {
                    return { ...org, image_url: defaultLogo };
                }
                return org;
            });
            
            setOrganizations(result);
            
            // Fetch volunteer pending counts for admin/super_admin users
            if (auth.user && (auth.user.role === 'admin' || auth.user.role === 'super_admin')) {
                const pendingCounts = {};
                const promises = result.map(async (org) => {
                    try {
                        const count = await organizationService.getPendingVolunteerApplicationsCount(org.id);
                        console.log('pending volunteer count', org.id, count);
                        pendingCounts[org.id] = count;
                    } catch (error) {
                        console.error(`Failed to fetch volunteer pending count for org ${org.id}:`, error);
                        pendingCounts[org.id] = 0;
                    }
                });

                // For super_admin, also fetch global verifications count
                if (auth.user.role === 'super_admin') {
                    promises.push(
                        organizationService.getPendingVerificationsCount()
                            .then(count => {
                                console.log('pending verifications count', count);
                                setVerificationsPendingCount(count);
                            })
                            .catch(error => {
                                console.error('Failed to fetch verifications pending count:', error);
                                setVerificationsPendingCount(0);
                            })
                    );
                }

                await Promise.allSettled(promises);
                setVolunteerPendingCounts(pendingCounts);
            } else {
                // Clear pending counts for non-admin users
                setVolunteerPendingCounts({});
                setVerificationsPendingCount(0);
            }
            
            const cachedOrgId = localStorage.getItem('currentOrganizationId');

            // 处理缓存的组织ID
            if (cachedOrgId === ALL_ORGANIZATION_ID) {
                // 只有当用户组织列表中确实存在ALL_ORGANIZATION_ID时才能使用
                const realAllOrg = result.find(org => org.id === ALL_ORGANIZATION_ID);
                
                if (realAllOrg) {
                    // 如果找到真实的ALL_ORGANIZATION_ID组织，直接使用它
                    setCurrentOrganization(realAllOrg);
                    localStorage.setItem('currentOrganizationObject', JSON.stringify(realAllOrg));
                } else {
                    // 否则使用用户可访问的第一个组织
                    if (result.length > 0) {
                        setCurrentOrganization(result[0]);
                        localStorage.setItem('currentOrganizationId', result[0].id);
                        localStorage.setItem('currentOrganizationObject', JSON.stringify(result[0]));
                    } else {
                        setCurrentOrganization(null);
                        localStorage.removeItem('currentOrganizationId');
                        localStorage.removeItem('currentOrganizationObject');
                    }
                }
            }
            // 处理其他有效的组织ID
            else if (cachedOrgId && cachedOrgId !== 'null') {
                const orgFromCacheInList = result.find(org => org.id === cachedOrgId);
                if (orgFromCacheInList) {
                    setCurrentOrganization(orgFromCacheInList);
                    localStorage.setItem('currentOrganizationObject', JSON.stringify(orgFromCacheInList));
                } else {
                    // Cached org no longer exists or not in the list
                    if (result.length > 0) {
                        setCurrentOrganization(result[0]);
                        localStorage.setItem('currentOrganizationId', result[0].id);
                        localStorage.setItem('currentOrganizationObject', JSON.stringify(result[0]));
                    } else {
                        setCurrentOrganization(null);
                        localStorage.removeItem('currentOrganizationId');
                        localStorage.removeItem('currentOrganizationObject');
                    }
                }
            } else if (result.length > 0) {
                // No cached org, set to first org if available
                // For logged-in users, default to first org
                if (auth.isLoggedIn && auth.user) {
                    setCurrentOrganization(result[0]);
                    localStorage.setItem('currentOrganizationId', result[0].id);
                    localStorage.setItem('currentOrganizationObject', JSON.stringify(result[0]));
                } else {
                    // 处理未登录用户的情况
                    // 默认设置为第一个组织
                    setCurrentOrganization(result[0]);
                    localStorage.setItem('currentOrganizationId', result[0].id);
                    localStorage.setItem('currentOrganizationObject', JSON.stringify(result[0]));
                }
            } else {
                // No orgs fetched
                setCurrentOrganization(null);
                localStorage.removeItem('currentOrganizationId');
                localStorage.removeItem('currentOrganizationObject');
            }
        } catch (err) {
            console.error('Failed to fetch organizations:', err);
            setError(err);
            setOrganizations([]);
            setCurrentOrganization(null);
            localStorage.removeItem('currentOrganizationId');
            localStorage.removeItem('currentOrganizationObject');
        } finally {
            setLoading(false);
        }
    }, [auth.isLoggedIn, auth.user, t]);

    // Only run fetchOrganizations if auth.loading is false to ensure auth state is resolved.
    useEffect(() => {
        if (!auth.loading) {
            fetchOrganizations();
        }
    }, [auth.loading, fetchOrganizations]);

    // New useEffect to fetch full, fresh details when currentOrganization.id changes or is set.
    useEffect(() => {
        const fetchFreshOrganizationDetails = async () => {
            if (currentOrganization && currentOrganization.id && currentOrganization.id !== ALL_ORGANIZATION_ID) {
                try {
                    const response = await organizationService.getOrganizationDetails(currentOrganization.id);
                    const freshDetails = response.data || response; 
                    
                    if (freshDetails) {
                        setCurrentOrganization(prevOrg => {
                            // Merge, ensuring the ID from freshDetails is used if it differs (should not ideally)
                            const updatedOrg = { ...prevOrg, ...freshDetails };
                            localStorage.setItem('currentOrganizationObject', JSON.stringify(updatedOrg));
                            return updatedOrg;
                        });
                    }
                } catch (err) {
                    console.error(`Failed to fetch fresh details for org ${currentOrganization.id}:`, err);
                }
            }
        };

        if (currentOrganization?.id) { // Only run if there's an ID to fetch for
            fetchFreshOrganizationDetails();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentOrganization?.id]); 

    // Memoize themeConfig to avoid re-computation on every render unless currentOrganization changes
    const themeConfig = useMemo(() => {
        let colorPrimaryValue = initialDefaultColorValue;

        if (currentOrganization && currentOrganization.theme_color) {
            const themeNameFromOrg = currentOrganization.theme_color;
            const selectedColor = THEME_COLORS.find(tc => tc.name === themeNameFromOrg);
            if (selectedColor) {
                colorPrimaryValue = selectedColor.value;
            } else {
                console.warn(`组织主题名称 "${themeNameFromOrg}" 在THEME_COLORS中不存在。使用默认颜色。`);
            }
        }

        return {
            token: {
                colorPrimary: colorPrimaryValue,
                borderRadius: 6,
                fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif",
            },
        };
    }, [currentOrganization]);

    const handleOrganizationChange = (orgId) => {
        if (orgId === ALL_ORGANIZATION_ID) {
            // 查找真实的ALL_ORGANIZATION_ID组织
            const realAllOrg = organizations.find(org => org.id === ALL_ORGANIZATION_ID);
            
            if (realAllOrg) {
                // 如果找到真实的ALL_ORGANIZATION_ID组织，直接使用它
                setCurrentOrganization(realAllOrg);
                localStorage.setItem('currentOrganizationId', ALL_ORGANIZATION_ID);
                localStorage.setItem('currentOrganizationObject', JSON.stringify(realAllOrg));
            } else {
                // 如果没有找到，只在用户有权访问此组织ID时处理
                console.warn("Unable to find organization with ALL_ORGANIZATION_ID in the list");
                // 不创建虚拟组织，而是转向第一个可用组织
                if (organizations.length > 0) {
                    setCurrentOrganization(organizations[0]);
                    localStorage.setItem('currentOrganizationId', organizations[0].id);
                    localStorage.setItem('currentOrganizationObject', JSON.stringify(organizations[0]));
                }
            }
            return;
        }
        const selectedOrg = organizations.find(org => org.id === orgId);
        if (selectedOrg) {
            setCurrentOrganization(selectedOrg);
            localStorage.setItem('currentOrganizationId', selectedOrg.id);
            localStorage.setItem('currentOrganizationObject', JSON.stringify(selectedOrg));
        }
    };

    const setCurrentOrganizationById = (orgId) => {
        if (orgId) {
            handleOrganizationChange(orgId);
            fetchOrganizations(); // Refresh organizations when setting a new current organization
        }
    };

    // 检查用户是否有权访问"所有组织"选项
    const hasAllOrganizationsAccess = useMemo(() => {
        // 检查组织列表中是否存在ALL_ORGANIZATION_ID
        return organizations.some(org => org.id === ALL_ORGANIZATION_ID);
    }, [organizations]);

    const value = {
        currentOrganization,
        themeConfig,
        handleOrganizationChange,
        organizations,
        loading,
        error,
        setCurrentOrganization,
        getOrganizationDisplayName, // 导出获取显示名称的函数
        fetchOrganizations, // Export the fetchOrganizations function
        setCurrentOrganizationById, // Add a new function to set current org by ID and refresh data
        hasAllOrganizationsAccess, // Export whether user has access to "all organizations"
        volunteerPendingCounts, // Export volunteer pending counts
        verificationsPendingCount, // Export verifications pending count for super org
    };

    return (
        <OrganizationContext.Provider value={value}>
            {children}
        </OrganizationContext.Provider>
    );
}; 