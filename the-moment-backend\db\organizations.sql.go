// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: organizations.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const addUserToOrganization = `-- name: AddUserToOrganization :one

INSERT INTO user_organization_memberships (
    user_id,
    organization_id,
    role,
    is_active,
    notifications_enabled
) VALUES (
    $1, $2, $3, $4, $5
)
RETURNING user_id, organization_id, role, joined_at, is_active, notifications_enabled
`

type AddUserToOrganizationParams struct {
	UserID               uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID       uuid.UUID `db:"organization_id" json:"organization_id"`
	Role                 string    `db:"role" json:"role"`
	IsActive             bool      `db:"is_active" json:"is_active"`
	NotificationsEnabled bool      `db:"notifications_enabled" json:"notifications_enabled"`
}

// Consider adding LIMIT and OFFSET for pagination later
func (q *Queries) AddUserToOrganization(ctx context.Context, arg AddUserToOrganizationParams) (UserOrganizationMembership, error) {
	row := q.db.QueryRow(ctx, addUserToOrganization,
		arg.UserID,
		arg.OrganizationID,
		arg.Role,
		arg.IsActive,
		arg.NotificationsEnabled,
	)
	var i UserOrganizationMembership
	err := row.Scan(
		&i.UserID,
		&i.OrganizationID,
		&i.Role,
		&i.JoinedAt,
		&i.IsActive,
		&i.NotificationsEnabled,
	)
	return i, err
}

const countUserOwnedOrganizations = `-- name: CountUserOwnedOrganizations :one
SELECT count(*) FROM user_organization_memberships
WHERE user_id = $1 AND role = 'owner'
`

func (q *Queries) CountUserOwnedOrganizations(ctx context.Context, userID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, countUserOwnedOrganizations, userID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createOrganization = `-- name: CreateOrganization :one
INSERT INTO organizations (
    name,
    description,
    is_default_org,
    image_url,
    theme_color,
    status
) VALUES (
    $1, $2, $3, $4, $5, $6
)
RETURNING id, name, description, is_default_org, created_at, updated_at, image_url, theme_color, status
`

type CreateOrganizationParams struct {
	Name         string  `db:"name" json:"name"`
	Description  *string `db:"description" json:"description"`
	IsDefaultOrg bool    `db:"is_default_org" json:"is_default_org"`
	ImageUrl     *string `db:"image_url" json:"image_url"`
	ThemeColor   *string `db:"theme_color" json:"theme_color"`
	Status       string  `db:"status" json:"status"`
}

func (q *Queries) CreateOrganization(ctx context.Context, arg CreateOrganizationParams) (Organization, error) {
	row := q.db.QueryRow(ctx, createOrganization,
		arg.Name,
		arg.Description,
		arg.IsDefaultOrg,
		arg.ImageUrl,
		arg.ThemeColor,
		arg.Status,
	)
	var i Organization
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.IsDefaultOrg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImageUrl,
		&i.ThemeColor,
		&i.Status,
	)
	return i, err
}

const deleteOrganization = `-- name: DeleteOrganization :exec
DELETE FROM organizations
WHERE id = $1
`

func (q *Queries) DeleteOrganization(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteOrganization, id)
	return err
}

const getCurrentOrganizationOwner = `-- name: GetCurrentOrganizationOwner :one
SELECT user_id FROM user_organization_memberships
WHERE organization_id = $1 AND role = 'owner'
LIMIT 1
`

func (q *Queries) GetCurrentOrganizationOwner(ctx context.Context, organizationID uuid.UUID) (uuid.UUID, error) {
	row := q.db.QueryRow(ctx, getCurrentOrganizationOwner, organizationID)
	var user_id uuid.UUID
	err := row.Scan(&user_id)
	return user_id, err
}

const getDefaultOrganization = `-- name: GetDefaultOrganization :one
SELECT id, name, description, is_default_org, created_at, updated_at, image_url, theme_color, status FROM organizations
WHERE is_default_org = TRUE LIMIT 1
`

func (q *Queries) GetDefaultOrganization(ctx context.Context) (Organization, error) {
	row := q.db.QueryRow(ctx, getDefaultOrganization)
	var i Organization
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.IsDefaultOrg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImageUrl,
		&i.ThemeColor,
		&i.Status,
	)
	return i, err
}

const getOrganizationByID = `-- name: GetOrganizationByID :one
SELECT id, name, description, is_default_org, created_at, updated_at, image_url, theme_color, status FROM organizations
WHERE id = $1 LIMIT 1
`

func (q *Queries) GetOrganizationByID(ctx context.Context, id uuid.UUID) (Organization, error) {
	row := q.db.QueryRow(ctx, getOrganizationByID, id)
	var i Organization
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.IsDefaultOrg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImageUrl,
		&i.ThemeColor,
		&i.Status,
	)
	return i, err
}

const getOrganizationByName = `-- name: GetOrganizationByName :one
SELECT id, name, description, is_default_org, created_at, updated_at, image_url, theme_color, status FROM organizations
WHERE name = $1 LIMIT 1
`

func (q *Queries) GetOrganizationByName(ctx context.Context, name string) (Organization, error) {
	row := q.db.QueryRow(ctx, getOrganizationByName, name)
	var i Organization
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.IsDefaultOrg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImageUrl,
		&i.ThemeColor,
		&i.Status,
	)
	return i, err
}

const getOrganizationWithMemberCount = `-- name: GetOrganizationWithMemberCount :one
SELECT
    o.id,
    o.name,
    o.description,
    o.is_default_org,
    o.image_url,
    o.theme_color,
    o.status,
    o.created_at,
    o.updated_at,
    COUNT(uom.user_id) AS member_count
FROM organizations o
JOIN user_organization_memberships uom ON o.id = uom.organization_id
GROUP BY o.id
ORDER BY o.name
`

type GetOrganizationWithMemberCountRow struct {
	ID           uuid.UUID `db:"id" json:"id"`
	Name         string    `db:"name" json:"name"`
	Description  *string   `db:"description" json:"description"`
	IsDefaultOrg bool      `db:"is_default_org" json:"is_default_org"`
	ImageUrl     *string   `db:"image_url" json:"image_url"`
	ThemeColor   *string   `db:"theme_color" json:"theme_color"`
	Status       string    `db:"status" json:"status"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
	MemberCount  int64     `db:"member_count" json:"member_count"`
}

func (q *Queries) GetOrganizationWithMemberCount(ctx context.Context) (GetOrganizationWithMemberCountRow, error) {
	row := q.db.QueryRow(ctx, getOrganizationWithMemberCount)
	var i GetOrganizationWithMemberCountRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.IsDefaultOrg,
		&i.ImageUrl,
		&i.ThemeColor,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.MemberCount,
	)
	return i, err
}

const getUserOrganizationMembership = `-- name: GetUserOrganizationMembership :one
SELECT user_id, organization_id, role, joined_at, is_active, notifications_enabled FROM user_organization_memberships
WHERE user_id = $1 AND organization_id = $2
LIMIT 1
`

type GetUserOrganizationMembershipParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
}

func (q *Queries) GetUserOrganizationMembership(ctx context.Context, arg GetUserOrganizationMembershipParams) (UserOrganizationMembership, error) {
	row := q.db.QueryRow(ctx, getUserOrganizationMembership, arg.UserID, arg.OrganizationID)
	var i UserOrganizationMembership
	err := row.Scan(
		&i.UserID,
		&i.OrganizationID,
		&i.Role,
		&i.JoinedAt,
		&i.IsActive,
		&i.NotificationsEnabled,
	)
	return i, err
}

const getUserOrganizationRole = `-- name: GetUserOrganizationRole :one
SELECT role FROM user_organization_memberships
WHERE user_id = $1 AND organization_id = $2
`

type GetUserOrganizationRoleParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
}

func (q *Queries) GetUserOrganizationRole(ctx context.Context, arg GetUserOrganizationRoleParams) (string, error) {
	row := q.db.QueryRow(ctx, getUserOrganizationRole, arg.UserID, arg.OrganizationID)
	var role string
	err := row.Scan(&role)
	return role, err
}

const isUserMemberOfOrganization = `-- name: IsUserMemberOfOrganization :one
SELECT EXISTS(
    SELECT 1
    FROM user_organization_memberships
    WHERE user_id = $1 AND organization_id = $2 AND is_active = TRUE
)
`

type IsUserMemberOfOrganizationParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
}

func (q *Queries) IsUserMemberOfOrganization(ctx context.Context, arg IsUserMemberOfOrganizationParams) (bool, error) {
	row := q.db.QueryRow(ctx, isUserMemberOfOrganization, arg.UserID, arg.OrganizationID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const listOrganizationMembers = `-- name: ListOrganizationMembers :many
SELECT u.id, u.display_name, u.hashed_password, u.profile_picture_url, u.phone, u.phone_verified_at, u.email, u.email_verified_at, u.phone_otp_channel, u.interface_language, u.communication_language, u.enable_app_notifications, u.enable_whatsapp_notifications, u.enable_sms_notifications, u.enable_email_notifications, u.created_at, u.updated_at, u.role, uom.role, uom.joined_at, uom.is_active AS membership_is_active, uom.notifications_enabled AS membership_notifications_enabled
FROM users u
JOIN user_organization_memberships uom ON u.id = uom.user_id
WHERE uom.organization_id = $1
ORDER BY u.display_name
`

type ListOrganizationMembersRow struct {
	ID                             uuid.UUID  `db:"id" json:"id"`
	DisplayName                    string     `db:"display_name" json:"display_name"`
	HashedPassword                 *string    `db:"hashed_password" json:"hashed_password"`
	ProfilePictureUrl              *string    `db:"profile_picture_url" json:"profile_picture_url"`
	Phone                          *string    `db:"phone" json:"phone"`
	PhoneVerifiedAt                *time.Time `db:"phone_verified_at" json:"phone_verified_at"`
	Email                          *string    `db:"email" json:"email"`
	EmailVerifiedAt                *time.Time `db:"email_verified_at" json:"email_verified_at"`
	PhoneOtpChannel                string     `db:"phone_otp_channel" json:"phone_otp_channel"`
	InterfaceLanguage              string     `db:"interface_language" json:"interface_language"`
	CommunicationLanguage          string     `db:"communication_language" json:"communication_language"`
	EnableAppNotifications         bool       `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications    bool       `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications         bool       `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications       bool       `db:"enable_email_notifications" json:"enable_email_notifications"`
	CreatedAt                      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt                      time.Time  `db:"updated_at" json:"updated_at"`
	Role                           UserRole   `db:"role" json:"role"`
	Role_2                         string     `db:"role_2" json:"role_2"`
	JoinedAt                       time.Time  `db:"joined_at" json:"joined_at"`
	MembershipIsActive             bool       `db:"membership_is_active" json:"membership_is_active"`
	MembershipNotificationsEnabled bool       `db:"membership_notifications_enabled" json:"membership_notifications_enabled"`
}

func (q *Queries) ListOrganizationMembers(ctx context.Context, organizationID uuid.UUID) ([]ListOrganizationMembersRow, error) {
	rows, err := q.db.Query(ctx, listOrganizationMembers, organizationID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrganizationMembersRow{}
	for rows.Next() {
		var i ListOrganizationMembersRow
		if err := rows.Scan(
			&i.ID,
			&i.DisplayName,
			&i.HashedPassword,
			&i.ProfilePictureUrl,
			&i.Phone,
			&i.PhoneVerifiedAt,
			&i.Email,
			&i.EmailVerifiedAt,
			&i.PhoneOtpChannel,
			&i.InterfaceLanguage,
			&i.CommunicationLanguage,
			&i.EnableAppNotifications,
			&i.EnableWhatsappNotifications,
			&i.EnableSmsNotifications,
			&i.EnableEmailNotifications,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Role,
			&i.Role_2,
			&i.JoinedAt,
			&i.MembershipIsActive,
			&i.MembershipNotificationsEnabled,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOrganizations = `-- name: ListOrganizations :many
SELECT id, name, description, is_default_org, created_at, updated_at, image_url, theme_color, status, COUNT(*) OVER() as total_count
FROM organizations
ORDER BY created_at ASC
LIMIT $1 OFFSET $2
`

type ListOrganizationsParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

type ListOrganizationsRow struct {
	ID           uuid.UUID `db:"id" json:"id"`
	Name         string    `db:"name" json:"name"`
	Description  *string   `db:"description" json:"description"`
	IsDefaultOrg bool      `db:"is_default_org" json:"is_default_org"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
	ImageUrl     *string   `db:"image_url" json:"image_url"`
	ThemeColor   *string   `db:"theme_color" json:"theme_color"`
	Status       string    `db:"status" json:"status"`
	TotalCount   int64     `db:"total_count" json:"total_count"`
}

func (q *Queries) ListOrganizations(ctx context.Context, arg ListOrganizationsParams) ([]ListOrganizationsRow, error) {
	rows, err := q.db.Query(ctx, listOrganizations, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrganizationsRow{}
	for rows.Next() {
		var i ListOrganizationsRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.IsDefaultOrg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ImageUrl,
			&i.ThemeColor,
			&i.Status,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOrganizationsForUser = `-- name: ListOrganizationsForUser :many
SELECT
    o.id,
    o.name,
    o.description,
    o.is_default_org,
    o.image_url,
    o.theme_color,
    o.status,
    o.created_at,
    o.updated_at
FROM organizations o
JOIN user_organization_memberships uom ON o.id = uom.organization_id
WHERE uom.user_id = $1
ORDER BY o.name
`

type ListOrganizationsForUserRow struct {
	ID           uuid.UUID `db:"id" json:"id"`
	Name         string    `db:"name" json:"name"`
	Description  *string   `db:"description" json:"description"`
	IsDefaultOrg bool      `db:"is_default_org" json:"is_default_org"`
	ImageUrl     *string   `db:"image_url" json:"image_url"`
	ThemeColor   *string   `db:"theme_color" json:"theme_color"`
	Status       string    `db:"status" json:"status"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}

func (q *Queries) ListOrganizationsForUser(ctx context.Context, userID uuid.UUID) ([]ListOrganizationsForUserRow, error) {
	rows, err := q.db.Query(ctx, listOrganizationsForUser, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrganizationsForUserRow{}
	for rows.Next() {
		var i ListOrganizationsForUserRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.IsDefaultOrg,
			&i.ImageUrl,
			&i.ThemeColor,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOrganizationsWhereUserIsOwner = `-- name: ListOrganizationsWhereUserIsOwner :many
SELECT o.id, o.name, o.description, o.is_default_org, o.created_at, o.updated_at, o.image_url, o.theme_color, o.status
FROM organizations o
JOIN user_organization_memberships uom ON o.id = uom.organization_id
WHERE uom.user_id = $1 AND uom.role = 'owner'
ORDER BY o.name ASC
`

func (q *Queries) ListOrganizationsWhereUserIsOwner(ctx context.Context, userID uuid.UUID) ([]Organization, error) {
	rows, err := q.db.Query(ctx, listOrganizationsWhereUserIsOwner, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Organization{}
	for rows.Next() {
		var i Organization
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.IsDefaultOrg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ImageUrl,
			&i.ThemeColor,
			&i.Status,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserOrganizations = `-- name: ListUserOrganizations :many
SELECT o.id, o.name, o.description, o.is_default_org, o.created_at, o.updated_at, o.image_url, o.theme_color, o.status
FROM organizations o
JOIN user_organization_memberships uom ON o.id = uom.organization_id
WHERE uom.user_id = $1 AND uom.is_active = TRUE
ORDER BY o.name
`

func (q *Queries) ListUserOrganizations(ctx context.Context, userID uuid.UUID) ([]Organization, error) {
	rows, err := q.db.Query(ctx, listUserOrganizations, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Organization{}
	for rows.Next() {
		var i Organization
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.IsDefaultOrg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ImageUrl,
			&i.ThemeColor,
			&i.Status,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const removeUserFromOrganization = `-- name: RemoveUserFromOrganization :exec
DELETE FROM user_organization_memberships
WHERE user_id = $1 AND organization_id = $2
`

type RemoveUserFromOrganizationParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
}

func (q *Queries) RemoveUserFromOrganization(ctx context.Context, arg RemoveUserFromOrganizationParams) error {
	_, err := q.db.Exec(ctx, removeUserFromOrganization, arg.UserID, arg.OrganizationID)
	return err
}

const updateOrganization = `-- name: UpdateOrganization :one
UPDATE organizations
SET
    name = $2,
    description = $3,
    image_url = $4,
    theme_color = $5,
    status = $6,
    updated_at = NOW()
WHERE id = $1
RETURNING id, name, description, is_default_org, created_at, updated_at, image_url, theme_color, status
`

type UpdateOrganizationParams struct {
	ID          uuid.UUID `db:"id" json:"id"`
	Name        string    `db:"name" json:"name"`
	Description *string   `db:"description" json:"description"`
	ImageUrl    *string   `db:"image_url" json:"image_url"`
	ThemeColor  *string   `db:"theme_color" json:"theme_color"`
	Status      string    `db:"status" json:"status"`
}

func (q *Queries) UpdateOrganization(ctx context.Context, arg UpdateOrganizationParams) (Organization, error) {
	row := q.db.QueryRow(ctx, updateOrganization,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.ImageUrl,
		arg.ThemeColor,
		arg.Status,
	)
	var i Organization
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.IsDefaultOrg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImageUrl,
		&i.ThemeColor,
		&i.Status,
	)
	return i, err
}

const updateOrganizationLogoURL = `-- name: UpdateOrganizationLogoURL :one
UPDATE organizations
SET
    image_url = $2,
    updated_at = NOW()
WHERE id = $1
RETURNING id, name, description, is_default_org, created_at, updated_at, image_url, theme_color, status
`

type UpdateOrganizationLogoURLParams struct {
	ID       uuid.UUID `db:"id" json:"id"`
	ImageUrl *string   `db:"image_url" json:"image_url"`
}

func (q *Queries) UpdateOrganizationLogoURL(ctx context.Context, arg UpdateOrganizationLogoURLParams) (Organization, error) {
	row := q.db.QueryRow(ctx, updateOrganizationLogoURL, arg.ID, arg.ImageUrl)
	var i Organization
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.IsDefaultOrg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImageUrl,
		&i.ThemeColor,
		&i.Status,
	)
	return i, err
}

const updateOrganizationMemberRole = `-- name: UpdateOrganizationMemberRole :exec
UPDATE user_organization_memberships
SET role = $3, updated_at = NOW()
WHERE user_id = $1 AND organization_id = $2
`

type UpdateOrganizationMemberRoleParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Role           string    `db:"role" json:"role"`
}

func (q *Queries) UpdateOrganizationMemberRole(ctx context.Context, arg UpdateOrganizationMemberRoleParams) error {
	_, err := q.db.Exec(ctx, updateOrganizationMemberRole, arg.UserID, arg.OrganizationID, arg.Role)
	return err
}

const updateUserOrganizationMembershipDetails = `-- name: UpdateUserOrganizationMembershipDetails :one
UPDATE user_organization_memberships
SET 
    role = $3,
    is_active = $4,
    notifications_enabled = $5
WHERE user_id = $1 AND organization_id = $2
RETURNING user_id, organization_id, role, joined_at, is_active, notifications_enabled
`

type UpdateUserOrganizationMembershipDetailsParams struct {
	UserID               uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID       uuid.UUID `db:"organization_id" json:"organization_id"`
	Role                 string    `db:"role" json:"role"`
	IsActive             bool      `db:"is_active" json:"is_active"`
	NotificationsEnabled bool      `db:"notifications_enabled" json:"notifications_enabled"`
}

func (q *Queries) UpdateUserOrganizationMembershipDetails(ctx context.Context, arg UpdateUserOrganizationMembershipDetailsParams) (UserOrganizationMembership, error) {
	row := q.db.QueryRow(ctx, updateUserOrganizationMembershipDetails,
		arg.UserID,
		arg.OrganizationID,
		arg.Role,
		arg.IsActive,
		arg.NotificationsEnabled,
	)
	var i UserOrganizationMembership
	err := row.Scan(
		&i.UserID,
		&i.OrganizationID,
		&i.Role,
		&i.JoinedAt,
		&i.IsActive,
		&i.NotificationsEnabled,
	)
	return i, err
}

const updateUserOrganizationMembershipRole = `-- name: UpdateUserOrganizationMembershipRole :one
UPDATE user_organization_memberships
SET role = $3
WHERE user_id = $1 AND organization_id = $2
RETURNING user_id, organization_id, role, joined_at, is_active, notifications_enabled
`

type UpdateUserOrganizationMembershipRoleParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Role           string    `db:"role" json:"role"`
}

func (q *Queries) UpdateUserOrganizationMembershipRole(ctx context.Context, arg UpdateUserOrganizationMembershipRoleParams) (UserOrganizationMembership, error) {
	row := q.db.QueryRow(ctx, updateUserOrganizationMembershipRole, arg.UserID, arg.OrganizationID, arg.Role)
	var i UserOrganizationMembership
	err := row.Scan(
		&i.UserID,
		&i.OrganizationID,
		&i.Role,
		&i.JoinedAt,
		&i.IsActive,
		&i.NotificationsEnabled,
	)
	return i, err
}
