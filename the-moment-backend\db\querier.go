// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

type Querier interface {
	AddEventRequiredVerificationType(ctx context.Context, arg AddEventRequiredVerificationTypeParams) error
	AddTagToEvent(ctx context.Context, arg AddTagToEventParams) error
	AddTagToPost(ctx context.Context, arg AddTagToPostParams) error
	// Consider adding LIMIT and OFFSET for pagination later
	AddUserToOrganization(ctx context.Context, arg AddUserToOrganizationParams) (UserOrganizationMembership, error)
	AdminGetFullVerificationRequestByID(ctx context.Context, id uuid.UUID) (AdminGetFullVerificationRequestByIDRow, error)
	AdminListFullVerificationRequests(ctx context.Context, arg AdminListFullVerificationRequestsParams) ([]AdminListFullVerificationRequestsRow, error)
	// Optional: Join with event_registrations if filter_event_id is provided
	AdminListFullVerificationRequestsWithFilters(ctx context.Context, arg AdminListFullVerificationRequestsWithFiltersParams) ([]AdminListFullVerificationRequestsWithFiltersRow, error)
	CancelEventRegistrationByUser(ctx context.Context, arg CancelEventRegistrationByUserParams) (EventRegistration, error)
	// Companion query for total count for pagination
	// Optional: Join with event_registrations if filter_event_id is provided
	CountAdminListFullVerificationRequestsWithFilters(ctx context.Context, arg CountAdminListFullVerificationRequestsWithFiltersParams) (int64, error)
	CountAllPendingReviewEventVolunteerApplications(ctx context.Context) (int64, error)
	CountAllPublishedResources(ctx context.Context) (int64, error)
	CountEventsByOrganization(ctx context.Context, arg CountEventsByOrganizationParams) (int64, error)
	CountOrgEventVolunteerApplicationsWithFilters(ctx context.Context, arg CountOrgEventVolunteerApplicationsWithFiltersParams) (int64, error)
	CountOrgVolunteerApplicationsWithFilters(ctx context.Context, arg CountOrgVolunteerApplicationsWithFiltersParams) (int64, error)
	CountOrganizationEventRegistrationsWithFilters(ctx context.Context, arg CountOrganizationEventRegistrationsWithFiltersParams) (int64, error)
	CountOrganizationFilesInFolder(ctx context.Context, arg CountOrganizationFilesInFolderParams) (int64, error)
	CountOrganizationFilesInRoot(ctx context.Context, organizationID uuid.UUID) (int64, error)
	CountPendingReviewEventVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID) (int64, error)
	CountPendingReviewEventVolunteerApplicationsForOrganization(ctx context.Context, organizationID uuid.UUID) (int64, error)
	CountPostsByOrganization(ctx context.Context, organizationID uuid.UUID) (int64, error)
	CountPostsByOrganizationWithTags(ctx context.Context, arg CountPostsByOrganizationWithTagsParams) (int64, error)
	CountPublicEvents(ctx context.Context, arg CountPublicEventsParams) (int64, error)
	CountPublicEventsByOrganization(ctx context.Context, arg CountPublicEventsByOrganizationParams) (int64, error)
	CountPublishedPosts(ctx context.Context, arg CountPublishedPostsParams) (int64, error)
	CountPublishedPostsByOrganization(ctx context.Context, organizationID uuid.UUID) (int64, error)
	CountPublishedResources(ctx context.Context, arg CountPublishedResourcesParams) (int64, error)
	CountPublishedResourcesByOrganization(ctx context.Context, organizationID uuid.UUID) (int64, error)
	CountPublishedResourcesByVisibility(ctx context.Context, visibility string) (int64, error)
	CountResourcesByOrganization(ctx context.Context, arg CountResourcesByOrganizationParams) (int64, error)
	CountUserOwnedOrganizations(ctx context.Context, userID uuid.UUID) (int64, error)
	CountUserRegistrationsWithFilters(ctx context.Context, arg CountUserRegistrationsWithFiltersParams) (int64, error)
	CountVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID) (int64, error)
	// Address Proof
	CreateAddressProofVerification(ctx context.Context, arg CreateAddressProofVerificationParams) (VerificationAddressProof, error)
	CreateAuthFlow(ctx context.Context, arg CreateAuthFlowParams) (AuthFlow, error)
	CreateEvent(ctx context.Context, arg CreateEventParams) (Event, error)
	CreateEventMediaItem(ctx context.Context, arg CreateEventMediaItemParams) (EventMediaItem, error)
	CreateEventRegistration(ctx context.Context, arg CreateEventRegistrationParams) (CreateEventRegistrationRow, error)
	CreateEventTag(ctx context.Context, arg CreateEventTagParams) (EventTag, error)
	CreateEventVolunteerApplication(ctx context.Context, arg CreateEventVolunteerApplicationParams) (EventVolunteerApplication, error)
	// Specific Verification Type Queries --
	// HK ID Card
	CreateHKIDCardVerification(ctx context.Context, arg CreateHKIDCardVerificationParams) (VerificationHkIDCard, error)
	// HK Youth+
	CreateHKYouthPlusVerification(ctx context.Context, arg CreateHKYouthPlusVerificationParams) (VerificationHkYouthPlu, error)
	// Home Visit
	CreateHomeVisitVerification(ctx context.Context, arg CreateHomeVisitVerificationParams) (VerificationHomeVisit, error)
	CreateJob(ctx context.Context, arg CreateJobParams) (Job, error)
	// Mainland China ID Card
	CreateMainlandChinaIDCardVerification(ctx context.Context, arg CreateMainlandChinaIDCardVerificationParams) (VerificationMainlandChinaIDCard, error)
	// Home Return Permit -> Mainland Travel Permit
	CreateMainlandTravelPermitVerification(ctx context.Context, arg CreateMainlandTravelPermitVerificationParams) (VerificationMainlandTravelPermit, error)
	CreateOrganization(ctx context.Context, arg CreateOrganizationParams) (Organization, error)
	CreateOrganizationFile(ctx context.Context, arg CreateOrganizationFileParams) (OrganizationFile, error)
	// Passport
	CreatePassportVerification(ctx context.Context, arg CreatePassportVerificationParams) (VerificationPassport, error)
	CreatePost(ctx context.Context, arg CreatePostParams) (Post, error)
	CreatePostMediaItem(ctx context.Context, arg CreatePostMediaItemParams) (PostMediaItem, error)
	CreatePostTag(ctx context.Context, arg CreatePostTagParams) (PostTag, error)
	CreateRefreshToken(ctx context.Context, arg CreateRefreshTokenParams) (RefreshToken, error)
	CreateResource(ctx context.Context, arg CreateResourceParams) (Resource, error)
	CreateResourceFile(ctx context.Context, arg CreateResourceFileParams) (ResourceFile, error)
	CreateStaffUserWithEmailPassword(ctx context.Context, arg CreateStaffUserWithEmailPasswordParams) (User, error)
	// Student ID
	CreateStudentIDVerification(ctx context.Context, arg CreateStudentIDVerificationParams) (VerificationStudentID, error)
	CreateUserVerificationRequest(ctx context.Context, arg CreateUserVerificationRequestParams) (UserVerificationRequest, error)
	CreateUserWithPhone(ctx context.Context, arg CreateUserWithPhoneParams) (User, error)
	CreateVerificationDocument(ctx context.Context, arg CreateVerificationDocumentParams) (VerificationDocument, error)
	CreateVolunteerApplication(ctx context.Context, arg CreateVolunteerApplicationParams) (UserVolunteerApplication, error)
	DeleteEvent(ctx context.Context, id uuid.UUID) error
	DeleteEventMediaItem(ctx context.Context, id uuid.UUID) error
	DeleteEventMediaItemsByEventID(ctx context.Context, eventID uuid.UUID) error
	DeleteEventRegistration(ctx context.Context, id uuid.UUID) error
	DeleteEventTag(ctx context.Context, id uuid.UUID) error
	DeleteJob(ctx context.Context, id uuid.UUID) error
	DeleteMediaItemsForEvent(ctx context.Context, eventID uuid.UUID) error
	DeleteOrganization(ctx context.Context, id uuid.UUID) error
	DeleteOrganizationFile(ctx context.Context, id uuid.UUID) error
	DeletePost(ctx context.Context, id uuid.UUID) error
	DeletePostMediaByPostID(ctx context.Context, postID uuid.UUID) error
	DeletePostMediaItem(ctx context.Context, id uuid.UUID) error
	DeletePostTag(ctx context.Context, id uuid.UUID) error
	DeleteRefreshTokenByTokenHash(ctx context.Context, tokenHash string) error
	DeleteResource(ctx context.Context, id uuid.UUID) error
	DeleteResourceFile(ctx context.Context, id uuid.UUID) error
	// Note: The CountUsers query is removed as ListUsersFiltered now includes COUNT(*) OVER()
	DeleteUser(ctx context.Context, id uuid.UUID) error
	DeleteVerificationDocument(ctx context.Context, arg DeleteVerificationDocumentParams) error
	GetAddressProofVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationAddressProof, error)
	GetAllDescendantOrganizationItemsIncludingParent(ctx context.Context, id uuid.UUID) ([]GetAllDescendantOrganizationItemsIncludingParentRow, error)
	GetApprovedEventVolunteerApplication(ctx context.Context, arg GetApprovedEventVolunteerApplicationParams) (EventVolunteerApplication, error)
	GetAuthFlowByID(ctx context.Context, id uuid.UUID) (AuthFlow, error)
	GetAuthFlowByStateAndType(ctx context.Context, arg GetAuthFlowByStateAndTypeParams) (AuthFlow, error)
	GetCurrentOrganizationOwner(ctx context.Context, organizationID uuid.UUID) (uuid.UUID, error)
	GetDefaultOrganization(ctx context.Context) (Organization, error)
	GetEligibleEventRegistrationsForParticipantCheckIn(ctx context.Context, arg GetEligibleEventRegistrationsForParticipantCheckInParams) ([]GetEligibleEventRegistrationsForParticipantCheckInRow, error)
	GetEligibleEventRegistrationsForUserCheckIn(ctx context.Context, arg GetEligibleEventRegistrationsForUserCheckInParams) ([]GetEligibleEventRegistrationsForUserCheckInRow, error)
	GetEventByID(ctx context.Context, id uuid.UUID) (Event, error)
	GetEventCategoryStats(ctx context.Context, arg GetEventCategoryStatsParams) ([]GetEventCategoryStatsRow, error)
	GetEventDetailsWithCounts(ctx context.Context, id uuid.UUID) (GetEventDetailsWithCountsRow, error)
	GetEventMediaItemByID(ctx context.Context, id uuid.UUID) (EventMediaItem, error)
	// Query to get Event's Organization ID --
	GetEventOrganizationID(ctx context.Context, id uuid.UUID) (uuid.UUID, error)
	GetEventParticipantsByAge(ctx context.Context, eventID uuid.UUID) ([]GetEventParticipantsByAgeRow, error)
	GetEventParticipantsByDate(ctx context.Context, eventID uuid.UUID) ([]GetEventParticipantsByDateRow, error)
	GetEventParticipantsByGender(ctx context.Context, eventID uuid.UUID) ([]GetEventParticipantsByGenderRow, error)
	GetEventRegistrationByID(ctx context.Context, id uuid.UUID) (EventRegistration, error)
	GetEventRegistrationByUserAndEvent(ctx context.Context, arg GetEventRegistrationByUserAndEventParams) (GetEventRegistrationByUserAndEventRow, error)
	GetEventRegistrationByUserAndEventAndRole(ctx context.Context, arg GetEventRegistrationByUserAndEventAndRoleParams) (EventRegistration, error)
	GetEventRegistrationsByEventIDAndStatus(ctx context.Context, arg GetEventRegistrationsByEventIDAndStatusParams) ([]GetEventRegistrationsByEventIDAndStatusRow, error)
	GetEventRegistrationsForUserByEventIDs(ctx context.Context, arg GetEventRegistrationsForUserByEventIDsParams) ([]GetEventRegistrationsForUserByEventIDsRow, error)
	// Single Event Statistics --
	GetEventStatistics(ctx context.Context, id uuid.UUID) (GetEventStatisticsRow, error)
	GetEventTag(ctx context.Context, id uuid.UUID) (EventTag, error)
	GetEventTimeByID(ctx context.Context, id uuid.UUID) (GetEventTimeByIDRow, error)
	GetEventVolunteerApplicationByID(ctx context.Context, id uuid.UUID) (EventVolunteerApplication, error)
	GetEventVolunteerApplicationByUserAndEvent(ctx context.Context, arg GetEventVolunteerApplicationByUserAndEventParams) (EventVolunteerApplication, error)
	GetEventVolunteerApplicationDetailsByID(ctx context.Context, id uuid.UUID) (EventVolunteerApplication, error)
	GetEventVolunteersByAge(ctx context.Context, eventID uuid.UUID) ([]GetEventVolunteersByAgeRow, error)
	GetEventVolunteersByDate(ctx context.Context, eventID uuid.UUID) ([]GetEventVolunteersByDateRow, error)
	GetEventVolunteersByGender(ctx context.Context, eventID uuid.UUID) ([]GetEventVolunteersByGenderRow, error)
	// END: Additional Event Volunteer Application Queries
	GetEventWithConfigurationForRegistration(ctx context.Context, id uuid.UUID) (GetEventWithConfigurationForRegistrationRow, error)
	GetEventWithOrganizationByID(ctx context.Context, id uuid.UUID) (GetEventWithOrganizationByIDRow, error)
	GetEventsByStartTimeRange(ctx context.Context, arg GetEventsByStartTimeRangeParams) ([]Event, error)
	GetEventsForTag(ctx context.Context, eventTagID uuid.UUID) ([]Event, error)
	GetFullVerificationDetails(ctx context.Context, id uuid.UUID) (GetFullVerificationDetailsRow, error)
	GetHKIDCardVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationHkIDCard, error)
	GetHKYouthPlusVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationHkYouthPlu, error)
	GetHomeVisitVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationHomeVisit, error)
	GetMainlandChinaIDCardVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationMainlandChinaIDCard, error)
	GetMainlandTravelPermitVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationMainlandTravelPermit, error)
	// or whatever status indicates confirmed volunteering
	GetMonthlyAttendedEvents(ctx context.Context, userID uuid.UUID) ([]GetMonthlyAttendedEventsRow, error)
	GetNextPendingJob(ctx context.Context) (GetNextPendingJobRow, error)
	GetOTPAttempt(ctx context.Context, phone string) (OtpAttempt, error)
	GetOldestWaitlistedRegistration(ctx context.Context, eventID uuid.UUID) (GetOldestWaitlistedRegistrationRow, error)
	GetOrganizationByID(ctx context.Context, id uuid.UUID) (Organization, error)
	GetOrganizationByName(ctx context.Context, name string) (Organization, error)
	GetOrganizationEventCategoryStats(ctx context.Context, arg GetOrganizationEventCategoryStatsParams) ([]GetOrganizationEventCategoryStatsRow, error)
	GetOrganizationEventStatistics(ctx context.Context, arg GetOrganizationEventStatisticsParams) ([]GetOrganizationEventStatisticsRow, error)
	GetOrganizationFileByID(ctx context.Context, id uuid.UUID) (OrganizationFile, error)
	GetOrganizationTopEventsByParticipantCount(ctx context.Context, arg GetOrganizationTopEventsByParticipantCountParams) ([]GetOrganizationTopEventsByParticipantCountRow, error)
	GetOrganizationWithMemberCount(ctx context.Context) (GetOrganizationWithMemberCountRow, error)
	GetPassportVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationPassport, error)
	GetPostByID(ctx context.Context, id uuid.UUID) (Post, error)
	GetPostBySlug(ctx context.Context, slug string) (Post, error)
	GetPostDetailsWithTags(ctx context.Context, id uuid.UUID) (GetPostDetailsWithTagsRow, error)
	GetPostMediaItemByID(ctx context.Context, id uuid.UUID) (PostMediaItem, error)
	GetPostTagByID(ctx context.Context, id uuid.UUID) (PostTag, error)
	GetPostTagByName(ctx context.Context, nameEn string) (PostTag, error)
	GetPostWithMedia(ctx context.Context, id uuid.UUID) ([]GetPostWithMediaRow, error)
	GetPostsForTag(ctx context.Context, tagID uuid.UUID) ([]Post, error)
	GetRefreshTokenByTokenHash(ctx context.Context, tokenHash string) (RefreshToken, error)
	// For atomicity if promoting
	GetRegisteredAndApprovedCountForEvent(ctx context.Context, eventID uuid.UUID) (int64, error)
	GetRegisteredCountForEvent(ctx context.Context, eventID uuid.UUID) (int64, error)
	GetResourceByID(ctx context.Context, id uuid.UUID) (Resource, error)
	GetResourceByOrgAndSlug(ctx context.Context, arg GetResourceByOrgAndSlugParams) (Resource, error)
	GetResourceByOrgAndSlugWithFiles(ctx context.Context, arg GetResourceByOrgAndSlugWithFilesParams) ([]GetResourceByOrgAndSlugWithFilesRow, error)
	GetResourceFileByID(ctx context.Context, id uuid.UUID) (ResourceFile, error)
	GetResourceWithFiles(ctx context.Context, id uuid.UUID) ([]GetResourceWithFilesRow, error)
	GetScheduledEventsToPublish(ctx context.Context) ([]uuid.UUID, error)
	GetScheduledPostsToPublish(ctx context.Context) ([]uuid.UUID, error)
	GetScheduledResourcesToPublish(ctx context.Context) ([]uuid.UUID, error)
	GetStudentIDVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationStudentID, error)
	GetTagsForPost(ctx context.Context, postID uuid.UUID) ([]PostTag, error)
	GetTopAttendedEventTags(ctx context.Context, userID uuid.UUID) ([]GetTopAttendedEventTagsRow, error)
	GetTopEventsByParticipantCount(ctx context.Context, arg GetTopEventsByParticipantCountParams) ([]GetTopEventsByParticipantCountRow, error)
	GetUpcomingEventsForUserReminders(ctx context.Context, limit int32) ([]GetUpcomingEventsForUserRemindersRow, error)
	GetUserByEmail(ctx context.Context, email *string) (User, error)
	GetUserByID(ctx context.Context, id uuid.UUID) (User, error)
	GetUserByPhone(ctx context.Context, phone *string) (User, error)
	GetUserEventStatistics(ctx context.Context, arg GetUserEventStatisticsParams) ([]GetUserEventStatisticsRow, error)
	GetUserEventVolunteerApplicationByID(ctx context.Context, arg GetUserEventVolunteerApplicationByIDParams) (GetUserEventVolunteerApplicationByIDRow, error)
	GetUserOrganizationMembership(ctx context.Context, arg GetUserOrganizationMembershipParams) (UserOrganizationMembership, error)
	GetUserOrganizationRole(ctx context.Context, arg GetUserOrganizationRoleParams) (string, error)
	GetUserRegisteredEventTimes(ctx context.Context, userID uuid.UUID) ([]GetUserRegisteredEventTimesRow, error)
	GetUserRegistrationDate(ctx context.Context, id uuid.UUID) (time.Time, error)
	GetUserTotalAttendedEvents(ctx context.Context, userID uuid.UUID) (int64, error)
	GetUserTotalVolunteerEvents(ctx context.Context, userID uuid.UUID) (int64, error)
	GetUserVerificationRequestByID(ctx context.Context, id uuid.UUID) (GetUserVerificationRequestByIDRow, error)
	GetUserVerificationRequestsSimple(ctx context.Context, userID uuid.UUID) ([]UserVerificationRequest, error)
	GetUserVerificationStatuses(ctx context.Context, userID uuid.UUID) (GetUserVerificationStatusesRow, error)
	GetUserVolunteerApplicationByID(ctx context.Context, arg GetUserVolunteerApplicationByIDParams) (UserVolunteerApplication, error)
	GetUserVolunteerApplicationForOrganization(ctx context.Context, arg GetUserVolunteerApplicationForOrganizationParams) (UserVolunteerApplication, error)
	GetVerificationDocumentByID(ctx context.Context, arg GetVerificationDocumentByIDParams) (VerificationDocument, error)
	GetVerificationDocumentDataByID(ctx context.Context, id uuid.UUID) ([]byte, error)
	GetVerificationDocumentMetaByID(ctx context.Context, id uuid.UUID) (GetVerificationDocumentMetaByIDRow, error)
	GetVolunteerApplicationByUserAndEventAndOrg(ctx context.Context, arg GetVolunteerApplicationByUserAndEventAndOrgParams) (EventVolunteerApplication, error)
	GetVolunteerApplicationDetailsForAdmin(ctx context.Context, arg GetVolunteerApplicationDetailsForAdminParams) (GetVolunteerApplicationDetailsForAdminRow, error)
	GetVolunteerApplicationsForEvent(ctx context.Context, arg GetVolunteerApplicationsForEventParams) ([]GetVolunteerApplicationsForEventRow, error)
	// Removed 'confirmed'
	GetWaitlistedCountForEvent(ctx context.Context, eventID uuid.UUID) (int64, error)
	IncrementJobAttempts(ctx context.Context, id uuid.UUID) (Job, error)
	IsUserMemberOfOrganization(ctx context.Context, arg IsUserMemberOfOrganizationParams) (bool, error)
	ListAllPendingReviewEventVolunteerApplications(ctx context.Context, arg ListAllPendingReviewEventVolunteerApplicationsParams) ([]ListAllPendingReviewEventVolunteerApplicationsRow, error)
	ListEventMediaItems(ctx context.Context, eventID uuid.UUID) ([]EventMediaItem, error)
	ListEventRegistrationsByStatusAndEventID(ctx context.Context, arg ListEventRegistrationsByStatusAndEventIDParams) ([]ListEventRegistrationsByStatusAndEventIDRow, error)
	ListEventTags(ctx context.Context) ([]EventTag, error)
	ListEventVolunteerApplicationsForEvent(ctx context.Context, arg ListEventVolunteerApplicationsForEventParams) ([]ListEventVolunteerApplicationsForEventRow, error)
	ListEventVolunteerApplicationsForUser(ctx context.Context, arg ListEventVolunteerApplicationsForUserParams) ([]ListEventVolunteerApplicationsForUserRow, error)
	ListEventsByOrganization(ctx context.Context, arg ListEventsByOrganizationParams) ([]Event, error)
	ListFilesByResource(ctx context.Context, resourceID uuid.UUID) ([]ListFilesByResourceRow, error)
	ListFullPendingVerificationRequests(ctx context.Context) ([]ListFullPendingVerificationRequestsRow, error)
	ListFullUserVerificationRequestsByUserID(ctx context.Context, userID uuid.UUID) ([]ListFullUserVerificationRequestsByUserIDRow, error)
	ListGloballyApprovedEventTags(ctx context.Context) ([]EventTag, error)
	ListMediaItemsByEventIDs(ctx context.Context, eventIds []uuid.UUID) ([]EventMediaItem, error)
	ListMediaItemsByPost(ctx context.Context, postID uuid.UUID) ([]PostMediaItem, error)
	ListMediaItemsByPostIDs(ctx context.Context, postIds []uuid.UUID) ([]PostMediaItem, error)
	ListMediaItemsForEvents(ctx context.Context, eventIds []uuid.UUID) ([]EventMediaItem, error)
	ListNotifiableUsersForEvent(ctx context.Context, eventID uuid.UUID) ([]ListNotifiableUsersForEventRow, error)
	ListOrgEventVolunteerApplicationsWithFilters(ctx context.Context, arg ListOrgEventVolunteerApplicationsWithFiltersParams) ([]ListOrgEventVolunteerApplicationsWithFiltersRow, error)
	ListOrgPostsWithMedia(ctx context.Context, arg ListOrgPostsWithMediaParams) ([]ListOrgPostsWithMediaRow, error)
	ListOrgVolunteerApplicationsWithFilters(ctx context.Context, arg ListOrgVolunteerApplicationsWithFiltersParams) ([]ListOrgVolunteerApplicationsWithFiltersRow, error)
	ListOrganizationEventRegistrationsWithFilters(ctx context.Context, arg ListOrganizationEventRegistrationsWithFiltersParams) ([]ListOrganizationEventRegistrationsWithFiltersRow, error)
	ListOrganizationFilesInFolder(ctx context.Context, arg ListOrganizationFilesInFolderParams) ([]ListOrganizationFilesInFolderRow, error)
	ListOrganizationFilesInRoot(ctx context.Context, arg ListOrganizationFilesInRootParams) ([]ListOrganizationFilesInRootRow, error)
	ListOrganizationMembers(ctx context.Context, organizationID uuid.UUID) ([]ListOrganizationMembersRow, error)
	ListOrganizations(ctx context.Context, arg ListOrganizationsParams) ([]ListOrganizationsRow, error)
	ListOrganizationsForUser(ctx context.Context, userID uuid.UUID) ([]ListOrganizationsForUserRow, error)
	ListOrganizationsWhereUserIsOwner(ctx context.Context, userID uuid.UUID) ([]Organization, error)
	// Only users with phone numbers
	ListParticipantUserIDsByEventAndStatus(ctx context.Context, arg ListParticipantUserIDsByEventAndStatusParams) ([]uuid.UUID, error)
	ListPendingReviewEventVolunteerApplicationsForEvent(ctx context.Context, arg ListPendingReviewEventVolunteerApplicationsForEventParams) ([]ListPendingReviewEventVolunteerApplicationsForEventRow, error)
	ListPendingReviewEventVolunteerApplicationsForOrganization(ctx context.Context, arg ListPendingReviewEventVolunteerApplicationsForOrganizationParams) ([]ListPendingReviewEventVolunteerApplicationsForOrganizationRow, error)
	ListPendingVerificationRequests(ctx context.Context) ([]ListPendingVerificationRequestsRow, error)
	ListPendingVolunteerApplicationsForOrganization(ctx context.Context, organizationID uuid.UUID) ([]ListPendingVolunteerApplicationsForOrganizationRow, error)
	ListPopularEvents(ctx context.Context, limit int32) ([]ListPopularEventsRow, error)
	ListPopularEventsByOrganization(ctx context.Context, arg ListPopularEventsByOrganizationParams) ([]ListPopularEventsByOrganizationRow, error)
	ListPostTags(ctx context.Context) ([]PostTag, error)
	ListPostsByOrganization(ctx context.Context, arg ListPostsByOrganizationParams) ([]Post, error)
	ListPostsByOrganizationWithTags(ctx context.Context, arg ListPostsByOrganizationWithTagsParams) ([]ListPostsByOrganizationWithTagsRow, error)
	ListPublicEvents(ctx context.Context, arg ListPublicEventsParams) ([]Event, error)
	// LEFT JOIN event_event_tags eet ON e.id = eet.event_id -- Add joins if tag filtering etc. is needed
	// LEFT JOIN event_tags et ON eet.tag_id = et.id
	ListPublicEventsByOrganization(ctx context.Context, arg ListPublicEventsByOrganizationParams) ([]ListPublicEventsByOrganizationRow, error)
	ListPublicEventsWithCounts(ctx context.Context, arg ListPublicEventsWithCountsParams) ([]ListPublicEventsWithCountsRow, error)
	ListPublishedPosts(ctx context.Context, arg ListPublishedPostsParams) ([]Post, error)
	ListPublishedPostsByOrganizationWithMedia(ctx context.Context, arg ListPublishedPostsByOrganizationWithMediaParams) ([]ListPublishedPostsByOrganizationWithMediaRow, error)
	ListPublishedPostsWithAuthor(ctx context.Context, arg ListPublishedPostsWithAuthorParams) ([]ListPublishedPostsWithAuthorRow, error)
	ListPublishedPostsWithMedia(ctx context.Context, arg ListPublishedPostsWithMediaParams) ([]ListPublishedPostsWithMediaRow, error)
	// Assuming public means published for the org context
	ListPublishedResourcesByOrganizationWithFiles(ctx context.Context, arg ListPublishedResourcesByOrganizationWithFilesParams) ([]ListPublishedResourcesByOrganizationWithFilesRow, error)
	ListPublishedResourcesByVisibility(ctx context.Context, arg ListPublishedResourcesByVisibilityParams) ([]Resource, error)
	ListPublishedResourcesWithFiles(ctx context.Context, arg ListPublishedResourcesWithFilesParams) ([]ListPublishedResourcesWithFilesRow, error)
	ListPublishedResources_NoFiles(ctx context.Context, arg ListPublishedResources_NoFilesParams) ([]Resource, error)
	ListRegistrationsForEvent(ctx context.Context, arg ListRegistrationsForEventParams) ([]ListRegistrationsForEventRow, error)
	ListRegistrationsForUser(ctx context.Context, arg ListRegistrationsForUserParams) ([]ListRegistrationsForUserRow, error)
	ListRequiredVerificationTypesForEvent(ctx context.Context, eventID uuid.UUID) ([]string, error)
	ListResourcesByOrganization(ctx context.Context, arg ListResourcesByOrganizationParams) ([]Resource, error)
	ListTagsByEventIDs(ctx context.Context, eventIds []uuid.UUID) ([]ListTagsByEventIDsRow, error)
	ListTagsForEvent(ctx context.Context, eventID uuid.UUID) ([]EventTag, error)
	ListUpcomingEvents(ctx context.Context, limit int32) ([]ListUpcomingEventsRow, error)
	ListUpcomingEventsByOrganization(ctx context.Context, arg ListUpcomingEventsByOrganizationParams) ([]ListUpcomingEventsByOrganizationRow, error)
	ListUserOrganizations(ctx context.Context, userID uuid.UUID) ([]Organization, error)
	ListUserRegistrationsForEvents(ctx context.Context, arg ListUserRegistrationsForEventsParams) ([]ListUserRegistrationsForEventsRow, error)
	ListUserRegistrationsWithFilters(ctx context.Context, arg ListUserRegistrationsWithFiltersParams) ([]ListUserRegistrationsWithFiltersRow, error)
	ListUserVerificationRequestsByUserID(ctx context.Context, userID uuid.UUID) ([]ListUserVerificationRequestsByUserIDRow, error)
	ListUserVerificationsByStatus(ctx context.Context, arg ListUserVerificationsByStatusParams) ([]UserVerificationRequest, error)
	ListUserVerificationsByStatusAndType(ctx context.Context, arg ListUserVerificationsByStatusAndTypeParams) ([]UserVerificationRequest, error)
	ListUserVerificationsByType(ctx context.Context, arg ListUserVerificationsByTypeParams) ([]UserVerificationRequest, error)
	ListUserVolunteerApplications(ctx context.Context, arg ListUserVolunteerApplicationsParams) ([]UserVolunteerApplication, error)
	ListUsersFiltered(ctx context.Context, arg ListUsersFilteredParams) ([]ListUsersFilteredRow, error)
	LockOTPAttempts(ctx context.Context, arg LockOTPAttemptsParams) (OtpAttempt, error)
	MarkEventVolunteerAttended(ctx context.Context, arg MarkEventVolunteerAttendedParams) error
	MarkJobRunning(ctx context.Context, id uuid.UUID) (Job, error)
	ReactivateEventVolunteerApplication(ctx context.Context, arg ReactivateEventVolunteerApplicationParams) (EventVolunteerApplication, error)
	RemoveAllRequiredVerificationsFromEvent(ctx context.Context, eventID uuid.UUID) error
	RemoveAllTagsFromEvent(ctx context.Context, eventID uuid.UUID) error
	RemoveEventRequiredVerificationType(ctx context.Context, arg RemoveEventRequiredVerificationTypeParams) error
	RemoveTagFromEvent(ctx context.Context, arg RemoveTagFromEventParams) error
	RemoveTagFromPost(ctx context.Context, arg RemoveTagFromPostParams) error
	RemoveUserFromOrganization(ctx context.Context, arg RemoveUserFromOrganizationParams) error
	ResetOTPAttempts(ctx context.Context, phone string) error
	RetryJob(ctx context.Context, arg RetryJobParams) (Job, error)
	SetBannerForEventMediaItem(ctx context.Context, arg SetBannerForEventMediaItemParams) (EventMediaItem, error)
	SetBannerForPostMediaItem(ctx context.Context, arg SetBannerForPostMediaItemParams) (PostMediaItem, error)
	SetEventStatusToPublished(ctx context.Context, id uuid.UUID) error
	SetEventVolunteerApplicationStatusToWithdrawnByUser(ctx context.Context, arg SetEventVolunteerApplicationStatusToWithdrawnByUserParams) (EventVolunteerApplication, error)
	SetPostStatusToPublished(ctx context.Context, id uuid.UUID) error
	SetResourceStatusToPublished(ctx context.Context, id uuid.UUID) error
	SetUserPhoneVerified(ctx context.Context, arg SetUserPhoneVerifiedParams) (User, error)
	SetUserVolunteerApplicationStatusToWithdrawnByUser(ctx context.Context, arg SetUserVolunteerApplicationStatusToWithdrawnByUserParams) (UserVolunteerApplication, error)
	UnsetBannerForEventMediaItems(ctx context.Context, eventID uuid.UUID) error
	// Order by post_id for easier grouping, then by banner status and uploaded_at
	UnsetBannerForPostMediaItems(ctx context.Context, postID uuid.UUID) error
	UpdateAuthFlowConsumed(ctx context.Context, arg UpdateAuthFlowConsumedParams) (AuthFlow, error)
	UpdateAuthFlowOTPSID(ctx context.Context, arg UpdateAuthFlowOTPSIDParams) (AuthFlow, error)
	UpdateEventDetails(ctx context.Context, arg UpdateEventDetailsParams) (Event, error)
	UpdateEventRegistrationPaymentStatus(ctx context.Context, arg UpdateEventRegistrationPaymentStatusParams) (UpdateEventRegistrationPaymentStatusRow, error)
	UpdateEventRegistrationStatus(ctx context.Context, arg UpdateEventRegistrationStatusParams) (EventRegistration, error)
	// Cast $2 to the enum type
	UpdateEventRegistrationStatusAndDetails(ctx context.Context, arg UpdateEventRegistrationStatusAndDetailsParams) (EventRegistration, error)
	UpdateEventRegistrationStatusByAdmin(ctx context.Context, arg UpdateEventRegistrationStatusByAdminParams) (EventRegistration, error)
	// Time window: now - X minutes AND now + Y minutes
	UpdateEventRegistrationToCheckIn(ctx context.Context, arg UpdateEventRegistrationToCheckInParams) (UpdateEventRegistrationToCheckInRow, error)
	UpdateEventRegistrationToCheckInByScanner(ctx context.Context, arg UpdateEventRegistrationToCheckInByScannerParams) (EventRegistration, error)
	UpdateEventStatus(ctx context.Context, arg UpdateEventStatusParams) (Event, error)
	UpdateEventTag(ctx context.Context, arg UpdateEventTagParams) (EventTag, error)
	UpdateEventVolunteerApplicationStatus(ctx context.Context, arg UpdateEventVolunteerApplicationStatusParams) (EventVolunteerApplication, error)
	UpdateJobStatus(ctx context.Context, arg UpdateJobStatusParams) (Job, error)
	UpdateOrganization(ctx context.Context, arg UpdateOrganizationParams) (Organization, error)
	UpdateOrganizationFile(ctx context.Context, arg UpdateOrganizationFileParams) (OrganizationFile, error)
	UpdateOrganizationLogoURL(ctx context.Context, arg UpdateOrganizationLogoURLParams) (Organization, error)
	UpdateOrganizationMemberRole(ctx context.Context, arg UpdateOrganizationMemberRoleParams) error
	UpdatePost(ctx context.Context, arg UpdatePostParams) (Post, error)
	UpdatePostTag(ctx context.Context, arg UpdatePostTagParams) (PostTag, error)
	UpdateResource(ctx context.Context, arg UpdateResourceParams) (Resource, error)
	UpdateUserDisplayName(ctx context.Context, arg UpdateUserDisplayNameParams) (User, error)
	UpdateUserEmailAndVerificationStatus(ctx context.Context, arg UpdateUserEmailAndVerificationStatusParams) (User, error)
	UpdateUserLanguagePreferences(ctx context.Context, arg UpdateUserLanguagePreferencesParams) (User, error)
	UpdateUserNotificationSettings(ctx context.Context, arg UpdateUserNotificationSettingsParams) (User, error)
	UpdateUserOrganizationMembershipDetails(ctx context.Context, arg UpdateUserOrganizationMembershipDetailsParams) (UserOrganizationMembership, error)
	UpdateUserOrganizationMembershipRole(ctx context.Context, arg UpdateUserOrganizationMembershipRoleParams) (UserOrganizationMembership, error)
	UpdateUserPhoneAndMarkVerified(ctx context.Context, arg UpdateUserPhoneAndMarkVerifiedParams) (User, error)
	UpdateUserPhoneAndVerificationStatus(ctx context.Context, arg UpdateUserPhoneAndVerificationStatusParams) (User, error)
	UpdateUserProfilePictureURL(ctx context.Context, arg UpdateUserProfilePictureURLParams) (User, error)
	UpdateUserVerificationRequestStatus(ctx context.Context, arg UpdateUserVerificationRequestStatusParams) (UserVerificationRequest, error)
	UpdateVolunteerApplicationStatus(ctx context.Context, arg UpdateVolunteerApplicationStatusParams) (UserVolunteerApplication, error)
	UpsertOTPAttempt(ctx context.Context, phone string) (OtpAttempt, error)
	UserDeleteVerificationData(ctx context.Context, arg UserDeleteVerificationDataParams) (UserVerificationRequest, error)
}

var _ Querier = (*Queries)(nil)
