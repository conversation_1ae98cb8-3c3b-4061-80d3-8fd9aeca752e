# Issues to Fix

## Static Page Modifications:

1. **Initial Screen Navigation**: The app currently navigates from a screen named "index" on first launch. This should be changed to use a splash screen without displaying the "index" title.

   <img src="./images/IMG_2012.PNG" alt="IMG_2012" style="zoom:50%;" />

2. **Profile Page Logo Interaction**: (app/tabs/login.tsx) The logo on the login page has a `TouchableOpacity` wrapper that makes it clickable. It's unclear whether this should implement organization switching functionality (dropdown to show org list for switching). This needs discussion with <PERSON>. If org switching is required, we can implement it using the existing `OrgSwitch` component. If not needed, remove the `TouchableOpacity` to avoid click.

3. **Post Details Screen Image Navigation**: The `PostDetailsScreen` (`/app/explore/posts/PostDetailsScreen.tsx`) currently lacks horizontal swipe functionality for image navigation. Please implement it considering using this following the pattern used in `EventDetailsScreen` (`/app/explore/events/components/`):
   - `ImageViewer` (for tap-to-open with zoom/pan capabilities)
   - `ImageCarousel` (for in-page horizontal swiping with indicator in top-left corner)

4. **Identity Information Input Focus**: On the identity card information input page (`/user-profile/identification/IdentityInfoScreen.tsx`), the screen should scroll to focus on the input field when switching between different input types.

## Token/Storage Related Modifications:

1. **Token and Storage Management Issues**: There are critical issues with token and storage management. When tokens expire and users are logged out, token refresh is not performed, and there are error scenarios after logout:
   
   **Error Scenarios:**
   - After initial token expiration and logout, the app still attempts to fetch volunteer API on event details pages
   - Clicking on "Past Events" or "My Stats" can pass authentication validation and reach related pages, trigger failed API calls, then require manual navigation back to home page before returning to normal behavior

2. **Language Preference Management**: Language preference management is inconsistent. New users' system interface defaults to English, but should default to Traditional Chinese. It's unclear whether this default is set on the frontend or transmitted from the backend.

## API Related Modifications:

1. **Change Phone Flow API Integration Issues**:
   - **Missing Code Verification Screen**: The current phone verification page (`app/user-profile/changePhone/CodeVerificationScreen.tsx`) was accidentally deleted. I've restored it from GitHub for testing purposes. Consider refactoring all phone/code input components.
   - **OTP Validation Error**: After entering OTP, the server returns a 400 error. I stop testing of subsequent flow steps from here.

2. **Passport Information Country Code Display**: In the passport information form (`app/user-profile/identification/IdentityInfoScreen.tsx`), we use a custom country picker component (`common_modules/CountryPicker`) which submits country codes (e.g., "CN"). This results in the admin view verification page displaying country codes like "CN" instead of full country names for the issuing country field.

3. **Dashboard Page Image Format Issues** (`app/tabs/dashboard.tsx`):
   In the posts section of this page, the backend doesn't return a unified image format (events start with "http", while posts start without "http" as "/upload"). This requires calculating the cover image path, which causes rendering flickering. This issue is also mentioned in the webapp to-fix.md home interface section.