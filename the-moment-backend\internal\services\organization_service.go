package services

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/utils"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5" // Added for type assertion
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
)

// OrganizationService defines the interface for organization-related operations.
// This interface will be used for mocking and dependency injection.
type OrganizationServiceInterface interface {
	CheckIfUserOwnsAnyOrganization(ctx context.Context, userID uuid.UUID) (bool, error)
	CheckUserOrganizationRole(ctx context.Context, opts payloads.CheckUserOrganizationRoleOptions) (bool, error)
	UploadOrganizationLogo(ctx context.Context, orgID uuid.UUID, actingUserID uuid.UUID, fileHeader *multipart.FileHeader) (db.Organization, error)
	// Add other methods from OrganizationService struct as needed by consumers for mocking
}

var (
	// ErrOrgNotFound        = errors.New("organization not found") // Moved to payloads
	ErrMembershipNotFound = errors.New("user organization membership not found")
	ErrDefaultOrgNotSetup = errors.New("default organization is not set up")
	// ErrAlreadyMember      = errors.New("user is already a member of this organization") // Moved to payloads
	// ErrNotMember          = errors.New("user is not a member of this organization") // Moved to payloads
	// ErrCannotRemoveOwner  = errors.New("organization owner cannot be removed. Transfer ownership first.") // Moved to payloads
	ErrUpdateConflict = errors.New("update conflict, data may have changed")
)

const DefaultOrganizationName = "Default Organization"
const DefaultOrganizationStatusPendingSetup = "pending_setup"
const DefaultOrganizationStatusActive = "active"

// OrganizationService provides methods for managing organizations.
type OrganizationService struct {
	Queries *db.Queries
	Pool    *pgxpool.Pool // Added for direct pool access for transactions
}

// NewOrganizationService creates a new OrganizationService.
func NewOrganizationService(queries *db.Queries, pool *pgxpool.Pool) *OrganizationService {
	return &OrganizationService{Queries: queries, Pool: pool}
}

// CheckIfUserOwnsAnyOrganization checks if a user is an owner of any organization.
func (s *OrganizationService) CheckIfUserOwnsAnyOrganization(ctx context.Context, userID uuid.UUID) (bool, error) {
	count, err := s.Queries.CountUserOwnedOrganizations(ctx, userID)
	if err != nil {
		// It's possible CountUserOwnedOrganizations returns 0 and no error if the user owns no orgs,
		// or if the user doesn't exist in the table at all. sqlc might return pgx.ErrNoRows if the query
		// was expected to return a row but didn't (e.g. if :one was used and no rows matched, but count(*) always returns a row).
		// For count(*), an error usually means a DB connection issue or syntax error.
		log.Ctx(ctx).Error().Err(err).Msgf("Error counting owned organizations for user %s", userID)
		return false, errors.New("failed to check organization ownership due to database error")
	}
	return count > 0, nil
}

// CreateOrganization creates a new organization.
// The specified OwnerUserID in the input becomes the organization's owner.
// The creatorUserID (user making the API call) is also added as a member (e.g., admin).
func (s *OrganizationService) CreateOrganization(ctx context.Context, creatorUserID uuid.UUID, input payloads.CreateOrganizationRequest) (db.Organization, error) {
	// Ensure the designated OwnerUserID is a valid user
	_, err := s.Queries.GetUserByID(ctx, input.OwnerUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("owner_user_id", input.OwnerUserID.String()).Msg("Designated Owner User ID not found for creating organization")
			return db.Organization{}, payloads.ErrOrgNotFound // Changed back from ErrUserNotFound, error message is specific
		}
		log.Ctx(ctx).Error().Err(err).Str("owner_user_id", input.OwnerUserID.String()).Msg("Error verifying designated Owner User ID for creating organization")
		return db.Organization{}, fmt.Errorf("could not verify designated owner user: %w", err)
	}

	// Ensure the creatorUserID is also a valid user (could be redundant if API gateway/auth already ensures this, but good for service layer integrity)
	if creatorUserID != input.OwnerUserID { // Only check if different, already checked OwnerUserID
		_, err := s.Queries.GetUserByID(ctx, creatorUserID)
		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				log.Ctx(ctx).Warn().Err(err).Str("creator_user_id", creatorUserID.String()).Msg("Creator User ID not found for creating organization")
				// This implies an issue with the token or upstream validation, as creatorUserID comes from authenticated claims.
				return db.Organization{}, fmt.Errorf("creator user ID %s not found, which is required to create organization: %w", creatorUserID.String(), payloads.ErrOrgNotFound) // Changed back from ErrUserNotFound
			}
			log.Ctx(ctx).Error().Err(err).Str("creator_user_id", creatorUserID.String()).Msg("Error verifying Creator User ID for creating organization")
			return db.Organization{}, fmt.Errorf("could not verify creator user: %w", err)
		}
	}

	var descPtr *string
	if input.Description != "" {
		descPtr = &input.Description
	}

	var imageURLPtr *string
	if input.ImageURL != "" {
		imageURLPtr = &input.ImageURL
	}

	var themeColorPtr *string
	if input.ThemeColor != "" {
		themeColorPtr = &input.ThemeColor
	}

	params := db.CreateOrganizationParams{
		Name:         input.Name,
		Description:  descPtr,
		IsDefaultOrg: input.IsDefault,
		ImageUrl:     imageURLPtr,
		ThemeColor:   themeColorPtr,
		Status:       DefaultOrganizationStatusPendingSetup, // New organizations start as pending_setup
	}

	org, err := s.Queries.CreateOrganization(ctx, params)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("failed to create organization in db")
		return db.Organization{}, fmt.Errorf("could not create organization: %w", err)
	}

	// Add the designated owner to the organization with the 'admin' role.
	// Ownership is now managed via the user_organization_memberships table.
	ownerMembershipParams := db.AddUserToOrganizationParams{
		UserID:               input.OwnerUserID, // Use OwnerUserID from the input payload
		OrganizationID:       org.ID,
		Role:                 "admin", // Designate owner as 'admin' in the membership table
		IsActive:             true,
		NotificationsEnabled: true,
	}
	_, err = s.Queries.AddUserToOrganization(ctx, ownerMembershipParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", input.OwnerUserID.String()).Str("org_id", org.ID.String()).Msg("CRITICAL: Failed to add designated owner (as admin) to organization_memberships after creating organization. Org created but unusable.")
		// Attempt to delete the organization to prevent an orphaned record if adding owner fails
		if delErr := s.Queries.DeleteOrganization(ctx, org.ID); delErr != nil {
			log.Ctx(ctx).Error().Err(delErr).Str("org_id", org.ID.String()).Msg("Failed to cleanup organization after failing to add owner.")
		}
		return db.Organization{}, fmt.Errorf("failed to add owner to organization: %w", err)
	}
	log.Ctx(ctx).Info().Str("user_id", input.OwnerUserID.String()).Str("org_id", org.ID.String()).Str("role", "admin").Msg("Successfully added designated owner to organization with admin role.")

	// If the creator is different from the designated owner, add the creator with an 'admin' role.
	if creatorUserID != input.OwnerUserID {
		log.Ctx(ctx).Info().Str("creator_user_id", creatorUserID.String()).Str("org_id", org.ID.String()).Msg("Creator is different from owner, adding creator as admin.")
		creatorMembershipParams := db.AddUserToOrganizationParams{
			UserID:               creatorUserID,
			OrganizationID:       org.ID,
			Role:                 "admin", // Or a different role like "creator_admin"
			IsActive:             true,
			NotificationsEnabled: true, // Or based on user preference/system default
		}
		_, err = s.Queries.AddUserToOrganization(ctx, creatorMembershipParams)
		if err != nil {
			// This is also problematic. The org is created, owner is a member.
			// Log the error. The system admin might need to manually add the creator or fix the issue.
			log.Ctx(ctx).Error().Err(err).Str("user_id", creatorUserID.String()).Str("org_id", org.ID.String()).Msg("Failed to add creator (superadmin) to organization_memberships after creating organization and adding owner.")
			// Do not return an error here that masks the successful organization creation and owner addition,
			// but log it prominently. The org is still usable by the owner.
		} else {
			log.Ctx(ctx).Info().Str("user_id", creatorUserID.String()).Str("org_id", org.ID.String()).Str("role", "admin").Msg("Successfully added creator (superadmin) to organization.")
		}
	} else {
		log.Ctx(ctx).Info().Str("user_id", creatorUserID.String()).Str("org_id", org.ID.String()).Msg("Creator is the same as the owner. No separate membership needed for creator.")
	}

	return org, nil
}

// GetOrganizationByID retrieves a single organization by its ID.
func (s *OrganizationService) GetOrganizationByID(ctx context.Context, orgID uuid.UUID) (db.Organization, error) {
	org, err := s.Queries.GetOrganizationByID(ctx, orgID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return db.Organization{}, payloads.ErrOrgNotFound
		}
		return db.Organization{}, fmt.Errorf("failed to get organization by ID: %w", err)
	}
	return org, nil
}

// GetDefaultOrganization retrieves the default organization.
// It's a good practice to ensure this exists.
func (s *OrganizationService) GetDefaultOrganization(ctx context.Context) (db.Organization, error) {
	org, err := s.Queries.GetDefaultOrganization(ctx)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return db.Organization{}, ErrDefaultOrgNotSetup
		}
		log.Ctx(ctx).Error().Err(err).Msg("failed to get default organization from db")
		return db.Organization{}, fmt.Errorf("could not retrieve default organization: %w", err)
	}
	return org, nil
}

// GetOrCreateDefaultOrganization ensures the default organization exists, creating it if necessary.
// This should ideally be run at application startup or by an admin.
// The owner_user_id for the default org needs careful consideration (e.g., a system user or the first admin).
// For now, we'll require an ownerID if creating.
func (s *OrganizationService) GetOrCreateDefaultOrganization(ctx context.Context, systemUserID uuid.UUID) (db.Organization, error) {
	org, err := s.GetDefaultOrganization(ctx)
	if err == nil {
		return org, nil // Default org already exists
	}

	if !errors.Is(err, ErrDefaultOrgNotSetup) {
		// Some other error occurred
		return db.Organization{}, err
	}

	// Default org not found, let's create it.
	log.Ctx(ctx).Info().Msg("Default organization not found, attempting to create it.")
	if systemUserID == uuid.Nil {
		return db.Organization{}, errors.New("systemUserID is required to create default organization")
	}

	defaultDescription := "The default global organization for all new users."
	defaultImageURL := "https://example.com/default_org_logo.png"
	defaultThemeColor := "#CCCCCC"

	defaultOrgParams := db.CreateOrganizationParams{
		Name:         DefaultOrganizationName,
		Description:  &defaultDescription,
		IsDefaultOrg: true,
		ImageUrl:     &defaultImageURL,
		ThemeColor:   &defaultThemeColor,
		Status:       DefaultOrganizationStatusActive, // Default org is created as active
	}
	createdOrg, dbErr := s.Queries.CreateOrganization(ctx, defaultOrgParams)
	if dbErr != nil {
		log.Ctx(ctx).Error().Err(dbErr).Msg("failed to create default organization in db")
		return db.Organization{}, fmt.Errorf("could not create default organization: %w", dbErr)
	}

	// Add the system user as an 'admin' or 'owner' of the default org
	_, memberErr := s.Queries.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:               systemUserID,
		OrganizationID:       createdOrg.ID,
		Role:                 "admin", // Or 'owner'
		IsActive:             true,
		NotificationsEnabled: false, // Default org notifications might be off by default for members
	})
	if memberErr != nil {
		log.Ctx(ctx).Error().Err(memberErr).Str("organization_id", createdOrg.ID.String()).Msg("failed to add system user to default organization")
		// As this is a critical setup step, an error here is serious.
		return createdOrg, fmt.Errorf("default organization created, but failed to add system user as member: %w", memberErr)
	}

	log.Ctx(ctx).Info().Str("organization_id", createdOrg.ID.String()).Msg("Default organization created successfully.")
	return createdOrg, nil
}

// IsUserMemberOfOrganization checks if a user is an active member of a specific organization.
func (s *OrganizationService) IsUserMemberOfOrganization(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) (bool, error) {
	exists, err := s.Queries.IsUserMemberOfOrganization(ctx, db.IsUserMemberOfOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
	})
	if err != nil {
		// Log the error but don't necessarily treat pgx.ErrNoRows as a critical application error here,
		// as EXISTS will return false in that case (which is not an error for the boolean check).
		// However, other DB errors should be reported.
		log.Ctx(ctx).Error().Err(err).Msg("Error checking user organization membership")
		return false, fmt.Errorf("could not check organization membership: %w", err)
	}
	return exists, nil
}

// UpdateOrganization updates an existing organization's mutable fields.
// If OwnerUserID is provided, it handles the change of ownership through the user_organization_memberships table.
func (s *OrganizationService) UpdateOrganization(ctx context.Context, orgID uuid.UUID, input payloads.UpdateOrganizationRequest) (db.Organization, error) {
	if s.Pool == nil {
		log.Ctx(ctx).Error().Msg("Database pool is not initialized in OrganizationService, cannot start transaction for UpdateOrganization")
		return db.Organization{}, errors.New("internal server error: database pool not configured for transaction")
	}

	// Begin transaction
	tx, err := s.Pool.Begin(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("org_id", orgID.String()).Msg("Failed to begin transaction for UpdateOrganization")
		return db.Organization{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	qtx := s.Queries.WithTx(tx)

	var finalOrg db.Organization

	currentOrg, txErr := qtx.GetOrganizationByID(ctx, orgID)
	if txErr != nil {
		if errors.Is(txErr, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(txErr).Str("org_id", orgID.String()).Msg("Organization not found for update within transaction")
			return db.Organization{}, payloads.ErrOrgNotFound
		}
		log.Ctx(ctx).Error().Err(txErr).Str("org_id", orgID.String()).Msg("Failed to get organization by ID before update within transaction")
		return db.Organization{}, fmt.Errorf("could not retrieve organization for update: %w", txErr)
	}

	// Prepare organization detail update parameters
	orgUpdateParams := db.UpdateOrganizationParams{
		ID:          orgID,
		Name:        currentOrg.Name,
		Description: currentOrg.Description,
		ImageUrl:    currentOrg.ImageUrl,
		ThemeColor:  currentOrg.ThemeColor,
		Status:      currentOrg.Status,
	}

	if input.Name != nil {
		orgUpdateParams.Name = *input.Name
	}
	if input.Description != nil {
		orgUpdateParams.Description = input.Description
	}
	if input.ImageURL != nil {
		orgUpdateParams.ImageUrl = input.ImageURL
	}
	if input.ThemeColor != nil {
		orgUpdateParams.ThemeColor = input.ThemeColor
	}
	if input.Status != nil {
		orgUpdateParams.Status = *input.Status
	}

	// Handle Owner Change, if requested
	if input.OwnerUserID != nil {
		newOwnerCandidateID := *input.OwnerUserID

		currentOwnerID, err := qtx.GetCurrentOrganizationOwner(ctx, orgID)
		isCurrentOwnerFound := true
		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				isCurrentOwnerFound = false // No current owner
			} else {
				log.Ctx(ctx).Error().Err(err).Str("org_id", orgID.String()).Msg("Failed to get current organization owner")
				return db.Organization{}, fmt.Errorf("failed to get current organization owner: %w", err)
			}
		}

		// Proceed with owner change only if new owner is different from current (or if no current owner and new one is specified)
		if !isCurrentOwnerFound || (isCurrentOwnerFound && currentOwnerID != newOwnerCandidateID) {
			// Demote old owner if exists and is different from new owner
			if isCurrentOwnerFound && currentOwnerID != newOwnerCandidateID {
				_, err = qtx.UpdateUserOrganizationMembershipRole(ctx, db.UpdateUserOrganizationMembershipRoleParams{
					UserID:         currentOwnerID,
					OrganizationID: orgID,
					Role:           "admin", // Demote to admin
				})
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Str("user_id", currentOwnerID.String()).Str("org_id", orgID.String()).Msg("Failed to demote old owner")
					return db.Organization{}, fmt.Errorf("failed to demote old owner: %w", err)
				}
			}

			// Promote/Add new owner
			// Check if the new owner is already a user in the system
			_, err := qtx.GetUserByID(ctx, newOwnerCandidateID)
			if err != nil {
				if errors.Is(err, pgx.ErrNoRows) {
					log.Ctx(ctx).Warn().Err(err).Str("new_owner_user_id", newOwnerCandidateID.String()).Msg("New owner user ID not found")
					return db.Organization{}, &payloads.NotFoundError{Message: fmt.Sprintf("new owner user with ID %s not found", newOwnerCandidateID.String())}
				}
				log.Ctx(ctx).Error().Err(err).Str("new_owner_user_id", newOwnerCandidateID.String()).Msg("Error verifying new owner user ID")
				return db.Organization{}, fmt.Errorf("could not verify new owner user: %w", err)
			}

			newOwnerMembership, err := qtx.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
				UserID:         newOwnerCandidateID,
				OrganizationID: orgID,
			})
			if err != nil {
				if errors.Is(err, pgx.ErrNoRows) { // New owner is not a member, add them
					_, err = qtx.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
						UserID:               newOwnerCandidateID,
						OrganizationID:       orgID,
						Role:                 "owner",
						IsActive:             true,
						NotificationsEnabled: true,
					})
					if err != nil {
						log.Ctx(ctx).Error().Err(err).Str("user_id", newOwnerCandidateID.String()).Str("org_id", orgID.String()).Msg("Failed to add new owner to organization")
						return db.Organization{}, fmt.Errorf("failed to add new owner: %w", err)
					}
				} else { // Other error fetching membership
					log.Ctx(ctx).Error().Err(err).Str("user_id", newOwnerCandidateID.String()).Str("org_id", orgID.String()).Msg("Failed to get new owner's membership status")
					return db.Organization{}, fmt.Errorf("failed to get new owner's membership: %w", err)
				}
			} else { // New owner is already a member, update their role if not already owner
				if newOwnerMembership.Role != "owner" {
					_, err = qtx.UpdateUserOrganizationMembershipRole(ctx, db.UpdateUserOrganizationMembershipRoleParams{
						UserID:         newOwnerCandidateID,
						OrganizationID: orgID,
						Role:           "owner",
					})
					if err != nil {
						log.Ctx(ctx).Error().Err(err).Str("user_id", newOwnerCandidateID.String()).Str("org_id", orgID.String()).Msg("Failed to promote existing member to owner")
						return db.Organization{}, fmt.Errorf("failed to promote new owner: %w", err)
					}
				}
			}
		}
	}

	// Update Organization Details (within the same transaction)
	var txOrg db.Organization
	txOrg, txErr = qtx.UpdateOrganization(ctx, orgUpdateParams)
	if txErr != nil {
		log.Ctx(ctx).Error().Err(txErr).Str("org_id", orgID.String()).Msg("Failed to update organization details in transaction")
		return db.Organization{}, fmt.Errorf("failed to update organization details: %w", txErr)
	}
	finalOrg = txOrg

	// Commit transaction
	err = tx.Commit(ctx)

	if err != nil {
		// Check if the error is of type *payloads.NotFoundError for user not found
		var notFoundErr *payloads.NotFoundError
		if errors.As(err, &notFoundErr) || errors.Is(err, payloads.ErrOrgNotFound) {
			return db.Organization{}, err
		}
		log.Ctx(ctx).Error().Err(err).Str("org_id", orgID.String()).Msg("Transaction failed for UpdateOrganization")
		return db.Organization{}, fmt.Errorf("organization update transaction failed: %w", err)
	}

	return finalOrg, nil
}

// DeleteOrganization deletes an organization.
// Ensure the calling user has permission. Consider soft delete.
// This is a hard delete as per current SQLC query.
func (s *OrganizationService) DeleteOrganization(ctx context.Context, orgID uuid.UUID) error {
	// Check if it's the default organization - prevent deletion
	org, err := s.GetOrganizationByID(ctx, orgID)
	if err != nil {
		return err // Handles payloads.ErrOrgNotFound if returned by GetOrganizationByID
	}
	if org.IsDefaultOrg {
		return payloads.ErrCannotDeleteDefaultOrg
	}

	// TODO: Add more checks, e.g., are there members other than the owner?
	// Are there associated resources that need to be handled?

	err = s.Queries.DeleteOrganization(ctx, orgID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("organization_id", orgID.String()).Msg("failed to delete organization from db")
		return fmt.Errorf("could not delete organization: %w", err)
	}
	log.Ctx(ctx).Info().Str("organization_id", orgID.String()).Msg("organization deleted successfully")
	return nil
}

// ListOrganizations retrieves a paginated list of all organizations and the total count.
func (s *OrganizationService) ListOrganizations(ctx context.Context, limit int, offset int) ([]db.Organization, int64, error) {
	if limit <= 0 {
		limit = 10 // Default limit
	}
	if offset < 0 {
		offset = 0 // Default offset
	}

	rows, err := s.Queries.ListOrganizations(ctx, db.ListOrganizationsParams{Limit: int32(limit), Offset: int32(offset)})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("failed to list organizations from db")
		return nil, 0, fmt.Errorf("could not list organizations: %w", err)
	}

	if len(rows) == 0 {
		return []db.Organization{}, 0, nil
	}

	// totalCount is the same for all rows, take it from the first.
	// SQLC should generate TotalCount as part of the ListOrganizationsRow struct.
	// If the field name is different, adjust s.Queries.ListOrganizationsRow.TotalCount accordingly.
	var totalCount int64
	if len(rows) > 0 { // Ensure there's at least one row to get TotalCount from
		// Assuming sqlc generated a struct like:
		// type ListOrganizationsRow struct {
		// ID          uuid.UUID
		// Name        string
		// Description *string
		// OwnerUserID uuid.UUID
		// ImageURL    *string
		// ThemeColor  *string
		// Status      string
		// CreatedAt   time.Time
		// UpdatedAt   time.Time
		// IsDefaultOrg bool
		// TotalCount  int64
		// }
		// The actual field name for total_count might be TotalCount or Total_count
		// depending on sqlc's naming strategy in your sqlc.yaml.
		// We'll assume it's TotalCount for now.
		totalCount = rows[0].TotalCount
	}

	orgs := make([]db.Organization, len(rows))
	for i, row := range rows {
		orgs[i] = db.Organization{
			ID:          row.ID,
			Name:        row.Name,
			Description: row.Description,
			// OwnerUserID has been removed from db.Organization and ListOrganizationsRow
			IsDefaultOrg: row.IsDefaultOrg,
			ImageUrl:     row.ImageUrl, // Make sure this matches the struct field from sqlc
			ThemeColor:   row.ThemeColor,
			Status:       row.Status,
			CreatedAt:    row.CreatedAt,
			UpdatedAt:    row.UpdatedAt,
		}
	}
	return orgs, totalCount, nil
}

// AddUserToOrganizationInput defines the input for adding a user to an organization
type AddUserToOrganizationInput struct {
	UserID         uuid.UUID `json:"user_id" validate:"required"`
	OrganizationID uuid.UUID `json:"organization_id" validate:"required"`
	Role           string    `json:"role" validate:"required,min=3,max=50"` // e.g., "member", "admin"
}

// AddUserToOrganization adds a user to an organization with a specific role.
// This is a more general method that can be called by specific "join" logic.
func (s *OrganizationService) AddUserToOrganization(ctx context.Context, input payloads.AddUserToOrganizationInput) (db.UserOrganizationMembership, error) {
	// Validate input if needed (validator assumed elsewhere or in handler)
	// if err := validator.Validate(input); err != nil { ... }

	// Check if user is already a member
	_, err := s.Queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
		UserID:         input.UserID,
		OrganizationID: input.OrganizationID,
	})
	if err == nil {
		return db.UserOrganizationMembership{}, payloads.ErrAlreadyMember // Or handle as idempotent success // Use payloads.ErrAlreadyMember
	}
	if !errors.Is(err, pgx.ErrNoRows) {
		log.Ctx(ctx).Error().Err(err).Msg("Error checking existing membership")
		return db.UserOrganizationMembership{}, fmt.Errorf("failed to check existing membership: %w", err)
	}

	// If not found (ErrNoRows), proceed to add
	// Use defaults for IsActive and NotificationsEnabled if not provided in input struct
	isActive := true                                                                           // Default for new member
	if !input.IsActive && input.IsActive != (payloads.AddUserToOrganizationInput{}).IsActive { // Check if explicitly set to false
		isActive = input.IsActive
	}
	notificationsEnabled := true                                                                                                   // Default for new member
	if !input.NotificationsEnabled && input.NotificationsEnabled != (payloads.AddUserToOrganizationInput{}).NotificationsEnabled { // Check if explicitly set to false
		notificationsEnabled = input.NotificationsEnabled
	}

	params := db.AddUserToOrganizationParams{
		UserID:               input.UserID,
		OrganizationID:       input.OrganizationID,
		Role:                 input.Role,
		IsActive:             isActive,
		NotificationsEnabled: notificationsEnabled,
	}

	membership, err := s.Queries.AddUserToOrganization(ctx, params) // Pass the SQLC params struct
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to add user to organization in DB")
		return db.UserOrganizationMembership{}, fmt.Errorf("could not add user to organization: %w", err)
	}
	return membership, nil
}

// GetUserMembershipDetails retrieves a user's membership details for a specific organization.
func (s *OrganizationService) GetUserMembershipDetails(ctx context.Context, userID, orgID uuid.UUID) (db.UserOrganizationMembership, error) {
	membership, err := s.Queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
		UserID:         userID,
		OrganizationID: orgID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return db.UserOrganizationMembership{}, ErrMembershipNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Str("organization_id", orgID.String()).Msg("failed to get user organization membership from db")
		return db.UserOrganizationMembership{}, fmt.Errorf("could not retrieve membership: %w", err)
	}
	return membership, nil
}

// ListUserOrganizations retrieves all organizations a user is an active member of.
func (s *OrganizationService) ListUserOrganizations(ctx context.Context, userID uuid.UUID) ([]db.Organization, error) {
	orgs, err := s.Queries.ListUserOrganizations(ctx, userID)
	if err != nil {
		// Consider if pgx.ErrNoRows should be handled differently (e.g., return empty slice, nil error)
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Msg("failed to list user organizations from db")
		if errors.Is(err, pgx.ErrNoRows) {
			return []db.Organization{}, nil // Return empty slice if no orgs found for user
		}
		return nil, fmt.Errorf("could not list user's organizations: %w", err)
	}
	return orgs, nil
}

// ListOrganizationMembersWithDetails retrieves a list of members for a given organization with their membership details.
func (s *OrganizationService) ListOrganizationMembersWithDetails(ctx context.Context, orgID uuid.UUID) ([]db.ListOrganizationMembersRow, error) {
	members, err := s.Queries.ListOrganizationMembers(ctx, orgID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("organization_id", orgID.String()).Msg("failed to list organization members from db")
		return nil, fmt.Errorf("could not list organization members: %w", err)
	}
	return members, nil
}

type UpdateMembershipInput struct {
	Role                 *string `json:"role,omitempty" validate:"omitempty,min=3,max=50"`
	IsActive             *bool   `json:"is_active,omitempty"`
	NotificationsEnabled *bool   `json:"notifications_enabled,omitempty"`
}

// UpdateUserMembershipDetails updates a user's role, active status, or notification preferences within an organization.
// Ensure the calling user has permission (e.g., org admin, or user themselves for some settings).
func (s *OrganizationService) UpdateUserMembershipDetails(ctx context.Context, userID, orgID uuid.UUID, input payloads.UpdateMembershipInput) (db.UserOrganizationMembership, error) {
	currentMembership, err := s.GetUserMembershipDetails(ctx, userID, orgID)
	if err != nil {
		return db.UserOrganizationMembership{}, err // Handles ErrMembershipNotFound
	}

	// Prevent owner from being made inactive or have role changed if they are the sole owner and it's not a default org
	// This logic can be more complex depending on business rules for ownership transfer.
	// org, err := s.GetOrganizationByID(ctx, orgID) // org variable is no longer used
	// if err != nil {
	// 	return db.UserOrganizationMembership{}, fmt.Errorf("could not retrieve organization details: %w", err)
	// }
	// Ownership checks are removed as OwnerUserID is no longer on the org struct.
	// Authorization for role changes should be handled by the calling admin's permissions.

	params := db.UpdateUserOrganizationMembershipDetailsParams{
		UserID:               userID,
		OrganizationID:       orgID,
		Role:                 currentMembership.Role,
		IsActive:             currentMembership.IsActive,
		NotificationsEnabled: currentMembership.NotificationsEnabled,
	}

	changed := false
	if input.Role != nil && *input.Role != currentMembership.Role {
		params.Role = *input.Role
		changed = true
	}
	if input.IsActive != nil && *input.IsActive != currentMembership.IsActive {
		params.IsActive = *input.IsActive
		changed = true
	}
	if input.NotificationsEnabled != nil && *input.NotificationsEnabled != currentMembership.NotificationsEnabled {
		params.NotificationsEnabled = *input.NotificationsEnabled
		changed = true
	}

	if !changed {
		return currentMembership, nil // No changes provided
	}

	updatedMembership, err := s.Queries.UpdateUserOrganizationMembershipDetails(ctx, params)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Str("organization_id", orgID.String()).Msg("failed to update user organization membership in db")
		return db.UserOrganizationMembership{}, fmt.Errorf("could not update membership: %w", err)
	}
	log.Ctx(ctx).Info().Str("user_id", userID.String()).Str("organization_id", orgID.String()).Msg("user organization membership updated")
	return updatedMembership, nil
}

// RemoveUserFromOrganization removes a user from an organization.
// It ensures the owner cannot be removed this way.
func (s *OrganizationService) RemoveUserFromOrganization(ctx context.Context, userID, orgID uuid.UUID) error {
	_, err := s.GetUserMembershipDetails(ctx, userID, orgID) // Check if membership exists
	if err != nil {
		if errors.Is(err, ErrMembershipNotFound) {
			return payloads.ErrNotMember // Use payloads.ErrNotMember
		}
		return err // Other errors during check
	}

	// Business rule: Cannot remove the owner of the organization this way.
	// Ownership transfer should be a separate, explicit process.
	// org, err := s.GetOrganizationByID(ctx, orgID) // org variable is no longer used
	// if err != nil {
	// 	return fmt.Errorf("could not retrieve organization details for validation: %w", err)
	// }
	// Ownership checks are removed as OwnerUserID is no longer on the org struct.
	// Authorization for removing users, including former owners, should be handled by the calling admin's permissions.
	// The concept of a single "owner" on the organization table is deprecated.
	// Roles like 'admin' or 'owner' in user_organization_memberships define authority.

	err = s.Queries.RemoveUserFromOrganization(ctx, db.RemoveUserFromOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Str("organization_id", orgID.String()).Msg("failed to remove user from organization in db")
		return fmt.Errorf("could not remove user from organization: %w", err)
	}
	log.Ctx(ctx).Info().Str("user_id", userID.String()).Str("organization_id", orgID.String()).Msg("user removed from organization")
	return nil
}

// JoinOrganization allows a user to join an organization with a default "member" role.
func (s *OrganizationService) JoinOrganization(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) (db.UserOrganizationMembership, error) {
	// Maybe add checks here: Does org exist? Does user exist?
	// Construct the input for AddUserToOrganization
	joinInput := payloads.AddUserToOrganizationInput{
		UserID:         userID,
		OrganizationID: orgID,
		Role:           "member", // Default role for joining
		// Use default IsActive=true, NotificationsEnabled=true from AddUserToOrganization logic
	}
	return s.AddUserToOrganization(ctx, joinInput) // Pass the correct input struct
}

// LeaveOrganization allows a user to leave an organization.
func (s *OrganizationService) LeaveOrganization(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) error {
	// Add checks: Does org exist? Is user a member?
	// Cannot leave if owner?
	membership, err := s.Queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{UserID: userID, OrganizationID: orgID})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.ErrNotMember
		}
		return fmt.Errorf("failed to check membership before leaving: %w", err)
	}
	if membership.Role == "owner" {
		// return ErrCannotRemoveOwner
		return payloads.ErrCannotRemoveOwner // Ensure this uses payloads version too
	}

	return s.Queries.RemoveUserFromOrganization(ctx, db.RemoveUserFromOrganizationParams{UserID: userID, OrganizationID: orgID})
}

// AddUserToDefaultOrganizationAndGrantRole is called typically during user registration.
func (s *OrganizationService) AddUserToDefaultOrganizationAndGrantRole(ctx context.Context, userID uuid.UUID, role string) error {
	defaultOrg, err := s.GetOrCreateDefaultOrganization(ctx, uuid.Nil) // Still has uuid.Nil issue for creation
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get or create default organization")
		return fmt.Errorf("could not ensure default organization exists: %w", err)
	}

	addInput := payloads.AddUserToOrganizationInput{
		UserID:         userID,
		OrganizationID: defaultOrg.ID,
		Role:           role,
		// Use default IsActive=true, NotificationsEnabled=true from AddUserToOrganization logic
	}

	_, err = s.AddUserToOrganization(ctx, addInput) // Pass the correct input struct

	if err != nil && !errors.Is(err, payloads.ErrAlreadyMember) { // Ignore payloads.ErrAlreadyMember
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Str("org_id", defaultOrg.ID.String()).Msg("Failed to add user to default organization")
		return fmt.Errorf("failed to add user to default organization: %w", err)
	}
	if errors.Is(err, payloads.ErrAlreadyMember) { // Check payloads.ErrAlreadyMember
		log.Ctx(ctx).Warn().Str("user_id", userID.String()).Str("org_id", defaultOrg.ID.String()).Msg("User already a member of the default organization. This is okay.")
	}

	log.Ctx(ctx).Info().Str("user_id", userID.String()).Str("org_id", defaultOrg.ID.String()).Str("role", role).Msg("User successfully added to default organization with role.")
	return nil
}

// CheckUserOrganizationRole checks if a user has one of the required roles in an organization.
func (s *OrganizationService) CheckUserOrganizationRole(ctx context.Context, opts payloads.CheckUserOrganizationRoleOptions) (bool, error) {
	user, err := s.Queries.GetUserByID(ctx, opts.UserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false, nil // User not found, so can't have the role.
		}
		log.Ctx(ctx).Error().Err(err).Str("user_id", opts.UserID.String()).Msg("failed to get user for role check")
		return false, fmt.Errorf("failed to get user for role check: %w", err)
	}

	// TODO: Confirm "superadmin" is the correct role name in db.UserRole enum.
	if string(user.Role) == "superadmin" {
		return true, nil
	}

	membership, err := s.Queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
		UserID:         opts.UserID,
		OrganizationID: opts.OrganizationID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false, payloads.ErrNotMember // User is not a member, so doesn't have the role
		}
		return false, fmt.Errorf("failed to get user membership: %w", err)
	}

	if !membership.IsActive {
		return false, nil // Inactive members don't have roles considered active
	}

	for _, requiredRole := range opts.RequiredRoles {
		if membership.Role == requiredRole {
			return true, nil
		}
	}

	return false, nil // User is a member but doesn't have any of the required roles
}

// UploadOrganizationLogo handles storing the organization's logo and updating the record.
func (s *OrganizationService) UploadOrganizationLogo(ctx context.Context, orgID uuid.UUID, actingUserID uuid.UUID, fileHeader *multipart.FileHeader) (db.Organization, error) {
	logger := log.Ctx(ctx).With().Str("organizationID", orgID.String()).Str("actingUserID", actingUserID.String()).Logger()

	// Authorization check is now performed in the handler before calling this service method.
	logger.Info().Msg("Proceeding with logo upload.")

	// 2. File validation (similar to user profile picture)
	// The Content-Type check from the header is unreliable, as seen in tests.
	// The validation logic now in the handler is more robust. We can simplify this part.
	const maxFileSize = 5 * 1024 * 1024 // 5MB
	if fileHeader.Size > maxFileSize {
		return db.Organization{}, &payloads.FileProcessingError{Message: "File size exceeds the 5MB limit."}
	}
	mimeType := fileHeader.Header.Get("Content-Type")
	allowedMimeTypes := map[string]bool{"image/jpeg": true, "image/png": true, "image/gif": true, "image/webp": true}
	if !allowedMimeTypes[mimeType] {
		return db.Organization{}, &payloads.FileProcessingError{Message: "Invalid file type. Only JPEG, PNG, GIF, WebP are allowed."}
	}

	// 3. Prepare file path and save file
	newFileName := uuid.New().String() + filepath.Ext(fileHeader.Filename)
	relativePathDir := filepath.Join("organizations", orgID.String(), "logos")
	storageDir := filepath.Join(".", "uploads", relativePathDir)
	storagePath := filepath.Join(storageDir, newFileName)

	if err := os.MkdirAll(storageDir, os.ModePerm); err != nil {
		logger.Error().Err(err).Str("path", storageDir).Msg("Failed to create directory for organization logo")
		return db.Organization{}, &payloads.FileProcessingError{Message: "Failed to create storage directory."}
	}

	src, err := fileHeader.Open()
	if err != nil {
		return db.Organization{}, &payloads.FileProcessingError{Message: "Failed to open uploaded file."}
	}
	defer src.Close()

	dst, err := os.Create(storagePath)
	if err != nil {
		return db.Organization{}, &payloads.FileProcessingError{Message: "Failed to create destination file."}
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		_ = os.Remove(storagePath) // Cleanup
		return db.Organization{}, &payloads.FileProcessingError{Message: "Failed to save file."}
	}

	// 4. Construct URL and update database
	webRelativePath := filepath.ToSlash(filepath.Join(relativePathDir, newFileName))
	fullURL := utils.ConstructURL("uploads", webRelativePath)

	updatedOrg, err := s.Queries.UpdateOrganizationLogoURL(ctx, db.UpdateOrganizationLogoURLParams{
		ID:       orgID,
		ImageUrl: &fullURL,
	})
	if err != nil {
		logger.Error().Err(err).Str("url", fullURL).Msg("Failed to update organization logo URL in database")
		_ = os.Remove(storagePath) // Cleanup
		return db.Organization{}, fmt.Errorf("failed to update organization logo in database: %w", ErrInternalServer)
	}

	return updatedOrg, nil
}

// AssignOrganizationManager assigns a user as a manager for an organization.
// If the user is already a member, their role is updated to 'manager'.
// If the user is not a member, they are added as a 'manager'.
func (s *OrganizationService) AssignOrganizationManager(ctx context.Context, organizationID uuid.UUID, targetUserID uuid.UUID, actorUserID uuid.UUID) error {
	// Verify targetUserID exists
	_, err := s.Queries.GetUserByID(ctx, targetUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("target_user_id", targetUserID.String()).Msg("Target user not found for assigning manager role")
			return ErrNotFound // Or a more specific ErrUserNotFound if defined
		}
		log.Ctx(ctx).Error().Err(err).Str("target_user_id", targetUserID.String()).Msg("Error verifying target user for assigning manager role")
		return fmt.Errorf("could not verify target user: %w", err)
	}

	// Verify organizationID exists
	_, err = s.Queries.GetOrganizationByID(ctx, organizationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("organization_id", organizationID.String()).Msg("Organization not found for assigning manager role")
			return ErrOrganizationNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("organization_id", organizationID.String()).Msg("Error verifying organization for assigning manager role")
		return fmt.Errorf("could not verify organization: %w", err)
	}

	// Check if targetUserID is already a member of organizationID
	membership, err := s.Queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
		UserID:         targetUserID,
		OrganizationID: organizationID,
	})

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// User is not a member, add them with 'manager' role
			_, addErr := s.Queries.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
				UserID:               targetUserID,
				OrganizationID:       organizationID,
				Role:                 "manager",
				IsActive:             true, // as per plan
				NotificationsEnabled: true, // as per plan
			})
			if addErr != nil {
				log.Ctx(ctx).Error().Err(addErr).Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("Error adding user to organization as manager")
				return fmt.Errorf("could not add user to organization as manager: %w", addErr)
			}
			log.Ctx(ctx).Info().Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("Successfully added user to organization as manager")
			return nil
		}
		// Other error fetching membership
		log.Ctx(ctx).Error().Err(err).Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("Error checking user organization membership")
		return fmt.Errorf("could not check user organization membership: %w", err)
	}

	// User is already a member, update their role to 'manager'
	if membership.Role == "manager" {
		log.Ctx(ctx).Info().Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("User is already a manager of this organization")
		return nil // Already a manager, no action needed
	}

	_, updateErr := s.Queries.UpdateUserOrganizationMembershipDetails(ctx, db.UpdateUserOrganizationMembershipDetailsParams{
		UserID:               targetUserID,
		OrganizationID:       organizationID,
		Role:                 "manager",
		IsActive:             membership.IsActive,             // Keep current active status
		NotificationsEnabled: membership.NotificationsEnabled, // Keep current notification status
	})
	if updateErr != nil {
		log.Ctx(ctx).Error().Err(updateErr).Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("Error updating user role to manager")
		return fmt.Errorf("could not update user role to manager: %w", updateErr)
	}

	log.Ctx(ctx).Info().Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("Successfully updated user role to manager")
	return nil
}

// RemoveOrganizationManager removes a user's 'manager' role from an organization,
// demoting them to a 'member'.
func (s *OrganizationService) RemoveOrganizationManager(ctx context.Context, organizationID uuid.UUID, targetUserID uuid.UUID, actorUserID uuid.UUID) error {
	// Verify targetUserID exists
	_, err := s.Queries.GetUserByID(ctx, targetUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("target_user_id", targetUserID.String()).Msg("Target user not found for removing manager role")
			return ErrNotFound // Or a more specific ErrUserNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("target_user_id", targetUserID.String()).Msg("Error verifying target user for removing manager role")
		return fmt.Errorf("could not verify target user: %w", err)
	}

	// Verify organizationID exists
	_, err = s.Queries.GetOrganizationByID(ctx, organizationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("organization_id", organizationID.String()).Msg("Organization not found for removing manager role")
			return ErrOrganizationNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("organization_id", organizationID.String()).Msg("Error verifying organization for removing manager role")
		return fmt.Errorf("could not verify organization: %w", err)
	}

	// Get current membership details to check role
	membership, err := s.Queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
		UserID:         targetUserID,
		OrganizationID: organizationID,
	})

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("User is not a member of this organization, cannot remove manager role.")
			return ErrMembershipNotFound // Or a more specific error like "user is not a member"
		}
		log.Ctx(ctx).Error().Err(err).Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("Error checking user organization membership for manager removal")
		return fmt.Errorf("could not check user organization membership: %w", err)
	}

	// Verify user is currently a 'manager'
	if membership.Role != "manager" {
		log.Ctx(ctx).Warn().Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Str("current_role", membership.Role).Msg("User is not a manager of this organization, cannot remove manager role.")
		return ErrForbidden // Or a more specific error like "user is not a manager"
	}

	// Update role from 'manager' to 'member'
	_, updateErr := s.Queries.UpdateUserOrganizationMembershipDetails(ctx, db.UpdateUserOrganizationMembershipDetailsParams{
		UserID:               targetUserID,
		OrganizationID:       organizationID,
		Role:                 "member",                        // Demote to member
		IsActive:             membership.IsActive,             // Keep current active status
		NotificationsEnabled: membership.NotificationsEnabled, // Keep current notification status
	})

	if updateErr != nil {
		log.Ctx(ctx).Error().Err(updateErr).Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("Error updating user role from manager to member")
		return fmt.Errorf("could not update user role from manager to member: %w", updateErr)
	}

	log.Ctx(ctx).Info().Str("target_user_id", targetUserID.String()).Str("organization_id", organizationID.String()).Msg("Successfully demoted user from manager to member")
	return nil
}
