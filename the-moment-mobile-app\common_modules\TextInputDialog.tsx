import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, ActivityIndicator, useWindowDimensions, KeyboardAvoidingView, Platform, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { TextInput, HelperText } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';

interface TextInputDialogProps {
    visible: boolean;
    title: string;
    subtitle?: string;
    placeholder: string;
    value: string;
    onChangeText: (text: string) => void;
    confirmText: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    confirmLoading?: boolean;
    error?: string;
    minLength?: number;
    maxLength?: number;
}

export const TextInputDialog: React.FC<TextInputDialogProps> = ({
    visible,
    title,
    subtitle,
    placeholder,
    value,
    onChangeText,
    confirmText,
    cancelText,
    onConfirm,
    onCancel,
    confirmLoading = false,
    error,
    minLength = 0,
    maxLength
}) => {
    const { t } = useTranslation();
    const theme = appStyleStore(state => state.theme);
    const { width: screenWidth } = useWindowDimensions();
    const [touched, setTouched] = useState(false);

    const handleChangeText = (text: string) => {
        setTouched(true);
        onChangeText(text);
    };

    const isValidLength = !touched || (value.length >= minLength && (!maxLength || value.length <= maxLength));
    const showLengthError = touched && !isValidLength;

    const styles = StyleSheet.create({
        overlay: {
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            justifyContent: 'center',
            alignItems: 'center',
        },
        container: {
            width: screenWidth * 0.85,
            backgroundColor: theme.system.background,
            borderRadius: 8,
            padding: 20,
        },
        title: {
            fontSize: 18,
            fontWeight: '600',
            color: theme.colors.onSurface,
            marginBottom: 8,
        },
        subtitle: {
            fontSize: 14,
            color: theme.colors.onSurfaceVariant,
            marginBottom: 16,
            lineHeight: 20,
        },
        input: {
            marginBottom: 4,
            backgroundColor: theme.system.background,
        },
        errorText: {
            color: theme.system.error,
            fontSize: 12,
            marginBottom: 16, 
        },
        helperText: {
            fontSize: 12,
            color: showLengthError ? theme.system.error : theme.colors.onSurfaceVariant,
            marginBottom: 16,
        },
        buttonContainer: {
            flexDirection: 'row',
            justifyContent: 'flex-end',
            gap: 12,
            marginTop: 16,
        },
        button: {
            paddingVertical: 8,
            paddingHorizontal: 16,
            borderRadius: 6,
            minWidth: 80,
            alignItems: 'center',
            justifyContent: 'center',
            height: 40,
        },
        cancelButton: {
            backgroundColor: '#F5F5F5',
        },
        confirmButton: {
            backgroundColor: theme.colors.primary,
            opacity: (!isValidLength || confirmLoading) ? 0.5 : 1,
        },
        buttonText: {
            fontSize: 14,
            fontWeight: '500',
        },
        cancelText: {
            color: theme.colors.onSurfaceVariant,
        },
        confirmText: {
            color: theme.colors.onPrimary,
        },
    });

    return (
        <Modal
            visible={visible}
            transparent
            animationType="fade"
            onRequestClose={onCancel}
        >
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <KeyboardAvoidingView
                    style={styles.overlay}
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
                >
                    <TouchableWithoutFeedback onPress={() => {}}>
                        <View style={styles.container}>
                            <Text style={styles.title}>{title}</Text>
                            {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
                            
                            <TextInput
                                value={value}
                                onChangeText={handleChangeText}
                                placeholder={placeholder}
                                style={styles.input}
                                mode="outlined"
                                autoCapitalize="none"
                                disabled={confirmLoading}
                                error={!!error || showLengthError}
                                maxLength={maxLength}
                            />
                            
                            {error ? (
                                <Text style={styles.errorText}>{error}</Text>
                            ) : (
                                <Text style={styles.helperText}>
                                    {touched ? `${value.length}/${maxLength || minLength} ${t('common.characters')}` : ''}
                                </Text>
                            )}
                            
                            <View style={styles.buttonContainer}>
                                {onCancel && cancelText && (
                                    <TouchableOpacity
                                        style={[styles.button, styles.cancelButton]}
                                        onPress={onCancel}
                                        disabled={confirmLoading}
                                    >
                                        <Text style={[styles.buttonText, styles.cancelText]}>{cancelText}</Text>
                                    </TouchableOpacity>
                                )}
                                
                                <TouchableOpacity
                                    style={[styles.button, styles.confirmButton]}
                                    onPress={onConfirm}
                                    disabled={!isValidLength || confirmLoading}
                                >
                                    {confirmLoading ? (
                                        <ActivityIndicator size="small" color={theme.colors.onPrimary} />
                                    ) : (
                                        <Text style={[styles.buttonText, styles.confirmText]}>{confirmText}</Text>
                                    )}
                                </TouchableOpacity>
                            </View>
                        </View>
                    </TouchableWithoutFeedback>
                </KeyboardAvoidingView>
            </TouchableWithoutFeedback>
        </Modal>
    );
};
