-- Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE, -- Assuming organization names should be unique for now
    description TEXT,
    owner_user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT, -- Prevent deleting user if they own orgs; handle in app logic
    is_default_org BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add a partial unique index to ensure only one default organization can exist
CREATE UNIQUE INDEX IF NOT EXISTS unique_default_organization_idx ON organizations (is_default_org) WHERE is_default_org = TRUE;

-- Create user_organization_memberships table (join table)
CREATE TABLE IF NOT EXISTS user_organization_memberships (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL DEFAULT 'member', -- e.g., 'member', 'admin', 'owner' (owner role here might be redundant if owner_user_id in orgs table is the source of truth for ownership)
    joined_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    notifications_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    PRIMARY KEY (user_id, organization_id)
);

-- Apply the updated_at trigger to the new organizations table
-- Ensure the update_updated_at_column function exists from the first migration
CREATE OR REPLACE TRIGGER update_organizations_updated_at
BEFORE UPDATE ON organizations
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Optional: Add indexes for frequently queried columns in user_organization_memberships
CREATE INDEX IF NOT EXISTS idx_user_organization_memberships_user_id ON user_organization_memberships(user_id);
CREATE INDEX IF NOT EXISTS idx_user_organization_memberships_organization_id ON user_organization_memberships(organization_id);

-- Seed a Default Organization (if one doesn't exist)
-- This requires a valid user ID to be the owner. This is problematic for a generic migration.
-- It's better to handle the creation of the default organization in application logic on startup
-- or via a separate seeding script after a user (e.g., system user or first admin) exists.
-- For now, we will not seed it directly in the migration to avoid dependency on specific user data.
-- The application will need to ensure this default organization is created. 