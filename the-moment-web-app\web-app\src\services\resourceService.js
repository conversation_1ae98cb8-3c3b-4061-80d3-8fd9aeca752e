import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

// Helper to map JS-style camelCase params to API-style snake_case for query params
const mapToApiQueryParams = (params, mapping) => {
  const apiParams = {};
  if (params) {
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        const mappedKey = mapping[key] || key;
        apiParams[mappedKey] = params[key];
      }
    }
  }
  return apiParams;
};

const listPublicResourcesParamsMapping = {
  organizationId: 'organization_id',
  // visibility, limit, offset can be used directly if names match
  searchTerm: 'search_term',
};

export const resourceService = {
  // List public resources (paginated, with optional filters)
  listPublicResources: async (clientParams = {}) => {
    // clientParams: { organizationId?, visibility?, limit?, offset?, searchTerm? }
    // API expects query: { organization_id?, visibility?, limit?, offset?, search_term? }
    const apiParams = mapToApiQueryParams(clientParams, listPublicResourcesParamsMapping);
    try {
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.RESOURCES.LIST_PUBLIC_RESOURCES, { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      let resources = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.resources)) {
        resources = responseData.resources;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || resources.length;
      } else if (Array.isArray(responseData)) {
        resources = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || resources.length;
      } else if (responseData && typeof responseData === 'object') {
        // If API returns an object with items or data field
        resources = responseData.items || responseData.data || [];
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || resources.length;
      }
      
      return { resources, total };
    } catch (error) {
      console.error('Error fetching public resources:', error);
      throw error;
    }
  },

  // Download a specific resource file from a public or organization resource
  downloadResourceFile: async (organizationId, resourceIdOrSlug, fileIdOrName) => {
    try {
      const response = await api.get(API_ENDPOINTS.RESOURCES.DOWNLOAD_RESOURCE_FILE(organizationId, resourceIdOrSlug, fileIdOrName), {
        responseType: 'blob', // Important for file downloads
      });
      return response; 
    } catch (error) {
      console.error(`Error downloading file ${fileIdOrName} for resource ${resourceIdOrSlug}:`, error);
      throw error;
    }
  },
}; 