-- Step 1: Rename the table first, as it's simpler and has fewer dependencies.
ALTER TABLE verification_home_return_permits RENAME TO verification_mainland_travel_permits;

-- Step 2: Handle ENUM rename.
-- The direct RENAME VALUE is cleaner if supported and no data loss risk.
-- If this fails during application, we'll need the more complex method outlined in comments.
ALTER TYPE verification_type_enum RENAME VALUE 'home_return_permit' TO 'mainland_travel_permit'; 