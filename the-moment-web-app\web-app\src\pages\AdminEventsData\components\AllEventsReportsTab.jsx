import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, Row, Col, Statistic, DatePicker, Empty, Alert, Spin, message } from 'antd';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import EventTrendChart from '../../../components/EventTrendChart';
import { eventService } from '../../../services/eventService';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../../contexts/OrganizationContext';

const { RangePicker } = DatePicker;

const ReportsTabContent = ({ isActive }) => {
    const { t, i18n } = useTranslation();
    const { currentOrganization } = useOrganization();
    
    const [reportData, setReportData] = useState(null);
    const [reportLoading, setReportLoading] = useState(false);
    const [reportError, setReportError] = useState(null);
    
    const initialDateRange = useMemo(() => [
        dayjs().subtract(30, 'days'),
        dayjs()
    ], []);
    
    const [dateRange, setDateRange] = useState(initialDateRange);

    const handleDateChange = useCallback((dates) => {
        setDateRange(dates);
    }, []);

    useEffect(() => {
        const fetchReportData = async () => {
            if (!isActive) return;

            setReportLoading(true);
            setReportError(null);
            
            let apiParams = {};
            if (dateRange && dateRange[0] && dateRange[1]) {
                apiParams.start_date = dateRange[0].startOf('day').toISOString();
                apiParams.end_date = dateRange[1].endOf('day').toISOString();
            }

            try {
                let response;
                if (currentOrganization && currentOrganization.id !== ALL_ORGANIZATION_ID) {
                    response = await eventService.listOrgEvents(currentOrganization.id, apiParams);
                } else {
                    response = await eventService.listPublicEvents(apiParams);
                }
                console.log('all events stats response', response);
                setReportData(response);
            } catch (err) {
                console.error('Error fetching event report data:', err);
                const errorMessage = err.response?.data?.message || err.message || t('adminEvents.report.errors.fetchStatsFailed');
                setReportError(errorMessage);
                message.error(errorMessage);
            }
            setReportLoading(false);
        };

        fetchReportData();
    }, [isActive, currentOrganization, dateRange, t, handleDateChange]);

    const { totalEvents, totalParticipants, averageParticipants } = useMemo(() => {
        const totalEventsCount = reportData?.total ?? (reportData?.events?.length || 0);
        const totalParticipantsCount = (reportData?.events || [])
            .reduce((sum, event) => sum + (event.registered_count || 0), 0); 
        
        const avgParticipants = totalEventsCount > 0 ? (totalParticipantsCount / totalEventsCount).toFixed(2) : 0;
        
        const categoryStats = reportData?.events?.map(event => ({
            name: event.title,
            value: 1
        })) || [];

        return {
            totalEvents: totalEventsCount,
            totalParticipants: totalParticipantsCount,
            averageParticipants: avgParticipants,
        };
    }, [reportData]);

    const chartData = useMemo(() => {
        if (!reportData?.events) return [];
        const dailyCounts = {};
        const volunteerCounts = {};
        
        reportData.events.forEach(event => {
            if (event.start_time) {
                const date = dayjs(event.start_time).format('YYYY-MM-DD');
                // Calculate participants
                dailyCounts[date] = (dailyCounts[date] || 0) + (event.registered_count || 0);
                // Calculate volunteers
                volunteerCounts[date] = (volunteerCounts[date] || 0) + (event.volunteer_count || 0);
            }
        });
        // Combine all dates from both counts
        const allDates = [...new Set([...Object.keys(dailyCounts), ...Object.keys(volunteerCounts)])];
        
        return allDates.map(date => ({
            date,
            Participants: dailyCounts[date] || 0,
            Volunteers: volunteerCounts[date] || 0
        }));
    }, [reportData]);
    
    const topCategories = useMemo(() => {
        if (!reportData?.events) return [];
        const counts = {};
        const tagNames = {}; // Store tag names by id
        
        reportData.events.forEach(event => {
            if (!event.tags || event.tags.length === 0) return;
            event.tags.forEach(tag => {
                // Count by tag id
                counts[tag.id] = (counts[tag.id] || 0) + 1;
                
                // Store the appropriate name based on current language
                if (!tagNames[tag.id]) {
                    let tagName;
                    switch(i18n.language) {
                        case 'zh-hk':
                        case 'zh-HK':
                            tagName = tag.name_zh_hk || tag.name_en;
                            break;
                        case 'zh-cn':
                        case 'zh-CN':
                        case 'zh':
                            tagName = tag.name_zh_cn || tag.name_en;
                            break;
                        default:
                            tagName = tag.name_en;
                    }
                    tagNames[tag.id] = tagName;
                }
            });
        });
        
        return Object.entries(counts).map(([id, value]) => ({ 
            name: tagNames[id], 
            value 
        })).sort((a,b) => b.value - a.value).slice(0, 10);
    }, [reportData, i18n.language]);

    const topEvents = useMemo(() => {
        if (!reportData?.events) return [];
        return reportData.events.map(event => ({
            title: event.title,
            participants: event.registered_count || 0,
            id: event.id
        })).sort((a,b) => b.participants - a.participants).slice(0, 10);
    }, [reportData]);

    const statisticsCards = useMemo(() => (
        <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={8}>
                <Card>
                    <Statistic
                        title={t('adminEvents.report.statistics.totalEvents')}
                        value={totalEvents}
                    />
                </Card>
            </Col>
            <Col xs={24} sm={24} md={8}>
                <Card>
                    <Statistic
                        title={t('adminEvents.report.statistics.totalParticipants')}
                        value={totalParticipants}
                    />
                </Card>
            </Col>
            <Col xs={24} sm={24} md={8}>
                <Card>
                    <Statistic
                        title={t('adminEvents.report.statistics.averageParticipants')}
                        value={averageParticipants}
                    />
                </Card>
            </Col>
        </Row>
    ), [totalEvents, totalParticipants, averageParticipants, t]);

    const chartSection = useMemo(() => (
        <Row gutter={[16, 16]} style={{ marginTop: '20px' }}>
            <Col span={24}>
                {(chartData.length === 0 && !reportLoading) ? (
                    <Card>
                        <Empty 
                            description={t('adminEvents.report.empty.noTrendData')} 
                            image={Empty.PRESENTED_IMAGE_SIMPLE} 
                        />
                    </Card>
                ) : (
                    <EventTrendChart
                        chartData={chartData}
                        topCategories={topCategories}
                        topEvents={topEvents}
                    />
                )}
            </Col>
        </Row>
    ), [chartData, topCategories, topEvents, t, reportLoading]);

    const datePresets = useMemo(() => [
        { label: t('eventFilter.dateFilter.allDates'), value: null },
        { label: t('eventFilter.dateFilter.thisWeek'), value: [dayjs().startOf('week'), dayjs().endOf('week')] },
        { label: t('eventFilter.dateFilter.nextWeek'), value: [dayjs().add(1, 'week').startOf('week'), dayjs().add(1, 'week').endOf('week')] },
        { label: t('eventFilter.dateFilter.thisMonth'), value: [dayjs().startOf('month'), dayjs().endOf('month')] },
        { label: t('eventFilter.dateFilter.nextMonth'), value: [dayjs().add(1, 'month').startOf('month'), dayjs().add(1, 'month').endOf('month')] },
    ], [t]);

    return (
        <Spin spinning={reportLoading} size="large" style={{ minHeight: '600px', width: '100%', display: 'block' }}>
            <div className="reports-tab-content">

                <div className="mb-6">
                    <RangePicker
                        value={dateRange}
                        size="large"
                        onChange={handleDateChange}
                        format="YYYY-MM-DD"
                        allowClear={true}
                        allowEmpty={true}
                        presets={datePresets}
                        disabled={reportLoading}
                    />
                </div>
                
                {(!reportLoading || reportData) && !reportError && (
                    <>
                        {statisticsCards}
                        {chartSection}
                    </>
                )}
            </div>
        </Spin>
    );
};

export default React.memo(ReportsTabContent); 