package services

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"

	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

var (
	ErrAlreadyApprovedApplication = errors.New("user already has an approved volunteer application for this organization")
	ErrPendingApplicationExists   = errors.New("user already has a pending volunteer application for this organization")
	ErrApplicationNotFound        = errors.New("volunteer application not found")
	ErrNotMemberOfOrg             = errors.New("user is not a member of this organization")
	ErrApplicationNotWithdrawable = errors.New("application status does not allow withdrawal")
)

type VolunteerService struct {
	Store  db.Querier           // Use Querier interface
	OrgSvc *OrganizationService // Changed from UserSvc to OrgSvc for membership check
	// NotificationSvc *NotificationService // For sending notifications upon approval/rejection (TODO)
}

func NewVolunteerService(store db.Querier, orgSvc *OrganizationService) *VolunteerService {
	return &VolunteerService{
		Store:  store,
		OrgSvc: orgSvc,
	}
}

// ApplyForVolunteerQualification allows a user to apply for volunteer qualification in an organization.
func (s *VolunteerService) ApplyForVolunteerQualification(ctx context.Context, userID uuid.UUID, orgID uuid.UUID, req payloads.ApplyVolunteerRequest) (db.UserVolunteerApplication, error) {
	// 1. Check if user is a member of the organization
	isMember, err := s.OrgSvc.IsUserMemberOfOrganization(ctx, userID, orgID) // Using OrgSvc
	if err != nil {
		log.Error().Err(err).Msg("Error checking organization membership")
		return db.UserVolunteerApplication{}, err
	}
	if !isMember {
		return db.UserVolunteerApplication{}, ErrNotMemberOfOrg
	}

	// 2. Check if user has an existing application for this organization
	existingApp, err := s.Store.GetUserVolunteerApplicationForOrganization(ctx, db.GetUserVolunteerApplicationForOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
	})

	if err == nil {
		// If an application exists, check its status
		if existingApp.Status == db.ApplicationStatusEnumApproved {
			return db.UserVolunteerApplication{}, ErrAlreadyApprovedApplication
		}
		if existingApp.Status == db.ApplicationStatusEnumPending {
			return db.UserVolunteerApplication{}, ErrPendingApplicationExists
		}
		// If it's rejected or another status, allow re-application (or define specific logic if needed)
	} else if !errors.Is(err, sql.ErrNoRows) {
		log.Error().Err(err).Msg("Error checking existing volunteer application")
		return db.UserVolunteerApplication{}, err
	}
	// If sql.ErrNoRows, proceed to create a new application.

	// 3. Create the application
	var motivationPtr *string
	if req.Motivation != "" {
		motivationPtr = &req.Motivation
	}

	params := db.CreateVolunteerApplicationParams{
		UserID:         userID,
		OrganizationID: orgID,
		Motivation:     motivationPtr,
	}

	application, err := s.Store.CreateVolunteerApplication(ctx, params)
	if err != nil {
		log.Error().Err(err).Msg("Error creating volunteer application")
		return db.UserVolunteerApplication{}, err
	}

	return application, nil
}

// Helper for mapping ReviewedByUserID (uuid.UUID from DB model) to *string for payload
func reviewedByUserIDToStringPtr(uid *uuid.UUID) *string {
	if uid == nil || *uid == uuid.Nil { // Treat nil or uuid.Nil as database NULL for payload purposes
		return nil
	}
	s := uid.String()
	return &s
}

// Helper to convert *string (from SQLC Row structs) to sql.NullString for mapDBApplicationToResponse
func stringPtrToSQLNullString(s *string) sql.NullString {
	if s == nil {
		return sql.NullString{}
	}
	return sql.NullString{String: *s, Valid: true}
}

// Helper to convert non-pointer string (from SQLC Row structs) to sql.NullString
func stringToNormalSQLNullString(s string) sql.NullString { // Renamed to avoid conflict if another 'stringToSQLNullString' existed
	if s == "" {
		return sql.NullString{}
	}
	return sql.NullString{String: s, Valid: true}
}

func (s *VolunteerService) mapDBApplicationToResponse(app db.UserVolunteerApplication, orgName sql.NullString, applicantName sql.NullString, applicantEmail sql.NullString, applicantPhone sql.NullString, reviewerDisplayName sql.NullString) payloads.VolunteerApplicationResponse {
	resp := payloads.VolunteerApplicationResponse{
		ID:               app.ID.String(),
		UserID:           app.UserID.String(),
		OrganizationID:   app.OrganizationID.String(),
		ApplicationDate:  app.ApplicationDate,
		Status:           string(app.Status),
		Motivation:       app.Motivation,
		ReviewedByUserID: reviewedByUserIDToStringPtr(app.ReviewedByUserID),
		ReviewDate:       app.ReviewDate,
		AdminNotes:       app.AdminNotes,
		CreatedAt:        app.CreatedAt,
		UpdatedAt:        app.UpdatedAt,
	}
	if orgName.Valid {
		resp.OrganizationName = orgName.String
	}
	if applicantName.Valid {
		resp.ApplicantDisplayName = &applicantName.String
	}
	if applicantEmail.Valid {
		resp.ApplicantEmail = &applicantEmail.String
	}
	if applicantPhone.Valid {
		resp.ApplicantPhone = &applicantPhone.String
	}
	if reviewerDisplayName.Valid {
		resp.ReviewerDisplayName = &reviewerDisplayName.String
	}
	return resp
}

// ListUserVolunteerApplications retrieves all volunteer applications for a given user.
func (s *VolunteerService) ListUserVolunteerApplications(ctx context.Context, userID uuid.UUID, status *string) ([]payloads.VolunteerApplicationResponse, error) {
	var statusFilter db.NullApplicationStatusEnum
	if status != nil && *status != "" {
		validatedStatus := db.ApplicationStatusEnum(*status)
		// Basic validation for status
		switch validatedStatus {
		case db.ApplicationStatusEnumPending, db.ApplicationStatusEnumApproved, db.ApplicationStatusEnumRejected, db.ApplicationStatusEnumWithdrawn:
			statusFilter = db.NullApplicationStatusEnum{
				ApplicationStatusEnum: validatedStatus,
				Valid:                 true,
			}
		default:
			log.Warn().Str("status", *status).Msg("Invalid status filter provided for ListUserVolunteerApplications")
		}
	}

	params := db.ListUserVolunteerApplicationsParams{
		UserID: userID,
		Status: statusFilter,
	}

	dbApps, err := s.Store.ListUserVolunteerApplications(ctx, params)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return []payloads.VolunteerApplicationResponse{}, nil
		}
		log.Error().Err(err).Msg("Error listing user volunteer applications")
		return nil, err
	}

	var appResponses []payloads.VolunteerApplicationResponse
	for _, app := range dbApps {
		org, orgErr := s.Store.GetOrganizationByID(ctx, app.OrganizationID)
		orgName := sql.NullString{}
		if orgErr == nil {
			orgName.String = org.Name
			orgName.Valid = true
		} else if !errors.Is(orgErr, sql.ErrNoRows) {
			log.Warn().Err(orgErr).Str("organizationID", app.OrganizationID.String()).Msg("Failed to fetch organization name for application list")
		}
		appResponses = append(appResponses, s.mapDBApplicationToResponse(app, orgName, sql.NullString{}, sql.NullString{}, sql.NullString{}, sql.NullString{}))
	}
	return appResponses, nil
}

// GetUserVolunteerApplicationDetails retrieves details for a specific application owned by the user.
func (s *VolunteerService) GetUserVolunteerApplicationDetails(ctx context.Context, userID uuid.UUID, appID uuid.UUID) (payloads.VolunteerApplicationResponse, error) {
	log.Ctx(ctx).Debug().Str("user_id_for_query", userID.String()).Str("app_id_for_query", appID.String()).Msg("Attempting to fetch volunteer application by ID and UserID")
	dbApp, err := s.Store.GetUserVolunteerApplicationByID(ctx, db.GetUserVolunteerApplicationByIDParams{
		ID:     appID,
		UserID: userID,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return payloads.VolunteerApplicationResponse{}, ErrApplicationNotFound
		}
		log.Error().Err(err).Msg("Error getting user volunteer application by ID")
		return payloads.VolunteerApplicationResponse{}, err
	}

	org, orgErr := s.Store.GetOrganizationByID(ctx, dbApp.OrganizationID)
	orgName := sql.NullString{}
	if orgErr == nil {
		orgName.String = org.Name
		orgName.Valid = true
	} else if !errors.Is(orgErr, sql.ErrNoRows) {
		log.Warn().Err(orgErr).Str("organizationID", dbApp.OrganizationID.String()).Msg("Failed to fetch organization name for application details")
	}

	return s.mapDBApplicationToResponse(dbApp, orgName, sql.NullString{}, sql.NullString{}, sql.NullString{}, sql.NullString{}), nil
}

// ListUserVolunteerQualifications retrieves all volunteer qualifications for a given user.
// This function is deprecated. "Qualification" is now implicitly defined by an "approved" UserVolunteerApplication.
// To get "qualified" organizations, one should list applications and filter by status.
func (s *VolunteerService) ListUserVolunteerQualifications(ctx context.Context, userID uuid.UUID) ([]payloads.VolunteerApplicationResponse, error) {
	approvedStatus := string(db.ApplicationStatusEnumApproved)
	// Directly call the updated ListUserVolunteerApplications with the "approved" status filter.
	// This is now much more efficient as it filters at the database level.
	return s.ListUserVolunteerApplications(ctx, userID, &approvedStatus)
}

// GetVolunteerQualificationStatusForOrg checks if a user has an approved volunteer application for a specific organization.
// It returns the approved application details if one exists.
// If no application or a non-approved application exists, it returns ErrApplicationNotFound.
func (s *VolunteerService) GetVolunteerQualificationStatusForOrg(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) (payloads.VolunteerApplicationResponse, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Str("org_id", orgID.String()).Logger()
	logger.Debug().Msg("Checking volunteer application status for organization (qualification status)")

	app, err := s.Store.GetUserVolunteerApplicationForOrganization(ctx, db.GetUserVolunteerApplicationForOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
	})

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			logger.Info().Msg("No volunteer application found for this user/org.")
			return payloads.VolunteerApplicationResponse{}, ErrApplicationNotFound
		}
		logger.Error().Err(err).Msg("Unexpected DB error checking for volunteer application")
		return payloads.VolunteerApplicationResponse{}, err
	}

	if app.Status == db.ApplicationStatusEnumApproved {
		logger.Info().Interface("application_found", app).Msg("User has an approved volunteer application.")
		org, orgErr := s.Store.GetOrganizationByID(ctx, orgID)
		orgName := sql.NullString{}
		if orgErr != nil {
			logger.Error().Err(orgErr).Msg("Failed to get organization details for approved application")
			// Still return the application details, but log the org name fetch error
		} else {
			orgName.String = org.Name
			orgName.Valid = true
		}
		// TODO: Fetch applicant name, email, phone for the response if needed, similar to other Get methods.
		// For now, returning with minimal details directly from 'app' and 'orgName'.
		// Consider if mapDBApplicationToResponse needs more args or if a simpler mapping is okay here.
		return s.mapDBApplicationToResponse(app, orgName, sql.NullString{}, sql.NullString{}, sql.NullString{}, sql.NullString{}), nil
	}

	logger.Info().Str("application_status", string(app.Status)).Msg("Found application, but status is not approved. User is not qualified.")
	return payloads.VolunteerApplicationResponse{}, ErrApplicationNotFound // Or a more specific error like ErrNotQualified / ErrApplicationNotApproved
}

// ListOrgVolunteerApplications retrieves all volunteer applications for a given organization with optional status filtering.
func (s *VolunteerService) ListOrgVolunteerApplications(ctx context.Context, orgID uuid.UUID, status *string, limit, offset int32) ([]payloads.VolunteerApplicationResponse, int64, error) {
	logger := log.Ctx(ctx).With().Str("org_id", orgID.String()).Logger()
	if status != nil {
		logger = logger.With().Str("status", *status).Logger()
	}
	logger = logger.With().Int32("limit", limit).Int32("offset", offset).Logger()
	logger.Debug().Msg("Listing organization volunteer applications")

	var queryStatus *string
	if status != nil && *status != "" {
		queryStatus = status
	} else {
		queryStatus = nil
	}

	countParams := db.CountOrgVolunteerApplicationsWithFiltersParams{
		OrganizationID: orgID,
		Status:         queryStatus,
	}
	totalCount, err := s.Store.CountOrgVolunteerApplicationsWithFilters(ctx, countParams)
	if err != nil {
		logger.Error().Err(err).Msg("Error counting organization volunteer applications")
		return nil, 0, err
	}

	if totalCount == 0 {
		return []payloads.VolunteerApplicationResponse{}, 0, nil
	}

	listParams := db.ListOrgVolunteerApplicationsWithFiltersParams{
		OrganizationID: orgID,
		Status:         queryStatus,
		LimitVal:       limit,
		OffsetVal:      offset,
	}
	dbApps, err := s.Store.ListOrgVolunteerApplicationsWithFilters(ctx, listParams)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) { // Should be handled by totalCount == 0 check, but good practice
			return []payloads.VolunteerApplicationResponse{}, 0, nil
		}
		logger.Error().Err(err).Msg("Error listing organization volunteer applications")
		return nil, 0, err
	}

	var appResponses []payloads.VolunteerApplicationResponse
	org, orgErr := s.Store.GetOrganizationByID(ctx, orgID)
	orgName := sql.NullString{}
	if orgErr == nil {
		orgName.String = org.Name
		orgName.Valid = true
	} else if !errors.Is(orgErr, sql.ErrNoRows) {
		logger.Warn().Err(orgErr).Msg("Failed to fetch organization name for application list")
	}

	for _, appRow := range dbApps {
		app := db.UserVolunteerApplication{
			ID:               appRow.ID,
			UserID:           appRow.UserID,
			OrganizationID:   appRow.OrganizationID,
			ApplicationDate:  appRow.ApplicationDate,
			Motivation:       appRow.Motivation,
			ReviewedByUserID: appRow.ReviewedByUserID,
			ReviewDate:       appRow.ReviewDate,
			AdminNotes:       appRow.AdminNotes,
			CreatedAt:        appRow.CreatedAt,
			UpdatedAt:        appRow.UpdatedAt,
			Status:           appRow.Status,
		}
		appResponses = append(appResponses, s.mapDBApplicationToResponse(
			app,
			orgName,
			stringToNormalSQLNullString(appRow.ApplicantDisplayName),
			stringPtrToSQLNullString(appRow.ApplicantEmail),
			stringPtrToSQLNullString(appRow.ApplicantPhoneNumber),
			stringPtrToSQLNullString(appRow.ReviewerDisplayName),
		))
	}
	return appResponses, totalCount, nil
}

// ---- Admin Methods ----

// ListPendingVolunteerApplicationsForOrganization retrieves pending applications for an organization (Admin).
func (s *VolunteerService) ListPendingVolunteerApplicationsForOrganization(ctx context.Context, orgID uuid.UUID) ([]payloads.VolunteerApplicationResponse, error) {
	dbApps, err := s.Store.ListPendingVolunteerApplicationsForOrganization(ctx, orgID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return []payloads.VolunteerApplicationResponse{}, nil
		}
		log.Error().Err(err).Msg("Error listing pending volunteer applications for organization")
		return nil, err
	}

	var appResponses []payloads.VolunteerApplicationResponse
	for _, row := range dbApps {
		org, orgErr := s.Store.GetOrganizationByID(ctx, row.OrganizationID)
		orgName := sql.NullString{}
		if orgErr == nil {
			orgName.String = org.Name
			orgName.Valid = true
		} else if !errors.Is(orgErr, sql.ErrNoRows) {
			log.Warn().Err(orgErr).Str("organizationID", row.OrganizationID.String()).Msg("Failed to fetch organization name for pending list")
		}

		appFromRow := db.UserVolunteerApplication{
			ID:               row.ID,
			UserID:           row.UserID,
			OrganizationID:   row.OrganizationID,
			ApplicationDate:  row.ApplicationDate,
			Status:           row.Status,
			Motivation:       row.Motivation,       // *string
			ReviewedByUserID: row.ReviewedByUserID, // uuid.UUID
			ReviewDate:       row.ReviewDate,       // *time.Time
			AdminNotes:       row.AdminNotes,       // *string
			CreatedAt:        row.CreatedAt,
			UpdatedAt:        row.UpdatedAt,
		}

		appResponses = append(appResponses, s.mapDBApplicationToResponse(
			appFromRow,
			orgName,
			stringToNormalSQLNullString(row.ApplicantDisplayName),
			stringPtrToSQLNullString(row.ApplicantEmail),
			stringPtrToSQLNullString(row.ApplicantPhoneNumber),
			sql.NullString{},
		))
	}
	return appResponses, nil
}

// EnhancedVolunteerApplicationDetails includes verification documents for admin review.
type EnhancedVolunteerApplicationDetails struct {
	payloads.VolunteerApplicationResponse
	VerificationRequests []payloads.UserVerificationRequestResponse `json:"verification_requests"`
}

// GetVolunteerApplicationDetailsForAdmin retrieves application details for admin review, including user verification documents.
func (s *VolunteerService) GetVolunteerApplicationDetailsForAdmin(ctx context.Context, orgID uuid.UUID, appID uuid.UUID, verificationSvc *UserVerificationService) (EnhancedVolunteerApplicationDetails, error) {
	dbApp, err := s.Store.GetVolunteerApplicationDetailsForAdmin(ctx, db.GetVolunteerApplicationDetailsForAdminParams{
		ID:             appID,
		OrganizationID: orgID,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return EnhancedVolunteerApplicationDetails{}, ErrApplicationNotFound
		}
		log.Error().Err(err).Msg("Error getting volunteer application details for admin")
		return EnhancedVolunteerApplicationDetails{}, err
	}

	org, orgErr := s.Store.GetOrganizationByID(ctx, dbApp.OrganizationID)
	orgName := sql.NullString{}
	if orgErr == nil {
		orgName.String = org.Name
		orgName.Valid = true
	} else if !errors.Is(orgErr, sql.ErrNoRows) {
		log.Warn().Err(orgErr).Str("organizationID", dbApp.OrganizationID.String()).Msg("Failed to fetch organization name for admin application details")
	}

	// Construct db.UserVolunteerApplication from dbApp (GetVolunteerApplicationDetailsForAdminRow)
	appFromRow := db.UserVolunteerApplication{
		ID:               dbApp.ID,
		UserID:           dbApp.UserID,
		OrganizationID:   dbApp.OrganizationID,
		ApplicationDate:  dbApp.ApplicationDate,
		Status:           dbApp.Status,
		Motivation:       dbApp.Motivation,       // *string
		ReviewedByUserID: dbApp.ReviewedByUserID, // uuid.UUID
		ReviewDate:       dbApp.ReviewDate,       // *time.Time
		AdminNotes:       dbApp.AdminNotes,       // *string
		CreatedAt:        dbApp.CreatedAt,
		UpdatedAt:        dbApp.UpdatedAt,
	}

	mappedApp := s.mapDBApplicationToResponse(
		appFromRow, // Use the constructed appFromRow
		orgName,
		stringToNormalSQLNullString(dbApp.ApplicantDisplayName),
		stringPtrToSQLNullString(dbApp.ApplicantEmail),
		stringPtrToSQLNullString(dbApp.ApplicantPhoneNumber),
		stringPtrToSQLNullString(dbApp.ReviewerDisplayName),
	)

	// Fetch user's *approved* verification records for admin review.
	statusFilter := "approved"
	verifications, vErr := verificationSvc.ListUserVerifications(ctx, dbApp.UserID, &payloads.ListUserVerificationsParams{Status: &statusFilter})
	if vErr != nil {
		log.Error().Err(vErr).Str("applicantUserID", dbApp.UserID.String()).Msg("Failed to fetch user verification records for admin review of volunteer application")
		// Do not fail the entire request; verifications are supplementary
	}

	return EnhancedVolunteerApplicationDetails{
		VolunteerApplicationResponse: mappedApp,
		VerificationRequests:         verifications, // This will be nil if vErr != nil or empty
	}, nil
}

// ReviewVolunteerApplication allows an admin to approve or reject a volunteer application.
func (s *VolunteerService) ReviewVolunteerApplication(ctx context.Context, orgID uuid.UUID, appID uuid.UUID, reviewerID uuid.UUID, req payloads.AdminReviewVolunteerApplicationRequest) (payloads.VolunteerApplicationResponse, error) {
	// 1. Check if the application exists and is pending for the given organization
	appToReview, err := s.Store.GetVolunteerApplicationDetailsForAdmin(ctx, db.GetVolunteerApplicationDetailsForAdminParams{
		ID:             appID,
		OrganizationID: orgID,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return payloads.VolunteerApplicationResponse{}, ErrApplicationNotFound
		}
		log.Error().Err(err).Msg("Error fetching application for review")
		return payloads.VolunteerApplicationResponse{}, err
	}

	if appToReview.Status != db.ApplicationStatusEnumPending {
		// return payloads.VolunteerApplicationResponse{}, errors.New("application is not pending review")
		log.Warn().Str("applicationID", appID.String()).Msg("Application is not pending review")
	}

	// 2. Update the application status
	var newStatus db.ApplicationStatusEnum
	dbStatus := db.ApplicationStatusEnum(req.Status)
	if !dbStatus.Valid() {
		return payloads.VolunteerApplicationResponse{}, fmt.Errorf("invalid review status: %s. Must be one of: pending, approved, rejected, withdrawn", req.Status)
	}
	newStatus = dbStatus

	var adminNotesPtr *string
	if req.AdminNotes != nil {
		adminNotesPtr = req.AdminNotes
	}

	updatedApp, err := s.Store.UpdateVolunteerApplicationStatus(ctx, db.UpdateVolunteerApplicationStatusParams{
		Status:           newStatus,
		ReviewedByUserID: &reviewerID,
		AdminNotes:       adminNotesPtr,
		ID:               appID,
		OrganizationID:   orgID,
	})
	if err != nil {
		log.Error().Err(err).Msg("Error updating volunteer application status")
		return payloads.VolunteerApplicationResponse{}, err
	}

	// Post-approval/rejection actions (like notifications) can still happen here
	if newStatus == db.ApplicationStatusEnumApproved {
		log.Info().Str("applicationID", updatedApp.ID.String()).Str("userID", updatedApp.UserID.String()).Msg("Volunteer application approved.")
		// TODO: Send notification to user about approval
		// s.NotificationSvc.SendVolunteerApprovedNotification(updatedApp.UserID, updatedApp.OrganizationID)
	} else if newStatus == db.ApplicationStatusEnumRejected {
		log.Info().Str("applicationID", updatedApp.ID.String()).Str("userID", updatedApp.UserID.String()).Msg("Volunteer application rejected.")
		// TODO: Send notification to user about rejection
		// s.NotificationSvc.SendVolunteerRejectedNotification(updatedApp.UserID, updatedApp.OrganizationID, req.AdminNotes)
	}

	org, orgErr := s.Store.GetOrganizationByID(ctx, updatedApp.OrganizationID)
	orgName := sql.NullString{}
	if orgErr == nil {
		orgName.String = org.Name
		orgName.Valid = true
	} else if !errors.Is(orgErr, sql.ErrNoRows) {
		log.Warn().Err(orgErr).Str("organizationID", updatedApp.OrganizationID.String()).Msg("Failed to fetch organization name for reviewed application")
	}

	// Fetch reviewer display name for the response
	var reviewerName sql.NullString
	if updatedApp.ReviewedByUserID != nil && *updatedApp.ReviewedByUserID != uuid.Nil {
		reviewer, err := s.Store.GetUserByID(ctx, *updatedApp.ReviewedByUserID)
		if err != nil {
			if !errors.Is(err, sql.ErrNoRows) {
				log.Warn().Err(err).Str("reviewerID", updatedApp.ReviewedByUserID.String()).Msg("Failed to fetch reviewer display name")
			}
		} else {
			reviewerName.String = reviewer.DisplayName
			reviewerName.Valid = true
		}
	}

	// Refetch applicant details for the response as UpdateVolunteerApplicationStatus only returns the application table fields
	applicantUser, appUserErr := s.Store.GetUserByID(ctx, updatedApp.UserID)
	applicantName := sql.NullString{}
	applicantEmail := sql.NullString{}
	applicantPhone := sql.NullString{}

	if appUserErr == nil {
		applicantName.String = applicantUser.DisplayName
		applicantName.Valid = true
		if applicantUser.Email != nil {
			applicantEmail.String = *applicantUser.Email
			applicantEmail.Valid = true
		}
		if applicantUser.Phone != nil {
			applicantPhone.String = *applicantUser.Phone
			applicantPhone.Valid = true
		}
	} else {
		log.Warn().Err(appUserErr).Str("applicantUserID", updatedApp.UserID.String()).Msg("Failed to fetch applicant details for response after review")
	}

	appForResponse := db.UserVolunteerApplication{
		ID:               updatedApp.ID,
		UserID:           updatedApp.UserID,
		OrganizationID:   updatedApp.OrganizationID,
		ApplicationDate:  updatedApp.ApplicationDate,
		Status:           updatedApp.Status,
		Motivation:       updatedApp.Motivation,
		ReviewedByUserID: updatedApp.ReviewedByUserID,
		ReviewDate:       updatedApp.ReviewDate,
		AdminNotes:       updatedApp.AdminNotes,
		CreatedAt:        updatedApp.CreatedAt,
		UpdatedAt:        updatedApp.UpdatedAt,
	}

	return s.mapDBApplicationToResponse(appForResponse, orgName, applicantName, applicantEmail, applicantPhone, reviewerName), nil
}

// WithdrawOrganizationApplication allows a user to withdraw their volunteer application for an organization.
func (s *VolunteerService) WithdrawOrganizationApplication(ctx context.Context, userID uuid.UUID, appID uuid.UUID) (payloads.VolunteerApplicationResponse, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Str("application_id", appID.String()).Logger()

	// 1. Fetch the application to ensure it belongs to the user and to check current status.
	app, err := s.Store.GetUserVolunteerApplicationByID(ctx, db.GetUserVolunteerApplicationByIDParams{
		ID:     appID,
		UserID: userID,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			logger.Warn().Msg("Volunteer application not found or does not belong to user")
			return payloads.VolunteerApplicationResponse{}, ErrApplicationNotFound
		}
		logger.Error().Err(err).Msg("Error fetching volunteer application")
		return payloads.VolunteerApplicationResponse{}, err
	}

	// 2. Check if the application status allows withdrawal (e.g., must be 'pending' or 'approved').
	//    This should align with the WHERE clause in the SetUserVolunteerApplicationStatusToWithdrawnByUser query.
	if !(app.Status == db.ApplicationStatusEnumPending || app.Status == db.ApplicationStatusEnumApproved) {
		logger.Warn().Str("current_status", string(app.Status)).Msg("Application is not in a withdrawable state")
		return payloads.VolunteerApplicationResponse{}, ErrApplicationNotWithdrawable
	}

	// 3. Update the application status to 'withdrawn' in the database.
	//    We expect a SQLC query like SetUserVolunteerApplicationStatusToWithdrawnByUser.
	//    The status 'withdrawn' will be hardcoded in that query.
	updatedApp, err := s.Store.SetUserVolunteerApplicationStatusToWithdrawnByUser(ctx, db.SetUserVolunteerApplicationStatusToWithdrawnByUserParams{
		ID:     appID,
		UserID: userID,
		// The new status 'withdrawn' is set directly in the SQL query for this specific action.
		// The query's WHERE clause should also validate that status is 'pending' or 'approved'.
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) { // Should not happen if initial fetch worked and status was correct, implies a race or inconsistent query logic
			logger.Error().Err(err).Msg("Failed to withdraw application, possibly due to status change or race condition")
			return payloads.VolunteerApplicationResponse{}, ErrApplicationNotWithdrawable // Or a more generic update error
		}
		logger.Error().Err(err).Msg("Error updating volunteer application status to withdrawn")
		return payloads.VolunteerApplicationResponse{}, err
	}

	// 4. Return the updated application details.
	// We need the organization name for the response.
	org, orgErr := s.Store.GetOrganizationByID(ctx, updatedApp.OrganizationID)
	orgName := sql.NullString{}
	if orgErr == nil {
		orgName.String = org.Name
		orgName.Valid = true
	} else if !errors.Is(orgErr, sql.ErrNoRows) {
		logger.Warn().Err(orgErr).Str("organization_id", updatedApp.OrganizationID.String()).Msg("Failed to fetch organization name for withdrawn application response")
	}

	return s.mapDBApplicationToResponse(updatedApp, orgName, sql.NullString{}, sql.NullString{}, sql.NullString{}, sql.NullString{}), nil
}
