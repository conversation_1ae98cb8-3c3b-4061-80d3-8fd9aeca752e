import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
  Platform,
  Image,
  Modal,
  TouchableWithoutFeedback,
  ScrollView,
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import { type OrganizationListPayload } from '@/api/api_config';

interface Props {
  visible: boolean;
  onClose: (selectedOrgIds?: string[]) => void;
  organizations: OrganizationListPayload[];
  userOrganizationIds: string[];
}

export default function OrganizationManagement({
  visible,
  onClose,
  organizations,
  userOrganizationIds,
}: Props) {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme);
  const [selectedOrgIds, setSelectedOrgIds] = useState<string[]>([]);
  const [animation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (visible) {
      // Initialize with user's current organizations
      setSelectedOrgIds([...userOrganizationIds]);
      
      // Enter animation
      Animated.timing(animation, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    } else {
      // Exit animation
      Animated.timing(animation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, userOrganizationIds]);

  const translateY = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [Dimensions.get('window').height, 0],
  });

  const handleClose = (selectedIds?: string[]) => {
    // Run exit animations first
    Animated.timing(animation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      // Call onClose after animations complete, passing the selected organization IDs
      onClose(selectedIds);
    });
  };

  const toggleOrganization = (orgId: string) => {
    setSelectedOrgIds(prevSelected => {
      if (prevSelected.includes(orgId)) {
        return prevSelected.filter(id => id !== orgId);
      } else {
        return [...prevSelected, orgId];
      }
    });
  };

  const handleConfirm = () => {
    // Check if there are any changes by comparing selected organizations with user's current organizations
    const hasChanges = 
      selectedOrgIds.length !== userOrganizationIds.length ||
      selectedOrgIds.some(id => !userOrganizationIds.includes(id)) ||
      userOrganizationIds.some(id => !selectedOrgIds.includes(id));
    
    if (hasChanges) {
      // Only pass selected IDs if there are changes
      handleClose(selectedOrgIds);
    } else {
      // Close without triggering API call if no changes
      handleClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={() => handleClose()}
    >
      <TouchableWithoutFeedback onPress={() => handleClose()}>
        <Animated.View style={[styles.overlay, { opacity: animation }]}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.content,
                {
                  transform: [{ translateY }],
                }
              ]}
            >
              <View style={styles.handle} />
              <Text style={styles.title}>{t('profile.organization.manageOrganizations')}</Text>
              <Text style={styles.subtitle}>{t('profile.organization.selectToJoin')}</Text>
              
              <ScrollView style={styles.optionsContainer} contentContainerStyle={styles.optionsContent}>
                {organizations.length > 0 ? (
                  organizations.map((org) => {
                    const isSelected = selectedOrgIds.includes(org.id!);
                    const isAlreadyJoined = userOrganizationIds.includes(org.id!);
                    // use theme_color from org and map it to a theme name, default is red.
                    const orgThemeName = org.theme_color || 'red';
                    const orgTheme = createTheme(orgThemeName);
                    
                    return (
                      <TouchableOpacity
                        key={org.id!}
                        style={[
                          styles.option,
                          isSelected && { backgroundColor: orgTheme.colors.primaryContainer }
                        ]}
                        onPress={() => toggleOrganization(org.id!)}
                      >
                        <View style={styles.orgContent}>
                          <Image
                            // Use image_url for the logo
                            source={org.image_url ? { uri: org.image_url } : require('@/assets/default-images/default-logo.png')}
                            style={styles.orgLogo}
                            resizeMode="contain"
                          />
                          <View style={styles.orgTextContainer}>
                            <Text style={[
                              styles.optionText,
                              isSelected && { 
                                fontWeight: '600',
                                color: orgTheme.colors.primary 
                              }
                            ]}>
                              {org.name}
                            </Text>
                          </View>
                        </View>
                        
                        <View style={[
                          styles.checkbox,
                          isSelected && { 
                            backgroundColor: orgTheme.colors.primary,
                            borderColor: orgTheme.colors.primary
                          }
                        ]}>
                          {isSelected && (
                            <MaterialCommunityIcons
                              name="check"
                              size={16}
                              color="#FFFFFF"
                            />
                          )}
                        </View>
                      </TouchableOpacity>
                    );
                  })
                ) : (
                  <View style={styles.emptyState}>
                    <Text style={styles.emptyTitle}>
                      {t('profile.organization.noOrganizations.title')}
                    </Text>
                    <Text style={styles.emptyMessage}>
                      {t('profile.organization.noOrganizations.message')}
                    </Text>
                  </View>
                )}
              </ScrollView>
              
              <TouchableOpacity
                style={[styles.confirmButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleConfirm}
              >
                <Text style={styles.confirmButtonText}>
                  {t('profile.organization.updateSettings')}
                </Text>
              </TouchableOpacity>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  content: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    paddingTop: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 24,
  },
  handle: {
    width: 36,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    marginLeft: 12,
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
    marginLeft: 12,
  },
  optionsContainer: {
    marginBottom: 24,
    maxHeight: Dimensions.get('window').height * 0.5,
  },
  optionsContent: {
    paddingHorizontal: 12,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 56,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  optionText: {
    fontSize: 16,
    color: '#333333',
  },
  orgContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  orgTextContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    flex: 1,
  },
  orgLogo: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#CCCCCC',
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButton: {
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 12,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
}); 