package utils

import (
	"Membership-SAAS-System-Backend/internal/payloads"
	"regexp"

	"github.com/go-playground/validator/v10"
)

// Regular expression for alphanumdash validation: allows letters, numbers, and dashes.
var alphanumdashRegex = regexp.MustCompile(`^[a-zA-Z0-9-]+$`)

// Regular expression for alphanumunderscore validation: allows letters, numbers, and underscores.
var alphanumUnderscoreRegex = regexp.MustCompile(`^[a-zA-Z0-9_]+$`)

// alphanumdash validation function
func validateAlphanumdash(fl validator.FieldLevel) bool {
	// Check if the field value is a string and matches the regex
	return alphanumdashRegex.MatchString(fl.Field().String())
}

// alphanumunderscore validation function
func validateAlphanumUnderscore(fl validator.FieldLevel) bool {
	// Check if the field value is a string and matches the regex
	return alphanumUnderscoreRegex.MatchString(fl.Field().String())
}

// RequestValidator wraps the validator instance.
type RequestValidator struct {
	validate *validator.Validate
}

// NewRequestValidator creates a new RequestValidator.
func NewRequestValidator() *RequestValidator {
	v := validator.New()
	// Register custom validations
	err := v.RegisterValidation("alphanumdash", validateAlphanumdash)
	if err != nil {
		// Handle error during registration, maybe panic or log fatal
		// For now, let's just log it, though it indicates a setup problem
		println("FATAL: Failed to register alphanumdash validator:", err.Error())
	}

	err = v.RegisterValidation("alphanumunderscore", validateAlphanumUnderscore)
	if err != nil {
		println("FATAL: Failed to register alphanumunderscore validator:", err.Error())
	}

	// Register verification_type_enum validator
	err = v.RegisterValidation("verification_type_enum", payloads.ValidateVerificationTypeEnum)
	if err != nil {
		println("FATAL: Failed to register verification_type_enum validator:", err.Error())
	}

	// Register admin_review_status_enum validator
	err = v.RegisterValidation("admin_review_status_enum", payloads.ValidateAdminReviewStatusEnum)
	if err != nil {
		println("FATAL: Failed to register admin_review_status_enum validator:", err.Error())
	}

	return &RequestValidator{validate: v}
}

// ValidateStruct validates a struct using the underlying validator.
// It's a convenience method to be called by handlers.
func (rv *RequestValidator) ValidateStruct(s interface{}) error {
	return rv.validate.Struct(s)
}

// RegisterValidation can be used to register custom validation functions if needed.
func (rv *RequestValidator) RegisterValidation(tag string, fn validator.Func) error {
	return rv.validate.RegisterValidation(tag, fn)
}
