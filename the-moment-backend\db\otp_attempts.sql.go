// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: otp_attempts.sql

package db

import (
	"context"
	"time"
)

const getOTPAttempt = `-- name: GetOTPAttempt :one
SELECT id, phone, attempt_count, last_attempt_at, locked_until, created_at 
FROM otp_attempts
WHERE phone = $1
`

func (q *Queries) GetOTPAttempt(ctx context.Context, phone string) (OtpAttempt, error) {
	row := q.db.QueryRow(ctx, getOTPAttempt, phone)
	var i OtpAttempt
	err := row.Scan(
		&i.ID,
		&i.Phone,
		&i.AttemptCount,
		&i.LastAttemptAt,
		&i.<PERSON>,
		&i.<PERSON>t,
	)
	return i, err
}

const lockOTPAttempts = `-- name: LockOTPAttempts :one
UPDATE otp_attempts
SET locked_until = $2, last_attempt_at = NOW()
WHERE phone = $1
RETURNING id, phone, attempt_count, last_attempt_at, locked_until, created_at
`

type LockOTPAttemptsParams struct {
	Phone       string     `db:"phone" json:"phone"`
	LockedUntil *time.Time `db:"locked_until" json:"locked_until"`
}

func (q *Queries) LockOTPAttempts(ctx context.Context, arg LockOTPAttemptsParams) (OtpAttempt, error) {
	row := q.db.QueryRow(ctx, lockOTPAttempts, arg.Phone, arg.LockedUntil)
	var i OtpAttempt
	err := row.Scan(
		&i.ID,
		&i.Phone,
		&i.AttemptCount,
		&i.LastAttemptAt,
		&i.LockedUntil,
		&i.CreatedAt,
	)
	return i, err
}

const resetOTPAttempts = `-- name: ResetOTPAttempts :exec
DELETE FROM otp_attempts
WHERE phone = $1
`

func (q *Queries) ResetOTPAttempts(ctx context.Context, phone string) error {
	_, err := q.db.Exec(ctx, resetOTPAttempts, phone)
	return err
}

const upsertOTPAttempt = `-- name: UpsertOTPAttempt :one
INSERT INTO otp_attempts (phone, attempt_count, last_attempt_at, locked_until)
VALUES ($1, 1, NOW(), NULL)
ON CONFLICT (phone) 
DO UPDATE SET 
    attempt_count = otp_attempts.attempt_count + 1,
    last_attempt_at = NOW(),
    -- Only update locked_until if explicitly set by a lock operation, otherwise keep existing or NULL
    locked_until = CASE WHEN otp_attempts.locked_until > NOW() THEN otp_attempts.locked_until ELSE NULL END
WHERE otp_attempts.phone = $1
RETURNING id, phone, attempt_count, last_attempt_at, locked_until, created_at
`

func (q *Queries) UpsertOTPAttempt(ctx context.Context, phone string) (OtpAttempt, error) {
	row := q.db.QueryRow(ctx, upsertOTPAttempt, phone)
	var i OtpAttempt
	err := row.Scan(
		&i.ID,
		&i.Phone,
		&i.AttemptCount,
		&i.LastAttemptAt,
		&i.LockedUntil,
		&i.CreatedAt,
	)
	return i, err
}
