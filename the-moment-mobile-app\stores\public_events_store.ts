import {create} from 'zustand';
import { 
    type EventListPayload,
    type EventListPayloadDetails,
    type EventTagPayload,
    type PublishedEventListPullRequest,
} from '@/api/api_config';
import { AxiosError } from 'axios'; 
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface DashboardEventsListState {
    dashboardEventsList: EventListPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setDashboardEventsList: (events: EventListPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const dashboardEventsListStore = create<DashboardEventsListState>()(
    persist<DashboardEventsListState>(
        (set) => ({
            dashboardEventsList: [],
            isFetching: false,
            error: null,
            setDashboardEventsList: (events: EventListPayload[]) => set({ dashboardEventsList: events, isFetching: false, error: null }),
            setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
        }),
        {
            name: 'dashboard-events-list-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface ExploreEventsListState {
    exploreEventsList: EventListPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setExploreEventsList: (events: EventListPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const exploreEventsListStore = create<ExploreEventsListState>()(
    persist<ExploreEventsListState>(
        (set) => ({
            exploreEventsList: [],
            isFetching: false,
            error: null,
            setExploreEventsList: (events: EventListPayload[]) => set({ exploreEventsList: events, isFetching: false, error: null }),
            setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
        }),
        {
            name: 'explore-events-list-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface EventDetailsState {
    eventDetails: EventListPayloadDetails | null;
    isFetching: boolean;
    error: AxiosError | null;
    setEventDetails: (event: EventListPayloadDetails) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const eventDetailsStore = create<EventDetailsState>()(
    persist<EventDetailsState>(
        (set) => ({
            eventDetails: null,
            isFetching: false,
            error: null,
            setEventDetails: (event: EventListPayloadDetails | null) => set({ eventDetails: event, isFetching: false, error: null }),
            setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
        }),
        {
            name: 'event-details-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface EventTagsState {
    eventTags: EventTagPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setEventTags: (tags: EventTagPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const eventTagsStore = create<EventTagsState>()(
    persist<EventTagsState>(
        (set) => ({
            eventTags: [],
            isFetching: false,
            error: null,
            setEventTags: (tags: EventTagPayload[]) => set({ eventTags: tags, isFetching: false, error: null }),
            setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
        }),
        {
            name: 'event-tags-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface ExploreEventsFilterState {
    filter: PublishedEventListPullRequest;
    setFilter: (filter: PublishedEventListPullRequest) => void;
}

export const exploreEventsFilterListStore = create<ExploreEventsFilterState>()(
    persist<ExploreEventsFilterState>(
        (set) => ({
            filter: {},
            setFilter: (filter) => set({ filter }),
        }),
        {
            name: 'explore-events-filter-list-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);
