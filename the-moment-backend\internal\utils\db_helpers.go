package utils

import (
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

// PgtypeTextToStringPtr converts pgtype.Text to *string.
// If the pgtype.Text is not valid (NULL in DB), it returns nil.
func PgtypeTextToStringPtr(pt pgtype.Text) *string {
	if !pt.Valid {
		return nil
	}
	return &pt.String
}

// PgtypeTimestamptzToTimePtr converts pgtype.Timestamptz to *time.Time.
// If the pgtype.Timestamptz is not valid (NULL in DB), it returns nil.
func PgtypeTimestamptzToTimePtr(pt pgtype.Timestamptz) *time.Time {
	if !pt.Valid {
		return nil
	}
	return &pt.Time
}

// StringToPgtypeText converts a string to pgtype.Text.
// If the string is empty, it creates an invalid pgtype.Text (NULL).
// For non-empty strings, it creates a valid pgtype.Text.
func StringToPgtypeText(s *string) pgtype.Text {
	if s == nil || *s == "" {
		return pgtype.Text{Valid: false}
	}
	return pgtype.Text{String: *s, Valid: true}
}
