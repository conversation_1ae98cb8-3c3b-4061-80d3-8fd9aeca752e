// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: users.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const createStaffUserWithEmailPassword = `-- name: CreateStaffUserWithEmailPassword :one
INSERT INTO users (
    display_name,
    email,
    email_verified_at, -- Assume verified for testing endpoint
    hashed_password,
    interface_language,
    communication_language,
    enable_app_notifications,
    enable_whatsapp_notifications,
    enable_sms_notifications,
    enable_email_notifications,
    phone_otp_channel, -- Can be default, not primary for this user type
    role
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11,
    'admin'
)
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type CreateStaffUserWithEmailPasswordParams struct {
	DisplayName                 string     `db:"display_name" json:"display_name"`
	Email                       *string    `db:"email" json:"email"`
	EmailVerifiedAt             *time.Time `db:"email_verified_at" json:"email_verified_at"`
	HashedPassword              *string    `db:"hashed_password" json:"hashed_password"`
	InterfaceLanguage           string     `db:"interface_language" json:"interface_language"`
	CommunicationLanguage       string     `db:"communication_language" json:"communication_language"`
	EnableAppNotifications      bool       `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications bool       `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool       `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications    bool       `db:"enable_email_notifications" json:"enable_email_notifications"`
	PhoneOtpChannel             string     `db:"phone_otp_channel" json:"phone_otp_channel"`
}

func (q *Queries) CreateStaffUserWithEmailPassword(ctx context.Context, arg CreateStaffUserWithEmailPasswordParams) (User, error) {
	row := q.db.QueryRow(ctx, createStaffUserWithEmailPassword,
		arg.DisplayName,
		arg.Email,
		arg.EmailVerifiedAt,
		arg.HashedPassword,
		arg.InterfaceLanguage,
		arg.CommunicationLanguage,
		arg.EnableAppNotifications,
		arg.EnableWhatsappNotifications,
		arg.EnableSmsNotifications,
		arg.EnableEmailNotifications,
		arg.PhoneOtpChannel,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const createUserWithPhone = `-- name: CreateUserWithPhone :one
INSERT INTO users (
    display_name,
    phone,
    phone_verified_at,
    -- email, -- Can be added later via profile update
    -- hashed_password, -- Not set during phone OTP registration
    interface_language,
    communication_language,
    enable_app_notifications,
    enable_whatsapp_notifications,
    enable_sms_notifications,
    enable_email_notifications,
    phone_otp_channel
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
)
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type CreateUserWithPhoneParams struct {
	DisplayName                 string     `db:"display_name" json:"display_name"`
	Phone                       *string    `db:"phone" json:"phone"`
	PhoneVerifiedAt             *time.Time `db:"phone_verified_at" json:"phone_verified_at"`
	InterfaceLanguage           string     `db:"interface_language" json:"interface_language"`
	CommunicationLanguage       string     `db:"communication_language" json:"communication_language"`
	EnableAppNotifications      bool       `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications bool       `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool       `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications    bool       `db:"enable_email_notifications" json:"enable_email_notifications"`
	PhoneOtpChannel             string     `db:"phone_otp_channel" json:"phone_otp_channel"`
}

func (q *Queries) CreateUserWithPhone(ctx context.Context, arg CreateUserWithPhoneParams) (User, error) {
	row := q.db.QueryRow(ctx, createUserWithPhone,
		arg.DisplayName,
		arg.Phone,
		arg.PhoneVerifiedAt,
		arg.InterfaceLanguage,
		arg.CommunicationLanguage,
		arg.EnableAppNotifications,
		arg.EnableWhatsappNotifications,
		arg.EnableSmsNotifications,
		arg.EnableEmailNotifications,
		arg.PhoneOtpChannel,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const deleteUser = `-- name: DeleteUser :exec

DELETE FROM users
WHERE id = $1
`

// Note: The CountUsers query is removed as ListUsersFiltered now includes COUNT(*) OVER()
func (q *Queries) DeleteUser(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteUser, id)
	return err
}

const getMonthlyAttendedEvents = `-- name: GetMonthlyAttendedEvents :many

SELECT
    TO_CHAR(e.start_time, 'YYYY-MM') AS month,
    COUNT(er.id) AS count
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
AND er.status = 'attended'
AND e.start_time >= NOW() - INTERVAL '6 months'
GROUP BY month
ORDER BY month DESC
`

type GetMonthlyAttendedEventsRow struct {
	Month string `db:"month" json:"month"`
	Count int64  `db:"count" json:"count"`
}

// or whatever status indicates confirmed volunteering
func (q *Queries) GetMonthlyAttendedEvents(ctx context.Context, userID uuid.UUID) ([]GetMonthlyAttendedEventsRow, error) {
	rows, err := q.db.Query(ctx, getMonthlyAttendedEvents, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetMonthlyAttendedEventsRow{}
	for rows.Next() {
		var i GetMonthlyAttendedEventsRow
		if err := rows.Scan(&i.Month, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTopAttendedEventTags = `-- name: GetTopAttendedEventTags :many
SELECT
    et.name_en,
    et.name_zh_hk,
    et.name_zh_cn,
    COUNT(er.id) AS count
FROM event_registrations er
JOIN events e ON er.event_id = e.id
JOIN event_event_tags eet ON e.id = eet.event_id
JOIN event_tags et ON eet.event_tag_id = et.id
WHERE er.user_id = $1
AND er.status = 'attended'
AND e.start_time >= NOW() - INTERVAL '6 months'
GROUP BY et.id, et.name_en, et.name_zh_hk, et.name_zh_cn
ORDER BY count DESC
LIMIT 5
`

type GetTopAttendedEventTagsRow struct {
	NameEn   string `db:"name_en" json:"name_en"`
	NameZhHk string `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn string `db:"name_zh_cn" json:"name_zh_cn"`
	Count    int64  `db:"count" json:"count"`
}

func (q *Queries) GetTopAttendedEventTags(ctx context.Context, userID uuid.UUID) ([]GetTopAttendedEventTagsRow, error) {
	rows, err := q.db.Query(ctx, getTopAttendedEventTags, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetTopAttendedEventTagsRow{}
	for rows.Next() {
		var i GetTopAttendedEventTagsRow
		if err := rows.Scan(
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.Count,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
FROM users
WHERE email = $1 LIMIT 1
`

func (q *Queries) GetUserByEmail(ctx context.Context, email *string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
FROM users
WHERE id = $1 LIMIT 1
`

func (q *Queries) GetUserByID(ctx context.Context, id uuid.UUID) (User, error) {
	row := q.db.QueryRow(ctx, getUserByID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const getUserByPhone = `-- name: GetUserByPhone :one
SELECT id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
FROM users
WHERE phone = $1 LIMIT 1
`

func (q *Queries) GetUserByPhone(ctx context.Context, phone *string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByPhone, phone)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const getUserRegistrationDate = `-- name: GetUserRegistrationDate :one
SELECT created_at AS registration_date FROM users
WHERE id = $1
`

func (q *Queries) GetUserRegistrationDate(ctx context.Context, id uuid.UUID) (time.Time, error) {
	row := q.db.QueryRow(ctx, getUserRegistrationDate, id)
	var registration_date time.Time
	err := row.Scan(&registration_date)
	return registration_date, err
}

const getUserTotalAttendedEvents = `-- name: GetUserTotalAttendedEvents :one
SELECT
    COUNT(er.id) AS total_attended_events
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
AND er.status = 'attended'
`

func (q *Queries) GetUserTotalAttendedEvents(ctx context.Context, userID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getUserTotalAttendedEvents, userID)
	var total_attended_events int64
	err := row.Scan(&total_attended_events)
	return total_attended_events, err
}

const getUserTotalVolunteerEvents = `-- name: GetUserTotalVolunteerEvents :one
SELECT COUNT(*) AS total_volunteer_events
FROM event_volunteer_applications eva
WHERE eva.user_id = $1
AND eva.status = 'approved'
`

func (q *Queries) GetUserTotalVolunteerEvents(ctx context.Context, userID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getUserTotalVolunteerEvents, userID)
	var total_volunteer_events int64
	err := row.Scan(&total_volunteer_events)
	return total_volunteer_events, err
}

const listUsersFiltered = `-- name: ListUsersFiltered :many
SELECT
    u.id,
    u.display_name,
    u.email,
    u.email_verified_at,
    u.phone,
    u.phone_verified_at,
    u.profile_picture_url AS avatar_url, -- Use schema name and alias
    u.role,
    u.created_at,
    u.updated_at,
    COUNT(*) OVER() AS total_count
FROM
    users u
WHERE
    ($1::TEXT IS NULL OR u.display_name ILIKE $1::TEXT OR u.email ILIKE $1::TEXT)
    AND ($2::TEXT IS NULL OR u.email = $2::TEXT)
    AND ($3::TEXT IS NULL OR u.role = $3::user_role)
    -- New condition for filtering admins by organizations owned by actor_org_owner_id
    AND (
        $4::UUID IS NULL OR -- If no owner ID, this condition is true
        $3::TEXT != 'admin' OR   -- If not filtering for admins, this condition is true
        EXISTS (                                     -- Otherwise, check if the user 'u' is an admin in an org owned by actor_org_owner_id
            SELECT 1
            FROM user_organization_memberships uom_member
            JOIN user_organization_memberships uom_owner ON uom_member.organization_id = uom_owner.organization_id
            WHERE uom_member.user_id = u.id -- The user 'u' we are considering
            AND uom_owner.user_id = $4::UUID -- The actor who is an owner
            AND uom_owner.role = 'owner'
            AND uom_member.role = 'admin' -- Ensure the user 'u' is an admin in that org (redundant if role_filter is 'admin', but good for clarity)
        )
    )
ORDER BY
    CASE WHEN $5::TEXT = 'display_name' AND $6::TEXT = 'asc' THEN u.display_name END ASC,
    CASE WHEN $5::TEXT = 'display_name' AND $6::TEXT = 'desc' THEN u.display_name END DESC,
    CASE WHEN $5::TEXT = 'email' AND $6::TEXT = 'asc' THEN u.email END ASC,
    CASE WHEN $5::TEXT = 'email' AND $6::TEXT = 'desc' THEN u.email END DESC,
    CASE WHEN $5::TEXT = 'created_at' AND $6::TEXT = 'asc' THEN u.created_at END ASC,
    CASE WHEN $5::TEXT = 'created_at' AND $6::TEXT = 'desc' THEN u.created_at END DESC,
    u.created_at DESC -- Default sort
LIMIT $8
OFFSET $7
`

type ListUsersFilteredParams struct {
	SearchQuery     *string    `db:"search_query" json:"search_query"`
	Email           *string    `db:"email" json:"email"`
	RoleFilter      *string    `db:"role_filter" json:"role_filter"`
	ActorOrgOwnerID *uuid.UUID `db:"actor_org_owner_id" json:"actor_org_owner_id"`
	SortBy          *string    `db:"sort_by" json:"sort_by"`
	SortOrder       *string    `db:"sort_order" json:"sort_order"`
	PageOffset      int32      `db:"page_offset" json:"page_offset"`
	PageSize        int32      `db:"page_size" json:"page_size"`
}

type ListUsersFilteredRow struct {
	ID              uuid.UUID  `db:"id" json:"id"`
	DisplayName     string     `db:"display_name" json:"display_name"`
	Email           *string    `db:"email" json:"email"`
	EmailVerifiedAt *time.Time `db:"email_verified_at" json:"email_verified_at"`
	Phone           *string    `db:"phone" json:"phone"`
	PhoneVerifiedAt *time.Time `db:"phone_verified_at" json:"phone_verified_at"`
	AvatarUrl       *string    `db:"avatar_url" json:"avatar_url"`
	Role            UserRole   `db:"role" json:"role"`
	CreatedAt       time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt       time.Time  `db:"updated_at" json:"updated_at"`
	TotalCount      int64      `db:"total_count" json:"total_count"`
}

func (q *Queries) ListUsersFiltered(ctx context.Context, arg ListUsersFilteredParams) ([]ListUsersFilteredRow, error) {
	rows, err := q.db.Query(ctx, listUsersFiltered,
		arg.SearchQuery,
		arg.Email,
		arg.RoleFilter,
		arg.ActorOrgOwnerID,
		arg.SortBy,
		arg.SortOrder,
		arg.PageOffset,
		arg.PageSize,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListUsersFilteredRow{}
	for rows.Next() {
		var i ListUsersFilteredRow
		if err := rows.Scan(
			&i.ID,
			&i.DisplayName,
			&i.Email,
			&i.EmailVerifiedAt,
			&i.Phone,
			&i.PhoneVerifiedAt,
			&i.AvatarUrl,
			&i.Role,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const setUserPhoneVerified = `-- name: SetUserPhoneVerified :one
UPDATE users
SET phone_verified_at = $2, updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type SetUserPhoneVerifiedParams struct {
	ID              uuid.UUID  `db:"id" json:"id"`
	PhoneVerifiedAt *time.Time `db:"phone_verified_at" json:"phone_verified_at"`
}

func (q *Queries) SetUserPhoneVerified(ctx context.Context, arg SetUserPhoneVerifiedParams) (User, error) {
	row := q.db.QueryRow(ctx, setUserPhoneVerified, arg.ID, arg.PhoneVerifiedAt)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const updateUserDisplayName = `-- name: UpdateUserDisplayName :one
UPDATE users
SET display_name = $2, updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type UpdateUserDisplayNameParams struct {
	ID          uuid.UUID `db:"id" json:"id"`
	DisplayName string    `db:"display_name" json:"display_name"`
}

func (q *Queries) UpdateUserDisplayName(ctx context.Context, arg UpdateUserDisplayNameParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserDisplayName, arg.ID, arg.DisplayName)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const updateUserEmailAndVerificationStatus = `-- name: UpdateUserEmailAndVerificationStatus :one
UPDATE users
SET
    email = $2,
    email_verified_at = $3, -- Assume verified for testing endpoint
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type UpdateUserEmailAndVerificationStatusParams struct {
	ID              uuid.UUID  `db:"id" json:"id"`
	Email           *string    `db:"email" json:"email"`
	EmailVerifiedAt *time.Time `db:"email_verified_at" json:"email_verified_at"`
}

func (q *Queries) UpdateUserEmailAndVerificationStatus(ctx context.Context, arg UpdateUserEmailAndVerificationStatusParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserEmailAndVerificationStatus, arg.ID, arg.Email, arg.EmailVerifiedAt)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const updateUserLanguagePreferences = `-- name: UpdateUserLanguagePreferences :one
UPDATE users
SET 
    interface_language = $2,
    communication_language = $3,
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type UpdateUserLanguagePreferencesParams struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	InterfaceLanguage     string    `db:"interface_language" json:"interface_language"`
	CommunicationLanguage string    `db:"communication_language" json:"communication_language"`
}

func (q *Queries) UpdateUserLanguagePreferences(ctx context.Context, arg UpdateUserLanguagePreferencesParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserLanguagePreferences, arg.ID, arg.InterfaceLanguage, arg.CommunicationLanguage)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const updateUserNotificationSettings = `-- name: UpdateUserNotificationSettings :one
UPDATE users
SET 
    enable_app_notifications = $2,
    enable_whatsapp_notifications = $3,
    enable_sms_notifications = $4,
    enable_email_notifications = $5,
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type UpdateUserNotificationSettingsParams struct {
	ID                          uuid.UUID `db:"id" json:"id"`
	EnableAppNotifications      bool      `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications bool      `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool      `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications    bool      `db:"enable_email_notifications" json:"enable_email_notifications"`
}

func (q *Queries) UpdateUserNotificationSettings(ctx context.Context, arg UpdateUserNotificationSettingsParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserNotificationSettings,
		arg.ID,
		arg.EnableAppNotifications,
		arg.EnableWhatsappNotifications,
		arg.EnableSmsNotifications,
		arg.EnableEmailNotifications,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const updateUserPhoneAndMarkVerified = `-- name: UpdateUserPhoneAndMarkVerified :one
UPDATE users
SET 
    phone = $2,
    phone_verified_at = NOW(), -- Mark as verified
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type UpdateUserPhoneAndMarkVerifiedParams struct {
	ID    uuid.UUID `db:"id" json:"id"`
	Phone *string   `db:"phone" json:"phone"`
}

func (q *Queries) UpdateUserPhoneAndMarkVerified(ctx context.Context, arg UpdateUserPhoneAndMarkVerifiedParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserPhoneAndMarkVerified, arg.ID, arg.Phone)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}

const updateUserPhoneAndVerificationStatus = `-- name: UpdateUserPhoneAndVerificationStatus :one
UPDATE users
SET 
    phone = $2,
    phone_verified_at = NULL, -- Needs re-verification
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type UpdateUserPhoneAndVerificationStatusParams struct {
	ID    uuid.UUID `db:"id" json:"id"`
	Phone *string   `db:"phone" json:"phone"`
}

func (q *Queries) UpdateUserPhoneAndVerificationStatus(ctx context.Context, arg UpdateUserPhoneAndVerificationStatusParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserPhoneAndVerificationStatus, arg.ID, arg.Phone)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}
