-- name: ListEventRegistrationsByStatusAndEventID :many
SELECT er.*, u.display_name as user_display_name, u.email as user_email, e.title as event_title
FROM event_registrations er
JOIN users u ON er.user_id = u.id
JOIN events e ON er.event_id = e.id
WHERE er.event_id = $1 AND er.status = $2
ORDER BY er.created_at DESC;

-- name: ListNotifiableUsersForEvent :many
SELECT
    u.id as user_id, -- Explicitly alias to user_id for clarity in Go struct
    u.phone,
    u.phone_otp_channel,
    u.enable_app_notifications,
    u.enable_whatsapp_notifications,
    u.enable_sms_notifications,
    u.enable_email_notifications,
    u.communication_language
FROM event_registrations er
JOIN users u ON er.user_id = u.id
WHERE er.event_id = $1
  AND er.status IN ('confirmed', 'accepted', 'checked_in') -- Define which statuses should receive reminders
  AND u.phone IS NOT NULL AND u.phone <> ''; -- Only users with phone numbers 

-- name: ListParticipantUserIDsByEventAndStatus :many
SELECT
    user_id
FROM
    event_registrations
WHERE
    event_id = $1
    AND ($2 = '' OR status = $2::event_registration_status_type); -- Cast $2 to the enum type

-- name: UpdateEventRegistrationStatusAndDetails :one
UPDATE event_registrations
SET
    status = $2,
    payment_status = $3,
    waitlist_priority = $4,
    registered_at = $5,
    cancellation_reason_by_user = NULL,
    admin_notes_on_registration = NULL,
    check_in_by_user_id = NULL,
    check_in_method = NULL,
    attended_at = NULL,
    updated_at = NOW()
WHERE id = $1
RETURNING *;