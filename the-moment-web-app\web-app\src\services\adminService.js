import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

export const adminService = {
  /**
   * Get user details by user ID (Admin)
   * @param {string} userId - The ID of the user to fetch.
   * @returns {Promise<UserDetailResponse>} - Promise resolving to user details.
   */
  getUserDetails: async (userId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ADMIN.GET_USER_DETAIL(userId));
      return response; // Assuming this response matches UserDetailResponse structure
    } catch (error) {
      console.error(`Error fetching user details for user ${userId} (admin):`, error);
      throw error;
    }
  },
  
  // Add other admin-specific service functions here in the future

  getUserVerifications: async (userId) => {
    try {
      // Use API.get to fetch verifications for a specific user
      const response = await api.get(`/admin/users/${userId}/verifications`);
      
      // Return the verification data
      return response.data;
    } catch (error) {
      console.error('Error in getUserVerifications:', error);
      // For demo purposes, return an empty array if the API is not implemented
      return [];
    }
  },

  // === Moved from adminUserService in organizationService.js ===
  
  createAdminUser: async (userData) => {
    // userData should match the backend's expected payload for creating a user
    try {
      const response = await api.post(API_ENDPOINTS.ADMIN.CREATE_ADMIN_USER, userData);
      return response;
    } catch (error) {
      console.error('Error creating admin user:', error);
      throw error;
    }
  },

  listUsers: async (params = {}) => {
    // List all users with pagination support
    // params: e.g., { page, limit, role, organization_id } - matches backend API /api/v1/admin/users
          try {
        const response = await api.get(API_ENDPOINTS.ADMIN.LIST_USERS, { params });
        return response;
      } catch (error) {
      console.error('Error listing users:', error);
      throw error;
    }
  },

  deleteUser: async (userId) => {
    // Delete user by UUID
    try {
      const response = await api.delete(API_ENDPOINTS.ADMIN.DELETE_USER(userId));
      return response;
    } catch (error) {
      console.error(`Error deleting user ${userId}:`, error);
      throw error;
    }
  },

  getAdminUserByPhone: async (phone) => {
    try {
      const response = await api.get(API_ENDPOINTS.ADMIN.GET_USER_BY_PHONE(phone));
      return response;
    } catch (error) {
      console.error(`Error fetching admin user by phone ${phone}:`, error);
      throw error;
    }
  },

  getAdminUserByEmail: async (email) => {
    try {
      // Ensure email is properly encoded if it contains special characters
      const encodedEmail = encodeURIComponent(email);
      const response = await api.get(API_ENDPOINTS.ADMIN.GET_USER_BY_EMAIL(encodedEmail));
      return response;
    } catch (error) {
      console.error(`Error fetching admin user by email ${email}:`, error);
      throw error;
    }
  },

  // Legacy methods (keeping for backward compatibility)
  listAdminSpecificUser: async (userId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ADMIN.LIST_SPECIFIC_USER(userId));
      return response;
    } catch (error) {
      console.error('Error listing admin users:', error);
      throw error;
    }
  },
}; 