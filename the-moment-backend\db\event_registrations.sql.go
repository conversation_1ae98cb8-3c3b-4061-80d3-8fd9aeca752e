// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: event_registrations.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const listEventRegistrationsByStatusAndEventID = `-- name: ListEventRegistrationsByStatusAndEventID :many
SELECT er.id, er.event_id, er.user_id, er.status, er.payment_status, er.registration_role, er.registered_at, er.attended_at, er.cancellation_reason_by_user, er.admin_notes_on_registration, er.waitlist_priority, er.created_at, er.updated_at, er.check_in_by_user_id, er.check_in_method, u.display_name as user_display_name, u.email as user_email, e.title as event_title
FROM event_registrations er
JOIN users u ON er.user_id = u.id
JOIN events e ON er.event_id = e.id
WHERE er.event_id = $1 AND er.status = $2
ORDER BY er.created_at DESC
`

type ListEventRegistrationsByStatusAndEventIDParams struct {
	EventID uuid.UUID                   `db:"event_id" json:"event_id"`
	Status  EventRegistrationStatusType `db:"status" json:"status"`
}

type ListEventRegistrationsByStatusAndEventIDRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
	CheckInByUserID          *uuid.UUID                  `db:"check_in_by_user_id" json:"check_in_by_user_id"`
	CheckInMethod            *string                     `db:"check_in_method" json:"check_in_method"`
	UserDisplayName          string                      `db:"user_display_name" json:"user_display_name"`
	UserEmail                *string                     `db:"user_email" json:"user_email"`
	EventTitle               string                      `db:"event_title" json:"event_title"`
}

func (q *Queries) ListEventRegistrationsByStatusAndEventID(ctx context.Context, arg ListEventRegistrationsByStatusAndEventIDParams) ([]ListEventRegistrationsByStatusAndEventIDRow, error) {
	rows, err := q.db.Query(ctx, listEventRegistrationsByStatusAndEventID, arg.EventID, arg.Status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListEventRegistrationsByStatusAndEventIDRow{}
	for rows.Next() {
		var i ListEventRegistrationsByStatusAndEventIDRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.Status,
			&i.PaymentStatus,
			&i.RegistrationRole,
			&i.RegisteredAt,
			&i.AttendedAt,
			&i.CancellationReasonByUser,
			&i.AdminNotesOnRegistration,
			&i.WaitlistPriority,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CheckInByUserID,
			&i.CheckInMethod,
			&i.UserDisplayName,
			&i.UserEmail,
			&i.EventTitle,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listNotifiableUsersForEvent = `-- name: ListNotifiableUsersForEvent :many
SELECT
    u.id as user_id, -- Explicitly alias to user_id for clarity in Go struct
    u.phone,
    u.phone_otp_channel,
    u.enable_app_notifications,
    u.enable_whatsapp_notifications,
    u.enable_sms_notifications,
    u.enable_email_notifications,
    u.communication_language
FROM event_registrations er
JOIN users u ON er.user_id = u.id
WHERE er.event_id = $1
  AND er.status IN ('confirmed', 'accepted', 'checked_in') -- Define which statuses should receive reminders
  AND u.phone IS NOT NULL AND u.phone <> ''
`

type ListNotifiableUsersForEventRow struct {
	UserID                      uuid.UUID `db:"user_id" json:"user_id"`
	Phone                       *string   `db:"phone" json:"phone"`
	PhoneOtpChannel             string    `db:"phone_otp_channel" json:"phone_otp_channel"`
	EnableAppNotifications      bool      `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications bool      `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool      `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications    bool      `db:"enable_email_notifications" json:"enable_email_notifications"`
	CommunicationLanguage       string    `db:"communication_language" json:"communication_language"`
}

func (q *Queries) ListNotifiableUsersForEvent(ctx context.Context, eventID uuid.UUID) ([]ListNotifiableUsersForEventRow, error) {
	rows, err := q.db.Query(ctx, listNotifiableUsersForEvent, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListNotifiableUsersForEventRow{}
	for rows.Next() {
		var i ListNotifiableUsersForEventRow
		if err := rows.Scan(
			&i.UserID,
			&i.Phone,
			&i.PhoneOtpChannel,
			&i.EnableAppNotifications,
			&i.EnableWhatsappNotifications,
			&i.EnableSmsNotifications,
			&i.EnableEmailNotifications,
			&i.CommunicationLanguage,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listParticipantUserIDsByEventAndStatus = `-- name: ListParticipantUserIDsByEventAndStatus :many

SELECT
    user_id
FROM
    event_registrations
WHERE
    event_id = $1
    AND ($2 = '' OR status = $2::event_registration_status_type)
`

type ListParticipantUserIDsByEventAndStatusParams struct {
	EventID uuid.UUID   `db:"event_id" json:"event_id"`
	Column2 interface{} `db:"column_2" json:"column_2"`
}

// Only users with phone numbers
func (q *Queries) ListParticipantUserIDsByEventAndStatus(ctx context.Context, arg ListParticipantUserIDsByEventAndStatusParams) ([]uuid.UUID, error) {
	rows, err := q.db.Query(ctx, listParticipantUserIDsByEventAndStatus, arg.EventID, arg.Column2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []uuid.UUID{}
	for rows.Next() {
		var user_id uuid.UUID
		if err := rows.Scan(&user_id); err != nil {
			return nil, err
		}
		items = append(items, user_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateEventRegistrationStatusAndDetails = `-- name: UpdateEventRegistrationStatusAndDetails :one

UPDATE event_registrations
SET
    status = $2,
    payment_status = $3,
    waitlist_priority = $4,
    registered_at = $5,
    cancellation_reason_by_user = NULL,
    admin_notes_on_registration = NULL,
    check_in_by_user_id = NULL,
    check_in_method = NULL,
    attended_at = NULL,
    updated_at = NOW()
WHERE id = $1
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method
`

type UpdateEventRegistrationStatusAndDetailsParams struct {
	ID               uuid.UUID                   `db:"id" json:"id"`
	Status           EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus    PaymentStatusType           `db:"payment_status" json:"payment_status"`
	WaitlistPriority *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	RegisteredAt     time.Time                   `db:"registered_at" json:"registered_at"`
}

// Cast $2 to the enum type
func (q *Queries) UpdateEventRegistrationStatusAndDetails(ctx context.Context, arg UpdateEventRegistrationStatusAndDetailsParams) (EventRegistration, error) {
	row := q.db.QueryRow(ctx, updateEventRegistrationStatusAndDetails,
		arg.ID,
		arg.Status,
		arg.PaymentStatus,
		arg.WaitlistPriority,
		arg.RegisteredAt,
	)
	var i EventRegistration
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CheckInByUserID,
		&i.CheckInMethod,
	)
	return i, err
}
