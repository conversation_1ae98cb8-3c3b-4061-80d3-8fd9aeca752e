.event-list-page .eventList-event-card {
    width: 100%;
    border-radius: 10px;
    position: relative;
    transition: transform 0.3s;
    background-color: white;
    margin: 10px 0;
}

.event-list-page .eventList-event-card:hover {
    transform: translateY(-5px);
}

.event-list-page .eventList-image {
    height: 150px;
    object-fit: cover;
    width: 100%;
}


.event-list-page .event-date,
.event-list-page .event-location,
.event-list-page .event-participants,
.event-list-page .event-price {
    color: #666;
    font-size: 14px;
    margin: 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    max-width: 100%;
}

.event-list-page .type-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    background: transparent;
}

.event-list-page .event-cards-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin: -10px;
}

.events-loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    width: 100%;
}

.events-empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    width: 100%;
    background-color: #fafafa;
    border-radius: 8px;
}

@media (max-width: 768px) {
    .events-loading-container,
    .events-empty-container {
        min-height: 200px;
    }
}

.eventList-image-container {
    position: relative;
    width: 100%;
    height: 150px;
    background-color: #f5f5f5;
    overflow: hidden;
}

.eventList-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.eventList-image-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
}

.event-skeleton {
    width: 100% !important;
    height: 100% !important;
}
