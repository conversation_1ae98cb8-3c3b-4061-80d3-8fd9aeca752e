/**
 * Utility functions for handling verification data
 */

/**
 * Groups verification requests by user_id
 * This allows showing a single row per user with all their verification documents expandable
 * 
 * @param {Array} verifications - Array of verification request objects from API
 * @returns {Array} - Grouped verification objects by user_id
 */
export const groupVerificationsByUser = (verifications) => {
  if (!Array.isArray(verifications) || verifications.length === 0) {
    return [];
  }

  // Group by user_id
  const userGroups = {};
  
  verifications.forEach(verification => {
    const userId = verification.user_id;
    
    if (!userId) return;
    
    if (!userGroups[userId]) {
      // Initialize user entry with basic info
      userGroups[userId] = {
        user_id: userId,
        user_display_name: verification.user_display_name,
        user_email: verification.user_email,
        // Use the most recent submission date as the user's submission date
        submitted_at: verification.submitted_at,
        // Store documents in array
        documents: []
      };
    }
    
    // Add this verification document to the user's documents array
    userGroups[userId].documents.push({
      request_id: verification.request_id,
      document_id: verification.document_id,
      document_id_2: verification.document_id_2,
      file_name: verification.file_name,
      verification_type: verification.verification_type,
      status: verification.status,
      submitted_at: verification.submitted_at,
      reviewed_at: verification.reviewed_at,
      reviewer_display_name: verification.reviewer_display_name
    });
    
    // If this verification is more recent, update the user's submission date
    if (new Date(verification.submitted_at) > new Date(userGroups[userId].submitted_at)) {
      userGroups[userId].submitted_at = verification.submitted_at;
    }
  });
  
  // Convert object to array and return
  return Object.values(userGroups);
};

/**
 * Determine the overall status of a user's verification based on all their documents
 * 
 * @param {Array} documents - Array of verification documents
 * @returns {string} - Overall status: approved, rejected, or pending
 */
export const getUserVerificationStatus = (documents) => {
  if (!Array.isArray(documents) || documents.length === 0) {
    return 'pending';
  }
  
  const statuses = documents.map(doc => doc.status);
  
  if (statuses.includes('rejected')) return 'rejected';
  if (statuses.includes('pending')) return 'pending';
  if (statuses.every(status => status === 'approved')) return 'approved';
  
  return 'pending';
}; 