import React, { useState } from 'react';
import { Input, Typography, Tooltip, Drawer, Timeline, Tag, Divider, App } from 'antd';
import {
  CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined,
  PaperClipOutlined, LoadingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { formatSimpleDateTime } from '../utils/dateFormatter';

const { TextArea } = Input;
const { Title, Text } = Typography;

// Helper component for account history
const AccountHistory = ({ accountHistory }) => {
  const { t } = useTranslation();

  const getStatusColor = (status) => {
    if (!status) return 'blue';

    const statusColors = {
      completed: 'green',
      rejected: 'red',
      approved: 'green',
      withdrawn: 'purple'
    };
    return statusColors[status] || 'default';
  };

  return (
    <>
      <Divider />
      <div className="mt-6">
        <Title level={5} className="flex items-center gap-2">
          <PaperClipOutlined />
          <span>{t('volunteerApproval.review.accountHistory')}</span>
        </Title>
        <Timeline
          className="mt-4"
          reverse
          items={accountHistory.map((history) => ({
            color: getStatusColor(history.status),
            children: (
              <>
                <div className="flex items-start gap-3 mb-3">
                  <Text strong className="flex-1 text-gray-900 text-base">{history.action}</Text>
                  {history.status && (
                    <Tag color={getStatusColor(history.status)} className="mt-0.5 !px-3 !py-0.5 leading-5 font-medium">
                      {t(`volunteerApproval.review.status.${history.status}`)}
                    </Tag>
                  )}
                </div>
                <div className="p-4 bg-gray-50 rounded-lg shadow-sm">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span className="whitespace-nowrap">{formatSimpleDateTime(history.timestamp)}</span>
                    {history.reviewer && (
                      <>
                        <span className="w-1 h-1 rounded-full bg-gray-300 shrink-0"></span>
                        <span className="whitespace-nowrap overflow-hidden">{history.reviewer}</span>
                      </>
                    )}
                  </div>
                  {history.comments && (
                    <div className="mt-3 text-sm leading-6 text-gray-800 font-normal">
                      {history.comments}
                    </div>
                  )}
                </div>
              </>
            ),
          }))}
        />
      </div>
    </>
  );
};

const ApprovalSidebar = ({
  status,
  reason,
  setReason,
  actionLoading,
  accountHistory,
  isMobile,
  sidebarVisible,
  setSidebarVisible,
  onStatusChange,
  type = 'verification',
  title,
}) => {
  const { t } = useTranslation();
  const [actionType, setActionType] = useState(null);

  const isPending = status === 'pending';
  const isApproved = status === 'approved';
  const isRejected = status === 'rejected';

  // Action button component
  const ActionButton = ({ onClick, bgColor, hoverColor, icon, text, textColor = "text-white" }) => (
    <button
      onClick={onClick}
      className={`flex-1 h-10 flex items-center justify-center gap-2 ${textColor} ${bgColor} ${hoverColor} rounded-lg transition-colors`}
    >
      {actionLoading ? (
        <LoadingOutlined className="text-lg" />
      ) : (
        icon
      )}
      <span className="font-medium">{text}</span>
    </button>
  );

  const handleConfirm = async () => {
    if (actionType) {
      // Handle withdrawn status - send 'pending' to server but keep 'withdrawn' label for UI
      const statusToSend = actionType === 'withdrawn' ? 'pending' : actionType;
      await onStatusChange(statusToSend);
    }
    setActionType(null);
    setReason('');
  };

  // Render the approval panel content
  const renderApprovalPanel = () => (
    <>
      {!isMobile && (
        <Title level={4} className="mb-6">
          {title || t('common.decision')}
        </Title>
      )}

      <div className="mb-4">
        <div className={`p-4 rounded-lg ${isApproved ? 'bg-emerald-50' :
          isRejected ? 'bg-red-50' :
            'bg-gray-50'
          }`}>
          <div className="flex items-center gap-2">
            {isApproved ? (
              <CheckCircleOutlined className="text-lg text-emerald-500" />
            ) : isRejected ? (
              <CloseCircleOutlined className="text-lg text-red-500" />
            ) : (
              <ClockCircleOutlined className="text-lg text-blue-500" />
            )}
            <Text strong className="text-base">
              {t(`volunteerApproval.review.status.${status}`)}
            </Text>
          </div>
        </div>
      </div>

      {actionType ? (
        <>
          <TextArea
            placeholder={t('volunteerApproval.buttons.writeReason')}
            rows={4}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="mb-4 rounded-lg resize-none"
            disabled={actionLoading}
          />
          <div className="flex gap-3">
            <ActionButton
              onClick={() => setActionType(null)}
              bgColor="bg-gray-100"
              hoverColor="hover:bg-gray-200"
              textColor="text-gray-700"
              icon={<CloseCircleOutlined className="text-lg" />}
              text={t('common.cancel')}
            />
            <ActionButton
              onClick={handleConfirm}
              bgColor={actionType === 'approved' ? 'bg-emerald-500' : 'bg-red-500'}
              hoverColor={actionType === 'approved' ? 'hover:bg-emerald-600' : 'hover:bg-red-600'}
              icon={actionType === 'approved' ? <CheckCircleOutlined className="text-lg" /> : <CloseCircleOutlined className="text-lg" />}
              text={t(`volunteerApproval.buttons.confirm${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`)}
            />
          </div>
        </>
      ) : (
        <div className="flex gap-3">
          {isRejected ? (
            <>
              <ActionButton
                onClick={() => setActionType('withdrawn')}
                bgColor="bg-gray-100"
                hoverColor="hover:bg-gray-200"
                textColor="text-gray-700"
                icon={<CloseCircleOutlined className="text-lg" />}
                text={t('volunteerApproval.buttons.withdraw')}
              />
              <ActionButton
                onClick={() => setActionType('approved')}
                bgColor="bg-emerald-500"
                hoverColor="hover:bg-emerald-600"
                icon={<CheckCircleOutlined className="text-lg" />}
                text={t('volunteerApproval.buttons.approve')}
              />
            </>
          ) : isApproved ? (
            <>
              <ActionButton
                onClick={() => setActionType('withdrawn')}
                bgColor="bg-gray-100"
                hoverColor="hover:bg-gray-200"
                textColor="text-gray-700"
                icon={<CloseCircleOutlined className="text-lg" />}
                text={t('volunteerApproval.buttons.withdraw')}
              />
              <ActionButton
                onClick={() => setActionType('rejected')}
                bgColor="bg-red-500"
                hoverColor="hover:bg-red-600"
                icon={<CloseCircleOutlined className="text-lg" />}
                text={t('volunteerApproval.buttons.reject')}
              />
            </>
          ) : (

            <>
              <ActionButton
                onClick={() => setActionType('rejected')}
                bgColor="bg-red-500"
                hoverColor="hover:bg-red-600"
                icon={<CloseCircleOutlined className="text-lg" />}
                text={t('volunteerApproval.buttons.reject')}
              />
              <ActionButton
                onClick={() => setActionType('approved')}
                bgColor="bg-emerald-500"
                hoverColor="hover:bg-emerald-600"
                icon={<CheckCircleOutlined className="text-lg" />}
                text={t('volunteerApproval.buttons.approve')}
              />
            </>
          )}
        </div>
      )}

      {accountHistory && <AccountHistory accountHistory={accountHistory} />}
    </>
  );

  // For desktop view
  if (!isMobile) {
    return (
      <div className="w-[350px] lg:w-[380px] flex flex-col h-[calc(100vh-72px)] sticky top-[72px] right-0 border-l border-gray-200 bg-white overflow-hidden">
        <div className="p-4 overflow-y-auto flex-1">
          {renderApprovalPanel()}
        </div>
      </div>
    );
  }

  // For mobile view
  return (
    <Drawer
      title={title}
      placement="bottom"
      onClose={() => setSidebarVisible(false)}
      open={sidebarVisible}
      height="80vh"
      styles={{ 
        body: { padding: '16px', overflow: 'auto' },
        header: { borderBottom: '1px solid #f0f0f0', padding: '16px' }
      }}
    >
      {renderApprovalPanel()}
    </Drawer>
  );
};

export default ApprovalSidebar;
