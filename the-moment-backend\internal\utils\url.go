package utils

import (
	"os"
	"path/filepath"
	"strings"

	"github.com/rs/zerolog/log"
)

// ConstructURL builds a full, absolute URL from a series of path parts.
// It reads the base URL from the APP_BASE_URL environment variable.
// If APP_BASE_URL is not set, it returns a root-relative path, which is not ideal but maintains basic functionality.
func ConstructURL(parts ...string) string {
	baseURL := os.Getenv("APP_BASE_URL")
	fullPath := filepath.Join(parts...)
	// Always use forward slashes for URLs
	urlPath := filepath.ToSlash(fullPath)

	if baseURL == "" {
		log.Warn().Str("path", urlPath).Msg("APP_BASE_URL is not set. Returning relative URL.")
		// Return a root-relative URL
		if !strings.HasPrefix(urlPath, "/") {
			return "/" + urlPath
		}
		return urlPath
	}

	// Ensure base URL doesn't have a trailing slash
	baseURL = strings.TrimSuffix(baseURL, "/")

	// Ensure the path part starts with a slash
	if !strings.HasPrefix(urlPath, "/") {
		urlPath = "/" + urlPath
	}

	return baseURL + urlPath
}
