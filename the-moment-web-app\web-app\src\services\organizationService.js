import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

// Helper to map JS-style camelCase params to API-style snake_case for query params
const mapToApiQueryParams = (params, mapping) => {
  const apiParams = {};
  if (params) {
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        const mappedKey = mapping[key] || key;
        apiParams[mappedKey] = params[key];
      }
    }
  }
  return apiParams;
};

const listOrgPostsParamsMapping = {
  tagIds: 'tag_ids',
  organizationId: 'organization_id', // Path param usually makes this redundant in query
};

const listOrgResourcesParamsMapping = {
  organizationId: 'organization_id', // Path param usually makes this redundant in query
  // visibility, limit, offset direct
  searchTerm: 'search_term',
};

const listOrgFilesParamsMapping = {
  parentFolderId: 'parent_folder_id',
  // path, limit, offset direct
};

export const organizationService = {
  // Get all publicly listable organizations
  listOrganizations: async (clientParams = {}) => {
    // api.md GET /organizations - no specific query params listed for general list.
    // Assuming PageRequest { limit, offset } might be supported.
    const apiParams = mapToApiQueryParams(clientParams, {}); // No specific mapping, pass as is
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_ORGANIZATIONS, { params: apiParams });
      return response;
    } catch (error) {
      console.error('Error fetching organizations:', error);
      throw error;
    }
  },

  // Create a new organization (Note: may be admin-restricted in practice)
  createOrganization: async (organizationData) => {
    // organizationData must match CreateOrganizationRequest (e.g., name, description, image_url, theme_color, is_default)
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.CREATE_ORGANIZATION, organizationData);
      return response;
    } catch (error) {
      console.error('Error creating organization:', error);
      throw error;
    }
  },

  // Get details for a specific organization
  getOrganizationDetails: async (orgId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_ORGANIZATION_DETAIL(orgId));
      return response;
    } catch (error) {
      console.error(`Error fetching details for organization ${orgId}:`, error);
      throw error;
    }
  },

  updateOrganization: async (orgId, updateData) => {
    // updateData must match UpdateOrganizationRequest (e.g., name, description, image_url, theme_color, status)
    try {
      const response = await api.put(API_ENDPOINTS.ORGANIZATIONS.UPDATE_ORGANIZATION(orgId), updateData);
      return response;
    } catch (error) {
      console.error(`Error updating organization ${orgId}:`, error);
      throw error;
    }
  },

  deleteOrganization: async (orgId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_ORGANIZATION(orgId));
    } catch (error) {
      console.error(`Error deleting organization ${orgId}:`, error);
      throw error;
    }
  },

  uploadOrganizationLogo: async (orgId, file) => {
    try {
      const formData = new FormData();
      formData.append('logo', file); // Changed 'file' to 'logo' to match backend handler
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.UPLOAD_ORGANIZATION_LOGO(orgId), formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response;
    } catch (error) {
      console.error(`Error uploading logo for organization ${orgId}:`, error);
      throw error;
    }
  },

  // Join an organization
  joinOrganization: async (orgId) => {
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.JOIN_ORGANIZATION(orgId));
      return response;
    } catch (error) {
      console.error(`Error joining organization ${orgId}:`, error);
      throw error;
    }
  },

  // Leave an organization
  leaveOrganization: async (orgId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.LEAVE_ORGANIZATION(orgId));
    } catch (error) {
      console.error(`Error leaving organization ${orgId}:`, error);
      throw error;
    }
  },

  // Apply to be a volunteer for an organization
  applyForVolunteer: async (orgId, motivation) => {
    // API Payload: { motivation: string }
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.APPLY_FOR_VOLUNTEER(orgId), { motivation });
      return response;
    } catch (error) {
      console.error(`Error applying for volunteer role in organization ${orgId}:`, error);
      throw error;
    }
  },

  // Get user's volunteer status for an organization
  getVolunteerStatus: async (orgId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_VOLUNTEER_STATUS(orgId));
      return response;
    } catch (error) {
      console.error(`Error fetching volunteer status for organization ${orgId}:`, error);
      throw error;
    }
  },

  // --- Organization Events ---
  createEvent: async (orgId, eventData) => {
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.CREATE_EVENT(orgId), eventData);
      return response;
    } catch (error) {
      console.error(`Error creating event for organization ${orgId}:`, error);
      throw error;
    }
  },

  listOrgEvents: async (orgId, params = {}) => {
    // params: PageRequest { limit?, offset? }
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_ORG_EVENTS(orgId), { params });
      return response;
    } catch (error) {
      console.error(`Error listing events for organization ${orgId}:`, error);
      throw error;
    }
  },

  getOrgEventDetail: async (orgId, eventId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_ORG_EVENT_DETAIL(orgId, eventId));
      return response;
    } catch (error) {
      console.error(`Error fetching event ${eventId} for organization ${orgId}:`, error);
      throw error;
    }
  },

  uploadEventMedia: async (orgId, eventId, file, isBanner = false) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('is_banner', isBanner ? 'true' : 'false');
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.UPLOAD_EVENT_MEDIA(orgId, eventId), formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response;
    } catch (error) {
      console.error(`Error uploading media for event ${eventId} in organization ${orgId}:`, error);
      throw error;
    }
  },

  listEventMedia: async (orgId, eventId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_EVENT_MEDIA(orgId, eventId));
      return response;
    } catch (error) {
      console.error(`Error listing media for event ${eventId} in organization ${orgId}:`, error);
      throw error;
    }
  },
  
  deleteEventMedia: async (orgId, eventId, mediaId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_EVENT_MEDIA(orgId, eventId, mediaId));
      console.log(`Successfully deleted media ID ${mediaId} from event ${eventId} in org ${orgId}`);
    } catch (error) {
      console.error(`Error deleting media ${mediaId} for event ${eventId} in organization ${orgId}:`, error);
      throw error;
    }
  },

  addEventTag: async (orgId, eventId, tagId) => {
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.ADD_EVENT_TAG(orgId, eventId, tagId));
      return response;
    } catch (error) {
      console.error(`Error adding tag ${tagId} to event ${eventId} in organization ${orgId}:`, error);
      throw error;
    }
  },

  listEventTagsForEvent: async (orgId, eventId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_EVENT_TAGS_FOR_EVENT(orgId, eventId));
      return response;
    } catch (error) {
      console.error(`Error listing tags for event ${eventId} in organization ${orgId}:`, error);
      throw error;
    }
  },

  addEventVerificationType: async (orgId, eventId, typeKey) => {
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.ADD_EVENT_VERIFICATION_TYPE(orgId, eventId, typeKey));
      return response;
    } catch (error) {
      console.error(`Error adding verification type ${typeKey} to event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },

  listEventVerificationTypes: async (orgId, eventId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_EVENT_VERIFICATION_TYPES(orgId, eventId));
      return response;
    } catch (error) {
      console.error(`Error listing verification types for event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },

  // --- Organization Posts ---
  createPost: async (orgId, postData) => {
    // postData must match CreatePostRequest (e.g., title, content, status, slug, published_at)
    try {
      console.log('检查创建updatePayload', postData);
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.CREATE_POST(orgId), postData);
      return response;
    } catch (error) {
      console.error(`Error creating post for organization ${orgId}:`, error);
      throw error;
    }
  },
  
  updatePost: async (orgId, postId, postData) => {
    // postData must match UpdatePostRequest
    try {
      console.log('检查编辑updatePayload', postData);
      const response = await api.put(API_ENDPOINTS.ORGANIZATIONS.UPDATE_POST(orgId, postId), postData);
      return response;
    } catch (error) {
      console.error(`Error updating post ${postId} in org ${orgId}:`, error);
      throw error;
    }
  },

  deletePost: async (orgId, postId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_POST(orgId, postId));
    } catch (error) {
      console.error(`Error deleting post ${postId} in org ${orgId}:`, error);
      throw error;
    }
  },

  listOrgPosts: async (orgId, clientParams = {}) => {
    // clientParams: { organization_id?, limit?, offset?, tagIds? }
    // API expects query: { organization_id?, limit?, offset?, tag_ids? }
    const apiParams = mapToApiQueryParams(clientParams, listOrgPostsParamsMapping);
    // If orgId from path is used, apiParams.organization_id might be removed or ignored by backend.
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_ORG_POSTS(orgId), { params: apiParams });
      return response;
    } catch (error) {
      console.error(`Error listing posts for organization ${orgId}:`, error);
      throw error;
    }
  },

  getOrgPostDetail: async (orgId, postId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_ORG_POST_DETAIL(orgId, postId));
      return response;
    } catch (error) {
      console.error(`Error fetching post ${postId} for organization ${orgId}:`, error);
      throw error;
    }
  },

  uploadPostMedia: async (orgId, postId, file, isBanner = false) => {
    // API: POST /organizations/:orgId/posts/:postId/media - Payload: UploadPostMediaRequest { File `form:"file"` }
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('is_banner', isBanner ? 'true' : 'false');
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.UPLOAD_POST_MEDIA(orgId, postId), formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response;
    } catch (error) {
      console.error(`Error uploading media for post ${postId} in organization ${orgId}:`, error);
      throw error;
    }
  },
  
  deletePostMedia: async (orgId, postId, mediaId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_POST_MEDIA(orgId, postId, mediaId));
    } catch (error) {
      console.error(`Error deleting media ${mediaId} for post ${postId} in org ${orgId}:`, error);
      throw error;
    }
  },

  addTagToPost: async (orgId, postId, tagId) => {
    // API: POST /organizations/:orgId/posts/:postId/tags/:tagId
    try {
      // No request body specified in api.md for this endpoint
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.ADD_TAG_TO_POST(orgId, postId, tagId));
      return response;
    } catch (error) {
      console.error(`Error adding tag ${tagId} to post ${postId} in organization ${orgId}:`, error);
      throw error;
    }
  },
  
  deleteTagFromPost: async (orgId, postId, tagId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_TAG_FROM_POST(orgId, postId, tagId));
    } catch (error) {
      console.error(`Error deleting tag ${tagId} from post ${postId} in org ${orgId}:`, error);
      throw error;
    }
  },

  listTagsForPost: async (orgId, postId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_TAGS_FOR_POST(orgId, postId));
      return response;
    } catch (error) {
      console.error(`Error listing tags for post ${postId} in organization ${orgId}:`, error);
      throw error;
    }
  },

  // --- Organization Resources ---
  createResource: async (orgId, resourceData) => {
    // resourceData must match CreateResourceRequest (e.g., title, visibility, status, published_at)
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.CREATE_RESOURCE(orgId), resourceData);
      return response;
    } catch (error) {
      console.error(`Error creating resource for organization ${orgId}:`, error);
      throw error;
    }
  },
  
  updateResource: async (orgId, resourceId, resourceData) => {
    // resourceData must match UpdateResourceRequest
    try {
      const response = await api.put(API_ENDPOINTS.ORGANIZATIONS.UPDATE_RESOURCE(orgId, resourceId), resourceData);
      return response;
    } catch (error) {
      console.error(`Error updating resource ${resourceId} in org ${orgId}:`, error);
      throw error;
    }
  },

  deleteResource: async (orgId, resourceId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_RESOURCE(orgId, resourceId));
    } catch (error) {
      console.error(`Error deleting resource ${resourceId} in org ${orgId}:`, error);
      throw error;
    }
  },

  listOrgResources: async (orgId, clientParams = {}) => {
    // clientParams: { organization_id?, visibility?, limit?, offset?, searchTerm? }
    // API expects query: { organization_id?, visibility?, limit?, offset?, search_term? }
    const apiParams = mapToApiQueryParams(clientParams, listOrgResourcesParamsMapping);
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_ORG_RESOURCES(orgId), { params: apiParams });
      return response;
    } catch (error) {
      console.error(`Error listing resources for organization ${orgId}:`, error);
      throw error;
    }
  },

  getOrgResourceDetail: async (orgId, resourceId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_ORG_RESOURCE_DETAIL(orgId, resourceId));
      return response;
    } catch (error) {
      console.error(`Error fetching resource ${resourceId} for organization ${orgId}:`, error);
      throw error;
    }
  },

  uploadResourceFile: async (orgId, resourceId, file) => {
    // API: POST /organizations/:orgId/resources/:resourceId/files - Payload: UploadResourceFileRequest { File `form:"file"` }
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.UPLOAD_RESOURCE_FILE(orgId, resourceId), formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response;
    } catch (error) {
      console.error(`Error uploading file for resource ${resourceId} in organization ${orgId}:`, error);
      throw error;
    }
  },
  
  deleteResourceFile: async (orgId, resourceId, fileId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_RESOURCE_FILE(orgId, resourceId, fileId));
    } catch (error) {
      console.error(`Error deleting file ${fileId} for resource ${resourceId} in org ${orgId}:`, error);
      throw error;
    }
  },

  getPublicOrgResourceDetail: async (orgId, resourceIdOrSlug) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_PUBLIC_ORG_RESOURCE_DETAIL(orgId, resourceIdOrSlug));
      return response;
    } catch (error) {
      console.error(`Error fetching public resource ${resourceIdOrSlug} for org ${orgId}:`, error);
      throw error;
    }
  },
  
  // --- Organization Files (General Cloud Storage) ---
  createOrganizationFolder: async (orgId, folderName, parentFolderId = null) => {
    // API: POST /organizations/:orgId/files/folder - Form data: { folder_name, parent_folder_id? }
    try {
      const formData = new FormData();
      formData.append('folder_name', folderName);
      if (parentFolderId) {
        formData.append('parent_folder_id', parentFolderId);
      }
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.CREATE_ORGANIZATION_FOLDER(orgId), formData);
      // Note: api.md implies form data for CreateOrganizationFolderRequest, so Content-Type multipart/form-data might be needed
      // but usually simple form data like this is sent as application/x-www-form-urlencoded by default if not files.
      // If backend strictly expects multipart, add headers. For now, assume default handling is fine.
      return response;
    } catch (error) {
      console.error(`Error creating folder in organization ${orgId}:`, error);
      throw error;
    }
  },
  
  uploadOrganizationFile: async (orgId, file, parentFolderId = null) => {
    // API: POST /organizations/:orgId/files/upload - Form data: { file, parent_folder_id? }
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (parentFolderId) {
        formData.append('parent_folder_id', parentFolderId);
      }
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.UPLOAD_ORGANIZATION_FILE(orgId), formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response;
    } catch (error) {
      console.error(`Error uploading file to organization ${orgId}:`, error);
      throw error;
    }
  },
  
  listOrganizationFiles: async (orgId, clientParams = {}) => {
    // clientParams: { parentFolderId?, path?, limit?, offset? }
    // API expects query: { parent_folder_id?, path?, limit?, offset? }
    const apiParams = mapToApiQueryParams(clientParams, listOrgFilesParamsMapping);
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_ORGANIZATION_FILES(orgId), { params: apiParams });
      return response;
    } catch (error) {
      console.error(`Error listing files for organization ${orgId}:`, error);
      throw error;
    }
  },
  
  updateOrganizationFileOrFolder: async (orgId, fileOrFolderId, updateData) => {
    // API: PUT /organizations/:orgId/files/:fileOrFolderId - JSON Payload: { new_name?, parent_folder_id? }
    // updateData: { newName?, parentFolderId? } -> map to { new_name, parent_folder_id }
    const payload = {
        ...(updateData.newName && { new_name: updateData.newName }),
        ...(updateData.parentFolderId && { parent_folder_id: updateData.parentFolderId }),
    };
    try {
      const response = await api.put(API_ENDPOINTS.ORGANIZATIONS.UPDATE_ORGANIZATION_FILE_OR_FOLDER(orgId, fileOrFolderId), payload);
      return response;
    } catch (error) {
      console.error(`Error updating file/folder ${fileOrFolderId} in organization ${orgId}:`, error);
      throw error;
    }
  },
  
  deleteOrganizationFileOrFolder: async (orgId, fileOrFolderId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_ORGANIZATION_FILE_OR_FOLDER(orgId, fileOrFolderId));
    } catch (error) {
      console.error(`Error deleting file/folder ${fileOrFolderId} in organization ${orgId}:`, error);
      throw error;
    }
  },
  
  downloadOrganizationFile: async (orgId, fileId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.DOWNLOAD_ORGANIZATION_FILE(orgId, fileId), {
        responseType: 'blob', // Important for file downloads
      });
      return response;
    } catch (error) {
      console.error(`Error downloading file ${fileId} for organization ${orgId}:`, error);
      throw error;
    }
  },

  /**
   * List event registrations for an organization, with optional filters.
   * @param {string} orgId - The ID of the organization.
   * @param {object} [clientParams] - Optional parameters for filtering and pagination.
   * @param {string} [clientParams.event_id] - Filter by event ID.
   * @param {string} [clientParams.user_id] - Filter by user ID.
   * @param {string} [clientParams.status] - Filter by registration status.
   * @param {number} [clientParams.limit] - Pagination limit.
   * @param {number} [clientParams.offset] - Pagination offset.
   * @returns {Promise<{data: EventRegistrationResponse[], totalCount: number}>} - Promise resolving to registrations and total count.
   */
  listOrganizationEventRegistrations: async (orgId, clientParams = {}) => {
    // API Endpoint: GET /organizations/{orgId}/event-registrations
    // Query Params: event_id, user_id, status, limit, offset (ensure these are snake_case if API expects that)
    // The API_ENDPOINTS.EVENT_REGISTRATIONS.LIST_ORGANIZATION_EVENT_REGISTRATIONS(orgId) gives the base path.
    // We need to pass clientParams as query parameters.
    // Assuming the API expects snake_case for query params, we might need a mapping or ensure clientParams uses snake_case.
    // For now, passing them directly as they are common enough names.
    try {
      const response = await api.get(API_ENDPOINTS.EVENT_REGISTRATIONS.LIST_ORGANIZATION_EVENT_REGISTRATIONS(orgId), { params: clientParams });
      // The plan expects {data: EventRegistrationResponse[], totalCount: number}
      // This might require adapting the response if the API returns a different structure (e.g., just an array, or { items: [], total: N })
      // For now, assume response is { data: [], totalCount: X } or can be transformed.
      // If it's just an array: return { data: response, totalCount: response.length }; (example)
      return response; 
    } catch (error) {
      console.error(`Error fetching event registrations for organization ${orgId}:`, error);
      throw error;
    }
  },

  /**
   * Get pending volunteer applications count for an organization
   * @param {string} orgId - The ID of the organization.
   * @returns {Promise<number>} - Promise resolving to pending volunteer applications count.
   */
  getPendingVolunteerApplicationsCount: async (orgId) => {
    try {
      // Use the admin endpoint to get pending volunteer applications with full response to get headers
      const response = await api.getWithFullResponse(API_ENDPOINTS.ADMIN.LIST_ORG_VOLUNTEER_APPLICATIONS(orgId), { 
        params: { 
          status: 'pending',
          limit: 1 // We only need the count, not the actual data
        } 
      });
      
      // Extract count from response headers or data
      const totalFromHeader = response.headers['x-total-count'] || response.headers['X-Total-Count'];
      if (totalFromHeader) {
        return parseInt(totalFromHeader, 10);
      }
      
      // Fallback to response data structure
      const responseData = response.data;
      if (responseData && typeof responseData.total === 'number') {
        return responseData.total;
      } else if (responseData && Array.isArray(responseData.data)) {
        // If data has pagination structure
        return responseData.data.length;
      } else if (Array.isArray(responseData)) {
        return responseData.length;
      }
      
      return 0;
    } catch (error) {
      console.error(`Error fetching pending volunteer applications count for organization ${orgId}:`, error);
      // Return 0 on error to prevent UI issues
      return 0;
    }
  },

  /**
   * Get pending verifications count (only for super org)
   * @returns {Promise<number>} - Promise resolving to pending verifications count.
   */
  getPendingVerificationsCount: async () => {
    try {
      // Use the admin endpoint to get pending verifications with full response to get headers
      const response = await api.getWithFullResponse(API_ENDPOINTS.ADMIN.LIST_ALL_VERIFICATIONS, { 
        params: { 
          status: 'pending',
          limit: 1 // We only need the count, not the actual data
        } 
      });
      
      // Extract count from response headers or data
      const totalFromHeader = response.headers['x-total-count'] || response.headers['X-Total-Count'];
      if (totalFromHeader) {
        return parseInt(totalFromHeader, 10);
      }
      
      // Fallback to response data structure
      const responseData = response.data;
      if (responseData && typeof responseData.total === 'number') {
        return responseData.total;
      } else if (responseData && responseData.pagination && typeof responseData.pagination.total_items === 'number') {
        return responseData.pagination.total_items;
      } else if (responseData && Array.isArray(responseData.data)) {
        // If data has pagination structure
        return responseData.data.length;
      } else if (Array.isArray(responseData)) {
        return responseData.length;
      }
      
      return 0;
    } catch (error) {
      console.error('Error fetching pending verifications count:', error);
      // Return 0 on error to prevent UI issues
      return 0;
    }
  }
};