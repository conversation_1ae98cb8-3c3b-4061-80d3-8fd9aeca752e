-- Revert image_url and theme_color for organizations updated in 000021_update_seeded_orgs_with_images_themes.up.sql

DO $$
DECLARE
    org_tongxing_id uuid;
    org_shatinsi_id uuid;
    org_blue_id uuid;
    org_pink_id uuid;
BEGIN
    SELECT id INTO org_tongxing_id FROM organizations WHERE name = '同行明天';
    SELECT id INTO org_shatinsi_id FROM organizations WHERE name = '沙田西關愛隊';
    SELECT id INTO org_blue_id FROM organizations WHERE name = '藍色主題組織';
    SELECT id INTO org_pink_id FROM organizations WHERE name = '玫紅色主題組織';

    IF org_tongxing_id IS NOT NULL THEN
        UPDATE organizations
        SET image_url = NULL,
            theme_color = NULL
        WHERE id = org_tongxing_id;
    END IF;

    IF org_shatinsi_id IS NOT NULL THEN
        UPDATE organizations
        SET image_url = NULL,
            theme_color = NULL
        WHERE id = org_shatinsi_id;
    END IF;

    IF org_blue_id IS NOT NULL THEN
        UPDATE organizations
        SET image_url = NULL,
            theme_color = NULL
        WHERE id = org_blue_id;
    END IF;

    IF org_pink_id IS NOT NULL THEN
        UPDATE organizations
        SET image_url = NULL,
            theme_color = NULL
        WHERE id = org_pink_id;
    END IF;

END $$; 