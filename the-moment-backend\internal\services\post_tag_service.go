package services

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"context"

	// "database/sql" // No longer needed here as sqlc uses *string for nullable TEXT

	"github.com/google/uuid"
)

type PostTagService struct {
	DB db.Querier
}

func NewPostTagService(db db.Querier) *PostTagService {
	return &PostTagService{DB: db}
}

func (s *PostTagService) CreatePostTag(ctx context.Context, req payloads.CreatePostTagRequest) (*payloads.PostTagResponse, error) {
	params := db.CreatePostTagParams{
		NameEn:          req.NameEn,
		NameZhHk:        req.NameZhHk,
		NameZhCn:        req.NameZhCn,
		DescriptionEn:   req.DescriptionEn,
		DescriptionZhHk: req.DescriptionZhHk,
		DescriptionZhCn: req.DescriptionZhCn,
	}

	tag, err := s.DB.CreatePostTag(ctx, params)
	if err != nil {
		return nil, err
	}
	return toPostTagResponse(&tag), nil
}

func (s *PostTagService) GetPostTag(ctx context.Context, id uuid.UUID) (*payloads.PostTagResponse, error) {
	tag, err := s.DB.GetPostTagByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return toPostTagResponse(&tag), nil
}

func (s *PostTagService) ListPostTags(ctx context.Context) ([]payloads.PostTagResponse, error) {
	tags, err := s.DB.ListPostTags(ctx)
	if err != nil {
		return nil, err
	}
	resp := make([]payloads.PostTagResponse, len(tags))
	for i, tag := range tags {
		resp[i] = *toPostTagResponse(&tag)
	}
	return resp, nil
}

func (s *PostTagService) UpdatePostTag(ctx context.Context, id uuid.UUID, req payloads.UpdatePostTagRequest) (*payloads.PostTagResponse, error) {
	tag, err := s.DB.GetPostTagByID(ctx, id)
	if err != nil {
		return nil, err
	}

	params := db.UpdatePostTagParams{
		ID:              id,
		NameEn:          tag.NameEn,
		NameZhHk:        tag.NameZhHk,
		NameZhCn:        tag.NameZhCn,
		DescriptionEn:   tag.DescriptionEn,
		DescriptionZhHk: tag.DescriptionZhHk,
		DescriptionZhCn: tag.DescriptionZhCn,
	}

	if req.NameEn != nil {
		params.NameEn = *req.NameEn
	}
	if req.NameZhHk != nil {
		params.NameZhHk = *req.NameZhHk
	}
	if req.NameZhCn != nil {
		params.NameZhCn = *req.NameZhCn
	}
	if req.DescriptionEn != nil {
		params.DescriptionEn = req.DescriptionEn
	}
	if req.DescriptionZhHk != nil {
		params.DescriptionZhHk = req.DescriptionZhHk
	}
	if req.DescriptionZhCn != nil {
		params.DescriptionZhCn = req.DescriptionZhCn
	}

	updated, err := s.DB.UpdatePostTag(ctx, params)
	if err != nil {
		return nil, err
	}
	return toPostTagResponse(&updated), nil
}

func (s *PostTagService) DeletePostTag(ctx context.Context, id uuid.UUID) error {
	return s.DB.DeletePostTag(ctx, id)
}

func (s *PostTagService) AddTagToPost(ctx context.Context, postID, tagID uuid.UUID) error {
	return s.DB.AddTagToPost(ctx, db.AddTagToPostParams{
		PostID: postID,
		TagID:  tagID,
	})
}

func (s *PostTagService) RemoveTagFromPost(ctx context.Context, postID, tagID uuid.UUID) error {
	return s.DB.RemoveTagFromPost(ctx, db.RemoveTagFromPostParams{
		PostID: postID,
		TagID:  tagID,
	})
}

func (s *PostTagService) GetTagsForPost(ctx context.Context, postID uuid.UUID) ([]payloads.PostTagResponse, error) {
	tags, err := s.DB.GetTagsForPost(ctx, postID)
	if err != nil {
		return nil, err
	}
	resp := make([]payloads.PostTagResponse, len(tags))
	for i, tag := range tags {
		resp[i] = *toPostTagResponse(&tag)
	}
	return resp, nil
}

func toPostTagResponse(tag *db.PostTag) *payloads.PostTagResponse {
	resp := &payloads.PostTagResponse{
		ID:              tag.ID,
		NameEn:          tag.NameEn,
		NameZhHk:        tag.NameZhHk,
		NameZhCn:        tag.NameZhCn,
		DescriptionEn:   tag.DescriptionEn,
		DescriptionZhHk: tag.DescriptionZhHk,
		DescriptionZhCn: tag.DescriptionZhCn,
	}
	return resp
}
