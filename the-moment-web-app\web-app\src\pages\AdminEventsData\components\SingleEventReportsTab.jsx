// 'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
    Card,
    DonutChart,
    List,
    ListItem,
    LineChart,
    Grid,
    Metric,
    Text,
    ProgressBar,
    ProgressCircle
} from '@tremor/react';
import { Tabs, App, Spin, Alert } from 'antd';
import { eventService, eventStatisticsService } from '../../../services/eventService';
import { useTranslation } from 'react-i18next';
import { LoadingOutlined } from '@ant-design/icons';
import ErrorPage from '../../ErrorPage';

function classNames(...classes) {
    return classes.filter(Boolean).join(' ');
}

function SingleEventReportStatDashboard({ eventId, participantData, statistics, dataByAgeGroup, dataByGender, trendData }) {
    console.log('SingleEventReportStatDashboard props:', { eventId, participantData, statistics, dataByAgeGroup, dataByGender, trendData });
    const { t } = useTranslation();
    const { message } = App.useApp();

    // States for data
    const [loading, setLoading] = useState(false);
    
    // If props are provided, use them, otherwise initialize with empty values
    const [dataByAgeGroupState, setDataByAgeGroup] = useState(dataByAgeGroup || []);
    const [dataByGenderState, setDataByGender] = useState(dataByGender || []);
    const [trendDataState, setTrendData] = useState(trendData || []);

    // Use provided statistics or initialize with defaults
    const [statsData, setStatsData] = useState(statistics || {
        participants: { total: 0 },
        volunteers: { total: 0 },
        registration: { current: 0, maximum: 0, percentage: 0 }
    });

    // Update local state when props change
    useEffect(() => {
        if (statistics) {
            setStatsData(statistics);
        }
        if (dataByAgeGroup) {
            setDataByAgeGroup(dataByAgeGroup);
        }
        if (dataByGender) {
            setDataByGender(dataByGender);
        }
        if (trendData) {
            setTrendData(trendData);
        }
    }, [participantData, statistics, dataByAgeGroup, dataByGender, trendData]);

    // Create kpiData from statistics with percentage calculated in frontend
    const existingKpiData = useMemo(() => [
        {
            name: t('adminEvents.singleEventReport.statistics.participants'),
            stat: statsData?.participants?.total || 0,
            color: 'bg-blue-500',
            type: 'basic'
        },
        {
            name: t('adminEvents.singleEventReport.statistics.volunteers'),
            stat: statsData?.volunteers?.total || 0,
            color: 'bg-pink-500',
            type: 'basic'
        },
        {
            name: t('adminEvents.singleEventReport.statistics.registrationRate'),
            stat: statsData?.registration?.current || 0,
            limit: statsData?.registration?.maximum || 0,
            // Calculate percentage in frontend with safeguards
            percentage: statsData?.registration && statsData?.registration?.maximum > 0 ?
                       Math.round((statsData?.registration?.current / statsData?.registration?.maximum) * 100) : 0,
            type: 'progress'
        }
    ], [statsData, t]);

    const allKpiData = useMemo(() => existingKpiData, [existingKpiData]);

    const participantFormatter = (number) => {
        return Intl.NumberFormat('en').format(number || 0).toString();
    };

    const CustomLegend = () => (
        <div className="flex justify-end gap-x-8 mt-2">
            {[
                {
                    key: 'Participants',
                    label: t('adminEvents.singleEventReport.statistics.participants'),
                    color: 'rgb(59, 130, 246)'
                },
                {
                    key: 'Volunteers',
                    label: t('adminEvents.singleEventReport.statistics.volunteers'),
                    color: 'rgb(236, 72, 153)'
                }
            ].map(({ key, label, color }) => (
                <div key={key} className="flex items-center gap-x-2">
                    <div className="h-2 w-2 rounded-full" style={{ backgroundColor: color }} />
                    <span className="text-tremor-default text-tremor-content">{label}</span>
                </div>
            ))}
        </div>
    );

    const customTooltip = (props) => {
        const { payload, active, label } = props;

        if (!active || !payload) return null;

        const total = payload[0]?.payload ?
            (payload[0].payload.Participants || 0) + (payload[0].payload.Volunteers || 0) : 0;

        return (
            <div className="w-56 rounded-tremor-default border border-tremor-border bg-tremor-background text-tremor-default shadow-tremor-dropdown">
                <div className="border-b border-tremor-border px-3 py-2">
                    <p className="font-medium text-tremor-content-strong">
                        {label}
                    </p>
                </div>
                <div className="space-y-2 px-3 py-2">
                    {payload.map((category, idx) => (
                        <div key={idx} className="flex space-x-2.5">
                            <span
                                className={`w-1 bg-${category.color}-500 shrink-0 rounded`}
                                aria-hidden={true}
                            />
                            <p className="flex w-full items-center justify-between space-x-8 truncate">
                                <span className="truncate font-medium text-tremor-content-strong">
                                    {t(`adminEvents.singleEventReport.statistics.${category.dataKey.toLowerCase()}`)}
                                </span>
                                <span className="font-bold text-tremor-content-strong">
                                    {category.value || 0}
                                </span>
                            </p>
                        </div>
                    ))}
                    <div className="border-t border-tremor-border pt-2">
                        <div className="flex space-x-2.5">
                            <span className="w-1 shrink-0 rounded bg-gray-400" aria-hidden={true} />
                            <p className="flex w-full items-center justify-between space-x-8 truncate">
                                <span className="truncate font-medium text-tremor-content-strong">
                                    {t('adminEvents.report.charts.total')}
                                </span>
                                <span className="font-bold text-tremor-content-strong">
                                    {total}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderTabContent = (data) => (
        <>
            <DonutChart
                className="mt-8"
                data={data || []}
                category="amount"
                index="name"
                valueFormatter={participantFormatter}
                showTooltip={false}
                colors={data === dataByAgeGroupState ?
                    ['indigo-400', 'indigo-500', 'violet-500', 'purple-500', 'gray-400'] :
                    ['blue', 'red', 'gray-400']}
            />
            <p className="mt-8 flex items-center justify-between text-tremor-label text-tremor-content">
                <span>
                    {data === dataByAgeGroupState ?
                        t('adminEvents.singleEventReport.charts.demographics.ageGroups.title') :
                        t('adminEvents.singleEventReport.charts.demographics.gender.title')}
                </span>
                <span>{t('adminEvents.singleEventReport.charts.demographics.amount')} / {t('adminEvents.singleEventReport.charts.demographics.share')}</span>
            </p>
            <List className="mt-2">
                {(data || []).map((item) => (
                    <ListItem key={item.name || 'unknown'} className="space-x-4 truncate">
                        <div
                            className={classNames(
                                item.borderColor || 'border-indigo-400',
                                'flex h-8 items-center truncate border-l-[2.5px] pl-4',
                            )}
                        >
                            <span className="truncate">
                                {data === dataByAgeGroupState 
                                    ? t(`adminEvents.singleEventReport.charts.demographics.ageGroups.${item.name}`)
                                    : t(`adminEvents.singleEventReport.charts.demographics.gender.${item.name}`)}
                            </span>
                        </div>
                        <span className="font-medium tabular-nums text-tremor-content-strong">
                            {participantFormatter(item.amount)}{' '}
                            <span className="font-normal">({item.share || '0%'})</span>
                        </span>
                    </ListItem>
                ))}
            </List>
        </>
    );

    const renderKpiCard = (item) => {
        if (item.type === 'basic') {
            return (
                <Card key={item.name}>
                    <div className="flex space-x-3">
                        <div className={classNames(item.color, 'w-1 shrink-0 rounded')} />
                        <dt className="text-tremor-default text-tremor-content">
                            <span className="truncate">{item.name}</span>
                        </dt>
                    </div>
                    <div className="mt-2 pl-4">
                        <dd className="text-tremor-metric font-semibold text-tremor-content-strong">
                            {item.stat || 0}
                        </dd>
                    </div>
                </Card>
            );
        }

        return (
            <Card key={item.name}>
                <div className="flex items-center justify-center gap-x-5">
                    <ProgressCircle value={item.percentage || 0}>
                        <span className="text-sm font-medium text-gray-900">
                            {item.percentage || 0}%
                        </span>
                    </ProgressCircle>
                    <div>
                        <p className="text-sm font-medium text-gray-900">
                            {item.stat || 0}/{item.limit || 0}
                        </p>
                        <p className="text-sm text-gray-500">
                            {t('adminEvents.singleEventReport.statistics.registrationProgress')}
                        </p>
                    </div>
                </div>
            </Card>
        );
    };

    return (
        <div className="flex flex-col lg:flex-row gap-4">
            {/* Left side - KPIs and Line Chart */}
            <div className="w-full lg:w-2/3 flex flex-col gap-4">
                {/* KPI Cards */}
                <dl className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {allKpiData.map((item) => renderKpiCard(item))}
                </dl>

                {/* Line Chart Card */}
                <Card className="p-4">
                    <div className="flex flex-col space-y-1">
                        <h3 className="text-tremor-default font-medium text-tremor-content-strong">
                            {t('adminEvents.singleEventReport.charts.participationTrends')}
                        </h3>
                        <p className="text-tremor-default text-tremor-content">
                            {t('adminEvents.singleEventReport.charts.dailyParticipants')}
                        </p>
                        <CustomLegend />
                    </div>
                    <div className="mt-4">
                        <LineChart
                            className="h-[300px] lg:h-[350px]"
                            data={trendDataState}
                            index="date"
                            categories={["Participants", "Volunteers"]}
                            colors={["blue", "pink"]}
                            valueFormatter={participantFormatter}
                            yAxisWidth={48}
                            showLegend={false}
                            customTooltip={customTooltip}
                            connectNulls={true}
                            curveType="monotone"
                            showAnimation={true}
                            enableLegendSlider={true}
                            startEndOnly={true}
                            showXAxis={true}
                            showYAxis={true}
                        />
                    </div>
                </Card>
            </div>

            {/* Right side - Demographics */}
            <Card className="w-full lg:w-1/3 p-4">
                <div>
                    <h3 className="text-tremor-default font-medium text-tremor-content-strong">
                        {t('adminEvents.singleEventReport.charts.demographics.title')}
                    </h3>
                    <p className="mt-1 text-tremor-default leading-6 text-tremor-content">
                        {t('adminEvents.singleEventReport.charts.demographics.subtitle')}
                    </p>
                </div>
                <Tabs
                    items={[
                        {
                            key: '1',
                            label: t('adminEvents.singleEventReport.charts.demographics.ageGroups.title'),
                            children: renderTabContent(dataByAgeGroupState),
                        },
                        {
                            key: '2',
                            label: t('adminEvents.singleEventReport.charts.demographics.gender.title'),
                            children: renderTabContent(dataByGenderState),
                        },
                    ]}
                />
            </Card>
        </div>
    );
}

export default SingleEventReportStatDashboard;