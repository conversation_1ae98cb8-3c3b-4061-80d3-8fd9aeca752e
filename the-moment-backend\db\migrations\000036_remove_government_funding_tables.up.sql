-- Drop the join table first due to foreign key constraints
DROP TABLE IF EXISTS event_government_funding_types;

-- Drop the government funding types table
DROP TABLE IF EXISTS government_funding_types;

-- Ensure the old boolean column is present on the events table
ALTER TABLE events DROP COLUMN IF EXISTS is_government_funded; -- Drop first to avoid error if it exists with wrong type/constraints
ALTER TABLE events ADD COLUMN is_government_funded BOOLEAN NOT NULL DEFAULT false;

COMMENT ON COLUMN events.is_government_funded IS 'Indicates if the event received any government funding.'; 