export enum VerificationStatusEnum {
    Pending = 'pending',
    Approved = 'approved',
    Rejected = 'rejected',
    Unverified = 'unverified',
    // Add other statuses as needed
}

export enum VerificationTypeEnum {
    HkIDCard = 'hk_id_card',
    MainlandChinaIDCard = 'mainland_china_id_card',
    MainlandTravelPermit = 'mainland_travel_permit',
    Passport = 'passport',
    HkYouthPlus = 'hk_youth_plus',
    AddressProof = 'address_proof',
    StudentID = 'student_id',
    HomeVisit = 'home_visit',
    // Add other types as needed
} 