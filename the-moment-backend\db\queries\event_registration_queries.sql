-- name: GetEligibleEventRegistrationsForUserCheckIn :many
SELECT
    er.id AS registration_id,
    er.event_id,
    er.user_id,
    er.status AS registration_status,
    e.title AS event_title,
    e.start_time AS event_start_time,
    e.end_time AS event_end_time,
    e.status AS event_status
FROM
    event_registrations er
JOIN
    events e ON er.event_id = e.id
WHERE
    er.user_id = $1
    AND er.status != 'cancelled_by_user' -- Assuming other non-attended statuses are eligible initially
    AND e.status = 'published' -- Use 'published' status based on enum
    AND e.start_time BETWEEN $2 AND $3; -- Time window: now - X minutes AND now + Y minutes

-- name: UpdateEventRegistrationToCheckIn :one
UPDATE event_registrations
SET
    status = 'attended',      -- Referencing db.EventRegistrationStatusTypeAttended
    attended_at = NOW(),
    updated_at = NOW()
WHERE
    id = $1
    AND user_id = $2          -- Ensure we are updating for the correct user as a safeguard
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at;

-- name: ListUserRegistrationsWithFilters :many
SELECT
    er.id,
    er.event_id,
    er.user_id,
    er.status,
    er.payment_status,
    er.registration_role,
    er.registered_at,
    er.attended_at,
    er.cancellation_reason_by_user,
    er.admin_notes_on_registration,
    er.waitlist_priority,
    er.created_at,
    er.updated_at,
    e.title AS event_title,
    e.start_time AS event_start_time,
    e.end_time AS event_end_time,
    -- e.description_content AS event_description_content, -- Consider if full content needed
    e.location_type AS event_location_type,
    e.location_full_address AS event_location_full_address,
    e.location_online_url AS event_location_online_url,
    e.status AS event_status,
    e.organization_id AS event_organization_id,
    (SELECT o.name FROM organizations o WHERE o.id = e.organization_id) AS event_organization_name,
    e.price AS event_price,
    e.contact_email AS event_contact_email,
    e.contact_phone AS event_contact_phone,
    COALESCE(
        (SELECT JSON_AGG(JSON_BUILD_OBJECT(
            'id', emi.id,
            'file_name', emi.file_name,
            'file_path', emi.file_path,
            'file_type', emi.file_type,
            'file_size', emi.file_size,
            'uploaded_at', emi.uploaded_at,
            'is_banner', emi.is_banner
        ))
        FROM event_media_items emi
        WHERE emi.event_id = e.id),
        '[]'::JSON
    ) AS media_items
FROM
    event_registrations er
JOIN
    events e ON er.event_id = e.id
WHERE
    er.user_id = sqlc.arg(user_id)
    AND (
        sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NOT NULL AND e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ AND e.start_time <= sqlc.narg(end_date)::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL AND e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ)
        )
    )
    AND (sqlc.narg(event_filter_status)::event_status_type IS NULL OR e.status = sqlc.narg(event_filter_status)::event_status_type)
    AND (sqlc.narg(role)::event_registration_role_type IS NULL OR er.registration_role = sqlc.narg(role)::event_registration_role_type)
    AND (sqlc.narg(organization_id)::UUID IS NULL OR e.organization_id = sqlc.narg(organization_id)::UUID)
GROUP BY
    er.id, e.id -- Group by event_registrations.id and events.id to ensure correct aggregation context for media_items
ORDER BY
    CASE WHEN sqlc.narg(sort_order)::text = 'date_asc' THEN e.start_time END ASC,
    CASE WHEN sqlc.narg(sort_order)::text = 'date_desc' THEN e.start_time END DESC,
    e.start_time DESC -- Default sort if no sort_order or invalid
LIMIT sqlc.arg(limit_val) OFFSET sqlc.arg(offset_val);

-- name: CountUserRegistrationsWithFilters :one
SELECT COUNT(*)
FROM
    event_registrations er
JOIN
    events e ON er.event_id = e.id
WHERE
    er.user_id = sqlc.arg(user_id)
    AND (
        sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NOT NULL AND e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ AND e.start_time <= sqlc.narg(end_date)::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL AND e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ)
        )
    )
    AND (sqlc.narg(event_filter_status)::event_status_type IS NULL OR e.status = sqlc.narg(event_filter_status)::event_status_type)
    AND (sqlc.narg(role)::event_registration_role_type IS NULL OR er.registration_role = sqlc.narg(role)::event_registration_role_type)
    AND (sqlc.narg(organization_id)::UUID IS NULL OR e.organization_id = sqlc.narg(organization_id)::UUID);

-- name: UpdateEventRegistrationToCheckInByScanner :one
UPDATE event_registrations
SET
    status = 'attended',
    attended_at = NOW(),
    check_in_by_user_id = sqlc.arg(check_in_by_user_id),
    check_in_method = sqlc.arg(check_in_method),
    updated_at = NOW()
WHERE
    id = sqlc.arg(registration_id) -- The ID of the event_registration record for the participant
RETURNING *;

-- name: GetEventRegistrationByUserAndEventAndRole :one
SELECT *
FROM event_registrations
WHERE user_id = $1 AND event_id = $2 AND registration_role = $3
LIMIT 1;

-- name: GetEventRegistrationByID :one
SELECT *
FROM event_registrations
WHERE id = $1
LIMIT 1;

-- name: ListOrganizationEventRegistrationsWithFilters :many
SELECT
    er.*, -- all columns from event_registrations
    e.title AS event_title,
    e.start_time AS event_start_time,
    e.end_time AS event_end_time,
    e.description_content AS event_description, -- Assuming description is stored in description_content
    e.location_type AS event_location_type,
    e.location_full_address AS event_location_full_address,
    e.location_online_url AS event_location_online_url,
    e.status AS event_status,
    e.organization_id AS event_organization_id,
    o.name AS event_organization_name, -- Join with organizations table for name
    e.price AS event_price,
    e.contact_email AS event_contact_email,
    e.contact_phone AS event_contact_phone,
    u.display_name AS user_display_name,
    u.email AS user_email,
    u.phone AS user_phone
FROM event_registrations er
JOIN events e ON er.event_id = e.id
JOIN users u ON er.user_id = u.id
JOIN organizations o ON e.organization_id = o.id
WHERE e.organization_id = sqlc.arg(organization_id)
  AND (sqlc.narg(event_id)::UUID IS NULL OR er.event_id = sqlc.narg(event_id)::UUID)
  AND (sqlc.narg(filter_start_date)::TIMESTAMPTZ IS NULL OR e.start_time >= sqlc.narg(filter_start_date)::TIMESTAMPTZ)
  AND (sqlc.narg(filter_end_date)::TIMESTAMPTZ IS NULL OR e.start_time <= sqlc.narg(filter_end_date)::TIMESTAMPTZ)
  AND (sqlc.narg(filter_registration_status)::TEXT IS NULL OR er.status = sqlc.arg(filter_registration_status)::event_registration_status_type)
  AND (sqlc.narg(filter_participant_user_id)::UUID IS NULL OR er.user_id = sqlc.narg(filter_participant_user_id)::UUID)
  AND (sqlc.narg(filter_registration_role)::TEXT IS NULL OR er.registration_role = sqlc.arg(filter_registration_role)::event_registration_role_type)
  AND (sqlc.narg(filter_payment_status)::TEXT IS NULL OR er.payment_status = sqlc.arg(filter_payment_status)::payment_status_type)
  AND (sqlc.narg(filter_user_name_search)::TEXT IS NULL OR (u.display_name ILIKE '%' || sqlc.narg(filter_user_name_search)::TEXT || '%' OR u.email ILIKE '%' || sqlc.narg(filter_user_name_search)::TEXT || '%'))
ORDER BY e.start_time DESC, er.registered_at DESC -- Or other sensible default sort
LIMIT sqlc.arg(lim)
OFFSET sqlc.arg(offs);

-- name: CountOrganizationEventRegistrationsWithFilters :one
SELECT COUNT(*)
FROM event_registrations er
JOIN events e ON er.event_id = e.id
JOIN users u ON er.user_id = u.id
JOIN organizations o ON e.organization_id = o.id
WHERE e.organization_id = sqlc.arg(organization_id)
  AND (sqlc.narg(event_id)::UUID IS NULL OR er.event_id = sqlc.narg(event_id)::UUID)
  AND (sqlc.narg(filter_start_date)::TIMESTAMPTZ IS NULL OR e.start_time >= sqlc.narg(filter_start_date)::TIMESTAMPTZ)
  AND (sqlc.narg(filter_end_date)::TIMESTAMPTZ IS NULL OR e.start_time <= sqlc.narg(filter_end_date)::TIMESTAMPTZ)
  AND (sqlc.narg(filter_registration_status)::TEXT IS NULL OR er.status = sqlc.arg(filter_registration_status)::event_registration_status_type)
  AND (sqlc.narg(filter_participant_user_id)::UUID IS NULL OR er.user_id = sqlc.narg(filter_participant_user_id)::UUID)
  AND (sqlc.narg(filter_registration_role)::TEXT IS NULL OR er.registration_role = sqlc.arg(filter_registration_role)::event_registration_role_type)
  AND (sqlc.narg(filter_payment_status)::TEXT IS NULL OR er.payment_status = sqlc.arg(filter_payment_status)::payment_status_type)
  AND (sqlc.narg(filter_user_name_search)::TEXT IS NULL OR (u.display_name ILIKE '%' || sqlc.narg(filter_user_name_search)::TEXT || '%' OR u.email ILIKE '%' || sqlc.narg(filter_user_name_search)::TEXT || '%'));

-- name: UpdateEventRegistrationStatusByAdmin :one
UPDATE event_registrations
SET
    status = sqlc.arg(new_status)::event_registration_status_type,
    admin_notes_on_registration = sqlc.arg(admin_notes),
    updated_at = sqlc.arg(updated_at)
WHERE id = sqlc.arg(registration_id)
RETURNING *;

-- name: GetEligibleEventRegistrationsForParticipantCheckIn :many
SELECT
    er.id AS registration_id,
    er.event_id,
    e.organization_id,
    er.user_id,
    er.status AS registration_status,
    e.title AS event_title,
    e.start_time AS event_start_time,
    e.end_time AS event_end_time,
    e.status AS event_status
FROM
    event_registrations er
JOIN
    events e ON er.event_id = e.id
WHERE
    er.user_id = @participant_user_id
    AND er.status = 'registered'
    AND e.status = 'published'
    AND e.start_time >= @earliest_check_in_time -- Parameter will be current_time - check_in_window_past
    AND e.start_time <= @latest_check_in_time   -- Parameter will be current_time + check_in_window_future
    -- Consider event end_time as well if check-in is allowed throughout the event. This might be better handled by latest_check_in_time calculation logic.
    -- e.g., latest_check_in_time = MIN(current_time + check_in_window_future, event_end_time)
ORDER BY
    e.start_time ASC;
 