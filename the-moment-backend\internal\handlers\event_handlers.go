package handlers

import (
	// Added for claims helpers
	"Membership-SAAS-System-Backend/internal/authn"    // Added for role constants
	"Membership-SAAS-System-Backend/internal/payloads" // Payloads for requests/responses
	"Membership-SAAS-System-Backend/internal/services" // The business logic service

	// For JWT claims
	utils "Membership-SAAS-System-Backend/internal/utils" // For validator

	"errors"
	"fmt"

	// Added for errors.New
	// For file copy
	"net/http" // Standard HTTP status codes
	// For file operations
	// For file extensions
	"strconv" // For parsing int from string
	"time"

	"strings"

	// Added for jwt.Token
	"github.com/google/uuid" // For UUIDs
	"github.com/jackc/pgx/v5"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log" // Or your application's logger
)

// EventHandler handles HTTP requests related to events.
type EventHandler struct {
	service    services.EventService
	orgService *services.OrganizationService // Changed to pointer
	validator  *utils.RequestValidator
}

// NewEventHandler creates a new EventHandler instance.
func NewEventHandler(svc services.EventService, orgSvc *services.OrganizationService, val *utils.RequestValidator) *EventHandler { // Changed to pointer
	return &EventHandler{
		service:    svc,
		orgService: orgSvc,
		validator:  val,
	}
}

// CreateEvent handles POST /api/v1/events (or similar path)
// @Summary Create a new event within an organization
// @Description Creates an event. Payload includes details like title, JsonContent, location_type, location_full_address, location_online_url, start_time, end_time, price, participant_limit, waitlist_limit, requires_approval_for_registration, tag_ids, verification_type_keys, contact_email, contact_phone, mediaeventitem, government_funding_keys.
// @Tags Events (Organization)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param body body payloads.CreateEventRequest true "Event details"
// @Success 201 {object} payloads.EventResponse "Newly created event"
// @Failure 400 {object} utils.ErrorResponse "Invalid request"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (no permission in org)"
// @Router /organizations/{orgId}/events [post]
func (h *EventHandler) CreateEvent(c echo.Context) error {
	// 1. Get user ID and potentially org ID from JWT claims
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		// GetValidatedClaims logs specific errors, so a general message here is fine.
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in CreateEvent")
		// Return status based on common outcomes of GetValidatedClaims or a generic one.
		// If GetValidatedClaims returns an error, it's likely an auth issue.
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}

	userID := claims.UserID
	// How to get OrgID?
	// Option 1: From claims if user always acts within one org context per token.
	// Option 2: From request body (payload.OrgID field - needs adding to CreateEventRequest).
	// Option 3: From path parameter if route is /organizations/{orgId}/events.
	// Assuming Option 3 for now - requires route adjustment in main.go
	orgIDStr := c.Param("orgId") // If route is /organizations/:orgId/events
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("Invalid organization ID in path parameter")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	// TODO: Add authorization check: Does user `userID` have permission to create events in `orgID`?

	// 2. Bind and validate request payload
	var payload payloads.CreateEventRequest
	if err := c.Bind(&payload); err != nil {
		log.Warn().Err(err).Msg("Failed to bind CreateEvent payload")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}
	if err := h.validator.ValidateStruct(payload); err != nil {
		log.Warn().Err(err).Interface("payload", payload).Msg("CreateEvent payload validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// 3. Call the service method
	// Pass orgID obtained from path param
	event, err := h.service.CreateEvent(c.Request().Context(), orgID, userID, payload)
	if err != nil {
		// Use your central error handler or map service errors to HTTP errors
		log.Error().Err(err).Msg("Failed to create event via service")
		// Example error mapping (customize as needed)
		// if errors.Is(err, services.ErrPermissionDenied) {
		// 	return echo.NewHTTPError(http.StatusForbidden, err.Error())
		// }
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create event")
	}

	// 4. Return successful response (e.g., the created event)
	return c.JSON(http.StatusCreated, event)
}

// @Summary Get public details of a specific event
// @Description Retrieves public-facing details for a given event ID.
// @Tags Events (Public)
// @Produce json
// @Param eventId path string true "Event ID (UUID)"
// @Success 200 {object} payloads.PublicEventResponse "Public event details"
// @Failure 404 {object} utils.ErrorResponse "Event not found"
// @Router /events/{eventId} [get]
// GetPublicEventDetails handles GET /events/:eventId (or similar path)
// This endpoint is public, no authentication required by default.
func (h *EventHandler) GetPublicEventDetails(c echo.Context) error {
	// 1. Parse event ID from path parameter
	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Str("eventIdParam", eventIDStr).Msg("Invalid event ID in path parameter for public details")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// 2. Optionally get current user ID if logged in
	currentUserID := authn.GetUserIDFromContext(c) // Returns uuid.Nil if not found or error

	// 3. Call the specific public service method
	eventDetails, err := h.service.GetPublicEventByID(c.Request().Context(), eventID, currentUserID)
	if err != nil {
		// Use central error handler or map service errors
		if errors.Is(err, services.ErrEventNotFound) { // Check specific error type
			log.Ctx(c.Request().Context()).Warn().Err(err).Str("eventId", eventIDStr).Msg("Public event details requested for non-existent event")
			return echo.NewHTTPError(http.StatusNotFound, "Event not found")
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Str("eventId", eventIDStr).Msg("Failed to get public event details via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve event details")
	}

	// 4. Return successful response
	return c.JSON(http.StatusOK, eventDetails)
}

// @Summary List public events
// @Description Lists public events. Supports filtering by orgId, startDate, endDate, searchTerm, tagIds, status. Supports sorting by sortBy. Supports pagination.
// @Tags Events (Public)
// @Produce json
// @Param org_id query string false "Filter by Organization UUID (org_id)"
// @Param org_id2 query string false "Filter by a second Organization UUID (org_id2)"
// @Param startDate query string false "Filter by start date (YYYY-MM-DDTHH:MM:SSZ)"
// @Param endDate query string false "Filter by end date (YYYY-MM-DDTHH:MM:SSZ)"
// @Param searchTerm query string false "Search term for title and location_full_address"
// @Param tagIds query string false "Comma-separated list of tag IDs"
// @Param status query string false "Filter by status (e.g., published, archived, deleted, draft, hidden, cancelled)"
// @Param government_funding_keys query string false "Filter by a specific government funding key"
// @Param event_verification_type_key query string false "Filter by a specific event verification type key"
// @Param sortBy query string false "Sort order (e.g., popularity_desc, start_time_asc)"
// @Param limit query int false "Limit number of results" default(10)
// @Param offset query int false "Offset for pagination" default(0)
// @Success 200 {array} payloads.PublicEventResponse "List of public events"
// @Header 200 {string} X-Total-Count "Total number of events"
// @Router /events [get]
// ListPublicEvents handles GET /api/v1/events
// Publicly lists events with filters for date range, location, funding, search, and tags.
func (h *EventHandler) ListPublicEvents(c echo.Context) error {
	var filterParams payloads.ListPublicEventsRequest

	// Bind query parameters to the filterParams struct
	// Echo's binder should handle basic types, time parsing (with layout tag), and slices.
	// We might need custom binding or manual parsing for complex types if Echo's default binder fails.
	if err := c.Bind(&filterParams); err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to bind query params for ListPublicEvents")
		// Return generic error or more specific based on bind error
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid query parameters: "+err.Error())
	}

	// Apply defaults if not provided by binder (or handle in service layer)
	if filterParams.Limit == 0 {
		filterParams.Limit = services.DefaultPageSize
	}

	// Validate the bound and defaulted parameters
	if err := h.validator.ValidateStruct(filterParams); err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Interface("filterParams", filterParams).Msg("ListPublicEvents filter validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid filter parameters: "+err.Error())
	}

	// Optionally get current user ID if logged in (for CurrentUserRegistrationStatus)
	currentUserID := authn.GetUserIDFromContext(c) // Returns uuid.Nil if not found or error

	// Call the service with filters and optional user ID
	events, totalCount, err := h.service.ListPublicEvents(c.Request().Context(), currentUserID, filterParams)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to list public events via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve public events")
	}

	if events == nil { // Ensure we return an empty list, not null
		events = []payloads.PublicEventResponse{}
	}

	// Set pagination header
	c.Response().Header().Set("X-Total-Count", strconv.FormatInt(totalCount, 10))

	return c.JSON(http.StatusOK, events)
}

// ListOrganizationEvents handles listing events managed by a specific organization.
// @Summary List events managed by the organization (Admin View)
// @Description Lists events for an organization. Supports pagination and potential admin-only filters.
// @Description Supports filtering by startDate, endDate, searchTerm, tagIds, status, government_funding_keys, event_verification_type_key.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param startDate query string false "Filter by start date (YYYY-MM-DDTHH:MM:SSZ)"
// @Param endDate query string false "Filter by end date (YYYY-MM-DDTHH:MM:SSZ)"
// @Param searchTerm query string false "Search term for title and location_full_address"
// @Param tagIds query string false "Comma-separated list of tag IDs"
// @Param status query string false "Filter by status (e.g., published, archived, deleted, draft, hidden, cancelled)"
// @Param government_funding_keys query string false "Filter by government funding key"
// @Param event_verification_type_key query string false "Filter by event verification type key"
// @Param isAdminView query bool false "Flag for admin view to include more details" default(false)
// @Param limit query int false "Limit number of results" default(10)
// @Param offset query int false "Offset for pagination" default(0)
// @Success 200 {array} payloads.EventResponse "List of events"
// @Header 200 {string} X-Total-Count "Total number of events"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden"
// @Failure 404 {object} utils.ErrorResponse "Organization not found"
// @Router /organizations/{orgId}/events [get]
func (h *EventHandler) ListOrganizationEvents(c echo.Context) error {
	// 1. Optionally get user ID from JWT claims
	currentUserID := authn.GetUserIDFromContext(c) // Returns uuid.Nil if not authenticated or error

	if currentUserID == uuid.Nil {
		authHeader := c.Request().Header.Get(echo.HeaderAuthorization)
		if authHeader != "" && strings.HasPrefix(strings.ToLower(authHeader), "bearer ") {
			log.Ctx(c.Request().Context()).Info().Msg("Optional JWT validation failed or token was invalid for ListOrganizationEvents. Proceeding as public request.")
		} else {
			log.Ctx(c.Request().Context()).Info().Msg("No JWT token provided for ListOrganizationEvents. Proceeding as public request.")
		}
	} else {
		log.Ctx(c.Request().Context()).Debug().Str("userID", currentUserID.String()).Msg("User authenticated for ListOrganizationEvents.")
	}

	// 2. Parse Org ID from path
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("Invalid organization ID in path")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	// Note: orgID is used in h.service.ListEventsByOrganization below.

	// 3. Bind and Validate Query Parameters
	var filterParams payloads.ListOrganizationEventsRequest // Corrected payload struct
	if err := c.Bind(&filterParams); err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to bind query params for ListOrganizationEvents")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid query parameters: "+err.Error())
	}

	if filterParams.Limit == 0 {
		filterParams.Limit = services.DefaultPageSize
	}

	if err := h.validator.ValidateStruct(filterParams); err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Interface("filterParams", filterParams).Msg("ListOrganizationEvents filter validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid filter parameters: "+err.Error())
	}

	// Note: The IsAdminView field from filterParams will be passed to the service.
	// The service ListEventsByOrganization will use currentUserID (which can be uuid.Nil)
	// and filterParams.IsAdminView to determine visibility and details.

	// 4. Call the service method
	events, totalCount, err := h.service.ListEventsByOrganization(c.Request().Context(), orgID, currentUserID, filterParams) // Corrected service method and params
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to list organization events via service")
		// Handle specific errors like pgx.ErrNoRows if the organization itself is not found, which might be wrapped by the service.
		// For now, a general error. The service layer should ideally return distinguishable errors.
		if errors.Is(err, services.ErrUserNotMemberOfOrg) { // Example if service checks membership for non-public views
			return echo.NewHTTPError(http.StatusForbidden, "Access denied to this organization's events.")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve organization events")
	}

	if events == nil {
		events = []payloads.EventResponse{} // Ensure empty list, not null
	}

	// Set pagination header
	c.Response().Header().Set("X-Total-Count", strconv.FormatInt(totalCount, 10))

	return c.JSON(http.StatusOK, events)
}

// --- Event Tags ---

// CreateEventTag godoc
// @Summary Create a new event tag (admin/staff)
// @Description Allows authorized users to add new event tags to the system.
// @Tags Event Tags, Admin
// @Accept json
// @Produce json
// @Param body body payloads.CreateEventTagRequest true "Event tag details"
// @Success 201 {object} db.EventTag "Newly created event tag"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request body or validation failed"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not admin/staff - check performed by middleware/route setup)"
// @Failure 409 {object} payloads.ErrorResponse "Conflict - Tag already exists"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /events-tags [post]
// TODO: Add admin authorization middleware for this route in main.go
func (h *EventHandler) CreateEventTag(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in CreateEventTag")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	var payload payloads.CreateEventTagRequest
	if err := c.Bind(&payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body.", err)
	}

	if err := h.validator.ValidateStruct(payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed.", err)
	}

	eventTag, err := h.service.CreateEventTag(c.Request().Context(), userID, payload)
	if err != nil {
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to create event tag.", err)
	}

	return c.JSON(http.StatusCreated, eventTag)
}

// UpdateEventTag handles the logic for updating an event tag.
// @Summary Update an event tag
// @Description Updates an existing event tag's details. Requires admin privileges.
// @Tags Events (Admin)
// @Accept json
// @Produce json
// @Param tagId path string true "Event Tag ID (UUID)"
// @Param body body payloads.UpdateEventTagRequest true "Event tag update payload"
// @Success 200 {object} payloads.TagResponse
// @Failure 400 {object} utils.ErrorResponse "Invalid request body or tag ID"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden"
// @Failure 404 {object} utils.ErrorResponse "Tag not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /event-tags/{tagId} [patch]
func (h *EventHandler) UpdateEventTag(c echo.Context) error {
	tagIDStr := c.Param("tagId")
	tagID, err := uuid.Parse(tagIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid tag ID format.", err)
	}

	var payload payloads.UpdateEventTagRequest
	if err := c.Bind(&payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body.", err)
	}

	if err := h.validator.ValidateStruct(payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed.", err)
	}

	updatedTag, err := h.service.UpdateEventTag(c.Request().Context(), tagID, payload)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return utils.HandleError(c, http.StatusNotFound, "Event tag not found.", err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to update event tag.", err)
	}

	return c.JSON(http.StatusOK, updatedTag)
}

// DeleteEventTag handles the logic for deleting an event tag.
// @Summary Delete an event tag
// @Description Deletes a specific event tag. Requires admin privileges.
// @Tags Events (Admin)
// @Accept json
// @Produce json
// @Param tagId path string true "Event Tag ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} utils.ErrorResponse "Invalid tag ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden"
// @Failure 404 {object} utils.ErrorResponse "Tag not found"
// @Router /event-tags/{tagId} [delete]
func (h *EventHandler) DeleteEventTag(c echo.Context) error {
	tagIDStr := c.Param("tagId")
	tagID, err := uuid.Parse(tagIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid tag ID format.", err)
	}

	err = h.service.DeleteEventTag(c.Request().Context(), tagID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return utils.HandleError(c, http.StatusNotFound, "Event tag not found.", err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to delete event tag.", err)
	}

	return c.NoContent(http.StatusNoContent)
}

// ListEventTags handles the logic for listing all event tags.
// @Summary List all event tags
// @Description Retrieves a list of all available event tags. Can be filtered by approval status.
// @Tags Event Tags, Events (Public)
// @Produce json
// @Param langCode query string false "Filter by language code (e.g., 'en', 'zh_HK', 'zh_CN')"
// @Param approved query boolean false "Filter by approval status (true or false)"
// @Param limit query int false "Limit number of results per page" default(20)
// @Param offset query int false "Offset for pagination" default(0)
// @Success 200 {array} db.EventTag "List of event tags"
// @Failure 400 {object} payloads.ErrorResponse "Invalid query parameters"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /event-tags [get]
// Publicly accessible, but can be filtered (e.g., for admin view)
func (h *EventHandler) ListEventTags(c echo.Context) error {
	var pageParams payloads.PageRequest
	if err := c.Bind(&pageParams); err != nil {
		log.Warn().Err(err).Msg("Failed to bind pagination params for ListEventTags")
		// Use defaults if binding fails but don't block the request
		pageParams = payloads.PageRequest{Limit: 20, Offset: 0}
	} else {
		if err := h.validator.ValidateStruct(pageParams); err != nil {
			log.Warn().Err(err).Msg("ListEventTags pagination validation failed")
			return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
		}
	}

	// Optional query params for filtering
	approvedStr := c.QueryParam("approved")

	var approvedPtr *bool
	if approvedStr != "" {
		approvedVal := approvedStr == "true"
		approvedPtr = &approvedVal
	}

	tags, err := h.service.ListEventTags(c.Request().Context(), approvedPtr, pageParams)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list event tags via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to list event tags")
	}

	return c.JSON(http.StatusOK, tags)
}

// AddTagToEvent godoc
// @Summary Add tag to event
// @Description Adds a tag to a specific event within an organization. Requires organization admin/event creator permission.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param tagId path string true "Tag ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 404 {object} payloads.ErrorResponse "Organization, Event, or Tag not found"
// @Router /organizations/{orgId}/events/{eventId}/tags/{tagId} [post]
func (h *EventHandler) AddTagToEvent(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in AddTagToEvent")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	eventID, err := uuid.Parse(c.Param("eventId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid tag ID format")
	}

	// TODO: Add authorization check

	err = h.service.AddTagToEvent(c.Request().Context(), eventID, tagID, orgID, userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "permission denied") {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Error().Err(err).Msg("Failed to add tag to event via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to add tag to event")
	}

	return c.NoContent(http.StatusNoContent)
}

// RemoveTagFromEvent godoc
// @Summary Remove tag from event
// @Description Removes a tag from a specific event within an organization. Requires organization admin/event creator permission.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param tagId path string true "Tag ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 404 {object} payloads.ErrorResponse "Organization, Event, or Tag not found"
// @Router /organizations/{orgId}/events/{eventId}/tags/{tagId} [delete]
func (h *EventHandler) RemoveTagFromEvent(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in RemoveTagFromEvent")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	eventID, err := uuid.Parse(c.Param("eventId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid tag ID format")
	}

	// TODO: Add authorization check

	err = h.service.RemoveTagFromEvent(c.Request().Context(), eventID, tagID, orgID, userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error()) // Technically deleting a non-existent link isn't always an error, but good to signal
		}
		if strings.Contains(err.Error(), "permission denied") {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Error().Err(err).Msg("Failed to remove tag from event via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to remove tag from event")
	}

	return c.NoContent(http.StatusNoContent)
}

// ListTagsForEvent godoc
// @Summary List tags for an event
// @Description Retrieves the list of tags associated with a specific event.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Success 200 {array} payloads.TagResponse "List of event tags" // Assuming payloads.TagResponse is the correct type
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized" // Assuming this endpoint requires auth based on context
// @Failure 403 {object} payloads.ErrorResponse "Forbidden" // Assuming this endpoint requires auth based on context
// @Failure 404 {object} payloads.ErrorResponse "Organization or Event not found"
// @Router /organizations/{orgId}/events/{eventId}/tags [get]
func (h *EventHandler) ListTagsForEvent(c echo.Context) error {
	// Ensure user is authenticated (route should have JWT middleware)
	_, err := authn.GetValidatedClaims(c) // We need to ensure claims are valid, but userID is not directly used here from claims.
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in ListTagsForEvent")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}

	orgIDStr := c.Param("orgId")
	_, err = uuid.Parse(orgIDStr) // Validate orgID format, though not directly used by service.ListTagsForEvent
	if err != nil {
		log.Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("Invalid organization ID in path parameter for ListTagsForEvent")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		log.Warn().Err(err).Str("eventIdParam", eventIDStr).Msg("Invalid event ID in path parameter for ListTagsForEvent")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// TODO: Authorization check: Does the user have permission to view tags for this event in this org?

	tags, err := h.service.ListTagsForEvent(c.Request().Context(), eventID)
	if err != nil {
		log.Error().Err(err).Str("eventID", eventIDStr).Msg("Failed to list tags for event via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to list tags for event")
	}

	if tags == nil {
		tags = []payloads.TagResponse{}
	}

	return c.JSON(http.StatusOK, tags)
}

// --- Event Required Verification Types ---

// AddEventRequiredVerificationType godoc
// @Summary Add required verification type to event
// @Description Adds a required verification type to a specific event. Requires organization admin/event creator permission.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param typeKey path string true "Verification Type Key (e.g., hk_id)"
// @Success 204 "No Content"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 404 {object} payloads.ErrorResponse "Organization, Event, or Verification Type not found"
// @Router /organizations/{orgId}/events/{eventId}/verification-types/{typeKey} [post]
func (h *EventHandler) AddEventRequiredVerificationType(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in AddEventRequiredVerificationType")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("Invalid organization ID in path parameter")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		log.Warn().Err(err).Str("eventIdParam", eventIDStr).Msg("Invalid event ID in path parameter")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	verificationTypeKey := c.Param("typeKey")
	if verificationTypeKey == "" { // Basic validation for typeKey presence
		log.Warn().Msg("Verification type key not provided in path parameter")
		return echo.NewHTTPError(http.StatusBadRequest, "Missing verification type key in path")
	}
	// Further validation for typeKey format can be added if necessary (e.g. regex, specific values)

	// TODO: Add authorization check: Does userID have permission to modify verification types for eventID in orgID?

	err = h.service.AddEventRequiredVerificationType(c.Request().Context(), eventID, verificationTypeKey, orgID, userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		} else if strings.Contains(err.Error(), "permission denied") {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		} else if strings.Contains(err.Error(), "already exists") || strings.Contains(err.Error(), "duplicate key") {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		log.Error().Err(err).Str("eventID", eventIDStr).Str("typeKey", verificationTypeKey).Msg("Failed to add event required verification type via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to add required verification type")
	}

	return c.NoContent(http.StatusNoContent)
}

// RemoveEventRequiredVerificationType godoc
// @Summary Remove required verification type from event
// @Description Removes a required verification type from a specific event. Requires organization admin/event creator permission.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param typeKey path string true "Verification Type Key (e.g., hk_id)"
// @Success 204 "No Content"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 404 {object} payloads.ErrorResponse "Organization, Event, or Verification Type not found"
// @Router /organizations/{orgId}/events/{eventId}/verification-types/{typeKey} [delete]
func (h *EventHandler) RemoveEventRequiredVerificationType(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in RemoveEventRequiredVerificationType")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	eventID, err := uuid.Parse(c.Param("eventId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}
	verificationTypeKey := c.Param("typeKey")
	if verificationTypeKey == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Missing verification type key")
	}

	// TODO: Add authorization check

	err = h.service.RemoveEventRequiredVerificationType(c.Request().Context(), eventID, verificationTypeKey, orgID, userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error()) // Similar to tag removal, deleting non-existent isn't strictly an error
		}
		if strings.Contains(err.Error(), "permission denied") {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Error().Err(err).Msg("Failed to remove required verification type from event via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to remove required verification type")
	}

	return c.NoContent(http.StatusNoContent)
}

// ListRequiredVerificationTypesForEvent godoc
// @Summary List required verification types for an event
// @Description Retrieves the list of required verification types associated with a specific event.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Success 200 {array} string "List of required verification type keys" // Assuming string is the correct type for keys
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized" // Assuming this endpoint requires auth
// @Failure 403 {object} payloads.ErrorResponse "Forbidden" // Assuming this endpoint requires auth
// @Failure 404 {object} payloads.ErrorResponse "Organization or Event not found"
// @Router /organizations/{orgId}/events/{eventId}/verification-types [get]
func (h *EventHandler) ListRequiredVerificationTypesForEvent(c echo.Context) error {
	ctx := c.Request().Context() // Use this context for logging and service calls
	logger := log.Ctx(ctx)

	// Optional Authentication Check
	var actingUserID uuid.NullUUID // Use uuid.NullUUID to represent an optional user ID

	userToken := c.Get("user") // "user" is the default context key from echo-jwt
	if userToken != nil {
		// A token was processed by the JWT middleware (either valid or error handled by optionalJwtAuthMiddleware)
		// Now, try to get validated claims. If GetValidatedClaims errors here, it means the token was present but invalid/malformed.
		claims, errAuth := authn.GetValidatedClaims(c)
		if errAuth != nil {
			// If a token was provided but it's invalid (e.g. malformed, expired if not caught by middleware ErrorHandler)
			logger.Warn().Err(errAuth).Msg("ListRequiredVerificationTypesForEvent: Invalid token provided.")
			return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: If a token is provided, it must be valid.")
		}
		if claims != nil { // Successfully got claims
			actingUserID = uuid.NullUUID{UUID: claims.UserID, Valid: true}
			logger.Debug().Str("userID", actingUserID.UUID.String()).Msg("ListRequiredVerificationTypesForEvent: Authenticated user.")
		} else {
			// This case might occur if optionalJwtAuthMiddleware's ErrorHandler allows proceeding without setting claims
			// or if GetValidatedClaims returns nil claims without error under some edge case.
			logger.Warn().Msg("ListRequiredVerificationTypesForEvent: Token processed by middleware but no claims obtained. Proceeding as unauthenticated.")
		}
	} else {
		logger.Debug().Msg("ListRequiredVerificationTypesForEvent: No token provided or middleware skipped. Proceeding as unauthenticated.")
	}

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		logger.Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("Invalid organization ID in path")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	_ = orgID // Acknowledge use of orgID to avoid linter error, even if not directly used in this version

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		logger.Warn().Err(err).Str("eventIdParam", eventIDStr).Msg("Invalid event ID in path")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// TODO (Future): The service layer could potentially use actingUserID if different data
	// needs to be returned for authenticated vs. unauthenticated users, or for permission checks if any.
	// For now, ListRequiredVerificationTypesForEvent service method might not need userID.
	_ = actingUserID // To acknowledge use if service layer doesn't take it yet

	verificationTypes, err := h.service.ListRequiredVerificationTypesForEvent(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) { // Assuming service might return this for non-existent event
			logger.Warn().Err(err).Str("eventID", eventIDStr).Msg("Event not found when listing verification types")
			return echo.NewHTTPError(http.StatusNotFound, "Event not found")
		}
		logger.Error().Err(err).Str("eventID", eventIDStr).Msg("Failed to list verification types for event via service")

		// Check if the error is already an echo.HTTPError
		var httpErr *echo.HTTPError
		if errors.As(err, &httpErr) {
			return httpErr // Return the original HTTPError
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to list verification types")
	}

	if verificationTypes == nil {
		verificationTypes = []string{} // Return empty list instead of null
	}

	return c.JSON(http.StatusOK, verificationTypes)
}

// ListEventRegistrations godoc
// @Summary List registrations for a specific event (Admin View)
// @Description Admin view of registrations for a specific event within the organization, with filtering options. Supports pagination.
// @Tags Events (Organization), Event Registrations (Organization Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param limit query int false "Limit number of results" default(10)
// @Param offset query int false "Offset for pagination" default(0)
// @Param status query string false "Filter by registration status (e.g., confirmed, pending_approval, cancelled)"
// @Param user_id query string false "Filter by specific User ID (UUID)"
// @Param search_name query string false "Search by user name"
// @Param payment_status query string false "Filter by payment status (e.g., paid, unpaid, pending)"
// @Success 200 {array} payloads.EventRegistrationResponse "List of event registrations"
// @Header 200 {string} X-Total-Count "Total number of registrations"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not admin/manager for organization)"
// @Failure 404 {object} payloads.ErrorResponse "Organization or Event not found"
// @Router /organizations/{orgId}/events/{eventId}/registrations [get]
func (h *EventHandler) ListEventRegistrations(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in ListEventRegistrations")
		return utils.HandleError(c, http.StatusUnauthorized, "Authentication error: "+err.Error(), err)
	}
	actingUserID := claims.UserID // Renamed from adminUserID for clarity

	var orgID uuid.UUID
	orgIDStr := c.Param("orgId")

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid event ID format", err)
	}

	// This handler is used by two routes:
	// 1. /api/v1/organizations/:orgId/events/:eventId/registrations (orgId is present)
	// 2. /api/v1/events/:eventId/registrations (orgId is NOT present)

	if orgIDStr != "" {
		// If orgId is present in the path, parse it. This is for the org-specific route.
		orgID, err = uuid.Parse(orgIDStr)
		if err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
		}
		// TODO: Add authorization check: does actingUserID have rights for this orgID?
		// This is partially handled by the adminRequiredMiddleware for this route variant,
		// and further by the service layer.
	} else {
		// If orgId is NOT present in the path, this is the /events/:eventId/registrations route.
		// We need orgID for the h.service.ListEventRegistrations call.
		// Fetch public event details to get the OrganizationID.
		publicEventDetails, err := h.service.GetPublicEventByID(c.Request().Context(), eventID, actingUserID)
		if err != nil {
			if errors.Is(err, services.ErrEventNotFound) { // Assuming services.ErrEventNotFound exists
				log.Ctx(c.Request().Context()).Warn().Err(err).Str("eventID", eventIDStr).Msg("Event not found when trying to get its public details")
				return utils.HandleError(c, http.StatusNotFound, "Event not found", err)
			}
			log.Ctx(c.Request().Context()).Error().Err(err).Str("eventID", eventIDStr).Msg("Failed to get public event details to determine orgID")
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve event information", err)
		}

		if publicEventDetails.OrganizationID == uuid.Nil {
			log.Ctx(c.Request().Context()).Error().Str("eventID", eventIDStr).Msg("Fetched public event details, but OrganizationID is nil")
			return utils.HandleError(c, http.StatusInternalServerError, "Could not determine event's organization", errors.New("event has no organization ID"))
		}
		orgID = publicEventDetails.OrganizationID
		// Note: The service method ListEventRegistrations will perform its own permission checks
		// based on orgID and actingUserID. For the public-facing /events/:eventId/registrations,
		// the permission model might need to be more nuanced than what the current
		// service.ListEventRegistrations(..., orgID, actingUserID, ...) provides if it assumes admin/staff context for orgID.
		// The jwtAuth middleware already ensures the user is authenticated.
		// Further checks in the service layer will determine if 'actingUserID' can view registrations for 'orgID' and 'eventID'.
	}

	var pageParams payloads.PageRequest
	if err := c.Bind(&pageParams); err != nil {
		// Log the binding error but proceed with defaults
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to bind pagination query params, using defaults.")
		pageParams = payloads.PageRequest{Limit: 20, Offset: 0}
	} else {
		if err := h.validator.ValidateStruct(pageParams); err != nil {
			// Log validation error and return Bad Request
			log.Ctx(c.Request().Context()).Warn().Err(err).Interface("params", pageParams).Msg("Pagination query params validation failed.")
			return utils.HandleError(c, http.StatusBadRequest, "Validation failed for pagination: "+err.Error(), err)
		}
	}
	// Ensure default limit if not set or zero after binding (validator might allow 0 if omitempty)
	if pageParams.Limit == 0 {
		pageParams.Limit = 20
	}

	// Call the service with the (potentially derived) orgID
	registrations, totalCount, err := h.service.ListEventRegistrations(c.Request().Context(), eventID, orgID, actingUserID, pageParams)
	if err != nil {
		if strings.Contains(err.Error(), "not found") { // Covers event not found or org not found by service
			return utils.HandleError(c, http.StatusNotFound, err.Error(), err)
		}
		if strings.Contains(err.Error(), "permission denied") || strings.Contains(err.Error(), "does not belong to the specified organization") {
			return utils.HandleError(c, http.StatusForbidden, err.Error(), err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Str("eventID", eventID.String()).Str("orgID", orgID.String()).Msg("Failed to list event registrations via service")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to list event registrations", err)
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, registrations)
}

// UpdateRegistrationStatusByAdmin godoc
// @Summary Update registration status (Admin Action)
// @Description Admin action to update a specific registration's status (e.g., approve, reject, mark attendance) for an event.
// @Tags Events (Organization), Event Registrations (Organization Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param registrationId path string true "Registration ID (UUID)"
// @Param body body payloads.UpdateOrgEventRegistrationStatusRequest true "New status and admin notes, e.g. {\"new_status\": \"approved\", \"admin_notes\": \"User confirmed eligibility.\"}" // Define payloads.UpdateOrgEventRegistrationStatusRequest
// @Success 200 {object} payloads.EventRegistrationResponse "Updated registration details"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request or status transition"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not admin/manager for organization)"
// @Failure 404 {object} payloads.ErrorResponse "Organization, Event, or Registration not found"
// @Router /organizations/{orgId}/events/{eventId}/registrations/{registrationId}/status [patch]
func (h *EventHandler) UpdateRegistrationStatusByAdmin(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in UpdateRegistrationStatusByAdmin")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	adminUserID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	registrationID, err := uuid.Parse(c.Param("registrationId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid registration ID format")
	}

	var payload struct {
		NewStatus  string  `json:"new_status" validate:"required,oneof=registered pending_approval cancelled_by_system absent"` // Add other valid admin-settable statuses
		AdminNotes *string `json:"admin_notes,omitempty"`
	}
	if err := c.Bind(&payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}
	if err := h.validator.ValidateStruct(payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// TODO: Add authorization check for adminUserID in orgID

	registrationResponse, err := h.service.UpdateRegistrationStatusByAdmin(c.Request().Context(), registrationID, orgID, adminUserID, payload.NewStatus, payload.AdminNotes)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "permission denied") {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		if strings.Contains(err.Error(), "invalid new registration status") {
			return echo.NewHTTPError(http.StatusBadRequest, err.Error())
		}
		log.Error().Err(err).Msg("Failed to update registration status by admin via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update registration status")
	}

	return c.JSON(http.StatusOK, registrationResponse)
}

// UpdatePaymentStatus godoc
// @Summary Update payment status for a registration (Admin Action)
// @Description Admin action to update the payment status of a registration for an event.
// @Tags Events (Organization), Event Registrations (Organization Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param registrationId path string true "Registration ID (UUID)"
// @Param body body payloads.UpdateEventRegistrationPaymentRequest true "New payment status and staff ID, e.g. {\"new_payment_status\": \"paid\", \"staff_id\": \"uuid\"}"
// @Success 200 {object} payloads.EventRegistrationResponse "Updated registration details"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not admin/manager for organization)"
// @Failure 404 {object} payloads.ErrorResponse "Organization, Event, or Registration not found"
// @Router /organizations/{orgId}/events/{eventId}/registrations/{registrationId}/payment [patch]
func (h *EventHandler) UpdatePaymentStatus(c echo.Context) error {
	registrationID, err := uuid.Parse(c.Param("registrationId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid registration ID format")
	}

	var payload struct {
		NewPaymentStatus string    `json:"new_payment_status" validate:"required,oneof=paid pending not_required failed refunded"`
		StaffID          uuid.UUID `json:"staff_id" validate:"required"` // Require ID of who made the change, even if not for auth
	}
	if err := c.Bind(&payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}
	if err := h.validator.ValidateStruct(payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	registrationResponse, err := h.service.UpdatePaymentStatus(c.Request().Context(), registrationID, payload.NewPaymentStatus, payload.StaffID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "invalid payment status") {
			return echo.NewHTTPError(http.StatusBadRequest, err.Error())
		}
		log.Error().Err(err).Msg("Failed to update payment status via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update payment status")
	}

	return c.JSON(http.StatusOK, registrationResponse)
}

// CancelEventRegistration handles DELETE /api/v1/registrations/{registrationId}
// Requires user authentication (user must own the registration).
func (h *EventHandler) CancelEventRegistration(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in CancelEventRegistration")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	registrationID, err := uuid.Parse(c.Param("registrationId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid registration ID format")
	}

	// No request body needed

	registrationResponse, err := h.service.CancelEventRegistration(c.Request().Context(), userID, registrationID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "permission denied") {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		if strings.Contains(err.Error(), "already cancelled") || strings.Contains(err.Error(), "cannot be cancelled") {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		log.Error().Err(err).Msg("Failed to cancel event registration via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to cancel event registration")
	}

	// Return the final state of the (now cancelled) registration
	return c.JSON(http.StatusOK, registrationResponse)
}

// --- Event Volunteering ---

// ApplyForEventVolunteering handles POST /api/v1/events/{eventId}/volunteer-applications
// Requires user authentication.
func (h *EventHandler) ApplyForEventVolunteering(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in ApplyForEventVolunteering")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	eventID, err := uuid.Parse(c.Param("eventId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	var payload struct {
		Notes *string `json:"notes"`
	}
	if err := c.Bind(&payload); err != nil {
		// Allow empty body or handle binding errors gracefully if notes are optional
		// return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}
	// No validation needed for optional notes

	application, err := h.service.ApplyForEventVolunteering(c.Request().Context(), userID, eventID, payload.Notes)
	if err != nil {
		if errors.Is(err, services.ErrEventNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Event not found")
		} else if errors.Is(err, services.ErrAlreadyAppliedToVolunteer) {
			return echo.NewHTTPError(http.StatusConflict, "User has already applied to volunteer for this event")
		} else if errors.Is(err, services.ErrOrgQualificationRequired) { // Handle new error
			return echo.NewHTTPError(http.StatusForbidden, "User is not a qualified volunteer for the event's organization")
		} else if strings.Contains(err.Error(), "not accepting volunteer applications") { // Service might return a more specific error here
			return echo.NewHTTPError(http.StatusBadRequest, err.Error())
		}
		log.Error().Err(err).Msg("Failed to apply for event volunteering via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to apply for event volunteering")
	}

	return c.JSON(http.StatusCreated, application)
}

// ListUserEventVolunteerApplications handles GET /api/v1/users/me/event-volunteer-applications
// Requires user authentication.
// @Summary List current user's volunteer applications for events
// @Description Retrieves a list of volunteer applications submitted by the authenticated user.
// @Tags Event Volunteering, User Profile
// @Produce json
// @Param limit query int false "Limit number of results" default(10)
// @Param offset query int false "Offset for pagination" default(0)
// @Success 200 {array} payloads.EventVolunteerApplicationResponse "List of user's event volunteer applications"
// @Header 200 {string} X-Total-Count "Total number of applications"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
func (h *EventHandler) ListUserEventVolunteerApplications(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in ListUserEventVolunteerApplications")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	var pageParams payloads.PageRequest
	if err := c.Bind(&pageParams); err != nil {
		pageParams = payloads.PageRequest{Limit: 20, Offset: 0}
	} else {
		if err := h.validator.ValidateStruct(pageParams); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
		}
	}

	applications, err := h.service.ListUserEventVolunteerApplications(c.Request().Context(), userID, pageParams)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list user event volunteer applications via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to list applications")
	}

	return c.JSON(http.StatusOK, applications)
}

// ListEventVolunteerApplicationsForEvent godoc
// @Summary List volunteer applications for an event (Admin View)
// @Description Admin view of volunteer applications for a specific event within the organization. Supports pagination.
// @Tags Events (Organization), Event Volunteering (Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param limit query int false "Limit number of results" default(10)
// @Param offset query int false "Offset for pagination" default(0)
// @Success 200 {array} payloads.EventVolunteerApplicationResponse "List of volunteer applications"
// @Header 200 {string} X-Total-Count "Total number of applications"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not admin/manager for organization)"
// @Failure 404 {object} payloads.ErrorResponse "Organization or Event not found"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/events/{eventId}/volunteer-applications [get]
func (h *EventHandler) ListEventVolunteerApplicationsForEvent(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in ListEventVolunteerApplicationsForEvent")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	adminUserID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	eventID, err := uuid.Parse(c.Param("eventId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	var pageParams payloads.PageRequest
	if err := c.Bind(&pageParams); err != nil {
		pageParams = payloads.PageRequest{Limit: 20, Offset: 0}
	} else {
		if err := h.validator.ValidateStruct(pageParams); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
		}
	}

	// Authorization check: Ensure adminUserID is an admin/owner/manager of orgID
	checkOpts := payloads.CheckUserOrganizationRoleOptions{
		UserID:         adminUserID,
		OrganizationID: orgID,
		RequiredRoles:  []string{"admin", "owner", "manager"},
	}
	isAuthorized, authErr := h.orgService.CheckUserOrganizationRole(c.Request().Context(), checkOpts)
	if authErr != nil {
		log.Ctx(c.Request().Context()).Error().Err(authErr).Str("user_id", adminUserID.String()).Str("org_id", orgID.String()).Msg("Error checking user role in organization for reviewing event volunteer application")
		return echo.NewHTTPError(http.StatusInternalServerError, "Error checking user permissions")
	}
	if !isAuthorized {
		log.Ctx(c.Request().Context()).Warn().Str("user_id", adminUserID.String()).Str("org_id", orgID.String()).Msg("User not authorized to review event volunteer applications for this organization")
		return echo.NewHTTPError(http.StatusForbidden, "User not authorized for this organization")
	}

	applications, err := h.service.ListEventVolunteerApplicationsForEvent(c.Request().Context(), eventID, orgID, adminUserID, pageParams)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "permission denied") {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Error().Err(err).Msg("Failed to list event volunteer applications for event via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to list applications")
	}

	return c.JSON(http.StatusOK, applications)
}

// ReviewEventVolunteerApplication godoc
// @Summary Review a volunteer application (Admin Action)
// @Description Admin action to review (approve or reject) a specific volunteer application for an event.
// @Tags Events (Organization), Event Volunteering (Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param applicationId path string true "Volunteer Application ID (UUID)"
// @Param body body payloads.ReviewVolunteerApplicationRequest true "Review details (status, notes)"
// @Success 200 {object} payloads.EventVolunteerApplicationResponse "Updated volunteer application details"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request or review action"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not admin/manager for organization)"
// @Failure 404 {object} payloads.ErrorResponse "Organization, Event, or Application not found"
// @Router /organizations/{orgId}/events/{eventId}/volunteer-applications/{applicationId}/review [patch]
func (h *EventHandler) ReviewEventVolunteerApplication(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in ReviewEventVolunteerApplication")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	adminUserID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	// Add parsing for eventId
	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	applicationID, err := uuid.Parse(c.Param("applicationId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid application ID format")
	}

	var payload struct {
		NewStatus  string  `json:"new_status" validate:"required,oneof=approved rejected"`
		AdminNotes *string `json:"admin_notes,omitempty"`
	}
	if err := c.Bind(&payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}
	if err := h.validator.ValidateStruct(payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Authorization check: Ensure adminUserID is an admin/owner/manager of orgID
	// This uses the new orgService method.
	checkOpts := payloads.CheckUserOrganizationRoleOptions{
		UserID:         adminUserID,
		OrganizationID: orgID,
		RequiredRoles:  []string{"admin", "owner", "manager"}, // Use string literals for roles
	}
	isAuthorized, authErr := (*h.orgService).CheckUserOrganizationRole(c.Request().Context(), checkOpts)
	if authErr != nil {
		log.Ctx(c.Request().Context()).Error().Err(authErr).Str("user_id", adminUserID.String()).Str("org_id", orgID.String()).Msg("Error checking user organization role in ReviewEventVolunteerApplication")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to verify authorization")
	}
	if !isAuthorized {
		log.Ctx(c.Request().Context()).Warn().Str("user_id", adminUserID.String()).Str("org_id", orgID.String()).Msg("User not authorized to review event volunteer application for this organization")
		return echo.NewHTTPError(http.StatusForbidden, "User not authorized for this organization")
	}

	// Call the service to review the application
	// Pass the parsed eventID to the service call
	updatedApplication, err := h.service.ReviewEventVolunteerApplication(c.Request().Context(), applicationID, orgID, eventID, adminUserID, payload.NewStatus, payload.AdminNotes)
	if err != nil {
		if errors.Is(err, services.ErrVolunteerApplicationNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Volunteer application not found")
		} else if errors.Is(err, services.ErrEventNotFound) { // If the event itself isn't found
			return echo.NewHTTPError(http.StatusNotFound, "Event not found")
		} else if strings.Contains(err.Error(), "permission denied") { // TODO: Replace with errors.Is(err, services.ErrPermissionDenied) when defined
			return echo.NewHTTPError(http.StatusForbidden, "Permission denied")
		} else if strings.Contains(err.Error(), "already been reviewed") { // Service might return a specific error e.g. services.ErrApplicationAlreadyReviewed
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		} else if strings.Contains(err.Error(), "invalid new volunteer application status") { // Service might return a specific error e.g. services.ErrInvalidApplicationStatus
			return echo.NewHTTPError(http.StatusBadRequest, err.Error())
		}
		log.Error().Err(err).Msg("Failed to review event volunteer application via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to review application")
	}

	return c.JSON(http.StatusOK, updatedApplication)
}

// --- Event Statistics ---

// GetUserEventStatistics handles GET /api/v1/users/me/event-statistics
// Requires user authentication.
func (h *EventHandler) GetUserEventStatistics(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in GetUserEventStatistics")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	var pageParams payloads.PageRequest
	if err := c.Bind(&pageParams); err != nil {
		pageParams = payloads.PageRequest{Limit: 20, Offset: 0}
	} else {
		if err := h.validator.ValidateStruct(pageParams); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
		}
	}

	// Parse optional date range query parameters
	startDateStr := c.QueryParam("startDate")
	endDateStr := c.QueryParam("endDate")
	var startDate, endDate *time.Time
	if startDateStr != "" {
		t, err := time.Parse(time.RFC3339, startDateStr) // Or appropriate format
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid start date format")
		}
		startDate = &t
	}
	if endDateStr != "" {
		t, err := time.Parse(time.RFC3339, endDateStr) // Or appropriate format
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid end date format")
		}
		endDate = &t
	}

	stats, err := h.service.GetUserEventStatistics(c.Request().Context(), userID, startDate, endDate, pageParams)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user event statistics via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get user event statistics")
	}

	return c.JSON(http.StatusOK, stats)
}

// GetOrganizationEventStatistics handles GET /api/v1/organizations/{orgId}/event-statistics
// Requires org admin/manager permission.
func (h *EventHandler) GetOrganizationEventStatistics(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in GetOrganizationEventStatistics")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	adminUserID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	var pageParams payloads.PageRequest
	if err := c.Bind(&pageParams); err != nil {
		pageParams = payloads.PageRequest{Limit: 20, Offset: 0}
	} else {
		if err := h.validator.ValidateStruct(pageParams); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
		}
	}

	// Optional query params
	startDateStr := c.QueryParam("startDate")
	endDateStr := c.QueryParam("endDate")
	tagIDsStr := c.QueryParam("tagIds") // Comma-separated UUIDs

	var startDate, endDate *time.Time
	if startDateStr != "" {
		t, err := time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid start date format")
		}
		startDate = &t
	}
	if endDateStr != "" {
		t, err := time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid end date format")
		}
		endDate = &t
	}

	var tagIDs []uuid.UUID
	if tagIDsStr != "" {
		idStrs := strings.Split(tagIDsStr, ",")
		tagIDs = make([]uuid.UUID, 0, len(idStrs))
		for _, idStr := range idStrs {
			id, err := uuid.Parse(strings.TrimSpace(idStr))
			if err != nil {
				return echo.NewHTTPError(http.StatusBadRequest, "Invalid tag ID format in query parameter")
			}
			tagIDs = append(tagIDs, id)
		}
	}

	// TODO: Authorization check for adminUserID in orgID

	// Note: tagIDs is passed to service but currently ignored due to query limitations
	stats, err := h.service.GetOrganizationEventStatistics(c.Request().Context(), orgID, adminUserID, startDate, endDate, tagIDs, pageParams)
	if err != nil {
		if strings.Contains(err.Error(), "permission denied") {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Error().Err(err).Msg("Failed to get organization event statistics via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get organization event statistics")
	}

	return c.JSON(http.StatusOK, stats)
}

// --- Other Event Actions (Update, Delete, Media) ---

// UpdateEventDetails godoc
// @Summary Update details of a specific event (Admin)
// @Description Updates an event. Payload can include title, JsonContent, location_type, location_full_address, location_online_url, start_time, end_time, price, participant_limit, waitlist_limit, requires_approval_for_registration, tag_ids, verification_type_keys, contact_email, contact_phone, mediaeventitem, government_funding_keys, status, published_at.
// @Tags Events (Organization)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID) to update"
// @Param body body payloads.UpdateEventRequest true "Event details to update. Only provide fields that need to be changed."
// @Success 200 {object} payloads.EventResponse "Updated event details"
// @Failure 400 {object} utils.ErrorResponse "Invalid request (e.g., bad format, validation error)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (user does not have permission to update events in this organization)"
// @Failure 404 {object} utils.ErrorResponse "Event or Organization not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/events/{eventId} [put]
func (h *EventHandler) UpdateEventDetails(c echo.Context) error {
	// 1. Get user ID and Org ID from JWT / path
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in UpdateEventDetails")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	eventID, err := uuid.Parse(c.Param("eventId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// 2. Bind and validate request payload
	var payload payloads.UpdateEventRequest
	if err := c.Bind(&payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}
	if err := h.validator.ValidateStruct(payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// TODO: Authorization check: Does userID have permission to update eventID in orgID?

	updatedEvent, err := h.service.UpdateEventDetails(c.Request().Context(), eventID, orgID, userID, payload)
	if err != nil {
		// Consider specific error mapping, e.g., not found, permission denied
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "permission denied") { // Assuming service returns such an error
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Error().Err(err).Msg("Failed to update event details via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update event")
	}

	return c.JSON(http.StatusOK, updatedEvent)
}

// PatchEventDetails godoc
// @Summary Partially update details of a specific event (Admin)
// @Description Partially updates an event. Only provide fields that need to be changed.
// @Tags Events (Organization)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID) to update"
// @Param body body payloads.UpdateEventRequest true "Event details to patch. Only provide fields that need to be changed."
// @Success 200 {object} payloads.EventResponse "Updated event details"
// @Failure 400 {object} utils.ErrorResponse "Invalid request (e.g., bad format, validation error)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (user does not have permission to update events in this organization)"
// @Failure 404 {object} utils.ErrorResponse "Event or Organization not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/events/{eventId} [patch]
func (h *EventHandler) PatchEventDetails(c echo.Context) error {
	// 1. Get user ID and Org ID from JWT / path
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in PatchEventDetails")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	orgID, err := uuid.Parse(c.Param("orgId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	eventID, err := uuid.Parse(c.Param("eventId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// 2. Bind and validate request payload
	var payload payloads.UpdateEventRequest
	if err := c.Bind(&payload); err != nil {
		// For PATCH, it's okay if the payload is empty or only contains a few fields.
		// The service layer will handle applying only the provided fields.
		// However, a completely unbindable payload is still an error.
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}

	// Validate the payload.
	// Note: For PATCH, fields are optional. The `omitempty` tag in `UpdateEventRequest` combined
	// with validator's behavior for pointer fields should correctly skip validation for absent fields.
	// If a field is present but invalid (e.g., malformed email), it should fail.
	if err := h.validator.ValidateStruct(payload); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// TODO: Authorization check: Does userID have permission to update eventID in orgID?
	// This should be similar to UpdateEventDetails.

	// 3. Call the service method.
	// We can reuse the UpdateEventDetails service method as it's designed to handle partial updates
	// (due to sqlc.narg and pointer fields in the request payload).
	updatedEvent, err := h.service.UpdateEventDetails(c.Request().Context(), eventID, orgID, userID, payload)
	if err != nil {
		// Consider specific error mapping, e.g., not found, permission denied
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "permission denied") { // Assuming service returns such an error
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Error().Err(err).Msg("Failed to patch event details via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to patch event")
	}

	return c.JSON(http.StatusOK, updatedEvent)
}

// DeleteEvent handles DELETE /api/v1/organizations/{orgId}/events/{eventId}
// Requires org admin/event creator permission.
// @Summary Delete an event
// @Description Deletes a specific event within an organization.
// @Tags Events (Organization)
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Success 204 "No Content"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 404 {object} payloads.ErrorResponse "Event not found"
// @Router /organizations/{orgId}/events/{eventId} [delete]
func (h *EventHandler) DeleteEvent(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims in DeleteEvent")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("Invalid organization ID in path parameter for DeleteEvent")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		log.Warn().Err(err).Str("eventIdParam", eventIDStr).Msg("Invalid event ID in path parameter for DeleteEvent")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// TODO: Authorization check: Does userID have permission to delete eventID in orgID?

	err = h.service.DeleteEvent(c.Request().Context(), eventID, orgID, userID)
	if err != nil {
		// Consider specific error mapping
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "permission denied") { // Assuming service returns such an error
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Error().Err(err).Msg("Failed to delete event via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete event")
	}

	return c.NoContent(http.StatusNoContent)
}

// GetOrganizationEventDetails retrieves detailed information about a specific event for an organization admin.
// It's similar to GetPublicEventDetails but might include more sensitive or administrative data.
// Route: GET /organizations/{orgId}/events/{eventId}
// @Summary Get detailed information about a specific event (Admin View)
// @Description Retrieves detailed admin view of an event, including counts, tags, required verifications, and media items.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Success 200 {object} payloads.EventResponse "Event details"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 404 {object} payloads.ErrorResponse "Event not found"
// @Router /organizations/{orgId}/events/{eventId} [get]
func (h *EventHandler) GetOrganizationEventDetails(c echo.Context) error {
	// 1. Optionally get user ID from JWT claims
	actingUserID := authn.GetUserIDFromContext(c) // Returns uuid.Nil if not authenticated or error

	if actingUserID == uuid.Nil {
		authHeader := c.Request().Header.Get(echo.HeaderAuthorization)
		if authHeader != "" && strings.HasPrefix(strings.ToLower(authHeader), "bearer ") {
			log.Ctx(c.Request().Context()).Info().Msg("Optional JWT validation failed or token was invalid for GetOrganizationEventDetails. Proceeding as public request.")
		} else {
			log.Ctx(c.Request().Context()).Info().Msg("No JWT token provided for GetOrganizationEventDetails. Proceeding as public request.")
		}
	} else {
		log.Ctx(c.Request().Context()).Debug().Str("userID", actingUserID.String()).Msg("User authenticated for GetOrganizationEventDetails.")
	}

	// 2. Parse Org ID and Event ID from path
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("Invalid organization ID in path for GetOrganizationEventDetails")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}
	_ = orgID // Acknowledge use of orgID to avoid linter error, even if not directly used in this version

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Str("eventIdParam", eventIDStr).Msg("Invalid event ID in path for GetOrganizationEventDetails")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// Determine isAdminView from query param, defaulting to false
	isAdminViewStr := c.QueryParam("isAdminView")
	isAdminView := false
	if isAdminViewStr == "true" {
		isAdminView = true
	}

	// If isAdminView is true, user must be authenticated.
	if isAdminView && actingUserID == uuid.Nil {
		log.Ctx(c.Request().Context()).Warn().Msg("Attempt to access admin view for GetOrganizationEventDetails without authentication.")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required for admin view.")
	}

	// The service method h.service.GetEventByID will use actingUserID (which can be uuid.Nil)
	// and isAdminView to determine visibility and details.
	// For public requests (actingUserID == uuid.Nil), isAdminView must be false.
	// For authenticated requests, isAdminView can be true or false based on the query param,
	// and the service layer should enforce permissions if isAdminView is true.

	// 3. Call the service method
	eventDetails, err := h.service.GetEventByID(c.Request().Context(), eventID, actingUserID, isAdminView) // Corrected service method
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgID", orgID.String()).Str("eventID", eventID.String()).Msg("Failed to get organization event details via service")
		if errors.Is(err, services.ErrEventNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Event not found")
		}
		// The service.GetEventByID should ideally handle cases where the event is not found or not public (if actingUserID is Nil).
		// If it returns a generic error, more specific handling might be needed here or in the service.
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve event details")
	}

	// 4. Authorization: Ensure the event fetched belongs to the organization specified in the path.
	// This is important if GetEventByID doesn't inherently filter by organization for the public/non-admin view.
	// If actingUserID is present, this check is a stronger form of authorization.
	// If actingUserID is uuid.Nil (public request), this ensures the public event is indeed under the specified org path.
	if eventDetails.OrganizationID != orgID {
		log.Ctx(c.Request().Context()).Warn().
			Str("eventID", eventID.String()).
			Str("eventOrgID", eventDetails.OrganizationID.String()).
			Str("pathOrgID", orgID.String()).
			Msg("Authorization mismatch: Event's organization does not match path organization ID.")
		// For a public request (actingUserID == uuid.Nil), this means the event is not found under this org path.
		// For an authenticated request, it's a genuine mismatch.
		return echo.NewHTTPError(http.StatusNotFound, "Event not found under the specified organization path.")
	}

	return c.JSON(http.StatusOK, eventDetails)
}

// GetEventVolunteerApplicationDetails godoc
// @Summary Get details of a specific volunteer application for an event
// @Description Retrieves details for a specific volunteer application to an event. Requires authentication.
// @Tags Events (Organization), Event Volunteering (User)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param appId path string true "Application ID (UUID)"
// @Success 200 {object} payloads.EventVolunteerApplicationResponse "Volunteer application details" // Assuming a suitable response payload
// @Failure 400 {object} utils.ErrorResponse "Invalid ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (user cannot access this application)"
// @Failure 404 {object} utils.ErrorResponse "Organization, Event, or Application not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/events/{eventId}/volunteer-applications/{appId} [get]
// @Security ApiKeyAuth
func (h *EventHandler) GetEventVolunteerApplicationDetails(c echo.Context) error {
	ctx := c.Request().Context()
	logger := log.Ctx(ctx)

	// claims, err := authn.GetValidatedClaims(c)
	// if err != nil {
	// 	logger.Warn().Err(err).Msg("Failed to get validated claims")
	// 	return utils.HandleError(c, http.StatusUnauthorized, "Authentication error: "+err.Error(), err)
	// }
	// requestingUserID := claims.UserID
	requestingUserID := uuid.Nil // Set to uuid.Nil for public access

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		logger.Warn().Err(err).Str("orgId", orgIDStr).Msg("Invalid organization ID format")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		logger.Warn().Err(err).Str("eventId", eventIDStr).Msg("Invalid event ID format")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid event ID format", err)
	}

	appIDStr := c.Param("appId")
	appID, err := uuid.Parse(appIDStr)
	if err != nil {
		logger.Warn().Err(err).Str("appId", appIDStr).Msg("Invalid application ID format")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid application ID format", err)
	}

	applicationDetails, serviceErr := h.service.GetEventVolunteerApplicationDetails(ctx, requestingUserID, orgID, eventID, appID)
	if serviceErr != nil {
		logger.Error().Err(serviceErr).
			Str("orgId", orgIDStr).
			Str("eventId", eventIDStr).
			Str("appId", appIDStr).
			Msg("Service failed to get event volunteer application details")

		if errors.Is(serviceErr, services.ErrVolunteerApplicationNotFound) || errors.Is(serviceErr, services.ErrEventNotFound) || errors.Is(serviceErr, services.ErrOrganizationNotFound) {
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		}
		if errors.Is(serviceErr, services.ErrForbidden) {
			return utils.HandleError(c, http.StatusForbidden, serviceErr.Error(), serviceErr)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve volunteer application details", serviceErr)
	}

	return c.JSON(http.StatusOK, applicationDetails)
}

// AddEventMediaItem godoc
// @Summary Add media to an event
// @Description Uploads a media file (image, video, etc.) and associates it with an event. Can optionally be set as the banner image.
// @Tags Events (Organization Media)
// @Accept mpfd
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param file formData file true "The media file to upload."
// @Param is_banner formData boolean false "Set this media as the event banner (defaults to false)."
// @Success 201 {object} payloads.MediaItemResponse "Media item created successfully"
// @Failure 400 {object} utils.ErrorResponse "Invalid request (e.g., bad ID format, file error)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not admin/manager for organization)"
// @Failure 404 {object} utils.ErrorResponse "Organization or Event not found"
// @Router /organizations/{orgId}/events/{eventId}/media [post]
func (h *EventHandler) AddEventMediaItem(c echo.Context) error {
	// 1. Extract path parameters
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// 2. Get user ID from JWT claims
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	// 3. Handle file upload from multipart form
	file, err := c.FormFile("file") // "file" is the name of the form field
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to get file from form")
		return echo.NewHTTPError(http.StatusBadRequest, "File upload error: "+err.Error())
	}

	// 4. Parse is_banner form field
	isBannerStr := c.FormValue("is_banner")
	isBanner, _ := strconv.ParseBool(isBannerStr) // Defaults to false if parsing fails or field not present

	// 5. Call the service method
	// The service method should handle saving the file and creating the DB record.
	mediaItem, err := h.service.AddEventMediaItem(c.Request().Context(), eventID, orgID, userID, file, isBanner)
	if err != nil {
		// Map service errors to HTTP errors
		if errors.Is(err, services.ErrEventNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Event not found")
		} else if errors.Is(err, services.ErrUserNotMemberOfOrg) { // Or a more generic permission error
			return echo.NewHTTPError(http.StatusForbidden, "User does not have permission to add media to this event")
		}
		// Log other errors and return a generic server error
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to add event media item via service")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to add media item: "+err.Error())
	}

	// 6. Return successful response with the created media item details
	return c.JSON(http.StatusCreated, mediaItem)
}

// ListEventMediaItems godoc
// @Summary List media for an event
// @Description Retrieves the list of media items associated with a specific event.
// @Tags Events (Organization), Event Media Management
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Success 200 {array} payloads.MediaItemResponse "List of media items"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized" // Assuming auth is required
// @Failure 403 {object} payloads.ErrorResponse "Forbidden" // Assuming auth is required
// @Failure 404 {object} payloads.ErrorResponse "Organization or Event not found"
// @Router /organizations/{orgId}/events/{eventId}/media [get]
func (h *EventHandler) ListEventMediaItems(c echo.Context) error {
	ctx := c.Request().Context()

	// orgIDStr := c.Param("orgId") // orgId might not be strictly needed for listing if eventId is globally unique and authz is handled by event's public/private status
	// _, err := uuid.Parse(orgIDStr)
	// if err != nil {
	// 	log.Ctx(ctx).Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("ListEventMediaItems: Invalid organization ID")
	// 	return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	// }

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("eventIdParam", eventIDStr).Msg("ListEventMediaItems: Invalid event ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	// TODO: Authorization: Check if the user (if any) can view media for this event.
	// This could depend on whether the event is public or if the user has specific permissions for the org/event.
	// For now, assuming service layer handles this (e.g., by only returning media for public events or if user has access).

	dbMediaItems, err := h.service.ListEventMediaItems(ctx, eventID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventIDStr).Msg("ListEventMediaItems: Service failed to list media")
		if errors.Is(err, services.ErrEventNotFound) { // If the event itself doesn't exist
			return echo.NewHTTPError(http.StatusNotFound, "Event not found, cannot list media items.")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to list media items")
	}

	responseItems := make([]payloads.MediaItemResponse, len(dbMediaItems))
	for i, item := range dbMediaItems {
		responseItems[i] = payloads.MediaItemResponse{
			ID:         item.ID,
			FileName:   item.FileName,
			FilePath:   item.FilePath, // Should be web-accessible URL
			FileType:   item.FileType,
			FileSize:   item.FileSize,
			UploadedAt: item.UploadedAt,
		}
	}

	if len(responseItems) == 0 {
		responseItems = []payloads.MediaItemResponse{} // Return empty list instead of null
	}

	return c.JSON(http.StatusOK, responseItems)
}

// DeleteEventMediaItem godoc
// @Summary Delete media from an event
// @Description Deletes a specific media item associated with an event. Requires organization admin/event creator permission.
// @Tags Events (Organization Media)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param itemId path string true "Media Item ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 404 {object} payloads.ErrorResponse "Organization, Event, or Media Item not found"
// @Router /organizations/{orgId}/events/{eventId}/media/{itemId} [delete]
func (h *EventHandler) DeleteEventMediaItem(c echo.Context) error {
	ctx := c.Request().Context()
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("DeleteEventMediaItem: Failed to get validated claims")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	userID := claims.UserID

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("orgIdParam", orgIDStr).Msg("DeleteEventMediaItem: Invalid organization ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("eventIdParam", eventIDStr).Msg("DeleteEventMediaItem: Invalid event ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	itemIDStr := c.Param("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("itemIdParam", itemIDStr).Msg("DeleteEventMediaItem: Invalid media item ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid media item ID format")
	}

	// TODO: Authorization check: Does userID have permission to delete media itemID from eventID in orgID?

	err = h.service.DeleteEventMediaItem(ctx, itemID, orgID, eventID, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("itemID", itemIDStr).Str("eventID", eventIDStr).Str("orgID", orgIDStr).Msg("DeleteEventMediaItem: Service failed to delete media item")
		if errors.Is(err, services.ErrEventNotFound) { // If the event or implicitly the item within a non-existent event
			return echo.NewHTTPError(http.StatusNotFound, "Resource not found (event or media item).")
		}
		// Using string contains for "media item not found" as services.ErrMediaItemNotFound is not defined
		if strings.Contains(err.Error(), "media item not found") {
			return echo.NewHTTPError(http.StatusNotFound, "Media item not found.")
		}
		if strings.Contains(err.Error(), "organization not found") { // Example if service returns this string
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found.")
		}
		if errors.Is(err, utils.ErrForbidden) {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete media item")
	}

	return c.NoContent(http.StatusNoContent)
}

// ListOrgEventVolunteerApplicationsHandler godoc
// @Summary List all event volunteer applications for an organization (Admin)
// @Description Retrieves all event volunteer applications for a specific organization, with optional status and pagination. Requires staff privileges with 'admin', 'owner', or 'manager' role within the organization.
// @Tags Events (Organization), Event Volunteering (Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param status query string false "Filter by application status (e.g., 'pending', 'approved', 'rejected')"
// @Param limit query int false "Limit number of results" default(20)
// @Param offset query int false "Offset for pagination" default(0)
// @Success 200 {array} payloads.EventVolunteerApplicationResponse "List of event volunteer applications"
// @Header 200 {string} X-Total-Count "Total number of applications matching criteria"
// @Failure 400 {object} utils.ErrorResponse "Invalid organization ID format or invalid status value or pagination params"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized (admin token missing or invalid)"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (admin not staff in organization or lacks required role)"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /admin/organizations/{orgId}/event-volunteer-applications [get] // Tentative route
func (h *EventHandler) ListOrgEventVolunteerApplicationsHandler(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	adminUserID := authn.GetUserIDFromContext(c)
	if adminUserID == uuid.Nil {
		// This should ideally be caught by adminRequired middleware, but defensive check here.
		return echo.NewHTTPError(http.StatusUnauthorized, "Admin User ID not found in token")
	}

	// Permission check: Staff in organization with appropriate role
	isAuthorized, authErr := h.orgService.CheckUserOrganizationRole(c.Request().Context(), payloads.CheckUserOrganizationRoleOptions{
		UserID:         adminUserID,
		OrganizationID: orgID,
		RequiredRoles:  []string{"admin", "owner", "manager"}, // Define appropriate roles
	})
	if authErr != nil {
		if errors.Is(authErr, payloads.ErrNotMember) {
			log.Ctx(c.Request().Context()).Warn().Err(authErr).Str("adminUserID", adminUserID.String()).Str("orgID", orgID.String()).Msg("Admin user not a member of the organization")
			return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You are not a member of this organization.")
		}
		log.Ctx(c.Request().Context()).Error().Err(authErr).Str("adminUserID", adminUserID.String()).Str("orgID", orgID.String()).Msg("Error checking admin user role in organization")
		return echo.NewHTTPError(http.StatusInternalServerError, "Error checking permissions")
	}
	if !isAuthorized {
		log.Ctx(c.Request().Context()).Warn().Str("adminUserID", adminUserID.String()).Str("orgID", orgID.String()).Msg("Admin user does not have required role for listing org event volunteer applications")
		return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You do not have the required role in this organization.")
	}

	statusQuery := c.QueryParam("status")
	var statusPtr *string
	if statusQuery != "" {
		// TODO: Add validation for statusQuery against db.ApplicationStatusEnum values if necessary
		statusPtr = &statusQuery
	}

	var pageParams payloads.PageRequest
	if err := c.Bind(&pageParams); err != nil {
		// Use defaults if binding fails or params are missing
		pageParams = payloads.PageRequest{Limit: services.DefaultPageSize, Offset: 0}
	} else {
		if err := h.validator.ValidateStruct(pageParams); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid pagination parameters: "+err.Error())
		}
	}
	// Ensure Limit has a default if not provided or zero after binding
	if pageParams.Limit == 0 {
		pageParams.Limit = services.DefaultPageSize
	}

	apps, totalCount, err := h.service.ListOrgEventVolunteerApplications(c.Request().Context(), orgID, statusPtr, pageParams)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgId", orgIDStr).Msg("Failed to list organization event volunteer applications")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve applications")
	}

	c.Response().Header().Set("X-Total-Count", strconv.FormatInt(totalCount, 10))
	return c.JSON(http.StatusOK, apps)
}

// @Summary Get event statistics
// @Description Retrieves statistics about events, including counts per category (tag) and top N events by participant count.
// @Description Supports optional startDate, endDate (RFC3339 format, e.g., "2023-01-01T00:00:00Z") and limit query parameters.
// @Description Defaults to the last 6 months and top 10 events if parameters are not provided.
// @Tags Events (Statistics)
// @Produce json
// @Param startDate query string false "Start date for filtering statistics (RFC3339 format)"
// @Param endDate query string false "End date for filtering statistics (RFC3339 format)"
// @Param limit query int false "Number of top events to return" default(10)
// @Success 200 {object} payloads.EventStatisticsResponse "Event statistics"
// @Failure 400 {object} utils.ErrorResponse "Invalid query parameters"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /event-statistics [get]
func (h *EventHandler) GetEventStatistics(c echo.Context) error {
	ctx := c.Request().Context()
	var req payloads.EventStatisticsRequest

	// Parse startDate
	startDateStr := c.QueryParam("startDate")
	if startDateStr != "" {
		parsedStartDate, err := time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Str("startDate", startDateStr).Msg("Invalid startDate format")
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid startDate format. Please use RFC3339 (e.g., YYYY-MM-DDTHH:MM:SSZ).")
		}
		req.StartDate = &parsedStartDate
	}

	// Parse endDate
	endDateStr := c.QueryParam("endDate")
	if endDateStr != "" {
		parsedEndDate, err := time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Str("endDate", endDateStr).Msg("Invalid endDate format")
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid endDate format. Please use RFC3339 (e.g., YYYY-MM-DDTHH:MM:SSZ).")
		}
		req.EndDate = &parsedEndDate
	}

	// Parse limit
	limitStr := c.QueryParam("limit")
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err != nil || parsedLimit <= 0 {
			log.Ctx(ctx).Warn().Err(err).Str("limit", limitStr).Msg("Invalid limit value")
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid limit value. Must be a positive integer.")
		}
		req.Limit = &parsedLimit
	}

	// Validate the request struct if needed (though individual params are validated above)
	// if err := h.validator.ValidateStruct(req); err != nil {
	//  log.Ctx(ctx).Warn().Err(err).Interface("request", req).Msg("EventStatisticsRequest validation failed")
	// 	return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	// }

	stats, err := h.service.GetEventStatistics(ctx, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get event statistics from service")
		// Consider mapping specific service errors to HTTP status codes
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve event statistics")
	}

	return c.JSON(http.StatusOK, stats)
}

// @Summary Get event statistics for a specific organization
// @Description Retrieves statistics about events for a specific organization, including counts per category (tag) and top N events by participant count.
// @Description Supports optional startDate, endDate (RFC3339 format, e.g., "2023-01-01T00:00:00Z") and limit query parameters.
// @Description Defaults to the last 6 months and top 10 events if parameters are not provided.
// @Tags Events (Organization Statistics)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param startDate query string false "Start date for filtering statistics (RFC3339 format)"
// @Param endDate query string false "End date for filtering statistics (RFC3339 format)"
// @Param limit query int false "Number of top events to return" default(10)
// @Success 200 {object} payloads.EventStatisticsResponse "Event statistics for the organization"
// @Failure 400 {object} utils.ErrorResponse "Invalid path or query parameters"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (user does not have permission to view stats for this organization)"
// @Failure 404 {object} utils.ErrorResponse "Organization not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/event-statistics [get]
func (h *EventHandler) GetEventStatisticsForOrganization(c echo.Context) error {
	ctx := c.Request().Context()

	// Get and validate adminUserID from claims (assuming this endpoint requires some level of auth)
	// The exact permission (e.g., member of org, admin of org) should be checked by the service or a middleware.
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("GetEventStatisticsForOrganization: Failed to get validated claims")
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	_ = claims.UserID // At least ensure user is authenticated. Service will do further checks if needed.

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("orgId", orgIDStr).Msg("Invalid organization ID format")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	var req payloads.EventStatisticsRequest

	// Parse startDate
	startDateStr := c.QueryParam("startDate")
	if startDateStr != "" {
		parsedStartDate, err := time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Str("startDate", startDateStr).Msg("Invalid startDate format")
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid startDate format. Please use RFC3339 (e.g., YYYY-MM-DDTHH:MM:SSZ).")
		}
		req.StartDate = &parsedStartDate
	}

	// Parse endDate
	endDateStr := c.QueryParam("endDate")
	if endDateStr != "" {
		parsedEndDate, err := time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Str("endDate", endDateStr).Msg("Invalid endDate format")
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid endDate format. Please use RFC3339 (e.g., YYYY-MM-DDTHH:MM:SSZ).")
		}
		req.EndDate = &parsedEndDate
	}

	// Parse limit
	limitStr := c.QueryParam("limit")
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err != nil || parsedLimit <= 0 {
			log.Ctx(ctx).Warn().Err(err).Str("limit", limitStr).Msg("Invalid limit value")
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid limit value. Must be a positive integer.")
		}
		req.Limit = &parsedLimit
	}

	stats, err := h.service.GetEventStatisticsForOrganization(ctx, orgID, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to get event statistics for organization from service")
		// Handle common error types
		if strings.Contains(strings.ToLower(err.Error()), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found or no statistics available.")
		} else if strings.Contains(strings.ToLower(err.Error()), "permission denied") || strings.Contains(strings.ToLower(err.Error()), "forbidden") {
			return echo.NewHTTPError(http.StatusForbidden, "Permission denied to view statistics for this organization.")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve event statistics for organization")
	}

	return c.JSON(http.StatusOK, stats)
}

// GetEventVolunteerCount godoc
// @Summary Get volunteer application count for an event
// @Description Retrieves the total number of volunteer applications for a specific event.
// @Tags Events (Public)
// @Produce json
// @Param eventId path string true "Event ID (UUID)" format(uuid)
// @Success 200 {object} payloads.EventVolunteerCountResponse "Successfully retrieved volunteer application count"
// @Failure 400 {object} utils.ErrorResponse "Invalid Event ID format"
// @Failure 404 {object} utils.ErrorResponse "Event not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /events/{eventId}/volunteer-count [get]
func (h *EventHandler) GetEventVolunteerCount(c echo.Context) error {
	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid Event ID format", err)
	}

	count, err := h.service.CountVolunteerApplicationsForEvent(c.Request().Context(), eventID)
	if err != nil {
		// Check if the error is due to event not found (this depends on how the service/query layer handles it)
		// For now, assuming a generic error. Specific error handling (e.g., pgx.ErrNoRows) can be added in the service layer
		// or checked here if the service returns distinguishable errors.
		if strings.Contains(err.Error(), "not found") { // Basic check, can be improved
			return utils.HandleError(c, http.StatusNotFound, "Event not found or no volunteer applications", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Str("eventID", eventIDStr).Msg("Failed to get event volunteer count")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve volunteer application count", err)
	}

	return c.JSON(http.StatusOK, payloads.EventVolunteerCountResponse{
		EventID:                   eventID,
		VolunteerApplicationCount: count,
	})
}

// WithdrawEventApplicationHandler godoc
// @Summary Withdraw an event volunteer application
// @Description Allows the authenticated user to withdraw their own volunteer application for a specific event. The application status must typically be 'pending' or 'approved'.
// @Tags Events (User Volunteering)
// @Produce json
// @Param appId path string true "Application ID (UUID) of the event volunteer application to withdraw"
// @Success 200 {object} payloads.EventVolunteerApplicationResponse "Application withdrawn successfully, returns the updated application."
// @Failure 400 {object} utils.ErrorResponse "Invalid application ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (e.g., application not owned by user, or application not in a withdrawable state)"
// @Failure 404 {object} utils.ErrorResponse "Application not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /api/v1/users/me/event-volunteer-applications/{appId} [patch]
func (h *EventHandler) WithdrawEventApplicationHandler(c echo.Context) error {
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "User ID not found in token")
	}

	appIDStr := c.Param("appId")
	appID, err := uuid.Parse(appIDStr)
	if err != nil {
		log.Ctx(c.Request().Context()).Warn().Err(err).Str("appIdParam", appIDStr).Msg("Invalid event volunteer application ID in path parameter")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid application ID format")
	}

	// No request body is expected for withdrawal as per plan.
	// req := new(payloads.WithdrawEventVolunteerApplicationRequest)
	// if err := c.Bind(req); err != nil {
	// 	 return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	// }

	updatedApp, err := h.service.WithdrawEventApplication(c.Request().Context(), userID, appID)
	if err != nil {
		switch {
		case errors.Is(err, services.ErrVolunteerApplicationNotFound):
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		// Assuming services.ErrApplicationNotWithdrawable is the error returned by eventService for this case.
		// If eventService defines its own error (e.g. services.ErrEventApplicationNotWithdrawable), use that.
		case errors.Is(err, services.ErrApplicationNotWithdrawable), strings.Contains(err.Error(), "application status does not allow withdrawal"):
			// Potentially could be a 403 Forbidden or 409 Conflict depending on precise meaning.
			// Using 403 as per plan for VolunteerHandler.
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		default:
			log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to withdraw event volunteer application")
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to withdraw application")
		}
	}

	return c.JSON(http.StatusOK, updatedApp)
}

// SetEventBannerMediaItemHandler handles PATCH /organizations/{orgId}/events/{eventId}/media/{mediaItemId}/set-banner
// @Summary Set a specific media item as the event banner
// @Description Designates an existing media item as the primary banner image for an event. If another item was banner, it will be unset.
// @Tags Events (Organization Media)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Param mediaItemId path string true "Media Item ID (UUID) to set as banner"
// @Success 200 {object} payloads.MediaItemResponse "Updated media item details (now banner)"
// @Failure 400 {object} utils.ErrorResponse "Invalid IDs format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (no permission)"
// @Failure 404 {object} utils.ErrorResponse "Event or Media Item not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/events/{eventId}/media/{mediaItemId}/set-banner [patch]
func (h *EventHandler) SetEventBannerMediaItemHandler(c echo.Context) error {
	// 1. Extract path parameters
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format: "+err.Error())
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format: "+err.Error())
	}

	mediaItemIDStr := c.Param("mediaItemId")
	mediaItemID, err := uuid.Parse(mediaItemIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid media item ID format: "+err.Error())
	}

	// 2. Get user ID from JWT claims
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}
	actingUserID := claims.UserID

	// 3. Call the service method
	updatedMediaItem, err := h.service.SetBannerForEventMediaItem(c.Request().Context(), eventID, mediaItemID, orgID, actingUserID)
	if err != nil {
		if errors.Is(err, services.ErrEventNotFound) || errors.Is(err, services.ErrEventMediaItemNotFound) {
			log.Ctx(c.Request().Context()).Warn().Err(err).Str("orgID", orgIDStr).Str("eventID", eventIDStr).Str("mediaItemID", mediaItemIDStr).Msg("SetEventBannerMediaItemHandler: Event or media item not found")
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		} else if errors.Is(err, utils.ErrForbidden) { // Assuming a generic Forbidden error from service if permission check fails
			log.Ctx(c.Request().Context()).Warn().Err(err).Str("userID", actingUserID.String()).Msg("SetEventBannerMediaItemHandler: Forbidden")
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("SetEventBannerMediaItemHandler: Service call failed")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to set banner image: "+err.Error())
	}

	// 4. Return successful response
	return c.JSON(http.StatusOK, updatedMediaItem)
}

// GetUserEventVolunteerApplicationDetailsHandler godoc
// @Summary Get details of a specific event volunteer application for the current user
// @Description Retrieves details for a specific event volunteer application submitted by the authenticated user.
// @Tags Event Volunteering (User), User Profile
// @Produce json
// @Param appId path string true "Application ID (UUID)"
// @Success 200 {object} payloads.EventVolunteerApplicationResponse "Application details"
// @Failure 400 {object} utils.ErrorResponse "Invalid application ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not owner of application, though this is implicitly handled by query)"
// @Failure 404 {object} utils.ErrorResponse "Application not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /api/v1/users/me/event-volunteer-applications/{appId} [get]
// @Security ApiKeyAuth
func (h *EventHandler) GetUserEventVolunteerApplicationDetailsHandler(c echo.Context) error {
	ctx := c.Request().Context()
	logger := log.Ctx(ctx)

	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		logger.Warn().Msg("User ID not found in token for GetUserEventVolunteerApplicationDetailsHandler")
		return utils.HandleError(c, http.StatusUnauthorized, "User ID not found in token", errors.New("unauthorized"))
	}

	appIDStr := c.Param("appId")
	appID, err := uuid.Parse(appIDStr)
	if err != nil {
		logger.Warn().Err(err).Str("appId", appIDStr).Msg("Invalid application ID format")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid application ID format", err)
	}

	applicationDetails, serviceErr := h.service.GetUserEventVolunteerApplicationDetails(ctx, userID, appID)
	if serviceErr != nil {
		logger.Error().Err(serviceErr).
			Str("appId", appIDStr).
			Str("userID", userID.String()).
			Msg("Service failed to get user event volunteer application details")

		if errors.Is(serviceErr, services.ErrVolunteerApplicationNotFound) {
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		}
		// Add other specific error handlings if needed, e.g., Forbidden
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve volunteer application details", serviceErr)
	}

	return c.JSON(http.StatusOK, applicationDetails)
}

func (h *EventHandler) GetEventTag(c echo.Context) error {
	tagIDStr := c.Param("tagId")
	tagID, err := uuid.Parse(tagIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid tag ID format.", err)
	}

	eventTag, err := h.service.GetEventTag(c.Request().Context(), tagID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return utils.HandleError(c, http.StatusNotFound, "Event tag not found.", err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to get event tag.", err)
	}

	return c.JSON(http.StatusOK, eventTag)
}

// GetSpecificEventStatistics handles GET /organizations/{orgId}/events/{eventId}/statistics
// @Summary Get detailed statistics for a specific event
// @Description Retrieves detailed statistics for a single event, including participant and volunteer demographics.
// @Tags Events (Organization)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param eventId path string true "Event ID (UUID)"
// @Success 200 {object} payloads.SpecificEventStatisticsResponse "Detailed event statistics"
// @Failure 400 {object} utils.ErrorResponse "Invalid ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden"
// @Failure 404 {object} utils.ErrorResponse "Event not found"
// @Router /organizations/{orgId}/events/{eventId}/statistics [get]
func (h *EventHandler) GetSpecificEventStatistics(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication error: "+err.Error())
	}

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid event ID format")
	}

	stats, err := h.service.GetSpecificEventStatistics(c.Request().Context(), eventID, orgID, claims.UserID)
	if err != nil {
		if errors.Is(err, services.ErrEventNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Event not found")
		}
		if errors.Is(err, services.ErrUserNotMemberOfOrg) {
			return echo.NewHTTPError(http.StatusForbidden, "You do not have permission to view statistics for this event.")
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to get specific event statistics")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve event statistics")
	}

	return c.JSON(http.StatusOK, stats)
}
