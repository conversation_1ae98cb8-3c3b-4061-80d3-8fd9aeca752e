-- Re-add the event_verification_type_key column to the events table
ALTER TABLE events
ADD COLUMN event_verification_type_key TEXT;

-- Optional: Re-add the index if it was previously there and deemed necessary
-- CREATE INDEX IF NOT EXISTS idx_events_event_verification_type_key ON events (event_verification_type_key);

-- Note: Data migration from event_required_verification_types back to a single
-- event_verification_type_key is not straightforward if an event had multiple
-- verification types. This down migration restores the schema but not necessarily
-- the exact previous state if multiple keys were used per event via the other table. 