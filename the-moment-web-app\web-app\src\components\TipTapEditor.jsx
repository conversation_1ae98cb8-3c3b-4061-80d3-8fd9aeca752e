// TipTapEditor.js
import React, { useEffect, useImperativeHandle } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import { Button } from 'antd';
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
} from '@ant-design/icons';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import '../styles/TipTapEditor.css';

const TipTapEditor = React.forwardRef((props, ref) => {
  const { initialContent = '', onChange, placeholder = 'Enter description...' } = props;

  const editor = useEditor({
    extensions: [
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
      }),
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        codeBlock: false,
        blockquote: false,
        hardBreak: false,
      }),
    ],
    content: initialContent,
    onUpdate: ({ editor }) => {
      onChange && onChange(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: 'tiptap-editor-content prose prose-sm sm:prose-base focus:outline-none',
      },
    },
    placeholder,
  });

  useImperativeHandle(ref, () => ({
    getContent: () => {
      if (!editor) return null;
      return editor.getHTML();
    },
    getJSON: () => {
      if (!editor) return null;
      return editor.getJSON();
    },
    isEmpty: () => {
      if (!editor) return true;
      return editor.isEmpty;
    }
  }));

  useEffect(() => {
    if (editor && initialContent && initialContent !== editor.getHTML()) {
      editor.commands.setContent(initialContent);
    }
  }, [initialContent, editor]);

  if (!editor) {
    return null;
  }

  return (
    <div className="tiptap-editor">
      {/* Toolbar */}
      <div className="tiptap-toolbar">
        <Button.Group>
          {/* Bold */}
          <Button
            onClick={() => editor.chain().focus().toggleBold().run()}
            icon={<BoldOutlined />}
            type={editor.isActive('bold') ? 'primary' : 'default'}
          />
          {/* Italic */}
          <Button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            icon={<ItalicOutlined />}
            type={editor.isActive('italic') ? 'primary' : 'default'}
          />
          {/* Underline */}
          <Button
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            icon={<UnderlineOutlined />}
            type={editor.isActive('underline') ? 'primary' : 'default'}
          />
          {/* Strikethrough */}
          <Button
            onClick={() => editor.chain().focus().toggleStrike().run()}
            icon={<StrikethroughOutlined />}
            type={editor.isActive('strike') ? 'primary' : 'default'}
          />
        </Button.Group>

        {/* Headings */}
        <Button.Group style={{ marginLeft: '8px' }}>
          {[1, 2, 3].map((level) => (
            <Button
              key={level}
              onClick={() => editor.chain().focus().toggleHeading({ level }).run()}
              type={editor.isActive('heading', { level }) ? 'primary' : 'default'}
            >
              H{level}
            </Button>
          ))}
        </Button.Group>

        {/* Lists */}
        <Button.Group style={{ marginLeft: '8px' }}>
          {/* Unordered List */}
          <Button
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            icon={<UnorderedListOutlined />}
            type={editor.isActive('bulletList') ? 'primary' : 'default'}
          />
          {/* Ordered List */}
          <Button
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            icon={<OrderedListOutlined />}
            type={editor.isActive('orderedList') ? 'primary' : 'default'}
          />
        </Button.Group>

        {/* Text Alignment */}
        <Button.Group style={{ marginLeft: '8px' }}>
          {/* Align Left */}
          <Button
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            icon={<AlignLeftOutlined />}
            type={editor.isActive({ textAlign: 'left' }) ? 'primary' : 'default'}
          />
          {/* Align Center */}
          <Button
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            icon={<AlignCenterOutlined />}
            type={editor.isActive({ textAlign: 'center' }) ? 'primary' : 'default'}
          />
          {/* Align Right */}
          <Button
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            icon={<AlignRightOutlined />}
            type={editor.isActive({ textAlign: 'right' }) ? 'primary' : 'default'}
          />
        </Button.Group>
      </div>

      {/* Content Area */}
      <div className="prose-container">
        <EditorContent editor={editor} />
      </div>
    </div>
  );
});

export default TipTapEditor;
