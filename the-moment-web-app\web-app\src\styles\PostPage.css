/* PostPage.css */

/* create post page */
.create-post-page {
    padding: 20px;
    min-height: 100vh;
}

.create-post-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #ffffff;
    padding: 10px 24px 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.editor-preview-container {
    display: flex;
    gap: 20px;
    min-width: 600px;
}

.editor,
.preview {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 300px;
    min-height: 500px;
}

.editor .ant-input-textarea,
.preview .markdown-preview {
    flex: 1;
    min-height: 500px;
}

.markdown-preview {
    padding: 15px;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    overflow-y: auto;
}

@media (max-width: 767px) {
    .editor-preview-container {
        flex-direction: column;
    }
}

/* post details page */
.post-details-page {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
}

.post-view-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.post-title {
    font-size: 2.4rem;
    margin-bottom: 16px;
    line-height: 1.3;
    color: #333;
}

.post-meta {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
}

.author-info {
    margin-left: 16px;
    flex: 1;
}

.author-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
    display: block;
}

.post-extra-info {
    display: flex;
    gap: 16px;
    color: #8c8c8c;
    font-size: 14px;
}

.post-content {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    color: #333;
}

/* Headings */
.post-content h1 {
    font-size: 2em;
    margin: 1em 0 0.5em;
    font-weight: 600;
}

.post-content h2 {
    font-size: 1.5em;
    margin: 1em 0 0.5em;
    font-weight: 600;
}

.post-content h3 {
    font-size: 1.25em;
    margin: 1em 0 0.5em;
    font-weight: 600;
}

/* Paragraphs and Lists */
.post-content p {
    font-size: 1.1em;
}

/* Lists - Enhanced styles */
.post-content ul {
    list-style-type: disc;
    padding-left: 1.625em;
    margin: 1.25em 0;
}

.post-content ol {
    list-style-type: decimal;
    padding-left: 1.625em;
    margin: 1.25em 0;
}

.post-content ul > li,
.post-content ol > li {
    margin: 0.5em 0;
    padding-left: 0.375em;
}

.post-content ul > li::marker {
    color: #666666;
}

.post-content ol > li::marker {
    color: #666666;
}

/* Nested lists */
.post-content ul ul,
.post-content ul ol,
.post-content ol ul,
.post-content ol ol {
    margin: 0.75em 0;
}

/* Links */
.post-content a {
    color: #1890ff;
    text-decoration: none;
}

.post-content a:hover {
    text-decoration: underline;
}

/* Text Styles */
.post-content strong {
    font-weight: 600;
    color: #222;
}

.post-content em {
    font-style: italic;
}

/* Text Alignment */
.post-content [style*="text-align: center"] {
    text-align: center;
}

.post-content [style*="text-align: right"] {
    text-align: right;
}

/* Attachments section styles */
.attachments-list {
    margin-top: 16px;
}

.attachments-list .ant-list-item {
    padding: 16px;
}

.attachments-list .ant-list-item-meta-avatar {
    align-self: flex-start;
    margin-top: 4px;
}

.attachments-list .ant-list-item-meta-title {
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 4px;
}

.attachments-list .ant-list-item-meta-description {
    font-size: 14px;
    color: #262626;
}

.attachments-list .ant-list-item-action {
    margin-left: 48px;
}

/* Loading skeleton styles */
.attachments-skeleton {
    margin-top: 24px;
}

.attachments-skeleton .ant-list-item {
    padding: 16px;
}

.attachments-skeleton .ant-skeleton-input {
    width: 100%;
}

/* Responsive styles */
@media (max-width: 576px) {
    .post-details-page {
        padding: 16px;
    }

    .post-view-container {
        padding: 16px;
    }

    .post-extra-info {
        flex-direction: column;
        gap: 8px;
    }

    .attachments-list .ant-list-item {
        padding: 12px;
    }

    .attachments-list .ant-list-item-action {
        margin-left: 0;
        margin-top: 12px;
    }

    .attachments-list .ant-list-item-meta-avatar {
        margin-right: 12px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    
    .post-content h1 {
        font-size: 1.75em;
    }
    
    .post-content h2 {
        font-size: 1.35em;
    }
    
    .post-content h3 {
        font-size: 1.15em;
    }
}