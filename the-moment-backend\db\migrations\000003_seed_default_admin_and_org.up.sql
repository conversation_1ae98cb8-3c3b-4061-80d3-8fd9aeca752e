-- Seed a default superadmin user
-- IMPORTANT: The hashed_password below is an EXAMPLE for "SystemPassword123!".
-- You MUST replace it with a correctly generated bcrypt hash for your desired password.
-- Example bcrypt hash for "SystemPassword123!": '$2a$10$9BqY9E9YgXWZzXtNzZ7NnO3Z7Q2wz8W2qXWqXyJ5ZqP5qZ.Y7Nn5K'
INSERT INTO users (
    id,
    display_name,
    email,
    email_verified_at,
    hashed_password,
    is_staff,
    phone_otp_channel,
    interface_language,
    communication_language,
    enable_app_notifications,
    enable_email_notifications,
    created_at,
    updated_at
) VALUES (
    '00000000-0000-0000-0000-000000000001',
    'Super Administrator',
    '<EMAIL>',
    CURRENT_TIMESTAMP,
    '$2a$12$v6MjHc6me1Iidicch8oFD.eIqbzpaRmi26pPgy9v7PxnBzgEeVb7S', -- ### REPLACE THIS HASH ###
    TRUE,
    'email', -- Defaulting, less critical if primary login is email/password
    'en',
    'en',
    TRUE,
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (id) DO NOTHING;

-- Seed a Default Organization owned by the superadmin
-- Note: country_code is omitted here as it's not in the current organizations table schema.
-- If you add country_code to the table, include it in this INSERT statement.
INSERT INTO organizations (
    id,
    name,
    description,
    owner_user_id,
    is_default_org,
    created_at,
    updated_at
) VALUES (
    '00000000-0000-0000-0000-000000000002',
    'Default Organization',
    'The global default organization for all users.',
    '00000000-0000-0000-0000-000000000001', -- Superadmin user ID
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (id) DO NOTHING; -- Assumes the partial unique index on (is_default_org) WHERE is_default_org = TRUE handles uniqueness for the default org.
                               -- If is_default_org is the sole conflict target, use ON CONFLICT (is_default_org) WHERE is_default_org = TRUE DO NOTHING;
                               -- Or use ON CONFLICT (name) DO NOTHING if name is unique and preferred for conflict resolution.

-- Add the superadmin as a member of the Default Organization with 'owner' role
INSERT INTO user_organization_memberships (
    user_id,
    organization_id,
    role,
    is_active,
    notifications_enabled,
    joined_at
) VALUES (
    '00000000-0000-0000-0000-000000000001', -- Superadmin user ID
    '00000000-0000-0000-0000-000000000002', -- Default Organization ID
    'owner',
    TRUE,
    TRUE,
    CURRENT_TIMESTAMP
) ON CONFLICT (user_id, organization_id) DO NOTHING; 