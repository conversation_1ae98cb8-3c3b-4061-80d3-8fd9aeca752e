import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Input,
  Select,
  DatePicker,
  Button,
  Col,
} from 'antd';
import {
  SearchOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from 'react-responsive';
import { postService } from '../services/postService';
import { useAuth } from '../contexts/AuthContext';
import { useOrganization, ALL_ORGANIZATION_ID } from '../contexts/OrganizationContext';

const { Option } = Select;
const { RangePicker } = DatePicker;

// Helper function to get tag name based on current language
const getTagName = (tag, language) => {
  if (!tag) return '';
  if (language.startsWith('zh')) {
    if (language.includes('HK')) {
      return tag.name_zh_hk || tag.name_en || '';
    } else {
      return tag.name_zh_cn || tag.name_en || '';
    }
  } else {
    return tag.name_en || '';
  }
};

const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};

const PostFilter = ({ onFilter, showStatusFilter = true }) => {
  const { t, i18n } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const debouncedSearchText = useDebounce(searchText, 500);
  const [dateRange, setDateRange] = useState(null);
  const [selectedPostTag, setSelectedPostTag] = useState(null);
  const [availableTags, setAvailableTags] = useState([]);
  const [loadingTags, setLoadingTags] = useState(false);
  const [status, setStatus] = useState(null);

  const isFirstRender = useRef(true);
  const isMobile = useMediaQuery({ maxWidth: 768 });

  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const isAdminView = !!(user?.role === 'admin' || user?.role === 'super_admin');

  const fetchTags = useCallback(async () => {
    setLoadingTags(true);
    try {
      const tagsResponse = await postService.listPostTags();
      const tags = Array.isArray(tagsResponse) ? tagsResponse : [];
      setAvailableTags(tags.filter(tag => tag.id && (tag.name_en || tag.name_zh_hk || tag.name_zh_cn)));
    } catch (error) {
      console.error("Failed to fetch post tags for filter", error);
      setAvailableTags([]);
    } finally {
      setLoadingTags(false);
    }
  }, []);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  // Simplified tag handling - no need for complex language switching logic
  useEffect(() => {
    // Check if selected tag still exists in available tags
    if (selectedPostTag && availableTags.length > 0) {
      const stillExists = availableTags.some(tag => tag.id === selectedPostTag);
      if (!stillExists) {
        setSelectedPostTag(null);
      }
    }
  }, [availableTags, selectedPostTag]);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    
    const filtersToApply = {
      searchText: debouncedSearchText,
      dateRange: dateRange ? [dayjs(dateRange[0]).startOf('day'), dayjs(dateRange[1]).endOf('day')] : null,
      tagIds: selectedPostTag ? [selectedPostTag] : [],
    };
    if (isAdminView && showStatusFilter && status) {
      filtersToApply.status = status;
    }
    onFilter(filtersToApply);
  }, [debouncedSearchText, dateRange, selectedPostTag, status, isAdminView, onFilter, showStatusFilter]);

  const handleSearchTextChange = useCallback((e) => {
    setSearchText(e.target.value);
  }, []);

  const handleDateRangeChange = useCallback((dates) => {
    setDateRange(dates);
  }, []);

  const handleSelectedTagChange = useCallback((value) => {
    setSelectedPostTag(value);
  }, []);
  
  const handleStatusChange = useCallback((value) => {
    setStatus(value);
  }, []);

  const handleClearFilters = useCallback(() => {
    setSearchText('');
    setDateRange(null);
    setSelectedPostTag(null);
    setStatus(null);
  }, []);

  return (
    <div className={`event-filter-container p-4 mb-4 border rounded-lg bg-white shadow-sm ${isMobile ? 'flex-col space-y-4' : 'flex flex-wrap items-center gap-4'}`}>
      <Input
        className={isMobile ? 'w-full' : 'flex-1 min-w-[200px]'}
        placeholder={t('postFilter.searchPlaceholder', 'Search posts...')}
        prefix={<SearchOutlined />}
        value={searchText}
        onChange={handleSearchTextChange}
        allowClear
      />
      <Select
        className={isMobile ? 'w-full' : 'min-w-[200px]'}
        placeholder={t('postFilter.selectTagPlaceholder', 'Select tag...')}
        onChange={handleSelectedTagChange}
        value={selectedPostTag}
        loading={loadingTags}
        allowClear
      >
        {availableTags.map(tag => (
          <Option key={tag.id} value={tag.id}>
            {getTagName(tag, i18n.language)}
          </Option>
        ))}
      </Select>
      <RangePicker
        className={isMobile ? 'w-full' : 'min-w-[280px]'}
        onChange={handleDateRangeChange}
        value={dateRange}
        placeholder={[t('eventFilter.startDate', 'Start Date'), t('eventFilter.endDate', 'End Date')]}
      />
      {isAdminView && showStatusFilter && (
          <Select
            className={isMobile ? 'w-full' : 'min-w-[180px]'}
            placeholder={t('postFilter.statusPlaceholder', 'Select status')}
            onChange={handleStatusChange}
            value={status}
            allowClear
          >
            <Option value="published">{t('adminPosts.status.published', 'Published')}</Option>
            <Option value="draft">{t('adminPosts.status.draft', 'Draft')}</Option>
            <Option value="hidden">{t('adminPosts.status.hidden', 'Hidden')}</Option>
          </Select>
      )}
      <Button onClick={handleClearFilters}>
        {t('eventFilter.clearFilters', 'Clear Filters')}
      </Button>
    </div>
  );
};

export default PostFilter; 