// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: jobs.sql

package db

import (
	"context"

	"github.com/google/uuid"
)

const createJob = `-- name: CreateJob :one
INSERT INTO jobs (job_type, payload, run_at, max_retries)
VALUES ($1, $2, CURRENT_TIMESTAMP + ($3::TEXT || ' seconds')::INTERVAL, $4)
RETURNING id, job_type, payload, status, max_retries, attempts, last_error, run_at, created_at, updated_at
`

type CreateJobParams struct {
	JobType    string `db:"job_type" json:"job_type"`
	Payload    []byte `db:"payload" json:"payload"`
	Column3    string `db:"column_3" json:"column_3"`
	MaxRetries int32  `db:"max_retries" json:"max_retries"`
}

func (q *Queries) CreateJob(ctx context.Context, arg CreateJobParams) (Job, error) {
	row := q.db.QueryRow(ctx, createJob,
		arg.JobType,
		arg.Payload,
		arg.Column3,
		arg.<PERSON>et<PERSON>,
	)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Payload,
		&i.Status,
		&i.MaxRetries,
		&i.Attempts,
		&i.LastError,
		&i.RunAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteJob = `-- name: DeleteJob :exec
DELETE FROM jobs WHERE id = $1
`

func (q *Queries) DeleteJob(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteJob, id)
	return err
}

const getNextPendingJob = `-- name: GetNextPendingJob :one
SELECT id, job_type, payload, attempts, max_retries
FROM jobs
WHERE status = 'pending' AND run_at <= CURRENT_TIMESTAMP AND attempts < max_retries
ORDER BY created_at ASC
LIMIT 1
FOR UPDATE SKIP LOCKED
`

type GetNextPendingJobRow struct {
	ID         uuid.UUID `db:"id" json:"id"`
	JobType    string    `db:"job_type" json:"job_type"`
	Payload    []byte    `db:"payload" json:"payload"`
	Attempts   int32     `db:"attempts" json:"attempts"`
	MaxRetries int32     `db:"max_retries" json:"max_retries"`
}

func (q *Queries) GetNextPendingJob(ctx context.Context) (GetNextPendingJobRow, error) {
	row := q.db.QueryRow(ctx, getNextPendingJob)
	var i GetNextPendingJobRow
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Payload,
		&i.Attempts,
		&i.MaxRetries,
	)
	return i, err
}

const incrementJobAttempts = `-- name: IncrementJobAttempts :one
UPDATE jobs
SET attempts = attempts + 1, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, job_type, payload, status, max_retries, attempts, last_error, run_at, created_at, updated_at
`

func (q *Queries) IncrementJobAttempts(ctx context.Context, id uuid.UUID) (Job, error) {
	row := q.db.QueryRow(ctx, incrementJobAttempts, id)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Payload,
		&i.Status,
		&i.MaxRetries,
		&i.Attempts,
		&i.LastError,
		&i.RunAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const markJobRunning = `-- name: MarkJobRunning :one
UPDATE jobs
SET status = 'running', attempts = attempts + 1, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND status = 'pending' -- Ensure it was pending before marking as running
RETURNING id, job_type, payload, status, max_retries, attempts, last_error, run_at, created_at, updated_at
`

func (q *Queries) MarkJobRunning(ctx context.Context, id uuid.UUID) (Job, error) {
	row := q.db.QueryRow(ctx, markJobRunning, id)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Payload,
		&i.Status,
		&i.MaxRetries,
		&i.Attempts,
		&i.LastError,
		&i.RunAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const retryJob = `-- name: RetryJob :one
UPDATE jobs
SET status = 'pending',
    attempts = attempts + 1,
    last_error = $2,
    run_at = CURRENT_TIMESTAMP + ($3::TEXT || ' seconds')::INTERVAL, -- $3 is delay in seconds
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, job_type, payload, status, max_retries, attempts, last_error, run_at, created_at, updated_at
`

type RetryJobParams struct {
	ID        uuid.UUID `db:"id" json:"id"`
	LastError *string   `db:"last_error" json:"last_error"`
	Column3   string    `db:"column_3" json:"column_3"`
}

func (q *Queries) RetryJob(ctx context.Context, arg RetryJobParams) (Job, error) {
	row := q.db.QueryRow(ctx, retryJob, arg.ID, arg.LastError, arg.Column3)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Payload,
		&i.Status,
		&i.MaxRetries,
		&i.Attempts,
		&i.LastError,
		&i.RunAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateJobStatus = `-- name: UpdateJobStatus :one
UPDATE jobs
SET status = $2, last_error = $3, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, job_type, payload, status, max_retries, attempts, last_error, run_at, created_at, updated_at
`

type UpdateJobStatusParams struct {
	ID        uuid.UUID `db:"id" json:"id"`
	Status    JobStatus `db:"status" json:"status"`
	LastError *string   `db:"last_error" json:"last_error"`
}

func (q *Queries) UpdateJobStatus(ctx context.Context, arg UpdateJobStatusParams) (Job, error) {
	row := q.db.QueryRow(ctx, updateJobStatus, arg.ID, arg.Status, arg.LastError)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Payload,
		&i.Status,
		&i.MaxRetries,
		&i.Attempts,
		&i.LastError,
		&i.RunAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
