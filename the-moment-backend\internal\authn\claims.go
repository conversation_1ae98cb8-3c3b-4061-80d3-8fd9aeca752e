package authn

import (
	"Membership-SAAS-System-Backend/internal/token"
	"errors"
	"fmt"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// GetValidatedClaims retrieves the AppClaims from the Echo context.
// It assumes a JWT middleware (e.g., echo-jwt with NewClaimsFunc) has already validated
// the token and stored the parsed *jwt.Token (which contains *token.AppClaims) in c.Get("user").
func GetValidatedClaims(c echo.Context) (*token.AppClaims, error) {
	logger := log.Ctx(c.Request().Context())
	jwtTokenRaw := c.Get("user") // Default key used by echo-jwt middleware

	// logger.Debug().Interface("jwt_token_raw_from_context", jwtTokenRaw).Msg("Raw value from c.Get(\"user\")")

	if jwtTokenRaw == nil {
		authHeader := c.Request().Header.Get(echo.HeaderAuthorization)
		logger.Warn().Str("authorization_header", authHeader).Msg("c.Get(\"user\") returned nil. JWT middleware might not have run or failed without erroring out.")
		return nil, errors.New("no JWT token found in context, check JWT middleware setup")
	}

	jwtToken, ok := jwtTokenRaw.(*jwt.Token) // Correctly get the *jwt.Token
	if !ok {
		logger.Warn().Str("actual_type", fmt.Sprintf("%T", jwtTokenRaw)).Msg("Value in context ('user') is not of type *jwt.Token")
		return nil, fmt.Errorf("token in context is not of expected type *jwt.Token, got %T", jwtTokenRaw)
	}

	claims, ok := jwtToken.Claims.(*token.AppClaims) // Extract *token.AppClaims from jwtToken.Claims
	if !ok {
		logger.Warn().Str("actual_claims_type", fmt.Sprintf("%T", jwtToken.Claims)).Msg("Claims within JWT token are not of type *token.AppClaims")
		return nil, fmt.Errorf("claims in JWT token are not of expected type *token.AppClaims, got %T", jwtToken.Claims)
	}

	// Check if UserID within claims is valid
	if claims.UserID == uuid.Nil {
		logger.Warn().Msg("UserID in claims is uuid.Nil")
		return nil, errors.New("invalid user ID in claims (Nil UUID)")
	}

	// For now, we trust the middleware has done basic validation (signature, expiry).

	return claims, nil
}

// GetUserIDFromContext retrieves the UserID from the validated JWT claims in the Echo context.
// It returns uuid.Nil if claims are not found or UserID cannot be parsed.
func GetUserIDFromContext(c echo.Context) uuid.UUID {
	claims, err := GetValidatedClaims(c)
	if err != nil {
		// Log the error for debugging, but the function signature returns uuid.Nil on failure
		// Check if context is nil before trying to get logger from it
		if c != nil && c.Request() != nil && c.Request().Context() != nil {
			log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Failed to get validated claims for UserID extraction")
		} else {
			// Fallback logger if context is not fully available
			log.Warn().Err(err).Msg("Failed to get validated claims (context unavailable) for UserID extraction")
		}
		return uuid.Nil
	}
	return claims.UserID
}

// GetOptionalValidatedClaims attempts to retrieve AppClaims from the Echo context.
// It differs from GetValidatedClaims by not returning an error if the token or claims are simply missing.
// It returns (nil, nil) if no token/user context is found (e.g. JWT middleware didn't set c.Get("user")).
// It returns (nil, error) ONLY if a token is present but malformed or its claims are of an unexpected type.
func GetOptionalValidatedClaims(c echo.Context) (*token.AppClaims, error) {
	logger := log.Ctx(c.Request().Context())
	jwtTokenRaw := c.Get("user") // Default key used by echo-jwt middleware

	if jwtTokenRaw == nil {
		// No token found in context. This is not an error for an optional claim retrieval.
		logger.Debug().Msg("No JWT token found in context for optional claims retrieval (c.Get(\"user\") is nil).")
		return nil, nil
	}

	jwtToken, ok := jwtTokenRaw.(*jwt.Token)
	if !ok {
		logger.Warn().Str("actual_type", fmt.Sprintf("%T", jwtTokenRaw)).Msg("Value in context ('user') is not of type *jwt.Token during optional claims retrieval.")
		// This is an unexpected state if a token was supposed to be set by middleware.
		return nil, fmt.Errorf("token in context is not of expected type *jwt.Token, got %T", jwtTokenRaw)
	}

	claims, ok := jwtToken.Claims.(*token.AppClaims)
	if !ok {
		logger.Warn().Str("actual_claims_type", fmt.Sprintf("%T", jwtToken.Claims)).Msg("Claims within JWT token are not of type *token.AppClaims during optional claims retrieval.")
		// This is an unexpected state if claims were set but are wrong type.
		return nil, fmt.Errorf("claims in JWT token are not of expected type *token.AppClaims, got %T", jwtToken.Claims)
	}

	// Check if UserID within claims is valid if claims are present.
	// A token with a nil UserID after validation is problematic.
	if claims.UserID == uuid.Nil {
		logger.Warn().Msg("UserID in claims is uuid.Nil during optional claims retrieval.")
		// Consider this a form of malformed/invalid claim for this function's purpose.
		return nil, errors.New("invalid user ID in claims (Nil UUID) during optional retrieval")
	}

	// If we reached here, claims were found and are of the correct type with a valid UserID.
	logger.Debug().Str("user_id", claims.UserID.String()).Msg("Successfully retrieved optional validated claims.")
	return claims, nil
}
