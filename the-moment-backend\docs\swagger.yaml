basePath: /api/v1
definitions:
  Membership-SAAS-System-Backend_internal_payloads.APIUserStats:
    description: Provides a summary of user activity, including event participation
      and engagement over time.
    properties:
      monthlyAttendedEvents:
        description: Statistics for events attended over the last 6 months.
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.MonthlyAttendedEvent'
        type: array
      topAttendedEventTags:
        description: Top 5 most frequently attended event tags over the last 6 months.
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.TopAttendedEventTag'
        type: array
      totalEvents:
        description: Total number of events the user has attended.
        example: 50
        type: integer
      userJoinedAt:
        description: Timestamp when the user registered.
        example: "2023-01-15T10:00:00Z"
        type: string
      volunteerEvents:
        description: Number of events the user has volunteered for.
        example: 15
        type: integer
    type: object
  Membership-SAAS-System-Backend_internal_payloads.AdminReviewVerificationRequest:
    properties:
      admin_notes:
        type: string
      status:
        description: '"approved" or "rejected"'
        type: string
    required:
    - status
    type: object
  Membership-SAAS-System-Backend_internal_payloads.AdminReviewVolunteerApplicationRequest:
    properties:
      admin_notes:
        example: Looks good.
        type: string
      status:
        enum:
        - approved
        - rejected
        example: approved
        type: string
    required:
    - status
    type: object
  Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse:
    properties:
      communication_language:
        type: string
      created_at:
        type: string
      display_name:
        type: string
      email:
        type: string
      email_verified_at:
        type: string
      enable_app_notifications:
        type: boolean
      enable_email_notifications:
        type: boolean
      enable_sms_notifications:
        type: boolean
      enable_whatsapp_notifications:
        type: boolean
      id:
        type: string
      interface_language:
        type: string
      is_staff:
        type: boolean
      phone:
        type: string
      phone_otp_channel:
        type: string
      phone_verified_at:
        type: string
      profile_picture_url:
        type: string
      updated_at:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.ApplyVolunteerRequest:
    properties:
      motivation:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CategoryStatItem:
    properties:
      name:
        description: Tag Name
        type: string
      value:
        description: Count
        type: integer
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CreateEventRequest:
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CreateEventTagRequest:
    properties:
      description:
        type: string
      language_code:
        enum:
        - en
        - zh_HK
        - zh_CN
        type: string
      tag_name:
        maxLength: 100
        minLength: 1
        type: string
    required:
    - language_code
    - tag_name
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CreateOrganizationFolderRequest:
    properties:
      folder_name:
        maxLength: 255
        minLength: 1
        type: string
      parent_folder_id:
        description: Optional
        type: string
    required:
    - folder_name
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CreateOrganizationRequest:
    description: Request body for creating a new organization.
    properties:
      description:
        description: Optional description of the organization
        example: We do great things!
        maxLength: 500
        type: string
      image_url:
        description: Optional URL for the organization's image/logo
        example: http://example.com/logo.png
        maxLength: 512
        type: string
      is_default:
        example: false
        type: boolean
      name:
        description: Name of the organization
        example: My Awesome Org
        maxLength: 100
        minLength: 2
        type: string
      owner_user_id:
        example: 123e4567-e89b-12d3-a456-************
        type: string
      theme_color:
        description: Optional theme color for the organization (e.g., hex code)
        example: '#FF5733'
        maxLength: 50
        type: string
    required:
    - name
    - owner_user_id
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CreatePostRequest:
    properties:
      content:
        items:
          type: integer
        type: array
      published_at:
        type: string
      slug:
        maxLength: 255
        type: string
      status:
        description: Added 'hidden'
        enum:
        - draft
        - published
        - hidden
        type: string
      title:
        maxLength: 255
        minLength: 3
        type: string
    required:
    - title
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CreatePostTagRequest:
    properties:
      description:
        type: string
      language_code:
        maxLength: 10
        minLength: 2
        type: string
      tag_name:
        maxLength: 100
        minLength: 1
        type: string
    required:
    - language_code
    - tag_name
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CreateResourceRequest:
    properties:
      description:
        type: string
      published_at:
        type: string
      slug:
        description: Optional, can be auto-generated
        maxLength: 255
        type: string
      status:
        enum:
        - draft
        - published
        type: string
      title:
        maxLength: 255
        minLength: 3
        type: string
      visibility:
        enum:
        - public
        - org_only
        type: string
    required:
    - title
    type: object
  Membership-SAAS-System-Backend_internal_payloads.CreateStaffUserRequest:
    properties:
      communication_language:
        type: string
      display_name:
        maxLength: 50
        minLength: 2
        type: string
      email:
        type: string
      interface_language:
        type: string
      password:
        description: Password will be hashed by the service
        minLength: 8
        type: string
    required:
    - display_name
    - email
    - password
    type: object
  Membership-SAAS-System-Backend_internal_payloads.ErrorResponse:
    properties:
      error:
        description: Optional more specific error
        example: Bad Request
        type: string
      message:
        example: Invalid input
        type: string
      statusCode:
        example: 400
        type: integer
    type: object
  Membership-SAAS-System-Backend_internal_payloads.EventCheckInRequest:
    description: Request body for marking a user's attendance at an event.
    properties:
      user_id:
        description: The UUID of the user to check in.
        example: 123e4567-e89b-12d3-a456-************
        type: string
    required:
    - user_id
    type: object
  Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse:
    properties:
      admin_notes_on_registration:
        type: string
      attended_at:
        type: string
      cancellation_reason_by_user:
        type: string
      check_in_by_user_id:
        description: ID of the user (staff/volunteer) who performed the check-in for
          this registration (participant or volunteer).
        type: string
      check_in_method:
        description: Method used for check-in (e.g., qr_scan, manual_staff, self_check_in).
        type: string
      created_at:
        type: string
      event_contact_email:
        type: string
      event_contact_phone:
        type: string
      event_description:
        type: string
      event_end_time:
        type: string
      event_id:
        type: string
      event_location_full_address:
        type: string
      event_location_online_url:
        type: string
      event_location_type:
        type: string
      event_organization_id:
        type: string
      event_organization_name:
        type: string
      event_price:
        type: string
      event_start_time:
        type: string
      event_status:
        type: string
      event_title:
        type: string
      id:
        type: string
      payment_status:
        $ref: '#/definitions/db.PaymentStatusType'
      registered_at:
        type: string
      registered_count:
        type: integer
      registration_role:
        $ref: '#/definitions/db.EventRegistrationRoleType'
      status:
        $ref: '#/definitions/db.EventRegistrationStatusType'
      updated_at:
        type: string
      user_display_name:
        type: string
      user_email:
        type: string
      user_id:
        type: string
      user_phone:
        type: string
      waitlist_priority:
        type: string
      waitlisted_count:
        type: integer
    type: object
  Membership-SAAS-System-Backend_internal_payloads.EventResponse:
    properties:
      attended_count:
        type: integer
      contact_email:
        type: string
      contact_phone:
        type: string
      created_at:
        type: string
      created_by_user_id:
        type: string
      current_user_registration_id:
        type: string
      current_user_registration_status:
        type: string
      current_user_volunteer_application_id:
        type: string
      current_user_volunteer_status:
        type: string
      end_time:
        type: string
      event_verification_type_key:
        type: string
      government_funding_keys:
        items:
          type: string
        type: array
      id:
        type: string
      jsonContent:
        items:
          type: integer
        type: array
      location_full_address:
        description: Consolidated address
        type: string
      location_online_url:
        type: string
      location_type:
        type: string
      media_items:
        description: Added for generic media
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse'
        type: array
      mediaeventitem:
        items:
          type: string
        type: array
      organization_id:
        type: string
      organization_name:
        type: string
      participant_limit:
        type: integer
      price:
        type: string
      published_at:
        type: string
      registered_count:
        type: integer
      required_verification_type_keys:
        items:
          type: string
        type: array
      requires_approval_for_registration:
        type: boolean
      start_time:
        type: string
      status:
        type: string
      tags:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.TagResponse'
        type: array
      title:
        type: string
      updated_at:
        type: string
      waitlist_limit:
        type: integer
      waitlisted_count:
        type: integer
    type: object
  Membership-SAAS-System-Backend_internal_payloads.EventStatisticsResponse:
    properties:
      categoryStats:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CategoryStatItem'
        type: array
      topEvents:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.TopEventItem'
        type: array
    type: object
  Membership-SAAS-System-Backend_internal_payloads.EventVolunteerApplicationResponse:
    properties:
      admin_review_notes:
        type: string
      application_notes_by_user:
        type: string
      applied_at:
        type: string
      created_at:
        type: string
      event_id:
        type: string
      event_start_time:
        type: string
      event_title:
        type: string
      id:
        type: string
      organization_id:
        type: string
      organization_name:
        type: string
      reviewed_at:
        type: string
      reviewed_by_user_id:
        type: string
      reviewer_display_name:
        type: string
      status:
        $ref: '#/definitions/db.ApplicationStatusEnum'
      updated_at:
        type: string
      user_display_name:
        type: string
      user_email:
        type: string
      user_id:
        type: string
      user_phone:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.InitiatePhoneChangeRequest:
    properties:
      client_id:
        description: |-
          For PKCE flow, if AuthnService requires it for this flow type for consistency
          However, for an already authenticated user changing phone, PKCE might be optional for this specific sub-flow.
          The current AuthnService.InitiatePhoneChangeOTP generates internal PKCE.
          ClientID, State are good for flow correlation even without full client-side PKCE.
        type: string
      new_phone_number:
        type: string
      phone_otp_channel:
        description: 'Added: e.g., "sms", "whatsapp"'
        type: string
      state:
        description: Client generated state
        type: string
    required:
    - client_id
    - new_phone_number
    - state
    type: object
  Membership-SAAS-System-Backend_internal_payloads.InitiatePhoneChangeResponse:
    properties:
      expires_in_sec:
        description: Added
        type: integer
      flow_id:
        type: string
      message:
        type: string
      otp_channel:
        description: Added
        type: string
      state:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.LogoutRequestPayload:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse:
    properties:
      file_name:
        type: string
      file_path:
        type: string
      file_size:
        type: integer
      file_type:
        type: string
      id:
        type: string
      uploaded_at:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.MonthlyAttendedEvent:
    properties:
      count:
        example: 5
        type: integer
      month:
        description: YYYY-MM
        example: 2023-08
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.OrgSlimResponse:
    description: Minimal details of an organization.
    properties:
      id:
        description: Unique identifier for the organization
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      name:
        description: Name of the organization
        example: Example Community
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse:
    properties:
      created_at:
        type: string
      file_name:
        type: string
      file_path:
        description: Public URL or identifier
        type: string
      file_size:
        type: integer
      file_type:
        type: string
      id:
        type: string
      is_folder:
        type: boolean
      organization_id:
        type: string
      parent_folder_id:
        type: string
      updated_at:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse:
    description: Full details of an organization.
    properties:
      created_at:
        description: Timestamp when the organization was created
        type: string
      description:
        description: Optional description of the organization
        example: A community for developers.
        type: string
      id:
        description: Unique identifier for the organization
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      image_url:
        description: URL of the organization's logo or banner image
        example: http://example.com/logo.png
        type: string
      is_default_org:
        description: Indicates if this is a default organization (e.g., for system
          use)
        example: false
        type: boolean
      name:
        description: Name of the organization
        example: Example Community
        type: string
      owner_user_id:
        description: ID of the user who owns the organization
        example: 123e4567-e89b-12d3-a456-************
        type: string
      status:
        description: The current status of the organization (e.g., pending_setup,
          active, suspended).
        example: active
        type: string
      theme_color:
        description: Primary theme color for the organization (e.g., 'red', 'blue',
          '#FF0000')
        example: '#007bff'
        type: string
      updated_at:
        description: Timestamp when the organization was last updated
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse:
    properties:
      data: {}
      page:
        type: integer
      page_size:
        type: integer
      total_items:
        type: integer
      total_pages:
        type: integer
    type: object
  Membership-SAAS-System-Backend_internal_payloads.PaginatedUserVerificationRequestsResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestAdminListResponse'
        type: array
      pagination:
        $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.Pagination'
    type: object
  Membership-SAAS-System-Backend_internal_payloads.PaginatedUsersResponse:
    properties:
      limit:
        type: integer
      page:
        type: integer
      total_count:
        type: integer
      users:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse'
        type: array
    type: object
  Membership-SAAS-System-Backend_internal_payloads.Pagination:
    properties:
      current_page:
        type: integer
      limit:
        type: integer
      total_items:
        type: integer
      total_pages:
        type: integer
    type: object
  Membership-SAAS-System-Backend_internal_payloads.PostMediaItemResponse:
    properties:
      file_name:
        type: string
      file_path:
        description: Public URL
        type: string
      file_size:
        type: integer
      file_type:
        type: string
      id:
        type: string
      post_id:
        type: string
      uploaded_at:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.PostResponse:
    properties:
      author_display_name:
        type: string
      content:
        items:
          type: integer
        type: array
      created_at:
        type: string
      id:
        type: string
      media_items:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostMediaItemResponse'
        type: array
      organization_id:
        type: string
      published_at:
        type: string
      slug:
        type: string
      status:
        type: string
      tags:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse'
        type: array
      title:
        type: string
      updated_at:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.PostTagResponse:
    properties:
      created_at:
        type: string
      description:
        type: string
      id:
        type: string
      language_code:
        type: string
      tag_name:
        type: string
      updated_at:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.PostTagsListResponse:
    properties:
      tags:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse'
        type: array
    type: object
  Membership-SAAS-System-Backend_internal_payloads.PublicEventResponse:
    properties:
      contact_email:
        type: string
      contact_phone:
        type: string
      current_user_registration_id:
        type: string
      current_user_registration_status:
        type: string
      current_user_volunteer_application_id:
        type: string
      current_user_volunteer_status:
        type: string
      end_time:
        type: string
      event_verification_type_key:
        type: string
      government_funding_keys:
        items:
          type: string
        type: array
      id:
        type: string
      jsonContent:
        items:
          type: integer
        type: array
      location_full_address:
        description: Consolidated address
        type: string
      location_online_url:
        type: string
      location_type:
        type: string
      media_items:
        description: Added for generic media
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse'
        type: array
      mediaeventitem:
        items:
          type: string
        type: array
      organization_id:
        type: string
      organization_name:
        type: string
      participant_limit:
        type: integer
      price:
        type: string
      published_at:
        description: Added for scheduled publishing
        type: string
      start_time:
        type: string
      status:
        type: string
      tags:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.TagResponse'
        type: array
      title:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.RegisterForEventRequest:
    description: Request body for registering for an event.
    properties:
      event_id:
        description: The UUID of the event to register for.
        example: 123e4567-e89b-12d3-a456-************
        type: string
    required:
    - event_id
    type: object
  Membership-SAAS-System-Backend_internal_payloads.ResourceFileResponse:
    properties:
      description:
        description: Optional description of the file.
        example: This is a detailed description of the file.
        type: string
      file_name:
        type: string
      file_path:
        description: Public URL
        type: string
      file_size:
        type: integer
      file_type:
        type: string
      id:
        type: string
      resource_id:
        type: string
      uploaded_at:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.ResourceResponse:
    properties:
      created_at:
        type: string
      description:
        type: string
      files:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceFileResponse'
        type: array
      id:
        type: string
      organization:
        allOf:
        - $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrgSlimResponse'
        description: For public listings
      organization_id:
        type: string
      published_at:
        type: string
      slug:
        type: string
      status:
        type: string
      title:
        type: string
      updated_at:
        type: string
      visibility:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.ReviewVolunteerApplicationRequest:
    properties:
      admin_notes:
        description: Optional notes from the admin regarding the review
        example: Applicant has relevant skills.
        type: string
      new_status:
        description: The new status for the application ('approved' or 'rejected')
        enum:
        - approved
        - rejected
        example: approved
        type: string
    required:
    - new_status
    type: object
  Membership-SAAS-System-Backend_internal_payloads.TagResponse:
    properties:
      description:
        type: string
      id:
        type: string
      is_globally_approved:
        type: boolean
      language_code:
        type: string
      tag_name:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.TopAttendedEventTag:
    properties:
      count:
        example: 12
        type: integer
      name:
        example: Community
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.TopEventItem:
    properties:
      id:
        type: string
      participants:
        description: Changed from int32 to int64 to match generated SQLC type
        type: integer
      title:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateEventRegistrationPaymentRequest:
    description: Request body for updating the payment status of an event registration
      (Public or Staff endpoint).
    properties:
      new_payment_status:
        description: The new payment status for the registration.
        enum:
        - paid
        - unpaid
        - not_required
        - refunded
        example: paid
        type: string
      staff_id:
        description: The UUID of the staff member performing the update (optional,
          for logging).
        example: 123e4567-e89b-12d3-a456-************
        type: string
    required:
    - new_payment_status
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateEventRequest:
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateLanguagePreferencesPayload:
    properties:
      communication_language:
        enum:
        - en
        - zh_HK
        - zh_CN
        type: string
      interface_language:
        enum:
        - en
        - zh_HK
        - zh_CN
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateNotificationSettingsPayload:
    properties:
      enable_app_notifications:
        type: boolean
      enable_email_notifications:
        type: boolean
      enable_sms_notifications:
        type: boolean
      enable_whatsapp_notifications:
        type: boolean
      phone_otp_channel:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateOrgEventRegistrationStatusRequest:
    properties:
      admin_notes:
        description: Optional notes from the admin regarding the status change
        example: Approved after reviewing application.
        type: string
      new_status:
        description: The new status for the registration (e.g., 'registered', 'cancelled_by_admin',
          'rejected', 'waitlisted', 'attended')
        enum:
        - registered
        - cancelled_by_admin
        - rejected
        - waitlisted
        - attended
        example: approved
        type: string
    required:
    - new_status
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateOrganizationFileRequest:
    properties:
      new_name:
        maxLength: 255
        minLength: 1
        type: string
      parent_folder_id:
        description: For moving
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateOrganizationRequest:
    description: Request body for updating an existing organization. All fields are
      optional.
    properties:
      description:
        description: New description for the organization
        example: We updated our description!
        maxLength: 500
        type: string
      image_url:
        description: New URL for the organization's image/logo
        example: http://example.com/new_logo.jpg
        maxLength: 512
        type: string
      name:
        description: New name for the organization
        example: Updated Org Name
        maxLength: 100
        minLength: 2
        type: string
      owner_user_id:
        description: Ownership change should be a separate, more controlled process.
        type: string
      status:
        description: New status for the organization (pending_setup, active, suspended)
        enum:
        - pending_setup
        - active
        - suspended
        example: active
        type: string
      theme_color:
        description: New theme color for the organization
        example: '#33FF57'
        maxLength: 50
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdatePostRequest:
    properties:
      content:
        items:
          type: integer
        type: array
      published_at:
        type: string
      slug:
        maxLength: 255
        type: string
      status:
        description: Added 'hidden'
        enum:
        - draft
        - published
        - hidden
        type: string
      title:
        maxLength: 255
        minLength: 3
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdatePostTagRequest:
    properties:
      description:
        type: string
      language_code:
        maxLength: 10
        minLength: 2
        type: string
      tag_name:
        maxLength: 100
        minLength: 1
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateResourceRequest:
    properties:
      description:
        type: string
      published_at:
        type: string
      slug:
        maxLength: 255
        type: string
      status:
        enum:
        - draft
        - published
        type: string
      title:
        maxLength: 255
        minLength: 3
        type: string
      visibility:
        enum:
        - public
        - org_only
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UpdateUserProfileRequest:
    properties:
      display_name:
        type: string
      language_preferences:
        $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateLanguagePreferencesPayload'
      notification_settings:
        $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateNotificationSettingsPayload'
      profile_picture_url:
        description: Basic for now, full upload is more complex
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse:
    properties:
      communication_language:
        type: string
      created_at:
        type: string
      display_name:
        type: string
      email:
        type: string
      email_verified_at:
        type: string
      enable_app_notifications:
        type: boolean
      enable_email_notifications:
        type: boolean
      enable_sms_notifications:
        type: boolean
      enable_whatsapp_notifications:
        type: boolean
      id:
        type: string
      interface_language:
        type: string
      is_staff:
        type: boolean
      phone:
        type: string
      phone_otp_channel:
        description: e.g., "sms", "whatsapp"
        type: string
      phone_verified_at:
        type: string
      profile_picture_url:
        type: string
      updated_at:
        type: string
      verification_status:
        $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationStatusResponse'
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UserUUIDResponse:
    properties:
      uuid:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestAdminListResponse:
    properties:
      admin_notes:
        type: string
      document_id:
        type: string
      document_id_2:
        type: string
      file_name:
        type: string
      request_id:
        type: string
      reviewed_at:
        type: string
      reviewed_by_user_id:
        type: string
      reviewer_display_name:
        type: string
      status:
        type: string
      submitted_at:
        type: string
      user_display_name:
        type: string
      user_email:
        type: string
      user_id:
        type: string
      verification_type:
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestResponse:
    properties:
      admin_notes:
        type: string
      created_at:
        type: string
      document_id:
        type: string
      document_id_2:
        type: string
      file_name:
        type: string
      file_name_2:
        type: string
      id:
        type: string
      mime_type:
        type: string
      mime_type_2:
        type: string
      reviewed_at:
        type: string
      reviewed_by_user_id:
        type: string
      reviewer_display_name:
        type: string
      specifics:
        description: |-
          Specifics will hold the data for the given verification type.
          This can be a map[string]interface{} or a more structured approach later.
      status:
        $ref: '#/definitions/db.VerificationStatusEnum'
      submitted_at:
        type: string
      updated_at:
        type: string
      user_display_name:
        description: Optional related info often included in responses
        type: string
      user_email:
        type: string
      user_id:
        type: string
      verification_type:
        $ref: '#/definitions/db.VerificationTypeEnum'
    type: object
  Membership-SAAS-System-Backend_internal_payloads.UserVerificationStatusResponse:
    properties:
      address_proof:
        type: boolean
      hk_id_card:
        type: boolean
      hk_youth_plus:
        type: boolean
      mainland_china_id_card:
        type: boolean
      mainland_travel_permit:
        type: boolean
      passport:
        type: boolean
      student_id:
        type: boolean
    type: object
  Membership-SAAS-System-Backend_internal_payloads.VerifyPhoneChangeRequest:
    properties:
      new_phone_number:
        type: string
      otp:
        type: string
      state:
        type: string
    required:
    - new_phone_number
    - otp
    - state
    type: object
  Membership-SAAS-System-Backend_internal_payloads.VerifyPhoneChangeResponse:
    properties:
      message:
        type: string
      user:
        allOf:
        - $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse'
        description: The updated user profile
    type: object
  Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse:
    properties:
      admin_notes:
        type: string
      applicant_display_name:
        description: Fields primarily for admin views of applications
        type: string
      applicant_email:
        type: string
      applicant_phone:
        type: string
      application_date:
        type: string
      created_at:
        type: string
      id:
        type: string
      motivation:
        type: string
      organization_id:
        type: string
      organization_name:
        type: string
      review_date:
        type: string
      reviewed_by_user_id:
        type: string
      reviewer_display_name:
        description: For admin views
        type: string
      status:
        type: string
      updated_at:
        type: string
      user_id:
        description: Kept for consistency, though often redundant if it's /me/
        type: string
    type: object
  Membership-SAAS-System-Backend_internal_services.EnhancedVolunteerApplicationDetails:
    properties:
      admin_notes:
        type: string
      applicant_display_name:
        description: Fields primarily for admin views of applications
        type: string
      applicant_email:
        type: string
      applicant_phone:
        type: string
      application_date:
        type: string
      created_at:
        type: string
      id:
        type: string
      motivation:
        type: string
      organization_id:
        type: string
      organization_name:
        type: string
      review_date:
        type: string
      reviewed_by_user_id:
        type: string
      reviewer_display_name:
        description: For admin views
        type: string
      status:
        type: string
      updated_at:
        type: string
      user_id:
        description: Kept for consistency, though often redundant if it's /me/
        type: string
      verification_requests:
        items:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestResponse'
        type: array
    type: object
  Membership-SAAS-System-Backend_internal_utils.ErrorResponse:
    description: Represents a standard error format returned by the API.
    properties:
      details:
        description: Optional technical details or underlying error string (usually
          omitted for 5xx errors).
        example: error details here
        type: string
      error:
        description: A short, general error message or code (often corresponds to
          HTTP status text).
        example: Not Found
        type: string
      message:
        description: A more detailed, human-readable message about the error.
        example: Resource not found
        type: string
    type: object
  db.ApplicationStatusEnum:
    enum:
    - pending
    - approved
    - rejected
    type: string
    x-enum-varnames:
    - ApplicationStatusEnumPending
    - ApplicationStatusEnumApproved
    - ApplicationStatusEnumRejected
  db.EventRegistrationRoleType:
    enum:
    - participant
    - volunteer
    type: string
    x-enum-varnames:
    - EventRegistrationRoleTypeParticipant
    - EventRegistrationRoleTypeVolunteer
  db.EventRegistrationStatusType:
    enum:
    - pending_approval
    - registered
    - waitlisted
    - rejected_approval
    - cancelled_by_user
    - attended
    - absent
    type: string
    x-enum-varnames:
    - EventRegistrationStatusTypePendingApproval
    - EventRegistrationStatusTypeRegistered
    - EventRegistrationStatusTypeWaitlisted
    - EventRegistrationStatusTypeRejectedApproval
    - EventRegistrationStatusTypeCancelledByUser
    - EventRegistrationStatusTypeAttended
    - EventRegistrationStatusTypeAbsent
  db.EventTag:
    properties:
      created_at:
        type: string
      created_by_user_id:
        type: string
      description:
        type: string
      id:
        type: string
      is_globally_approved:
        type: boolean
      language_code:
        type: string
      tag_name:
        type: string
      updated_at:
        type: string
    type: object
  db.ListAllPendingReviewEventVolunteerApplicationsRow:
    properties:
      admin_review_notes:
        type: string
      applicant_display_name:
        type: string
      applicant_email:
        type: string
      applicant_phone:
        type: string
      application_notes_by_user:
        type: string
      applied_at:
        type: string
      created_at:
        type: string
      event_id:
        type: string
      event_title:
        type: string
      id:
        type: string
      organization_id:
        type: string
      organization_name:
        type: string
      reviewed_at:
        type: string
      reviewed_by_user_id:
        type: string
      status:
        $ref: '#/definitions/db.ApplicationStatusEnum'
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  db.ListPendingReviewEventVolunteerApplicationsForEventRow:
    properties:
      admin_review_notes:
        type: string
      applicant_display_name:
        type: string
      applicant_email:
        type: string
      applicant_phone:
        type: string
      application_notes_by_user:
        type: string
      applied_at:
        type: string
      created_at:
        type: string
      event_id:
        type: string
      event_title:
        type: string
      id:
        type: string
      organization_id:
        type: string
      organization_name:
        type: string
      reviewed_at:
        type: string
      reviewed_by_user_id:
        type: string
      status:
        $ref: '#/definitions/db.ApplicationStatusEnum'
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  db.ListPendingReviewEventVolunteerApplicationsForOrganizationRow:
    properties:
      admin_review_notes:
        type: string
      applicant_display_name:
        type: string
      applicant_email:
        type: string
      applicant_phone:
        type: string
      application_notes_by_user:
        type: string
      applied_at:
        type: string
      created_at:
        type: string
      event_id:
        type: string
      event_title:
        type: string
      id:
        type: string
      organization_id:
        type: string
      organization_name:
        type: string
      reviewed_at:
        type: string
      reviewed_by_user_id:
        type: string
      status:
        $ref: '#/definitions/db.ApplicationStatusEnum'
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  db.PaymentStatusType:
    enum:
    - paid
    - unpaid
    - not_required
    - refunded
    type: string
    x-enum-varnames:
    - PaymentStatusTypePaid
    - PaymentStatusTypeUnpaid
    - PaymentStatusTypeNotRequired
    - PaymentStatusTypeRefunded
  db.User:
    properties:
      communication_language:
        type: string
      created_at:
        type: string
      display_name:
        type: string
      email:
        type: string
      email_verified_at:
        type: string
      enable_app_notifications:
        type: boolean
      enable_email_notifications:
        type: boolean
      enable_sms_notifications:
        type: boolean
      enable_whatsapp_notifications:
        type: boolean
      hashed_password:
        type: string
      id:
        type: string
      interface_language:
        type: string
      is_staff:
        type: boolean
      phone:
        type: string
      phone_otp_channel:
        type: string
      phone_verified_at:
        type: string
      profile_picture_url:
        type: string
      updated_at:
        type: string
    type: object
  db.UserVerificationRequest:
    properties:
      admin_notes:
        type: string
      created_at:
        type: string
      document_id:
        type: string
      document_id_2:
        type: string
      id:
        type: string
      reviewed_at:
        type: string
      reviewed_by_user_id:
        type: string
      status:
        $ref: '#/definitions/db.VerificationStatusEnum'
      submitted_at:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
      verification_type:
        $ref: '#/definitions/db.VerificationTypeEnum'
    type: object
  db.VerificationStatusEnum:
    enum:
    - pending
    - approved
    - rejected
    - data_deleted_by_user
    type: string
    x-enum-varnames:
    - VerificationStatusEnumPending
    - VerificationStatusEnumApproved
    - VerificationStatusEnumRejected
    - VerificationStatusEnumDataDeletedByUser
  db.VerificationTypeEnum:
    enum:
    - hk_id_card
    - mainland_china_id_card
    - mainland_travel_permit
    - passport
    - hk_youth_plus
    - address_proof
    - student_id
    - home_visit
    type: string
    x-enum-varnames:
    - VerificationTypeEnumHkIDCard
    - VerificationTypeEnumMainlandChinaIDCard
    - VerificationTypeEnumMainlandTravelPermit
    - VerificationTypeEnumPassport
    - VerificationTypeEnumHkYouthPlus
    - VerificationTypeEnumAddressProof
    - VerificationTypeEnumStudentID
    - VerificationTypeEnumHomeVisit
  echo.Map:
    additionalProperties: true
    type: object
  internal_authn.CheckPhoneRequest:
    properties:
      phone:
        type: string
    required:
    - phone
    type: object
  internal_authn.CheckPhoneResponse:
    properties:
      exists:
        type: boolean
      user:
        allOf:
        - $ref: '#/definitions/db.User'
        description: Optionally return user details if exists
    type: object
  internal_authn.CheckStaffEmailRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  internal_authn.CheckStaffEmailResponse:
    properties:
      exists:
        type: boolean
      is_staff:
        description: To confirm the user is actually staff
        type: boolean
      user_hint:
        description: e.g., if exists but not staff
        type: string
    type: object
  internal_authn.InitiatePhoneOTPRequest:
    properties:
      client_id:
        type: string
      code_challenge:
        type: string
      code_challenge_method:
        type: string
      phone:
        type: string
      phone_otp_channel:
        description: e.g., 'whatsapp', 'sms'
        type: string
      redirect_uri:
        type: string
      state:
        type: string
    required:
    - client_id
    - code_challenge
    - code_challenge_method
    - phone
    - state
    type: object
  internal_authn.InitiatePhoneOTPResponse:
    properties:
      message:
        description: e.g., "OTP initiated successfully"
        type: string
      state:
        description: Echo back the state for client verification
        type: string
    type: object
  internal_authn.InitiatePhoneRegistrationRequest:
    properties:
      client_id:
        type: string
      code_challenge:
        type: string
      code_challenge_method:
        description: e.g., "S256"
        type: string
      phone:
        type: string
      phone_otp_channel:
        description: e.g., 'whatsapp', 'sms'
        enum:
        - whatsapp
        - sms
        type: string
      redirect_uri:
        description: Optional, but good for consistency
        type: string
      state:
        description: Opaque value for CSRF protection
        type: string
    required:
    - client_id
    - code_challenge
    - code_challenge_method
    - phone
    - state
    type: object
  internal_authn.InitiatePhoneRegistrationResponse:
    properties:
      flow_id:
        description: The ID of the created auth_flow, useful for client tracking
        type: string
      message:
        description: e.g., "Registration OTP initiated successfully"
        type: string
      state:
        description: Echo back the state for client verification
        type: string
    type: object
  internal_authn.InitiateStaffLoginRequest:
    properties:
      client_id:
        type: string
      code_challenge:
        type: string
      code_challenge_method:
        type: string
      email:
        type: string
      redirect_uri:
        type: string
      state:
        type: string
    required:
    - client_id
    - code_challenge
    - code_challenge_method
    - email
    - state
    type: object
  internal_authn.InitiateStaffLoginResponse:
    properties:
      flow_id:
        description: ID of the created auth_flow
        type: string
      state:
        description: Echo back state
        type: string
    type: object
  internal_authn.RefreshTokenRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  internal_authn.RefreshTokenResponse:
    properties:
      access_token:
        type: string
      refresh_token:
        type: string
      token_type:
        description: Usually "Bearer"
        type: string
    type: object
  internal_authn.VerifyPhoneOTPRequest:
    properties:
      code_verifier:
        description: PKCE code verifier
        type: string
      otp:
        description: The OTP received by the user
        type: string
      state:
        description: The state value from the initiation step
        type: string
    required:
    - code_verifier
    - otp
    - state
    type: object
  internal_authn.VerifyPhoneOTPResponse:
    properties:
      access_token:
        type: string
      message:
        type: string
      refresh_token:
        type: string
      user_id:
        description: ID of the logged-in user
        type: string
    type: object
  internal_authn.VerifyPhoneRegistrationRequest:
    properties:
      code_verifier:
        description: PKCE code verifier
        type: string
      communication_language:
        type: string
      display_name:
        description: User's chosen display name
        type: string
      interface_language:
        type: string
      otp:
        description: The OTP received by the user
        type: string
      phone_otp_channel:
        description: e.g., 'whatsapp', 'sms'
        enum:
        - whatsapp
        - sms
        type: string
      state:
        description: The state value from the initiation step
        type: string
    required:
    - code_verifier
    - display_name
    - otp
    - state
    type: object
  internal_authn.VerifyPhoneRegistrationResponse:
    properties:
      access_token:
        type: string
      message:
        type: string
      refresh_token:
        type: string
      user_id:
        type: string
    type: object
  internal_authn.VerifyStaffLoginRequest:
    properties:
      code_verifier:
        type: string
      email:
        description: For verification against the flow
        type: string
      password:
        type: string
      state:
        type: string
    required:
    - code_verifier
    - email
    - password
    - state
    type: object
  internal_authn.VerifyStaffLoginResponse:
    properties:
      access_token:
        type: string
      message:
        type: string
      refresh_token:
        type: string
      user_id:
        type: string
    type: object
  internal_handlers.GovernmentFundingTypeResponse:
    properties:
      key:
        example: gov_funded_prog_green
        type: string
      langcode:
        example: en
        type: string
      name:
        example: Government-funded programme (Green)
        type: string
    type: object
  internal_handlers.VerificationTypeResponse:
    properties:
      key:
        example: passport
        type: string
      langcode:
        example: en
        type: string
      name:
        example: Passport
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.example.com/support
  description: This is the API for the Membership SAAS System backend.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://example.com/terms/
  title: Membership SAAS System API
  version: "1.0"
paths:
  /admin/event-applications/pending-review:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of all 'pending' volunteer applications
        across the system. For System Admin use.
      parameters:
      - default: 1
        description: Page number for pagination
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 20
        description: Number of items per page
        in: query
        maximum: 100
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved applications
          schema:
            allOf:
            - $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/db.ListAllPendingReviewEventVolunteerApplicationsRow'
                  type: array
              type: object
        "400":
          description: Invalid request parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth // Placeholder for system admin auth: []
      summary: List all pending volunteer applications (System Admin)
      tags:
      - Admin VolunteerApplications
  /admin/organizations/{orgId}/event-volunteer-applications:
    get:
      description: Retrieves all event volunteer applications for a specific organization,
        with optional status and pagination. Requires staff privileges with 'admin',
        'owner', or 'manager' role within the organization.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Filter by application status (e.g., 'pending', 'approved', 'rejected')
        in: query
        name: status
        type: string
      - default: 20
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of event volunteer applications
          headers:
            X-Total-Count:
              description: Total number of applications matching criteria
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventVolunteerApplicationResponse'
            type: array
        "400":
          description: Invalid organization ID format or invalid status value or pagination
            params
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized (admin token missing or invalid)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (admin not staff in organization or lacks required
            role)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List all event volunteer applications for an organization (Admin)
      tags:
      - Events (Organization)
      - Event Volunteering (Admin)
  /admin/organizations/{orgId}/volunteer/applications:
    get:
      description: Retrieves all volunteer applications for a specific organization,
        with optional status filter, limit, and offset. Requires staff privileges
        with 'admin', 'owner', or 'manager' role within the organization.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Filter by application status (e.g., 'pending', 'approved', 'rejected')
        in: query
        name: status
        type: string
      - default: 10
        description: Number of applications to return per page
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination (number of items to skip)
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Paginated list of volunteer applications
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse'
        "400":
          description: Invalid organization ID format or invalid status value
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized (admin token missing or invalid)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (admin not staff in organization or lacks required
            role)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List all volunteer applications for an organization (Admin)
      tags:
      - Volunteer Management (Admin)
      - Organizations
  /admin/organizations/{orgId}/volunteer/applications/{appId}:
    get:
      description: Retrieves full details of a specific volunteer application, including
        user info and associated verification documents, for admin review. Requires
        staff privileges within the organization.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Application ID (UUID)
        in: path
        name: appId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Application details with user info and verifications
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_services.EnhancedVolunteerApplicationDetails'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not staff in organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization or Application not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Get details of a volunteer application for admin review
      tags:
      - Volunteer Management (Admin)
      - Organizations
  /admin/organizations/{orgId}/volunteer/applications/{appId}/review:
    patch:
      consumes:
      - application/json
      description: Allows an admin to review a volunteer application, updating its
        status (e.g., to 'approved' or 'rejected') and optionally providing feedback.
        Uses the consolidated application_status_enum for status values.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Volunteer Application ID (UUID)
        in: path
        name: appId
        required: true
        type: string
      - description: Review details (new status using application_status_enum, and
          optional admin_feedback)
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminReviewVolunteerApplicationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Application reviewed and status updated
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse'
        "400":
          description: Invalid request (e.g., invalid ID format, invalid status value
            from enum, bad request body)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized (admin token missing or invalid)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (admin not staff in organization or lacks required
            role, or trying to review an application not belonging to the org)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Application not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "409":
          description: Conflict (e.g., application is not in a reviewable state like
            'pending', or invalid status transition)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Review a volunteer application (Admin)
      tags:
      - Volunteer Management (Admin)
      - Organizations
  /admin/organizations/{orgId}/volunteer/applications/pending:
    get:
      description: Retrieves all volunteer applications with status 'pending' for
        a specific organization. Requires staff privileges with 'admin', 'owner',
        or 'manager' role within the organization.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of pending applications with status 'pending'
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse'
            type: array
        "400":
          description: Invalid organization ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized (admin token missing or invalid)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (admin not staff in organization or lacks required
            role)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List pending volunteer applications for an organization (Admin)
      tags:
      - Volunteer Management (Admin)
      - Organizations
  /admin/users:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of all users. Admin only. Can filter
        by is_staff status.
      parameters:
      - description: Filter by staff status
        in: query
        name: is_staff
        type: boolean
      - description: 'Page number for pagination (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Number of items per page (default: 10, max: 100)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of users with pagination info
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedUsersResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized (JWT missing or invalid)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (User is not an admin)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: List all users (Admin)
      tags:
      - Admin Users
    post:
      consumes:
      - application/json
      description: Creates a new user with is_staff set to true. Admin only.
      parameters:
      - description: Details of the staff user to create
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateStaffUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Newly created staff user details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse'
        "400":
          description: Invalid request payload or validation error (e.g., email exists,
            password too short)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "409":
          description: Conflict (e.g., email already exists)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error (e.g., failed to hash password or save
            user)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new staff user (Admin)
      tags:
      - Admin Users
  /admin/users/{userId}:
    get:
      consumes:
      - application/json
      description: Retrieves a specific user by their ID. Admin only.
      parameters:
      - description: User ID (UUID)
        in: path
        name: userId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse'
        "400":
          description: Invalid user ID format in path
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user by ID (Admin)
      tags:
      - Admin Users
  /admin/users/email/{email}:
    get:
      consumes:
      - application/json
      description: Retrieves a specific user by their email address. Admin only.
      parameters:
      - description: User Email
        in: path
        name: email
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse'
        "400":
          description: Invalid email format in path
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user by email (Admin)
      tags:
      - Admin Users
  /admin/users/phone/{phone}:
    get:
      consumes:
      - application/json
      description: Retrieves a specific user by their phone number. Admin only.
      parameters:
      - description: User Phone Number (ensure URL encoding if it contains special
          characters like +)
        in: path
        name: phone
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminUserResponse'
        "400":
          description: Invalid phone format in path
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user by phone (Admin)
      tags:
      - Admin Users
  /admin/verifications:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of user verification requests with filtering
        capabilities for admins.
      parameters:
      - description: Filter by User ID (UUID)
        in: query
        name: user_id
        type: string
      - description: Filter by Event ID (UUID)
        in: query
        name: event_id
        type: string
      - description: Filter by verification type (e.g., hk_id_card, student_id)
        in: query
        name: verification_type
        type: string
      - description: Filter by status (e.g., pending, approved, rejected)
        in: query
        name: status
        type: string
      - description: Filter by Organization ID (UUID)
        in: query
        name: org_id
        type: string
      - default: 1
        description: Page number for pagination
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Paginated list of verification requests
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedUserVerificationRequestsResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List user verification requests (Admin)
      tags:
      - Admin - User Verification
  /admin/verifications/:
    get:
      description: |-
        Retrieves all verification requests. For testing purposes. Requires staff privileges.
        Can be filtered by status (pending, approved, rejected) and/or organization ID.
      parameters:
      - description: Filter by verification status (e.g., pending, approved, rejected)
        enum:
        - pending
        - approved
        - rejected
        in: query
        name: status
        type: string
      - description: Filter by organization ID (UUID)
        format: uuid
        in: query
        name: org_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of all verification requests
          schema:
            items:
              $ref: '#/definitions/db.UserVerificationRequest'
            type: array
        "400":
          description: Bad request (e.g., invalid status or org_id)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not staff)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List all verification requests regardless of status (Admin - Testing
        Only)
      tags:
      - Admin Verification
  /admin/verifications/{reqID}:
    get:
      description: Retrieves full details of a specific verification request, including
        user info. Requires staff privileges.
      parameters:
      - description: Verification Request ID
        in: path
        name: reqID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Full verification request details
          schema:
            $ref: '#/definitions/db.UserVerificationRequest'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not staff)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Request not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Get full details of a verification request (Admin)
      tags:
      - Admin Verification
  /admin/verifications/{reqID}/review:
    patch:
      consumes:
      - application/json
      description: Allows staff to approve or reject a pending verification request.
      parameters:
      - description: Verification Request ID
        in: path
        name: reqID
        required: true
        type: string
      - description: Review action (approve/reject) and comments
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.AdminReviewVerificationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated verification request
          schema:
            $ref: '#/definitions/db.UserVerificationRequest'
        "400":
          description: Invalid request or action
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not staff)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Request not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Approve or reject a verification request (Admin)
      tags:
      - Admin Verification
  /admin/verifications/documents/{docID}:
    get:
      description: Allows staff to download any submitted verification document.
      parameters:
      - description: Document ID
        in: path
        name: docID
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: Verification document file
          schema:
            type: file
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not staff)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Document not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Download any verification document (Admin)
      tags:
      - Admin Verification
  /admin/verifications/pending:
    get:
      description: Retrieves all verification requests with status 'pending'. Requires
        staff privileges.
      produces:
      - application/json
      responses:
        "200":
          description: List of pending verification requests
          schema:
            items:
              $ref: '#/definitions/db.UserVerificationRequest'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not staff)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List pending verification requests (Admin)
      tags:
      - Admin Verification
  /api/v1/government-funding-types:
    get:
      consumes:
      - application/json
      description: |-
        Retrieves a list of all possible government funding types supported by the system, with names localized by language code.
        It uses the 'lang_code' query parameter. If 'lang_code' is provided and a localization exists, it's returned.
        If the specific 'lang_code' localization doesn't exist for a type, it falls back to English ('en').
        If neither the specified 'lang_code' nor English is available for a type, that type is omitted.
        If 'lang_code' is not provided, it defaults to English ('en') for all types.
      parameters:
      - description: Language code (e.g., 'en', 'zh_HK') to filter types. Defaults
          to 'en' if omitted or if specific localization not found.
        in: query
        name: lang_code
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved localized government funding types
          schema:
            items:
              $ref: '#/definitions/internal_handlers.GovernmentFundingTypeResponse'
            type: array
      summary: List all supported government funding types with localizations
      tags:
      - Public
      - Verification
      - GovernmentFunding
  /api/v1/verification-types:
    get:
      consumes:
      - application/json
      description: Retrieves a list of all possible verification types supported by
        the system, with names localized by language code. If the 'lang_code' query
        parameter is provided, the list is filtered to include only types matching
        that language code. Otherwise, all verification types with their available
        localizations are returned.
      parameters:
      - description: Language code (e.g., 'en', 'zh_HK') to filter verification types.
          If omitted, all types with all localizations are returned.
        in: query
        name: lang_code
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved localized verification types
          schema:
            items:
              $ref: '#/definitions/internal_handlers.VerificationTypeResponse'
            type: array
      summary: List all supported verification types with localizations
      tags:
      - Public
      - Verification
  /authn/logout:
    post:
      consumes:
      - application/json
      description: Invalidates the user's current refresh token.
      parameters:
      - description: Refresh token to invalidate
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.LogoutRequestPayload'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Logout successful
        "400":
          description: Invalid request format or Invalid request data
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: An error occurred during logout.
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Logout user
      tags:
      - Authentication
  /authn/phone/check:
    post:
      consumes:
      - application/json
      description: Checks if a given phone number is already registered in the system.
      parameters:
      - description: Phone number to check
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.CheckPhoneRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Existence status
          schema:
            $ref: '#/definitions/internal_authn.CheckPhoneResponse'
        "400":
          description: Invalid request payload or Phone number is required
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to check phone number
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Check if phone number exists
      tags:
      - Authentication
  /authn/phone/otp/initiate:
    post:
      consumes:
      - application/json
      description: Sends an OTP to the user's phone for login. Requires PKCE parameters.
      parameters:
      - description: Phone number and PKCE details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.InitiatePhoneOTPRequest'
      produces:
      - application/json
      responses:
        "200":
          description: State and message
          schema:
            $ref: '#/definitions/internal_authn.InitiatePhoneOTPResponse'
        "400":
          description: Invalid request payload or missing required fields
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: User with this phone number not found. Please register first.
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to check phone number or Failed to initiate login flow
            or Failed to send OTP
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Initiate OTP for existing user login
      tags:
      - Authentication
  /authn/phone/otp/verify:
    post:
      consumes:
      - application/json
      description: Verifies the OTP and PKCE challenge for phone login and issues
        tokens.
      parameters:
      - description: State, OTP, and PKCE verifier
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.VerifyPhoneOTPRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Message, UserID, AccessToken, RefreshToken
          schema:
            $ref: '#/definitions/internal_authn.VerifyPhoneOTPResponse'
        "400":
          description: Invalid request payload or missing required fields or Invalid
            PKCE code verifier or Invalid OTP
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Account locked due to too many failed OTP attempts or Too many
            failed OTP attempts. Account locked for a short period.
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Invalid or expired login state
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to retrieve login flow or Login flow integrity error
            or Failed to process OTP attempts or Unsupported code challenge method
            or OTP verification failed or User account inconsistency or Failed to
            retrieve user details or Failed to generate access token or Failed to
            generate refresh token
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Verify OTP for login
      tags:
      - Authentication
  /authn/register/phone/initiate:
    post:
      consumes:
      - application/json
      description: Starts the registration process for a new user with phone OTP and
        PKCE.
      parameters:
      - description: Phone number, client details, PKCE parameters, and OTP channel
          preference
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.InitiatePhoneRegistrationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: State, message and FlowID
          schema:
            $ref: '#/definitions/internal_authn.InitiatePhoneRegistrationResponse'
        "400":
          description: Invalid request payload or missing required fields
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to initiate registration flow or Failed to send OTP
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Initiate OTP for new user registration
      tags:
      - Authentication
  /authn/register/phone/verify:
    post:
      consumes:
      - application/json
      description: Verifies OTP and PKCE, creates the user, and issues tokens.
      parameters:
      - description: State, OTP, code verifier and user details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.VerifyPhoneRegistrationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Message, UserID, AccessToken, RefreshToken
          schema:
            $ref: '#/definitions/internal_authn.VerifyPhoneRegistrationResponse'
        "400":
          description: Invalid request payload or missing required fields or Invalid
            PKCE code verifier or Invalid OTP
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Account locked due to too many failed OTP attempts or Too many
            failed OTP attempts. Account locked for a short period.
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Invalid or expired registration state
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to retrieve registration flow or Registration flow integrity
            error or Failed to process OTP attempts or Unsupported code challenge
            method or OTP verification failed or Failed to create user account or
            Failed to generate access token or Failed to generate refresh token
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Verify OTP for registration
      tags:
      - Authentication
  /authn/staff/email/check:
    post:
      consumes:
      - application/json
      description: Checks if a given email is registered for a staff member.
      parameters:
      - description: Email to check
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.CheckStaffEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Existence status and staff status
          schema:
            $ref: '#/definitions/internal_authn.CheckStaffEmailResponse'
        "400":
          description: Invalid request payload or Email is required
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to check staff email
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Check if staff email exists
      tags:
      - Authentication
      - Staff
  /authn/staff/login/initiate:
    post:
      consumes:
      - application/json
      description: Initiates the login process for a staff member using email and
        password (first step).
      parameters:
      - description: Staff email
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.InitiateStaffLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login initiated, includes state for verification step
          schema:
            $ref: '#/definitions/internal_authn.InitiateStaffLoginResponse'
        "400":
          description: Invalid request payload or Email is required
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: User is not authorized as staff or Account is locked
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Staff user not found with this email
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to initiate staff login or Failed to process login attempts
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Initiate staff login
      tags:
      - Authentication
      - Staff
  /authn/staff/login/verify:
    post:
      consumes:
      - application/json
      description: Verifies the staff member's password against the initiated login
        flow and issues tokens.
      parameters:
      - description: State from initiation and password
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.VerifyStaffLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful, includes access and refresh tokens
          schema:
            $ref: '#/definitions/internal_authn.VerifyStaffLoginResponse'
        "400":
          description: Invalid request payload, Missing state or password, or Invalid
            password
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Account locked due to too many failed login attempts
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Invalid or expired login state
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to retrieve login flow, Login flow integrity error,
            Failed to process login attempts, User account inconsistency, Failed to
            retrieve user details, Failed to generate access token, or Failed to generate
            refresh token
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Verify staff login
      tags:
      - Authentication
      - Staff
  /authn/token/refresh:
    post:
      consumes:
      - application/json
      description: Issues new access and refresh tokens using a valid refresh token.
      parameters:
      - description: Refresh token
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/internal_authn.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: New access token, new refresh token, and token type
          schema:
            $ref: '#/definitions/internal_authn.RefreshTokenResponse'
        "400":
          description: Invalid request payload or Refresh token is required
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Invalid or expired refresh token or Refresh token expired
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Could not process token due to server configuration error or
            Failed to process refresh token or Failed to generate new tokens
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Refresh JWT tokens
      tags:
      - Authentication
  /event-registrations:
    post:
      consumes:
      - application/json
      description: Registers the authenticated user for a specific event. Handles
        capacity, waitlist, eligibility, and time conflicts.
      parameters:
      - description: Event ID to register for, e.g. {\
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.RegisterForEventRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration successful (or waitlisted)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
        "400":
          description: Invalid request, event full, not eligible, or time conflict
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Register current user for an event
      tags:
      - Event Registrations
  /event-registrations/{registrationId}:
    delete:
      description: Allows the authenticated user to cancel their registration for
        an event.
      parameters:
      - description: Registration ID (UUID)
        in: path
        name: registrationId
        required: true
        type: string
      responses:
        "200":
          description: 'Updated registration details (status: cancelled)'
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not owner of registration)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Registration not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Cancel user's own registration
      tags:
      - Event Registrations
    get:
      description: Retrieves details of a specific event registration owned by the
        user.
      parameters:
      - description: Registration ID (UUID)
        in: path
        name: registrationId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Details of the event registration
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not owner of registration)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Registration not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get details of a specific registration
      tags:
      - Event Registrations
  /event-registrations/{registrationId}/payment-status:
    patch:
      consumes:
      - application/json
      description: Updates the payment status of a registration. This is a public
        endpoint potentially used by payment gateways or staff with a specific staff_id.
      parameters:
      - description: Registration ID (UUID)
        in: path
        name: registrationId
        required: true
        type: string
      - description: New payment status and staff ID, e.g. {\
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateEventRegistrationPaymentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated registration details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Registration not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Update payment status for a specific registration (Public)
      tags:
      - Event Registrations
  /event-registrations/check-in:
    post:
      consumes:
      - application/json
      description: |-
        A volunteer or staff member (scanner) checks in a participant for an eligible event. The system identifies the relevant event for the participant (e.g., an active, published event they are registered for).
        The scanner's user_id and staff status are taken from their JWT.
        The participant's user_id is provided in the request body.
        Authorization for Scanners:
        - Staff Scanners: Must be a member of the organization hosting the event with an appropriate role.
        - Volunteer Scanners: Must satisfy two conditions:
        1. Have an 'approved' `user_volunteer_applications` record for the organization hosting the event.
        2. Have an 'approved' `event_volunteer_applications` record for the specific event they are scanning for.
        (Successfully scanning also marks the volunteer as attended for that event).
        IMPORTANT NOTE: Currently, the API to create and approve `event_volunteer_applications` (event-specific volunteer roles) is not yet implemented. Therefore, volunteer scanning will result in a 403 error until this functionality is added.
      parameters:
      - description: Participant's User ID to check in
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventCheckInRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Participant's registration details on successful check-in
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
        "400":
          description: Invalid request (e.g., missing or invalid participant_user_id,
            malformed body)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized (scanner token invalid or missing)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: 'Forbidden (scanner not authorized: e.g., volunteer lacks approved
            org-level application, lacks approved event-specific application, or staff
            not in org/lacks role. See API description for details on volunteer authorization.)'
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Not Found (e.g., participant not registered for any eligible
            event, or the specified participant user does not exist)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "409":
          description: Conflict (e.g., participant already checked in to the event)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Check in a participant for an event by a scanner (volunteer/staff)
      tags:
      - Event Registrations
  /event-registrations/me:
    get:
      description: Returns paginated list of the user's event registrations with extensive
        filtering options.
      parameters:
      - default: 10
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      - description: Filter by event start date (ISO 8601)
        in: query
        name: start_date
        type: string
      - description: Filter by event end date (ISO 8601)
        in: query
        name: end_date
        type: string
      - description: Filter by event status (published, archived, deleted, draft,
          hidden, cancelled)
        in: query
        name: status
        type: string
      - description: Sort order (date_asc, date_desc)
        in: query
        name: sort
        type: string
      - description: Filter by user's role in event (attendee, volunteer)
        in: query
        name: role
        type: string
      - description: Filter by organization UUID
        in: query
        name: organization_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of user's event registrations
          headers:
            X-Total-Count:
              description: Total number of registrations
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: List all event registrations for the current user
      tags:
      - Event Registrations
      - User Profile
  /event-statistics:
    get:
      description: |-
        Retrieves statistics about events, including counts per category (tag) and top N events by participant count.
        Supports optional startDate, endDate (RFC3339 format, e.g., "2023-01-01T00:00:00Z") and limit query parameters.
        Defaults to the last 6 months and top 10 events if parameters are not provided.
      parameters:
      - description: Start date for filtering statistics (RFC3339 format)
        in: query
        name: startDate
        type: string
      - description: End date for filtering statistics (RFC3339 format)
        in: query
        name: endDate
        type: string
      - default: 10
        description: Number of top events to return
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Event statistics
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventStatisticsResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Get event statistics
      tags:
      - Events (Statistics)
  /event-tags:
    get:
      description: Retrieves a list of event tags, potentially filtered by language
        code and approval status. Supports pagination.
      parameters:
      - description: Filter by language code (e.g., 'en', 'zh_HK', 'zh_CN')
        in: query
        name: langCode
        type: string
      - description: Filter by approval status (true or false)
        in: query
        name: approved
        type: boolean
      - default: 20
        description: Limit number of results per page
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of event tags
          schema:
            items:
              $ref: '#/definitions/db.EventTag'
            type: array
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List all available event tags
      tags:
      - Event Tags
      - Events (Public)
  /events:
    get:
      description: Lists public events. Supports filtering by orgId, startDate, endDate,
        searchTerm, tagIds, status. Supports sorting by sortBy. Supports pagination.
      parameters:
      - description: Filter by Organization UUID
        in: query
        name: orgId
        type: string
      - description: Filter by start date (YYYY-MM-DDTHH:MM:SSZ)
        in: query
        name: startDate
        type: string
      - description: Filter by end date (YYYY-MM-DDTHH:MM:SSZ)
        in: query
        name: endDate
        type: string
      - description: Search term for title and location_full_address
        in: query
        name: searchTerm
        type: string
      - description: Comma-separated list of tag IDs
        in: query
        name: tagIds
        type: string
      - description: Filter by status (e.g., published, archived, deleted, draft,
          hidden, cancelled)
        in: query
        name: status
        type: string
      - description: Filter by a specific government funding type key
        in: query
        name: government_funding_type_key
        type: string
      - description: Filter by a specific event verification type key
        in: query
        name: event_verification_type_key
        type: string
      - description: Sort order (e.g., popularity_desc, start_time_asc)
        in: query
        name: sortBy
        type: string
      - default: 10
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of public events
          headers:
            X-Total-Count:
              description: Total number of events
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PublicEventResponse'
            type: array
      summary: List public events
      tags:
      - Events (Public)
  /events-tags:
    post:
      consumes:
      - application/json
      description: Allows authorized users to add new event tags to the system.
      parameters:
      - description: Event tag details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateEventTagRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Newly created event tag
          schema:
            $ref: '#/definitions/db.EventTag'
        "400":
          description: Invalid request body or validation failed
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/staff - check performed by middleware/route
            setup)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "409":
          description: Conflict - Tag already exists
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new event tag (admin/staff)
      tags:
      - Event Tags
      - Admin
  /events/{event_id}/event-applications/pending-review:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of volunteer applications with 'pending'
        status for a specific event.
      parameters:
      - description: Event ID (UUID format)
        format: uuid
        in: path
        name: event_id
        required: true
        type: string
      - default: 1
        description: Page number for pagination
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 20
        description: Number of items per page
        in: query
        maximum: 100
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved applications
          schema:
            allOf:
            - $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/db.ListPendingReviewEventVolunteerApplicationsForEventRow'
                  type: array
              type: object
        "400":
          description: Invalid request parameters (e.g., invalid UUID, non-positive
            page/page_size)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth: []
      summary: List pending volunteer applications for an event
      tags:
      - Events VolunteerApplications
  /events/{eventId}:
    get:
      description: Retrieves public-facing details for a given event ID.
      parameters:
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Public event details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PublicEventResponse'
        "404":
          description: Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Get public details of a specific event
      tags:
      - Events (Public)
  /health:
    get:
      description: Checks the health of the API.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Health check
      tags:
      - Health
  /organizations:
    get:
      description: Lists all organizations. Visibility may depend on user role. Supports
        pagination. Response items include image_url, theme_color, and status.
      parameters:
      - default: 10
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of organizations
          headers:
            X-Total-Count:
              description: Total number of organizations
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse'
            type: array
        "401":
          description: Unauthorized" // If auth is strictly required
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List all organizations
      tags:
      - Organizations
    post:
      consumes:
      - application/json
      description: Creates a new organization. The authenticated user becomes the
        owner. Name is required. Description, image_url, theme_color are optional.
        Status defaults to 'pending_setup'.
      parameters:
      - description: Organization details (name required, description, image_url,
          theme_color optional)
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateOrganizationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Newly created organization (includes image_url, theme_color,
            status)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Create a new organization
      tags:
      - Organizations
  /organizations/{orgId}:
    delete:
      description: Deletes an organization. Requires owner role in that org.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Cannot delete default organization or invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not owner)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Delete an organization
      tags:
      - Organizations
    get:
      description: Retrieves details for a given organization ID. Response includes
        image_url, theme_color, and status.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Organization details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse'
        "400":
          description: Invalid organization ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (if user not member and it's private)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Get details of a specific organization
      tags:
      - Organizations
    put:
      consumes:
      - application/json
      description: Updates details of an organization. Requires admin/owner role in
        that org. All fields in body are optional (name, description, image_url, theme_color,
        status).
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Fields to update (name, description, image_url, theme_color,
          status - all optional)
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateOrganizationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated organization details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/owner)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Update an organization
      tags:
      - Organizations
  /organizations/{orgId}/event-registrations:
    get:
      description: Admin view of all registrations for events within the organization,
        with filtering options. Supports pagination. Requires organization admin/manager
        permission.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - default: 10
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      - description: Filter by specific Event ID (UUID)
        in: query
        name: event_id
        type: string
      - description: Filter by event start date range (ISO 8601)
        in: query
        name: start_date
        type: string
      - description: Filter by event end date range (ISO 8601)
        in: query
        name: end_date
        type: string
      - description: Filter by registration status (e.g., confirmed, pending_approval,
          cancelled)
        in: query
        name: status
        type: string
      - description: Filter by specific User ID (UUID)
        in: query
        name: user_id
        type: string
      - description: Search by user name
        in: query
        name: search_name
        type: string
      - description: Filter by payment status (e.g., paid, unpaid, pending)
        in: query
        name: payment_status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of event registrations for the organization
          headers:
            X-Total-Count:
              description: Total number of registrations
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not admin for this organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List event registrations for an organization (Admin View)
      tags:
      - Event Registrations (Organization Admin)
  /organizations/{orgId}/event-registrations/{registrationId}/status:
    patch:
      consumes:
      - application/json
      description: Admin action to update a specific registration's status (e.g.,
        approve, reject, mark attendance). Requires organization admin/manager permission.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Registration ID (UUID)
        in: path
        name: registrationId
        required: true
        type: string
      - description: New status and admin notes, e.g. {\
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateOrgEventRegistrationStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated registration details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
        "400":
          description: Invalid request or status transition
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not admin for this organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Organization or Registration not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Update registration status (Admin Action)
      tags:
      - Event Registrations (Organization Admin)
  /organizations/{orgId}/event-statistics:
    get:
      description: |-
        Retrieves statistics about events for a specific organization, including counts per category (tag) and top N events by participant count.
        Supports optional startDate, endDate (RFC3339 format, e.g., "2023-01-01T00:00:00Z") and limit query parameters.
        Defaults to the last 6 months and top 10 events if parameters are not provided.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Start date for filtering statistics (RFC3339 format)
        in: query
        name: startDate
        type: string
      - description: End date for filtering statistics (RFC3339 format)
        in: query
        name: endDate
        type: string
      - default: 10
        description: Number of top events to return
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Event statistics for the organization
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventStatisticsResponse'
        "400":
          description: Invalid path or query parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (user does not have permission to view stats for
            this organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Get event statistics for a specific organization
      tags:
      - Events (Organization Statistics)
  /organizations/{orgId}/events:
    get:
      description: |-
        Lists events for an organization. Supports pagination and potential admin-only filters.
        Supports filtering by startDate, endDate, searchTerm, tagIds, status, government_funding_type_key, event_verification_type_key.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Filter by start date (YYYY-MM-DDTHH:MM:SSZ)
        in: query
        name: startDate
        type: string
      - description: Filter by end date (YYYY-MM-DDTHH:MM:SSZ)
        in: query
        name: endDate
        type: string
      - description: Search term for title and location_full_address
        in: query
        name: searchTerm
        type: string
      - description: Comma-separated list of tag IDs
        in: query
        name: tagIds
        type: string
      - description: Filter by status (e.g., published, archived, deleted, draft,
          hidden, cancelled)
        in: query
        name: status
        type: string
      - description: Filter by government funding type key
        in: query
        name: government_funding_type_key
        type: string
      - description: Filter by event verification type key
        in: query
        name: event_verification_type_key
        type: string
      - default: false
        description: Flag for admin view to include more details
        in: query
        name: isAdminView
        type: boolean
      - default: 10
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of events
          headers:
            X-Total-Count:
              description: Total number of events
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventResponse'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List events managed by the organization (Admin View)
      tags:
      - Events (Organization)
    post:
      consumes:
      - application/json
      description: Creates an event. Payload includes details like title, JsonContent,
        location_type, location_full_address, location_online_url, start_time, end_time,
        price, participant_limit, waitlist_limit, requires_approval_for_registration,
        tag_ids, required_verification_type_keys, contact_email, contact_phone, mediaeventitem,
        government_funding_keys.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateEventRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Newly created event
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (no permission in org)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Create a new event within an organization
      tags:
      - Events (Organization)
  /organizations/{orgId}/events/{eventId}:
    delete:
      description: Deletes a specific event within an organization.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Delete an event
      tags:
      - Events (Organization)
    get:
      description: Retrieves detailed admin view of an event, including counts, tags,
        required verifications, and media items.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Event details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Get detailed information about a specific event (Admin View)
      tags:
      - Events (Organization)
    put:
      consumes:
      - application/json
      description: Updates an event. Payload can include title, JsonContent, location_type,
        location_full_address, location_online_url, start_time, end_time, price, participant_limit,
        waitlist_limit, requires_approval_for_registration, tag_ids, required_verification_type_keys,
        contact_email, contact_phone, mediaeventitem, government_funding_keys, status,
        published_at.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID) to update
        in: path
        name: eventId
        required: true
        type: string
      - description: Event details to update. Only provide fields that need to be
          changed.
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateEventRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated event details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventResponse'
        "400":
          description: Invalid request (e.g., bad format, validation error)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (user does not have permission to update events in
            this organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Event or Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Update details of a specific event (Admin)
      tags:
      - Events (Organization)
  /organizations/{orgId}/events/{eventId}/media:
    get:
      description: Retrieves the list of media items associated with a specific event.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of media items
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse'
            type: array
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized" // Assuming auth is required
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden" // Assuming auth is required
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization or Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List media for an event
      tags:
      - Events (Organization)
      - Event Media Management
    post:
      consumes:
      - multipart/form-data
      description: Uploads image, video, or PDF for a specific post.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - description: Media file (image, video, or PDF)
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Uploaded post media item
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.MediaItemResponse'
        "400":
          description: Invalid request or file type/size
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/manager for organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization or Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Upload media for a post
      tags:
      - Events (Organization)
      - Event Media Management
  /organizations/{orgId}/events/{eventId}/media/{itemId}:
    delete:
      responses: {}
  /organizations/{orgId}/events/{eventId}/registrations:
    get:
      description: Admin view of registrations for a specific event within the organization,
        with filtering options. Supports pagination.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - default: 10
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      - description: Filter by registration status (e.g., confirmed, pending_approval,
          cancelled)
        in: query
        name: status
        type: string
      - description: Filter by specific User ID (UUID)
        in: query
        name: user_id
        type: string
      - description: Search by user name
        in: query
        name: search_name
        type: string
      - description: Filter by payment status (e.g., paid, unpaid, pending)
        in: query
        name: payment_status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of event registrations
          headers:
            X-Total-Count:
              description: Total number of registrations
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
            type: array
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/manager for organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization or Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List registrations for a specific event (Admin View)
      tags:
      - Events (Organization)
      - Event Registrations (Organization Admin)
  /organizations/{orgId}/events/{eventId}/registrations/{registrationId}/payment:
    patch:
      consumes:
      - application/json
      description: Admin action to update the payment status of a registration for
        an event.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - description: Registration ID (UUID)
        in: path
        name: registrationId
        required: true
        type: string
      - description: New payment status and staff ID, e.g. {\
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateEventRegistrationPaymentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated registration details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/manager for organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization, Event, or Registration not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Update payment status for a registration (Admin Action)
      tags:
      - Events (Organization)
      - Event Registrations (Organization Admin)
  /organizations/{orgId}/events/{eventId}/registrations/{registrationId}/status:
    patch:
      consumes:
      - application/json
      description: Admin action to update a specific registration's status (e.g.,
        approve, reject, mark attendance) for an event.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - description: Registration ID (UUID)
        in: path
        name: registrationId
        required: true
        type: string
      - description: New status and admin notes, e.g. {\
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateOrgEventRegistrationStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated registration details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventRegistrationResponse'
        "400":
          description: Invalid request or status transition
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/manager for organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization, Event, or Registration not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Update registration status (Admin Action)
      tags:
      - Events (Organization)
      - Event Registrations (Organization Admin)
  /organizations/{orgId}/events/{eventId}/tags:
    get:
      description: Retrieves the list of tags associated with a specific event.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of event tags" // Assuming payloads.TagResponse is the
            correct type
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.TagResponse'
            type: array
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized" // Assuming this endpoint requires auth based
            on context
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden" // Assuming this endpoint requires auth based on
            context
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization or Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List tags for an event
      tags:
      - Events (Organization)
  /organizations/{orgId}/events/{eventId}/tags/{tagId}:
    delete:
      description: Removes a tag from a specific event within an organization. Requires
        organization admin/event creator permission.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - description: Tag ID (UUID)
        in: path
        name: tagId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization, Event, or Tag not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Remove tag from event
      tags:
      - Events (Organization)
    post:
      description: Adds a tag to a specific event within an organization. Requires
        organization admin/event creator permission.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - description: Tag ID (UUID)
        in: path
        name: tagId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization, Event, or Tag not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Add tag to event
      tags:
      - Events (Organization)
  /organizations/{orgId}/events/{eventId}/verification-types:
    get:
      description: Retrieves the list of required verification types associated with
        a specific event.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of required verification type keys" // Assuming string
            is the correct type for keys
          schema:
            items:
              type: string
            type: array
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized" // Assuming this endpoint requires auth
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden" // Assuming this endpoint requires auth
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization or Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List required verification types for an event
      tags:
      - Events (Organization)
  /organizations/{orgId}/events/{eventId}/verification-types/{typeKey}:
    delete:
      description: Removes a required verification type from a specific event. Requires
        organization admin/event creator permission.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - description: Verification Type Key (e.g., hk_id)
        in: path
        name: typeKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization, Event, or Verification Type not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Remove required verification type from event
      tags:
      - Events (Organization)
    post:
      description: Adds a required verification type to a specific event. Requires
        organization admin/event creator permission.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - description: Verification Type Key (e.g., hk_id)
        in: path
        name: typeKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization, Event, or Verification Type not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Add required verification type to event
      tags:
      - Events (Organization)
  /organizations/{orgId}/events/{eventId}/volunteer-applications:
    get:
      description: Admin view of volunteer applications for a specific event within
        the organization. Supports pagination.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - default: 10
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of volunteer applications
          headers:
            X-Total-Count:
              description: Total number of applications
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventVolunteerApplicationResponse'
            type: array
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/manager for organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization or Event not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: List volunteer applications for an event (Admin View)
      tags:
      - Events (Organization)
      - Event Volunteering (Admin)
  /organizations/{orgId}/events/{eventId}/volunteer-applications/{applicationId}/review:
    patch:
      consumes:
      - application/json
      description: Admin action to review (approve or reject) a specific volunteer
        application for an event.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Event ID (UUID)
        in: path
        name: eventId
        required: true
        type: string
      - description: Volunteer Application ID (UUID)
        in: path
        name: applicationId
        required: true
        type: string
      - description: Review details (status, notes)
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ReviewVolunteerApplicationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated volunteer application details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.EventVolunteerApplicationResponse'
        "400":
          description: Invalid request or review action
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/manager for organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization, Event, or Application not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Review a volunteer application (Admin Action)
      tags:
      - Events (Organization)
      - Event Volunteering (Admin)
  /organizations/{orgId}/events/public:
    get:
      description: Lists public events for a specific organization. Supports filtering
        by startDate, endDate, searchTerm, tagIds, status, government_funding_type_key,
        event_verification_type_key. Supports pagination.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Filter by start date (YYYY-MM-DDTHH:MM:SSZ)
        in: query
        name: startDate
        type: string
      - description: Filter by end date (YYYY-MM-DDTHH:MM:SSZ)
        in: query
        name: endDate
        type: string
      - description: Search term for title and location_full_address
        in: query
        name: searchTerm
        type: string
      - description: Comma-separated list of tag IDs
        in: query
        name: tagIds
        type: string
      - description: Filter by status (e.g., published, archived, deleted, draft,
          hidden, cancelled)
        in: query
        name: status
        type: string
      - description: Filter by government funding type key
        in: query
        name: government_funding_type_key
        type: string
      - description: Filter by event verification type key
        in: query
        name: event_verification_type_key
        type: string
      - default: 10
        description: Limit number of results
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of public events for the organization
          headers:
            X-Total-Count:
              description: Total number of events
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PublicEventResponse'
            type: array
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List public events for a specific organization
      tags:
      - Events (Public)
  /organizations/{orgId}/files:
    get:
      description: Lists files and folders within an organization's storage. Use the
        `parent_folder_id` query parameter to list contents of a specific subfolder.
        Omitting `parent_folder_id` lists the root contents. Requires admin/staff
        privileges.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Parent Folder ID (UUID) to list contents of (optional, defaults
          to root)
        in: query
        name: parent_folder_id
        type: string
      - default: 50
        description: Limit number of results per page
        in: query
        maximum: 200
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        minimum: 0
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of files and folders in the specified location
          headers:
            X-Total-Count:
              description: Total number of items in the specified location
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse'
            type: array
        "400":
          description: Invalid ID format or query parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Parent folder not found (if specified)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve file list
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: List files and folders for an organization
      tags:
      - Content Management (Organization Files - Admin)
  /organizations/{orgId}/files/{fileId}/download:
    get:
      description: Downloads a specific file directly using its ID from the organization's
        file storage. Requires admin/staff privileges.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: File ID (UUID) to download
        in: path
        name: fileId
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: The requested organization file
          schema:
            type: file
        "400":
          description: Invalid ID format or trying to download a folder
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: File not found or does not belong to this organization
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Error preparing file for download
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Download a file directly from organization storage
      tags:
      - Content Management (Organization Files - Admin)
  /organizations/{orgId}/files/{fileOrFolderId}:
    delete:
      description: Deletes a file or folder from the organization's storage. If a
        folder is deleted, its contents are deleted recursively. Requires admin/staff
        privileges.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: File or Folder ID (UUID) to delete
        in: path
        name: fileOrFolderId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: File/folder not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to delete file/folder
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Delete a file or folder (recursively if folder)
      tags:
      - Content Management (Organization Files - Admin)
    put:
      consumes:
      - application/json
      description: Updates the name of a file/folder or moves it to a different parent
        folder. Provide `name` to rename, `parent_folder_id` to move. Both can be
        provided. Requires admin/staff privileges.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: File or Folder ID (UUID) to update
        in: path
        name: fileOrFolderId
        required: true
        type: string
      - description: New name and/or new parent_folder_id
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateOrganizationFileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Details of the updated file or folder
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse'
        "400":
          description: Invalid request body or ID format, or invalid operation (e.g.,
            cyclic move)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Operation not allowed (e.g., moving root)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: File/folder or target parent folder not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "409":
          description: Name conflict in the target location
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to update file/folder
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Rename a file or folder, or move it
      tags:
      - Content Management (Organization Files - Admin)
  /organizations/{orgId}/files/folder:
    post:
      consumes:
      - application/json
      description: Creates a new folder. A parent_folder_id can be provided to create
        a subfolder. Requires admin/staff privileges.
      parameters:
      - description: Organization ID (UUID) where the folder will be created
        in: path
        name: orgId
        required: true
        type: string
      - description: Folder name and optional parent_folder_id
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateOrganizationFolderRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Details of the newly created folder
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse'
        "400":
          description: Invalid request body or organization/parent_folder ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Parent folder not found (if specified)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "409":
          description: A folder or file with the same name already exists in the target
            location
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to create folder
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Create a new folder within an organization's file storage
      tags:
      - Content Management (Organization Files - Admin)
  /organizations/{orgId}/files/upload:
    post:
      consumes:
      - multipart/form-data
      description: Uploads a file to the organization's file storage. Can specify
        a parent_folder_id to upload into a specific folder, otherwise uploads to
        the root. Requires admin/staff privileges.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: File to upload
        in: formData
        name: file
        required: true
        type: file
      - description: Parent Folder ID (UUID) to upload into (optional, defaults to
          root)
        in: formData
        name: parent_folder_id
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Details of the uploaded organization file
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationFileResponse'
        "400":
          description: Invalid ID format, file upload error, or file type/size issue
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Parent folder not found (if specified)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "409":
          description: File name conflict in the target folder
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to process or save uploaded file
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Upload a file directly to an organization's folder
      tags:
      - Content Management (Organization Files - Admin)
  /organizations/{orgId}/join:
    post:
      description: Allows the authenticated user to join a specific organization as
        a member.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully joined organization" // Or return membership details
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Already a member or invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Join an organization
      tags:
      - Organizations
  /organizations/{orgId}/leave:
    delete:
      description: Allows the authenticated user to leave a specific organization.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Not a member or invalid request
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Leave an organization
      tags:
      - Organizations
  /organizations/{orgId}/logo:
    post:
      consumes:
      - multipart/form-data
      description: Uploads a logo for the organization. This action updates the organization's
        `image_url` and sets its `status` to `active`. Requires admin/owner permission.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Logo image file
        in: formData
        name: logo
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Updated organization with new logo URL and active status
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse'
        "400":
          description: Invalid request or file
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not admin/owner)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Organization not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Upload organization logo
      tags:
      - Organizations
  /organizations/{orgId}/posts:
    get:
      description: Retrieves a list of posts belonging to a specific organization.
        Includes drafts and hidden posts. Supports pagination and filtering by tag
        IDs.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - default: 10
        description: Limit number of results per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        minimum: 0
        name: offset
        type: integer
      - description: Comma-separated list of Tag IDs (UUIDs) to filter by
        in: query
        name: tag_ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of posts belonging to the organization
          headers:
            X-Total-Count:
              description: Total number of posts matching the query
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse'
            type: array
        "400":
          description: Invalid organization ID format or query parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve posts
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: List posts for an organization (admin view, includes drafts)
      tags:
      - Content Management (Posts - Admin)
    post:
      consumes:
      - application/json
      description: Author ID is automatically set from the authenticated user's token.
        The status of the post can be 'draft', 'published', or 'hidden'. If media
        items are included and the first one is an image, its URL might be used as
        the post's cover_image_url by the service.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Post content, status, tag IDs, and optional media items
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreatePostRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Newly created post with media items and cover image URL
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse'
        "400":
          description: Invalid request body or organization ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized - User ID not found in token
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to create post or retrieve full details after creation
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Create a new post within an organization
      tags:
      - Content Management (Posts - Admin)
  /organizations/{orgId}/posts/{postId}:
    delete:
      description: Deletes a specific post. Requires user to be the author or have
        appropriate permissions.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Post ID (UUID)
        format: uuid
        in: path
        name: postId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid post ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized - User cannot delete this post
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Post not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to delete post
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Delete a post
      tags:
      - Content Management (Posts - Admin)
    get:
      description: Retrieves full details of a specific post belonging to an organization,
        including associated media items. Does not filter by status (shows drafts,
        hidden, etc.).
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Post ID (UUID)
        format: uuid
        in: path
        name: postId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Post details with media items
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse'
        "400":
          description: Invalid post ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Post not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve post
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Get details of a specific post (admin view, includes media)
      tags:
      - Content Management (Posts - Admin)
    put:
      consumes:
      - application/json
      description: Updates the content, status, tags, or other attributes of an existing
        post. Requires user to be the author or have appropriate permissions.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Post ID (UUID)
        format: uuid
        in: path
        name: postId
        required: true
        type: string
      - description: Post content, status, tag IDs, etc. to update
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdatePostRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated post details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse'
        "400":
          description: Invalid request body or ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized - User cannot update this post
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Post not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to update post or retrieve details after update
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Update an existing post
      tags:
      - Content Management (Posts - Admin)
  /organizations/{orgId}/posts/{postId}/media:
    post:
      consumes:
      - multipart/form-data
      description: Uploads an image, video, or PDF file and associates it with a specific
        post. Requires appropriate permissions.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Post ID (UUID)
        format: uuid
        in: path
        name: postId
        required: true
        type: string
      - description: Media file to upload
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Details of the uploaded media item
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostMediaItemResponse'
        "400":
          description: Invalid post ID format, file upload error, or file type/size
            issue
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Post not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to process or save uploaded media
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Upload media for a post
      tags:
      - Content Management (Posts - Admin)
  /organizations/{orgId}/posts/{postId}/media/{mediaId}:
    delete:
      description: Deletes a specific media item associated with a post. Requires
        appropriate permissions.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Post ID (UUID)
        format: uuid
        in: path
        name: postId
        required: true
        type: string
      - description: Media Item ID (UUID)
        format: uuid
        in: path
        name: mediaId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid media ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Media item not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to delete media item
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Delete media from a post
      tags:
      - Content Management (Posts - Admin)
  /organizations/{orgId}/resources:
    get:
      description: Retrieves a list of resources belonging to a specific organization,
        including drafts and hidden ones. Supports pagination.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - default: 10
        description: Limit number of results per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        minimum: 0
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of resources belonging to the organization
          headers:
            X-Total-Count:
              description: Total number of resources matching the query
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse'
            type: array
        "400":
          description: Invalid organization ID format or query parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve resources
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: List resources for an organization (admin view, includes drafts)
      tags:
      - Content Management (Resources - Admin)
    post:
      consumes:
      - application/json
      description: Creates a new resource, which is a collection of downloadable files.
        Requires admin/staff privileges for the organization.
      parameters:
      - description: Organization ID (UUID) to associate the resource with
        in: path
        name: orgId
        required: true
        type: string
      - description: Details of the resource to create (title, description, visibility,
          status, etc.)
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreateResourceRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Newly created resource with its files (if any were included
            in request, though typically files are added separately)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse'
        "400":
          description: Invalid request body or organization ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to create resource or retrieve details after creation
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Create a new resource entry
      tags:
      - Content Management (Resources - Admin)
  /organizations/{orgId}/resources/{resourceId}:
    delete:
      description: Deletes a resource and its associated files. Requires admin/staff
        privileges.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Resource ID (UUID) of the resource to delete
        in: path
        name: resourceId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid resource ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Resource not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to delete resource
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Delete a resource entry
      tags:
      - Content Management (Resources - Admin)
    get:
      description: Retrieves full details of a specific resource, including its associated
        files. Does not filter by status.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Resource ID (UUID)
        format: uuid
        in: path
        name: resourceId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Resource details with associated files
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse'
        "400":
          description: Invalid resource ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Resource not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve resource
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Get details of a specific resource (admin view)
      tags:
      - Content Management (Resources - Admin)
    put:
      consumes:
      - application/json
      description: Updates an existing resource's details (title, description, visibility,
        status, etc.). Requires admin/staff privileges.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Resource ID (UUID) of the resource to update
        in: path
        name: resourceId
        required: true
        type: string
      - description: Resource details to update
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateResourceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated resource details with files
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse'
        "400":
          description: Invalid request body or resource ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Resource not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to update resource or retrieve details after update
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Update a resource entry
      tags:
      - Content Management (Resources - Admin)
  /organizations/{orgId}/resources/{resourceId}/files:
    post:
      consumes:
      - multipart/form-data
      description: Uploads a file and associates it with a specific resource. Requires
        admin/staff privileges.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Resource ID (UUID) to attach the file to
        in: path
        name: resourceId
        required: true
        type: string
      - description: The file to upload
        in: formData
        name: file
        required: true
        type: file
      - description: Optional description for the file
        in: formData
        name: description
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Details of the uploaded file
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceFileResponse'
        "400":
          description: Invalid input (e.g., missing file, resource ID format, file
            too large)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Resource not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to upload file or save its details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Upload a file to a resource
      tags:
      - Content Management (Resources - Admin)
  /organizations/{orgId}/resources/{resourceId}/files/{fileId}:
    delete:
      description: Deletes a specific file associated with a resource. Requires admin/staff
        privileges.
      parameters:
      - description: Organization ID (UUID)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Resource ID (UUID)
        format: uuid
        in: path
        name: resourceId
        required: true
        type: string
      - description: File ID (UUID) of the file to delete
        in: path
        name: fileId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid file ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: File not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to delete resource file
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Delete a file from a resource
      tags:
      - Content Management (Resources - Admin)
  /organizations/{orgId}/resources/{resourceIdOrSlug}/public:
    get:
      description: Retrieves the public details of a specific published resource,
        identified either by its UUID or its slug within the context of its organization.
      parameters:
      - description: Organization ID (UUID) (needed for slug lookup context)
        format: uuid
        in: path
        name: orgId
        required: true
        type: string
      - description: Resource ID (UUID) or Slug
        in: path
        name: resourceIdOrSlug
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Public resource details with associated files
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse'
        "400":
          description: Invalid organization ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Resource not found, not published, or not publicly visible
            in this organization context
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve resource
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Get public details of a specific published resource by ID or slug
      tags:
      - Content Management (Resources - Public)
  /organizations/{orgId}/volunteer/apply:
    post:
      consumes:
      - application/json
      description: Allows the authenticated user to submit an application to become
        a volunteer for an organization. This creates a user_volunteer_applications
        record with status 'pending'.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      - description: Application details (e.g., motivation letter)
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ApplyVolunteerRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Application submitted successfully (includes application_id).
            Status is 'pending'.
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid request (e.g., invalid orgId format, bad request body)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (e.g., user is not a member of the organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "409":
          description: Conflict (user already has an 'approved' application, or another
            'pending' application exists for this organization)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Apply for volunteer status in a specific organization
      tags:
      - Volunteer Management (User)
      - Organizations
  /organizations/{orgId}/volunteer/status:
    get:
      description: Retrieves the user's volunteer application for the specified organization
        if its status is 'approved'. This indicates the user is qualified to volunteer
        for that organization.
      parameters:
      - description: Organization ID (UUID)
        in: path
        name: orgId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Approved volunteer application. A 404 is returned if no 'approved'
            application exists.
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse'
        "400":
          description: Invalid organization ID format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: No approved volunteer application (qualification) found for
            this user in this organization.
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Check volunteer qualification status for the user in a specific organization
      tags:
      - Volunteer Management (User)
      - Organizations
  /organizations/{organization_id}/event-applications/pending-review:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of 'pending' volunteer applications
        for all events within a given organization.
      parameters:
      - description: Organization ID (UUID format)
        format: uuid
        in: path
        name: organization_id
        required: true
        type: string
      - default: 1
        description: Page number for pagination
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 20
        description: Number of items per page
        in: query
        maximum: 100
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved applications
          schema:
            allOf:
            - $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/db.ListPendingReviewEventVolunteerApplicationsForOrganizationRow'
                  type: array
              type: object
        "400":
          description: Invalid request parameters
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Organization not found or no pending applications
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      security:
      - BearerAuth // Placeholder for org admin/manager auth: []
      summary: List pending volunteer applications for an organization
      tags:
      - Organizations VolunteerApplications
  /organizations/debugjwt:
    get:
      description: A temporary handler to debug JWT claims on organization routes.
      produces:
      - application/json
      responses:
        "200":
          description: UserID and debug message
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Error processing claims or token not found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Temporary JWT Debug Handler for Organizations
      tags:
      - Debug
      - Organizations
  /posts:
    get:
      description: Retrieves a list of all publicly visible posts across all organizations.
        Supports pagination and filtering by organization ID and tag IDs. The cover
        image URL is derived from the first media item if available.
      parameters:
      - description: Filter by Organization ID (UUID)
        format: uuid
        in: query
        name: org_id
        type: string
      - default: 10
        description: Limit number of results per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        minimum: 0
        name: offset
        type: integer
      - description: Comma-separated list of Tag IDs (UUIDs) to filter by
        in: query
        name: tag_ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of published posts
          headers:
            X-Total-Count:
              description: Total number of published posts matching the query
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse'
            type: array
        "400":
          description: Invalid query parameter format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve published posts
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List all published posts
      tags:
      - Content Management (Posts - Public)
  /posts/{postId}/tags:
    get:
      consumes:
      - application/json
      description: Retrieves a list of all tags associated with a given post UUID.
      parameters:
      - description: Post ID (UUID)
        in: path
        name: postId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagsListResponse'
        "400":
          description: Invalid post ID format
          schema:
            $ref: '#/definitions/echo.Map'
        "500":
          description: Internal server error / Failed to get tags for post
          schema:
            $ref: '#/definitions/echo.Map'
      summary: Get all tags for a specific post
      tags:
      - Posts
      - Post Tags
  /posts/{postId}/tags/{tagId}:
    delete:
      consumes:
      - application/json
      description: Disassociates a post tag from a post.
      parameters:
      - description: Post ID (UUID)
        in: path
        name: postId
        required: true
        type: string
      - description: Post Tag ID (UUID)
        in: path
        name: tagId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid post ID or tag ID format
          schema:
            $ref: '#/definitions/echo.Map'
        "500":
          description: Internal server error / Failed to remove tag from post
          schema:
            $ref: '#/definitions/echo.Map'
      summary: Remove a tag from a post
      tags:
      - Posts
      - Post Tags
    post:
      consumes:
      - application/json
      description: Associates an existing post tag with an existing post.
      parameters:
      - description: Post ID (UUID)
        in: path
        name: postId
        required: true
        type: string
      - description: Post Tag ID (UUID)
        in: path
        name: tagId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid post ID or tag ID format
          schema:
            $ref: '#/definitions/echo.Map'
        "500":
          description: Internal server error / Failed to add tag to post
          schema:
            $ref: '#/definitions/echo.Map'
      summary: Add a tag to a post
      tags:
      - Posts
      - Post Tags
  /posts/{postIdOrSlug}:
    get:
      description: Retrieves details of a specific publicly visible post. The post
        can be identified by its UUID or its unique slug. The cover image URL is derived
        from the first media item if available.
      parameters:
      - description: Post ID (UUID) or Slug
        in: path
        name: postIdOrSlug
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Published post details with media items
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostResponse'
        "404":
          description: Post not found or not published
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve post
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Get details of a specific published post by ID or slug
      tags:
      - Content Management (Posts - Public)
  /resources:
    get:
      description: Retrieves a list of all publicly visible resources across all organizations.
        Supports pagination and filtering by organization ID and visibility.
      parameters:
      - description: Filter by Organization ID (UUID)
        format: uuid
        in: query
        name: organizationId
        type: string
      - description: Filter by visibility (e.g., public, org_only)
        enum:
        - public
        - org_only
        in: query
        name: visibility
        type: string
      - default: 10
        description: Limit number of results per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        minimum: 0
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of published resources
          headers:
            X-Total-Count:
              description: Total number of published resources matching the query
              type: string
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ResourceResponse'
            type: array
        "400":
          description: Invalid query parameter format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Failed to retrieve published resources
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List all published resources
      tags:
      - Content Management (Resources - Public)
  /resources/download/{orgId}/{resourceIdOrSlug}/{fileIdOrName}:
    get:
      description: Downloads a specific file associated with a public resource. Access
        checks are performed based on resource visibility before serving the file.
      parameters:
      - description: Organization ID (UUID) of the resource's organization
        in: path
        name: orgId
        required: true
        type: string
      - description: Resource ID (UUID) or Slug
        in: path
        name: resourceIdOrSlug
        required: true
        type: string
      - description: File ID (UUID) or exact File Name within the resource
        in: path
        name: fileIdOrName
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: The requested resource file
          schema:
            type: file
        "400":
          description: Invalid ID format in path
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Resource or File not found, or not publicly accessible
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Error preparing file for download
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Download a specific file from a resource (public)
      tags:
      - Content Management (Resources - Public)
  /tags/posts:
    get:
      consumes:
      - application/json
      description: Retrieves a list of all available post tags, ordered by tag name
        and language code.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagsListResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/echo.Map'
      summary: List all post tags
      tags:
      - Post Tags
    post:
      consumes:
      - application/json
      description: Creates a new post tag with name, language, and optional description.
      parameters:
      - description: Post Tag Create Payload
        in: body
        name: tag
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.CreatePostTagRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse'
        "400":
          description: Invalid request format or validation error
          schema:
            $ref: '#/definitions/echo.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/echo.Map'
      summary: Create a new post tag
      tags:
      - Post Tags
  /tags/posts/{tagId}:
    delete:
      consumes:
      - application/json
      description: Deletes a post tag by its UUID. Also removes associations from
        posts.
      parameters:
      - description: Post Tag ID (UUID)
        in: path
        name: tagId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Invalid tag ID format
          schema:
            $ref: '#/definitions/echo.Map'
        "500":
          description: Internal server error / Failed to delete tag
          schema:
            $ref: '#/definitions/echo.Map'
      summary: Delete a post tag
      tags:
      - Post Tags
    get:
      consumes:
      - application/json
      description: Retrieves details of a post tag using its UUID.
      parameters:
      - description: Post Tag ID (UUID)
        in: path
        name: tagId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse'
        "400":
          description: Invalid tag ID format
          schema:
            $ref: '#/definitions/echo.Map'
        "404":
          description: Tag not found
          schema:
            $ref: '#/definitions/echo.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/echo.Map'
      summary: Get a specific post tag by ID
      tags:
      - Post Tags
    put:
      consumes:
      - application/json
      description: Updates an existing post tag's name, language, or description.
      parameters:
      - description: Post Tag ID (UUID)
        in: path
        name: tagId
        required: true
        type: string
      - description: Post Tag Update Payload
        in: body
        name: tag
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdatePostTagRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.PostTagResponse'
        "400":
          description: Invalid request format or validation error / Invalid tag ID
          schema:
            $ref: '#/definitions/echo.Map'
        "404":
          description: Tag not found
          schema:
            $ref: '#/definitions/echo.Map'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/echo.Map'
      summary: Update an existing post tag
      tags:
      - Post Tags
  /users/me:
    get:
      description: Fetches the profile information of the authenticated user.
      produces:
      - application/json
      responses:
        "200":
          description: User profile data
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Failed to retrieve user profile
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Retrieve current user's profile
      tags:
      - User Profile
    patch:
      consumes:
      - application/json
      description: Updates user profile fields like display name, bio, language preferences,
        or user settings like notification preferences.
      parameters:
      - description: Fields to update (include only fields to be changed)
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UpdateUserProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated user profile data
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse'
        "400":
          description: Invalid request payload or validation error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Failed to update user profile
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update current user's profile or settings
      tags:
      - User Profile
  /users/me/organizations:
    get:
      consumes:
      - application/json
      description: Retrieves a list of organizations that the currently authenticated
        user is an active member of.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.OrganizationResponse'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get User's Organizations
      tags:
      - Users
      - Organizations
  /users/me/phone/initiate-change:
    post:
      consumes:
      - application/json
      description: Starts the process to change the user's registered phone number.
        Sends OTP to new number.
      parameters:
      - description: New phone number, client ID, and state for phone change initiation
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.InitiatePhoneChangeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Details of the initiated phone change flow, including state
            and flow ID
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.InitiatePhoneChangeResponse'
        "400":
          description: Invalid request or phone already in use
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "409":
          description: Conflict if the new phone number is already in use by another
            user
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Failed to initiate phone number change
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Initiate phone number change
      tags:
      - User Profile
  /users/me/phone/verify-change:
    post:
      consumes:
      - application/json
      description: Verifies the OTP sent to the new phone number to complete the change.
      parameters:
      - description: State, OTP, and new phone number for verification
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.VerifyPhoneChangeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Phone number changed successfully, returns updated user profile
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.VerifyPhoneChangeResponse'
        "400":
          description: Invalid OTP, state, or request format
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Failed to verify phone number change
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Verify OTP for new phone number
      tags:
      - User Profile
  /users/me/profile-picture:
    post:
      consumes:
      - multipart/form-data
      description: Uploads a new profile picture for the current authenticated user.
      parameters:
      - description: Image file (JPEG, PNG, GIF, WebP; max 5MB)
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Updated user profile with new profile_picture_url
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserProfileResponse'
        "400":
          description: Invalid request or file type/size
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Failed to upload profile picture
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Upload user profile picture
      tags:
      - User Profile
  /users/me/stats:
    get:
      consumes:
      - application/json
      description: Retrieves statistics for the currently authenticated user.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.APIUserStats'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get User Statistics
      tags:
      - Users
  /users/me/uuid:
    get:
      description: Retrieves the UUID of the currently authenticated user.
      produces:
      - application/json
      responses:
        "200":
          description: User UUID
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserUUIDResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get current user's UUID
      tags:
      - User Profile
  /users/me/verifications:
    get:
      description: Retrieves a list of verification requests submitted by the authenticated
        user.
      produces:
      - application/json
      responses:
        "200":
          description: List of user's verification requests
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestResponse'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: List all verification requests for the current user
      tags:
      - User Verification
  /users/me/verifications/{reqID}:
    delete:
      description: Allows user to request deletion of their verification data associated
        with a request.
      parameters:
      - description: Verification Request ID
        in: path
        name: reqID
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not owner)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Request not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Request deletion of verification data
      tags:
      - User Verification
    get:
      description: Retrieves details for a specific verification request owned by
        the user.
      parameters:
      - description: Verification Request ID
        in: path
        name: reqID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Verification request details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.UserVerificationRequestResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not owner)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Request not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Get details of a specific verification request
      tags:
      - User Verification
  /users/me/verifications/documents/{docID}:
    get:
      description: Retrieves a specific verification document submitted by the authenticated
        user.
      parameters:
      - description: Verification Document ID
        in: path
        name: docID
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: Verification document file
          schema:
            type: file
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "403":
          description: Forbidden (not owner)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "404":
          description: Document not found or access denied
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_utils.ErrorResponse'
      summary: Get a user's verification document
      tags:
      - User Verification
  /users/me/volunteer/applications:
    get:
      description: Retrieves a list of volunteer applications submitted by the authenticated
        user across all organizations.
      produces:
      - application/json
      responses:
        "200":
          description: List of user's volunteer applications
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List current user's volunteer applications across all orgs
      tags:
      - Volunteer Management (User)
      - User Profile
  /users/me/volunteer/applications/{appId}:
    get:
      description: Retrieves details for a specific volunteer application submitted
        by the authenticated user.
      parameters:
      - description: Application ID (UUID)
        in: path
        name: appId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Application details
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "403":
          description: Forbidden (not owner of application)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "404":
          description: Application not found
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: Get details of a specific volunteer application owned by the user
      tags:
      - Volunteer Management (User)
      - User Profile
  /users/me/volunteer/qualifications:
    get:
      description: Retrieves a list of the user's volunteer applications that have
        a status of 'approved'. An approved application signifies the user is qualified
        to volunteer for that organization.
      produces:
      - application/json
      responses:
        "200":
          description: List of approved volunteer applications (representing qualifications)
          schema:
            items:
              $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.VolunteerApplicationResponse'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      summary: List organizations the user is a qualified volunteer for
      tags:
      - Volunteer Management (User)
      - User Profile
  /ws:
    get:
      description: Establishes a WebSocket connection. Requires authentication via
        token in query param or valid Bearer header initially.
      parameters:
      - description: Authentication token (alternative to Bearer header for WS)
        in: query
        name: token
        type: string
      responses:
        "101":
          description: Switching Protocols
          schema:
            type: string
        "400":
          description: Bad Request (e.g. missing auth or invalid token)
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
        "500":
          description: Internal server error during upgrade
          schema:
            $ref: '#/definitions/Membership-SAAS-System-Backend_internal_payloads.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Upgrade connection to WebSocket for real-time notifications
      tags:
      - WebSocket
      - Real-time
schemes:
- http
- https
securityDefinitions:
  ApiKeyAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
