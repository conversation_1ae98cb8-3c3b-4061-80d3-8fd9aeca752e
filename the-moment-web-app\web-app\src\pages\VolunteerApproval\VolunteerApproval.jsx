import React, { useState, useEffect, useMemo } from 'react';
import { Table, Tabs, App, Tag, Space } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  IdcardOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { formatSimpleDateTime } from '../../utils/dateFormatter';
import { volunteerService } from '../../services/volunteerService';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { useUser } from '../../contexts/UserContext';
import ErrorPage from '../ErrorPage';
import '../../styles/TableBorder.css';

const VolunteerApproval = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { message } = App.useApp();
  const { currentOrganization } = useOrganization();
  const { currentUser } = useUser();
  
  const isSuperAdmin = currentUser?.role === 'super_admin';
  const [showErrorPage, setShowErrorPage] = useState(false);
  
  useEffect(() => {
    if (currentOrganization && currentOrganization.id === ALL_ORGANIZATION_ID && !isSuperAdmin) {
      message.info(t('messages.notAvailableForAllOrgs'));
      setShowErrorPage(true);
    } else {
      setShowErrorPage(false);
    }
  }, [currentOrganization, isSuperAdmin, message, t]);
  
  const searchParams = useMemo(() => new URLSearchParams(location.search), [location.search]);
  
  const tabFromUrl = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabFromUrl || 'qualification');
  const [activeFilter, setActiveFilter] = useState('pending');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    position: ['bottomCenter'],
    showTotal: total => t('common.totalItems', { total: total })
  });

  const [currentData, setCurrentData] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [fetchError, setFetchError] = useState(null);

  useEffect(() => {
    const loadVolunteerData = async () => {
      setIsLoading(true);
      setFetchError(null);

      if (currentOrganization?.id === ALL_ORGANIZATION_ID && !isSuperAdmin) {
        setCurrentData([]);
        setTotalItems(0);
        setIsLoading(false);
        return;
      }

      const orgId = currentOrganization?.id;

      if (!orgId) {
        setCurrentData([]);
        setTotalItems(0);
        setIsLoading(false);
        return;
      }

      try {
        const page = pagination.current;
        const pageSizeVal = pagination.pageSize;
        const offset = (page - 1) * pageSizeVal;
        let response;

        if (activeTab === 'qualification') {
          const params = {
            status: activeFilter,
            limit: pageSizeVal,
            offset: offset,
          };
          response = await volunteerService.listOrgVolunteerApplications(orgId, params);
        } else {
          const params = {
            status: activeFilter,
            limit: pageSizeVal,
            offset: offset,
          };
          response = await volunteerService.listEventVolunteerApplicationsForOrg(orgId, params);
        }

        setCurrentData(response?.applications || []);
        setTotalItems(response?.total || 0);

      } catch (err) {
        setFetchError(err);
        message.error(t('messages.fetchError') + (err.message ? `: ${err.message}` : ''));
        console.error('Error fetching volunteer data in VolunteerApproval:', err);
        setCurrentData([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    };

    loadVolunteerData();

  }, [
    currentOrganization?.id,
    activeTab,
    activeFilter,
    pagination.current,
    pagination.pageSize,
    message,
    t,
    isSuperAdmin
  ]);

  const handleTabChange = (key) => {
    setActiveTab(key);
    setActiveFilter('pending');
    setPagination(prev => ({ ...prev, current: 1 }));
    const newSearchParams = new URLSearchParams(location.search);
    newSearchParams.set('tab', key);
    navigate({ pathname: location.pathname, search: newSearchParams.toString() }, { replace: true });
  };

  useEffect(() => {
    if (tabFromUrl && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    }
    const currentTabInUrl = searchParams.get('tab');
    if (activeTab !== currentTabInUrl) {
        const newSearchParamsForUpdate = new URLSearchParams(location.search);
        newSearchParamsForUpdate.set('tab', activeTab);
        navigate({ pathname: location.pathname, search: newSearchParamsForUpdate.toString() }, { replace: true });
    }

  }, [tabFromUrl, activeTab, location.pathname, location.search, navigate, searchParams]);

  const handleTableChange = (newPagination) => {
    setPagination(prev => ({ ...prev, current: newPagination.current }));
  };

  const handleFilterChange = (status) => {
    setActiveFilter(status);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const getStatusTag = (status) => {
    let displayStatus = status?.toLowerCase();
    if (displayStatus === 'pending_review') displayStatus = 'pending';

    const statusConfig = {
      pending: { color: 'default', icon: <ClockCircleOutlined /> },
      approved: { color: 'success', icon: <CheckCircleOutlined /> },
      rejected: { color: 'error', icon: <CloseCircleOutlined /> },
    };
    const config = statusConfig[displayStatus] || statusConfig.pending;
    return (
      <Tag color={config.color} icon={config.icon}>
        {t(`common.${displayStatus || 'pending'}`)}
      </Tag>
    );
  };

  const qualificationColumns = [
    { title: t('volunteerApproval.table.columns.name'), dataIndex: 'applicant_display_name', key: 'name', sorter: (a, b) => (a.applicant_display_name || '').localeCompare(b.applicant_display_name || ''), width: 200 },
    { title: t('volunteerApproval.table.columns.phoneNumber'), dataIndex: 'applicant_phone', key: 'phoneNumber', width: 180 },
    {
      title: t('volunteerApproval.table.columns.applicationTime'),
      dataIndex: 'application_date',
      key: 'applicationTime',
      render: (text) => text ? formatSimpleDateTime(text) : '-',
      sorter: (a, b) => new Date(a.application_date) - new Date(b.application_date),
      width: 180,
    },
    { title: t('volunteerApproval.table.columns.status'), dataIndex: 'status', key: 'status', render: (status) => getStatusTag(status), width: 120 },
  ];

  const eventRegistrationColumns = [
    { title: t('volunteerApproval.table.columns.name'), dataIndex: 'user_display_name', key: 'name', sorter: (a, b) => (a.user_display_name || '').localeCompare(b.user_display_name || ''), width: 200 },
    { title: t('volunteerApproval.table.columns.phoneNumber'), dataIndex: 'user_phone', key: 'phoneNumber', width: 180 },
    { title: t('volunteerApproval.table.columns.eventName'), dataIndex: 'event_title', key: 'eventName', width: 250 },
    {
      title: t('volunteerApproval.table.columns.applicationTime'),
      dataIndex: 'applied_at',
      key: 'applicationTime',
      render: (text) => text ? formatSimpleDateTime(text) : '-',
      sorter: (a, b) => new Date(a.applied_at) - new Date(b.applied_at),
      width: 180,
    },
    { title: t('volunteerApproval.table.columns.status'), dataIndex: 'status', key: 'status', render: (status) => getStatusTag(status), width: 120 },
  ];

  const handleRowClick = (record) => {
    const appId = record.id || record.applicationId;
    if (!appId) {
        message.error(t('volunteerApproval.messages.missingApplicationId'));
        return;
    }
    navigate(`/volunteers-approval/${activeTab}/details/${appId}`, {
      state: { 
        type: activeTab,
        eventId: record.event_id,
        orgId: record.organization_id || currentOrganization?.id,
        applicationId: appId 
      }
    });
  };

  const FilterButtons = () => {
    const buttonStyles = {
      pending: { active: 'border-gray-500 text-gray-600 bg-gray-100', default: 'border-gray-200 text-gray-600 hover:border-gray-500 hover:text-gray-600' },
      approved: { active: 'border-green-500 text-green-600 bg-green-50', default: 'border-gray-200 text-gray-600 hover:border-green-500 hover:text-green-600' },
      rejected: { active: 'border-red-500 text-red-600 bg-red-50', default: 'border-gray-200 text-gray-600 hover:border-red-500 hover:text-red-600' }
    };
    return (
      <div className="grid grid-cols-3 sm:flex sm:flex-wrap gap-2 mb-6">
        {Object.entries({
          pending: { icon: <ClockCircleOutlined />, label: t('volunteerApproval.buttons.pending') },
          approved: { icon: <CheckCircleOutlined />, label: t('volunteerApproval.buttons.approved') },
          rejected: { icon: <CloseCircleOutlined />, label: t('volunteerApproval.buttons.rejected') }
        }).map(([status, { icon, label }]) => (
          <button
            key={status}
            onClick={() => handleFilterChange(status)}
            className={`px-2 sm:px-4 py-2 border rounded-md font-medium text-xs sm:text-sm transition-all duration-200 flex items-center gap-1 sm:gap-2 justify-center 
              ${activeFilter === status ? buttonStyles[status].active : buttonStyles[status].default}
            `}
          >
            <div className="flex items-center">
              <span className="text-lg sm:text-base">{icon}</span>
              <span className="inline ml-1">{label}</span>
            </div>
          </button>
        ))}
      </div>
    );
  };

  const TableContent = () => (
    <div className="border rounded-lg overflow-hidden">
      <Table
        loading={isLoading}
        dataSource={currentData}
        columns={activeTab === 'qualification' ? qualificationColumns : eventRegistrationColumns}
        pagination={{ ...pagination, total: totalItems }}
        onChange={handleTableChange}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        onRow={(record) => ({ onClick: () => handleRowClick(record) })}
        className="border-collapse"
      />
    </div>
  );

  const tabItems = [
    { key: 'qualification', label: <span className="flex items-center gap-2"><IdcardOutlined />{t('volunteerApproval.tabs.identityApproval')}</span>, children: <div><FilterButtons /><TableContent /></div> },
    { key: 'eventRegistration', label: <span className="flex items-center gap-2"><CalendarOutlined />{t('volunteerApproval.tabs.eventVolunteerApproval')}</span>, children: <div><FilterButtons /><TableContent /></div> },
  ];

  if (showErrorPage) {
    return <ErrorPage type="403" />;
  }

  return (
    <div className="min-h-screen p-2 md:p-6">
      <div className="max-w-7xl mx-auto">
        <Tabs activeKey={activeTab} items={tabItems} className="border rounded-lg p-4" onChange={handleTabChange} />
      </div>
    </div>
  );
};

export default VolunteerApproval;
