import React from 'react';
import { Alert } from 'antd';

const ErrorDisplay = ({ message, description, type = 'error', onRetry, showRetry = true }) => {
  if (!message) return null;

  return (
    <Alert
      message={message}
      description={description}
      type={type}
      showIcon
      closable
      style={{ margin: '20px 0' }}
      action={
        showRetry && onRetry ? (
          <button onClick={onRetry} className="ant-btn ant-btn-sm ant-btn-link">
            Retry
          </button>
        ) : null
      }
    />
  );
};

export default ErrorDisplay; 