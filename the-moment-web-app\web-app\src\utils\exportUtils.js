import moment from 'moment';
import { message } from 'antd';
import { t } from 'i18next';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Import Chinese fonts (they will be automatically added to jsPDF)
import '../assets/fonts/notosanstc-normal.js';
import '../assets/fonts/notosanstc-bold.js';

/**
 * Get ordered parent fields based on column order and checked list
 * @param {Array} columnOrder - Array of column objects in display order
 * @param {Array} checkedList - Array of checked column keys
 * @param {Array} fixedColumns - Array of fixed column keys to exclude
 * @param {Object} parentFieldsMap - Mapping of column keys to data field names
 * @returns {Array} - Ordered array of parent field names
 */
const getOrderedParentFields = (columnOrder, checkedList, fixedColumns = [], parentFieldsMap = {}) => {
  // Filter columns that are visible (checked) and not fixed
  const visibleColumns = columnOrder.filter(col => 
    checkedList.includes(col.key) && !fixedColumns.includes(col.key)
  );
  
  // Map to data field names and filter out undefined values
  return visibleColumns
    .map(col => parentFieldsMap[col.key] || col.key)
    .filter(Boolean);
};

/**
 * Export data as CSV file with hierarchical structure
 * @param {Array} data - Array of objects to export
 * @param {String} filename - Filename without extension
 * @param {Object} options - Optional configuration including column headers mapping
 * @returns {Promise} - Resolves when export is complete
 */
export const exportAsCSV = (data, filename, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      if (!data || !Array.isArray(data) || data.length === 0) {
        throw new Error('No data to export');
      }

      // Check if we should preserve hierarchical structure
      const preserveHierarchy = options.preserveHierarchy === true;
      let csvRows = [];
      
      // Support for translated headers
      const headerMap = options.headerMap || {};
      
      if (preserveHierarchy) {
        // Build expanded rows with sub-items
        const expandedRows = [];
        
        // Use ordered parent fields if provided, otherwise fall back to original logic
        let parentFields;
        if (options.columnOrder && options.checkedList && options.parentFieldsMap) {
          parentFields = getOrderedParentFields(
            options.columnOrder, 
            options.checkedList, 
            options.fixedColumns, 
            options.parentFieldsMap
          );
        } else {
          // Fallback to original logic
          parentFields = options.parentFields || Object.keys(data[0]).filter(key => key !== 'verifications');
        }
        
        const childFields = options.childFields || ['type', 'status', 'submitted_at', 'reviewed_at'];
        
        // Create expanded header row with both parent and child columns
        const headersRow = [
          ...parentFields.map(field => headerMap[field] || field),
          ...childFields.map(field => headerMap[field] || field)
        ];
        csvRows.push(headersRow.join(','));
        
        // Create data rows with parent data repeated for each verification
        data.forEach(item => {
          if (item.verifications && item.verifications.length > 0) {
            // For each verification, create a row with parent data + verification data
            item.verifications.forEach(verification => {
              const row = [
                // Include parent fields in correct order
                ...parentFields.map(field => 
                  `"${(item[field] !== undefined && item[field] !== null) ? 
                    String(item[field]).replace(/"/g, '""') : 
                    ''}"`
                ),
                // Include verification fields
                ...childFields.map(field => 
                  `"${(verification[field] !== undefined && verification[field] !== null) ? 
                    String(verification[field]).replace(/"/g, '""') : 
                    ''}"`
                )
              ];
              expandedRows.push(row.join(','));
            });
          } else {
            // No verifications, just include parent data and empty child fields
            const row = [
              // Include parent fields in correct order
              ...parentFields.map(field => 
                `"${(item[field] !== undefined && item[field] !== null) ? 
                  String(item[field]).replace(/"/g, '""') : 
                  ''}"`
              ),
              // Include empty verification fields
              ...childFields.map(() => '""')
            ];
            expandedRows.push(row.join(','));
          }
        });
        
        csvRows = csvRows.concat(expandedRows);
      } else {
        // Original flattened approach with proper ordering
        const flattenedData = data.map(item => {
          const flatItem = { ...item };
          
          // Handle nested arrays like verifications
          if (item.verifications && Array.isArray(item.verifications)) {
            flatItem.verification_types = item.verifications.map(v => v.type || '').join(', ');
            flatItem.verification_statuses = item.verifications.map(v => v.status || '').join(', ');
            delete flatItem.verifications;
          }
          
          return flatItem;
        });
        
        // Get available columns with proper ordering
        let availableColumns;
        if (options.columnOrder && options.checkedList && options.parentFieldsMap) {
          availableColumns = getOrderedParentFields(
            options.columnOrder, 
            options.checkedList, 
            options.fixedColumns, 
            options.parentFieldsMap
          );
        } else {
          // Fallback to original logic
          availableColumns = options.parentFields || Object.keys(flattenedData[0] || {});
        }
        
        // Create CSV content
        const translatedHeaders = availableColumns.map(header => headerMap[header] || header);
        
        csvRows = [
          translatedHeaders.join(','),
          ...flattenedData.map(row => 
            availableColumns.map(header => 
              `"${(row[header] !== undefined && row[header] !== null) ? 
                String(row[header]).replace(/"/g, '""') : 
                ''}"`
            ).join(',')
          )
        ];
      }
      
      const csvContent = csvRows.join('\n');
      
      // Add UTF-8 BOM to ensure Excel recognizes UTF-8 encoding correctly
      const BOM = "\uFEFF";
      const csvContentWithBOM = BOM + csvContent;

      // Create blob with UTF-8 encoding explicitly specified
      const blob = new Blob([csvContentWithBOM], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      resolve();
    } catch (error) {
      console.error('CSV export error:', error);
      reject(error);
    }
  });
};

/**
 * Export data as PDF file with hierarchical structure and proper CJK support
 * @param {Array} data - Array of objects to export
 * @param {String} filename - Filename without extension
 * @param {Object} options - Optional configuration including title, headers, and table styling
 * @returns {Promise} - Resolves when export is complete
 */
export const exportAsPDF = (data, filename, options = {}) => {
  return new Promise(async (resolve, reject) => {
    try {
      if (!data || !Array.isArray(data) || data.length === 0) {
        throw new Error('No data to export');
      }

      // Create new PDF document
      const doc = new jsPDF({
        orientation: options.orientation || 'landscape',
        unit: 'mm',
        format: options.format || 'a4'
      });
      
      // Set Chinese font as default font
      doc.setFont('notosanstc', 'normal');

      // Set document properties
      const title = options.title || filename;
      doc.setProperties({
        title: title,
        subject: options.subject || title,
        author: options.author || 'System Export',
        keywords: options.keywords || 'export,data',
        creator: options.creator || 'Export Utility'
      });

      // Add Title and Export Date
      const exportDate = moment().format('YYYY-MM-DD HH:mm:ss');
      const exportDateLabel = options.exportDateLabel || 'Export date:';
      doc.setFontSize(16);
      doc.text(title, 14, 20);
      doc.setFontSize(10);
      doc.text(`${exportDateLabel} ${exportDate}`, 14, 28);
      
      // Support for translated headers
      const headerMap = options.headerMap || {};
      const preserveHierarchy = options.preserveHierarchy === true;
      
      let head = [];
      let body = [];
      
      if (preserveHierarchy) {
        let parentFields;
        if (options.columnOrder && options.checkedList && options.parentFieldsMap) {
          parentFields = getOrderedParentFields(
            options.columnOrder, 
            options.checkedList, 
            options.fixedColumns, 
            options.parentFieldsMap
          );
        } else {
          parentFields = options.parentFields || Object.keys(data[0]).filter(key => key !== 'verifications');
        }
        
        const childFields = options.childFields || ['type', 'status', 'submitted_at', 'reviewed_at'];
        
        // Create header row
        const parentHeaders = parentFields.map(field => headerMap[field] || field);
        const childHeaders = childFields.map(field => headerMap[field] || field);
        head = [parentHeaders.concat(childHeaders)];

        // Create data rows
        data.forEach(item => {
          if (item.verifications && item.verifications.length > 0) {
            item.verifications.forEach((verification, index) => {
              const parentData = parentFields.map(field => (item[field] !== undefined && item[field] !== null) ? String(item[field]) : '');
              const childData = childFields.map(field => (verification[field] !== undefined && verification[field] !== null) ? String(verification[field]) : '');
              
              if (index === 0) {
                // First child row includes parent data
                body.push(parentData.concat(childData));
              } else {
                // Subsequent child rows only have child data, preceded by empty cells for parent columns
                body.push(Array(parentFields.length).fill('').concat(childData));
              }
            });
          } else {
            // No verifications, just parent data and empty cells for child columns
            const parentData = parentFields.map(field => (item[field] !== undefined && item[field] !== null) ? String(item[field]) : '');
            body.push(parentData.concat(Array(childFields.length).fill('')));
          }
        });

      } else {
        // Flattened approach
        let availableColumns;
        if (options.columnOrder && options.checkedList && options.parentFieldsMap) {
          availableColumns = getOrderedParentFields(
            options.columnOrder, 
            options.checkedList, 
            options.fixedColumns, 
            options.parentFieldsMap
          );
        } else {
          availableColumns = options.parentFields || Object.keys(data[0] || {});
        }
        
        head = [availableColumns.map(header => headerMap[header] || header)];
        body = data.map(row => 
          availableColumns.map(header => 
            (row[header] !== undefined && row[header] !== null) ? String(row[header]) : ''
          )
        );
      }
      
      doc.autoTable({
        startY: 32,
        head: head,
        body: body,
        theme: 'grid',
        styles: {
          font: 'notosanstc', // Use the Chinese font for table content
          fontSize: 8,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [66, 133, 244], // Google Blue
          textColor: 255,
          font: 'notosanstc',
          fontStyle: 'bold',
        },
        bodyStyles: {
          font: 'notosanstc',
          fontStyle: 'normal',
        },
        didParseCell: function (data) {
          // Ensure all cells use the Chinese font
          data.cell.styles.font = 'notosanstc';
        },
      });

      // Save the PDF
      doc.save(`${filename}.pdf`);
      
      resolve();
    } catch (error) {
      console.error('PDF export error:', error);
      message.error(t('export.pdfError'));
      reject(error);
    }
  });
};

/**
 * Export data as PNG image with proper CJK text support
 * @param {Array} data - Array of objects to export
 * @param {String} filename - Filename without extension
 * @param {Object} options - Optional configuration
 * @returns {Promise} - Resolves when export is complete
 */
export const exportDataAsPNG = async (data, filename, options = {}) => {
  try {
    if (!data || !Array.isArray(data) || data.length === 0) {
      throw new Error('No data to export');
    }
    
    // Create a temporary div for rendering with better CJK support
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '1200px'; // Wider for better Chinese rendering
    container.style.backgroundColor = '#ffffff'; // Ensure white background
    container.style.padding = '20px';
    document.body.appendChild(container);
    
    try {
      // Support for translated headers
      const headerMap = options.headerMap || {};
      const preserveHierarchy = options.preserveHierarchy === true;
      
      // Build HTML with styles for better rendering
      let tableHtml = `
        <style>
          * { font-family: "Arial Unicode MS", "Microsoft YaHei", "SimHei", sans-serif; }
          body { background-color: white; color: black; }
          table { width: 100%; border-collapse: collapse; margin-top: 15px; }
          th { background-color: #4285f4; color: white; padding: 8px; text-align: left; border: 1px solid #ddd; font-weight: bold; font-size: 13px; }
          td { padding: 8px; border: 1px solid #ddd; font-size: 12px; }
          tr:nth-child(even) { background-color: #f9f9f9; }
          .child-row { background-color: #e9ecef !important; }
          .child-row td:first-child { padding-left: 15px; font-style: italic; }
          .title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
          .export-date { font-size: 12px; margin-bottom: 15px; }
        </style>
        <div class="title">${options.title || filename}</div>
        <div class="export-date">${options.exportDateLabel || 'Export date:'} ${moment().format('YYYY-MM-DD HH:mm:ss')}</div>
        <table>
          <thead><tr>`;
      
      if (preserveHierarchy) {
        // Use combined headers for hierarchical view with proper ordering
        let parentFields;
        if (options.columnOrder && options.checkedList && options.parentFieldsMap) {
          parentFields = getOrderedParentFields(
            options.columnOrder, 
            options.checkedList, 
            options.fixedColumns, 
            options.parentFieldsMap
          );
        } else {
          // Fallback to original logic
          parentFields = options.parentFields || Object.keys(data[0]).filter(key => key !== 'verifications');
        }
        
        const childFields = options.childFields || ['type', 'status', 'submitted_at', 'reviewed_at'];
        
        // Create all column headers in correct order
        [...parentFields, ...childFields].forEach(field => {
          tableHtml += `<th>${headerMap[field] || field}</th>`;
        });
      } else {
        // Use provided parent fields or all keys for flattened view with proper ordering
        let availableColumns;
        if (options.columnOrder && options.checkedList && options.parentFieldsMap) {
          availableColumns = getOrderedParentFields(
            options.columnOrder, 
            options.checkedList, 
            options.fixedColumns, 
            options.parentFieldsMap
          );
        } else {
          // Fallback to original logic
          availableColumns = options.parentFields || Object.keys(data[0]);
        }
        
        availableColumns.forEach(field => {
          tableHtml += `<th>${headerMap[field] || field}</th>`;
        });
      }
      
      tableHtml += `</tr></thead><tbody>`;
      
      // Add data rows
      if (preserveHierarchy) {
        // Create expanded rows with hierarchy and proper ordering
        let parentFields;
        if (options.columnOrder && options.checkedList && options.parentFieldsMap) {
          parentFields = getOrderedParentFields(
            options.columnOrder, 
            options.checkedList, 
            options.fixedColumns, 
            options.parentFieldsMap
          );
        } else {
          // Fallback to original logic
          parentFields = options.parentFields || Object.keys(data[0]).filter(key => key !== 'verifications');
        }
        
        const childFields = options.childFields || ['type', 'status', 'submitted_at', 'reviewed_at'];
        
        data.forEach((item, itemIndex) => {
          // Parent row
          tableHtml += `<tr>`;
          
          // Parent fields in correct order
          parentFields.forEach(field => {
            tableHtml += `<td>${item[field] || ''}</td>`;
          });
          
          // Empty cells for child fields in parent row
          childFields.forEach(() => {
            tableHtml += `<td></td>`;
          });
          
          tableHtml += `</tr>`;
          
          // Child rows for verifications
          if (item.verifications && item.verifications.length > 0) {
            item.verifications.forEach(verification => {
              tableHtml += `<tr class="child-row">`;
              
              // Empty parent fields except first one
              parentFields.forEach((field, fieldIndex) => {
                if (fieldIndex === 0) {
                  tableHtml += `<td>→ ${item[field] || ''}</td>`;
                } else {
                  tableHtml += `<td></td>`;
                }
              });
              
              // Verification fields
              childFields.forEach(field => {
                tableHtml += `<td>${verification[field] || ''}</td>`;
              });
              
              tableHtml += `</tr>`;
            });
          }
        });
      } else {
        // Flattened approach with respect to selected columns and proper ordering
        let availableColumns;
        if (options.columnOrder && options.checkedList && options.parentFieldsMap) {
          availableColumns = getOrderedParentFields(
            options.columnOrder, 
            options.checkedList, 
            options.fixedColumns, 
            options.parentFieldsMap
          );
        } else {
          // Fallback to original logic
          availableColumns = options.parentFields || Object.keys(data[0]).filter(key => key !== 'verifications');
        }
        
        data.forEach((item, index) => {
          const flatItem = { ...item };
          
          // Handle nested arrays like verifications
          if (item.verifications && Array.isArray(item.verifications)) {
            flatItem.verification_types = item.verifications.map(v => v.type || '').join(', ');
            flatItem.verification_statuses = item.verifications.map(v => v.status || '').join(', ');
            delete flatItem.verifications;
          }
          
          tableHtml += `<tr${index % 2 === 0 ? ' style="background-color: #f9f9f9;"' : ''}>`;
          availableColumns.forEach(field => {
            tableHtml += `<td>${flatItem[field] || ''}</td>`;
          });
          tableHtml += `</tr>`;
        });
      }
      
      tableHtml += `</tbody></table>`;
      
      // Set the HTML content
      container.innerHTML = tableHtml;
      
      // Use html2canvas with better settings for CJK
      const canvas = await html2canvas(container, {
        scale: 2, // Higher scale for better quality
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
        allowTaint: true,
        fontFamily: '"Arial Unicode MS", "Microsoft YaHei", "SimHei", sans-serif',
        letterRendering: true,
        foreignObjectRendering: false // This can sometimes help with CJK rendering
      });
      
      // Export the image
      const url = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}.png`;
      link.click();
      
      return url;
    } finally {
      // Clean up
      if (container && container.parentNode) {
        container.parentNode.removeChild(container);
      }
    }
  } catch (error) {
    console.error('PNG export error:', error);
    throw error;
  }
};

/**
 * Export HTML element as PNG image (legacy method)
 * @param {HTMLElement|String} element - DOM element or selector to export
 * @param {String} filename - Filename without extension
 * @param {Object} options - Optional configuration
 * @returns {Promise} - Resolves when export is complete
 */
export const exportElementAsPNG = (element, filename, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      // Get element if string selector is provided
      const targetElement = typeof element === 'string'
        ? document.querySelector(element)
        : element;
        
      if (!targetElement) {
        throw new Error('Target element not found');
      }
      
      // Configure html2canvas options with better CJK support
      const html2canvasOptions = {
        scale: options.scale || 2,
        useCORS: true,
        logging: false,
        backgroundColor: options.backgroundColor || '#ffffff',
        allowTaint: true,
        letterRendering: true,
        ...options.html2canvasOptions
      };
      
      // Render and download
      html2canvas(targetElement, html2canvasOptions).then(canvas => {
        const url = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.href = url;
        link.download = `${filename}.png`;
        link.click();
        resolve();
      }).catch(error => {
        console.error('Error generating canvas:', error);
        reject(error);
      });
    } catch (error) {
      console.error('PNG export error:', error);
      reject(error);
    }
  });
};

/**
 * Export data as file based on format with hierarchical structure support
 * @param {Array} data - Data to export
 * @param {String} format - Export format ('csv', 'pdf', 'png')
 * @param {String} filename - Filename without extension
 * @param {Object} options - Format-specific options
 * @param {HTMLElement|String} element - DOM element for legacy PNG export
 * @returns {Promise} - Resolves when export is complete
 */
export const exportData = async (data, format, filename, options = {}, element = null) => {
  try {
    const defaultFilename = `export-${moment().format('YYYY-MM-DD')}`;
    const exportFilename = filename || defaultFilename;
    
    switch (format?.toLowerCase()) {
      case 'csv':
        return await exportAsCSV(data, exportFilename, options);
      case 'pdf':
        return await exportAsPDF(data, exportFilename, options);
      case 'png':
        // If preserveHierarchy is true or we're using the new table rendering approach
        if (options.preserveHierarchy || options.useTableRendering) {
          return await exportDataAsPNG(data, exportFilename, options);
        } else if (element) {
          // Legacy screenshot approach
          return await exportElementAsPNG(element, exportFilename, options);
        } else {
          throw new Error('Either element or preserveHierarchy/useTableRendering must be specified for PNG export');
        }
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  } catch (error) {
    console.error('Export error:', error);
    throw error;
  }
}; 