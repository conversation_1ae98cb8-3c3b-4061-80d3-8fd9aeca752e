package services

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"context" // Added for sql.NullString etc.
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5" // For pgx.ErrNoRows
	"github.com/rs/zerolog/log"
)

const (
	MaxVerificationFileSize = 5 * 1024 * 1024 // 5 MB
)

var AllowedMimeTypes = map[string]bool{
	"application/pdf": true,
	"image/jpeg":      true,
	"image/png":       true,
}

// --- Helper functions for constructing SQLC Params ---
func stringToPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

func boolToPtr(b bool) *bool {
	return &b
}

// parseDateTime parses a YYYY-MM-DD string to time.Time.
// Returns zero time.Time if input is empty or invalid.
func parseDateTime(dateStr string) (time.Time, error) {
	if dateStr == "" {
		return time.Time{}, nil // Return zero time for empty string, signaling optional/not provided
	}
	t, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return time.Time{}, err
	}
	return t, nil
}

// UserVerificationService handles the business logic for user verification requests.
type UserVerificationService struct {
	Queries *db.Queries
	DB      db.DBTX             // For potential transactions
	Notify  NotificationService // Changed from *NotificationService
}

// NewUserVerificationService creates a new UserVerificationService.
func NewUserVerificationService(q *db.Queries, dbtx db.DBTX, notifyService NotificationService) *UserVerificationService { // Changed from *NotificationService
	return &UserVerificationService{
		Queries: q,
		DB:      dbtx,
		Notify:  notifyService,
	}
}

func (s *UserVerificationService) SubmitVerificationRequest(
	ctx context.Context,
	userID uuid.UUID,
	verificationType db.VerificationTypeEnum,
	fileHeader *multipart.FileHeader,
	fileHeader2 *multipart.FileHeader,
	formData map[string]string,
) (*payloads.UserVerificationRequestResponse, error) {

	var docUUIDForRequest uuid.UUID // For the first document
	var createdDocument *db.VerificationDocument
	var docUUIDForRequest2 uuid.UUID // For the second document
	var createdDocument2 *db.VerificationDocument

	// Process the first document (fileHeader)
	if fileHeader != nil {
		if fileHeader.Size > MaxVerificationFileSize {
			return nil, &payloads.FileProcessingError{Message: "File size exceeds maximum allowed limit for document 1."}
		}
		mimeType := fileHeader.Header.Get("Content-Type")
		if !AllowedMimeTypes[mimeType] {
			return nil, &payloads.FileProcessingError{Message: "File type not allowed for document 1. Supported: PDF, JPEG, PNG."}
		}
		file, err := fileHeader.Open()
		if err != nil {
			return nil, &payloads.FileProcessingError{Message: "Failed to process uploaded file for document 1."}
		}
		defer file.Close()
		fileData, err := io.ReadAll(file)
		if err != nil {
			return nil, &payloads.FileProcessingError{Message: "Failed to read uploaded file data for document 1."}
		}
		createdDocInternal, err := s.Queries.CreateVerificationDocument(ctx, db.CreateVerificationDocumentParams{
			UserID:   userID,
			FileName: fileHeader.Filename,
			MimeType: mimeType,
			FileData: fileData,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to create document 1 record: %w", err)
		}
		docUUIDForRequest = createdDocInternal.ID
		createdDocument = &createdDocInternal
	} else {
		if verificationType != db.VerificationTypeEnumHomeVisit {
			log.Ctx(ctx).Error().Str("verificationType", string(verificationType)).Msg("Service received nil fileHeader for a type that should require a document according to handler logic.")
			return nil, &payloads.IllegalArgumentError{Message: fmt.Sprintf("Document file is mandatory for verification type '%s' but was not received by the service.", verificationType)}
		}
	}

	// Process the second document (fileHeader2) if provided and applicable
	if fileHeader2 != nil {
		log.Ctx(ctx).Info().Msg("Processing fileHeader2") // LOGGING
		switch verificationType {
		case db.VerificationTypeEnumHkIDCard,
			db.VerificationTypeEnumMainlandChinaIDCard,
			db.VerificationTypeEnumMainlandTravelPermit:
			// Proceed to process fileHeader2
			if fileHeader2.Size > MaxVerificationFileSize {
				return nil, &payloads.FileProcessingError{Message: "File size exceeds maximum allowed limit for document 2."}
			}
			mimeType2 := fileHeader2.Header.Get("Content-Type")
			if !AllowedMimeTypes[mimeType2] {
				return nil, &payloads.FileProcessingError{Message: "File type not allowed for document 2. Supported: PDF, JPEG, PNG."}
			}
			file2, err := fileHeader2.Open()
			if err != nil {
				return nil, &payloads.FileProcessingError{Message: "Failed to process uploaded file for document 2."}
			}
			defer file2.Close()
			fileData2, err := io.ReadAll(file2)
			if err != nil {
				return nil, &payloads.FileProcessingError{Message: "Failed to read uploaded file data for document 2."}
			}
			createdDoc2Internal, err := s.Queries.CreateVerificationDocument(ctx, db.CreateVerificationDocumentParams{
				UserID:   userID,
				FileName: fileHeader2.Filename,
				MimeType: mimeType2,
				FileData: fileData2,
			})
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to create document 2 record in DB") // LOGGING
				return nil, fmt.Errorf("failed to create document 2 record: %w", err)
			}
			docUUIDForRequest2 = createdDoc2Internal.ID
			createdDocument2 = &createdDoc2Internal                                                                     // Keep a reference if needed for response
			log.Ctx(ctx).Info().Str("docUUIDForRequest2", docUUIDForRequest2.String()).Msg("Document 2 record created") // LOGGING
		default:
			// fileHeader2 was provided for a type that doesn't support it. Handler should prevent this.
			log.Ctx(ctx).Warn().Str("verificationType", string(verificationType)).Msg("Service received fileHeader2 for a type that does not support a second document.")
			// Optionally, could return an error, but handler should be the primary gate.
			// For now, we'll just ignore it if the type isn't one of the three.
			fileHeader2 = nil // Ensure it's not processed further by mistake
		}
	}

	// Prepare DocumentID pointers for CreateUserVerificationRequestParams
	var docIDParam *uuid.UUID
	if docUUIDForRequest != uuid.Nil {
		docIDParam = &docUUIDForRequest
	}
	var docID2Param *uuid.UUID
	if docUUIDForRequest2 != uuid.Nil {
		docID2Param = &docUUIDForRequest2
	}

	log.Ctx(ctx).Info().
		Bool("fileHeader2_is_nil", fileHeader2 == nil).
		Str("docUUIDForRequest2", docUUIDForRequest2.String()).
		Bool("docID2Param_is_nil", docID2Param == nil).
		Msg("Preparing CreateUserVerificationRequestParams") // LOGGING
	if docID2Param != nil { // LOGGING
		log.Ctx(ctx).Info().Str("docID2Param_value", docID2Param.String()).Msg("docID2Param is not nil") // LOGGING
	} // LOGGING

	requestParams := db.CreateUserVerificationRequestParams{
		UserID:           userID,
		VerificationType: verificationType,
		Status:           db.VerificationStatusEnumPending,
		DocumentID:       docIDParam,
		DocumentID2:      docID2Param, // Pass the pointer for the second document ID
	}

	log.Ctx(ctx).Info().Interface("requestParams_for_CreateUserVerificationRequest", requestParams).Msg("Params before calling CreateUserVerificationRequest") // LOGGING

	createdRequest, err := s.Queries.CreateUserVerificationRequest(ctx, requestParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Interface("failed_requestParams", requestParams).Msg("Failed to create user verification request in DB") // LOGGING
		return nil, err
	}
	log.Ctx(ctx).Info().Interface("createdRequest_from_DB", createdRequest).Msg("User verification request created in DB") // LOGGING
	if createdRequest.DocumentID2 != nil {                                                                                 // LOGGING
		log.Ctx(ctx).Info().Str("createdRequest.DocumentID2", createdRequest.DocumentID2.String()).Msg("createdRequest.DocumentID2 is not nil from DB") // LOGGING
	} else { // LOGGING
		log.Ctx(ctx).Info().Msg("createdRequest.DocumentID2 is nil from DB") // LOGGING
	} // LOGGING

	switch verificationType {
	case db.VerificationTypeEnumHkIDCard:
		isPermanentVal := strings.ToLower(formData["is_hk_permanent_resident"]) == "true"
		_, err = s.Queries.CreateHKIDCardVerification(ctx, db.CreateHKIDCardVerificationParams{
			VerificationRequestID: createdRequest.ID,
			ChineseName:           stringToPtr(formData["chinese_name"]),
			ChineseCommercialCode: stringToPtr(formData["chinese_code"]),
			EnglishName:           stringToPtr(formData["english_name"]),
			Sex:                   stringToPtr(formData["sex"]),
			DateOfBirth:           formData["dob"],
			HkIDNumber:            stringToPtr(formData["id_number"]),
			IsPermanentResident:   boolToPtr(isPermanentVal),
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create hkid card verification details")
			return nil, fmt.Errorf("failed to save verification details: %w", err)
		}
	case db.VerificationTypeEnumMainlandChinaIDCard:
		_, err = s.Queries.CreateMainlandChinaIDCardVerification(ctx, db.CreateMainlandChinaIDCardVerificationParams{
			VerificationRequestID: createdRequest.ID,
			ChineseName:           stringToPtr(formData["chinese_name"]),
			Sex:                   stringToPtr(formData["sex"]),
			DateOfBirth:           formData["date_of_birth"],
			MainlandIDNumber:      stringToPtr(formData["mainland_id_number"]),
			ValidUntil:            formData["valid_until"],
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create mainland china id card verification details")
			return nil, fmt.Errorf("failed to save verification details: %w", err)
		}
	case db.VerificationTypeEnumMainlandTravelPermit:
		_, err = s.Queries.CreateMainlandTravelPermitVerification(ctx, db.CreateMainlandTravelPermitVerificationParams{
			VerificationRequestID: createdRequest.ID,
			PermitNumber:          stringToPtr(formData["mainland_travel_permit_number"]),
			IssueDate:             formData["mainland_travel_permit_issue_date"],
			ExpiryDate:            formData["mainland_travel_permit_expiry_date"],
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create mainland travel permit verification details")
			return nil, fmt.Errorf("failed to save verification details: %w", err)
		}
	case db.VerificationTypeEnumPassport:
		_, err = s.Queries.CreatePassportVerification(ctx, db.CreatePassportVerificationParams{
			VerificationRequestID: createdRequest.ID,
			PassportNumber:        stringToPtr(formData["passport_number"]),
			IssuingCountry:        stringToPtr(formData["issuing_country"]),
			IssueDate:             formData["passport_issue_date"],
			ExpiryDate:            formData["passport_expiry_date"],
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create passport verification details")
			return nil, fmt.Errorf("failed to save verification details: %w", err)
		}
	case db.VerificationTypeEnumHkYouthPlus:
		// The document ID is now directly associated with the HK Youth Plus verification entry,
		// as the 'verification_hk_youth_plus' table requires a non-null 'document_id'.
		_, err = s.Queries.CreateHKYouthPlusVerification(ctx, db.CreateHKYouthPlusVerificationParams{
			VerificationRequestID: createdRequest.ID,
			MemberNumber:          stringToPtr(formData["member_number"]),
			DocumentID:            *docIDParam, // Changed to dereference docIDParam
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create hkyouthplus verification details")
			return nil, fmt.Errorf("failed to save verification details: %w", err)
		}
	case db.VerificationTypeEnumAddressProof:
		_, err = s.Queries.CreateAddressProofVerification(ctx, db.CreateAddressProofVerificationParams{
			VerificationRequestID: createdRequest.ID,
			FullAddress:           stringToPtr(formData["full_address"]),
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create address proof verification details")
			return nil, fmt.Errorf("failed to save verification details: %w", err)
		}
	case db.VerificationTypeEnumStudentID:
		_, err = s.Queries.CreateStudentIDVerification(ctx, db.CreateStudentIDVerificationParams{
			VerificationRequestID: createdRequest.ID,
			SchoolName:            stringToPtr(formData["school_name"]),
			Grade:                 stringToPtr(formData["grade"]),
			ExpiryDate:            formData["expiry_date"],
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create student id verification details")
			return nil, fmt.Errorf("failed to save verification details: %w", err)
		}
	case db.VerificationTypeEnumHomeVisit:
		notes := stringToPtr(formData["home_visit_notes"]) // From SubmitUserVerificationRequest
		_, err = s.Queries.CreateHomeVisitVerification(ctx, db.CreateHomeVisitVerificationParams{
			VerificationRequestID: createdRequest.ID,
			Notes:                 notes,
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create home visit verification details")
			return nil, fmt.Errorf("failed to save home visit verification details: %w", err)
		}
		break
	default:
		return nil, &payloads.IllegalArgumentError{Message: "Unsupported verification type: " + string(verificationType)}
	}

	response := &payloads.UserVerificationRequestResponse{
		ID:               createdRequest.ID.String(),
		UserID:           createdRequest.UserID.String(),
		VerificationType: createdRequest.VerificationType,
		Status:           createdRequest.Status,
		SubmittedAt:      createdRequest.SubmittedAt,
		CreatedAt:        createdRequest.CreatedAt,
		UpdatedAt:        createdRequest.UpdatedAt,
	}

	// Populate details for the first document, if it exists
	if createdDocument != nil { // Or check createdRequest.DocumentID if preferred
		docIDStr := createdDocument.ID.String()
		response.DocumentID = &docIDStr
		response.FileName = &createdDocument.FileName
		response.MimeType = &createdDocument.MimeType
	}

	// Populate details for the second document, if it exists
	// The createdRequest.DocumentID2 is *uuid.UUID from SQLC if the column is nullable.
	if createdRequest.DocumentID2 != nil { // Check if the pointer is not nil
		log.Ctx(ctx).Info().Str("response_population_docID2", createdRequest.DocumentID2.String()).Msg("Populating response for document 2") // LOGGING
		d2IDStr := createdRequest.DocumentID2.String()                                                                                       // Dereference and get string
		response.DocumentID2 = &d2IDStr
		// If createdDocument2 was populated, use its details for FileName2 and MimeType2
		if createdDocument2 != nil && createdDocument2.ID == *createdRequest.DocumentID2 {
			response.FileName2 = &createdDocument2.FileName
			response.MimeType2 = &createdDocument2.MimeType
			log.Ctx(ctx).Info().Str("fileName2", *response.FileName2).Str("mimeType2", *response.MimeType2).Msg("Populated FileName2 and MimeType2 from createdDocument2") // LOGGING
		} else if createdDocument2 != nil {
			// This case should ideally not happen if logic is correct: DocumentID2 is set but createdDocument2 details don't match or are missing.
			log.Ctx(ctx).Warn().Str("documentID2_from_request", createdRequest.DocumentID2.String()).Str("createdDocument2_id", createdDocument2.ID.String()).Msg("Mismatch or issue with createdDocument2 details for response population.")
		}
		// As a fallback, if createdDocument2 is nil but DocumentID2 exists, we at least have the ID.
		// More robustly, one might fetch details for DocumentID2 here if not already available in createdDocument2.
	}

	return response, nil
}

// GetUserVerificationRequests retrieves all verification requests for a specific user.
func (s *UserVerificationService) GetUserVerificationRequests(ctx context.Context, userID uuid.UUID) ([]payloads.UserVerificationRequestResponse, error) {
	requests, err := s.Queries.ListFullUserVerificationRequestsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	responses := make([]payloads.UserVerificationRequestResponse, len(requests))
	for i, req := range requests { // req is db.ListFullUserVerificationRequestsByUserIDRow
		// Assuming the generated row struct `req` now uses *uuid.UUID for nullable fields
		response := payloads.UserVerificationRequestResponse{
			ID:               req.RequestID.String(),
			UserID:           req.UserID.String(),
			VerificationType: req.VerificationType,
			Status:           req.Status,
			SubmittedAt:      req.SubmittedAt,
			CreatedAt:        req.RequestCreatedAt,
			UpdatedAt:        req.RequestUpdatedAt,
			UserDisplayName:  req.UserDisplayName,
			UserEmail:        req.UserEmail,
			ReviewedAt:       req.ReviewedAt,
			AdminNotes:       req.AdminNotes,
			FileName:         req.FileName,
			MimeType:         req.MimeType,
		}
		// Correct check: if pointer is not nil
		if req.DocumentID != nil {
			// If req.DocumentID is *uuid.UUID, dereference it:
			docIDStr := (*req.DocumentID).String()
			response.DocumentID = &docIDStr
		}
		// Populate DocumentID2, FileName2, MimeType2
		if req.DocumentID2 != nil {
			docID2Str := (*req.DocumentID2).String()
			response.DocumentID2 = &docID2Str
			// Assuming sqlc generated FileName2 and MimeType2 in the 'req' struct
			// These might be named req.FileName_2 and req.MimeType_2 or similar by sqlc if it auto-capitalizes.
			// Or, if the SQL alias was file_name_2, it might be req.FileName_2.
			// Let's assume sqlc generates them as req.FileName2 and req.MimeType2 based on the SQL aliases.
			// We should check the generated db/user_verifications.sql.go if this causes a compile error.
			if req.FileName2 != nil { // Check if FileName2 itself is a pointer
				response.FileName2 = req.FileName2
			}
			if req.MimeType2 != nil { // Check if MimeType2 itself is a pointer
				response.MimeType2 = req.MimeType2
			}
		}
		// Correct check: if pointer is not nil
		if req.ReviewedByUserID != nil {
			// If req.ReviewedByUserID is *uuid.UUID, dereference it:
			revIDStr := (*req.ReviewedByUserID).String()
			response.ReviewedByUserID = &revIDStr
		}
		response.ReviewerDisplayName = req.ReviewerDisplayName

		// Populate Specifics data
		specificsData, specificsErr := s.getSpecificVerificationData(ctx, req.RequestID, req.VerificationType)
		if specificsErr != nil {
			log.Ctx(ctx).Error().Err(specificsErr).Str("requestID", req.RequestID.String()).Msg("Failed to get specific verification data during GetUserVerificationRequests list operation")
			response.Specifics = nil // Log and skip specifics for this item
		} else {
			response.Specifics = specificsData
		}

		responses[i] = response
	}
	return responses, nil
}

// GetFullVerificationDetails retrieves a single verification request with all associated details.
func (s *UserVerificationService) GetFullVerificationDetails(ctx context.Context, requestID uuid.UUID) (*payloads.UserVerificationRequestResponse, error) {
	fvd, err := s.Queries.GetFullVerificationDetails(ctx, requestID) // fvd is db.GetFullVerificationDetailsRow
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, &payloads.NotFoundError{Message: "Verification request not found."}
		}
		return nil, err
	}

	// Assuming the generated row struct `fvd` now uses *uuid.UUID for nullable fields
	resp := &payloads.UserVerificationRequestResponse{
		ID:               fvd.RequestID.String(),
		UserID:           fvd.UserID.String(),
		VerificationType: fvd.VerificationType,
		Status:           fvd.Status,
		SubmittedAt:      fvd.SubmittedAt,
		CreatedAt:        fvd.RequestCreatedAt,
		UpdatedAt:        fvd.RequestUpdatedAt,
		ReviewedAt:       fvd.ReviewedAt,
		AdminNotes:       fvd.AdminNotes,
		UserDisplayName:  fvd.UserDisplayName,
		UserEmail:        fvd.UserEmail,
	}

	if fvd.FileName != "" {
		resp.FileName = &fvd.FileName
	}
	if fvd.MimeType != "" {
		resp.MimeType = &fvd.MimeType
	}

	// Correct check: if pointer is not nil
	if fvd.DocumentID != nil {
		docIDStr := (*fvd.DocumentID).String()
		resp.DocumentID = &docIDStr
	}

	// Populate DocumentID2, FileName2, MimeType2
	if fvd.DocumentID2 != nil {
		docID2Str := (*fvd.DocumentID2).String() // fvd.DocumentID2 should exist from the SQL query
		resp.DocumentID2 = &docID2Str
		// Assuming sqlc generates these as fvd.FileName2 and fvd.MimeType2
		// based on SQL aliases file_name_2 and mime_type_2.
		// These fields in fvd are now string.
		if fvd.FileName2 != "" { // Check for non-empty string instead of nil
			resp.FileName2 = &fvd.FileName2 // Assign address of string
		}
		if fvd.MimeType2 != "" { // Check for non-empty string instead of nil
			resp.MimeType2 = &fvd.MimeType2 // Assign address of string
		}
	}

	// Correct check: if pointer is not nil
	if fvd.ReviewedByUserID != nil {
		revIDStr := (*fvd.ReviewedByUserID).String()
		resp.ReviewedByUserID = &revIDStr
	}
	resp.ReviewerDisplayName = fvd.ReviewerDisplayName

	specificsData, specificsErr := s.getSpecificVerificationData(ctx, fvd.RequestID, fvd.VerificationType)
	if specificsErr != nil {
		log.Ctx(ctx).Error().Err(specificsErr).Str("requestID", requestID.String()).Msg("Failed to get specific verification data")
	} else {
		resp.Specifics = specificsData
	}

	return resp, nil
}

// Helper function to get specific verification data based on type
// This avoids repeating the switch statement
func (s *UserVerificationService) getSpecificVerificationData(ctx context.Context, requestID uuid.UUID, vType db.VerificationTypeEnum) (interface{}, error) {
	switch vType {
	case db.VerificationTypeEnumHkIDCard:
		return s.Queries.GetHKIDCardVerificationByRequestID(ctx, requestID)
	case db.VerificationTypeEnumMainlandChinaIDCard:
		return s.Queries.GetMainlandChinaIDCardVerificationByRequestID(ctx, requestID)
	case db.VerificationTypeEnumMainlandTravelPermit:
		return s.Queries.GetMainlandTravelPermitVerificationByRequestID(ctx, requestID)
	case db.VerificationTypeEnumPassport:
		return s.Queries.GetPassportVerificationByRequestID(ctx, requestID)
	case db.VerificationTypeEnumHkYouthPlus:
		return s.Queries.GetHKYouthPlusVerificationByRequestID(ctx, requestID)
	case db.VerificationTypeEnumAddressProof:
		return s.Queries.GetAddressProofVerificationByRequestID(ctx, requestID)
	case db.VerificationTypeEnumStudentID:
		data, err := s.Queries.GetStudentIDVerificationByRequestID(ctx, requestID)
		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return nil, nil // No specific data is not an error here
			}
			return nil, err
		}
		return data, nil
	case db.VerificationTypeEnumHomeVisit:
		data, err := s.Queries.GetHomeVisitVerificationByRequestID(ctx, requestID)
		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return nil, nil // No specific data is not an error here
			}
			return nil, err
		}
		return data, nil
	default:
		log.Ctx(ctx).Warn().Str("verification_type", string(vType)).Msg("Unhandled verification type in getSpecificVerificationData")
		return nil, fmt.Errorf("unsupported verification type: %s", vType)
	}
}

func (s *UserVerificationService) ListPendingVerificationRequests(ctx context.Context) ([]payloads.UserVerificationRequestResponse, error) {
	requests, err := s.Queries.ListFullPendingVerificationRequests(ctx)
	if err != nil {
		return nil, err
	}
	responses := make([]payloads.UserVerificationRequestResponse, len(requests))
	for i, req := range requests { // req is db.ListFullPendingVerificationRequestsRow
		// Assuming the generated row struct `req` now uses *uuid.UUID for nullable fields
		response := payloads.UserVerificationRequestResponse{
			ID:               req.RequestID.String(),
			UserID:           req.UserID.String(),
			VerificationType: req.VerificationType,
			Status:           req.Status,
			SubmittedAt:      req.SubmittedAt,
			CreatedAt:        req.RequestCreatedAt,
			UpdatedAt:        req.RequestUpdatedAt,
			ReviewedAt:       req.ReviewedAt,
			AdminNotes:       req.AdminNotes,
			FileName:         req.FileName,
			MimeType:         req.MimeType,
			UserDisplayName:  req.UserDisplayName,
			UserEmail:        req.UserEmail,
		}
		// Correct check: if pointer is not nil
		if req.DocumentID != nil {
			// If req.DocumentID is *uuid.UUID, dereference it:
			docIDStr := (*req.DocumentID).String()
			response.DocumentID = &docIDStr
		}
		// Correct check: if pointer is not nil
		if req.ReviewedByUserID != nil {
			// If req.ReviewedByUserID is *uuid.UUID, dereference it:
			revIDStr := (*req.ReviewedByUserID).String()
			response.ReviewedByUserID = &revIDStr
		}
		responses[i] = response
	}
	return responses, nil
}

// ReviewVerificationRequest allows an admin to approve or reject a verification request.
func (s *UserVerificationService) ReviewVerificationRequest(ctx context.Context, adminUserID, requestID uuid.UUID, input payloads.ReviewVerificationRequestInput) (*db.UserVerificationRequest, error) {
	logger := log.Ctx(ctx)

	// Enhanced logging for debugging
	logger.Info().Str("requestID_str", requestID.String()).Msg("ReviewVerificationRequest: Attempting to fetch verification request by ID")
	logger.Info().Msgf("ReviewVerificationRequest: RequestID type: %T, value: %v", requestID, requestID)

	// Update the request
	updateParams := db.UpdateUserVerificationRequestStatusParams{
		ID:               requestID,
		Status:           input.Status,
		AdminNotes:       input.AdminNotes,
		ReviewedByUserID: &adminUserID, // Pass pointer
	}

	updatedRequest, err := s.Queries.UpdateUserVerificationRequestStatus(ctx, updateParams)
	if err != nil {
		logger.Error().Err(err).Str("requestID", requestID.String()).Msg("Failed to update verification request status")
		return nil, err
	}

	// Send notification
	if s.Notify != nil {
		var messageType string
		var dataPayload map[string]interface{}

		if updatedRequest.Status == db.VerificationStatusEnumApproved {
			messageType = NotificationTypeVerificationApproved // Use new specific type
			dataPayload = map[string]interface{}{
				"VerificationType": string(updatedRequest.VerificationType),
				"DocumentDetails":  "", // Placeholder, actual details might need to be fetched or constructed
				"AdminNotes":       updatedRequest.AdminNotes,
				"DetailsURL":       fmt.Sprintf("/verifications/me/%s", updatedRequest.ID.String()), // Example URL
				// UserName will be added by notificationService when it fetches the user, or passed if available
			}
		} else {
			// For other statuses like rejected, use the existing generic type or define new ones if needed
			messageType = "verification_status_update" // Existing generic type
			dataPayload = map[string]interface{}{
				"request_id":        updatedRequest.ID.String(),
				"status":            string(updatedRequest.Status),
				"admin_notes":       updatedRequest.AdminNotes,
				"verification_type": string(updatedRequest.VerificationType),
				"message":           fmt.Sprintf("Your verification request (%s) has been updated to %s.", updatedRequest.VerificationType, updatedRequest.Status),
			}
		}

		if err := s.Notify.SendToUser(ctx, updatedRequest.UserID, messageType, dataPayload); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("userID", updatedRequest.UserID.String()).Msg("Failed to send verification status update notification")
		}
	} else {
		log.Ctx(ctx).Warn().Msg("NotificationService (s.Notify) is nil in UserVerificationService. Skipping notification.")
	}

	return &updatedRequest, nil
}

func (s *UserVerificationService) UserDeleteVerificationData(ctx context.Context, requestID uuid.UUID, userID uuid.UUID) error {
	reqDetails, err := s.Queries.GetUserVerificationRequestByID(ctx, requestID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return &payloads.NotFoundError{Message: "Verification request not found."}
		}
		return err
	}

	if reqDetails.UserID != userID {
		return &payloads.ForbiddenError{Message: "User does not own this verification request."}
	}

	// Assuming GetUserVerificationRequestByIDRow embeds VerificationDocument
	// Check if the nested VerificationDocument struct's ID is non-Nil
	if reqDetails.VerificationDocument.ID != uuid.Nil {
		err = s.Queries.DeleteVerificationDocument(ctx, db.DeleteVerificationDocumentParams{
			ID:     reqDetails.VerificationDocument.ID, // Pass uuid.UUID directly
			UserID: userID,
		})
		if err != nil && !errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Error().Err(err).Str("documentID", reqDetails.VerificationDocument.ID.String()).Msg("Failed to delete verification document file data")
			// Decide if this is a critical failure or if we can proceed to mark request as deleted.
			// For now, proceed but log.
		}
	}

	_, err = s.Queries.UserDeleteVerificationData(ctx, db.UserDeleteVerificationDataParams{
		ID:     requestID,
		UserID: userID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return &payloads.NotFoundError{Message: "Verification request not found during final update status."}
		}
		return err
	}
	return nil
}

// GetUserVerificationStatuses retrieves the verification status for a given user.
func (s *UserVerificationService) GetUserVerificationStatuses(ctx context.Context, userID uuid.UUID) (*payloads.UserVerificationStatusResponse, error) {
	dbStatuses, err := s.Queries.GetUserVerificationStatuses(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// If no rows are found, it means no verifications exist, so all statuses are false.
			// This is not an error, but a valid state.
			return &payloads.UserVerificationStatusResponse{
				HKIDCard:             false,
				MainlandChinaIDCard:  false,
				MainlandTravelPermit: false,
				Passport:             false,
				HKYouthPlus:          false,
				AddressProof:         false,
				StudentID:            false,
			}, nil
		}
		log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("Failed to get user verification statuses from DB")
		return nil, fmt.Errorf("failed to get user verification statuses: %w", err)
	}

	return &payloads.UserVerificationStatusResponse{
		HKIDCard:             dbStatuses.HkIDCard,
		MainlandChinaIDCard:  dbStatuses.MainlandChinaIDCard,
		MainlandTravelPermit: dbStatuses.MainlandTravelPermit,
		Passport:             dbStatuses.Passport,
		HKYouthPlus:          dbStatuses.HkYouthPlus,
		AddressProof:         dbStatuses.AddressProof,
		StudentID:            dbStatuses.StudentID,
	}, nil
}

// GetVerificationDocument retrieves the file data for a specific verification document owned by the user.
func (s *UserVerificationService) GetVerificationDocument(ctx context.Context, documentID uuid.UUID, userID uuid.UUID) (fileData []byte, mimeType string, fileName string, err error) {
	doc, err := s.Queries.GetVerificationDocumentByID(ctx, db.GetVerificationDocumentByIDParams{
		ID:     documentID,
		UserID: userID, // Ensure user owns the document
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, "", "", &payloads.NotFoundError{Message: "Document not found or access denied."}
		}
		return nil, "", "", err
	}
	return doc.FileData, doc.MimeType, doc.FileName, nil
}

// AdminGetVerificationDocumentData allows an admin to retrieve file data without the user ownership check.
func (s *UserVerificationService) AdminGetVerificationDocumentData(ctx context.Context, documentID uuid.UUID) (fileData []byte, mimeType string, fileName string, err error) {
	docMeta, err := s.Queries.GetVerificationDocumentMetaByID(ctx, documentID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, "", "", &payloads.NotFoundError{Message: "Document metadata not found."}
		}
		return nil, "", "", err // Other DB error
	}

	actualFileData, err := s.Queries.GetVerificationDocumentDataByID(ctx, documentID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// This case implies metadata exists but data doesn't, which is unusual but possible.
			return nil, "", "", &payloads.NotFoundError{Message: "Document data not found for existing metadata."}
		}
		return nil, "", "", err // Other DB error
	}

	return actualFileData, docMeta.MimeType, docMeta.FileName, nil
}

// AdminListAllVerificationRequests retrieves all requests, potentially with filters.
func (s *UserVerificationService) AdminListAllVerificationRequests(ctx context.Context, statusFilter string, orgIDFilter *uuid.UUID) ([]payloads.UserVerificationRequestResponse, error) {
	var dbStatusFilter db.NullVerificationStatusEnum
	actualStatusFilter := statusFilter
	if statusFilter == "pending" {
		actualStatusFilter = "pending"
	}

	if actualStatusFilter != "" {
		tempStatus := db.VerificationStatusEnum(actualStatusFilter)
		if !tempStatus.Valid() {
			// This error should ideally not be hit if handler validation and this mapping are correct
			log.Ctx(ctx).Error().Str("api_status", statusFilter).Str("db_status", actualStatusFilter).Msg("Status validation failed in service layer despite mapping")
			return nil, fmt.Errorf("invalid status value after mapping: %s (api) -> %s (db)", statusFilter, actualStatusFilter)
		}
		dbStatusFilter.VerificationStatusEnum = tempStatus
		dbStatusFilter.Valid = true
	} else {
		dbStatusFilter.Valid = false
	}

	params := db.AdminListFullVerificationRequestsParams{
		FilterStatus: dbStatusFilter,
		FilterOrgID:  orgIDFilter,
	}

	requests, err := s.Queries.AdminListFullVerificationRequests(ctx, params)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return []payloads.UserVerificationRequestResponse{}, nil
		}
		log.Ctx(ctx).Error().Err(err).Msg("Failed to retrieve admin list of verification requests")
		return nil, fmt.Errorf("failed to retrieve verification requests: %w", err)
	}

	responses := make([]payloads.UserVerificationRequestResponse, len(requests))
	for i, req := range requests { // req is db.AdminListFullVerificationRequestsRow
		response := payloads.UserVerificationRequestResponse{
			ID:               req.RequestID.String(),
			UserID:           req.UserID.String(),
			VerificationType: req.VerificationType,
			Status:           req.Status,
			SubmittedAt:      req.SubmittedAt,
			CreatedAt:        req.RequestCreatedAt,
			UpdatedAt:        req.RequestUpdatedAt,
		}

		// req.UserDisplayName is string, payload.UserDisplayName is *string
		response.UserDisplayName = stringToPtr(req.UserDisplayName)

		// These fields are *string in SQLC row and *string in payload
		response.UserEmail = req.UserEmail
		response.AdminNotes = req.AdminNotes
		response.FileName = req.FileName
		response.MimeType = req.MimeType
		response.ReviewerDisplayName = req.ReviewerDisplayName

		// Handle *uuid.UUID fields from SQLC row to *string in payload
		if req.DocumentID != nil {
			docIDStr := req.DocumentID.String()
			response.DocumentID = &docIDStr
		}
		if req.DocumentID2 != nil {
			docID2Str := req.DocumentID2.String()
			response.DocumentID2 = &docID2Str
		}
		if req.ReviewedByUserID != nil {
			revIDStr := req.ReviewedByUserID.String()
			response.ReviewedByUserID = &revIDStr
		}

		// req.ReviewedAt is *time.Time in SQLC row, payload.ReviewedAt is *time.Time
		response.ReviewedAt = req.ReviewedAt // Direct assignment

		// Populate Specifics data
		specificsData, specificsErr := s.getSpecificVerificationData(ctx, req.RequestID, req.VerificationType)
		if specificsErr != nil {
			log.Ctx(ctx).Error().Err(specificsErr).Str("requestID", req.RequestID.String()).Msg("Failed to get specific verification data during list operation")
			// Decide if we should return an error for the whole list or just log and skip specifics for this item
			// For now, logging and skipping specifics for the item.
			response.Specifics = nil
		} else {
			response.Specifics = specificsData
		}

		responses[i] = response
	}
	return responses, nil
}

// ListUserVerifications provides a filtered list of a user's verification requests.
func (s *UserVerificationService) ListUserVerifications(ctx context.Context, userID uuid.UUID, params *payloads.ListUserVerificationsParams) ([]payloads.UserVerificationRequestResponse, error) {
	// Restore previous conditional logic for calling different list queries
	var dbRequests []db.UserVerificationRequest // Simpler return type from basic list queries
	var err error

	// Prepare filters safely
	var statusFilter *db.VerificationStatusEnum
	if params != nil && params.Status != nil {
		tempStatus := db.VerificationStatusEnum(*params.Status)
		if tempStatus.Valid() {
			statusFilter = &tempStatus
		} else {
			return nil, fmt.Errorf("invalid status filter: %s", *params.Status)
		}
	}
	var verificationTypeFilter *db.VerificationTypeEnum
	if params != nil && params.VerificationType != nil {
		tempType := db.VerificationTypeEnum(*params.VerificationType)
		if tempType.Valid() {
			verificationTypeFilter = &tempType
		} else {
			return nil, fmt.Errorf("invalid verification_type filter: %s", *params.VerificationType)
		}
	}

	if statusFilter != nil && verificationTypeFilter != nil {
		dbRequests, err = s.Queries.ListUserVerificationsByStatusAndType(ctx, db.ListUserVerificationsByStatusAndTypeParams{
			UserID:           userID,
			Status:           *statusFilter,
			VerificationType: *verificationTypeFilter,
		})
	} else if statusFilter != nil {
		dbRequests, err = s.Queries.ListUserVerificationsByStatus(ctx, db.ListUserVerificationsByStatusParams{
			UserID: userID,
			Status: *statusFilter,
		})
	} else if verificationTypeFilter != nil {
		dbRequests, err = s.Queries.ListUserVerificationsByType(ctx, db.ListUserVerificationsByTypeParams{
			UserID:           userID,
			VerificationType: *verificationTypeFilter,
		})
	} else {
		// Use GetUserVerificationRequestsSimple as the fallback (fetches all basic request info for user)
		dbRequests, err = s.Queries.GetUserVerificationRequestsSimple(ctx, userID) // Use the simple query
	}

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return []payloads.UserVerificationRequestResponse{}, nil
		}
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list user verification requests from DB")
		return nil, err
	}

	// Map results to the full response DTO, potentially fetching more details
	var responses []payloads.UserVerificationRequestResponse
	for _, req := range dbRequests {
		// Fetch full details for each request. This can be inefficient (N+1 query problem).
		// Consider optimizing if performance becomes an issue, e.g., by creating a dedicated
		// SQLC query that joins all necessary details for the filtered list.
		fullDetails, detailErr := s.GetFullVerificationDetails(ctx, req.ID)
		if detailErr != nil {
			log.Ctx(ctx).Warn().Err(detailErr).Str("requestID", req.ID.String()).Msg("Failed to get full details for a verification request during list operation")
			// Fallback to basic info if full details fail for one item
			basicResp := payloads.UserVerificationRequestResponse{
				ID:               req.ID.String(),
				UserID:           req.UserID.String(),
				VerificationType: req.VerificationType,
				Status:           req.Status,
				SubmittedAt:      req.SubmittedAt,
				CreatedAt:        req.CreatedAt,
				UpdatedAt:        req.UpdatedAt,
				ReviewedAt:       req.ReviewedAt,
				AdminNotes:       req.AdminNotes,
			}
			if req.DocumentID != nil {
				docIDStr := (*req.DocumentID).String()
				basicResp.DocumentID = &docIDStr
			}
			if req.ReviewedByUserID != nil {
				reviewerIDStr := (*req.ReviewedByUserID).String()
				basicResp.ReviewedByUserID = &reviewerIDStr
			}
			responses = append(responses, basicResp)
			continue
		}
		responses = append(responses, *fullDetails)
	}
	return responses, nil
}

// AdminListUserVerificationsFiltered retrieves a paginated list of user verification requests based on the provided filters.
func (s *UserVerificationService) AdminListUserVerificationsFiltered(
	ctx context.Context,
	filters payloads.AdminListVerificationFilters,
) (*payloads.PaginatedUserVerificationRequestsResponse, error) {
	var err error

	// Prepare params for the count query
	countParams := db.CountAdminListFullVerificationRequestsWithFiltersParams{
		FilterUserID:  filters.UserID,
		FilterEventID: filters.EventID,
		FilterOrgID:   filters.OrgID,
	}

	if filters.VerificationType != "" {
		// Validate filters.VerificationType against db.AllVerificationTypeEnum before casting
		validType := false
		for _, enumVal := range db.AllVerificationTypeEnumValues() {
			if string(enumVal) == filters.VerificationType {
				validType = true
				break
			}
		}
		if validType {
			countParams.FilterVerificationType = db.NullVerificationTypeEnum{VerificationTypeEnum: db.VerificationTypeEnum(filters.VerificationType), Valid: true}
		} else {
			// Handle invalid type string, perhaps return error or ignore filter
			log.Ctx(ctx).Warn().Str("type", filters.VerificationType).Msg("Invalid verification type filter value")
			countParams.FilterVerificationType = db.NullVerificationTypeEnum{Valid: false}
		}
	} else {
		countParams.FilterVerificationType = db.NullVerificationTypeEnum{Valid: false}
	}

	if filters.Status != "" {
		// Validate filters.Status against db.AllVerificationStatusEnum before casting
		validStatus := false
		for _, enumVal := range db.AllVerificationStatusEnumValues() {
			if string(enumVal) == filters.Status {
				validStatus = true
				break
			}
		}
		if validStatus {
			countParams.FilterStatus = db.NullVerificationStatusEnum{VerificationStatusEnum: db.VerificationStatusEnum(filters.Status), Valid: true}
		} else {
			log.Ctx(ctx).Warn().Str("status", filters.Status).Msg("Invalid verification status filter value")
			countParams.FilterStatus = db.NullVerificationStatusEnum{Valid: false}
		}
	} else {
		countParams.FilterStatus = db.NullVerificationStatusEnum{Valid: false}
	}

	totalItems, err := s.Queries.CountAdminListFullVerificationRequestsWithFilters(ctx, countParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to count filtered user verification requests")
		return nil, fmt.Errorf("failed to count verification requests: %w", err)
	}

	// Prepare params for the main data query
	offset := (filters.Page - 1) * filters.Limit
	listParams := db.AdminListFullVerificationRequestsWithFiltersParams{
		FilterUserID:  filters.UserID,
		FilterEventID: filters.EventID,
		FilterOrgID:   filters.OrgID,
		Lim:           int32(filters.Limit),
		Offs:          int32(offset),
	}
	// Apply validated filters to listParams as well
	if filters.VerificationType != "" && countParams.FilterVerificationType.Valid {
		listParams.FilterVerificationType = countParams.FilterVerificationType
	} else {
		listParams.FilterVerificationType = db.NullVerificationTypeEnum{Valid: false}
	}

	if filters.Status != "" && countParams.FilterStatus.Valid {
		listParams.FilterStatus = countParams.FilterStatus
	} else {
		listParams.FilterStatus = db.NullVerificationStatusEnum{Valid: false}
	}

	dbRequests, err := s.Queries.AdminListFullVerificationRequestsWithFilters(ctx, listParams)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return &payloads.PaginatedUserVerificationRequestsResponse{
				Data:       []payloads.UserVerificationRequestAdminListResponse{},
				Pagination: payloads.Pagination{TotalItems: 0, TotalPages: 0, CurrentPage: filters.Page, Limit: filters.Limit},
			}, nil
		}
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list filtered user verification requests")
		return nil, fmt.Errorf("failed to list verification requests: %w", err)
	}

	responseItems := make([]payloads.UserVerificationRequestAdminListResponse, 0, len(dbRequests))
	for _, req := range dbRequests { // req is db.AdminListFullVerificationRequestsWithFiltersRow
		item := payloads.UserVerificationRequestAdminListResponse{
			RequestID:        req.RequestID,
			UserID:           req.UserID,
			UserDisplayName:  req.UserDisplayName,
			VerificationType: string(req.VerificationType),
			Status:           string(req.Status),
			SubmittedAt:      req.SubmittedAt,
			// DocumentID and DocumentID2 are *uuid.UUID in req, but uuid.UUID in item
			// Initialize with uuid.Nil and update if pointer is not nil
			DocumentID:  uuid.Nil,
			DocumentID2: uuid.Nil,
		}
		if req.DocumentID != nil {
			item.DocumentID = *req.DocumentID
		}
		if req.DocumentID2 != nil {
			item.DocumentID2 = *req.DocumentID2
		}
		if req.UserEmail != nil {
			item.UserEmail = *req.UserEmail
		}
		if req.ReviewedAt != nil {
			item.ReviewedAt = *req.ReviewedAt
		}
		if req.ReviewedByUserID != nil {
			item.ReviewedByUserID = *req.ReviewedByUserID
		}
		if req.ReviewerDisplayName != nil {
			item.ReviewerDisplayName = *req.ReviewerDisplayName
		}
		if req.AdminNotes != nil {
			item.AdminNotes = *req.AdminNotes
		}
		if req.FileName != nil {
			item.FileName = *req.FileName
		}

		responseItems = append(responseItems, item)
	}

	totalPages := 0
	if totalItems > 0 {
		totalPages = (int(totalItems) + filters.Limit - 1) / filters.Limit
	}

	return &payloads.PaginatedUserVerificationRequestsResponse{
		Data: responseItems,
		Pagination: payloads.Pagination{
			TotalItems:  int(totalItems),
			TotalPages:  totalPages,
			CurrentPage: filters.Page,
			Limit:       filters.Limit,
		},
	}, nil
}

func (s *UserVerificationService) GetAllVerificationTypes() []db.VerificationTypeEnum {
	return db.AllVerificationTypeEnumValues()
}

func (s *UserVerificationService) GetAllVerificationStatuses() []db.VerificationStatusEnum {
	return db.AllVerificationStatusEnumValues()
}
