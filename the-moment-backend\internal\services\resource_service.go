package services

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/utils"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gosimple/slug"
	"github.com/rs/zerolog/log"
)

const (
	DefaultResourceStatus         = "draft"
	DefaultResourceVisibility     = "org_only"
	UploadDirResources            = "uploads/organization-resources"
	UploadDirOrganizationFiles    = "uploads/organization-files"
	MaxUploadSizeResourceFile     = 20 << 20 // 20 MB
	MaxUploadSizeOrganizationFile = 20 << 20 // 20 MB
)

type ResourceService struct {
	Querier db.Querier
	// Config can be added here (e.g. base URL for file links)
}

func NewResourceService(querier db.Querier) *ResourceService {
	return &ResourceService{Querier: querier}
}

// CreateResource creates a new resource for an organization.
func (s *ResourceService) CreateResource(ctx context.Context, req payloads.CreateResourceRequest) (db.Resource, error) {
	slugStr := req.Slug
	if slugStr == "" {
		slugStr = slug.Make(req.Title)
	} else {
		slugStr = slug.Make(req.Slug) // Ensure clean slug
	}

	// Check if slug is unique for this organization
	_, err := s.Querier.GetResourceByOrgAndSlug(ctx, db.GetResourceByOrgAndSlugParams{OrganizationID: req.OrganizationID, Slug: slugStr})

	if err == nil {
		// Slug already exists, generate a unique one
		slugStr = fmt.Sprintf("%s-%s", slugStr, uuid.New().String()[:8])
	} else if !errors.Is(err, sql.ErrNoRows) { // Use errors.Is for robust check
		// An actual error occurred during the query (not sql.ErrNoRows)
		return db.Resource{}, fmt.Errorf("failed database check for resource slug uniqueness: %w", err)
	}
	// If err IS sql.ErrNoRows, the slug is unique, proceed.

	status := DefaultResourceStatus // Default to 'draft'
	if req.Status != "" {
		status = req.Status
	}

	visibility := DefaultResourceVisibility
	if req.Visibility != "" {
		visibility = req.Visibility
	}

	var publishedAtParam *time.Time // This will be passed to db.CreateResourceParams

	if req.PublishedAt != nil { // published_at is provided in the request
		// Basic validation: if status is 'published', published_at shouldn't be in the future.
		// If status is 'draft', published_at should ideally be in the future.
		// For now, we'll trust the input or add more complex validation later if needed.
		// Example: if status == "published" && req.PublishedAt.After(time.Now()) { /* set to now or error */ }

		if status == "published" {
			// If status is 'published' and published_at is provided, use the provided time.
			// It acts as the "official" publication time.
			publishedAtParam = req.PublishedAt
		} else { // status is 'draft' (or default 'draft')
			// Resource is being scheduled. Store the provided published_at.
			publishedAtParam = req.PublishedAt
			// Ensure status is 'draft' if a future published_at is set
			status = "draft"
		}
	} else { // published_at is NOT provided in the request
		if status == "published" {
			// Publish immediately, set published_at to now.
			now := time.Now()
			publishedAtParam = &now
		}
		// If status is 'draft' and published_at is not provided, publishedAtParam remains nil (no schedule).
	}

	var descriptionParam *string
	if req.Description != "" {
		desc := req.Description
		descriptionParam = &desc
	}

	params := db.CreateResourceParams{
		OrganizationID: req.OrganizationID,
		Title:          req.Title,
		Slug:           slugStr,
		Description:    descriptionParam,
		Visibility:     visibility,
		Status:         status,           // Final status based on logic above
		PublishedAt:    publishedAtParam, // Final published_at based on logic above
	}

	createdResource, err := s.Querier.CreateResource(ctx, params)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error from s.Querier.CreateResource")
		return db.Resource{}, fmt.Errorf("failed to create resource in database: %w", err)
	}
	log.Ctx(ctx).Info().Str("createdResourceID", createdResource.ID.String()).Msg("Resource created in DB by s.Querier.CreateResource")

	// Attach uploaded files if any
	// For now, we assume no files are directly uploaded with create, they are attached separately.
	return createdResource, nil
}

// GetResourceByID retrieves a single resource by ID, including its files.
func (s *ResourceService) GetResourceByID(ctx context.Context, resourceID uuid.UUID) (*payloads.ResourceResponse, error) {
	log.Ctx(ctx).Info().Str("resourceID", resourceID.String()).Msg("GetResourceByID called with ID")
	rows, err := s.Querier.GetResourceWithFiles(ctx, resourceID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, utils.ErrNotFound
		}
		return nil, fmt.Errorf("failed to get resource with files by ID: %w", err)
	}
	if len(rows) == 0 { // Should be caught by sql.ErrNoRows, but safeguard
		return nil, utils.ErrNotFound
	}
	// Before converting, modify file paths to be absolute URLs
	for i := range rows {
		if rows[i].ResourceFileFilePath != nil {
			*rows[i].ResourceFileFilePath = utils.ConstructURL(*rows[i].ResourceFileFilePath)
		}
	}

	return payloads.ToResourceResponseFromGetResourceWithFilesRow(rows), nil
}

// GetResourceByOrgAndSlug retrieves a resource by organization ID and slug, including files.
// This is typically for public/member view, so might enforce 'published' status.
func (s *ResourceService) GetResourceByOrgAndSlug(ctx context.Context, orgID uuid.UUID, slugStr string, isPublicAccess bool) (*payloads.ResourceResponse, error) {
	rows, err := s.Querier.GetResourceByOrgAndSlugWithFiles(ctx, db.GetResourceByOrgAndSlugWithFilesParams{OrganizationID: orgID, Slug: slugStr})
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, utils.ErrNotFound
		}
		return nil, fmt.Errorf("failed to get resource by org and slug: %w", err)
	}
	if len(rows) == 0 {
		return nil, utils.ErrNotFound
	}

	resourceData := rows[0].Resource
	if isPublicAccess {
		if resourceData.Status != "published" {
			return nil, utils.ErrNotFound // Only published for public slug access
		}
		if resourceData.Visibility == "org_only" {
			// For a true public access call, org_only would also be not found or forbidden
			// Depending on if the caller is known to be outside the org or not.
			// For simplicity here, treating it as not found for a purely public (non-org-member) context.
			return nil, utils.ErrNotFound
		}
	}

	return payloads.ToResourceResponseFromGetResourceByOrgAndSlugWithFilesRow(rows), nil
}

// ListResourcesByOrganization retrieves resources for a specific organization (admin view).
func (s *ResourceService) ListResourcesByOrganization(ctx context.Context, orgID uuid.UUID, params payloads.ListResourcesParams) ([]payloads.ResourceResponse, int64, error) {
	// Prepare parameters for counting resources
	countParams := db.CountResourcesByOrganizationParams{
		OrganizationID: orgID,
		Status:         params.Status,
		SearchTerm:     params.SearchTerm,
	}
	totalCount, err := s.Querier.CountResourcesByOrganization(ctx, countParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to count resources by organization")
		// Depending on requirements, you might want to return an error here
		// For now, we log and proceed, potentially returning 0 or an inaccurate count.
		return nil, 0, fmt.Errorf("failed to count resources by organization: %w", err) // Or handle error differently
	}

	if totalCount == 0 {
		return []payloads.ResourceResponse{}, 0, nil
	}

	// Prepare parameters for listing resources
	dbListParams := db.ListResourcesByOrganizationParams{
		OrganizationID: orgID,
		LimitVal:       int32(params.Limit),
		OffsetVal:      int32(params.Offset),
		Status:         params.Status,
		SearchTerm:     params.SearchTerm,
	}
	dbResources, err := s.Querier.ListResourcesByOrganization(ctx, dbListParams)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list resources by organization: %w", err)
	}

	// TODO: Fetch files for these resources if needed by ToResourceListResponse for this context.
	// Currently, ToResourceListResponse can take nil for files, meaning files won't be populated.
	// If admin list view should show files, this part needs enhancement (e.g., N+1 queries or a join in ListResourcesByOrganization SQL query).
	// For now, assuming files are not critical for this specific list view or handled elsewhere.
	return payloads.ToResourceListResponse(dbResources, nil, nil), totalCount, nil
}

// ListPublishedResources retrieves published resources, potentially filtered by visibility.
func (s *ResourceService) ListPublishedResources(ctx context.Context, params payloads.ListResourcesParams) ([]payloads.ResourceResponse, int64, error) {
	// Prepare parameters for ListPublishedResourcesWithFiles
	dbParams := db.ListPublishedResourcesWithFilesParams{
		LimitVal:              int32(params.Limit),
		OffsetVal:             int32(params.Offset),
		VisibilityFilter:      nil,
		OrganizationIDFilter:  params.OrganizationID,
		OrganizationId2Filter: params.OrganizationID2,
		SearchTerm:            params.SearchTerm,
	}
	if params.Visibility != nil {
		// Validate visibility string before using it
		if *params.Visibility == "public" || *params.Visibility == "org_only" { // Check against valid string values
			dbParams.VisibilityFilter = params.Visibility // Assign the string pointer directly
		} else {
			log.Ctx(ctx).Warn().Str("visibility", *params.Visibility).Msg("Invalid visibility filter provided to ListPublishedResources, ignoring.")
		}
	}

	// Call the new query that includes files
	dbResourceRows, err := s.Querier.ListPublishedResourcesWithFiles(ctx, dbParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list published resources with files")
		return nil, 0, fmt.Errorf("failed to list published resources: %w", err)
	}

	// Use the new payload helper to group files
	resourceResponses := payloads.ToResourceListResponseFromListPublishedResourcesWithFilesRows(dbResourceRows)

	// Prepare parameters for CountPublishedResources
	countParams := db.CountPublishedResourcesParams{
		VisibilityFilter:      dbParams.VisibilityFilter,
		OrganizationIDFilter:  dbParams.OrganizationIDFilter,
		OrganizationId2Filter: params.OrganizationID2,
		SearchTerm:            params.SearchTerm,
	}

	// Get total count matching the filters
	totalCount, err := s.Querier.CountPublishedResources(ctx, countParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to count published resources")
		// Return data even if count fails, but with totalCount 0
		return resourceResponses, 0, nil
	}

	return resourceResponses, totalCount, nil
}

// ListPublishedResourcesByOrganization retrieves published resources for a specific organization with pagination.
func (s *ResourceService) ListPublishedResourcesByOrganization(ctx context.Context, orgID uuid.UUID, params payloads.ListResourcesParams) ([]payloads.ResourceResponse, int64, error) {
	// Get total count first
	totalCount, err := s.Querier.CountPublishedResourcesByOrganization(ctx, orgID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count published resources by organization: %w", err)
	}

	if totalCount == 0 {
		return []payloads.ResourceResponse{}, 0, nil
	}

	dbParams := db.ListPublishedResourcesByOrganizationWithFilesParams{
		OrganizationID: orgID,
		Limit:          int32(params.Limit),
		Offset:         int32(params.Offset),
	}

	dbResourceRows, err := s.Querier.ListPublishedResourcesByOrganizationWithFiles(ctx, dbParams)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list published resources by organization with files: %w", err)
	}

	// Process rows into ResourceResponse with grouped files
	// Re-use the payload helper structure similar to posts
	resourceResponses := payloads.ToResourceListResponseFromListOrgResourcesWithFilesRows(dbResourceRows)

	return resourceResponses, totalCount, nil
}

// UpdateResource updates an existing resource.
func (s *ResourceService) UpdateResource(ctx context.Context, resourceID uuid.UUID, req payloads.UpdateResourceRequest) (db.Resource, error) {
	originalResource, err := s.Querier.GetResourceByID(ctx, resourceID) // Check existence and get current state
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) { // Use errors.Is for robust check
			return db.Resource{}, utils.ErrNotFound
		}
		return db.Resource{}, fmt.Errorf("failed to get resource for update: %w", err)
	}

	params := db.UpdateResourceParams{ID: resourceID}

	// Handle standard field updates, defaulting to original values if not provided in request
	if req.Title != nil {
		params.Title = req.Title
	} else {
		params.Title = &originalResource.Title // Keep original if not provided
	}

	if req.Slug != nil {
		slugStr := slug.Make(*req.Slug)
		params.Slug = &slugStr
	} else {
		params.Slug = &originalResource.Slug // Keep original if not provided
	}

	if req.Description != nil {
		params.Description = req.Description
	} else {
		params.Description = originalResource.Description // Keep original if not provided (already a pointer)
	}

	if req.Visibility != nil {
		params.Visibility = req.Visibility
	} else {
		originalVis := originalResource.Visibility // originalResource.Visibility is string, params.Visibility is *string
		params.Visibility = &originalVis
	}

	// --- Status and PublishedAt logic ---
	currentStatus := originalResource.Status
	newStatus := currentStatus
	if req.Status != nil {
		newStatus = *req.Status
	}
	params.Status = &newStatus

	// Determine the final publishedAt value
	// Default to original published_at, then apply request changes if any.
	finalPublishedAt := originalResource.PublishedAt // This is *time.Time or nil
	userInteractedWithPublishedAt := false           // New flag

	if req.PublishedAt != nil { // published_at is explicitly provided in the update request
		userInteractedWithPublishedAt = true // User interacted
		// If *req.PublishedAt is the zero value of time.Time, it means client wants to clear it.
		if (*req.PublishedAt).IsZero() {
			finalPublishedAt = nil // Clear scheduled publish time
		} else {
			finalPublishedAt = req.PublishedAt // Set to the new requested time
		}
	}

	if newStatus == "published" {
		// If status is changing to or remaining 'published'
		if finalPublishedAt == nil { // If no specific published_at is set (either originally or by request)
			// and we are publishing, set it to now.
			now := time.Now()
			finalPublishedAt = &now
		} else {
			// If a specific published_at is provided (e.g. client wants to set a specific historical publish time)
			// or if it was already published with a time, keep that time.
			// No change to finalPublishedAt needed here if it's already set and status is published.
		}
		// Any future schedule is overridden if status becomes 'published'.
		// The job itself won't pick up 'published' items.
	} else if newStatus == "draft" {
		// If status is changing from 'published' to 'draft' and user didn't specify a new published_at,
		// clear the published_at time.
		if currentStatus == "published" && !userInteractedWithPublishedAt {
			finalPublishedAt = nil
		}
		// If status is changing to or remaining 'draft'
		// finalPublishedAt determined above (from req.PublishedAt or cleared) will be used.
		// If finalPublishedAt is nil, it's a draft without a schedule.
		// If finalPublishedAt is a future time, it's a scheduled draft.
		// If finalPublishedAt is a past time for a draft, the scheduler will pick it up immediately.
	} else {
		// Handle other potential statuses if they exist, or this implies an invalid status.
		// For now, assume only "draft" and "published".
		log.Ctx(ctx).Warn().Str("status", newStatus).Msg("Encountered unexpected status in UpdateResource logic")
	}

	params.PublishedAt = finalPublishedAt // Set the determined PublishedAt for the DB update

	updatedResource, err := s.Querier.UpdateResource(ctx, params)
	if err != nil {
		return db.Resource{}, fmt.Errorf("failed to update resource: %w", err)
	}
	return updatedResource, nil
}

// DeleteResource deletes a resource and its associated files.
func (s *ResourceService) DeleteResource(ctx context.Context, resourceID uuid.UUID) error {
	resource, err := s.Querier.GetResourceByID(ctx, resourceID)
	if err != nil {
		if err == sql.ErrNoRows {
			return utils.ErrNotFound
		}
		return fmt.Errorf("failed to get resource before deletion: %w", err)
	}

	files, err := s.Querier.ListFilesByResource(ctx, resourceID)
	if err != nil && err != sql.ErrNoRows {
		return fmt.Errorf("failed to list files for resource deletion: %w", err)
	}

	for _, file := range files {
		if err := os.Remove(file.FilePath); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("file_path", file.FilePath).Msg("Failed to delete resource file from storage")
		}
	}

	// Resource files are deleted by CASCADE. If not, s.Querier.DeleteResourceFilesByResourceID(ctx, resourceID)
	err = s.Querier.DeleteResource(ctx, resourceID)
	if err != nil {
		if err == sql.ErrNoRows { // Or equivalent error for delete
			return utils.ErrNotFound
		}
		return fmt.Errorf("failed to delete resource from database: %w", err)
	}

	// Delete the resource-specific folder as well
	orgIDStr := resource.OrganizationID.String()
	resourceIDStr := resource.ID.String()
	resourceFolderPath := filepath.Join(UploadDirResources, orgIDStr, resourceIDStr)
	if err := os.RemoveAll(resourceFolderPath); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("folder_path", resourceFolderPath).Msg("Failed to delete resource directory")
		// Non-fatal, resource is deleted from DB anyway
	}

	return nil
}

// UploadResourceFile handles uploading a file associated with a resource.
// It saves the file to a structured path and records its metadata in the database.
func (s *ResourceService) UploadResourceFile(ctx context.Context, resourceID uuid.UUID, fileHeader *multipart.FileHeader, description string) (db.ResourceFile, error) {
	log.Ctx(ctx).Info().Str("resourceID", resourceID.String()).Str("fileName", fileHeader.Filename).Msg("UploadResourceFile called")

	// Basic validation
	if fileHeader.Size > MaxUploadSizeResourceFile {
		return db.ResourceFile{}, fmt.Errorf("file size %d exceeds maximum allowed size %d bytes", fileHeader.Size, MaxUploadSizeResourceFile)
	}

	// Use the original filename as per plan, sanitization can be added if specifically required later.
	fileName := fileHeader.Filename
	fileExt := strings.ToLower(filepath.Ext(fileName))

	// Determine file type (simple version based on extension)
	fileType := strings.TrimPrefix(fileExt, ".")
	if fileType == "" {
		// Attempt to get Content-Type from header as a fallback or primary method
		contentType := fileHeader.Header.Get("Content-Type")
		if parsedType, _, err := mime.ParseMediaType(contentType); err == nil && strings.Contains(parsedType, "/") {
			fileType = parsedType // e.g., "image/jpeg"
		} else {
			fileType = "unknown"
		}
	}

	// Create resource-specific directory if it doesn't exist
	resource, err := s.Querier.GetResourceByID(ctx, resourceID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("resourceID", resourceID.String()).Msg("Failed to get resource by ID to determine organization ID")
		return db.ResourceFile{}, fmt.Errorf("failed to retrieve resource details: %w", err)
	}
	orgID := resource.OrganizationID.String()

	resourceFileDir := filepath.Join(UploadDirResources, orgID, resourceID.String())
	if err := os.MkdirAll(resourceFileDir, os.ModePerm); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("path", resourceFileDir).Msg("Failed to create resource file directory")
		return db.ResourceFile{}, fmt.Errorf("failed to create resource file directory %s: %w", resourceFileDir, err)
	}

	uniqueFileName := fmt.Sprintf("%s%s", uuid.New().String(), fileExt)
	filePath := filepath.Join(resourceFileDir, uniqueFileName)

	src, err := fileHeader.Open()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to open uploaded file")
		return db.ResourceFile{}, fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer src.Close()

	dst, err := os.Create(filePath)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("filePath", filePath).Msg("Failed to create destination file")
		return db.ResourceFile{}, fmt.Errorf("failed to create destination file %s: %w", filePath, err)
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to copy uploaded file to destination")
		return db.ResourceFile{}, fmt.Errorf("failed to save uploaded file: %w", err)
	}

	var descriptionParam *string // Changed to *string to match sqlc generated type
	if description != "" {
		descriptionParam = &description // Assign address of description
	}

	params := db.CreateResourceFileParams{
		ResourceID:  resourceID,
		FileName:    fileName, // Original filename
		FilePath:    filePath, // Path on server
		FileType:    fileType, // Determined file type
		FileSize:    fileHeader.Size,
		Description: descriptionParam, // Pass the new description (*string)
	}

	createdFile, err := s.Querier.CreateResourceFile(ctx, params)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error from s.Querier.CreateResourceFile")
		if removeErr := os.Remove(filePath); removeErr != nil {
			log.Ctx(ctx).Error().Err(removeErr).Str("filePath", filePath).Msg("Failed to remove file after DB error")
		}
		return db.ResourceFile{}, fmt.Errorf("failed to create resource file record in database: %w", err)
	}

	log.Ctx(ctx).Info().Str("fileID", createdFile.ID.String()).Str("filePath", filePath).Msg("Resource file uploaded and DB record created")
	return createdFile, nil
}

// DeleteResourceFile deletes a file associated with a resource.
func (s *ResourceService) DeleteResourceFile(ctx context.Context, fileID uuid.UUID) error {
	file, err := s.Querier.GetResourceFileByID(ctx, fileID)
	if err != nil {
		if err == sql.ErrNoRows {
			return utils.ErrNotFound
		}
		return fmt.Errorf("failed to get resource file for deletion: %w", err)
	}

	if err := os.Remove(file.FilePath); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("file_path", file.FilePath).Msg("Failed to delete resource file from storage")
		// Decide if fatal, for now proceed with DB deletion.
	}

	if err := s.Querier.DeleteResourceFile(ctx, fileID); err != nil {
		return fmt.Errorf("failed to delete resource file from database: %w", err)
	}
	return nil
}

// --- Organization File Management (Directly in org folders) ---

// CreateOrganizationFolder creates a new folder within an organization's file storage.
func (s *ResourceService) CreateOrganizationFolder(ctx context.Context, orgID uuid.UUID, req payloads.CreateOrganizationFolderRequest) (db.OrganizationFile, error) {
	safeFolderName := slug.Make(req.FolderName)
	if safeFolderName == "" {
		return db.OrganizationFile{}, fmt.Errorf("folder name cannot be empty after sanitization")
	}

	var parentFolderIDParam *uuid.UUID
	if req.ParentFolderID != uuid.Nil {
		parentFolder, err := s.Querier.GetOrganizationFileByID(ctx, req.ParentFolderID)
		if err != nil || !parentFolder.IsFolder || parentFolder.OrganizationID != orgID {
			return db.OrganizationFile{}, fmt.Errorf("parent folder with ID %s not found or invalid: %w", req.ParentFolderID, utils.ErrBadRequest)
		}
		parentFolderIDParam = &req.ParentFolderID
	}

	// Check for name collision in the target parent folder
	var existingItemsInParent []db.ListOrganizationFilesInRootRow // Use RootRow as common structure
	var listErr error
	if parentFolderIDParam == nil {
		existingItemsInParent, listErr = s.Querier.ListOrganizationFilesInRoot(ctx, db.ListOrganizationFilesInRootParams{
			OrganizationID: orgID,
			Limit:          1000, // Check many files for collision
			Offset:         0,
		})
	} else {
		folderItems, listErrIntermediate := s.Querier.ListOrganizationFilesInFolder(ctx, db.ListOrganizationFilesInFolderParams{
			OrganizationID: orgID,
			ParentFolderID: parentFolderIDParam,
			Limit:          1000, // Check many files for collision
			Offset:         0,
		})
		if listErrIntermediate == nil {
			existingItemsInParent = make([]db.ListOrganizationFilesInRootRow, len(folderItems))
			for i, row := range folderItems {
				existingItemsInParent[i] = db.ListOrganizationFilesInRootRow(row) // Convert FolderRow to RootRow
			}
		} else {
			listErr = listErrIntermediate
		}
	}

	if listErr != nil && listErr != sql.ErrNoRows {
		log.Ctx(ctx).Error().Err(listErr).Msg("Failed to list existing files for folder name collision check")
		// Potentially return an error here instead of just logging
		// return db.OrganizationFile{}, fmt.Errorf("failed to check for folder name collision: %w", listErr)
	} else if listErr == nil {
		for _, f := range existingItemsInParent {
			if f.FileName == safeFolderName && f.IsFolder {
				return db.OrganizationFile{}, fmt.Errorf("a folder with name '%s' already exists in this location: %w", safeFolderName, utils.ErrConflict)
			}
		}
	}

	fullLogicalPath, err := s.buildLogicalPath(ctx, orgID, parentFolderIDParam, safeFolderName)
	if err != nil {
		return db.OrganizationFile{}, fmt.Errorf("failed to build logical path for folder: %w", err)
	}

	params := db.CreateOrganizationFileParams{
		OrganizationID: orgID,
		FileName:       safeFolderName,
		FilePath:       fullLogicalPath,
		IsFolder:       true,
		ParentFolderID: parentFolderIDParam,
	}

	folder, err := s.Querier.CreateOrganizationFile(ctx, params)
	if err != nil {
		return db.OrganizationFile{}, fmt.Errorf("failed to create folder in database: %w", err)
	}
	return folder, nil
}

// buildLogicalPath constructs the logical path for a file/folder within an organization's storage.
// For folders: "folder1/subfolder2/current_folder_name"
// For files (conceptual, not stored as file's FilePath): "folder1/subfolder2/original_file_name.ext"
func (s *ResourceService) buildLogicalPath(ctx context.Context, orgID uuid.UUID, parentFolderID *uuid.UUID, itemName string) (string, error) {
	if parentFolderID == nil { // Item is in the root of the org's directory
		return itemName, nil
	}

	var pathParts []string
	currentParentID := *parentFolderID

	// Max depth to prevent infinite loops if there's a cycle (though schema should prevent this)
	maxDepth := 20
	depth := 0

	for depth < maxDepth {
		parentFolder, err := s.Querier.GetOrganizationFileByID(ctx, currentParentID)
		if err != nil {
			// If parent not found, it's an orphaned path or error.
			log.Ctx(ctx).Error().Err(err).Stringer("parentFolderID", &currentParentID).Str("itemName", itemName).Msg("Failed to get parent folder during path construction")
			return "", fmt.Errorf("failed to get parent folder %s during path construction: %w", currentParentID, err)
		}
		if !parentFolder.IsFolder {
			log.Ctx(ctx).Error().Stringer("ancestorID", &currentParentID).Str("itemName", itemName).Msg("Ancestor is not a folder during path construction")
			return "", fmt.Errorf("ancestor ID %s is not a folder during path construction for %s", currentParentID, itemName)
		}
		if parentFolder.OrganizationID != orgID {
			log.Ctx(ctx).Error().Stringer("parentFolderID", &currentParentID).Stringer("orgID", &orgID).Msg("Parent folder belongs to a different organization during path construction")
			return "", fmt.Errorf("parent folder %s belongs to a different organization", currentParentID)
		}

		pathParts = append([]string{parentFolder.FileName}, pathParts...)

		if parentFolder.ParentFolderID == nil {
			break
		}
		currentParentID = *parentFolder.ParentFolderID
		depth++
	}
	if depth >= maxDepth {
		return "", fmt.Errorf("exceeded max depth constructing path for %s, potential circular dependency", itemName)
	}

	return filepath.Join(append(pathParts, itemName)...), nil
}

// UploadOrganizationFile uploads a file directly to an organization's folder structure.
// Files are stored physically with UUID names in a flat structure per organization.
func (s *ResourceService) UploadOrganizationFile(ctx context.Context, orgID uuid.UUID, req payloads.UploadOrganizationFileRequest, fileHeader *multipart.FileHeader) (db.OrganizationFile, error) {
	src, err := fileHeader.Open()
	if err != nil {
		return db.OrganizationFile{}, fmt.Errorf("failed to open uploaded organization file: %w", err)
	}
	defer src.Close()

	if fileHeader.Size > MaxUploadSizeOrganizationFile {
		return db.OrganizationFile{}, fmt.Errorf("organization file exceeds max size %d bytes", MaxUploadSizeOrganizationFile)
	}

	var parentFolderIDParam *uuid.UUID
	if req.ParentFolderID != uuid.Nil {
		parentFolder, err := s.Querier.GetOrganizationFileByID(ctx, req.ParentFolderID)
		if err != nil || !parentFolder.IsFolder || parentFolder.OrganizationID != orgID {
			return db.OrganizationFile{}, fmt.Errorf("parent folder %s not found or invalid for file upload: %w", req.ParentFolderID, utils.ErrBadRequest)
		}
		parentFolderIDParam = &req.ParentFolderID
	}

	// Check for name collision in the target parent folder
	var existingFilesInParent []db.ListOrganizationFilesInRootRow // Use RootRow as common structure
	var listErrUpload error
	if parentFolderIDParam == nil {
		existingFilesInParent, listErrUpload = s.Querier.ListOrganizationFilesInRoot(ctx, db.ListOrganizationFilesInRootParams{
			OrganizationID: orgID,
			Limit:          1000, // Check many files for collision
			Offset:         0,
		})
	} else {
		folderFiles, listErrIntermediate := s.Querier.ListOrganizationFilesInFolder(ctx, db.ListOrganizationFilesInFolderParams{
			OrganizationID: orgID,
			ParentFolderID: parentFolderIDParam,
			Limit:          1000, // Check many files for collision
			Offset:         0,
		})
		if listErrIntermediate == nil {
			existingFilesInParent = make([]db.ListOrganizationFilesInRootRow, len(folderFiles))
			for i, row := range folderFiles {
				existingFilesInParent[i] = db.ListOrganizationFilesInRootRow(row)
			}
		} else {
			listErrUpload = listErrIntermediate
		}
	}

	if listErrUpload != nil && listErrUpload != sql.ErrNoRows {
		log.Ctx(ctx).Error().Err(listErrUpload).Msg("Failed to list files for name collision check during upload")
		// Consider returning error: return db.OrganizationFile{}, fmt.Errorf("failed to check for file name collision: %w", listErrUpload)
	} else if listErrUpload == nil {
		originalFileName := fileHeader.Filename
		for _, f := range existingFilesInParent {
			if f.FileName == originalFileName && !f.IsFolder {
				return db.OrganizationFile{}, fmt.Errorf("a file with name '%s' already exists in this virtual location: %w", originalFileName, utils.ErrConflict)
			}
		}
	}

	// Generate UUID for physical filename, preserve original extension.
	// Use original file name for collision check and final DB record.
	originalFileName := fileHeader.Filename
	physicalFileNameUUID := uuid.New().String()
	originalFileExt := filepath.Ext(originalFileName)
	physicalFileNameWithExt := physicalFileNameUUID + originalFileExt

	// Calculate the correct logical path for the *file name* (not the physical name)
	fileLogicalPath, err := s.buildLogicalPath(ctx, orgID, parentFolderIDParam, originalFileName)
	if err != nil {
		return db.OrganizationFile{}, fmt.Errorf("failed to build logical path for file: %w", err)
	}

	// Save the file physically
	orgFileStorePath := filepath.Join(UploadDirOrganizationFiles, orgID.String())
	if err := os.MkdirAll(orgFileStorePath, os.ModePerm); err != nil {
		return db.OrganizationFile{}, fmt.Errorf("failed to create organization's file store directory: %w", err)
	}
	physicalFilePathOnDisk := filepath.Join(orgFileStorePath, physicalFileNameWithExt)

	dst, err := os.Create(physicalFilePathOnDisk)
	if err != nil {
		return db.OrganizationFile{}, fmt.Errorf("failed to create destination file '%s': %w", physicalFilePathOnDisk, err)
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		os.Remove(physicalFilePathOnDisk) // Attempt cleanup
		return db.OrganizationFile{}, fmt.Errorf("failed to copy uploaded file content: %w", err)
	}

	// Prepare DB parameters
	var fileTypeParam *string
	contentType := fileHeader.Header.Get("Content-Type")
	if contentType != "" {
		fileTypeParam = &contentType
	}
	fileSize := fileHeader.Size
	fileSizeParam := &fileSize

	params := db.CreateOrganizationFileParams{
		OrganizationID: orgID,
		FileName:       originalFileName,        // Store original file name
		FilePath:       physicalFileNameWithExt, // Store physical name (relative to org dir)
		FileType:       fileTypeParam,
		FileSize:       fileSizeParam,
		IsFolder:       false, // Explicitly false for file uploads
		ParentFolderID: parentFolderIDParam,
	}

	createdFile, err := s.Querier.CreateOrganizationFile(ctx, params)
	if err != nil {
		os.Remove(physicalFilePathOnDisk) // Rollback file creation if DB insert fails
		return db.OrganizationFile{}, fmt.Errorf("failed to save organization file metadata to database: %w", err)
	}
	// Inject the conceptual logical path for the response, DB stores physical
	createdFile.FilePath = fileLogicalPath
	return createdFile, nil
}

// recursivelyUpdateDescendantFolderPaths updates the logical FilePath for all subfolders
func (s *ResourceService) recursivelyUpdateDescendantFolderPaths(ctx context.Context, orgID, currentParentFolderID uuid.UUID, oldParentLogicalPath, newParentLogicalPath string) error {
	// List direct children using ListOrganizationFilesInFolder
	directChildrenRows, err := s.Querier.ListOrganizationFilesInFolder(ctx, db.ListOrganizationFilesInFolderParams{
		OrganizationID: orgID,
		ParentFolderID: &currentParentFolderID, // Pass pointer as the SQL query expects it
		Limit:          10000,                  // High limit to get all children for recursion
		Offset:         0,
	})
	if err != nil && err != sql.ErrNoRows {
		return fmt.Errorf("failed to list children of folder %s for recursive path update: %w", currentParentFolderID, err)
	}

	for _, child := range directChildrenRows { // Iterate over the new Row type
		if child.IsFolder {
			var childNewLogicalPath string
			if newParentLogicalPath == "" {
				childNewLogicalPath = child.FileName
			} else {
				childNewLogicalPath = filepath.Join(newParentLogicalPath, child.FileName)
			}

			childOldLogicalPath := child.FilePath

			updateChildParams := db.UpdateOrganizationFileParams{
				ID:       child.ID,
				FilePath: &childNewLogicalPath,
			}
			_, err := s.Querier.UpdateOrganizationFile(ctx, updateChildParams)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("childFolderID", child.ID.String()).Msg("Failed to update FilePath for child folder during recursive update")
			}

			err = s.recursivelyUpdateDescendantFolderPaths(ctx, orgID, child.ID, childOldLogicalPath, childNewLogicalPath)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("childFolderID", child.ID.String()).Msg("Error in deeper recursion for descendant folder path update")
			}
		}
	}
	return nil
}

// isAncestorOf checks if potentialAncestorID is an ancestor of itemID in the folder hierarchy.
// This is used to prevent moving a folder into its own descendant.
func (s *ResourceService) isAncestorOf(ctx context.Context, orgID, itemIDToCheck, potentialAncestorID uuid.UUID) (bool, error) {
	if itemIDToCheck == potentialAncestorID {
		return true, nil
	}

	currentID := itemIDToCheck
	maxDepth := 20

	for i := 0; i < maxDepth; i++ {
		item, err := s.Querier.GetOrganizationFileByID(ctx, currentID)
		if err != nil {
			if err == sql.ErrNoRows {
				return false, nil
			}
			return false, fmt.Errorf("failed to get item %s during ancestor check: %w", currentID, err)
		}

		if item.ParentFolderID == nil {
			return false, nil
		}

		if *item.ParentFolderID == potentialAncestorID {
			return true, nil
		}
		currentID = *item.ParentFolderID
	}
	log.Ctx(ctx).Warn().Stringer("itemIDToCheck", &itemIDToCheck).Stringer("potentialAncestorID", &potentialAncestorID).Msg("isAncestorOf check reached max depth, potential cycle or very deep structure")
	return false, fmt.Errorf("max depth reached in ancestor check, potential cycle or very deep structure")
}

// DeleteOrganizationFile deletes a file or folder (including its contents if a folder).
func (s *ResourceService) DeleteOrganizationFile(ctx context.Context, orgID, fileOrFolderID uuid.UUID) error {
	item, err := s.Querier.GetOrganizationFileByID(ctx, fileOrFolderID)
	if err != nil {
		if err == sql.ErrNoRows {
			return utils.ErrNotFound
		}
		return fmt.Errorf("file/folder with ID %s not found for deletion: %w", fileOrFolderID, err)
	}

	if item.OrganizationID != orgID {
		return utils.ErrForbidden
	}

	if item.IsFolder {
		allFetchedItems, err := s.Querier.GetAllDescendantOrganizationItemsIncludingParent(ctx, item.ID)
		if err != nil && err != sql.ErrNoRows {
			return fmt.Errorf("failed to list descendant items for folder %s deletion: %w", item.ID, err)
		}

		orgFileStorePath := filepath.Join(UploadDirOrganizationFiles, orgID.String())
		for _, currentItem := range allFetchedItems {
			if currentItem.ID == item.ID {
				continue
			}

			if !currentItem.IsFolder {
				physicalDescendantPath := filepath.Join(orgFileStorePath, currentItem.FilePath)
				if err := os.Remove(physicalDescendantPath); err != nil {
					log.Ctx(ctx).Error().Err(err).Str("path", physicalDescendantPath).Str("fileID", currentItem.ID.String()).Msg("Failed to delete physical file for descendant during folder deletion")
				}
			}
		}
	} else {
		physicalItemPath := filepath.Join(UploadDirOrganizationFiles, orgID.String(), item.FilePath)
		if err := os.Remove(physicalItemPath); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("path", physicalItemPath).Str("fileID", item.ID.String()).Msg("Failed to delete physical file from storage")
		}
	}

	if err := s.Querier.DeleteOrganizationFile(ctx, fileOrFolderID); err != nil {
		if err == sql.ErrNoRows {
			return utils.ErrNotFound
		}
		return fmt.Errorf("failed to delete file/folder record %s from database: %w", fileOrFolderID, err)
	}

	return nil
}

// ListOrganizationFiles lists files and folders for an organization.
// It calls the appropriate Root or Folder querier based on ParentFolderID.
func (s *ResourceService) ListOrganizationFiles(ctx context.Context, orgID uuid.UUID, params payloads.ListOrgFilesParams) ([]payloads.OrganizationFileResponse, int64, error) {
	var totalCount int64
	// Use ListOrganizationFilesInRootRow as the common type, since Root and Folder rows are identical
	var dbFileRows []db.ListOrganizationFilesInRootRow
	var err error

	if params.Path != nil && *params.Path != "" && *params.Path != "/" {
		// Path based listing requires resolving path to a folder ID first.
		// This part is complex and not fully implemented here.
		// For now, we rely on ParentFolderID.
		return nil, 0, fmt.Errorf("path-based listing is not implemented via this parameter directly. Resolve path to parent_folder_id first")
	}

	if params.ParentFolderID == nil || *params.ParentFolderID == uuid.Nil {
		// List Root
		listParams := db.ListOrganizationFilesInRootParams{
			OrganizationID: orgID,
			Limit:          int32(params.Limit),
			Offset:         int32(params.Offset),
		}
		dbFileRows, err = s.Querier.ListOrganizationFilesInRoot(ctx, listParams)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to list organization files in root: %w", err)
		}

		totalCount, err = s.Querier.CountOrganizationFilesInRoot(ctx, orgID)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to count organization files in root")
			// Non-fatal, proceed with empty count if needed
			totalCount = 0
		}
	} else {
		// List Folder
		parentFolderID := *params.ParentFolderID
		// Validate parent folder exists and belongs to org
		parentFolder, errVal := s.Querier.GetOrganizationFileByID(ctx, parentFolderID)
		if errVal != nil {
			log.Ctx(ctx).Error().Err(errVal).Stringer("parentFolderID", &parentFolderID).Msg("Failed to get parent folder for validation in List")
			if errVal == sql.ErrNoRows {
				return nil, 0, fmt.Errorf("parent folder not found: %w", utils.ErrNotFound)
			}
			return nil, 0, fmt.Errorf("failed validating parent folder: %w", errVal)
		}
		if parentFolder.OrganizationID != orgID || !parentFolder.IsFolder {
			log.Ctx(ctx).Error().Stringer("parentFolderID", &parentFolderID).Msg("Invalid parent folder specified (wrong org or not a folder)")
			return nil, 0, fmt.Errorf("invalid parent folder specified: %w", utils.ErrBadRequest)
		}

		listParams := db.ListOrganizationFilesInFolderParams{
			OrganizationID: orgID,
			ParentFolderID: &parentFolderID, // Pass pointer for sqlc
			Limit:          int32(params.Limit),
			Offset:         int32(params.Offset),
		}
		// Assign to the common slice type
		folderRows, err := s.Querier.ListOrganizationFilesInFolder(ctx, listParams)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to list organization files in folder %s: %w", parentFolderID, err)
		}
		// Convert FolderRow slice to RootRow slice for the payload function
		dbFileRows = make([]db.ListOrganizationFilesInRootRow, len(folderRows))
		for i, row := range folderRows {
			dbFileRows[i] = db.ListOrganizationFilesInRootRow(row) // Convert
		}

		totalCount, err = s.Querier.CountOrganizationFilesInFolder(ctx, db.CountOrganizationFilesInFolderParams{
			OrganizationID: orgID,
			ParentFolderID: &parentFolderID, // Pass pointer for sqlc
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Stringer("parentFolderID", &parentFolderID).Msg("Failed to count organization files in folder")
			// Non-fatal, proceed with empty count if needed
			totalCount = 0
		}
	}

	return payloads.ToOrganizationFileListResponseFromRows(dbFileRows), totalCount, nil
}

// UpdateOrganizationFile renames a file/folder or moves it (changing parent_folder_id).
// It also handles recursive updates of logical FilePath for descendant folders.
func (s *ResourceService) UpdateOrganizationFile(ctx context.Context, orgID, fileOrFolderID uuid.UUID, req payloads.UpdateOrganizationFileRequest) (db.OrganizationFile, error) {
	item, err := s.Querier.GetOrganizationFileByID(ctx, fileOrFolderID)
	if err != nil {
		if err == sql.ErrNoRows {
			return db.OrganizationFile{}, utils.ErrNotFound
		}
		return db.OrganizationFile{}, fmt.Errorf("file/folder with ID %s not found for update: %w", fileOrFolderID, err)
	}

	if item.OrganizationID != orgID {
		return db.OrganizationFile{}, utils.ErrForbidden // Ensure item belongs to the org
	}

	isNameChange := req.NewName != nil && *req.NewName != "" && *req.NewName != item.FileName
	isMove := false
	var newParentFolderIDParam *uuid.UUID // This will be the value used in the final DB update

	currentParentIDOnItem := item.ParentFolderID // Can be nil

	// Determine if it's a move and set newParentFolderIDParam for the update query
	if req.ParentFolderID != nil { // A specific parent folder ID was requested in the payload
		requestedNewParentUUID := *req.ParentFolderID
		if requestedNewParentUUID == uuid.Nil { // Requesting move to root
			if currentParentIDOnItem != nil { // Only a move if it wasn't already in root
				isMove = true
			}
			newParentFolderIDParam = nil // Set to NULL for DB update
		} else { // Requesting move to a specific folder
			if currentParentIDOnItem == nil || *currentParentIDOnItem != requestedNewParentUUID { // A move if current parent is different
				isMove = true
			}
			newParentFolderIDParam = &requestedNewParentUUID // Use the requested UUID for DB update
		}
	} else {
		// No parent folder ID was provided in the request, so keep the existing one for the update query
		newParentFolderIDParam = currentParentIDOnItem
	}

	if !isNameChange && !isMove {
		return item, nil // No change requested
	}

	// Validate New Name
	newName := item.FileName
	if isNameChange {
		newName = slug.Make(*req.NewName) // Sanitize new name
		if newName == "" {
			return db.OrganizationFile{}, fmt.Errorf("new name sanitizes to an empty string: %w", utils.ErrBadRequest)
		}
	}

	// Validate Move Target (if moving)
	if isMove {
		if newParentFolderIDParam != nil { // Moving to a specific folder (not root)
			// 1. Check if target parent exists, is a folder, and belongs to the org
			targetParentFolder, err := s.Querier.GetOrganizationFileByID(ctx, *newParentFolderIDParam)
			if err != nil || !targetParentFolder.IsFolder || targetParentFolder.OrganizationID != orgID {
				return db.OrganizationFile{}, fmt.Errorf("target parent folder ID %s invalid: %w", *newParentFolderIDParam, utils.ErrBadRequest)
			}
			// 2. Check for cyclic move (cannot move a folder into itself or a descendant)
			if item.IsFolder {
				if item.ID == *newParentFolderIDParam {
					return db.OrganizationFile{}, fmt.Errorf("cannot move a folder into itself: %w", utils.ErrBadRequest)
				}
				isAncestor, errCheckCycle := s.isAncestorOf(ctx, orgID, *newParentFolderIDParam, item.ID)
				if errCheckCycle != nil {
					return db.OrganizationFile{}, fmt.Errorf("error checking for cyclic move: %w", errCheckCycle)
				}
				if isAncestor {
					return db.OrganizationFile{}, fmt.Errorf("cannot move folder into one of its own subfolders: %w", utils.ErrBadRequest)
				}
			}
		}
		// No specific validation needed for moving to root (newParentFolderIDParam is nil)
	}

	// Check for Name Collision in the *new* parent folder
	if isNameChange || isMove {
		var siblings []db.ListOrganizationFilesInRootRow // Use RootRow as common structure
		var listSiblingsErr error
		if newParentFolderIDParam == nil { // Checking root
			siblings, listSiblingsErr = s.Querier.ListOrganizationFilesInRoot(ctx, db.ListOrganizationFilesInRootParams{
				OrganizationID: orgID,
				Limit:          10000, // High limit for collision check
				Offset:         0,
			})
		} else { // Checking specific folder
			folderSiblings, listSiblingsIntermediateErr := s.Querier.ListOrganizationFilesInFolder(ctx, db.ListOrganizationFilesInFolderParams{
				OrganizationID: orgID,
				ParentFolderID: newParentFolderIDParam,
				Limit:          10000, // High limit for collision check
				Offset:         0,
			})
			if listSiblingsIntermediateErr == nil {
				siblings = make([]db.ListOrganizationFilesInRootRow, len(folderSiblings))
				for i, row := range folderSiblings {
					siblings[i] = db.ListOrganizationFilesInRootRow(row)
				}
			} else {
				listSiblingsErr = listSiblingsIntermediateErr
			}
		}

		if listSiblingsErr != nil && listSiblingsErr != sql.ErrNoRows {
			return db.OrganizationFile{}, fmt.Errorf("failed to list siblings for collision check: %w", listSiblingsErr)
		}

		// Perform the collision check
		for _, sibling := range siblings {
			if sibling.ID != item.ID && sibling.FileName == newName && sibling.IsFolder == item.IsFolder {
				return db.OrganizationFile{}, fmt.Errorf("an item named '%s' (%s) already exists in the target location: %w", newName, map[bool]string{true: "folder", false: "file"}[sibling.IsFolder], utils.ErrConflict)
			}
		}
	}

	// Prepare DB Update Parameters
	updateParams := db.UpdateOrganizationFileParams{
		ID: item.ID,
	}
	var newLogicalPathForThisItem string // Will store the updated logical path
	oldLogicalPath := item.FilePath      // Store old path for recursive update trigger

	if isNameChange {
		updateParams.FileName = &newName
	}
	// Only set ParentFolderID in update params if it's actually a move
	if isMove {
		updateParams.ParentFolderID = newParentFolderIDParam // This can be nil (move to root) or a pointer to UUID
	}

	// Calculate and set the new FilePath (logical path) if needed
	if item.IsFolder {
		// Folder path changes if name changes OR it moves
		if isNameChange || isMove {
			newLogicalPathForThisItem, err = s.buildLogicalPath(ctx, orgID, newParentFolderIDParam, newName)
			if err != nil {
				return db.OrganizationFile{}, fmt.Errorf("failed to build new logical path for updated folder: %w", err)
			}
			updateParams.FilePath = &newLogicalPathForThisItem
		} else {
			newLogicalPathForThisItem = item.FilePath // Path doesn't change if only content is updated
		}
	} else {
		// File's FilePath in DB stores the physical name, which does NOT change on rename/move
		// Logical path is conceptual for files, DB FilePath remains the physical identifier.
		newLogicalPathForThisItem = item.FilePath
	}

	// Execute the Update
	updatedItemDB, err := s.Querier.UpdateOrganizationFile(ctx, updateParams)
	if err != nil {
		return db.OrganizationFile{}, fmt.Errorf("failed to update file/folder metadata: %w", err)
	}

	// Trigger recursive update for descendants if a folder was moved or renamed
	if item.IsFolder && (isNameChange || isMove) {
		err = s.recursivelyUpdateDescendantFolderPaths(ctx, orgID, item.ID, oldLogicalPath, updatedItemDB.FilePath)
		if err != nil {
			// Log the error, but don't fail the primary update operation
			log.Ctx(ctx).Error().Err(err).Str("folderID", item.ID.String()).Msg("Failed to recursively update descendant folder paths after move/rename")
		}
	}

	return updatedItemDB, nil
}
