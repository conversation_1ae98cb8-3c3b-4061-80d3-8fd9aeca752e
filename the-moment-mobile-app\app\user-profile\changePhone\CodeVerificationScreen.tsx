import React, { useEffect, useState } from 'react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import BaseCodeVerificationScreen from '@/login/components/BaseCodeVerificationScreen';
import {
    useExistingPhoneOtpInitiate,
    useExistingPhoneOtpVerify
} from '@/api/authentication_services';
import {
    ExistingPhoneOtpInitiateRequest,
    ExistingPhoneOtpVerifyRequest,
    PhoneOtpVerifyResponse
} from '@/api/api_config';
import { generatePKCE } from '@/utils/pkce';

export default function OldPhoneCodeVerificationScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<{ phoneNumber: string; method?: string; apiState?: string; codeVerifier?: string }>();
  // const {
  //   initiatePhoneOTP,
  //   verifyPhoneOTP,
  //   generatePKCECredentials,
  //   getPKCECredentials,
  //   clearPKCECredentials,
  //   error: authError
  // } = useAuth();
  const { mutateAsync: initiatePhoneOTP, error: initiateError } = useExistingPhoneOtpInitiate();
  const { mutateAsync: verifyPhoneOTP } = useExistingPhoneOtpVerify();

  const phoneNumber = params.phoneNumber || "";
  // Method is now always whatsapp, but we can keep it if BaseCodeVerificationScreen expects it.
  const verificationMethod = (params.method || 'whatsapp') as 'sms' | 'whatsapp'; 

  // State to hold PKCE verifier and API state for verification
  const [currentCodeVerifier, setCurrentCodeVerifier] = useState<string | null>(params.codeVerifier || null);
  const [currentApiState, setCurrentApiState] = useState<string | null>(params.apiState || null);

  useEffect(() => {
    console.log('Profile Change - Old Phone CodeVerificationScreen loaded');
    console.log('Verifying OLD phone number:', phoneNumber);
    console.log('Verification method:', verificationMethod);

    // Check if we have valid PKCE credentials
    if (!params.apiState || !params.codeVerifier) {
        console.warn('OldPhoneCodeVerificationScreen: apiState or codeVerifier missing from params. Attempting to resend code.');
        // Automatically initiate OTP and PKCE generation on first load if params are missing
        handleResendOldPhoneCode().catch(err => {
            console.error("Initial OTP send failed:", err);
        });
    } else {
        // Test if the code verifier is from weak generation (Math.random based)
        const isWeakPattern = /^[a-z0-9]+$/.test(params.codeVerifier);

        if (isWeakPattern) {
            // Weak PKCE credentials only contain lowercase alphanumeric characters
            console.warn('OldPhoneCodeVerificationScreen: codeVerifier appears to be from weak generation (Math.random based). Regenerating with proper PKCE.');
            handleResendOldPhoneCode().catch(err => {
                console.error("PKCE regeneration failed:", err);
            });
        } else {
            console.log('[CodeVerificationScreen] PKCE appears to be strong, updating state');
            // Ensure state is updated if params change (e.g. navigation within the same screen instance)
            setCurrentApiState(params.apiState);
            setCurrentCodeVerifier(params.codeVerifier);
        }
    }
  }, [params.apiState, params.codeVerifier, phoneNumber, verificationMethod]);

  const handleVerifyOldPhoneCode = async (code: string) => {
    console.log('Verifying OTP for OLD phone:', code, 'for number:', phoneNumber);

    if (!currentCodeVerifier || !currentApiState) {
      console.error('PKCE credentials not found or incomplete for old phone verification.');
      throw new Error(t('error.phoneVerification.PKCE_MISSING'));
    }

    const verifyParams: ExistingPhoneOtpVerifyRequest = {
      otp: code,
      code_verifier: currentCodeVerifier,
      state: currentApiState,
    };

    console.log('[CodeVerificationScreen] Params for verifyPhoneOTP:', verifyParams);

    try {
      // This calls the login verification endpoint (/authn/phone/otp/verify)
      // which is correct for verifying the user's identity with their current phone
      const authResponse: PhoneOtpVerifyResponse = await verifyPhoneOTP(verifyParams);

      console.log('Old phone verified successfully. Response:', authResponse);

      return { success: true, data: authResponse };
    } catch (error: any) {
      console.error('Error in handleVerifyOldPhoneCode:', error);

      // Enhanced error handling for better user experience
      let detailedErrorMessage = t('error.phoneVerification.SYSTEM_ERROR');

      if (error && error.response && error.response.data && error.response.data.error) {
        const backendErrorCode = error.response.data.error;
        detailedErrorMessage = t(`error.phoneVerification.${backendErrorCode}`, {
          defaultValue: detailedErrorMessage
        });
      } else if (error && error.message) {
        detailedErrorMessage = error.message;
      }

      throw new Error(detailedErrorMessage);
    }
  };

  const handleResendOldPhoneCode = async () => {
    console.log('Resending code for OLD phone:', phoneNumber, 'method:', verificationMethod);
    const pkceData = await generatePKCE();

    if (!pkceData || !pkceData.codeChallenge || !pkceData.state || !pkceData.codeVerifier) {
      console.error('Failed to generate PKCE credentials for resend or data incomplete.');
      throw new Error(t('error.phoneVerification.PKCE_GENERATION_FAILED', { defaultValue: 'Failed to generate security credentials for resending code.' }));
    }

    setCurrentCodeVerifier(pkceData.codeVerifier); // Store for verification

    const requestBody: ExistingPhoneOtpInitiateRequest = {
      phone: phoneNumber,
      code_challenge: pkceData.codeChallenge,
      code_challenge_method: 'S256' as const,
      client_id: phoneNumber.replace('+852', ''), // Match format used in PhoneVerificationScreen
      state: pkceData.state,
      phone_otp_channel: 'whatsapp',
    };

    try {
      const initiateResponse = await initiatePhoneOTP(requestBody);
      if (initiateResponse && initiateResponse.state) {
        setCurrentApiState(initiateResponse.state); // IMPORTANT: Use state from API response for verify step
      } else {
        console.error('OTP Initiation response missing state.');
        throw new Error(t('error.phoneVerification.INITIATE_STATE_MISSING'));
      }
      console.log('Code resent successfully for old phone. API state for verify:', initiateResponse.state);
    } catch (error: any) {
      console.error('Resend code error for old phone (caught in handleResendOldPhoneCode):', error);
      const errorMessage = initiateError?.message || error.message || t('error.phoneVerification.RESEND_FAILED', { defaultValue: 'Failed to resend verification code.' });
      throw new Error(errorMessage);
    }
  };

  const handleSuccessNavigation = () => {
    // Navigate to new phone entry screen after successful old phone verification
    router.push({
      pathname: '/user-profile/changePhone/ChangePhoneScreen',
      params: {
        oldPhoneNumber: phoneNumber,
        oldPhoneVerified: 'true'
      }
    });
  };

  return (
    <BaseCodeVerificationScreen
      phoneNumber={phoneNumber}
      verificationMethod={verificationMethod}
      onVerify={handleVerifyOldPhoneCode}
      onResendCode={handleResendOldPhoneCode}
      onSuccessNavigation={handleSuccessNavigation}
      screenStackTitleKey="profile.changePhone.verifyCurrentPhone"
      headerSubtitleKey="profile.changePhone.verificationCodeDesc"
      showDisplayNameInput={false}
      isNewUserFlow={false}
    />
  );
} 