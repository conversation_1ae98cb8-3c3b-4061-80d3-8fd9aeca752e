-- Add 'home_visit' to verification_type_enum if it doesn't already exist
-- This ensures idempotency if migration 000029 was run or not.
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'home_visit' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'verification_type_enum')) THEN
        ALTER TYPE verification_type_enum ADD VALUE 'home_visit';
    END IF;
END$$;

-- Table for home visit specific data
CREATE TABLE verification_home_visits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID NOT NULL UNIQUE REFERENCES user_verification_requests(id) ON DELETE CASCADE,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE INDEX idx_verification_home_visits_request_id ON verification_home_visits(verification_request_id);

-- Apply the trigger for updated_at
DO $$
DECLARE
    trigger_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        JOIN pg_namespace n ON c.relnamespace = n.oid
        WHERE n.nspname = current_schema()
          AND c.relname = 'verification_home_visits'
          AND t.tgname = 'set_timestamp'
    ) INTO trigger_exists;

    IF NOT trigger_exists THEN
        EXECUTE format('CREATE TRIGGER set_timestamp
                        BEFORE UPDATE ON %I
                        FOR EACH ROW EXECUTE PROCEDURE trigger_set_timestamp()', 'verification_home_visits');
    END IF;
END;
$$; 