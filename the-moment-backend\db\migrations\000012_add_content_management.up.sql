-- Posts Table
CREATE TABLE "posts" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "organization_id" uuid NOT NULL,
  "title" VARCHAR(255) NOT NULL,
  "slug" VARCHAR(255) UNIQUE NOT NULL,
  "markdown_content" TEXT,
  "status" VARCHAR(50) NOT NULL DEFAULT 'draft', -- 'draft', 'published', 'hidden'
  "published_at" TIMESTAMPTZ,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  FOREIGN KEY ("organization_id") REFERENCES "organizations" ("id") ON DELETE CASCADE
);

CREATE INDEX ON "posts" ("organization_id");
CREATE INDEX ON "posts" ("slug");
CREATE INDEX ON "posts" ("status");

-- Post Media Items Table (for images/PDFs embedded in posts)
CREATE TABLE "post_media_items" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "post_id" uuid NOT NULL,
  "file_name" VARCHAR(255) NOT NULL,
  "file_path" VARCHAR(1024) NOT NULL UNIQUE, -- Path on local server storage
  "file_type" VARCHAR(100) NOT NULL, -- e.g., 'image/jpeg', 'application/pdf'
  "file_size" BIGINT NOT NULL, -- File size in bytes
  "uploaded_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  FOREIGN KEY ("post_id") REFERENCES "posts" ("id") ON DELETE CASCADE
);

CREATE INDEX ON "post_media_items" ("post_id");

-- Resources Table
CREATE TABLE "resources" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "organization_id" uuid NOT NULL,
  "title" VARCHAR(255) NOT NULL,
  "slug" VARCHAR(255) NOT NULL, -- slug should be unique per organization
  "description" TEXT,
  "visibility" VARCHAR(50) NOT NULL DEFAULT 'org_only', -- 'public', 'org_only'
  "status" VARCHAR(50) NOT NULL DEFAULT 'draft', -- 'draft', 'published'
  "published_at" TIMESTAMPTZ,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  FOREIGN KEY ("organization_id") REFERENCES "organizations" ("id") ON DELETE CASCADE,
  UNIQUE ("organization_id", "slug")
);

CREATE INDEX ON "resources" ("organization_id");
CREATE INDEX ON "resources" ("slug");
CREATE INDEX ON "resources" ("visibility");
CREATE INDEX ON "resources" ("status");

-- Resource Files Table (files attached to a resource post)
CREATE TABLE "resource_files" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "resource_id" uuid NOT NULL,
  "file_name" VARCHAR(255) NOT NULL,
  "file_path" VARCHAR(1024) NOT NULL UNIQUE, -- Path on local server storage (e.g., uploads/org_id/resource_id/file_name)
  "file_type" VARCHAR(100) NOT NULL,
  "file_size" BIGINT NOT NULL,
  "uploaded_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  FOREIGN KEY ("resource_id") REFERENCES "resources" ("id") ON DELETE CASCADE
);

CREATE INDEX ON "resource_files" ("resource_id");

-- Organization Files Table (direct file uploads to org folders, not tied to a specific resource post)
CREATE TABLE "organization_files" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "organization_id" uuid NOT NULL,
  "file_name" VARCHAR(255) NOT NULL,
  "file_path" VARCHAR(1024) NOT NULL UNIQUE, -- Path on local server storage (e.g., uploads/org_id/folder_path/file_name)
  "file_type" VARCHAR(100), -- Nullable for folders
  "file_size" BIGINT, -- Nullable for folders
  "is_folder" BOOLEAN NOT NULL DEFAULT FALSE,
  "parent_folder_id" uuid NULL, -- Self-referential for subfolders, NULL for root files/folders in org
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  FOREIGN KEY ("organization_id") REFERENCES "organizations" ("id") ON DELETE CASCADE,
  FOREIGN KEY ("parent_folder_id") REFERENCES "organization_files" ("id") ON DELETE CASCADE,
  UNIQUE ("organization_id", "parent_folder_id", "file_name", "is_folder") -- Ensure unique file/folder names within the same directory
);

CREATE INDEX ON "organization_files" ("organization_id");
CREATE INDEX ON "organization_files" ("parent_folder_id");

-- Triggers to update "updated_at" columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_posts_updated_at
BEFORE UPDATE ON "posts"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_resources_updated_at
BEFORE UPDATE ON "resources"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organization_files_updated_at
BEFORE UPDATE ON "organization_files"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();