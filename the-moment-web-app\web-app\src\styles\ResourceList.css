.resource-card {
    margin-bottom: 16px;
    position: relative;
    transition: all 0.3s;
}

/* Ensure ribbons are above selection indicators */
.resource-card .ant-badge-ribbon {
    z-index: 2;
}

.resource-card.selected {
    border-color: #1890ff;
}

.resource-card .ant-card-meta-title {
    margin-bottom: 4px !important;
}

.resource-card .ant-card-body {
    padding: 20px;
}

.resource-card .truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.resource-card .download-link {
    display: inline-block;
    max-width: 100%;
    color: rgba(0, 0, 0, 0.45);
}

.resource-card .download-link:hover {
    color: #1890ff;
}

.resource-card .file-info {
    display: inline-flex;
    align-items: center;
}

.file-size {
    color: rgba(0, 0, 0, 0.65);
    font-size: 12px;
    margin-left: 4px;
    text-wrap: nowrap;
}

.resource-card .more-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 16px;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.45);
    z-index: 1;
}

.resource-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.resource-card.selected:hover {
    border-color: #1890ff;
}

.file-list-popover .file-item {
    transition: background-color 0.3s;
    padding: 4px 8px;
}

.file-list-popover .file-item:hover {
    background-color: #f5f5f5;
}

.file-list-popover .file-name {
    color: rgba(0, 0, 0, 0.85);
}

/* Enhanced file list popover styles */
.file-popover-overlay .ant-popover-inner {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.file-popover-overlay .ant-popover-inner-content {
    padding: 0;
}

.file-list-popover {
    min-width: 250px;
    max-width: 500px;
    max-height: 400px;
    overflow: auto;
}

.file-list-popover::-webkit-scrollbar {
    width: 5px;
}

.file-list-popover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.file-list-popover::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
}

.file-list-popover .file-item {
    transition: all 0.3s ease;
    border: 1px solid transparent;
    position: relative;
}

.file-list-popover .file-item:last-child {
    margin-bottom: 0;
}

.file-list-popover .file-item:hover {
    background-color: #f7f9fa;
    border-color: #e6f7ff;
    transform: translateY(-1px);
}

.file-description {
    position: relative;
}

.quote-bar {
    background: #aaaaaa;
    border-radius: 4px;
    width: 3px;
    position: absolute;
    left: 0;
    top: 4px;
    bottom: 4px;
    opacity: 0.8;
}

.file-description .ant-typography {
    line-height: 1.5;
    color: #666;
    font-size: 14px;
}

.file-meta {
    color: #888;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-type {
    letter-spacing: 0.5px;
    font-weight: 500;
    opacity: 0.7;
}

/* Animations for the file items */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-list-popover .file-item {
    animation: fadeIn 0.2s ease-out forwards;
    animation-delay: calc(var(--item-index, 0) * 0.05s);
    opacity: 0;
}

/* Make the popover responsive */
@media (max-width: 576px) {
    .file-popover-overlay .ant-popover-inner {
        width: calc(100vw - 32px) !important;
        max-width: none !important;
    }

    .file-list-popover {
        width: 100% !important;
        max-width: none !important;
    }
}

.batch-download-bar {
    position: fixed;
    bottom: 24px;
    right: 24px;
    left: auto;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    z-index: 1000;
    border: 2px solid #b8ddff;
    transition: all 0.3s ease;
    width: 400px;
    display: flex;
    flex-direction: column;
}

@media screen and (max-width: 768px) {
    .batch-download-bar {
        right: 16px;
        left: 16px;
        width: auto;
        min-width: unset;
        max-width: unset;
    }
}

@media screen and (max-width: 480px) {
    .batch-download-bar {
        bottom: 0;
        right: 0;
        left: 0;
        border-radius: 8px 8px 0 0;
    }
}

.download-bar-content {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.download-bar-content.expanded {
    max-height: 400px;
    padding: 16px 24px;
    overflow-y: auto;
    opacity: 1;
    transform: translateY(0);
}

.batch-download-bar.expanded {
    width: auto;
    max-width: 600px;
}

.download-bar-header {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    flex-wrap: wrap;
    gap: 8px;
}

.download-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.download-item:last-child {
    border-bottom: none;
}

.download-item:hover {
    background-color: #f5f5f5;
}

.download-item-info {
    flex: 1;
    min-width: 0;
    margin-right: 16px;
}

.download-item-info .ant-space {
    width: 100%;
}

.download-item-info .ant-space-item:nth-child(2) {
    flex: 1;
    min-width: 0;
}

.download-item-title {
    color: rgba(0, 0, 0, 0.85);
    margin-right: 8px;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 600px;
}

.download-item-size {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
}

.download-bar-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.download-bar-footer .ant-btn {
    flex: 1;
    min-width: 100px;
    max-width: calc(50% - 4px);
}

.download-group {
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;
    transition: all 0.3s;
}

.download-group-header {
    padding: 6px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.85);
    transition: all 0.3s;
}

.download-group-header:hover {
    background: rgba(0, 0, 0, 0.02);
}

.download-group-title-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
    margin-right: 16px;
}

.download-group-title {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
}

.download-group-content {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.download-group-content.expanded {
    max-height: 300px;
    opacity: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.download-group-arrow {
    flex-shrink: 0;
    transition: transform 0.3s;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
}

.download-group-arrow.expanded {
    transform: rotate(90deg);
}

.download-group-summary {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    flex-shrink: 0;
    white-space: nowrap;
}

@media screen and (max-width: 480px) {
    .download-group-title {
        max-width: 150px;
    }
}

/* Ensure icons and buttons don't shrink */
.download-item .anticon,
.download-item .ant-btn {
    flex-shrink: 0;
}

/* Ensure space between items */
.download-item-info .ant-space {
    gap: 8px;
}

/* Better handle long file names */
.download-item-info .ant-space-item {
    min-width: 0;
}

@media screen and (max-width: 576px) {
    /* Removed resource-header related media queries since they're now handled by Tailwind CSS */
}