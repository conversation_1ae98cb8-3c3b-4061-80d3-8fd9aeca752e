import { create } from 'zustand';
import { AxiosError } from 'axios';
import type {
    ProfileResponse,
    OrganizationListPayload,
    StatisticsPullResponse,
    UserIdPullResponse,
    VerificationPayload,
    VolunteerQualificationPayload,
} from '@/api/api_config';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface UserProfileState {
    profile: ProfileResponse | null;
    isFetching: boolean;
    error: AxiosError | null;
    setProfile: (profile: ProfileResponse | null) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userProfileStore = create<UserProfileState>()(persist<UserProfileState>(
    (set) => ({
        profile: null,
        isFetching: false,
        error: null,
        setProfile: (profile: ProfileResponse | null) => set({ profile, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-profile-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

interface UserOrganizationsState {
    userOrganizations: OrganizationListPayload[] | [];
    isFetching: boolean;
    error: AxiosError | null;
    setUserOrganizations: (organizations: OrganizationListPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userOrganizationsStore = create<UserOrganizationsState>()(persist<UserOrganizationsState>(
    (set) => ({
        userOrganizations: [] ,
        isFetching: false,
        error: null,
        setUserOrganizations: (organizations: OrganizationListPayload[]) => set({ userOrganizations: organizations, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-organizations-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

interface UserStatisticsState {
    statistics: StatisticsPullResponse | null;
    isFetching: boolean;
    error: AxiosError | null;
    setStatistics: (statistics: StatisticsPullResponse) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userStatisticsStore = create<UserStatisticsState>()(persist<UserStatisticsState>(
    (set) => ({
        statistics: null,
        isFetching: false,
        error: null,
        setStatistics: (statistics: StatisticsPullResponse) => set({ statistics, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-statistics-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

interface UserIdState {
    userId: UserIdPullResponse | null;
    isFetching: boolean;
    error: AxiosError | null;
    setUserId: (id: UserIdPullResponse) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userIdStore = create<UserIdState>()(persist<UserIdState>(
    (set) => ({
        userId: null,
        isFetching: false,
        error: null,
        setUserId: (id: UserIdPullResponse) => set({ userId: id, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-id-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

interface UserVerificationsState {
    verifications: VerificationPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setVerifications: (verifications: VerificationPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userVerificationsStore = create<UserVerificationsState>()(persist<UserVerificationsState>(
    (set) => ({
        verifications: [],
        isFetching: false,
        error: null,
        setVerifications: (verifications: VerificationPayload[]) => set({ verifications, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-verifications-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

interface UserVerificationDetailsState {
    verificationDetails: VerificationPayload | null;
    isFetching: boolean;
    error: AxiosError | null;
    setVerificationDetails: (verification: VerificationPayload) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userVerificationDetailsStore = create<UserVerificationDetailsState>()(persist<UserVerificationDetailsState>(
    (set) => ({
        verificationDetails: null,
        isFetching: false,
        error: null,
        setVerificationDetails: (verification: VerificationPayload) => set({ verificationDetails: verification, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-verification-details-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

// State for User's Volunteer Organization Qualifications
interface UserVolunteerQualificationsState {
    volunteerQualifications: VolunteerQualificationPayload[] | [];
    isFetching: boolean;
    error: AxiosError | null;
    setVolunteerQualifications: (verification: VolunteerQualificationPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const userVolunteerQualificationsStore = create<UserVolunteerQualificationsState>()(persist<UserVolunteerQualificationsState>(
    (set) => ({
        volunteerQualifications: [],
        isFetching: false,
        error: null,
        setVolunteerQualifications: (verification: VolunteerQualificationPayload[]) => set({ volunteerQualifications: verification, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'user-volunteer-qualifications-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));
