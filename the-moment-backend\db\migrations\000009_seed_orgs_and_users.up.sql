-- Seed Organizations and Users

DO $$
DECLARE
    org_tongxing_id uuid;
    org_shatinsi_id uuid;
    org_blue_id uuid;
    org_pink_id uuid;

    user_admin_tongxing_id uuid;
    user_manager_shatinsi_id uuid;
    user_admin_shatinsi_id uuid;
    user_admin_blue_id uuid;
    user_admin_pink_id uuid;
    user_regular1_id uuid;
    user_regular2_id uuid;
    user_regular3_id uuid;
    superadmin_id uuid;

    hashed_password TEXT := '$2a$12$JZLhcEoAGMPDxUHWUoxVy.qXa/sR2s52ELi565EqPS.LuC/6Ri.Qm'; -- Hash for '123456'
BEGIN
    -- 0. Get Superadmin ID (assuming seeded by 000003)
    SELECT id INTO superadmin_id FROM users WHERE email = '<EMAIL>';
    IF superadmin_id IS NULL THEN
        RAISE EXCEPTION 'Superadmin user not found, cannot proceed with seeding owners.';
    END IF;

    -- 1. Seed Users First
    -- Org Admin (for 同行明天)
    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at)
    VALUES ('Admin TongXing', '<EMAIL>', '+85299990001', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id INTO user_admin_tongxing_id;

    -- Org Manager (for 沙田西關愛隊)
    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at)
    VALUES ('Manager ShaTinSi', '<EMAIL>', '+85299990002', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id INTO user_manager_shatinsi_id;

    -- New Admin User for 沙田西關愛隊
    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at)
    VALUES ('Admin ShaTinSi', '<EMAIL>', '+85299990003', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id INTO user_admin_shatinsi_id;

    -- New Admin User for 藍色主題組織
    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at)
    VALUES ('Admin BlueOrg', '<EMAIL>', '+85299990004', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id INTO user_admin_blue_id;

    -- New Admin User for 玫紅色主題組織
    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at)
    VALUES ('Admin PinkOrg', '<EMAIL>', '+85299990005', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id INTO user_admin_pink_id;

    -- Regular User 1
    INSERT INTO users (display_name, phone, hashed_password, phone_verified_at)
    VALUES ('Regular User One', '+85299991001', hashed_password, CURRENT_TIMESTAMP)
    RETURNING id INTO user_regular1_id;

    -- Regular User 2
    INSERT INTO users (display_name, phone, hashed_password, phone_verified_at)
    VALUES ('Regular User Two', '+85299991002', hashed_password, CURRENT_TIMESTAMP)
    RETURNING id INTO user_regular2_id;
    
    -- Regular User 3
    INSERT INTO users (display_name, phone, hashed_password, phone_verified_at)
    VALUES ('Regular User Three', '+85299991003', hashed_password, CURRENT_TIMESTAMP)
    RETURNING id INTO user_regular3_id;

    -- 2. Seed Organizations (Now with Owners)
    INSERT INTO organizations (name, description, owner_user_id) VALUES ('同行明天', '一個關心社區發展的組織', user_admin_tongxing_id) RETURNING id INTO org_tongxing_id;
    INSERT INTO organizations (name, description, owner_user_id) VALUES ('沙田西關愛隊', '服務沙田西區居民', user_manager_shatinsi_id) RETURNING id INTO org_shatinsi_id;
    INSERT INTO organizations (name, description, owner_user_id) VALUES ('藍色主題組織', '以藍色為主題的活動', superadmin_id) RETURNING id INTO org_blue_id;
    INSERT INTO organizations (name, description, owner_user_id) VALUES ('玫紅色主題組織', '以玫紅色為主題的活動', superadmin_id) RETURNING id INTO org_pink_id;

    -- 3. Seed Memberships
    -- Owner for 同行明天
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_admin_tongxing_id, org_tongxing_id, 'admin', true);

    -- Owner for 沙田西關愛隊
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_manager_shatinsi_id, org_shatinsi_id, 'admin', true);

    -- Admin for 沙田西關愛隊
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_admin_shatinsi_id, org_shatinsi_id, 'admin', true);

    -- Admin for 藍色主題組織
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_admin_blue_id, org_blue_id, 'admin', true);

    -- Owner for 藍色主題組織
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (superadmin_id, org_blue_id, 'admin', true);

    -- Admin for 玫紅色主題組織
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_admin_pink_id, org_pink_id, 'admin', true);

    -- Owner for 玫紅色主題組織
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (superadmin_id, org_pink_id, 'admin', true);

    -- Regular User 1 in 同行明天 and 藍色主題組織
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_regular1_id, org_tongxing_id, 'member', true);
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_regular1_id, org_blue_id, 'member', true);

    -- Regular User 2 in 沙田西關愛隊
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_regular2_id, org_shatinsi_id, 'member', true);
    
    -- Regular User 3 in 玫紅色主題組織
    INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active)
    VALUES (user_regular3_id, org_pink_id, 'member', true);

END $$; 