import axios from 'axios';
import { API_ENDPOINTS } from './apiEndpoints';

// Token storage should ideally be managed by authService, but api.js currently uses localStorage directly for some checks.
// For the debounce logic, we'll reference localStorage directly as currently structured.
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token'
};

let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

// Real API implementation
class RealApi {
  BASE_URL = import.meta.env.VITE_API_BASE;

  constructor() {
    this.instance = axios.create({
      baseURL: this.BASE_URL,
      timeout: 15000, // Consider if this timeout is affecting refresh calls if they are slow
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupRequestInterceptor();
    this.setupResponseInterceptor();
  }

  setupRequestInterceptor() {
    this.instance.interceptors.request.use(
      (config) => {
        const accessToken = localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
        // console.log('[api.js] Request Interceptor: Sending request to', config.url, 'with headers:', JSON.parse(JSON.stringify(config.headers)));
        return config;
      },
      (error) => Promise.reject(error)
    );
  }

  setupResponseInterceptor() {
    this.instance.interceptors.response.use(
      (response) => {
        if (response.config?.returnFullResponse) {
          return response;
        }
        return response.data;
      },
      async (error) => {
        const originalRequest = error.config;

        // Check if it's a 401 error and not a retry already, and not the refresh token URL itself
        if (error.response?.status === 401 && !originalRequest._retry && originalRequest.url !== API_ENDPOINTS.AUTH.TOKEN_REFRESH) {
          if (isRefreshing) {
            // If a refresh is already in progress, queue this request
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject });
            })
            .then(newAccessToken => {
              originalRequest.headers['Authorization'] = 'Bearer ' + newAccessToken;
              originalRequest._retry = true; // Mark as retried with new token
              return this.instance(originalRequest); // Re-attempt the original request
            })
            .catch(err => {
              // This catch is if the queued promise itself is rejected (e.g. refresh definitively failed)
              return Promise.reject(err); 
            });
          }

          // Mark that we are attempting a refresh for this request path
          originalRequest._retry = true;
          isRefreshing = true;

          try {
            console.log(`[api.js] Interceptor: Attempting token refresh. Original request: ${originalRequest.url}`);
            const currentRefreshToken = localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);

            if (!currentRefreshToken) {
              console.warn('[api.js] Interceptor: No refresh token found. Aborting refresh and processing queue with error.');
              isRefreshing = false; // Reset flag
              const noTokenError = new Error('No refresh token available for refresh attempt.');
              processQueue(noTokenError, null); // Reject all queued requests
              // Also reject the current request that initiated this.
              // authService and AuthContext will handle this error (e.g. by logging out).
              return Promise.reject(noTokenError);
            }

            // Make the actual refresh token call
            const refreshResponse = await this.instance.post(API_ENDPOINTS.AUTH.TOKEN_REFRESH, { refresh_token: currentRefreshToken });
            
            console.log('[api.js] Interceptor: Token refresh successful.');
            const { access_token: newAccessToken, refresh_token: newRefreshToken } = refreshResponse; // Assuming refreshResponse is data
            
            localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, newAccessToken);
            localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, newRefreshToken);
            
            // Update the current original request's header
            originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
            
            // Process the queue with the new token
            processQueue(null, newAccessToken);
            
            // Retry the original request that initiated the refresh
            return this.instance(originalRequest);

          } catch (refreshError) {
            console.error('[api.js] Interceptor: Token refresh attempt FAILED.');
            if (refreshError.isAxiosError && refreshError.response) {
              console.error(`[api.js] Refresh failed with HTTP status: ${refreshError.response.status}`, 
                            { data: refreshError.response.data, headers: refreshError.response.headers });
            } else if (refreshError.isAxiosError && refreshError.request) {
              console.error('[api.js] Refresh failed: No response received, request made.', { config: refreshError.config });
            } else {
              console.error('[api.js] Refresh failed with non-Axios error:', refreshError.message, refreshError);
            }

            // An error during refresh means the session might be unrecoverable.
            // Let authService/AuthContext decide the ultimate fate (logout vs error display)
            // based on the nature of refreshError.
            // Clear out potentially problematic tokens based on refreshError type.
            localStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN); // Access token is definitely bad or stale
            if (this.instance.defaults?.headers?.common) {
              delete this.instance.defaults.headers.common['Authorization'];
            }
            
            // If the refresh token itself is rejected (e.g., 401/403 on refresh endpoint), clear it.
            // This signals to authService that the refresh token is invalid.
            if (refreshError.response && (refreshError.response.status === 401 || refreshError.response.status === 403)) {
                console.warn('[api.js] Interceptor: Refresh token rejected by server. Clearing refresh token.');
                localStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN);
            }

            processQueue(refreshError, null); // Reject all queued requests with the refreshError
            return Promise.reject(refreshError); // Reject the current request with the refreshError
          } finally {
            isRefreshing = false; // Always reset the flag
          }
        } else if (error.response?.status === 401 && originalRequest.url === API_ENDPOINTS.AUTH.TOKEN_REFRESH) {
          // This case is when the refresh token call *itself*, when made directly (e.g. by authService), gets a 401.
          // This is a definitive signal that the refresh token is bad.
          console.warn('[api.js] Interceptor: Direct call to TOKEN_REFRESH failed with 401. Clearing all tokens.');
          localStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN);
          localStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN);
          if (this.instance.defaults?.headers?.common) {
            delete this.instance.defaults.headers.common['Authorization'];
          }
          // The error will propagate to authService.refreshToken, which will then
          // also see the 401 and confirm token clearing, then AuthContext will logout.
          return Promise.reject(error); // Propagate the original 401 error from refresh endpoint
        }

        // For other errors, or if it's a 401 that has already been retried, or for non-401s.
        if (error.response) {
          return Promise.reject(error.response.data || error.response);
        }
        return Promise.reject(error); // Fallback for non-response errors
      }
    );
  }

  // API methods that directly use axios instance
  async get(url, config) {
    return this.instance.get(url, config);
  }

  async getWithFullResponse(url, config = {}) {
    return this.instance.get(url, { ...config, returnFullResponse: true });
  }

  async post(url, data, config) {
    return this.instance.post(url, data, config);
  }

  async put(url, data, config) {
    return this.instance.put(url, data, config);
  }

  async delete(url, config) {
    return this.instance.delete(url, config);
  }

  async patch(url, data, config) {
    return this.instance.patch(url, data, config);
  }
}

const api = new RealApi();

export default api; 