// AdminEventList.js
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Table, Button, Space, Badge, App, Input } from 'antd';
import { useNavigate } from 'react-router-dom';
import { CalendarOutlined, EnvironmentOutlined, UserOutlined, DollarOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import EventFilter from '../../../components/EventFilter';
import { formatDate } from '../../../utils/dateFormatter';
import { formatNumber } from '../../../utils/numberFormatter';
import { eventService } from '../../../services/eventService';
import dayjs from 'dayjs';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../../contexts/OrganizationContext';
import { useAuth } from '../../../contexts/AuthContext';
import { useUser } from '../../../contexts/UserContext';
import ErrorPage from '../../ErrorPage';
import '../../../styles/TableBorder.css';

// Helper function to get tag name based on current language
const getTagName = (tag, language) => {
  if (!tag) return '';
  
  if (language.startsWith('zh')) {
    if (language.includes('HK')) {
      return tag.name_zh_hk || tag.name_en || '';
    } else {
      return tag.name_zh_cn || tag.name_en || '';
    }
  } else {
    return tag.name_en || '';
  }
};

// Function to get the color based on event tags (assuming first tag determines color)
// const getTagColor = (tags) => {
//   if (tags && tags.length > 0 && tags[0].tag_name) {
//     // eventTypes config might need to be keyed by tag_name or id
//     return eventTypes[tags[0].tag_name]?.color || 'gray';
//   }
//   return 'gray';
// };

const AdminEventList = ({ isActive = true }) => {
  const { message } = App.useApp();
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({
    searchText: '',
    dateRange: null,
    eventTypes: [],
    verificationTypes: [],
    fundingTypes: [],
    organizationId: null
  });
  const [dataFetched, setDataFetched] = useState(false);
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { currentOrganization } = useOrganization();
  const { user: authUser, isLoggedIn } = useAuth();
  const { currentUser } = useUser();
  const isSuperAdmin = currentUser?.role === 'super_admin';
  const [showErrorPage, setShowErrorPage] = useState(false);
  const pageSize = 100;
  
  // Refs for tracking previous filter state to avoid redundant fetches
  const lastFiltersRef = useRef(null);
  const lastPageRef = useRef(null);
  const messageRef = useRef();
  const tRef = useRef();
  
  // Store t and message in refs to avoid dependency changes
  useEffect(() => {
    messageRef.current = message;
    tRef.current = t;
  }, [message, t]);

  // Check for access control when organization changes
  useEffect(() => {
    if (currentOrganization?.id === ALL_ORGANIZATION_ID && !isSuperAdmin) {
      message.info(t('messages.notAvailableForAllOrgs'));
      setShowErrorPage(true);
    } else {
      setShowErrorPage(false);
    }
  }, [currentOrganization, isSuperAdmin, message, t]);

  // Update filters when organization changes
  useEffect(() => {
    setFilters(prevFilters => {
      // Don't update if organization ID is the same to prevent unnecessary renders
      if (prevFilters.organizationId === (currentOrganization?.id || ALL_ORGANIZATION_ID)) {
        return prevFilters;
      }
      
      const newFilters = {
        ...prevFilters,
        organizationId: currentOrganization ? currentOrganization.id : ALL_ORGANIZATION_ID,
      };
      return newFilters;
    });
  }, [currentOrganization]);

  const fetchEvents = useCallback(async (page, currentFilters) => {
    setLoading(true);

    try {
      // 获取组织ID的更健壮方法
      const contextOrgId = (currentOrganization?.id) ? currentOrganization.id : null;
      
      // 检查是否为"所有组织"视图
      const isAllOrgsView = currentOrganization?.id === ALL_ORGANIZATION_ID || 
                           currentOrganization?.isAllOrgs === true;
      
      // If not super admin and trying to access all organizations, don't fetch data
      const currentIsSuperAdmin = currentUser?.role === 'super_admin';
      if (isAllOrgsView && !currentIsSuperAdmin) {
        setEvents([]);
        setTotal(0);
        setLoading(false);
        setDataFetched(true);
        return;
      }
      
      let filterPreference = null;
      if (currentFilters.organizationId && currentFilters.organizationId !== '') {
        // currentFilters.organizationId can be a specific org ID or ALL_ORGANIZATION_ID
        filterPreference = currentFilters.organizationId;
      }

      const isLoggedInUser = isLoggedIn && authUser;
      const userRole = isLoggedInUser ? authUser.role : null;
      
      const isSuperAdmin = userRole === 'super_admin';
      const isAdminRole = userRole === 'admin'; // Just admin, not super_admin
      const isStaffRole = userRole === 'staff';
      
      // Combined check for any admin-like role that might use admin views
      const isPrivilegedUser = isSuperAdmin || isAdminRole || isStaffRole;

      let orgIdForQueryResolution;
      if (isPrivilegedUser) {
        orgIdForQueryResolution = filterPreference || contextOrgId;
        
        // 调整为使用新的"所有组织"检测
        if (isAllOrgsView && !filterPreference) {
          orgIdForQueryResolution = ALL_ORGANIZATION_ID;
        }
      } else { 
        // Non-privileged user (e.g., 'user' role, though less likely for this admin component)
        orgIdForQueryResolution = contextOrgId;
        // For non-privileged users, ALL_ORGANIZATION_ID context means "all public events globally"
        if (orgIdForQueryResolution === ALL_ORGANIZATION_ID || isAllOrgsView) {
          orgIdForQueryResolution = null; 
        }
      }

      const clientParams = {
        limit: pageSize,
        offset: (page - 1) * pageSize,
        ...(currentFilters.searchText && { search_term: currentFilters.searchText }),
        ...(currentFilters.dateRange && currentFilters.dateRange[0] && { 
          start_date: dayjs(currentFilters.dateRange[0]).startOf('day').toISOString() 
        }),
        ...(currentFilters.dateRange && currentFilters.dateRange[1] && { 
          end_date: dayjs(currentFilters.dateRange[1]).endOf('day').toISOString() 
        }),
        ...(currentFilters.eventTypes && currentFilters.eventTypes.length > 0 && { tagIds: currentFilters.eventTypes }),
        ...(currentFilters.verificationTypes && currentFilters.verificationTypes.length > 0 && { eventVerificationTypeKeys: currentFilters.verificationTypes }),
        ...(currentFilters.fundingTypes && currentFilters.fundingTypes.length > 0 && { governmentFundingTypeIds: currentFilters.fundingTypes }),
        ...(currentFilters.status && { status: currentFilters.status }),
      };

      let responseData;

      if (isSuperAdmin && orgIdForQueryResolution === ALL_ORGANIZATION_ID) {
        responseData = await eventService.listOrgEvents(ALL_ORGANIZATION_ID, { ...clientParams, isAdminView: true });
      } else if (isPrivilegedUser && orgIdForQueryResolution && orgIdForQueryResolution !== ALL_ORGANIZATION_ID) {
        responseData = await eventService.listOrgEvents(orgIdForQueryResolution, { ...clientParams, isAdminView: true });
      } else if (isPrivilegedUser && (orgIdForQueryResolution === null || (orgIdForQueryResolution === ALL_ORGANIZATION_ID && !isSuperAdmin))) {
        responseData = await eventService.listAllEventsAsAdmin(clientParams); 
      } else if (!isPrivilegedUser) {
        const paramsForPublicList = { ...clientParams };
        if (orgIdForQueryResolution && orgIdForQueryResolution !== ALL_ORGANIZATION_ID) {
            paramsForPublicList.org_id = orgIdForQueryResolution;
        }
        responseData = await eventService.listPublicEvents(paramsForPublicList);
      }

      setEvents(responseData.events || []);
      setTotal(responseData.total || 0);
      setDataFetched(true);

    } catch (error) {
      if (error.name !== 'CanceledError' && error.code !== 'ECONNABORTED') {
        messageRef.current.error(tRef.current('messages.fetchError'));
        console.error("AdminEventsListTab: Error fetching events:", error);
        setEvents([]);
        setTotal(0);
      } else {
        console.warn("AdminEventsListTab: Fetch aborted", error.message);
      }
      setDataFetched(true);
    } finally {
      setLoading(false);
    }
  }, [currentOrganization, pageSize, isLoggedIn, authUser, currentUser]);

  // Effect for handling both page and filter changes
  useEffect(() => {
    if (!isActive) return;
    
    // Check if anything has actually changed that would require a new fetch
    const shouldFetch = 
      // First time this effect runs
      lastFiltersRef.current === null || 
      lastPageRef.current === null ||
      // Page changed
      lastPageRef.current !== currentPage ||
      // Some actual filter value changed
      lastFiltersRef.current?.searchText !== filters.searchText ||
      lastFiltersRef.current?.organizationId !== filters.organizationId ||
      // Compare date ranges (only compare if both exist)
      (lastFiltersRef.current?.dateRange && filters.dateRange 
        ? (lastFiltersRef.current.dateRange[0]?.format() !== filters.dateRange[0]?.format() ||
           lastFiltersRef.current.dateRange[1]?.format() !== filters.dateRange[1]?.format())
        : lastFiltersRef.current?.dateRange !== filters.dateRange) ||
      // Add other filter comparisons as needed
      JSON.stringify(lastFiltersRef.current?.eventTypes) !== JSON.stringify(filters.eventTypes) ||
      JSON.stringify(lastFiltersRef.current?.verificationTypes) !== JSON.stringify(filters.verificationTypes) ||
      JSON.stringify(lastFiltersRef.current?.fundingTypes) !== JSON.stringify(filters.fundingTypes);
    
    if (shouldFetch) {
      // Fetch events when currentPage or filters change,
      // but only if organizationId is set (either from context or default).
      if (filters.organizationId !== undefined && filters.organizationId !== null) {
        fetchEvents(currentPage, filters);
        
        // Update references
        lastFiltersRef.current = {...filters};
        lastPageRef.current = currentPage;
      }
    }
  }, [currentPage, filters, fetchEvents, isActive]);

  // Callback for EventFilter component - with same logic as EventList.js
  const handleFilter = useCallback((newFiltersFromEventFilter) => {
    
    setFilters(prevFilters => {
      let determinedOrgId;

      // Priority to newFiltersFromEventFilter if it provides a valid organizationId
      if (newFiltersFromEventFilter.hasOwnProperty('organizationId') &&
          newFiltersFromEventFilter.organizationId !== undefined &&
          newFiltersFromEventFilter.organizationId !== null &&
          newFiltersFromEventFilter.organizationId !== '') {
          determinedOrgId = newFiltersFromEventFilter.organizationId;
      } else {
          // Fallback to prevFilters.organizationId (from context or previous state)
          determinedOrgId = prevFilters.organizationId;
      }

      // If still no valid organizationId, then use the global default.
      if (determinedOrgId === undefined || determinedOrgId === null || determinedOrgId === '') {
          determinedOrgId = ALL_ORGANIZATION_ID;
      }

      const updatedFilters = {
          ...prevFilters, // Keep other existing filters from previous state
          ...newFiltersFromEventFilter, // Apply all incoming changes from EventFilter
          organizationId: determinedOrgId, // Explicitly set the determined organizationId
      };
      
      return updatedFilters;
    });
    
    setCurrentPage(1); // Reset to page 1 when filters change
  }, []);

  // Format price display
  const formatPrice = useCallback((priceString) => {
    // API returns price as string, e.g., "$50" or "Free" or just numeric string
    if (typeof priceString === 'string') {
      const lowerPriceString = priceString.toLowerCase();
      if (lowerPriceString === 'free' || priceString === '0' || priceString === '') {
        return t('eventCalendar.eventList.price.free');
      }
      // Attempt to parse numeric value if it's like "50" or "$50"
      const numericPrice = parseFloat(priceString.replace(/[^\d.-]/g, ''));
      if (!isNaN(numericPrice)) {
        if (numericPrice === 0) return t('eventCalendar.eventList.price.free');
        return `${t('eventCalendar.eventList.price.prefix')} $${numericPrice.toFixed(2)}`;
      }
    } else if (typeof priceString === 'number') { // Handle if it's already a number
      if (priceString === 0) return t('eventCalendar.eventList.price.free');
      return `${t('eventCalendar.eventList.price.prefix')} ${priceString.toFixed(2)}`;
    }
    // Fallback for unparseable or unexpected format
    return priceString || t('eventCalendar.eventList.price.tbd');
  }, [t]);

  // Handler for create event button
  const handleCreateEvent = useCallback(() => {
    navigate('/events-management/create');
  }, [navigate]);

  // Columns of table
  const columns = [
    {
      title: t('adminEvents.list.columns.coverImage'),
      dataIndex: 'images',
      key: 'images',
      width: 120,
      render: (images, record) => ( // images might be undefined, use record.banner_image_urls
        <img
          src={record.images?.[0] || '/placeholder-image.jpg'}
          alt={record.title || "Event"}
          style={{ width: 100, height: 75, objectFit: 'cover' }}
        />
      ),
    },
    {
      title: t('adminEvents.list.columns.eventName'),
      dataIndex: 'title',
      key: 'title',
      sorter: (a, b) => {
        const titleA = (a.title || '').toLowerCase();
        const titleB = (b.title || '').toLowerCase();
        return titleA.localeCompare(titleB);
      },
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={t('adminEvents.list.filters.searchEventName')}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              size="small"
              style={{ width: 90 }}
            >
              {t('common.search')}
            </Button>
            <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
              {t('common.reset')}
            </Button>
          </Space>
        </div>
      ),
      onFilter: (value, record) => {
        const title = record.title || '';
        return title.toLowerCase().includes(value.toLowerCase());
      },
    },
    {
      title: (
        <>
          <CalendarOutlined /> {t('adminEvents.list.columns.date')}
        </>
      ),
      dataIndex: 'start_time', // Changed from startTime
      key: 'start_time',       // Changed from startTime
      sorter: (a, b) => {
        const dateA = new Date(a.start_time || 0);
        const dateB = new Date(b.start_time || 0);
        return dateA.getTime() - dateB.getTime();
      },
      render: (date) => (
        <span style={{ whiteSpace: 'nowrap' }}>
          {formatDate(date, i18n.language)}
        </span>
      ),
    },
    // {
    //   title: t('adminEvents.list.columns.organization'),
    //   dataIndex: 'organization_id',
    //   key: 'organization_id',
    //   render: (organization_id) => {
    //     return organization_id || t('common.unknown');
    //   }
    // },
    {
      title: (
        <>
          <EnvironmentOutlined /> {t('adminEvents.list.columns.location')}
        </>
      ),
      dataIndex: 'location',
      key: 'location',
      width: '15%',
      sorter: (a, b) => {
        const getLocationText = (record) => {
          if (record.location_type === 'physical') return record.location_full_address || '';
          if (record.location_type === 'online') return record.location_online_url || '';
          if (record.location_type === 'hybrid') return `${record.location_full_address || ''} (${record.location_online_url || ''})`;
          return record.location_full_address || record.location_online_url || '';
        };
        const locationA = getLocationText(a).toLowerCase();
        const locationB = getLocationText(b).toLowerCase();
        return locationA.localeCompare(locationB);
      },
      filters: [
        { text: t('adminEvents.list.filters.physical'), value: 'physical' },
        { text: t('adminEvents.list.filters.online'), value: 'online' },
        { text: t('adminEvents.list.filters.hybrid'), value: 'hybrid' },
      ],
      onFilter: (value, record) => record.location_type === value,
      render: (text, record) => {
        if (record.location_type === 'physical') return record.location_full_address;
        if (record.location_type === 'online') return record.location_online_url;
        if (record.location_type === 'hybrid') return `${record.location_full_address} (${record.location_online_url})`;
        return record.location_full_address || record.location_online_url || 'N/A'; // Fallback for old data or if type not set
      }
    },
    {
      title: (
        <>
          {t('adminEvents.list.columns.count')}
        </>
      ),
      dataIndex: 'registered_count', // Changed from participantsCount
      key: 'registered_count',     // Changed from participantsCount
      width: 100,
      sorter: (a, b) => (a.registered_count || 0) - (b.registered_count || 0),
      render: (count) => formatNumber(count || 0) , // registered_count from API is int64
    },  
    {
      title: (
        <>
          <DollarOutlined /> {t('adminEvents.list.columns.price')}
        </>
      ),
      dataIndex: 'price',
      key: 'price',
      width: 120,
      sorter: (a, b) => {
        const getPriceValue = (priceString) => {
          if (typeof priceString === 'string') {
            const lowerPriceString = priceString.toLowerCase();
            if (lowerPriceString === 'free' || priceString === '0' || priceString === '') return 0;
            const numericPrice = parseFloat(priceString.replace(/[^\d.-]/g, ''));
            return isNaN(numericPrice) ? 0 : numericPrice;
          } else if (typeof priceString === 'number') {
            return priceString;
          }
          return 0;
        };
        return getPriceValue(a.price) - getPriceValue(b.price);
      },
      filters: [
        { text: t('adminEvents.list.filters.free'), value: 'free' },
        { text: t('adminEvents.list.filters.paid'), value: 'paid' },
      ],
      onFilter: (value, record) => {
        const priceValue = typeof record.price === 'string' ? 
          (record.price.toLowerCase() === 'free' || record.price === '0' || record.price === '' ? 0 : parseFloat(record.price.replace(/[^\d.-]/g, ''))) :
          (typeof record.price === 'number' ? record.price : 0);
        
        if (value === 'free') return priceValue === 0;
        if (value === 'paid') return priceValue > 0;
        return true;
      },
      render: (price) => (
        <span style={{ whiteSpace: 'nowrap' }}>
          {formatPrice(price)}
        </span>
      ),
    },
    {
      title: t('adminEvents.list.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      filters: [
        { text: t('adminEvents.status.published'), value: 'published' },
        { text: t('adminEvents.status.draft'), value: 'draft' },
        { text: t('adminEvents.status.hidden'), value: 'hidden' },
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => (
        <span style={{ whiteSpace: 'nowrap' }}>
          <Badge
            status={status === 'published' ? 'success' : (status === 'draft' || status === 'hidden' ? 'warning' : 'default')} // Adjust status mapping
            text={t(`adminEvents.status.${status}`, status)} // Use translation for status
          />
        </span>
      ),
    },
    {
      title: t('adminEvents.list.columns.type'),
      dataIndex: 'tags', // Changed from 'type'
      key: 'tags',       // Changed from 'type'
      sorter: (a, b) => {
        const getTagsText = (tags) => {
          if (!tags || tags.length === 0) return '';
          return tags.map(tag => getTagName(tag, i18n.language)).join(', ').toLowerCase();
        };
        const tagsA = getTagsText(a.tags);
        const tagsB = getTagsText(b.tags);
        return tagsA.localeCompare(tagsB);
      },
      render: (tags) => { // tags is an array of TagResponse
        if (!tags || tags.length === 0) return '';
        return tags.map(tag => getTagName(tag, i18n.language)).join(', ');
      }
    },
    {
      title: t('adminEvents.list.columns.action'),
      key: 'action',
      fixed: 'right',
      width: 100,
      render: (record) => (
        <Space size="middle">
          <Button
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/events-management/${record.id}/edit?returnTo=/events-management`);
            }}
          >
            {t('adminEvents.list.buttons.edit')}
          </Button>
        </Space>
      ),
    },
  ];

  const onRowClick = useCallback((record) => ({
    onClick: () => {
      navigate(`/events-management/${record.id}`, {
        state: { event: record }
      });
    },
  }), [navigate]);

  if (showErrorPage) {
    return <ErrorPage type="403" />;
  }

  return (
    <div>
      {/* EventFilter component */}
      <div className="mb-6">
        <EventFilter
          onFilter={handleFilter}
          showCreateButton={true}
          onCreateEvent={handleCreateEvent}
          initialFilters={filters}
        />
      </div>

      {/* Table to display events */}
      <div className="border rounded-lg overflow-hidden">
        <Table
          loading={loading}
          columns={columns}
          dataSource={events}
          onRow={onRowClick}
          scroll={{ x: 'max-content' }}
          style={{ overflowX: 'auto' }}
          rowKey="id"
          pagination={{
            pageSize: pageSize,
            current: currentPage,
            total: total,
            onChange: (page) => setCurrentPage(page),
            position: ['bottomCenter'],
            showTotal: total => t('common.totalItems', { total: total })
          }}
        />
      </div>
    </div>
  );
};

export default AdminEventList;
