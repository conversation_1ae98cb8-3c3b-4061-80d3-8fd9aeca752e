package services

import (
	"Membership-SAAS-System-Backend/db"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
)

const (
	DefaultJobMaxRetries           = 3
	JobTypePromoteWaitlist         = "promote_waitlist"
	JobTypeNotifyEventPromotion    = "notify_event_promotion"
	JobTypeNotifyEventCancellation = "notify_event_cancellation"
	JobTypeNotifyRegStatusUpdate   = "notify_reg_status_update"
	JobTypeEventReminder           = "event_reminder"
	// TODO: Add job types for notifications
	// JobTypeNotifyEventPromotion      = "notify_event_promotion"
	// JobTypeNotifyEventCancellation   = "notify_event_cancellation"
	// JobTypeNotifyRegStatusUpdate   = "notify_reg_status_update"
	// Add other job types here
)

// JobService defines the interface for job queue operations.
type JobService interface {
	EnqueueJob(ctx context.Context, jobType string, payload interface{}, delay time.Duration, maxRetries *int) (db.Job, error)
	StartWorker(ctx context.Context, workerID string, interval time.Duration, eventSvc EventService, notifySvc NotificationService)
}

type jobService struct {
	queries *db.Queries
	pool    *pgxpool.Pool
}

// NewJobService creates a new instance of JobService.
func NewJobService(q *db.Queries, pool *pgxpool.Pool) JobService {
	return &jobService{
		queries: q,
		pool:    pool,
	}
}

// EnqueueJob adds a new job to the queue.
func (s *jobService) EnqueueJob(ctx context.Context, jobType string, payloadData interface{}, delay time.Duration, maxRetriesOverride *int) (db.Job, error) {
	log.Ctx(ctx).Info().Str("jobType", jobType).Interface("payloadData", payloadData).Msg("[JobService.EnqueueJob] Entry")

	payloadBytes, err := json.Marshal(payloadData)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("jobType", jobType).Msg("[JobService.EnqueueJob] Failed to marshal job payload")
		return db.Job{}, fmt.Errorf("failed to marshal job payload: %w", err)
	}

	dbMaxRetries := int32(DefaultJobMaxRetries)
	if maxRetriesOverride != nil {
		dbMaxRetries = int32(*maxRetriesOverride)
	}

	// The CreateJob SQLC query expects delay in seconds as text for interval conversion.
	delaySeconds := int64(delay.Seconds())

	log.Ctx(ctx).Info().Str("jobType", jobType).Bytes("payloadBytes", payloadBytes).Int64("delaySeconds", delaySeconds).Msg("[JobService.EnqueueJob] Attempting s.queries.CreateJob")

	// Ensure sqlc generate was run and Column3 is correct or adjust field name
	job, err := s.queries.CreateJob(ctx, db.CreateJobParams{
		JobType:    jobType,
		Payload:    payloadBytes,
		Column3:    fmt.Sprintf("%d", delaySeconds), // Assuming sqlc named $3 -> Column3
		MaxRetries: dbMaxRetries,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("jobType", jobType).Msg("[JobService.EnqueueJob] s.queries.CreateJob failed")
		return db.Job{}, fmt.Errorf("failed to enqueue job: %w", err)
	}

	log.Ctx(ctx).Info().Str("jobID", job.ID.String()).Str("jobType", jobType).Msg("[JobService.EnqueueJob] s.queries.CreateJob successful, job enqueued")
	return job, nil
}

// --- Worker Logic ---

type JobPayloadPromoteWaitlist struct {
	EventID     uuid.UUID `json:"event_id"`
	SlotsToFill int       `json:"slots_to_fill"`
}

// --- Notification Job Payloads ---
type JobPayloadNotifyEvent struct { // Base for simple user+event notifications
	UserID  uuid.UUID `json:"user_id"`
	EventID uuid.UUID `json:"event_id"`
}

type JobPayloadNotifyRegStatusUpdate struct {
	UserID    uuid.UUID `json:"user_id"`
	EventID   uuid.UUID `json:"event_id"`
	NewStatus string    `json:"new_status"`
	// Use a value type for admin notes in the payload to avoid pointer issues across JSON marshal/unmarshal
	AdminNotes *string `json:"admin_notes,omitempty"`
}

// Added new payload structure for event reminders
type JobPayloadEventReminder struct {
	EventID uuid.UUID `json:"event_id"`
	// ReminderType could be "24h_before", "1h_before", etc. - for future flexibility
	// ReminderType string `json:"reminder_type"`
}

// StartWorker starts a worker goroutine that polls for pending jobs.
func (s *jobService) StartWorker(ctx context.Context, workerID string, interval time.Duration, eventSvc EventService, notifySvc NotificationService) {
	log.Info().Str("workerID", workerID).Dur("interval", interval).Msg("Starting Job Worker...")
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Info().Str("workerID", workerID).Msg("Job Worker stopping due to context cancellation.")
			return
		case <-ticker.C:
			s.processNextJob(ctx, workerID, eventSvc, notifySvc)
		}
	}
}

// ProcessPromoteWaitlistJob handles the promote_waitlist job type.
func (s *jobService) ProcessPromoteWaitlistJob(ctx context.Context, payloadBytes []byte, eventSvc EventService) error {
	var payload JobPayloadPromoteWaitlist
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		// This is a permanent error, the payload is malformed.
		return fmt.Errorf("failed to unmarshal promote_waitlist payload: %w", err)
	}
	// Call the actual service method. PromoteFromWaitlist handles its own logic and notifications (currently).
	_, err := eventSvc.PromoteFromWaitlist(ctx, payload.EventID, payload.SlotsToFill)
	// We only care if the service method itself errored, not the content of the response.
	return err
}

// ProcessNotifyEventPromotion handles sending promotion notifications.
func (s *jobService) ProcessNotifyEventPromotion(ctx context.Context, payloadBytes []byte, notifySvc NotificationService) error {
	var payload JobPayloadNotifyEvent
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return fmt.Errorf("failed to unmarshal notify_event_promotion payload: %w", err)
	}

	// Fetch User and Event details required by the notification service method
	user, err := s.queries.GetUserByID(ctx, payload.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user %s for notification: %w", payload.UserID, err)
	}
	event, err := s.queries.GetEventByID(ctx, payload.EventID)
	if err != nil {
		return fmt.Errorf("failed to get event %s for notification: %w", payload.EventID, err)
	}

	return notifySvc.SendEventPromotionNotification(ctx, user, event)
}

// ProcessNotifyEventCancellation handles sending cancellation notifications.
func (s *jobService) ProcessNotifyEventCancellation(ctx context.Context, payloadBytes []byte, notifySvc NotificationService) error {
	var payload JobPayloadNotifyEvent
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return fmt.Errorf("failed to unmarshal notify_event_cancellation payload: %w", err)
	}

	user, err := s.queries.GetUserByID(ctx, payload.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user %s for notification: %w", payload.UserID, err)
	}
	event, err := s.queries.GetEventByID(ctx, payload.EventID)
	if err != nil {
		return fmt.Errorf("failed to get event %s for notification: %w", payload.EventID, err)
	}

	return notifySvc.SendEventCancellationConfirmation(ctx, user, event)
}

// ProcessNotifyRegStatusUpdate handles sending registration status update notifications.
func (s *jobService) ProcessNotifyRegStatusUpdate(ctx context.Context, payloadBytes []byte, notifySvc NotificationService) error {
	var payload JobPayloadNotifyRegStatusUpdate
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return fmt.Errorf("failed to unmarshal notify_reg_status_update payload: %w", err)
	}

	user, err := s.queries.GetUserByID(ctx, payload.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user %s for notification: %w", payload.UserID, err)
	}
	event, err := s.queries.GetEventByID(ctx, payload.EventID)
	if err != nil {
		return fmt.Errorf("failed to get event %s for notification: %w", payload.EventID, err)
	}

	// Convert payload admin notes string back to *string for the service method
	var adminNotesPtr *string
	if payload.AdminNotes != nil {
		adminNotesPtr = payload.AdminNotes
	}

	return notifySvc.SendRegistrationStatusUpdate(ctx, user, event, payload.NewStatus, adminNotesPtr)
}

// ProcessEventReminderJob handles sending event reminders.
func (s *jobService) ProcessEventReminderJob(ctx context.Context, payloadBytes []byte, notifySvc NotificationService, eventSvc EventService) error {
	logger := log.Ctx(ctx)
	var payload JobPayloadEventReminder
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		logger.Error().Err(err).Msg("Failed to unmarshal event_reminder payload")
		return fmt.Errorf("failed to unmarshal event_reminder payload: %w", err) // Permanent error
	}

	logger.Info().Str("eventID", payload.EventID.String()).Msg("Processing event reminder job")

	// 1. Fetch Event Details
	// We need EventService to fetch full event details, not just from db.Queries directly
	// as EventService might have more complex logic or combined data.
	// Assuming EventService has a method like GetEventDetails(ctx, eventID) that returns a rich event object.
	// For now, let's use the simpler GetEventByID from db.Queries for basic info.
	event, err := s.queries.GetEventByID(ctx, payload.EventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Str("eventID", payload.EventID.String()).Msg("Event not found for reminder, job will be marked failed.")
			return fmt.Errorf("event %s not found for reminder: %w", payload.EventID, err) // Permanent error if event is gone
		}
		logger.Error().Err(err).Str("eventID", payload.EventID.String()).Msg("Failed to fetch event details for reminder")
		return err // Retriable error
	}

	// 2. Fetch Confirmed/Accepted Registrants for the Event
	// This requires a new query: e.g., ListConfirmedRegistrationsByEventID(ctx, eventID)
	// It should return a list of user IDs or full user objects.
	// For now, let's assume it returns []db.User or similar structure that includes user.ID and user.Phone
	// and notification preferences (EnableWhatsappNotifications, EnableSmsNotifications, PhoneOtpChannel).

	// Placeholder for the new query call - this query needs to be defined and generated.
	// registrants, err := s.queries.ListConfirmedAndAttendedUsersForEvent(ctx, payload.EventID)
	// For now, we will use a more general query and filter in code, or assume a specific query exists.
	// Let's assume a query `ListUsersForEventNotification` that gets users who should be notified.
	// usersToNotify, err := s.queries.GetUsersRegisteredForEvent(ctx, payload.EventID) // Placeholder name, needs to be specific
	// Use the newly generated query
	usersToNotify, err := s.queries.ListNotifiableUsersForEvent(ctx, payload.EventID)
	if err != nil {
		logger.Error().Err(err).Str("eventID", payload.EventID.String()).Msg("Failed to fetch registrants for event reminder")
		return err // Retriable error
	}

	if len(usersToNotify) == 0 {
		logger.Info().Str("eventID", payload.EventID.String()).Msg("No users to notify for this event reminder.")
		return nil // No error, job done
	}

	logger.Info().Str("eventID", payload.EventID.String()).Int("user_count", len(usersToNotify)).Msg("Sending reminders to users")

	// 3. For each registrant, send a notification
	var firstError error
	for _, userRow := range usersToNotify { // Assuming usersToNotify is []db.GetUsersRegisteredForEventRow
		// Construct the payload for NotificationService.SendToUser
		// The message will be constructed by SendToUser based on this payload and messageType.
		notificationData := map[string]interface{}{
			"event_id":    event.ID.String(),
			"event_title": event.Title,
			"event_time":  event.StartTime.Format(time.RFC1123), // Or a more user-friendly format
			// "event_location": event.Location, // If available and needed
			// Add a placeholder for message that SendToUser can use for WebSocket if necessary
			"message": fmt.Sprintf("Reminder: Your event '%s' is starting soon at %s.", event.Title, event.StartTime.Format(time.RFC1123)),
		}

		// UserID is directly from the userRow, e.g., userRow.UserID
		// We need to ensure userRow (from GetUsersRegisteredForEventRow) contains the UserID
		// Let's assume the row struct has a UserID field: userRow.UserID
		// If GetUsersRegisteredForEventRow returns the full user object, we can use userRow.ID
		// For now, assuming userRow has a .ID field of type uuid.UUID for the user's ID.
		// This depends on the actual structure of db.GetUsersRegisteredForEventRow
		// If userRow is a specific SQLC row type like db.GetUsersRegisteredForEventRow, it might be userRow.UserID
		// Let's assume the SQLC generated struct is `db.GetUsersRegisteredForEventRow` and it has `UserID uuid.UUID`
		// currentUserID = userRow.UserID // This line needs to match the actual SQLC generated struct field name

		// The generated struct db.ListNotifiableUsersForEventRow has UserID uuid.UUID due to the alias in SQL
		currentUserID := userRow.UserID

		if err := notifySvc.SendToUser(ctx, currentUserID, JobTypeEventReminder, notificationData); err != nil {
			logger.Error().Err(err).Str("userID", currentUserID.String()).Str("eventID", payload.EventID.String()).Msg("Failed to send event reminder to user")
			if firstError == nil {
				firstError = err // Store the first error encountered
			}
			// Continue trying to notify other users
		}
	}

	return firstError // Return the first error if any occurred, or nil if all successful
}

// processNextJob attempts to fetch and process one job.
func (s *jobService) processNextJob(ctx context.Context, workerID string, eventSvc EventService, notifySvc NotificationService) {
	var jobRow db.GetNextPendingJobRow
	var jobID uuid.UUID

	tx, err := s.pool.Begin(ctx)
	if err != nil {
		log.Error().Err(err).Str("workerID", workerID).Msg("[JobService.processNextJob] Worker failed to begin transaction")
		return // Exit processing attempt
	}
	defer tx.Rollback(ctx) // Rollback unless committed

	qtx := s.queries.WithTx(tx)

	// Get next job and lock it
	jobRow, err = qtx.GetNextPendingJob(ctx)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// No pending jobs, normal exit - do not log as error
			return
		}
		log.Error().Err(err).Str("workerID", workerID).Msg("[JobService.processNextJob] Worker failed to get next pending job")
		return // Exit processing attempt
	}
	jobID = jobRow.ID

	// Mark job as running within the same transaction
	_, err = qtx.MarkJobRunning(ctx, jobID)
	if err != nil {
		// This could happen if another worker grabbed it between GetNextPendingJob and MarkJobRunning despite SKIP LOCKED,
		// or if the job status somehow changed. Log and exit, letting rollback handle it.
		log.Warn().Err(err).Str("workerID", workerID).Str("jobID", jobID.String()).Msg("[JobService.processNextJob] Worker failed to mark job as running, likely contention or status change")
		return
	}

	// Commit the transaction to release the lock quickly
	if err = tx.Commit(ctx); err != nil {
		log.Error().Err(err).Str("workerID", workerID).Str("jobID", jobID.String()).Msg("[JobService.processNextJob] Worker failed to commit transaction after marking job running")
		return // Exit processing attempt
	}

	// Process the job (outside the lock transaction)
	jobCtx := log.Ctx(ctx).With().Str("jobID", jobID.String()).Str("jobType", jobRow.JobType).Str("workerID", workerID).Logger().WithContext(context.Background())
	log.Ctx(jobCtx).Info().Bytes("payload", jobRow.Payload).Msg("[JobService.processNextJob] Processing job")

	var processingErr error
	switch jobRow.JobType {
	case JobTypePromoteWaitlist:
		processingErr = s.ProcessPromoteWaitlistJob(jobCtx, jobRow.Payload, eventSvc)
	case JobTypeNotifyEventPromotion:
		processingErr = s.ProcessNotifyEventPromotion(jobCtx, jobRow.Payload, notifySvc)
	case JobTypeNotifyEventCancellation:
		processingErr = s.ProcessNotifyEventCancellation(jobCtx, jobRow.Payload, notifySvc)
	case JobTypeNotifyRegStatusUpdate:
		processingErr = s.ProcessNotifyRegStatusUpdate(jobCtx, jobRow.Payload, notifySvc)
	case JobTypeEventReminder:
		processingErr = s.ProcessEventReminderJob(jobCtx, jobRow.Payload, notifySvc, eventSvc)
	default:
		processingErr = fmt.Errorf("unknown job type: %s", jobRow.JobType)
		// Treat unknown job type as a permanent failure
		log.Ctx(jobCtx).Error().Err(processingErr).Msg("[JobService.processNextJob] Unknown job type encountered")
		jobRow.Attempts = jobRow.MaxRetries // Force failure handling by setting attempts >= max_retries
	}

	// Update job status based on processing result (using a background context for updates)
	updateCtx := context.Background()
	if processingErr != nil {
		log.Ctx(jobCtx).Error().Err(processingErr).Int32("attempts", jobRow.Attempts+1).Msg("[JobService.processNextJob] Job processing failed")
		if jobRow.Attempts+1 >= jobRow.MaxRetries {
			// Mark as permanently failed
			errMsg := processingErr.Error()
			_, updateErr := s.queries.UpdateJobStatus(updateCtx, db.UpdateJobStatusParams{
				ID:        jobID,
				Status:    db.JobStatusFailed,
				LastError: &errMsg,
			})
			if updateErr != nil {
				log.Ctx(jobCtx).Error().Err(updateErr).Msg("[JobService.processNextJob] Failed to mark job as permanently failed")
			}
		} else {
			// Schedule for retry with exponential backoff
			retryDelaySeconds := int64(math.Pow(2, float64(jobRow.Attempts+1)) * 5) // Example: 10s, 20s, 40s...
			errMsg := processingErr.Error()
			// Ensure the field name here matches the sqlc generated param struct for RetryJob query ($3)
			_, retryErr := s.queries.RetryJob(updateCtx, db.RetryJobParams{
				ID:        jobID,
				LastError: &errMsg,
				Column3:   fmt.Sprintf("%d", retryDelaySeconds), // Assuming $3 maps to Column3
			})
			if retryErr != nil {
				log.Ctx(jobCtx).Error().Err(retryErr).Msg("[JobService.processNextJob] Failed to schedule job for retry")
			}
		}
	} else {
		// Mark as completed
		log.Ctx(jobCtx).Info().Msg("[JobService.processNextJob] Job processed successfully")
		_, updateErr := s.queries.UpdateJobStatus(updateCtx, db.UpdateJobStatusParams{
			ID:     jobID,
			Status: db.JobStatusCompleted,
			// LastError should be set to NULL or omitted if using COALESCE
			LastError: nil, // Explicitly set LastError to nil on success
		})
		if updateErr != nil {
			log.Ctx(jobCtx).Error().Err(updateErr).Msg("[JobService.processNextJob] Failed to mark job as completed")
		}
	}
}
