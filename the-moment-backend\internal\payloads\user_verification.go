package payloads

import (
	"reflect"
	"time"

	"Membership-SAAS-System-Backend/db" // For db.VerificationTypeEnum etc.

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

// --- Request Structs ---

// SubmitVerificationRequest is a common wrapper for all verification submissions.
// The document itself will be handled as a multipart file.
// The specific data fields are expected to be sent as form data alongside the file.
type SubmitVerificationRequest struct {
	VerificationType string `form:"verification_type" validate:"required,verification_type_enum"` // Using string here, will convert to db.VerificationTypeEnum
	// We expect individual form fields for each type, not nested JSON within the form.
	// Example for HKID:
	// form field "chinese_name"
	// form field "english_name"
	// ... etc.
	// The handler will parse these based on VerificationType.
	// File will be accessed via c.FormFile("document")
}

// For binding specific data from form, used by the handler internally after checking VerificationType.
type HKIDCardPayload struct {
	ChineseName           string `form:"chinese_name"`
	ChineseCommercialCode string `form:"chinese_commercial_code,omitempty"`
	EnglishName           string `form:"english_name" validate:"required"`
	Sex                   string `form:"sex" validate:"required"`
	DateOfBirth           string `form:"date_of_birth" validate:"required,isodate"` // e.g., "YYYY-MM-DD"
	HKIDNumber            string `form:"hk_id_number" validate:"required"`
	IsPermanentResident   *bool  `form:"is_permanent_resident" validate:"required"` // Pointer for explicit true/false
}

type MainlandIDPayload struct {
	ChineseName      string `form:"chinese_name" validate:"required"`
	Sex              string `form:"sex" validate:"required"`
	DateOfBirth      string `form:"date_of_birth" validate:"required,isodate"`
	MainlandIDNumber string `form:"mainland_id_number" validate:"required"`
	ValidUntil       string `form:"valid_until,omitempty" validate:"omitempty,isodate"`
}

type MainlandTravelPermitPayload struct {
	PermitNumber string `form:"permit_number" validate:"required"`
	IssueDate    string `form:"issue_date,omitempty" validate:"omitempty,isodate"`
	ExpiryDate   string `form:"expiry_date" validate:"required,isodate"`
}

type PassportPayload struct {
	PassportNumber string `form:"passport_number" validate:"required"`
	IssuingCountry string `form:"issuing_country" validate:"required"`
	IssueDate      string `form:"issue_date,omitempty" validate:"omitempty,isodate"`
	ExpiryDate     string `form:"expiry_date" validate:"required,isodate"`
}

type HKYouthPlusPayload struct {
	MemberNumber string `form:"member_number" validate:"required"`
}

type AddressProofPayload struct {
	FullAddress string `form:"full_address" validate:"required"`
}

type StudentIDPayload struct {
	SchoolName string `form:"school_name" validate:"required"`
	Grade      string `form:"grade" validate:"required"`
	ExpiryDate string `form:"expiry_date" validate:"required,isodate"`
}

type HomeVisitPayload struct {
	Notes *string `form:"notes"` // Notes for the home visit
}

type AdminReviewVerificationRequest struct {
	Status     string  `json:"status" validate:"required,admin_review_status_enum"` // "approved" or "rejected"
	AdminNotes *string `json:"admin_notes,omitempty"`
}

// ReviewVerificationRequestInput is for PATCH /admin/verifications/{reqID}/review
type ReviewVerificationRequestInput struct {
	Status     db.VerificationStatusEnum `json:"status" validate:"required,oneof=approved rejected"`
	AdminNotes *string                   `json:"admin_notes,omitempty" validate:"omitempty,max=500"`
}

// --- Response Structs ---

type VerificationRequestResponse struct {
	RequestID        uuid.UUID                 `json:"request_id"`
	UserID           uuid.UUID                 `json:"user_id"`
	VerificationType db.VerificationTypeEnum   `json:"verification_type"`
	Status           db.VerificationStatusEnum `json:"status"`
	DocumentID       *uuid.UUID                `json:"document_id,omitempty"`
	FileName         *string                   `json:"file_name,omitempty"` // From joined verification_documents
	MimeType         *string                   `json:"mime_type,omitempty"` // From joined verification_documents
	SubmittedAt      time.Time                 `json:"submitted_at"`
	ReviewedAt       *time.Time                `json:"reviewed_at,omitempty"`
	ReviewedByUserID *uuid.UUID                `json:"reviewed_by_user_id,omitempty"`
	AdminNotes       *string                   `json:"admin_notes,omitempty"`
	CreatedAt        time.Time                 `json:"created_at"`
	UpdatedAt        time.Time                 `json:"updated_at"`
	UserDisplayName  *string                   `json:"user_display_name,omitempty"` // For admin views (from users table)
	UserEmail        *string                   `json:"user_email,omitempty"`        // For admin views (from users table)
	ReviewerEmail    *string                   `json:"reviewer_email,omitempty"`    // For admin views (from users table)

	// Specific data (populated based on verification_type)
	HKIDCardData             *db.VerificationHkIDCard             `json:"hk_id_card_data,omitempty"`
	MainlandIDData           *db.VerificationMainlandChinaIDCard  `json:"mainland_china_id_card_data,omitempty"`
	MainlandTravelPermitData *db.VerificationMainlandTravelPermit `json:"mainland_travel_permit_data,omitempty"`
	PassportData             *db.VerificationPassport             `json:"passport_data,omitempty"`
	HKYouthPlusData          *db.VerificationHkYouthPlu           `json:"hk_youth_plus_data,omitempty"`
	AddressProofData         *db.VerificationAddressProof         `json:"address_proof_data,omitempty"`
	StudentIDData            *db.VerificationStudentID            `json:"student_id_data,omitempty"`
	HomeVisitData            *db.VerificationHomeVisit            `json:"home_visit_data,omitempty"`
}

// Specific data structures for each verification type (for responses)

type HKIDCardData struct {
	ID                    uuid.UUID `json:"id"`
	ChineseName           *string   `json:"chinese_name,omitempty"`
	ChineseCommercialCode *string   `json:"chinese_commercial_code,omitempty"`
	EnglishName           *string   `json:"english_name,omitempty"`
	Sex                   *string   `json:"sex,omitempty"`
	DateOfBirth           string    `json:"date_of_birth,omitempty"` // Changed from time.Time
	HkIDNumber            *string   `json:"hk_id_number,omitempty"`
	IsPermanentResident   *bool     `json:"is_permanent_resident,omitempty"`
}

type MainlandIDData struct {
	ID               uuid.UUID `json:"id"`
	ChineseName      *string   `json:"chinese_name,omitempty"`
	Sex              *string   `json:"sex,omitempty"`
	DateOfBirth      string    `json:"date_of_birth,omitempty"` // Changed from time.Time
	MainlandIDNumber *string   `json:"mainland_id_number,omitempty"`
	ValidUntil       string    `json:"valid_until,omitempty"` // Changed from time.Time
}

type HomePermitData struct {
	ID           uuid.UUID `json:"id"`
	PermitNumber *string   `json:"permit_number,omitempty"`
	IssueDate    string    `json:"issue_date,omitempty"`  // Changed from time.Time
	ExpiryDate   string    `json:"expiry_date,omitempty"` // Changed from time.Time
}

type PassportData struct {
	ID             uuid.UUID `json:"id"`
	PassportNumber *string   `json:"passport_number,omitempty"`
	IssuingCountry *string   `json:"issuing_country,omitempty"`
	IssueDate      string    `json:"issue_date,omitempty"`  // Changed from time.Time
	ExpiryDate     string    `json:"expiry_date,omitempty"` // Changed from time.Time
}

type StudentIDData struct {
	ID         uuid.UUID `json:"id"`
	SchoolName *string   `json:"school_name,omitempty"`
	Grade      *string   `json:"grade,omitempty"`
	ExpiryDate string    `json:"expiry_date,omitempty"` // Changed from time.Time
}

// --- Custom Validators ---

// IsISO8601Date validates if the string is a "YYYY-MM-DD" date.
func IsISO8601Date(fl validator.FieldLevel) bool {
	if fl.Field().Kind() != reflect.String {
		return false
	}
	_, err := time.Parse("2006-01-02", fl.Field().String())
	return err == nil
}

// ValidateVerificationTypeEnum checks if the string is a valid db.VerificationTypeEnum.
func ValidateVerificationTypeEnum(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	switch value {
	case string(db.VerificationTypeEnumHkIDCard),
		string(db.VerificationTypeEnumMainlandChinaIDCard),
		string(db.VerificationTypeEnumMainlandTravelPermit),
		string(db.VerificationTypeEnumPassport),
		string(db.VerificationTypeEnumHkYouthPlus),
		string(db.VerificationTypeEnumAddressProof),
		string(db.VerificationTypeEnumStudentID),
		string(db.VerificationTypeEnumHomeVisit):
		return true
	default:
		return false
	}
}

// ValidateAdminReviewStatusEnum checks if the string is a valid admin review status.
func ValidateAdminReviewStatusEnum(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	switch value {
	case string(db.VerificationStatusEnumApproved),
		string(db.VerificationStatusEnumPending),
		string(db.VerificationStatusEnumRejected):
		return true
	default:
		return false
	}
}

// Utility to get db.VerificationTypeEnum from string, returns error if invalid
func GetVerificationTypeEnumFromString(s string) (db.VerificationTypeEnum, error) {
	val := db.VerificationTypeEnum(s)
	if !val.Valid() { // Assuming sqlc generates Valid() method for enums
		return "", &IllegalArgumentError{Message: "Invalid verification type: " + s}
	}
	return val, nil
}

// Utility to get db.VerificationStatusEnum from string for admin review, returns error if invalid
func GetAdminReviewStatusEnumFromString(s string) (db.VerificationStatusEnum, error) {
	val := db.VerificationStatusEnum(s)
	switch val {
	case db.VerificationStatusEnumApproved, db.VerificationStatusEnumPending, db.VerificationStatusEnumRejected:
		if !val.Valid() { // Double check validity
			return "", &IllegalArgumentError{Message: "Invalid admin review status (internal check failed): " + s}
		}
		return val, nil
	default:
		return "", &IllegalArgumentError{Message: "Invalid admin review status: " + s}
	}
}
