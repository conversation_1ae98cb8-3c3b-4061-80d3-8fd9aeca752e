# THE Moment Documentation

---

## Project Overview

### For Normal Users
THE Moment is a platform that allows you to join organizations, register for and attend events, read and interact with posts, access resources, and manage your profile and verification status. You can use the mobile app or web portal to:
- Register/login using your phone (WhatsApp OTP; SMS is available for login, but disabled as client's request. The code remains for future use.)
- Join organizations and participate in their activities
- Register for events, check in (no check-out), and view your event history
- Read posts and download resources
- Apply for volunteer roles and submit verification documents
- Join or cancel volunteer applications
- Change your phone number
- Manage your profile, language, and notification preferences

### For Admins
Admins manage organizations, events, posts, resources, and user verifications. Admins can:
- Approve/reject user verification and volunteer applications
- Create and manage events, posts, and resources for their organization
- View analytics and reports
- Manage organization members and roles

### For Super Admins
Super Admins have all admin privileges across all organizations. They can:
- Manage all organizations, users, and admins
- Access platform-wide analytics and reports
- Oversee all content and user management

---

## Features Documentation (by Page)

### Mobile App (Membership-SAAS-System-Front-End)

#### Dashboard
- **Features:**
  - Shows featured events, posts, and organizations
  - Quick access to event registration, post creation, and organization switch
- **Variables Displayed:**
  - List of featured events (title, date, location, image)
  - List of featured posts (title, summary, image)
  - Current organization (name, logo)
  - Quick action buttons
- **Logic Flow:**
  1. On load, fetch dashboard events, posts, and organizations
  2. Display lists and allow navigation to details
  3. User can switch organization or tap quick actions

#### Explore
- **Overview:**
  - The Explore section is divided into three independent pages: Events, Posts, and Resources.

##### Explore Events
- **Features:**
  - Browse/search upcoming and past events
  - Register for events
  - View event details
- **Variables Displayed:**
  - Search bar, filter options (date, tag, organization)
  - List of events (title, date, location, status, image)
- **Logic Flow:**
  1. User enters search/filter criteria
  2. Fetch and display matching events
  3. Tap event to view details or register

##### Explore Posts
- **Features:**
  - Browse/search posts
  - View post details
- **Variables Displayed:**
  - Search bar, filter options (tag, organization)
  - List of posts (title, summary, date, image)
- **Logic Flow:**
  1. User enters search/filter criteria
  2. Fetch and display matching posts
  3. Tap post to view details

##### Explore Resources
- **Features:**
  - Browse/search downloadable resources
  - View/download resource details
- **Variables Displayed:**
  - Search bar, filter options (type, organization)
  - List of resources (title, summary, date, file type)
- **Logic Flow:**
  1. User enters search/filter criteria
  2. Fetch and display matching resources
  3. Tap resource to view/download

#### My Events
- **Features:**
  - View registered and past events
  - Cancel registration
- **Variables Displayed:**
  - List of upcoming/past events (title, date, status)
- **Logic Flow:**
  1. Fetch user's event registrations
  2. Display events with status and actions
  3. Allow cancellation

#### Profile
- **Features:**
  - View and edit profile info
  - Manage organizations, verification, and volunteer applications
  - Change phone number
- **Variables Displayed:**
  - User info (name, phone, avatar)
  - Verification status
  - Organization memberships
  - Volunteer application status (join/cancel)
- **Logic Flow:**
  1. Fetch user profile and related data
  2. Display info and allow edits or navigation to subpages
  3. For volunteer: show join/cancel button and status
  4. For change phone: user enters new phone, receives OTP, verifies, and updates phone

#### QR Code
- **Features:**
  - Show QR code for event check-in (no check-out)
- **Variables Displayed:**
  - User's event QR code
- **Logic Flow:**
  1. Generate and display QR code for current event
  2. Staff scans QR code to check in

#### Login/Phone Verification
- **Features:**
  - Login/register with phone (WhatsApp OTP; SMS available but disabled)
- **Variables Displayed:**
  - Phone input, OTP input, display name (for new users), error messages
- **Logic Flow:**
  1. User enters phone number
  2. App checks if user exists
  3. Initiate OTP (WhatsApp preferred, SMS code remains for future)
  4. User enters OTP
  5. Verify and log in or register

---

### Web App (Membership-SAAS-System-WebApp)

#### Home
- **Features:**
  - Dashboard with latest posts/events for selected organization
- **Variables Displayed:**
  - Organization info (name, logo)
  - List of latest posts/events (title, date, image)
- **Logic Flow:**
  1. On load, fetch organization, posts, and events
  2. Display lists and allow navigation

#### Login
- **Features:**
  - Login with phone (WhatsApp OTP; SMS available but disabled) or staff email/password
- **Variables Displayed:**
  - Phone/email input, OTP/password input, error messages
- **Logic Flow:**
  1. User selects login method
  2. For phone: enter number, receive OTP, verify
  3. For staff: enter email/password, PKCE challenge, verify
  4. On success, redirect to home

#### User Profile
- **Features:**
  - View stats, edit info, upload avatar, see participation charts
  - Change phone number
- **Variables Displayed:**
  - User info (name, phone, avatar)
  - Participation stats (charts, event counts)
- **Logic Flow:**
  1. Fetch user profile and stats
  2. Display info and charts
  3. Allow edits and navigation
  4. For change phone: user enters new phone, receives OTP, verifies, and updates phone

#### Events
- **Features:**
  - List, view, register, and manage events
- **Variables Displayed:**
  - List of events (title, date, status, image)
  - Event details (full info, media, actions)
- **Logic Flow:**
  1. Fetch and display list
  2. Allow search/filter
  3. View details or register

#### Posts
- **Features:**
  - List, create, and view posts
- **Variables Displayed:**
  - List of posts (title, date, status, image)
  - Post details (full info, media, actions)
- **Logic Flow:**
  1. Fetch and display list
  2. Allow search/filter
  3. View details or create post

#### Resources
- **Features:**
  - List, upload, and download resources
- **Variables Displayed:**
  - List of resources (title, date, file type)
  - Resource details (full info, download link)
- **Logic Flow:**
  1. Fetch and display list
  2. Allow search/filter
  3. View details or upload/download

#### Admin Pages (Management)
Each admin-only page is described below:

##### User Management
- **Features:**
  - List, view, create, edit, and delete users
- **Logic Flow:**
  1. Fetch user list
  2. View user details
  3. Create, edit, or delete users

##### Event Management
- **Features:**
  - List, create, edit, and delete events
  - View event registrations and statistics
- **Logic Flow:**
  1. Fetch event list
  2. View event details and registrations
  3. Create, edit, or delete events

##### Post Management
- **Features:**
  - List, create, edit, and delete posts
- **Logic Flow:**
  1. Fetch post list
  2. View post details
  3. Create, edit, or delete posts

##### Resource Management
- **Features:**
  - List, upload, edit, and delete resources
- **Logic Flow:**
  1. Fetch resource list
  2. View resource details
  3. Upload, edit, or delete resources

##### Volunteer Management
- **Features:**
  - Review volunteer applications (approve/reject)
  - View volunteer status
- **Logic Flow:**
  1. Fetch volunteer applications
  2. Approve or reject applications
  3. View volunteer status

##### Verification Management
- **Features:**
  - Review user verification requests (approve/reject)
- **Logic Flow:**
  1. Fetch verification requests
  2. Approve or reject requests

##### Reports/Analytics
- **Features:**
  - View analytics and reports for events, users, and trends
- **Logic Flow:**
  1. Fetch and display analytics data

---

## Backend API Definition (Key Endpoints)

Below, each endpoint includes the HTTP method, path, whether JWT access_token is required, and the request/response payloads with parameter types and expected input.

### Authentication
- `POST /authn/phone/check` — Check if phone exists
  - **JWT Required:** No
  - **Request:**
    - `phone` (string, required): E.164 formatted phone number, e.g. "+85212345678"
  - **Response:**
    - `exists` (boolean): true if the phone is registered
- `POST /authn/phone/otp/initiate` — Start OTP login (WhatsApp/SMS)
  - **JWT Required:** No
  - **Request:**
    - `phone` (string, required): E.164 formatted phone number
    - `code_challenge` (string, required): PKCE code challenge
    - `state` (string, required): Unique state string for CSRF protection
    - `phone_otp_channel` (string, optional): "whatsapp" or "sms"
  - **Response:**
    - `state` (string): Echoed state value
- `POST /authn/phone/otp/verify` — Verify OTP and login
  - **JWT Required:** No
  - **Request:**
    - `phone` (string, required): E.164 formatted phone number
    - `otp` (string, required): 6-digit OTP code
    - `code_verifier` (string, required): PKCE code verifier
    - `state` (string, required): State string from initiation
  - **Response:**
    - `access_token` (string): JWT access token
    - `refresh_token` (string): JWT refresh token
- `POST /authn/register/phone/initiate` — Start registration
  - **JWT Required:** No
  - **Request:**
    - `phone` (string, required): E.164 formatted phone number
    - `code_challenge` (string, required): PKCE code challenge
    - `state` (string, required): Unique state string
    - `phone_otp_channel` (string, optional): "whatsapp" or "sms"
  - **Response:**
    - `state` (string): Echoed state value
- `POST /authn/register/phone/verify` — Complete registration
  - **JWT Required:** No
  - **Request:**
    - `phone` (string, required): E.164 formatted phone number
    - `otp` (string, required): 6-digit OTP code
    - `display_name` (string, required): User's display name
    - `code_verifier` (string, required): PKCE code verifier
    - `state` (string, required): State string from initiation
  - **Response:**
    - `access_token` (string): JWT access token
    - `refresh_token` (string): JWT refresh token
- `POST /authn/staff/login/initiate` — Staff login (PKCE)
  - **JWT Required:** No
  - **Request:**
    - `email` (string, required): Staff email address
    - `code_challenge` (string, required): PKCE code challenge
    - `state` (string, required): Unique state string
  - **Response:**
    - `state` (string): Echoed state value
- `POST /authn/staff/login/verify` — Staff login verify
  - **JWT Required:** No
  - **Request:**
    - `email` (string, required): Staff email address
    - `password` (string, required): Staff password
    - `code_verifier` (string, required): PKCE code verifier
    - `state` (string, required): State string from initiation
  - **Response:**
    - `access_token` (string): JWT access token
    - `refresh_token` (string): JWT refresh token
- `POST /authn/logout` — Logout
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:**
    - `success` (boolean): true if logout successful
- `POST /authn/token/refresh` — Refresh JWT token
  - **JWT Required:** No (uses refresh_token)
  - **Request:**
    - `refresh_token` (string, required): JWT refresh token
  - **Response:**
    - `access_token` (string): New JWT access token

### Users
- `GET /admin/users` — List users
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:** Array of user objects:
    - `id` (string): User ID
    - `display_name` (string): User's display name
    - ... (other user fields)
- `GET /admin/users/{userId}` — Get user details
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:**
    - `id` (string): User ID
    - `display_name` (string): User's display name
    - ... (other user fields)
- `POST /admin/users` — Create user
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `display_name` (string, required): User's display name
    - `email` (string, required): User's email
    - ... (other fields as needed)
  - **Response:**
    - `id` (string): User ID
    - `display_name` (string): User's display name
    - ... (other user fields)
- `PATCH /admin/users/{userId}` — Update user
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `display_name` (string, optional): User's display name
    - ... (other updatable fields)
  - **Response:**
    - `id` (string): User ID
    - `display_name` (string): User's display name
    - ... (other user fields)
- `DELETE /admin/users/{userId}` — Delete user
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:**
    - `success` (boolean): true if deletion successful

### Organizations
- `GET /organizations` — List organizations
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:** Array of organization objects:
    - `id` (string): Organization ID
    - `name` (string): Organization name
    - ... (other org fields)
- `POST /organizations` — Create organization
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `name` (string, required): Organization name
    - `owner_user_id` (string, required): User ID of owner
    - ... (other fields as needed)
  - **Response:**
    - `id` (string): Organization ID
    - `name` (string): Organization name
    - ... (other org fields)
- `PATCH /organizations/{orgId}` — Update organization
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `name` (string, optional): Organization name
    - ... (other updatable fields)
  - **Response:**
    - `id` (string): Organization ID
    - `name` (string): Organization name
    - ... (other org fields)
- `DELETE /organizations/{orgId}` — Delete organization
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:**
    - `success` (boolean): true if deletion successful
- `POST /organizations/{orgId}/join` — Join organization
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:**
    - `success` (boolean): true if join successful
- `POST /organizations/{orgId}/leave` — Leave organization
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:**
    - `success` (boolean): true if leave successful
- `POST /organizations/{orgId}/logo` — Upload org logo
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `file` (file, required): Image file (multipart/form-data)
  - **Response:**
    - `image_url` (string): URL of uploaded logo

### Events
- `GET /events` — List events
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:** Array of event objects:
    - `id` (string): Event ID
    - `title` (string): Event title
    - ... (other event fields)
- `POST /events` — Create event
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `title` (string, required): Event title
    - `start_time` (string, required): ISO8601 date-time
    - ... (other fields as needed)
  - **Response:**
    - `id` (string): Event ID
    - `title` (string): Event title
    - ... (other event fields)
- `GET /events/{eventId}` — Get event details
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:**
    - `id` (string): Event ID
    - `title` (string): Event title
    - ... (other event fields)
- `PUT /events/{eventId}` — Update event
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `title` (string, optional): Event title
    - ... (other updatable fields)
  - **Response:**
    - `id` (string): Event ID
    - `title` (string): Event title
    - ... (other event fields)
- `DELETE /events/{eventId}` — Delete event
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:**
    - `success` (boolean): true if deletion successful
- `POST /event-registrations` — Register for event
  - **JWT Required:** Yes
  - **Request:**
    - `event_id` (string, required): Event ID
  - **Response:**
    - `registration_id` (string): Registration ID
    - `status` (string): "registered"
- `POST /event-registrations/check-in` — Check-in to event
  - **JWT Required:** Yes
  - **Request:**
    - `registration_id` (string, required): Registration ID
  - **Response:**
    - `success` (boolean): true if check-in successful
- `GET /event-registrations/me` — Get my event registrations
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:** Array of registration objects:
    - `event_id` (string): Event ID
    - `status` (string): Registration status
    - ... (other fields)

### Posts
- `GET /posts` — List posts
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:** Array of post objects:
    - `id` (string): Post ID
    - `title` (string): Post title
    - ... (other post fields)
- `POST /posts` — Create post
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `title` (string, required): Post title
    - `content` (string, required): Post content
    - ... (other fields as needed)
  - **Response:**
    - `id` (string): Post ID
    - `title` (string): Post title
    - ... (other post fields)
- `GET /posts/{postId}` — Get post details
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:**
    - `id` (string): Post ID
    - `title` (string): Post title
    - ... (other post fields)
- `PUT /posts/{postId}` — Update post
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `title` (string, optional): Post title
    - `content` (string, optional): Post content
    - ... (other updatable fields)
  - **Response:**
    - `id` (string): Post ID
    - `title` (string): Post title
    - ... (other post fields)
- `DELETE /posts/{postId}` — Delete post
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:**
    - `success` (boolean): true if deletion successful

### Resources
- `GET /resources` — List resources
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:** Array of resource objects:
    - `id` (string): Resource ID
    - `title` (string): Resource title
    - ... (other resource fields)
- `POST /resources` — Create resource
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `title` (string, required): Resource title
    - `file` (file, required): Resource file (multipart/form-data)
    - ... (other fields as needed)
  - **Response:**
    - `id` (string): Resource ID
    - `title` (string): Resource title
    - ... (other resource fields)
- `GET /resources/{resourceId}` — Get resource details
  - **JWT Required:** Yes
  - **Request:** None
  - **Response:**
    - `id` (string): Resource ID
    - `title` (string): Resource title
    - ... (other resource fields)
- `PUT /resources/{resourceId}` — Update resource
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `title` (string, optional): Resource title
    - ... (other updatable fields)
  - **Response:**
    - `id` (string): Resource ID
    - `title` (string): Resource title
    - ... (other resource fields)
- `DELETE /resources/{resourceId}` — Delete resource
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:**
    - `success` (boolean): true if deletion successful

### Verification
- `POST /admin/verifications` — Submit verification
  - **JWT Required:** Yes
  - **Request:**
    - `type` (string, required): Verification type (e.g., "address_proof")
    - `file` (file, required): Document file (multipart/form-data)
  - **Response:**
    - `id` (string): Verification request ID
    - `status` (string): "pending", "approved", or "rejected"
- `PATCH /admin/verifications/{reqID}/review` — Admin review
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `status` (string, required): "approved" or "rejected"
    - `admin_notes` (string, optional): Notes from admin
  - **Response:**
    - `id` (string): Verification request ID
    - `status` (string): New status
- `GET /admin/verifications` — List verifications
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:** Array of verification objects:
    - `id` (string): Verification request ID
    - `status` (string): Status
    - ... (other fields)
- `GET /admin/verifications/{reqID}` — Get verification details
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:**
    - `id` (string): Verification request ID
    - `status` (string): Status
    - ... (other fields)

### Volunteers
- `POST /organizations/{orgId}/volunteer/applications` — Apply as volunteer
  - **JWT Required:** Yes
  - **Request:**
    - `motivation` (string, required): Reason for applying
  - **Response:**
    - `application_id` (string): Application ID
    - `status` (string): "pending", "approved", or "rejected"
- `GET /organizations/{orgId}/volunteer/applications` — List volunteer applications
  - **JWT Required:** Yes (admin)
  - **Request:** None
  - **Response:** Array of application objects:
    - `application_id` (string): Application ID
    - `status` (string): Status
    - ... (other fields)
- `PATCH /organizations/{orgId}/volunteer/applications/{appId}/review` — Admin review
  - **JWT Required:** Yes (admin)
  - **Request:**
    - `status` (string, required): "approved" or "rejected"
    - `admin_notes` (string, optional): Notes from admin
  - **Response:**
    - `application_id` (string): Application ID
    - `status` (string): New status

---

## User Guide (Manual)

### For Normal Users

#### Getting Started
1. **Download the mobile app** or visit the web portal.
2. **Register/Login:**
   - Enter your phone number and choose WhatsApp for OTP (SMS is available for login, but disabled as client's request).
   - Enter the OTP you receive to log in or register.
3. **Set up your profile:**
   - Add your name, upload a profile picture, and join organizations.

#### Using the App
- **Join Organizations:**
  - Browse available organizations and join those you are interested in.
- **Register for Events:**
  - View upcoming events, register, and check in using your QR code (no check-out).
- **Read Posts and Download Resources:**
  - Access posts and resources shared by your organizations.
- **Apply for Volunteer Roles:**
  - Submit applications to become a volunteer for organizations. You can also cancel your application if needed.
- **Submit Verification Documents:**
  - Upload required documents for identity or address verification.
- **Manage Your Profile:**
  - Edit your info, change your phone number (enter new phone, receive OTP, verify), and set notification/language preferences.

### For Admins

#### Getting Started
1. **Login** using your staff email/password or phone (if enabled for staff).
2. **Access the Admin Dashboard** from the web portal.

#### Managing Your Organization
- **User Management:**
  - View, create, edit, and delete users in your organization.
- **Event Management:**
  - Create, edit, and delete events. View registrations and statistics.
- **Post Management:**
  - Create, edit, and delete posts for your organization.
- **Resource Management:**
  - Upload, edit, and delete resources.
- **Volunteer Management:**
  - Review volunteer applications, approve or reject them, and view volunteer status.
- **Verification Management:**
  - Review user verification requests and approve or reject them.
- **Reports/Analytics:**
  - View analytics and reports for your organization's activities.
- **Change Phone Number:**
  - Update your phone number by entering a new number, receiving OTP, and verifying.

### For Super Admins

#### Getting Started
1. **Login** using your super admin credentials.
2. **Access the Super Admin Dashboard** from the web portal.

#### Managing the Platform
- **Organization Management:**
  - View, create, edit, and delete any organization.
- **User/Admin Management:**
  - Manage all users and admins across organizations.
- **Content Oversight:**
  - Oversee all events, posts, and resources platform-wide.
- **Volunteer and Verification Oversight:**
  - Review all volunteer and verification applications across organizations.
- **Platform Analytics:**
  - View platform-wide reports and analytics.
- **Change Phone Number:**
  - Update your phone number by entering a new number, receiving OTP, and verifying.

### FAQ
- **Why can't I use SMS for login?**
  - The SMS method is currently disabled for users, but may be enabled in the future. Please use WhatsApp for OTP.
- **How do I check in to an event?**
  - Go to your event page and show your QR code at the event entrance. There is no check-out function.
- **How do I become a volunteer?**
  - Apply through your profile or the event page. You can also cancel your application. Admins will review your application.
- **How do I change my phone number?**
  - Go to your profile, select change phone, enter your new number, receive OTP, and verify.

---

*For more help, contact your organization admin or support.*

---

## Client Update Requirements
**Web App**
**Super Admin Role**
1. Manual Register User Account
  1. Input a phone number
  2. WhatsApp OTP verification
  3. Successfully Registered
2. Manual Event Registration
  1. Go into Events Details Page
  2. Click into Manual Event Registration Page
  3. Select a user by phone number or user name
  4. Click submit
  5. Send API request to join an event
  6. Reject or Completed
3. Manual Events Participant Control
  1. Click into the events details page
  2. Click into the joiner list
  3. click on check-in or cancel button
  4. Send API request to check-in or withdraw an event
4. Admin Account Management
  - Create Admin Account
  - Edit Admin Account Email or Password
  - Remove Admin Account
    - Need to block and prompt if the admin account to remove is a owner of an organization
  - Assign the admin account to an organization
5. Organization Management
  - For haytech, cannot change the owner
  - For the other organizations, can change the owner
  - need to have both english & chinese for the organization name & description
  - Remove the organization
    - need to remove all events, posts, resources of that organization or any other organization related database record
    - need to handle the organization switch drop down (When the one selected the organization that needed to delete, the deleted organization will still be there and cannot change the organization in both webapp & mobile app)
6. Views Counter
  - Show the number of viewers of the posts & resources (each user can only count once)
7. Event / Post Tags Management
  - Add Event / Post Tags
  - Eeit Event / Post Tags
  - Delete Event / Post Tags
8. User Details Page Additional Block
  - Add a text box for the admin descript their user (every organization has a independent content in database)
9. Verification Approval
  - Do not alert the comment box when the admin click "approve"
  - Admin can edit the verification data field 
10. Apllication Number Alert
  - Show the total number of pending application for volunteers & verifications on the menu bar
  - Show a point on the organization switch when any pending volunteers & verifications existing in other organizations
  - Periodically refresh the number (1 minutes)
11. Event Sharing
  - Add a QR code download option to share the events, posts, resources (if app installed, open app page, otherwise redirect to app store download page)
12. User List
  - Search Term
    - Phone
    - Name
      - Display Name
      - Verified Name
  - Column
    - Display Name
    - Phone
    - Number of joined days
    - Events Data
      - Number of events as participant applied
      - Number of events as participant cancelled
      - Number of event as participant attended
      - Percentage of attendance rate as participant 
    - Each Verification
      - Verification Status
      - Verification Contents
    - Each Volunteer
      - Organization Volunteer Application Status
      - Number of events as volunteer applied
      - Number of events as volunteer cancelled
      - Number of events as volunteer attended
      - Percentage of attendance rate as volunteer 
  13. Events Overall report
    - Montly Based Report
      - number of all events
      - number of all participants
      - number of all volunteers
  14. Event Join List
    - Show the event role (participant / volunteer)
  15. Event Management
    - Start Time & Endtime need to be shown
    - Time period length needs to be shown
      - xx days xx hours xx mins
    - Add manually "stop event enrollment" button
**Admin Role**
1. Manual Register User Account
  1. Input a phone number
  2. WhatsApp OTP verification
  3. Successfully Registered
2. Manual Event Registration
  1. Go into Events Details Page
  2. Click into Manual Event Registration Page
  3. Select a user by phone number or user name
  4. Click submit
  5. Send API request to join an event
  6. Reject or Completed
3. Manual Events Participant Control
  1. Click into the events details page
  2. Click into the joiner list
  3. click on check-in or cancel button
  4. Send API request to check-in or withdraw an event
4. Admin Account Management
  - Create Admin Account
    - auto assign the admin account to the owner's organization
  - Edit Admin Account Email or Password for the owner's organization admin account
  - Remove the owner's organization admin account
    - cannot remove the owner account
5. Organization Management
  - cannot edit the owner
  - cannot delete the organization
  - need to have both english & chinese for the organization name & description
6. Views Counter
  - Show the number of viewers of the posts & resources (each user can only count once)
7. Event / Post Tags Management
  - Add Event / Post Tags
  - Eeit Event / Post Tags
  - Delete Event / Post Tags
8. User Details Page Additional Block
  - Add a text box for the admin descript their user (every organization has a independent content in database)
9. Verification Approval
  - Do not alert the comment box when the admin click "approve"
  - Admin can edit the verification data field 
10. Apllication Number Alert
  - Show the total number of pending application for volunteers & verifications on the menu bar
  - Show a point on the organization switch when any pending volunteers & verifications existing in other organizations
  - Periodically refresh the number (1 minutes)
11. Event Sharing
  - Add a QR code download option to share the events, posts, resources (if app installed, open app page, otherwise redirect to app store download page)
12. User List (Only show the one joined the event of that organization before only)
  - Search Term
    - Phone
    - Name
      - Display Name
      - Verified Name
  - Column
    - Display Name
    - Phone
    - Number of joined days
    - Events Data
      - Number of events as participant applied
      - Number of events as participant cancelled
      - Number of event as participant attended
      - Percentage of attendance rate as participant 
    - Each Verification (Only show the data passed to the organization by joining an event)
      - Verification Status
      - Verification Contents
    - Each Volunteer
      - Organization Volunteer Application Status
      - Number of events as volunteer applied
      - Number of events as volunteer cancelled
      - Number of events as volunteer attended
      - Percentage of attendance rate as volunteer 
  13. Events Overall report
    - Montly Based Report
      - number of all events
      - number of all participants
      - number of all volunteers
  14. Event Join List
    - Show the event role (participant / volunteer)
  15. Event Management
    - Start Time & Endtime need to be shown
    - Time period length needs to be shown
      - xx days xx hours xx mins
    - Add manually "stop event enrollment" button
**Member Role**
1. Events Button Logic
  - When the user is not the organization volunteer of the organization that created the event, "Join as volunteer" button hided.
  - When the user did not join as a participant or did not aprove as event volunteer, show the "joined as participant" and "Join as volunteer" button
  - When The user joined as a participant, change the "join as participant" button to "cancel" button.
  - When the user apply as an event volunteer but not yet approved,  change the "join as volunteer" button to "pending for approval" button.
  - When the user event volunteer application approved, change the "pending for approval" button to "cancel" button.
  - When the user event volunteer application approved, "Join as volunteer" button hided.
  - Volunteer cannot be a participant at the same time. When the event volunteer application approved, cancel the participant join record.
2. My Events Page Status
  - Show "cancelled" if the events is cancelled by the user.
  - Show "cancelled by admin" if the events is cancelled by the admin.
  - Show "joined as volunteer" if the events is cancelled due to event volunteer application approved.
3. Views Counter
  - Count the number of viewers of the posts & resources (each user can only count once)
4. Add All Organization 
  - add "All Organization" on the organization drop down list (haytech not equal to "All organization)
  - When the user, who is not the member of the organization that created the event, wants to join an event, show pop let the user agree to join the organization that created the event
5. Organization & Description must have english & chinese version


**Mobile App**
1. Events Button Logic
  - When the user is not the organization volunteer of the organization that created the event, "Join as volunteer" button hided.
  - When the user did not join as a participant or did not aprove as event volunteer, show the "joined as participant" and "Join as volunteer" button
  - When The user joined as a participant, change the "join as participant" button to "cancel" button.
  - When the user apply as an event volunteer but not yet approved,  change the "join as volunteer" button to "pending for approval" button.
  - When the user event volunteer application approved, change the "pending for approval" button to "cancel" button.
  - When the user event volunteer application approved, "Join as volunteer" button hided.
  - Volunteer cannot be a participant at the same time. When the event volunteer application approved, cancel the participant join record.
2. My Events Page Status
  - Show "cancelled" if the events is cancelled by the user.
  - Show "cancelled by admin" if the events is cancelled by the admin.
  - Show "joined as volunteer" if the events is cancelled due to event volunteer application approved.
3. Refresh by scrolling up
  - User Proile Page
  - User Verification Page
  - User Volunteer Application Page
  - Explore Pages
  - Dashboard Page
4. Views Counter
  - Count the number of viewers of the posts & resources (each user can only count once)
5. User Verfications
  - "home visit" verification requires completion of "hkid_card" and "apartment proof" verification before applying.
  - List all verification details for "home visit" verification.
  - Do not show +852 for the phone number
  - Add "apartment proof" verification type
6. Scan QR code to help the others finishing verification applications
  - need to test
7. Allow scrolling left/right to view different images in event details page directly (show dots for number of images if more than one).
8. App Notifications
  - new posts/events published
  - call for enrollment at 1 month/2 weeks/1 week before event
9. Enrollment Type
  - "first come first in" type
    - show waiting list, number of enrollment, participant number limit
  - "draw straws" type
    - do not show number of enrollment, participant number limit
10. Add All Organization 
  - add "All Organization" on the organization drop down list (haytech not equal to "All organization)
  - When the user, who is not the member of the organization that created the event, wants to join an event, show pop let the user agree to join the organization that created the event
11. Volunteer Check-in
  - volunteer can help another volunteer to check-in the event
12. All default lanuage must be Chinese
13. Organization & Description must have english & chinese version

**Backend**
1. Change WhatsApp sender to Hexacubic WhatsApp number (+852 69403132).
2. WhatsApp Notifications
  - verification status update
  - event reminders 2 days before
  - event registration templates for:
    - payment required
    - success
    - inside waiting list
    - event list to sucess
  - event check-in
  - event canceled by organization or user
  - volunteer application status update
3. App Notifications
  - new posts/events published
  - call for enrollment at 1 month/2 weeks/1 week before event
4. Enrollment Type
  - "first come first in" type
    - waiting list
  - "draw straws" type
    - admin selects successful applicants


**Over All**
1. Wording Changes
  - 志願者 -> 義工
2. List Filter & Sort
  - All list need to implement Excel like filter & sorting


**Next Phase**
1. 論壇
2. 問卷調查