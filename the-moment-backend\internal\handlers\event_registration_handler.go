package handlers

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"Membership-SAAS-System-Backend/db"                // Added for db.EventRegistrationStatusType and db.AllEventRegistrationStatusType
	"Membership-SAAS-System-Backend/internal/authn"    // For response type
	"Membership-SAAS-System-Backend/internal/payloads" // Added for shared payloads
	"Membership-SAAS-System-Backend/internal/services"
	"Membership-SAAS-System-Backend/internal/utils"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5" // Added import for pgx.ErrNoRows
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
	// "github.com/rs/zerolog/log"
)

type EventRegistrationHandler struct {
	registrationService services.EventRegistrationService // For check-in, and now also for RegisterForEvent
	validator           *utils.RequestValidator           // For validating request bodies
}

// NewEventRegistrationHandler creates a new EventRegistrationHandler.
// It no longer takes EventService.
func NewEventRegistrationHandler(ers services.EventRegistrationService, val *utils.RequestValidator) *EventRegistrationHandler {
	return &EventRegistrationHandler{
		registrationService: ers,
		validator:           val,
	}
}

// CheckInParticipantByScanner godoc
// @Summary Check in a participant for an event by a scanner (volunteer/staff)
// @Description A volunteer or staff member (scanner) checks in a participant for an eligible event. The system identifies the relevant event for the participant (e.g., an active, published event they are registered for).
// @Description The scanner's user_id is taken from their JWT. The service layer handles authorization based on the scanner being an approved volunteer for the organization and the specific event.
// @Description The participant's user_id is provided in the request body.
// @Description Authorization for Scanners (handled by the service):
// @Description   - Scanners (assumed to be volunteers for this flow) must satisfy two conditions:
// @Description     1. Have an 'approved' `user_volunteer_applications` record for the organization hosting the event.
// @Description     2. Have an 'approved' `event_volunteer_applications` record for the specific event they are scanning for.
// @Description     (Successfully scanning also marks the volunteer as attended for that event).
// @Description NOTE: The distinction between staff and volunteer scanners for different logic paths in the service has been removed for this specific flow. Authorization is based on volunteer criteria.
// @Tags Event Registrations
// @Accept json
// @Produce json
// @Param body body payloads.EventCheckInRequest true "Participant's User ID to check in"
// @Success 200 {object} payloads.EventRegistrationResponse "Participant's registration details on successful check-in. Includes `check_in_by_user_id` and `check_in_method` from the embedded `EventRegistration`."
// @Failure 400 {object} utils.ErrorResponse "Invalid request (e.g., missing or invalid participant_user_id, malformed body)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized (scanner token invalid or missing)"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (scanner not authorized: e.g., volunteer lacks approved org-level application or lacks approved event-specific application. See API description for details on volunteer authorization.)"
// @Failure 404 {object} utils.ErrorResponse "Not Found (e.g., participant not registered for any eligible event, or the specified participant user does not exist)"
// @Failure 409 {object} utils.ErrorResponse "Conflict (e.g., participant already checked in to the event)"
// @Security BearerAuth
// @Router /event-registrations/check-in [post]
func (h *EventRegistrationHandler) CheckInParticipantByScanner(c echo.Context) error {
	scannerClaims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Could not validate scanner claims.", err)
	}
	if scannerClaims == nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Scanner claims are missing.", errors.New("scanner claims nil post GetValidatedClaims"))
	}
	scannerUserID := scannerClaims.UserID
	// scannerIsStaff := scannerClaims.IsStaff // This is no longer passed to the service

	var req payloads.EventCheckInRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body: "+err.Error(), err)
	}
	if err := h.validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	participantUserID, err := uuid.Parse(req.UserID)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid participant_user_id format.", err)
	}

	registrationResponse, serviceErr := h.registrationService.CheckInParticipantByScanner(c.Request().Context(), scannerUserID, participantUserID)
	if serviceErr != nil {
		// Handle specific errors from the service layer
		if errors.Is(serviceErr, services.ErrNoEligibleEventForCheckIn) {
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		}
		if errors.Is(serviceErr, services.ErrAlreadyCheckedIn) { // Participant already checked in
			return utils.HandleError(c, http.StatusConflict, serviceErr.Error(), serviceErr)
		}
		if errors.Is(serviceErr, services.ErrScannerNotAuthorized) { // Custom error for authorization failure
			return utils.HandleError(c, http.StatusForbidden, serviceErr.Error(), serviceErr)
		}
		if errors.Is(serviceErr, services.ErrParticipantNotRegistered) { // Custom error
			return utils.HandleError(c, http.StatusNotFound, "Participant not registered for an eligible event.", serviceErr)
		}
		// log.Ctx(c.Request().Context()).Error().Err(serviceErr).
		// Str("scannerUserID", scannerUserID.String()).
		// Str("participantUserID", participantUserID.String()).
		// Msg("Error in CheckInParticipantByScanner handler")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to process check-in for participant", serviceErr)
	}

	return c.JSON(http.StatusOK, registrationResponse)
}

// RegisterForEventRequest defines the expected request body for event registration.
type RegisterForEventRequest struct {
	EventID uuid.UUID `json:"event_id" validate:"required"`
}

// RegisterForEvent godoc
// @Summary Register current user for an event
// @Description Registers the authenticated user for a specific event. Handles capacity, waitlist, eligibility, and time conflicts.
// @Tags Event Registrations
// @Accept json
// @Produce json
// @Param body body payloads.RegisterForEventRequest true "Event ID to register for, e.g. {\"event_id\": \"uuid\"}"
// @Success 201 {object} payloads.EventRegistrationResponse "Registration successful (or waitlisted)"
// @Failure 400 {object} utils.ErrorResponse "Invalid request, event full, not eligible, or time conflict"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Event not found"
// @Security BearerAuth
// @Router /me/event-registrations [post]
func (h *EventRegistrationHandler) RegisterForEvent(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Could not validate claims.", err)
	}
	userID := claims.UserID

	var req payloads.RegisterForEventRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body: "+err.Error(), err)
	}

	if err := h.validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	// Call RegisterForEvent on EventRegistrationService instance
	registrationResponse, serviceErr := h.registrationService.RegisterForEvent(c.Request().Context(), userID, req.EventID)
	if serviceErr != nil {
		var timeConflictErr *services.EventTimeConflictError // EventTimeConflictError is defined in services package (event_service.go)
		if errors.As(serviceErr, &timeConflictErr) {
			return utils.HandleError(c, http.StatusConflict, timeConflictErr.Error(), timeConflictErr)
		} else if errors.Is(serviceErr, services.ErrEventNotFound) { // Shared errors from services package
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		} else if errors.Is(serviceErr, services.ErrAlreadyRegistered) ||
			errors.Is(serviceErr, services.ErrAlreadyWaitlisted) ||
			errors.Is(serviceErr, services.ErrEventCapacityReached) ||
			errors.Is(serviceErr, services.ErrWaitlistFull) {
			return utils.HandleError(c, http.StatusConflict, serviceErr.Error(), serviceErr)
		} else if errors.Is(serviceErr, services.ErrUserNotVerifiedForRequiredTypes) ||
			errors.Is(serviceErr, services.ErrRegistrationClosed) {
			return utils.HandleError(c, http.StatusForbidden, serviceErr.Error(), serviceErr)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to register for event", serviceErr)
	}

	return c.JSON(http.StatusCreated, registrationResponse)
}

// ListUserRegistrations godoc
// @Summary List all event registrations for the current user
// @Description Returns paginated list of the user's event registrations with extensive filtering options.
// @Tags Event Registrations, User Profile
// @Produce json
// @Param limit query int false "Limit number of results" default(10)
// @Param offset query int false "Offset for pagination" default(0)
// @Param start_date query string false "Filter by event start date (ISO 8601)"
// @Param end_date query string false "Filter by event end date (ISO 8601)"
// @Param status query string false "Filter by event status (published, archived, deleted, draft, hidden, cancelled)"
// @Param sort query string false "Sort order (date_asc, date_desc)"
// @Param role query string false "Filter by user's role in event (attendee, volunteer)"
// @Param organization_id query string false "Filter by organization UUID"
// @Success 200 {array} payloads.EventRegistrationResponse "List of user's event registrations"
// @Header 200 {string} X-Total-Count "Total number of registrations"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Security BearerAuth
// @Router /me/event-registrations [get]
func (h *EventRegistrationHandler) ListUserRegistrations(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		// GetValidatedClaims logs specific errors, so a general message here is fine.
		return utils.HandleError(c, http.StatusUnauthorized, "Authentication error: "+err.Error(), err)
	}
	userID := claims.UserID

	// Bind ListUserRegistrationsRequest which includes PageRequest and other potential filters
	var listParams payloads.ListUserRegistrationsRequest
	if err := c.Bind(&listParams); err != nil {
		// Default PageRequest if binding fails for the whole struct
		listParams.PageRequest = payloads.PageRequest{Limit: 20, Offset: 0}
	} else {
		if err := h.validator.ValidateStruct(listParams); err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
		}
	}
	// Ensure default limit if not set or invalid after binding
	if listParams.Limit == 0 {
		listParams.Limit = 20
	}

	registrations, totalCount, err := h.registrationService.ListUserRegistrations(c.Request().Context(), userID, listParams)
	if err != nil {
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to list user registrations", err)
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, registrations)
}

// GetUserEventRegistration godoc
// @Summary Get details of a specific registration
// @Description Retrieves details of a specific event registration owned by the user.
// @Tags Event Registrations
// @Produce json
// @Param registrationId path string true "Registration ID (UUID)"
// @Success 200 {object} payloads.EventRegistrationResponse "Details of the event registration"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Registration not found or not owned by user"
// @Security BearerAuth
// @Router /me/event-registrations/{registrationId} [get]
func (h *EventRegistrationHandler) GetUserEventRegistration(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Authentication error: "+err.Error(), err)
	}
	userID := claims.UserID

	registrationIDStr := c.Param("registrationId")
	registrationID, err := uuid.Parse(registrationIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid registration ID format", err)
	}

	// This will need to be implemented in EventRegistrationService
	registration, err := h.registrationService.GetUserEventRegistration(c.Request().Context(), userID, registrationID)
	if err != nil {
		if errors.Is(err, services.ErrRegistrationNotFound) {
			return utils.HandleError(c, http.StatusNotFound, "Registration not found", err)
		}
		// Check if the error from the service is due to the event or user not being found
		if errors.Is(err, pgx.ErrNoRows) {
			// The service layer logs the specifics (event or user not found)
			return utils.HandleError(c, http.StatusNotFound, "Registration details incomplete or associated event/user not found.", err)
		}
		if errors.Is(err, utils.ErrForbidden) {
			return utils.HandleError(c, http.StatusForbidden, "Not authorized to view this registration", err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to get registration", err)
	}

	return c.JSON(http.StatusOK, registration)
}

// CancelEventRegistration godoc
// @Summary Cancel user's own registration
// @Description Allows the authenticated user to cancel their registration for an event.
// @Tags Event Registrations
// @Param registrationId path string true "Registration ID (UUID)"
// @Success 200 {object} payloads.EventRegistrationResponse "Updated registration details (status: cancelled)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Registration not found or not owned by user"
// @Failure 409 {object} utils.ErrorResponse "Registration already cancelled or not cancellable"
// @Security BearerAuth
// @Router /me/event-registrations/{registrationId}/cancel [patch]
func (h *EventRegistrationHandler) CancelEventRegistration(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Authentication error: "+err.Error(), err)
	}
	userID := claims.UserID

	registrationID, err := uuid.Parse(c.Param("registrationId"))
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid registration ID format", err)
	}

	// This will need to be implemented in EventRegistrationService
	registrationResponse, err := h.registrationService.CancelEventRegistration(c.Request().Context(), userID, registrationID)
	if err != nil {
		if errors.Is(err, services.ErrRegistrationNotFound) {
			return utils.HandleError(c, http.StatusNotFound, "Registration not found", err)
		}
		if errors.Is(err, utils.ErrForbidden) {
			return utils.HandleError(c, http.StatusForbidden, "Not authorized to cancel this registration", err)
		}
		if strings.Contains(err.Error(), "already cancelled") || strings.Contains(err.Error(), "cannot be cancelled") {
			return utils.HandleError(c, http.StatusConflict, err.Error(), err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to cancel event registration", err)
	}

	// Return the final state of the (now cancelled) registration
	return c.JSON(http.StatusOK, registrationResponse)
}

// UpdatePaymentStatus handles PATCH /api/v1/registrations/{registrationId}/payment-status
// Public endpoint as per requirements (no auth needed).
// @Summary Update payment status for a specific registration (Public)
// @Description Updates the payment status of a registration. This is a public endpoint potentially used by payment gateways or staff with a specific staff_id.
// @Tags Event Registrations
// @Accept json
// @Produce json
// @Param registrationId path string true "Registration ID (UUID)"
// @Param body body payloads.UpdateEventRegistrationPaymentRequest true "New payment status and staff ID, e.g. {\"new_payment_status\": \"paid\", \"staff_id\": \"uuid\"}"
// @Success 200 {object} payloads.EventRegistrationResponse "Updated registration details"
// @Failure 400 {object} utils.ErrorResponse "Invalid request"
// @Failure 404 {object} utils.ErrorResponse "Registration not found"
// @Router /event-registrations/{registrationId}/payment-status [patch]
func (h *EventRegistrationHandler) UpdatePaymentStatus(c echo.Context) error {
	registrationID, err := uuid.Parse(c.Param("registrationId"))
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid registration ID format", err)
	}

	var payload struct {
		NewPaymentStatus string    `json:"new_payment_status" validate:"required,oneof=paid pending not_required failed refunded"`
		StaffID          uuid.UUID `json:"staff_id" validate:"required"` // Require ID of who made the change, even if not for auth
	}
	if err := c.Bind(&payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body: "+err.Error(), err)
	}
	if err := h.validator.ValidateStruct(payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	// This will need to be implemented in EventRegistrationService
	registrationResponse, err := h.registrationService.UpdatePaymentStatus(c.Request().Context(), registrationID, payload.NewPaymentStatus, payload.StaffID)
	if err != nil {
		if errors.Is(err, services.ErrRegistrationNotFound) {
			return utils.HandleError(c, http.StatusNotFound, "Registration not found", err)
		}
		if errors.Is(err, utils.ErrInvalidPaymentStatus) {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid payment status", err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to update payment status", err)
	}

	return c.JSON(http.StatusOK, registrationResponse)
}

// ListOrganizationEventRegistrations godoc
// @Summary List event registrations for an organization (Admin view)
// @Description Retrieves a paginated list of event registrations for a specific organization, with filtering options. Intended for organization administrators or managers.
// @Tags Event Registrations, Organizations
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param event_id query string false "Filter by specific Event ID (UUID)"
// @Param start_date query string false "Filter events by start date (ISO 8601, e.g., 2023-01-01T00:00:00Z)"
// @Param end_date query string false "Filter events by end date (ISO 8601, e.g., 2023-12-31T23:59:59Z)"
// @Param status query string false "Filter by registration status (e.g., registered, waitlisted, attended)"
// @Param user_id query string false "Filter registrations by specific User ID (UUID) of the participant"
// @Param role query string false "Filter by user's role in event (e.g., participant, volunteer)"
// @Param search_name query string false "Search by participant user name or email (partial match)"
// @Param payment_status query string false "Filter by payment status (e.g., paid, pending, not_required)"
// @Param limit query int false "Limit number of results" default(20)
// @Param offset query int false "Offset for pagination" default(0)
// @Param filter_payment_status query string false "Filter by payment status (e.g., 'paid', 'unpaid', 'pending', 'refunded') - See `db.PaymentStatusType`"
// @Param filter_user_name_search query string false "Search by user display name or email (case-insensitive)"
// @Success 200 {array} payloads.EventRegistrationResponse "List of event registrations for the organization"
// @Header 200 {string} X-Total-Count "Total number of registrations matching filters"
// @Failure 400 {object} utils.ErrorResponse "Invalid request parameters (e.g., invalid UUID format, invalid filter values)"
// @Failure 404 {object} utils.ErrorResponse "Organization not found (if orgId is invalid or org doesn't exist - though service currently doesn't check org existence for this endpoint)"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/event-registrations [get]
// @Description Note: Authentication via JWT is optional. If provided, requester_user_id is logged. Authorization checks are removed from the service.
func (h *EventRegistrationHandler) ListOrganizationEventRegistrations(c echo.Context) error {
	ctx := c.Request().Context()
	logger := log.Ctx(ctx)

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	var requesterUserID uuid.UUID                      // Will be uuid.Nil if no token or claims are not found
	claims, err := authn.GetOptionalValidatedClaims(c) // Assuming GetOptionalValidatedClaims is implemented as per plan
	if err != nil {                                    // This error is for malformed tokens, not missing tokens
		return utils.HandleError(c, http.StatusBadRequest, "Invalid token format", err)
	}
	if claims != nil {
		requesterUserID = claims.UserID
	}

	var params payloads.ListOrgEventRegistrationsRequest
	if err := c.Bind(&params); err != nil {
		// Set default pagination if binding fails for the whole struct, but log the error
		logger.Warn().Err(err).Msg("Failed to bind query parameters for ListOrganizationEventRegistrations, using defaults for pagination")
		params.PageRequest = payloads.PageRequest{Limit: 20, Offset: 0}
	} else {
		// Validate the bound parameters
		if err := h.validator.ValidateStruct(params); err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Validation failed for query parameters: "+err.Error(), err)
		}
	}

	// Ensure default limit if not set or invalid after binding/validation
	if params.Limit <= 0 {
		params.Limit = 20 // Default limit
	}
	if params.Offset < 0 {
		params.Offset = 0 // Default offset
	}

	registrations, totalCount, serviceErr := h.registrationService.ListOrganizationEventRegistrations(ctx, orgID, requesterUserID, params)
	if serviceErr != nil {
		if errors.Is(serviceErr, utils.ErrBadRequest) { // Check for bad request errors from service (e.g. invalid filter enum)
			return utils.HandleError(c, http.StatusBadRequest, serviceErr.Error(), serviceErr)
		}
		logger.Error().Err(serviceErr).Str("org_id", orgID.String()).Msg("Service error listing organization event registrations")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to list event registrations for the organization", serviceErr)
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, registrations)
}

// UpdateRegistrationStatus godoc
// @Summary Update an event registration's status (Admin view)
// @Description Allows an administrator to update the status of a specific event registration (e.g., approve, reject, mark as attended). Authenticated user (if token provided) is logged for audit purposes but not used for authorization here.
// @Tags Event Registrations, Organizations
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID) - for route structure, not used for auth in service as per plan"
// @Param registrationId path string true "Registration ID (UUID) of the event registration to update"
// @Param body body payloads.UpdateOrgEventRegistrationStatusRequest true "New status and optional admin notes"
// @Success 200 {object} payloads.EventRegistrationResponse "Updated event registration details"
// @Failure 400 {object} utils.ErrorResponse "Invalid request (e.g., invalid UUID, invalid status value, malformed body)"
// @Failure 404 {object} utils.ErrorResponse "Registration not found or Organization not found (though org check is not currently performed by service for this endpoint)"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/event-registrations/{registrationId}/status [patch]
// @Description Note: Authentication via JWT is optional. If provided, requester_user_id is logged. Authorization checks are removed from the service. This endpoint currently has no authorization; any API call with valid IDs can change registration status.
func (h *EventRegistrationHandler) UpdateRegistrationStatus(c echo.Context) error {
	ctx := c.Request().Context()
	logger := log.Ctx(ctx)

	orgIDStr := c.Param("orgId") // Extracted for route consistency, but service won't use it for auth
	_, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format in path", err)
	}

	registrationIDStr := c.Param("registrationId")
	registrationID, err := uuid.Parse(registrationIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid registration ID format", err)
	}

	var requesterUserID uuid.UUID                      // Will be uuid.Nil if no token or claims are not found
	claims, err := authn.GetOptionalValidatedClaims(c) // Assuming GetOptionalValidatedClaims is implemented as per plan
	if err != nil {                                    // This error is for malformed tokens, not missing tokens
		return utils.HandleError(c, http.StatusBadRequest, "Invalid token format", err)
	}
	if claims != nil {
		requesterUserID = claims.UserID
	}

	var req payloads.UpdateOrgEventRegistrationStatusRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body: "+err.Error(), err)
	}
	if err := h.validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed for request body: "+err.Error(), err)
	}

	// The orgID from path is passed to service but plan states it won't be used for auth
	// It's kept for potential logging or if future auth iterations re-introduce org-level checks here.
	orgIDForService, _ := uuid.Parse(orgIDStr) // Re-parse or use the already parsed one if needed for service signature

	updatedRegistration, serviceErr := h.registrationService.UpdateRegistrationStatus(ctx, registrationID, orgIDForService, requesterUserID, req.NewStatus, derefString(req.AdminNotes))
	if serviceErr != nil {
		if errors.Is(serviceErr, services.ErrRegistrationNotFound) {
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		} else if errors.Is(serviceErr, utils.ErrBadRequest) { // Catch bad request from service (e.g. invalid status string)
			return utils.HandleError(c, http.StatusBadRequest, serviceErr.Error(), serviceErr)
		}
		logger.Error().Err(serviceErr).Str("registration_id", registrationID.String()).Msg("Service error updating registration status")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to update registration status", serviceErr)
	}

	return c.JSON(http.StatusOK, updatedRegistration)
}

// Helper function to dereference a string pointer or return an empty string if nil.
// Useful for passing optional string fields to service methods that expect string.
func derefString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// ListEventParticipantUserIDs godoc
// @Summary List user IDs of event participants by status
// @Description Retrieves a list of user UUIDs for participants of a given event, filtered by their registration status.
// @Tags Event Registrations
// @Accept json
// @Produce json
// @Param eventId path string true "Event ID (UUID)"
// @Param status query string false "Registration Status (e.g., registered, attended, waitlisted, pending_approval). If omitted, participants with any status are returned." Enums(registered, attended, waitlisted, pending_approval, confirmed, absent, cancelled_by_user, cancelled_by_admin, cancelled_by_system, rejected_approval)
// @Success 200 {array} uuid.UUID "List of participant user IDs"
// @Failure 400 {object} utils.ErrorResponse "Invalid request (e.g., invalid event ID or status if provided)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized (if authentication is required for this endpoint - adjust as needed)"
// @Failure 404 {object} utils.ErrorResponse "Event not found (if event ID does not exist - service might return empty list too)"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Security BearerAuth // Assuming this might require auth, adjust if public
// @Router /event-registrations/event/{eventId}/participants/user-ids [get]
func (h *EventRegistrationHandler) ListEventParticipantUserIDs(c echo.Context) error {
	// For now, let's assume admin/staff access. Claims can be checked if needed.
	_, err := authn.GetValidatedClaims(c) // Example: Check for valid token, can add role checks
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid or missing token.", err)
	}

	eventIDStr := c.Param("eventId")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid event ID format.", err)
	}

	statusStr := c.QueryParam("status")
	var statusPtr *db.EventRegistrationStatusType

	if statusStr != "" {
		var regStatus db.EventRegistrationStatusType
		if scanErr := regStatus.Scan(statusStr); scanErr != nil {
			return utils.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Invalid type for status parameter: %s", scanErr.Error()), scanErr)
		}
		if !regStatus.Valid() {
			validStatuses := []string{}
			for _, s := range db.AllEventRegistrationStatusTypeValues() {
				validStatuses = append(validStatuses, string(s))
			}
			return utils.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Invalid status value '%s'. Must be one of: %s", statusStr, strings.Join(validStatuses, ", ")), errors.New("invalid status value"))
		}
		statusPtr = &regStatus
	}
	// If statusStr is empty, statusPtr remains nil, signifying "all statuses" to the service.

	userIDs, serviceErr := h.registrationService.ListParticipantUserIDsByEventAndStatus(c.Request().Context(), eventID, statusPtr)
	if serviceErr != nil {
		// Consider if specific errors from the service need special handling (e.g., event not found)
		// For now, a generic internal server error, but could be refined.
		// For example, if the service returns a specific ErrEventNotFound, map it to 404.
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve participant user IDs.", serviceErr)
	}

	if userIDs == nil {
		// Service returns []uuid.UUID{} for no rows, so nil check might not be strictly necessary
		// if it always initializes the slice. But as a safeguard:
		return c.JSON(http.StatusOK, []uuid.UUID{})
	}

	return c.JSON(http.StatusOK, userIDs)
}
