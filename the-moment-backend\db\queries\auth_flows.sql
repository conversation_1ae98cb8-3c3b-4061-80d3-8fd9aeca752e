-- name: CreateAuthFlow :one
INSERT INTO auth_flows (
    flow_type,
    code_verifier,
    code_challenge,
    code_challenge_method,
    state,
    client_id,
    redirect_uri,
    phone,
    email,
    expires_at,
    user_id,
    purpose
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
)
RETURNING *;

-- name: GetAuthFlowByStateAndType :one
SELECT *
FROM auth_flows
WHERE state = $1 AND flow_type = $2 AND expires_at > NOW()
LIMIT 1;

-- name: UpdateAuthFlowConsumed :one
UPDATE auth_flows
SET 
    code_verifier = $2, 
    otp_sid = $3, -- Assuming otp_sid is also updated here, can be NULL if not applicable
    updated_at = NOW(),
    expires_at = NOW() -- Effectively mark as consumed by expiring it
WHERE id = $1
RETURNING *;

-- name: UpdateAuthFlowOTPSID :one
UPDATE auth_flows
SET otp_sid = $2, updated_at = NOW()
WHERE id = $1 AND expires_at > NOW() -- Ensure flow is still valid
RETURNING *;

-- name: GetAuthFlowByID :one
SELECT *
FROM auth_flows
WHERE id = $1
LIMIT 1; 