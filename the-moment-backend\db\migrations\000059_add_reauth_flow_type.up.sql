-- Add the new flow type to the chk_flow_type CHECK constraint
ALTER TABLE auth_flows
DROP CONSTRAINT IF EXISTS chk_flow_type,
ADD CONSTRAINT chk_flow_type CHECK (flow_type IN ('phone_otp', 'email_password', 'phone_registration', 'PHONE_CHANGE_VERIFY', 're_auth_verify'));

-- Update the chk_flow_data CHECK constraint to include re_auth_verify
-- Re-auth uses phone, so it belongs in the first condition.
ALTER TABLE auth_flows
DROP CONSTRAINT IF EXISTS chk_flow_data,
ADD CONSTRAINT chk_flow_data CHECK (
    ((flow_type = 'phone_otp' OR flow_type = 'phone_registration' OR flow_type = 'PHONE_CHANGE_VERIFY' OR flow_type = 're_auth_verify') AND phone IS NOT NULL AND email IS NULL) OR
    (flow_type = 'email_password' AND email IS NOT NULL AND phone IS NULL)
); 