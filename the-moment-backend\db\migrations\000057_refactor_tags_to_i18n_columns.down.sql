-- Revert event_tags to the old structure
CREATE TABLE "event_tags" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "tag_name" VARCHAR(100) NOT NULL,
    "language_code" VARCHAR(10) NOT NULL, -- e.g., 'en', 'zh_HK', 'zh_CN'
    "description" TEXT,
    "created_by_user_id" UUID REFERENCES "users"("id"), -- Nullable if system-defined
    "is_globally_approved" BOOLEAN NOT NULL DEFAULT TRUE,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE ("tag_name", "language_code")
);

-- Revert post_tags to the old structure
CREATE TABLE "post_tags" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "tag_name" VARCHAR(255) NOT NULL,
  "language_code" VARCHAR(10) NOT NULL,
  "description" TEXT,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  UNIQUE ("tag_name", "language_code")
);

-- Recreate the "post_post_tags" table
CREATE TABLE "post_post_tags" (
  "post_id" uuid NOT NULL,
  "tag_id" uuid NOT NULL,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  PRIMARY KEY ("post_id", "tag_id")
);

-- Recreate the "event_event_tags" table
CREATE TABLE "event_event_tags" (
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "event_tag_id" UUID NOT NULL REFERENCES "event_tags"("id") ON DELETE CASCADE,
    PRIMARY KEY ("event_id", "event_tag_id")
);

-- Add foreign keys
ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("post_id") REFERENCES "posts" ("id") ON DELETE CASCADE;
ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("tag_id") REFERENCES "post_tags" ("id") ON DELETE CASCADE;

-- Note: Data from the new i18n columns cannot be perfectly migrated back to the old structure.
-- The original data would be lost. We will re-seed the English versions as a baseline.

-- Seed initial event tags with the old structure
INSERT INTO "event_tags" (tag_name, language_code, description, is_globally_approved) VALUES
('Exchange Group / Exchange Tour', 'en', 'Events related to exchange programs or tours.', TRUE),
('Youth Activity', 'en', 'Activities specifically for youth.', TRUE),
('Senior Activity', 'en', 'Activities designed for seniors.', TRUE),
('Funding Application / Grant', 'en', 'Events or information related to funding applications or grants.', TRUE),
('Volunteer Service', 'en', 'Opportunities for volunteer service.', TRUE),
('Material Distribution', 'en', 'Events involving the distribution of materials or goods.', TRUE),
('Government Funding Scheme', 'en', 'Events related to government funding schemes.', TRUE);

-- Seed initial post tags with the old structure
INSERT INTO "post_tags" (tag_name, language_code) VALUES
('Government News', 'en'),
('Community Update', 'en'),
('Event Highlight', 'en'),
('Member Story', 'en'),
('Official Announcement', 'en');
