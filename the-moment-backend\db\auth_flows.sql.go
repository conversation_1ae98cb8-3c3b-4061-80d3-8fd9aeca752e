// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: auth_flows.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const createAuthFlow = `-- name: CreateAuthFlow :one
INSERT INTO auth_flows (
    flow_type,
    code_verifier,
    code_challenge,
    code_challenge_method,
    state,
    client_id,
    redirect_uri,
    phone,
    email,
    expires_at,
    user_id,
    purpose
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
)
RETURNING id, flow_type, code_verifier, code_challenge, code_challenge_method, state, client_id, redirect_uri, phone, email, otp_sid, expires_at, updated_at, created_at, user_id, purpose
`

type CreateAuthFlowParams struct {
	FlowType            string     `db:"flow_type" json:"flow_type"`
	CodeVerifier        *string    `db:"code_verifier" json:"code_verifier"`
	CodeChallenge       string     `db:"code_challenge" json:"code_challenge"`
	CodeChallengeMethod string     `db:"code_challenge_method" json:"code_challenge_method"`
	State               string     `db:"state" json:"state"`
	ClientID            string     `db:"client_id" json:"client_id"`
	RedirectUri         string     `db:"redirect_uri" json:"redirect_uri"`
	Phone               *string    `db:"phone" json:"phone"`
	Email               *string    `db:"email" json:"email"`
	ExpiresAt           time.Time  `db:"expires_at" json:"expires_at"`
	UserID              *uuid.UUID `db:"user_id" json:"user_id"`
	Purpose             *string    `db:"purpose" json:"purpose"`
}

func (q *Queries) CreateAuthFlow(ctx context.Context, arg CreateAuthFlowParams) (AuthFlow, error) {
	row := q.db.QueryRow(ctx, createAuthFlow,
		arg.FlowType,
		arg.CodeVerifier,
		arg.CodeChallenge,
		arg.CodeChallengeMethod,
		arg.State,
		arg.ClientID,
		arg.RedirectUri,
		arg.Phone,
		arg.Email,
		arg.ExpiresAt,
		arg.UserID,
		arg.Purpose,
	)
	var i AuthFlow
	err := row.Scan(
		&i.ID,
		&i.FlowType,
		&i.CodeVerifier,
		&i.CodeChallenge,
		&i.CodeChallengeMethod,
		&i.State,
		&i.ClientID,
		&i.RedirectUri,
		&i.Phone,
		&i.Email,
		&i.OtpSid,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.UserID,
		&i.Purpose,
	)
	return i, err
}

const getAuthFlowByID = `-- name: GetAuthFlowByID :one
SELECT id, flow_type, code_verifier, code_challenge, code_challenge_method, state, client_id, redirect_uri, phone, email, otp_sid, expires_at, updated_at, created_at, user_id, purpose
FROM auth_flows
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetAuthFlowByID(ctx context.Context, id uuid.UUID) (AuthFlow, error) {
	row := q.db.QueryRow(ctx, getAuthFlowByID, id)
	var i AuthFlow
	err := row.Scan(
		&i.ID,
		&i.FlowType,
		&i.CodeVerifier,
		&i.CodeChallenge,
		&i.CodeChallengeMethod,
		&i.State,
		&i.ClientID,
		&i.RedirectUri,
		&i.Phone,
		&i.Email,
		&i.OtpSid,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.UserID,
		&i.Purpose,
	)
	return i, err
}

const getAuthFlowByStateAndType = `-- name: GetAuthFlowByStateAndType :one
SELECT id, flow_type, code_verifier, code_challenge, code_challenge_method, state, client_id, redirect_uri, phone, email, otp_sid, expires_at, updated_at, created_at, user_id, purpose
FROM auth_flows
WHERE state = $1 AND flow_type = $2 AND expires_at > NOW()
LIMIT 1
`

type GetAuthFlowByStateAndTypeParams struct {
	State    string `db:"state" json:"state"`
	FlowType string `db:"flow_type" json:"flow_type"`
}

func (q *Queries) GetAuthFlowByStateAndType(ctx context.Context, arg GetAuthFlowByStateAndTypeParams) (AuthFlow, error) {
	row := q.db.QueryRow(ctx, getAuthFlowByStateAndType, arg.State, arg.FlowType)
	var i AuthFlow
	err := row.Scan(
		&i.ID,
		&i.FlowType,
		&i.CodeVerifier,
		&i.CodeChallenge,
		&i.CodeChallengeMethod,
		&i.State,
		&i.ClientID,
		&i.RedirectUri,
		&i.Phone,
		&i.Email,
		&i.OtpSid,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.UserID,
		&i.Purpose,
	)
	return i, err
}

const updateAuthFlowConsumed = `-- name: UpdateAuthFlowConsumed :one
UPDATE auth_flows
SET 
    code_verifier = $2, 
    otp_sid = $3, -- Assuming otp_sid is also updated here, can be NULL if not applicable
    updated_at = NOW(),
    expires_at = NOW() -- Effectively mark as consumed by expiring it
WHERE id = $1
RETURNING id, flow_type, code_verifier, code_challenge, code_challenge_method, state, client_id, redirect_uri, phone, email, otp_sid, expires_at, updated_at, created_at, user_id, purpose
`

type UpdateAuthFlowConsumedParams struct {
	ID           uuid.UUID `db:"id" json:"id"`
	CodeVerifier *string   `db:"code_verifier" json:"code_verifier"`
	OtpSid       *string   `db:"otp_sid" json:"otp_sid"`
}

func (q *Queries) UpdateAuthFlowConsumed(ctx context.Context, arg UpdateAuthFlowConsumedParams) (AuthFlow, error) {
	row := q.db.QueryRow(ctx, updateAuthFlowConsumed, arg.ID, arg.CodeVerifier, arg.OtpSid)
	var i AuthFlow
	err := row.Scan(
		&i.ID,
		&i.FlowType,
		&i.CodeVerifier,
		&i.CodeChallenge,
		&i.CodeChallengeMethod,
		&i.State,
		&i.ClientID,
		&i.RedirectUri,
		&i.Phone,
		&i.Email,
		&i.OtpSid,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.UserID,
		&i.Purpose,
	)
	return i, err
}

const updateAuthFlowOTPSID = `-- name: UpdateAuthFlowOTPSID :one
UPDATE auth_flows
SET otp_sid = $2, updated_at = NOW()
WHERE id = $1 AND expires_at > NOW() -- Ensure flow is still valid
RETURNING id, flow_type, code_verifier, code_challenge, code_challenge_method, state, client_id, redirect_uri, phone, email, otp_sid, expires_at, updated_at, created_at, user_id, purpose
`

type UpdateAuthFlowOTPSIDParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	OtpSid *string   `db:"otp_sid" json:"otp_sid"`
}

func (q *Queries) UpdateAuthFlowOTPSID(ctx context.Context, arg UpdateAuthFlowOTPSIDParams) (AuthFlow, error) {
	row := q.db.QueryRow(ctx, updateAuthFlowOTPSID, arg.ID, arg.OtpSid)
	var i AuthFlow
	err := row.Scan(
		&i.ID,
		&i.FlowType,
		&i.CodeVerifier,
		&i.CodeChallenge,
		&i.CodeChallengeMethod,
		&i.State,
		&i.ClientID,
		&i.RedirectUri,
		&i.Phone,
		&i.Email,
		&i.OtpSid,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.UserID,
		&i.Purpose,
	)
	return i, err
}
