// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type ApplicationStatusEnum string

const (
	ApplicationStatusEnumPending   ApplicationStatusEnum = "pending"
	ApplicationStatusEnumApproved  ApplicationStatusEnum = "approved"
	ApplicationStatusEnumRejected  ApplicationStatusEnum = "rejected"
	ApplicationStatusEnumWithdrawn ApplicationStatusEnum = "withdrawn"
)

func (e *ApplicationStatusEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ApplicationStatusEnum(s)
	case string:
		*e = ApplicationStatusEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for ApplicationStatusEnum: %T", src)
	}
	return nil
}

type NullApplicationStatusEnum struct {
	ApplicationStatusEnum ApplicationStatusEnum `json:"application_status_enum"`
	Valid                 bool                  `json:"valid"` // Valid is true if ApplicationStatusEnum is not NULL
}

// <PERSON><PERSON> implements the Scanner interface.
func (ns *NullApplicationStatusEnum) Scan(value interface{}) error {
	if value == nil {
		ns.ApplicationStatusEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ApplicationStatusEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullApplicationStatusEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ApplicationStatusEnum), nil
}

func (e ApplicationStatusEnum) Valid() bool {
	switch e {
	case ApplicationStatusEnumPending,
		ApplicationStatusEnumApproved,
		ApplicationStatusEnumRejected,
		ApplicationStatusEnumWithdrawn:
		return true
	}
	return false
}

func AllApplicationStatusEnumValues() []ApplicationStatusEnum {
	return []ApplicationStatusEnum{
		ApplicationStatusEnumPending,
		ApplicationStatusEnumApproved,
		ApplicationStatusEnumRejected,
		ApplicationStatusEnumWithdrawn,
	}
}

type EventLocationType string

const (
	EventLocationTypePhysical EventLocationType = "physical"
	EventLocationTypeOnline   EventLocationType = "online"
	EventLocationTypeHybrid   EventLocationType = "hybrid"
)

func (e *EventLocationType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EventLocationType(s)
	case string:
		*e = EventLocationType(s)
	default:
		return fmt.Errorf("unsupported scan type for EventLocationType: %T", src)
	}
	return nil
}

type NullEventLocationType struct {
	EventLocationType EventLocationType `json:"event_location_type"`
	Valid             bool              `json:"valid"` // Valid is true if EventLocationType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEventLocationType) Scan(value interface{}) error {
	if value == nil {
		ns.EventLocationType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EventLocationType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEventLocationType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EventLocationType), nil
}

func (e EventLocationType) Valid() bool {
	switch e {
	case EventLocationTypePhysical,
		EventLocationTypeOnline,
		EventLocationTypeHybrid:
		return true
	}
	return false
}

func AllEventLocationTypeValues() []EventLocationType {
	return []EventLocationType{
		EventLocationTypePhysical,
		EventLocationTypeOnline,
		EventLocationTypeHybrid,
	}
}

type EventRegistrationRoleType string

const (
	EventRegistrationRoleTypeParticipant EventRegistrationRoleType = "participant"
	EventRegistrationRoleTypeVolunteer   EventRegistrationRoleType = "volunteer"
)

func (e *EventRegistrationRoleType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EventRegistrationRoleType(s)
	case string:
		*e = EventRegistrationRoleType(s)
	default:
		return fmt.Errorf("unsupported scan type for EventRegistrationRoleType: %T", src)
	}
	return nil
}

type NullEventRegistrationRoleType struct {
	EventRegistrationRoleType EventRegistrationRoleType `json:"event_registration_role_type"`
	Valid                     bool                      `json:"valid"` // Valid is true if EventRegistrationRoleType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEventRegistrationRoleType) Scan(value interface{}) error {
	if value == nil {
		ns.EventRegistrationRoleType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EventRegistrationRoleType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEventRegistrationRoleType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EventRegistrationRoleType), nil
}

func (e EventRegistrationRoleType) Valid() bool {
	switch e {
	case EventRegistrationRoleTypeParticipant,
		EventRegistrationRoleTypeVolunteer:
		return true
	}
	return false
}

func AllEventRegistrationRoleTypeValues() []EventRegistrationRoleType {
	return []EventRegistrationRoleType{
		EventRegistrationRoleTypeParticipant,
		EventRegistrationRoleTypeVolunteer,
	}
}

type EventRegistrationStatusType string

const (
	EventRegistrationStatusTypePendingApproval  EventRegistrationStatusType = "pending_approval"
	EventRegistrationStatusTypeRegistered       EventRegistrationStatusType = "registered"
	EventRegistrationStatusTypeWaitlisted       EventRegistrationStatusType = "waitlisted"
	EventRegistrationStatusTypeRejectedApproval EventRegistrationStatusType = "rejected_approval"
	EventRegistrationStatusTypeCancelledByUser  EventRegistrationStatusType = "cancelled_by_user"
	EventRegistrationStatusTypeAttended         EventRegistrationStatusType = "attended"
	EventRegistrationStatusTypeAbsent           EventRegistrationStatusType = "absent"
)

func (e *EventRegistrationStatusType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EventRegistrationStatusType(s)
	case string:
		*e = EventRegistrationStatusType(s)
	default:
		return fmt.Errorf("unsupported scan type for EventRegistrationStatusType: %T", src)
	}
	return nil
}

type NullEventRegistrationStatusType struct {
	EventRegistrationStatusType EventRegistrationStatusType `json:"event_registration_status_type"`
	Valid                       bool                        `json:"valid"` // Valid is true if EventRegistrationStatusType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEventRegistrationStatusType) Scan(value interface{}) error {
	if value == nil {
		ns.EventRegistrationStatusType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EventRegistrationStatusType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEventRegistrationStatusType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EventRegistrationStatusType), nil
}

func (e EventRegistrationStatusType) Valid() bool {
	switch e {
	case EventRegistrationStatusTypePendingApproval,
		EventRegistrationStatusTypeRegistered,
		EventRegistrationStatusTypeWaitlisted,
		EventRegistrationStatusTypeRejectedApproval,
		EventRegistrationStatusTypeCancelledByUser,
		EventRegistrationStatusTypeAttended,
		EventRegistrationStatusTypeAbsent:
		return true
	}
	return false
}

func AllEventRegistrationStatusTypeValues() []EventRegistrationStatusType {
	return []EventRegistrationStatusType{
		EventRegistrationStatusTypePendingApproval,
		EventRegistrationStatusTypeRegistered,
		EventRegistrationStatusTypeWaitlisted,
		EventRegistrationStatusTypeRejectedApproval,
		EventRegistrationStatusTypeCancelledByUser,
		EventRegistrationStatusTypeAttended,
		EventRegistrationStatusTypeAbsent,
	}
}

type EventStatusType string

const (
	EventStatusTypePublished EventStatusType = "published"
	EventStatusTypeArchived  EventStatusType = "archived"
	EventStatusTypeDeleted   EventStatusType = "deleted"
	EventStatusTypeDraft     EventStatusType = "draft"
	EventStatusTypeHidden    EventStatusType = "hidden"
	EventStatusTypeCancelled EventStatusType = "cancelled"
)

func (e *EventStatusType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EventStatusType(s)
	case string:
		*e = EventStatusType(s)
	default:
		return fmt.Errorf("unsupported scan type for EventStatusType: %T", src)
	}
	return nil
}

type NullEventStatusType struct {
	EventStatusType EventStatusType `json:"event_status_type"`
	Valid           bool            `json:"valid"` // Valid is true if EventStatusType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEventStatusType) Scan(value interface{}) error {
	if value == nil {
		ns.EventStatusType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EventStatusType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEventStatusType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EventStatusType), nil
}

func (e EventStatusType) Valid() bool {
	switch e {
	case EventStatusTypePublished,
		EventStatusTypeArchived,
		EventStatusTypeDeleted,
		EventStatusTypeDraft,
		EventStatusTypeHidden,
		EventStatusTypeCancelled:
		return true
	}
	return false
}

func AllEventStatusTypeValues() []EventStatusType {
	return []EventStatusType{
		EventStatusTypePublished,
		EventStatusTypeArchived,
		EventStatusTypeDeleted,
		EventStatusTypeDraft,
		EventStatusTypeHidden,
		EventStatusTypeCancelled,
	}
}

type JobStatus string

const (
	JobStatusPending   JobStatus = "pending"
	JobStatusRunning   JobStatus = "running"
	JobStatusCompleted JobStatus = "completed"
	JobStatusFailed    JobStatus = "failed"
)

func (e *JobStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = JobStatus(s)
	case string:
		*e = JobStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for JobStatus: %T", src)
	}
	return nil
}

type NullJobStatus struct {
	JobStatus JobStatus `json:"job_status"`
	Valid     bool      `json:"valid"` // Valid is true if JobStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullJobStatus) Scan(value interface{}) error {
	if value == nil {
		ns.JobStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.JobStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullJobStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.JobStatus), nil
}

func (e JobStatus) Valid() bool {
	switch e {
	case JobStatusPending,
		JobStatusRunning,
		JobStatusCompleted,
		JobStatusFailed:
		return true
	}
	return false
}

func AllJobStatusValues() []JobStatus {
	return []JobStatus{
		JobStatusPending,
		JobStatusRunning,
		JobStatusCompleted,
		JobStatusFailed,
	}
}

type PaymentStatusType string

const (
	PaymentStatusTypePaid        PaymentStatusType = "paid"
	PaymentStatusTypeUnpaid      PaymentStatusType = "unpaid"
	PaymentStatusTypeNotRequired PaymentStatusType = "not_required"
	PaymentStatusTypeRefunded    PaymentStatusType = "refunded"
)

func (e *PaymentStatusType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = PaymentStatusType(s)
	case string:
		*e = PaymentStatusType(s)
	default:
		return fmt.Errorf("unsupported scan type for PaymentStatusType: %T", src)
	}
	return nil
}

type NullPaymentStatusType struct {
	PaymentStatusType PaymentStatusType `json:"payment_status_type"`
	Valid             bool              `json:"valid"` // Valid is true if PaymentStatusType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullPaymentStatusType) Scan(value interface{}) error {
	if value == nil {
		ns.PaymentStatusType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.PaymentStatusType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullPaymentStatusType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.PaymentStatusType), nil
}

func (e PaymentStatusType) Valid() bool {
	switch e {
	case PaymentStatusTypePaid,
		PaymentStatusTypeUnpaid,
		PaymentStatusTypeNotRequired,
		PaymentStatusTypeRefunded:
		return true
	}
	return false
}

func AllPaymentStatusTypeValues() []PaymentStatusType {
	return []PaymentStatusType{
		PaymentStatusTypePaid,
		PaymentStatusTypeUnpaid,
		PaymentStatusTypeNotRequired,
		PaymentStatusTypeRefunded,
	}
}

type UserRole string

const (
	UserRoleSuperadmin UserRole = "superadmin"
	UserRoleAdmin      UserRole = "admin"
	UserRoleUser       UserRole = "user"
)

func (e *UserRole) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = UserRole(s)
	case string:
		*e = UserRole(s)
	default:
		return fmt.Errorf("unsupported scan type for UserRole: %T", src)
	}
	return nil
}

type NullUserRole struct {
	UserRole UserRole `json:"user_role"`
	Valid    bool     `json:"valid"` // Valid is true if UserRole is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullUserRole) Scan(value interface{}) error {
	if value == nil {
		ns.UserRole, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.UserRole.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullUserRole) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.UserRole), nil
}

func (e UserRole) Valid() bool {
	switch e {
	case UserRoleSuperadmin,
		UserRoleAdmin,
		UserRoleUser:
		return true
	}
	return false
}

func AllUserRoleValues() []UserRole {
	return []UserRole{
		UserRoleSuperadmin,
		UserRoleAdmin,
		UserRoleUser,
	}
}

type VerificationStatusEnum string

const (
	VerificationStatusEnumPending           VerificationStatusEnum = "pending"
	VerificationStatusEnumApproved          VerificationStatusEnum = "approved"
	VerificationStatusEnumRejected          VerificationStatusEnum = "rejected"
	VerificationStatusEnumDataDeletedByUser VerificationStatusEnum = "data_deleted_by_user"
)

func (e *VerificationStatusEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = VerificationStatusEnum(s)
	case string:
		*e = VerificationStatusEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for VerificationStatusEnum: %T", src)
	}
	return nil
}

type NullVerificationStatusEnum struct {
	VerificationStatusEnum VerificationStatusEnum `json:"verification_status_enum"`
	Valid                  bool                   `json:"valid"` // Valid is true if VerificationStatusEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullVerificationStatusEnum) Scan(value interface{}) error {
	if value == nil {
		ns.VerificationStatusEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.VerificationStatusEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullVerificationStatusEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.VerificationStatusEnum), nil
}

func (e VerificationStatusEnum) Valid() bool {
	switch e {
	case VerificationStatusEnumPending,
		VerificationStatusEnumApproved,
		VerificationStatusEnumRejected,
		VerificationStatusEnumDataDeletedByUser:
		return true
	}
	return false
}

func AllVerificationStatusEnumValues() []VerificationStatusEnum {
	return []VerificationStatusEnum{
		VerificationStatusEnumPending,
		VerificationStatusEnumApproved,
		VerificationStatusEnumRejected,
		VerificationStatusEnumDataDeletedByUser,
	}
}

type VerificationTypeEnum string

const (
	VerificationTypeEnumHkIDCard             VerificationTypeEnum = "hk_id_card"
	VerificationTypeEnumMainlandChinaIDCard  VerificationTypeEnum = "mainland_china_id_card"
	VerificationTypeEnumMainlandTravelPermit VerificationTypeEnum = "mainland_travel_permit"
	VerificationTypeEnumPassport             VerificationTypeEnum = "passport"
	VerificationTypeEnumHkYouthPlus          VerificationTypeEnum = "hk_youth_plus"
	VerificationTypeEnumAddressProof         VerificationTypeEnum = "address_proof"
	VerificationTypeEnumStudentID            VerificationTypeEnum = "student_id"
	VerificationTypeEnumHomeVisit            VerificationTypeEnum = "home_visit"
)

func (e *VerificationTypeEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = VerificationTypeEnum(s)
	case string:
		*e = VerificationTypeEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for VerificationTypeEnum: %T", src)
	}
	return nil
}

type NullVerificationTypeEnum struct {
	VerificationTypeEnum VerificationTypeEnum `json:"verification_type_enum"`
	Valid                bool                 `json:"valid"` // Valid is true if VerificationTypeEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullVerificationTypeEnum) Scan(value interface{}) error {
	if value == nil {
		ns.VerificationTypeEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.VerificationTypeEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullVerificationTypeEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.VerificationTypeEnum), nil
}

func (e VerificationTypeEnum) Valid() bool {
	switch e {
	case VerificationTypeEnumHkIDCard,
		VerificationTypeEnumMainlandChinaIDCard,
		VerificationTypeEnumMainlandTravelPermit,
		VerificationTypeEnumPassport,
		VerificationTypeEnumHkYouthPlus,
		VerificationTypeEnumAddressProof,
		VerificationTypeEnumStudentID,
		VerificationTypeEnumHomeVisit:
		return true
	}
	return false
}

func AllVerificationTypeEnumValues() []VerificationTypeEnum {
	return []VerificationTypeEnum{
		VerificationTypeEnumHkIDCard,
		VerificationTypeEnumMainlandChinaIDCard,
		VerificationTypeEnumMainlandTravelPermit,
		VerificationTypeEnumPassport,
		VerificationTypeEnumHkYouthPlus,
		VerificationTypeEnumAddressProof,
		VerificationTypeEnumStudentID,
		VerificationTypeEnumHomeVisit,
	}
}

type AuthFlow struct {
	ID                  uuid.UUID  `db:"id" json:"id"`
	FlowType            string     `db:"flow_type" json:"flow_type"`
	CodeVerifier        *string    `db:"code_verifier" json:"code_verifier"`
	CodeChallenge       string     `db:"code_challenge" json:"code_challenge"`
	CodeChallengeMethod string     `db:"code_challenge_method" json:"code_challenge_method"`
	State               string     `db:"state" json:"state"`
	ClientID            string     `db:"client_id" json:"client_id"`
	RedirectUri         string     `db:"redirect_uri" json:"redirect_uri"`
	Phone               *string    `db:"phone" json:"phone"`
	Email               *string    `db:"email" json:"email"`
	OtpSid              *string    `db:"otp_sid" json:"otp_sid"`
	ExpiresAt           time.Time  `db:"expires_at" json:"expires_at"`
	UpdatedAt           time.Time  `db:"updated_at" json:"updated_at"`
	CreatedAt           time.Time  `db:"created_at" json:"created_at"`
	UserID              *uuid.UUID `db:"user_id" json:"user_id"`
	Purpose             *string    `db:"purpose" json:"purpose"`
}

type Event struct {
	ID                              uuid.UUID         `db:"id" json:"id"`
	OrganizationID                  uuid.UUID         `db:"organization_id" json:"organization_id"`
	Title                           string            `db:"title" json:"title"`
	LocationType                    EventLocationType `db:"location_type" json:"location_type"`
	LocationOnlineUrl               *string           `db:"location_online_url" json:"location_online_url"`
	StartTime                       time.Time         `db:"start_time" json:"start_time"`
	EndTime                         time.Time         `db:"end_time" json:"end_time"`
	Status                          EventStatusType   `db:"status" json:"status"`
	ParticipantLimit                *int32            `db:"participant_limit" json:"participant_limit"`
	WaitlistLimit                   *int32            `db:"waitlist_limit" json:"waitlist_limit"`
	RequiresApprovalForRegistration bool              `db:"requires_approval_for_registration" json:"requires_approval_for_registration"`
	CreatedByUserID                 uuid.UUID         `db:"created_by_user_id" json:"created_by_user_id"`
	PublishedAt                     *time.Time        `db:"published_at" json:"published_at"`
	GovernmentFundingKeys           []string          `db:"government_funding_keys" json:"government_funding_keys"`
	CreatedAt                       time.Time         `db:"created_at" json:"created_at"`
	UpdatedAt                       time.Time         `db:"updated_at" json:"updated_at"`
	// Full address details for physical or hybrid events.
	LocationFullAddress *string `db:"location_full_address" json:"location_full_address"`
	DescriptionContent  []byte  `db:"description_content" json:"description_content"`
	// Price of the event, stored as text. E.g., "Free", "10.99", "Contact us".
	Price *string `db:"price" json:"price"`
	// Contact email for event inquiries.
	ContactEmail *string `db:"contact_email" json:"contact_email"`
	// Contact phone for event inquiries.
	ContactPhone *string `db:"contact_phone" json:"contact_phone"`
	// Indicates if the event received any government funding.
	IsGovernmentFunded bool `db:"is_government_funded" json:"is_government_funded"`
}

type EventMediaItem struct {
	ID         uuid.UUID `db:"id" json:"id"`
	EventID    uuid.UUID `db:"event_id" json:"event_id"`
	FileName   string    `db:"file_name" json:"file_name"`
	FilePath   string    `db:"file_path" json:"file_path"`
	FileType   string    `db:"file_type" json:"file_type"`
	FileSize   int64     `db:"file_size" json:"file_size"`
	UploadedAt time.Time `db:"uploaded_at" json:"uploaded_at"`
	IsBanner   bool      `db:"is_banner" json:"is_banner"`
}

type EventRegistration struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
	// ID of the user (staff/volunteer) who performed the check-in for this registration (participant or volunteer).
	CheckInByUserID *uuid.UUID `db:"check_in_by_user_id" json:"check_in_by_user_id"`
	// Method used for check-in (e.g., qr_scan, manual_staff, self_check_in).
	CheckInMethod *string `db:"check_in_method" json:"check_in_method"`
}

type EventTag struct {
	ID                 uuid.UUID  `db:"id" json:"id"`
	NameEn             string     `db:"name_en" json:"name_en"`
	NameZhHk           string     `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn           string     `db:"name_zh_cn" json:"name_zh_cn"`
	DescriptionEn      *string    `db:"description_en" json:"description_en"`
	DescriptionZhHk    *string    `db:"description_zh_hk" json:"description_zh_hk"`
	DescriptionZhCn    *string    `db:"description_zh_cn" json:"description_zh_cn"`
	CreatedByUserID    *uuid.UUID `db:"created_by_user_id" json:"created_by_user_id"`
	IsGloballyApproved bool       `db:"is_globally_approved" json:"is_globally_approved"`
	CreatedAt          time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt          time.Time  `db:"updated_at" json:"updated_at"`
}

type EventVolunteerApplication struct {
	ID                     uuid.UUID             `db:"id" json:"id"`
	EventID                uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                 uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID         uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationNotesByUser *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
	AdminReviewNotes       *string               `db:"admin_review_notes" json:"admin_review_notes"`
	AppliedAt              time.Time             `db:"applied_at" json:"applied_at"`
	ReviewedAt             *time.Time            `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID       *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	CreatedAt              time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt              time.Time             `db:"updated_at" json:"updated_at"`
	Status                 ApplicationStatusEnum `db:"status" json:"status"`
	AttendedAt             *time.Time            `db:"attended_at" json:"attended_at"`
}

type Job struct {
	ID uuid.UUID `db:"id" json:"id"`
	// Type of the job, e.g., promote_waitlist, send_email_notification
	JobType string `db:"job_type" json:"job_type"`
	// JSON payload with job-specific parameters
	Payload []byte `db:"payload" json:"payload"`
	// Current status of the job
	Status JobStatus `db:"status" json:"status"`
	// Maximum number of times this job will be retried if it fails
	MaxRetries int32 `db:"max_retries" json:"max_retries"`
	// Number of times this job has been attempted
	Attempts int32 `db:"attempts" json:"attempts"`
	// Error message from the last failed attempt
	LastError *string `db:"last_error" json:"last_error"`
	// Timestamp when the job is scheduled to run (or next retry)
	RunAt     time.Time `db:"run_at" json:"run_at"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}

type Organization struct {
	ID           uuid.UUID `db:"id" json:"id"`
	Name         string    `db:"name" json:"name"`
	Description  *string   `db:"description" json:"description"`
	IsDefaultOrg bool      `db:"is_default_org" json:"is_default_org"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
	// URL of the organization's logo or banner image
	ImageUrl *string `db:"image_url" json:"image_url"`
	// Primary theme color for the organization (e.g., 'red', 'blue', '#FF0000')
	ThemeColor *string `db:"theme_color" json:"theme_color"`
	// The current status of the organization (e.g., pending_setup, active, suspended).
	Status string `db:"status" json:"status"`
}

type OrganizationFile struct {
	ID             uuid.UUID  `db:"id" json:"id"`
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	FileName       string     `db:"file_name" json:"file_name"`
	FilePath       string     `db:"file_path" json:"file_path"`
	FileType       *string    `db:"file_type" json:"file_type"`
	FileSize       *int64     `db:"file_size" json:"file_size"`
	IsFolder       bool       `db:"is_folder" json:"is_folder"`
	ParentFolderID *uuid.UUID `db:"parent_folder_id" json:"parent_folder_id"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}

type OtpAttempt struct {
	ID            uuid.UUID  `db:"id" json:"id"`
	Phone         string     `db:"phone" json:"phone"`
	AttemptCount  int32      `db:"attempt_count" json:"attempt_count"`
	LastAttemptAt time.Time  `db:"last_attempt_at" json:"last_attempt_at"`
	LockedUntil   *time.Time `db:"locked_until" json:"locked_until"`
	CreatedAt     time.Time  `db:"created_at" json:"created_at"`
}

// Removed cover_image_url, is_hidden, and is_for_banner. Cover image is now derived from the first post_media_item. Visibility is handled by the status field. Author and updater are from token.
type Post struct {
	ID             uuid.UUID  `db:"id" json:"id"`
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	Title          string     `db:"title" json:"title"`
	Slug           string     `db:"slug" json:"slug"`
	Status         string     `db:"status" json:"status"`
	PublishedAt    *time.Time `db:"published_at" json:"published_at"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
	Content        []byte     `db:"content" json:"content"`
	AuthorID       uuid.UUID  `db:"author_id" json:"author_id"`
	// ID of the user who last updated the post. Nullable if updated by system or if original updater is deleted.
	UpdatedBy *uuid.UUID `db:"updated_by" json:"updated_by"`
}

type PostMediaItem struct {
	ID         uuid.UUID `db:"id" json:"id"`
	PostID     uuid.UUID `db:"post_id" json:"post_id"`
	FileName   string    `db:"file_name" json:"file_name"`
	FilePath   string    `db:"file_path" json:"file_path"`
	FileType   string    `db:"file_type" json:"file_type"`
	FileSize   int64     `db:"file_size" json:"file_size"`
	UploadedAt time.Time `db:"uploaded_at" json:"uploaded_at"`
	IsBanner   bool      `db:"is_banner" json:"is_banner"`
}

type PostTag struct {
	ID              uuid.UUID `db:"id" json:"id"`
	NameEn          string    `db:"name_en" json:"name_en"`
	NameZhHk        string    `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn        string    `db:"name_zh_cn" json:"name_zh_cn"`
	DescriptionEn   *string   `db:"description_en" json:"description_en"`
	DescriptionZhHk *string   `db:"description_zh_hk" json:"description_zh_hk"`
	DescriptionZhCn *string   `db:"description_zh_cn" json:"description_zh_cn"`
	CreatedAt       time.Time `db:"created_at" json:"created_at"`
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at"`
}

type RefreshToken struct {
	ID        uuid.UUID `db:"id" json:"id"`
	UserID    uuid.UUID `db:"user_id" json:"user_id"`
	TokenHash string    `db:"token_hash" json:"token_hash"`
	ExpiresAt time.Time `db:"expires_at" json:"expires_at"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
}

type Resource struct {
	ID             uuid.UUID  `db:"id" json:"id"`
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	Title          string     `db:"title" json:"title"`
	Slug           string     `db:"slug" json:"slug"`
	Description    *string    `db:"description" json:"description"`
	Visibility     string     `db:"visibility" json:"visibility"`
	Status         string     `db:"status" json:"status"`
	PublishedAt    *time.Time `db:"published_at" json:"published_at"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}

type ResourceFile struct {
	ID          uuid.UUID `db:"id" json:"id"`
	ResourceID  uuid.UUID `db:"resource_id" json:"resource_id"`
	FileName    string    `db:"file_name" json:"file_name"`
	FilePath    string    `db:"file_path" json:"file_path"`
	FileType    string    `db:"file_type" json:"file_type"`
	FileSize    int64     `db:"file_size" json:"file_size"`
	UploadedAt  time.Time `db:"uploaded_at" json:"uploaded_at"`
	Description *string   `db:"description" json:"description"`
}

type User struct {
	ID                          uuid.UUID  `db:"id" json:"id"`
	DisplayName                 string     `db:"display_name" json:"display_name"`
	HashedPassword              *string    `db:"hashed_password" json:"hashed_password"`
	ProfilePictureUrl           *string    `db:"profile_picture_url" json:"profile_picture_url"`
	Phone                       *string    `db:"phone" json:"phone"`
	PhoneVerifiedAt             *time.Time `db:"phone_verified_at" json:"phone_verified_at"`
	Email                       *string    `db:"email" json:"email"`
	EmailVerifiedAt             *time.Time `db:"email_verified_at" json:"email_verified_at"`
	PhoneOtpChannel             string     `db:"phone_otp_channel" json:"phone_otp_channel"`
	InterfaceLanguage           string     `db:"interface_language" json:"interface_language"`
	CommunicationLanguage       string     `db:"communication_language" json:"communication_language"`
	EnableAppNotifications      bool       `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications bool       `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool       `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications    bool       `db:"enable_email_notifications" json:"enable_email_notifications"`
	CreatedAt                   time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt                   time.Time  `db:"updated_at" json:"updated_at"`
	Role                        UserRole   `db:"role" json:"role"`
}

type UserOrganizationMembership struct {
	UserID               uuid.UUID `db:"user_id" json:"user_id"`
	OrganizationID       uuid.UUID `db:"organization_id" json:"organization_id"`
	Role                 string    `db:"role" json:"role"`
	JoinedAt             time.Time `db:"joined_at" json:"joined_at"`
	IsActive             bool      `db:"is_active" json:"is_active"`
	NotificationsEnabled bool      `db:"notifications_enabled" json:"notifications_enabled"`
}

type UserVerificationRequest struct {
	ID               uuid.UUID              `db:"id" json:"id"`
	UserID           uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status           VerificationStatusEnum `db:"status" json:"status"`
	DocumentID       *uuid.UUID             `db:"document_id" json:"document_id"`
	SubmittedAt      time.Time              `db:"submitted_at" json:"submitted_at"`
	ReviewedAt       *time.Time             `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID *uuid.UUID             `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes       *string                `db:"admin_notes" json:"admin_notes"`
	CreatedAt        time.Time              `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time              `db:"updated_at" json:"updated_at"`
	DocumentID2      *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
}

type UserVolunteerApplication struct {
	ID               uuid.UUID             `db:"id" json:"id"`
	UserID           uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID   uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationDate  time.Time             `db:"application_date" json:"application_date"`
	Motivation       *string               `db:"motivation" json:"motivation"`
	ReviewedByUserID *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	ReviewDate       *time.Time            `db:"review_date" json:"review_date"`
	AdminNotes       *string               `db:"admin_notes" json:"admin_notes"`
	CreatedAt        time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time             `db:"updated_at" json:"updated_at"`
	Status           ApplicationStatusEnum `db:"status" json:"status"`
}

type VerificationAddressProof struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	FullAddress           *string   `db:"full_address" json:"full_address"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
}

type VerificationDocument struct {
	ID         uuid.UUID `db:"id" json:"id"`
	UserID     uuid.UUID `db:"user_id" json:"user_id"`
	FileName   string    `db:"file_name" json:"file_name"`
	MimeType   string    `db:"mime_type" json:"mime_type"`
	FileData   []byte    `db:"file_data" json:"file_data"`
	UploadedAt time.Time `db:"uploaded_at" json:"uploaded_at"`
	CreatedAt  time.Time `db:"created_at" json:"created_at"`
	UpdatedAt  time.Time `db:"updated_at" json:"updated_at"`
}

type VerificationHkIDCard struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	ChineseName           *string   `db:"chinese_name" json:"chinese_name"`
	ChineseCommercialCode *string   `db:"chinese_commercial_code" json:"chinese_commercial_code"`
	EnglishName           *string   `db:"english_name" json:"english_name"`
	Sex                   *string   `db:"sex" json:"sex"`
	DateOfBirth           string    `db:"date_of_birth" json:"date_of_birth"`
	HkIDNumber            *string   `db:"hk_id_number" json:"hk_id_number"`
	IsPermanentResident   *bool     `db:"is_permanent_resident" json:"is_permanent_resident"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
}

type VerificationHkYouthPlu struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	MemberNumber          *string   `db:"member_number" json:"member_number"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
	DocumentID            uuid.UUID `db:"document_id" json:"document_id"`
}

type VerificationHomeVisit struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	Notes                 *string   `db:"notes" json:"notes"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
}

type VerificationMainlandChinaIDCard struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	ChineseName           *string   `db:"chinese_name" json:"chinese_name"`
	Sex                   *string   `db:"sex" json:"sex"`
	DateOfBirth           string    `db:"date_of_birth" json:"date_of_birth"`
	MainlandIDNumber      *string   `db:"mainland_id_number" json:"mainland_id_number"`
	ValidUntil            string    `db:"valid_until" json:"valid_until"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
}

type VerificationMainlandTravelPermit struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	PermitNumber          *string   `db:"permit_number" json:"permit_number"`
	IssueDate             string    `db:"issue_date" json:"issue_date"`
	ExpiryDate            string    `db:"expiry_date" json:"expiry_date"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
}

type VerificationPassport struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	PassportNumber        *string   `db:"passport_number" json:"passport_number"`
	IssuingCountry        *string   `db:"issuing_country" json:"issuing_country"`
	IssueDate             string    `db:"issue_date" json:"issue_date"`
	ExpiryDate            string    `db:"expiry_date" json:"expiry_date"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
}

type VerificationStudentID struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	SchoolName            *string   `db:"school_name" json:"school_name"`
	Grade                 *string   `db:"grade" json:"grade"`
	ExpiryDate            string    `db:"expiry_date" json:"expiry_date"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
}
