/**
 * @fileoverview JSDoc type definitions for volunteer application management.
 */

/**
 * Represents a summary of a volunteer application, typically for list views.
 * @typedef {object} VolunteerApplicationSummary
 * @property {string} application_id - The unique identifier for the application.
 * @property {string} user_id - The unique identifier of the applicant.
 * @property {string} user_name - The name of the applicant.
 * @property {string} application_date - ISO 8601 datetime string of when the application was submitted.
 * @property {string} status - Current status of the application (e.g., "pending_review").
 * @property {string} [event_title] - Optional title of the event if the application is event-specific.
 * @property {string} [organization_id] - The ID of the organization this application is for.
 */

/**
 * Represents the detailed information of a volunteer application.
 * @typedef {object} VolunteerApplicationDetail
 * @property {string} application_id - The unique identifier for the application.
 * @property {string} organization_id - The ID of the organization this application is for.
 * @property {object} user_info - Information about the applicant.
 * @property {string} user_info.user_id - The unique identifier of the user.
 * @property {string} user_info.name - The full name of the user.
 * @property {string} user_info.email - The email address of the user.
 * @property {string} [user_info.phone] - The phone number of the user (optional).
 * @property {string} [user_info.avatar_url] - URL of the user's avatar (optional).
 * @property {string} application_date - ISO 8601 datetime string of when the application was submitted.
 * @property {string} status - Current status of the application (e.g., "pending_review", "approved", "rejected").
 * @property {string} [motivation_letter] - The applicant's motivation statement (optional).
 * @property {Array<VerificationDocument>} [verification_documents] - Array of verification documents submitted.
 * @property {string} [review_comments] - Comments from the admin who reviewed the application (optional).
 * @property {string} [reviewed_by_id] - ID of the admin user who reviewed the application (optional).
 * @property {string} [reviewed_at] - ISO 8601 datetime string of when the review was done (optional).
 * @property {string} [event_id] - Optional ID of the event if this application is for a specific event.
 * @property {string} [event_title] - Optional title of the event.
 * @property {object} [additional_fields] - Any other dynamic fields or answers provided in the application (optional).
 */

/**
 * Represents a verification document submitted with an application.
 * @typedef {object} VerificationDocument
 * @property {string} document_id - The unique identifier for the document.
 * @property {string} document_type - The type of document (e.g., "ID_CARD", "CERTIFICATE").
 * @property {string} file_name - The original name of the uploaded file.
 * @property {string} file_url - A URL to access/download the file.
 * @property {string} mime_type - The MIME type of the file.
 * @property {number} file_size - Size of the file in bytes.
 * @property {string} upload_date - ISO 8601 datetime string of when the document was uploaded.
 */

/**
 * Payload for reviewing a volunteer application.
 * @typedef {object} VolunteerApplicationReviewPayload
 * @property {"approved"|"rejected"} new_status - The new status to set for the application.
 * @property {string} [review_comments] - Optional comments from the reviewer.
 */

export {}; // Ensures this file is treated as a module 