import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Space, Avatar, Typography, Dropdown, Drawer, Select, Tooltip, Badge } from 'antd';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { UserOutlined } from '@ant-design/icons';
import OrganizationDropdown from './OrganizationDropdown';
import { useOrganization } from '../contexts/OrganizationContext';
const { Sider } = Layout;
const { Option } = Select;

const Sidebar = ({
    collapsed,
    setCollapsed,
    userMenuItems,
    currentOrganization,
    handleOrganizationChange,
    organizations,
    isTablet,
    isMobile,
    mobileDrawerOpen,
    onMobileDrawerClose,
    filteredItems,
    getSelectedKeys,
    handleMenuClick,
    user,
    role,
}) => {
    const { t, i18n } = useTranslation();
    const { isLoggedIn } = useAuth();
    const { volunteerPendingCounts, verificationsPendingCount } = useOrganization();
    const navigate = useNavigate();

    // Get all parent keys that have children
    const getAllParentKeys = (items) => {
        return items.reduce((keys, item) => {
            if (item.children) {
                keys.push(item.key);
            }
            return keys;
        }, []);
    };

    // Initialize with all parent keys expanded
    const [openKeys, setOpenKeys] = useState(() => getAllParentKeys(filteredItems));

    // Only reset open keys when drawer closes in mobile view
    useEffect(() => {
        if (isMobile && !mobileDrawerOpen) {
            setOpenKeys([]);
        }
    }, [isMobile, mobileDrawerOpen]);

    // Handle menu open/close while preserving user's choices
    const onOpenChange = (keys) => {
        setOpenKeys(keys);
    };

    // Customize menu items to make text bolder and add badges
    const enhancedMenuItems = filteredItems.map(item => {
        // Calculate total pending count for current organization
        const currentOrgPendingCount = currentOrganization ? (volunteerPendingCounts[currentOrganization.id] || 0) : 0;
        const shouldShowVolunteerBadge = (user?.role === 'admin' || user?.role === 'super_admin') && currentOrgPendingCount > 0;
        
        // For verifications, only super_admin can see and only for global verifications
        const shouldShowVerificationsBadge = user?.role === 'super_admin' && verificationsPendingCount > 0;
        
        if (item.children) {
            return {
                ...item,
                children: item.children.map(child => {
                    // Add badge to volunteers-approval menu item
                    if (child.key === 'volunteers-approval' && shouldShowVolunteerBadge) {
                        return {
                            ...child,
                            // For collapsed state, use simple text and set title for tooltip
                            label: collapsed ? child.label : (
                                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                                    <span>{child.label}</span>
                                    <Badge 
                                        count={currentOrgPendingCount} 
                                        size="large" 
                                        color="pink"
                                    />
                                </div>
                            ),
                            // Set title for collapsed tooltip showing count
                            title: collapsed ? `${child.label} (${currentOrgPendingCount})` : undefined,
                            style: { fontSize: '14px', fontWeight: 500 }
                        };
                    }
                    
                    // Add badge to verification menu item
                    if (child.key === 'verification' && shouldShowVerificationsBadge) {
                        return {
                            ...child,
                            // For collapsed state, use simple text and set title for tooltip
                            label: collapsed ? child.label : (
                                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                                    <span>{child.label}</span>
                                    <Badge 
                                        count={verificationsPendingCount} 
                                        size="large" 
                                        color="orange"
                                    />
                                </div>
                            ),
                            // Set title for collapsed tooltip showing count
                            title: collapsed ? `${child.label} (${verificationsPendingCount})` : undefined,
                            style: { fontSize: '14px', fontWeight: 500 }
                        };
                    }
                    
                    return {
                        ...child,
                        // Set title for collapsed tooltip for regular items
                        title: collapsed ? child.label : undefined,
                        style: { fontSize: '14px', fontWeight: 500 }
                    };
                }),
                // Set title for collapsed tooltip for parent items
                title: collapsed ? item.label : undefined,
                style: { fontSize: '15px', fontWeight: 600 }
            };
        }
        
        return {
            ...item,
            // Set title for collapsed tooltip for regular items
            title: collapsed ? item.label : undefined,
            style: { fontSize: '15px', fontWeight: 600 }
        };
    });

    const sidebarContent = (
        <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
            {/* Header Section */}
            <div className="border-b border-gray-200">
                {/* Expanded State Layout */}
                {!collapsed && !isMobile && (
                    <div className="h-16 px-4 flex items-center justify-between">
                        <OrganizationDropdown
                            organizations={organizations}
                            currentOrganization={currentOrganization}
                            handleOrganizationChange={handleOrganizationChange}
                            collapsed={collapsed}
                            isMobile={isMobile}
                        />
                        <Button
                            type="text"
                            icon={
                                <Icon
                                    icon="iconoir:sidebar-collapse"
                                    className="text-2xl"
                                />
                            }
                            className="flex items-center justify-center w-10 h-10 ml-2 flex-shrink-0"
                            onClick={() => setCollapsed(true)}
                        />
                    </div>
                )}

                {/* Collapsed State Layout */}
                {collapsed && !isMobile && (
                    <div className="py-4 flex flex-col items-center">
                        <Tooltip 
                            title={isTablet ? t('mainLayout.sidebarLockedOnMobile') : t('mainLayout.expandSidebar')}
                            placement="right"
                        >
                            <Button
                                type="text"
                                icon={
                                    <Icon
                                        icon="iconoir:sidebar-expand"
                                        className="text-2xl"
                                    />
                                }
                                className="flex items-center justify-center w-10 h-10 mb-4"
                                onClick={() => !isTablet && setCollapsed(false)}
                            />
                        </Tooltip>
                        <OrganizationDropdown
                            organizations={organizations}
                            currentOrganization={currentOrganization}
                            handleOrganizationChange={handleOrganizationChange}
                            collapsed={collapsed}
                            isMobile={isMobile}
                        />
                    </div>
                )}

                {/* Mobile Layout */}
                {isMobile && (
                    <div className="h-16 px-4 flex items-center">
                        <OrganizationDropdown
                            organizations={organizations}
                            currentOrganization={currentOrganization}
                            handleOrganizationChange={handleOrganizationChange}
                            collapsed={collapsed}
                            isMobile={isMobile}
                        />
                    </div>
                )}
            </div>

            {/* Menu Items */}
            <div style={{ flex: 1, overflowY: 'auto' }}>
                <Menu
                    theme="light"
                    mode="inline"
                    inlineCollapsed={collapsed && !isMobile}
                    selectedKeys={getSelectedKeys()}
                    openKeys={openKeys}
                    onOpenChange={onOpenChange}
                    items={enhancedMenuItems}
                    onClick={(info) => {
                        handleMenuClick(info);
                        if (isMobile) {
                            onMobileDrawerClose();
                        }
                    }}
                    style={{
                        fontSize: '15px',
                        fontWeight: 500
                    }}
                />
            </div>

            {/* Mobile Footer - Only show in mobile drawer */}
            {isMobile && (
                <div className="border-t border-gray-200 p-3 bg-white">
                    <div className="mb-3">
                        <Select
                            defaultValue={i18n.language}
                            className="w-full"
                            onChange={(value) => i18n.changeLanguage(value)}
                            size="large"
                        >
                            <Option value="en">English</Option>
                            <Option value="zh-HK">繁體中文</Option>
                        </Select>
                    </div>
                    {isLoggedIn ? (
                        <Dropdown
                            menu={{
                                items: userMenuItems,
                            }}
                            placement="bottomRight"
                            trigger={['click']}
                        >
                            <div className="px-3 py-2 cursor-pointer rounded-lg hover:bg-gray-100 transition-colors flex items-center justify-between">
                                <Space size={10}>
                                    <Avatar
                                        className="w-8 h-8 bg-[#87d068] flex items-center justify-center"
                                        icon={<UserOutlined className="text-base" />}
                                    />
                                    <div className="flex flex-col gap-0">
                                        <Typography.Text strong className="text-sm leading-tight">
                                            {user?.name || 'User'}
                                        </Typography.Text>
                                        <Typography.Text type="secondary" className="text-xs leading-tight">
                                            {t(`mainLayout.roles.${role || 'user'}`)}
                                        </Typography.Text>
                                    </div>
                                </Space>
                                <Icon
                                    icon="mingcute:up-line"
                                    className="text-base text-gray-600"
                                />
                            </div>
                        </Dropdown>
                    ) : (
                        <Space direction="vertical" className="w-full" size="small">
                            <Button
                                type="primary"
                                onClick={() => {
                                    navigate("/login");
                                    onMobileDrawerClose();
                                }}
                                size="middle"
                                block
                                className="h-9"
                            >
                                {t('mainLayout.confirmLogout.buttons.signIn')}
                            </Button>
                        </Space>
                    )}
                </div>
            )}
        </div>
    );

    if (isMobile) {
        return (
            <Drawer
                placement="left"
                onClose={onMobileDrawerClose}
                open={mobileDrawerOpen}
                width={250}
                styles={{
                    body: {
                        padding: 0,
                        overflow: 'hidden',
                    },
                }}
            >
                {sidebarContent}
            </Drawer>
        );
    }

    return (
        <Sider
            trigger={null}
            collapsible
            collapsed={collapsed}
            theme="light"
            style={{
                overflow: 'auto',
                height: '100vh',
                position: 'fixed',
            }}
            width={250}
        >
            {sidebarContent}
        </Sider>
    );
};

export default Sidebar; 