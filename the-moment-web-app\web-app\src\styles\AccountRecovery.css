.recovery-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7ff 0%, #ffffff 100%);
}

.recovery-content {
  background: white;
  padding: 40px;
  border-radius: 16px;
  width: 100%;
  max-width: 520px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.recovery-header {
  text-align: center;
  margin-bottom: 40px;
}

.recovery-header h2 {
  margin-bottom: 8px;
}

.recovery-form .ant-form-item {
  margin-bottom: 24px;
}

.recovery-form .ant-input-affix-wrapper {
  border-radius: 8px;
}

.recovery-form .ant-select-selector {
  border-radius: 8px !important;
}

.recovery-form-buttons {
  gap: 12px;
  margin-top: 40px;
}

.recovery-form-buttons .ant-btn {
  flex: 1;
  height: 40px;
  border-radius: 8px;
}

.ant-input-textarea-show-count::after {
  color: rgba(0, 0, 0, 0.45);
}

/* Responsive Design */
@media (max-width: 576px) {
  .recovery-content {
    padding: 30px 20px;
  }
  
  .recovery-form-buttons {
    flex-direction: column-reverse;
  }
  
  .recovery-form-buttons .ant-btn {
    width: 100%;
  }
} 