import React, { useState } from 'react';
import { Typography, Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import '../../styles/UserInfo.css';
import UserInfoTab from './UserInfoTab';
import PreferencesTab from './PreferencesTab';
import IdentityVerificationTab from './IdentityVerificationTab';
import ProgramApplicationsTab from './ProgramApplicationsTab';

const { TabPane } = Tabs;

const UserSettings = () => {
    const [activeTab, setActiveTab] = useState('1');
    const { t } = useTranslation();

    return (
        <div className="activity-card">
            <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                tabPosition="top"
                className="settings-tabs"
            >
                {/* Personal Info Tab - TEMPORARILY HIDDEN
                <TabPane tab={t('userSettings.tabs.infoChange')} key="1">
                    <UserInfoTab />
                </TabPane>
                */}

                {/* Preferences Tab - TEMPORARILY HIDDEN
                <TabPane tab={t('userSettings.tabs.preference')} key="2">
                    <PreferencesTab />
                </TabPane>
                */}

                {/* Identity Verification Tab */}
                <TabPane tab={t('userSettings.tabs.identityVerification')} key="1">
                    <IdentityVerificationTab />
                </TabPane>

                {/* Program Applications Tab */}
                <TabPane tab={t('userSettings.tabs.programApplications')} key="2">
                    <ProgramApplicationsTab />
                </TabPane>
            </Tabs>
        </div>
    );
}

export default UserSettings;