// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: scheduler_queries.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const getEventRegistrationsByEventIDAndStatus = `-- name: GetEventRegistrationsByEventIDAndStatus :many
SELECT er.id, er.event_id, er.user_id, er.status, er.payment_status, er.registration_role, er.registered_at, er.attended_at, er.cancellation_reason_by_user, er.admin_notes_on_registration, er.waitlist_priority, er.created_at, er.updated_at, er.check_in_by_user_id, er.check_in_method, u.display_name as user_display_name -- Select user details as well
FROM event_registrations er
JOIN users u ON er.user_id = u.id
WHERE er.event_id = $1 AND er.status = $2
`

type GetEventRegistrationsByEventIDAndStatusParams struct {
	EventID uuid.UUID                   `db:"event_id" json:"event_id"`
	Status  EventRegistrationStatusType `db:"status" json:"status"`
}

type GetEventRegistrationsByEventIDAndStatusRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
	CheckInByUserID          *uuid.UUID                  `db:"check_in_by_user_id" json:"check_in_by_user_id"`
	CheckInMethod            *string                     `db:"check_in_method" json:"check_in_method"`
	UserDisplayName          string                      `db:"user_display_name" json:"user_display_name"`
}

func (q *Queries) GetEventRegistrationsByEventIDAndStatus(ctx context.Context, arg GetEventRegistrationsByEventIDAndStatusParams) ([]GetEventRegistrationsByEventIDAndStatusRow, error) {
	rows, err := q.db.Query(ctx, getEventRegistrationsByEventIDAndStatus, arg.EventID, arg.Status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventRegistrationsByEventIDAndStatusRow{}
	for rows.Next() {
		var i GetEventRegistrationsByEventIDAndStatusRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.Status,
			&i.PaymentStatus,
			&i.RegistrationRole,
			&i.RegisteredAt,
			&i.AttendedAt,
			&i.CancellationReasonByUser,
			&i.AdminNotesOnRegistration,
			&i.WaitlistPriority,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CheckInByUserID,
			&i.CheckInMethod,
			&i.UserDisplayName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventsByStartTimeRange = `-- name: GetEventsByStartTimeRange :many
SELECT id, organization_id, title, location_type, location_online_url, start_time, end_time, status, participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at, government_funding_keys, created_at, updated_at, location_full_address, description_content, price, contact_email, contact_phone, is_government_funded
FROM events
WHERE start_time >= $1 AND start_time <= $2
ORDER BY start_time ASC
`

type GetEventsByStartTimeRangeParams struct {
	StartTime   time.Time `db:"start_time" json:"start_time"`
	StartTime_2 time.Time `db:"start_time_2" json:"start_time_2"`
}

func (q *Queries) GetEventsByStartTimeRange(ctx context.Context, arg GetEventsByStartTimeRangeParams) ([]Event, error) {
	rows, err := q.db.Query(ctx, getEventsByStartTimeRange, arg.StartTime, arg.StartTime_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Event{}
	for rows.Next() {
		var i Event
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.LocationType,
			&i.LocationOnlineUrl,
			&i.StartTime,
			&i.EndTime,
			&i.Status,
			&i.ParticipantLimit,
			&i.WaitlistLimit,
			&i.RequiresApprovalForRegistration,
			&i.CreatedByUserID,
			&i.PublishedAt,
			&i.GovernmentFundingKeys,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LocationFullAddress,
			&i.DescriptionContent,
			&i.Price,
			&i.ContactEmail,
			&i.ContactPhone,
			&i.IsGovernmentFunded,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
