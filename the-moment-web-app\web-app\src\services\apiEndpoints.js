/**
 * API Endpoints Documentation
 * This file documents all API endpoints and their expected request/response formats
 */

export const API_ENDPOINTS = {
  AUTH: {
    PHONE_CHECK: '/authn/phone/check',
    PHONE_OTP_INITIATE: '/authn/phone/otp/initiate',
    PHONE_OTP_VERIFY: '/authn/phone/otp/verify',
    REGISTER_PHONE_INITIATE: '/authn/register/phone/initiate',
    REGISTER_PHONE_VERIFY: '/authn/register/phone/verify',
    STAFF_EMAIL_CHECK: '/authn/staff/email/check',
    STAFF_LOGIN_INITIATE: '/authn/staff/login/initiate',
    STAFF_LOGIN_VERIFY: '/authn/staff/login/verify',
    TOKEN_REFRESH: '/authn/token/refresh',
    LOGOUT: '/authn/logout',
  },
  USERS: {
    ME: '/users/me',
    UPDATE_ME: '/users/me',
    UPLOAD_PROFILE_PICTURE: '/users/me/profile-picture',
    INITIATE_PHONE_CHANGE: '/users/me/phone/initiate-change',
    VERIFY_PHONE_CHANGE: '/users/me/phone/verify-change',
    MY_ORGANIZATIONS: '/users/me/organizations',
    MY_STATS: '/users/me/stats',
    MY_UUID: '/users/me/uuid',
    SUBMIT_VERIFICATION: '/users/me/verifications',
    LIST_MY_VERIFICATIONS: '/users/me/verifications',
    GET_MY_VERIFICATION_DETAIL: (reqId) => `/users/me/verifications/${reqId}`,
    DELETE_MY_VERIFICATION: (reqId) => `/users/me/verifications/${reqId}`,
    GET_MY_VERIFICATION_DOCUMENT: (docId) => `/users/me/verifications/documents/${docId}`,
    LIST_MY_VOLUNTEER_APPLICATIONS: '/users/me/volunteer/applications',
    GET_MY_VOLUNTEER_APPLICATION_DETAIL: (appId) => `/users/me/volunteer/applications/${appId}`,
    LIST_MY_VOLUNTEER_QUALIFICATIONS: '/users/me/volunteer/qualifications',
    MY_EVENT_STATISTICS: '/users/me/event-statistics',
  },
  ORGANIZATIONS: {
    CREATE_ORGANIZATION: '/organizations',
    LIST_ORGANIZATIONS: '/organizations',
    GET_ORGANIZATION_DETAIL: (orgId) => `/organizations/${orgId}`,
    UPDATE_ORGANIZATION: (orgId) => `/organizations/${orgId}`,
    DELETE_ORGANIZATION: (orgId) => `/organizations/${orgId}`,
    UPLOAD_ORGANIZATION_LOGO: (orgId) => `/organizations/${orgId}/logo`,
    JOIN_ORGANIZATION: (orgId) => `/organizations/${orgId}/join`,
    LEAVE_ORGANIZATION: (orgId) => `/organizations/${orgId}/leave`,
    APPLY_FOR_VOLUNTEER: (orgId) => `/organizations/${orgId}/volunteer/apply`,
    GET_VOLUNTEER_STATUS: (orgId) => `/organizations/${orgId}/volunteer/status`,
    CREATE_EVENT: (orgId) => `/organizations/${orgId}/events`,
    LIST_ORG_EVENTS: (orgId) => `/organizations/${orgId}/events`,
    GET_ORG_EVENT_DETAIL: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}`,
    UPDATE_EVENT: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}`,
    UPDATE_EVENT_STATUS: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}/status`,
    DELETE_EVENT: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}`,
    UPLOAD_EVENT_MEDIA: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}/media`,
    LIST_EVENT_MEDIA: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}/media`,
    DELETE_EVENT_MEDIA: (orgId, eventId, itemId) => `/organizations/${orgId}/events/${eventId}/media/${itemId}`,
    ADD_EVENT_TAG: (orgId, eventId, tagId) => `/organizations/${orgId}/events/${eventId}/tags/${tagId}`,
    DELETE_EVENT_TAG: (orgId, eventId, tagId) => `/organizations/${orgId}/events/${eventId}/tags/${tagId}`,
    LIST_EVENT_TAGS_FOR_EVENT: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}/tags`,
    ADD_EVENT_VERIFICATION_TYPE: (orgId, eventId, typeKey) => `/organizations/${orgId}/events/${eventId}/verification-types/${typeKey}`,
    DELETE_EVENT_VERIFICATION_TYPE: (orgId, eventId, typeKey) => `/organizations/${orgId}/events/${eventId}/verification-types/${typeKey}`,
    LIST_EVENT_VERIFICATION_TYPES: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}/verification-types`,
    LIST_ORG_EVENT_REGISTRATIONS: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}/registrations`,
    LIST_ORG_EVENT_VOLUNTEER_APPLICATIONS: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}/volunteer-applications`,
    UPDATE_REGISTRATION_STATUS: (orgId, registrationId) => `/organizations/${orgId}/registrations/${registrationId}/status`,
    UPDATE_ORG_EVENT_REGISTRATION_STATUS: (orgId, registrationId) => `/organizations/${orgId}/event-registrations/${registrationId}/status`,
    REVIEW_EVENT_VOLUNTEER_APPLICATION: (orgId, eventId, applicationId) => `/organizations/${orgId}/events/${eventId}/volunteer-applications/${applicationId}/review`,
    GET_ORG_EVENT_STATISTICS: (orgId) => `/organizations/${orgId}/event-statistics`,
    GET_SPECIFIC_EVENT_STATISTICS: (orgId, eventId) => `/organizations/${orgId}/events/${eventId}/statistics`,
    CREATE_POST: (orgId) => `/organizations/${orgId}/posts`,
    LIST_ORG_POSTS: (orgId) => `/organizations/${orgId}/posts`,
    GET_ORG_POST_DETAIL: (orgId, postId) => `/organizations/${orgId}/posts/${postId}`,
    UPDATE_POST: (orgId, postId) => `/organizations/${orgId}/posts/${postId}`,
    DELETE_POST: (orgId, postId) => `/organizations/${orgId}/posts/${postId}`,
    UPLOAD_POST_MEDIA: (orgId, postId) => `/organizations/${orgId}/posts/${postId}/media`,
    DELETE_POST_MEDIA: (orgId, postId, mediaId) => `/organizations/${orgId}/posts/${postId}/media/${mediaId}`,
    ADD_TAG_TO_POST: (orgId, postId, tagId) => `/organizations/${orgId}/posts/${postId}/tags/${tagId}`,
    DELETE_TAG_FROM_POST: (orgId, postId, tagId) => `/organizations/${orgId}/posts/${postId}/tags/${tagId}`,
    LIST_TAGS_FOR_POST: (orgId, postId) => `/organizations/${orgId}/posts/${postId}/tags`,
    CREATE_RESOURCE: (orgId) => `/organizations/${orgId}/resources`,
    LIST_ORG_RESOURCES: (orgId) => `/organizations/${orgId}/resources`,
    GET_ORG_RESOURCE_DETAIL: (orgId, resourceId) => `/organizations/${orgId}/resources/${resourceId}`,
    UPDATE_RESOURCE: (orgId, resourceId) => `/organizations/${orgId}/resources/${resourceId}`,
    DELETE_RESOURCE: (orgId, resourceId) => `/organizations/${orgId}/resources/${resourceId}`,
    GET_PUBLIC_ORG_RESOURCE_DETAIL: (orgId, resourceIdOrSlug) => `/organizations/${orgId}/resources/${resourceIdOrSlug}/public`,
    UPLOAD_RESOURCE_FILE: (orgId, resourceId) => `/organizations/${orgId}/resources/${resourceId}/files`,
    DELETE_RESOURCE_FILE: (orgId, resourceId, fileId) => `/organizations/${orgId}/resources/${resourceId}/files/${fileId}`,
    CREATE_ORGANIZATION_FOLDER: (orgId) => `/organizations/${orgId}/files/folder`,
    UPLOAD_ORGANIZATION_FILE: (orgId) => `/organizations/${orgId}/files/upload`,
    LIST_ORGANIZATION_FILES: (orgId) => `/organizations/${orgId}/files`,
    UPDATE_ORGANIZATION_FILE_OR_FOLDER: (orgId, fileOrFolderId) => `/organizations/${orgId}/files/${fileOrFolderId}`,
    DELETE_ORGANIZATION_FILE_OR_FOLDER: (orgId, fileOrFolderId) => `/organizations/${orgId}/files/${fileOrFolderId}`,
    DOWNLOAD_ORGANIZATION_FILE: (orgId, fileId) => `/organizations/${orgId}/files/${fileId}/download`,
  },
  EVENTS: {
    LIST_PUBLIC_EVENTS: '/events',
    GET_PUBLIC_EVENT_DETAIL: (eventId) => `/events/${eventId}`,
    APPLY_TO_VOLUNTEER_FOR_EVENT: (eventId) => `/events/${eventId}/volunteer-applications`,
    GET_EVENT_PARTICIPANT_USER_IDS: (eventId) => `/event-registrations/event/${eventId}/participants/user-ids`,
    CREATE_EVENT_TAG: '/event-tags',
    LIST_EVENT_TAGS: '/event-tags',
    DELETE_EVENT_TAG: (tagId) => `/event-tags/${tagId}`,
    GET_EVENT_STATISTICS: '/event-statistics',
  },
  POSTS: {
    LIST_PUBLIC_POSTS: '/posts',
    GET_PUBLIC_POST_DETAIL: (postIdOrSlug) => `/posts/${postIdOrSlug}`,
    CREATE_POST_TAG: '/post-tags',
    LIST_POST_TAGS_PAGINATED: '/post-tags',
    GET_POST_TAG_DETAIL: (tagId) => `/post-tags/${tagId}`,
    UPDATE_POST_TAG: (tagId) => `/post-tags/${tagId}`,
    DELETE_POST_TAG: (tagId) => `/post-tags/${tagId}`,
  },
  RESOURCES: {
    LIST_PUBLIC_RESOURCES: '/resources',
    DOWNLOAD_RESOURCE_FILE: (orgId, resourceIdOrSlug, fileIdOrName) => `/resources/download/${orgId}/${resourceIdOrSlug}/${fileIdOrName}`,
  },
  EVENT_REGISTRATIONS: {
    CHECK_IN: '/event-registrations/check-in',
    LIST_MY_REGISTRATIONS: '/me/event-registrations',
    REGISTER_FOR_EVENT: '/me/event-registrations',
    GET_REGISTRATION_DETAIL: (registrationId) => `/event-registrations/${registrationId}`,
    CANCEL_REGISTRATION: (registrationId) => `/event-registrations/${registrationId}`,
    UPDATE_REGISTRATION_PAYMENT_STATUS: (registrationId) => `/event-registrations/${registrationId}/payment-status`,
    LIST_ORGANIZATION_EVENT_REGISTRATIONS: (orgId) => `/organizations/${orgId}/event-registrations`,
  },
  ADMIN: {
    LIST_PENDING_VERIFICATIONS: '/admin/verifications',
    LIST_ALL_VERIFICATIONS: '/admin/verifications',
    GET_VERIFICATION_DETAIL: (reqId) => `/admin/verifications/${reqId}`,
    REVIEW_VERIFICATION: (reqId) => `/admin/verifications/${reqId}/review`,
    GET_VERIFICATION_DOCUMENT: (docId) => `/admin/verifications/documents/${docId}`,
    LIST_EVENT_REGISTRATIONS_ADMIN: (eventId) => `/events/${eventId}/registrations`,
    LIST_ORG_VOLUNTEER_APPLICATIONS: (orgId) => `/admin/organizations/${orgId}/volunteer/applications`,
    LIST_EVENT_VOLUNTEER_APPLICATIONS: (orgId) => `/admin/organizations/${orgId}/event-volunteer-applications`,
    GET_ORG_VOLUNTEER_APPLICATION_DETAIL: (orgId, appId) => `/admin/organizations/${orgId}/volunteer/applications/${appId}`,
    GET_ADMIN_EVENT_VOLUNTEER_APPLICATION_DETAIL: (orgId, eventId, appId) => `/organizations/${orgId}/events/${eventId}/volunteer-applications/${appId}`,
    REVIEW_ORG_VOLUNTEER_APPLICATION: (orgId, appId) => `/admin/organizations/${orgId}/volunteer/applications/${appId}/review`,
    CREATE_ADMIN_USER: '/admin/users',
    LIST_USERS: '/admin/users',
    GET_USER_DETAIL: (userId) => `/admin/users/${userId}`,
    DELETE_USER: (userId) => `/admin/users/${userId}`,
    GET_USER_BY_PHONE: (phone) => `/admin/users/phone/${phone}`,
    GET_USER_BY_EMAIL: (email) => `/admin/users/email/${email}`,
    LIST_SPECIFIC_USER: (userId) => `/admin/users/${userId}`,
  },
  UTILS: {
    LIST_VERIFICATION_TYPES: '/verification-types',
    LIST_GOVERNMENT_FUNDING_TYPES: '/government-funding-types',
  }
};