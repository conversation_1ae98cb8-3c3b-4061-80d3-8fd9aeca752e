package payloads

import "github.com/google/uuid"

// InitiateReAuthOTPRequest defines the request for initiating a re-authentication OTP.
type InitiateReAuthOTPRequest struct {
	// The purpose for which re-authentication is required. e.g., "delete_account", "view_sensitive_data"
	// This helps in logging and potentially applying different logic for different purposes.
	Purpose string `json:"purpose" validate:"required"`
	// Optional: client can specify which channel to use. If empty, user's preference is used.
	Channel *string `json:"channel,omitempty" validate:"omitempty,oneof=sms whatsapp"`
}

// InitiateReAuthOTPResponse defines the response after successfully initiating re-authentication OTP.
type InitiateReAuthOTPResponse struct {
	// A unique identifier for this re-authentication flow.
	FlowID uuid.UUID `json:"flow_id"`
	// The channel through which the OTP was sent.
	Channel string `json:"channel"`
	// Message indicating success.
	Message string `json:"message"`
}

// VerifyReAuthOTPRequest defines the request for verifying a re-authentication OTP.
type VerifyReAuthOTPRequest struct {
	// The unique identifier for the re-authentication flow, returned from the initiate step.
	FlowID string `json:"flow_id" validate:"required"`
	// The one-time password from the user.
	OTP string `json:"otp" validate:"required,len=6,numeric"`
}

// VerifyReAuthOTPResponse defines the response after successfully verifying re-authentication OTP.
type VerifyReAuthOTPResponse struct {
	// Message indicating success.
	Message string `json:"message"`
	// A short-lived token that can be used once to perform the sensitive action.
	// The backend will verify this token before allowing the action.
	ReAuthToken string `json:"reauth_token"`
}
