import React, { useState, useRef, useEffect } from 'react';
import { Form, Input, DatePicker, Button, Typography, InputNumber, AutoComplete, message, Upload, Select, Row, Col, Switch, Divider, Skeleton, Space, Checkbox, Modal, Card } from 'antd';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { InboxOutlined, PlusOutlined, SaveOutlined, EyeOutlined, ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import TipTapEditor from '../../components/TipTapEditor';
import TagManagerModal from '../../components/TagManagerModal';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { eventService } from '../../services/eventService';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { organizationService } from '../../services/organizationService';
import { useAuth } from '../../contexts/AuthContext';

// Initialize dayjs plugins for timezone handling
dayjs.extend(utc);
dayjs.extend(timezone);

// Set default timezone to Hong Kong (matching what's used in the event form)
const DEFAULT_TIMEZONE = 'Asia/Hong_Kong';

const { RangePicker } = DatePicker;
const { Title } = Typography;
const { Dragger } = Upload;
const { Option } = Select;

// Helper function to get tag name based on current language
const getTagName = (tag, language) => {
    if (!tag) return '';

    if (language.startsWith('zh')) {
        if (language.includes('HK')) {
            return tag.name_zh_hk || tag.name_en || '';
        } else {
            return tag.name_zh_cn || tag.name_en || '';
        }
    } else {
        return tag.name_en || '';
    }
};

const EventEdit = () => {
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [description, setDescription] = useState(null);
    const [mediaFiles, setMediaFiles] = useState([]);
    const [videoFiles, setVideoFiles] = useState([]);
    const [isHidden, setIsHidden] = useState(false);
    const [coverImageFileList, setCoverImageFileList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [event, setEvent] = useState(null);
    const [submitting, setSubmitting] = useState(false);
    const { t, i18n } = useTranslation();
    const { currentOrganization } = useOrganization();
    const { user } = useAuth();
    const [eventStatus, setEventStatus] = useState('draft');
    const [availableEventTags, setAvailableEventTags] = useState([]);
    const [loadingTags, setLoadingTags] = useState(false);
    const [originalEventStatus, setOriginalEventStatus] = useState(null);
    const [isHiddenApiCheckbox, setIsHiddenApiCheckbox] = useState(false);
    const [availableVerificationTypes, setAvailableVerificationTypes] = useState([]);
    const [loadingVerificationTypes, setLoadingVerificationTypes] = useState(false);
    const [availableGovernmentFundingTypes, setAvailableGovernmentFundingTypes] = useState([]);
    const [loadingGovernmentFundingTypes, setLoadingGovernmentFundingTypes] = useState(false);
    const [filesToDelete, setFilesToDelete] = useState([]);
    const [removedFilePaths, setRemovedFilePaths] = useState([]);
    const [removedFileIds, setRemovedFileIds] = useState([]);
    const [tagManagerModalVisible, setTagManagerModalVisible] = useState(false);

    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const returnTo = queryParams.get('returnTo');

    const { eventId } = useParams();
    const isEditMode = !!eventId;

    useEffect(() => {
        if (eventId) {
            const fetchEventDetails = async () => {
                setLoading(true);
                try {
                    const data = await eventService.getPublicEventDetail(eventId);

                    // 检查事件是否属于ALL_ORGANIZATION_ID，且用户不是super_admin
                    if (data.organization_id === ALL_ORGANIZATION_ID && user?.role !== 'super_admin') {
                        setLoading(false);
                        navigate('/error/403');
                        return;
                    }

                    setEvent(data);
                    setDescription(data.jsonContent || null);
                    setOriginalEventStatus(data.status);
                    setIsHiddenApiCheckbox(data.status === 'hidden');

                    let startMoment, endMoment, publishedAtMoment;
                    try {
                        startMoment = data.start_time ? dayjs(data.start_time) : null;
                        endMoment = data.end_time ? dayjs(data.end_time) : null;
                        publishedAtMoment = data.published_at ? dayjs(data.published_at) : null;
                        console.log('startMoment', startMoment);
                        console.log('endMoment', endMoment);
                        console.log('publishedAtMoment', publishedAtMoment);
                        if (startMoment && !startMoment.isValid()) startMoment = null;
                        if (endMoment && !endMoment.isValid()) endMoment = null;
                        if (publishedAtMoment && !publishedAtMoment.isValid()) publishedAtMoment = null;

                        // Clear published_at if it's in the past to avoid showing invalid past dates
                        if (publishedAtMoment && publishedAtMoment.isBefore(dayjs())) {
                            console.log('Clearing past published_at date:', publishedAtMoment.format());
                            publishedAtMoment = null;
                            // We don't show a message here to avoid confusion when just opening the form
                        }
                    } catch (err) {
                        console.error('Date parsing error:', err);
                        startMoment = null;
                        endMoment = null;
                        publishedAtMoment = null;
                    }

                    const currentVerificationTypeKeys = Array.isArray(data.verification_type_keys) ? data.verification_type_keys : [];
                    const currentFundingTypes = Array.isArray(data.government_funding_keys) ? data.government_funding_keys : [];

                    setEventStatus(data.status || 'draft');

                    const tagValue = data.tags && data.tags.length > 0 ?
                        { value: data.tags[0].id, label: getTagName(data.tags[0], i18n.language) } :
                        undefined;

                    form.setFieldsValue({
                        title: data.title,
                        location: data.location_full_address || '',
                        location_type: data.location_type || 'physical',
                        location_online_url: data.location_online_url || '',
                        startDate: (startMoment && endMoment) ? [startMoment, endMoment] : undefined,
                        published_at: publishedAtMoment,
                        maxAttendees: data.participant_limit,
                        maxWaitingList: data.waitlist_limit,
                        applicationFee: data.price ? parseFloat(String(data.price).replace(/[^\d.-]/g, '')) : 0,
                        organization: data.organization_id,
                        verification_type_keys: currentVerificationTypeKeys,
                        government_funding_keys: currentFundingTypes,
                        hidden_api_checkbox: data.status === 'hidden',
                        type: tagValue,
                        coverImage: data.processed_image_url ? true : undefined,
                    });

                    setIsHidden(data.status === 'Cancelled');

                    if (data.processed_image_url) {
                        setCoverImageFileList([{
                            uid: '-1',
                            name: 'cover-image.jpg',
                            status: 'done',
                            url: data.processed_image_url,
                        }]);
                    }

                    // Initialize media files from media_items
                    if (data.media_items && Array.isArray(data.media_items)) {
                        const mediaItems = [...data.media_items];
                        // Skip the first item if it's the cover image
                        const coverImageUrl = data.processed_image_url;
                        const itemsToProcess = mediaItems.filter(item =>
                            item.file_path !== coverImageUrl
                        );

                        // Separate media items into images and videos
                        const images = [];
                        const videos = [];

                        itemsToProcess.forEach((item, index) => {
                            const fileItem = {
                                uid: `server-${item.file_path}`,
                                name: item.file_name || `file-${index}`,
                                status: 'done',
                                url: item.file_path,
                                originalItem: item,
                                type: item.file_type
                            };

                            if (item.file_type && item.file_type.startsWith('video/')) {
                                videos.push(fileItem);
                            } else {
                                images.push(fileItem);
                            }
                        });

                        setMediaFiles(images);
                        setVideoFiles(videos);
                    }
                } catch (error) {
                    console.error('Error fetching event details:', error);
                    message.error(t('common.error'));
                } finally {
                    setLoading(false);
                }
            };

            fetchEventDetails();
        } else {
            form.setFieldsValue({
                location_type: 'physical',
                applicationFee: 0,
                maxAttendees: 50,
                maxWaitingList: 100,
                verification_type_keys: [],
                government_funding_keys: [],
                published_at: null,
                hidden_api_checkbox: false,
                title: "",
                type: undefined,
                location: "",
                location_online_url: "",
                startDate: undefined,
                coverImage: undefined,
                organization: currentOrganization && currentOrganization.id !== ALL_ORGANIZATION_ID ? currentOrganization.id : undefined,
            });
            setDescription(null);
            setCoverImageFileList([]);
            setMediaFiles([]);
            setVideoFiles([]);
            setEvent(null);
            setOriginalEventStatus(null);
            setIsHiddenApiCheckbox(false);
            setEventStatus('draft');
            setIsHidden(false);
            setRemovedFilePaths([]);
            setRemovedFileIds([]);
        }
    }, [eventId, form, t, currentOrganization, i18n.language]);

    useEffect(() => {
        const fetchTagsForEditor = async () => {
            setLoadingTags(true);
            try {
                // API now returns tags with name_en, name_zh_hk, name_zh_cn format
                const tagsResponse = await eventService.listEventTags();
                const tags = Array.isArray(tagsResponse) ? tagsResponse : (tagsResponse.data || tagsResponse.items || []);
                setAvailableEventTags(tags.filter(tag => tag.id && (tag.name_en || tag.name_zh_hk || tag.name_zh_cn)));
            } catch (error) {
                console.error("Failed to fetch event tags for editor:", error);
                message.error(t('eventEdit.messages.fetchTagsError'));
                setAvailableEventTags([]);
            } finally {
                setLoadingTags(false);
            }
        };
        fetchTagsForEditor();
    }, [i18n.language, t]);

    useEffect(() => {
        const fetchVerificationTypes = async () => {
            setLoadingVerificationTypes(true);
            try {
                const lang_code = i18n.language.replace('-', '_');
                const response = await eventService.listVerificationTypes({ lang_code: lang_code });
                const types = Array.isArray(response) ? response : (response.data || response.items || []);
                setAvailableVerificationTypes(types.filter(type => type.key && type.name));
            } catch (error) {
                console.error("Failed to fetch verification types:", error);
                message.error(t('eventEdit.messages.fetchVerificationTypesError'));
                setAvailableVerificationTypes([]);
            } finally {
                setLoadingVerificationTypes(false);
            }
        };
        fetchVerificationTypes();
    }, [i18n.language, t]);

    useEffect(() => {
        const fetchGovernmentFundingTypes = async () => {
            setLoadingGovernmentFundingTypes(true);
            try {
                const lang_code = i18n.language.replace('-', '_');
                const response = await eventService.listGovernmentFundingTypes({ lang_code: lang_code });
                const types = Array.isArray(response) ? response : (response.data || response.items || []);
                setAvailableGovernmentFundingTypes(types.filter(type => type.key && type.name));
            } catch (error) {
                console.error("Failed to fetch government funding types:", error);
                message.error(t('eventEdit.messages.fetchGovernmentFundingTypesError'));
                setAvailableGovernmentFundingTypes([]);
            } finally {
                setLoadingGovernmentFundingTypes(false);
            }
        };
        fetchGovernmentFundingTypes();
    }, [i18n.language, t]);

    const rangePresets = [
        {
            label: t('eventEdit.form.dateRange.presets.2hours'),
            value: [dayjs(), dayjs().add(2, 'h')],
        },
        {
            label: t('eventEdit.form.dateRange.presets.4hours'),
            value: [dayjs(), dayjs().add(4, 'h')],
        },
        {
            label: t('eventEdit.form.dateRange.presets.8hours'),
            value: [dayjs(), dayjs().add(8, 'h')],
        },
        {
            label: t('eventEdit.form.dateRange.presets.3days'),
            value: [dayjs(), dayjs().add(3, 'd')],
        },
        {
            label: t('eventEdit.form.dateRange.presets.7days'),
            value: [dayjs(), dayjs().add(7, 'd')],
        },
    ];

    const initialEventData = isEditMode
        ? {
            maxWaitingList: 100,
            organization: currentOrganization && currentOrganization.id !== "00000000-0000-0000-0000-000000000002" ? currentOrganization.id : undefined,
            verification_type_keys: [],
            location_type: 'physical',
            applicationFee: 0,
            maxAttendees: 50,
        }
        : {
            title: "",
            location: "",
            startDate: [],
            maxAttendees: 0,
            maxWaitingList: 0,
            applicationFee: 0,
            description: null,
            organization: undefined,
            verification_type_keys: [],
            location_type: 'physical',
            type: undefined,
        };

    const onFinish = async (fieldsValue) => {
        if (!editorRef.current || !editorRef.current.getText || !editorRef.current.getText().trim()) {
            message.error(t('eventEdit.messages.contentRequired'));
            return;
        }

        const dateRange = fieldsValue['startDate'];
        let startDateISO, endDateISO;

        try {
            if (dateRange && dateRange[0]) startDateISO = dayjs(dateRange[0]).toISOString();
            if (dateRange && dateRange[1]) endDateISO = dayjs(dateRange[1]).toISOString();
        } catch (err) {
            console.error('Error converting dates to ISO:', err);
            message.error(t('eventEdit.messages.invalidDate'));
            return;
        }

        const verificationTypeKeys = Array.isArray(fieldsValue.verification_type_keys) ? fieldsValue.verification_type_keys : [];
        const editorContent = editorRef.current?.getJSON();

        const tagId = fieldsValue.type && fieldsValue.type.value ? fieldsValue.type.value : undefined;

        const basePayload = {
            title: fieldsValue.title,
            jsonContent: editorContent,
            location_type: fieldsValue.location_type || 'physical',
            location_full_address: fieldsValue.location_type === 'physical' || fieldsValue.location_type === 'hybrid' ? fieldsValue.location : undefined,
            location_online_url: fieldsValue.location_type === 'online' || fieldsValue.location_type === 'hybrid' ? fieldsValue.location_online_url : undefined,
            start_time: startDateISO,
            end_time: endDateISO,
            timezone: fieldsValue.timezone || 'Asia/Hong_Kong',
            price: fieldsValue.applicationFee !== undefined ? String(fieldsValue.applicationFee) : undefined,
            participant_limit: fieldsValue.maxAttendees,
            waitlist_limit: fieldsValue.maxWaitingList,
            tag_ids: tagId ? [tagId] : [],
            verification_type_keys: verificationTypeKeys.length > 0 ? verificationTypeKeys : undefined,
            processed_image_url: coverImageFileList.length > 0 && (coverImageFileList[0].url || coverImageFileList[0].response?.url)
                ? coverImageFileList[0].url || coverImageFileList[0].response.url
                : undefined,
            requires_approval_for_registration: false,
        };

        const apiPayload = isEditMode
            ? {
                title: fieldsValue.title,
                jsonContent: editorContent,
                location_type: fieldsValue.location_type,
                location_full_address: fieldsValue.location_type === 'physical' || fieldsValue.location_type === 'hybrid' ? fieldsValue.location : undefined,
                location_online_url: fieldsValue.location_type === 'online' || fieldsValue.location_type === 'hybrid' ? fieldsValue.location_online_url : undefined,
                start_time: startDateISO,
                end_time: endDateISO,
                timezone: fieldsValue.timezone || 'Asia/Hong_Kong',
                price: fieldsValue.applicationFee !== undefined ? String(fieldsValue.applicationFee) : undefined,
                participant_limit: fieldsValue.maxAttendees,
                waitlist_limit: fieldsValue.maxWaitingList,
                tag_ids: tagId ? [tagId] : undefined,
                verification_type_keys: verificationTypeKeys.length > 0 ? verificationTypeKeys : undefined,
                processed_image_url: coverImageFileList.length > 0 && (coverImageFileList[0].url || coverImageFileList[0].response?.url)
                    ? coverImageFileList[0].url || coverImageFileList[0].response.url
                    : undefined,
            }
            : { ...basePayload };

        setSubmitting(true);

        try {
            let orgIdToUse;
            if (isEditMode) {
                orgIdToUse = fieldsValue.organization;
                if (!orgIdToUse || orgIdToUse === "00000000-0000-0000-0000-000000000002") {
                    message.error(t('eventEdit.messages.organizationRequired'));
                    setSubmitting(false);
                    return;
                }
                await eventService.updateEventInOrg(orgIdToUse, eventId, apiPayload);
                message.success(t('eventEdit.messages.updateSuccess'));
                if (eventStatus !== event.status) {
                    await eventService.updateEventStatus(orgIdToUse, eventId, { new_status: eventStatus });
                }
            } else {
                orgIdToUse = fieldsValue.organization;
                if (!orgIdToUse || orgIdToUse === "00000000-0000-0000-0000-000000000002") {
                    message.error(t('eventEdit.messages.organizationRequired'));
                    setSubmitting(false);
                    return;
                }
                const createdEvent = await eventService.createEventInOrg(orgIdToUse, apiPayload);
                message.success(t('eventEdit.messages.createSuccess'));
                if (eventStatus !== 'draft' && createdEvent && createdEvent.id) {
                    await eventService.updateEventStatus(orgIdToUse, createdEvent.id, { new_status: eventStatus });
                }
            }
            navigateAfterSubmit(eventId);
        } catch (error) {
            console.error('Error submitting event:', error.response?.data || error.message || error);
            message.error(isEditMode ? t('eventEdit.messages.updateError') : t('eventEdit.messages.createError'));
        } finally {
            setSubmitting(false);
        }
    };

    const beforeImageUpload = (file) => {
        const isImage = file.type.startsWith('image/');
        if (!isImage) {
            message.error(t('eventEdit.messages.imageUploadError'));
            return Upload.LIST_IGNORE;
        }
        return true;
    };

    const beforeVideoUpload = (file) => {
        const isVideo = file.type === 'video/mp4' || file.type === 'video/avi';
        if (!isVideo) {
            message.error(t('eventEdit.messages.videoUploadError'));
            return Upload.LIST_IGNORE;
        }
        return true;
    };

    const handleMediaChange = (info) => {
        const { status } = info.file;
        if (status === 'done') {
            message.success(`${info.file.name} file uploaded successfully`);
            setMediaFiles(prev => [...prev, info.file]);
        } else if (status === 'error') {
            message.error(`${info.file.name} file upload failed.`);
        }
    };

    const handleVideoChange = (info) => {
        const { status } = info.file;
        if (status === 'done') {
            message.success(`${info.file.name} video uploaded successfully`);
            setVideoFiles(prev => [...prev, info.file]);
        } else if (status === 'error') {
            message.error(`${info.file.name} video upload failed.`);
        }
    };

    const handleCoverImageChange = ({ fileList: newFileList }) => {
        setCoverImageFileList(newFileList.slice(-1));
    };

    const handleMediaFilesChange = ({ fileList }) => {
        setMediaFiles(fileList);
    };

    const handleVideoFilesChange = ({ fileList }) => {
        setVideoFiles(fileList);
    };

    const coverImageUploadProps = {
        onRemove: file => {
            setCoverImageFileList(prevList => {
                const index = prevList.indexOf(file);
                const newFileList = prevList.slice();
                newFileList.splice(index, 1);
                return newFileList;
            });
            if (file.url) {
                setRemovedFilePaths(prev => [...prev, file.url]);
            }
            if (file.originalItem && file.originalItem.id) {
                setRemovedFileIds(prev => [...prev, file.originalItem.id]);
            }
        },
        beforeUpload: beforeImageUpload,
        onChange: handleCoverImageChange,
        fileList: coverImageFileList,
        listType: "picture-card",
        maxCount: 1,
        showUploadList: {
            showRemoveIcon: true,
        }
    };

    const mediaUploadProps = {
        onRemove: file => {
            setMediaFiles(prevList => {
                const index = prevList.indexOf(file);
                const newFileList = prevList.slice();
                newFileList.splice(index, 1);
                return newFileList;
            });
            if (file.url) {
                setRemovedFilePaths(prev => [...prev, file.url]);
            }
            if (file.originalItem && file.originalItem.id) {
                setRemovedFileIds(prev => [...prev, file.originalItem.id]);
            }
        },
        beforeUpload: beforeImageUpload,
        onChange: handleMediaFilesChange,
        fileList: mediaFiles,
        listType: "picture-card",
        multiple: true,
        showUploadList: {
            showPreviewIcon: true,
            showRemoveIcon: true,
        }
    };

    const videoUploadProps = {
        onRemove: file => {
            setVideoFiles(prevList => {
                const index = prevList.indexOf(file);
                const newFileList = prevList.slice();
                newFileList.splice(index, 1);
                return newFileList;
            });
            if (file.url) {
                setRemovedFilePaths(prev => [...prev, file.url]);
            }
            if (file.originalItem && file.originalItem.id) {
                setRemovedFileIds(prev => [...prev, file.originalItem.id]);
            }
        },
        beforeUpload: beforeVideoUpload,
        onChange: handleVideoFilesChange,
        fileList: videoFiles,
        listType: "picture-card",
        multiple: true,
        accept: "video/*",
        showUploadList: {
            showPreviewIcon: true,
            showRemoveIcon: true,
        }
    };

    const validateCoverImage = () => {
        if (coverImageFileList.length === 0) {
            message.error(t('eventEdit.form.coverImage.required'));
            return false;
        }
        return true;
    };

    const editorRef = useRef(null);

    const FormSkeleton = () => (
        <div className="space-y-8">
            <Skeleton.Input active size="large" style={{ width: '100%', height: '40px' }} />

            <div className="pt-4">
                <Skeleton active paragraph={{ rows: 8 }} />
            </div>

            <div className="pt-4">
                <Skeleton active paragraph={{ rows: 3 }} />
            </div>

            <div className="pt-6 flex justify-end">
                <Skeleton.Button active size="large" style={{ width: '120px', marginRight: '16px' }} />
                <Skeleton.Button active size="large" style={{ width: '120px' }} />
            </div>
        </div>
    );

    const handleStatusChange = (newStatus) => {
        setEventStatus(newStatus);
    };

    const handleSubmit = async (targetStatus) => {
        if (!validateCoverImage()) {
            return;
        }

        try {
            const fieldsValue = await form.validateFields();
            setSubmitting(true);

            let orgIdToUse = isEditMode ? event.organization_id : currentOrganization.id;
            if (!orgIdToUse) {
                message.error(t('common.error') + " (Organization ID missing for submission)");
                setSubmitting(false);
                return;
            }

            let currentEventId = eventId;

            if (isEditMode) {
                // Include verification_type_keys in the main payload instead of handling separately
                const apiPayload = constructApiPayload(fieldsValue, targetStatus, [], []);
                // 移除verification_type_keys，单独处理
                delete apiPayload.verification_type_keys;
                await eventService.updateEventInOrg(orgIdToUse, currentEventId, apiPayload);

                // 单独处理验证类型
                const newVerificationTypeKeys = Array.isArray(fieldsValue.verification_type_keys) ? fieldsValue.verification_type_keys : [];
                const existingVerificationTypeKeys = event.verification_type_keys || [];

                // 找出要添加的验证类型
                const typesToAdd = newVerificationTypeKeys.filter(key => !existingVerificationTypeKeys.includes(key));
                // 找出要删除的验证类型
                const typesToRemove = existingVerificationTypeKeys.filter(key => !newVerificationTypeKeys.includes(key));
                // 添加新的验证类型
                for (const typeKey of typesToAdd) {
                    try {
                        const response = await eventService.addVerificationTypeToEvent(orgIdToUse, currentEventId, typeKey);
                    } catch (error) {
                        console.error(`添加验证类型 ${typeKey} 失败:`, error);
                    }
                }

                // 删除移除的验证类型
                for (const typeKey of typesToRemove) {
                    try {
                        const response = await eventService.removeVerificationTypeFromEvent(orgIdToUse, currentEventId, typeKey);
                    } catch (error) {
                        console.error(`删除验证类型 ${typeKey} 失败:`, error);
                    }
                }

            } else {
                const apiPayload = constructApiPayload(fieldsValue, targetStatus, [], []);
                // 移除verification_type_keys，单独处理
                delete apiPayload.verification_type_keys;
                const createdEventResponse = await eventService.createEventInOrg(orgIdToUse, apiPayload);
                currentEventId = createdEventResponse.id;
                if (!currentEventId) {
                    message.error(t('eventEdit.messages.createErrorNoId'));
                    setSubmitting(false);
                    return;
                }

                // 为新创建的事件添加验证类型
                const newVerificationTypeKeys = Array.isArray(fieldsValue.verification_type_keys) ? fieldsValue.verification_type_keys : [];

                for (const typeKey of newVerificationTypeKeys) {
                    try {
                        const response = await eventService.addVerificationTypeToEvent(orgIdToUse, currentEventId, typeKey);
                    } catch (error) {
                        console.error(`添加验证类型 ${typeKey} 失败:`, error);
                    }
                }

                setEvent(prev => ({ ...(prev || {}), id: currentEventId, organization_id: orgIdToUse }));
            }

            let uploadedBannerUrl = null;
            if (coverImageFileList.length > 0 && coverImageFileList[0].originFileObj) {
                try {
                    const bannerUploadResponse = await organizationService.uploadEventMedia(orgIdToUse, currentEventId, coverImageFileList[0].originFileObj, true);
                    uploadedBannerUrl = bannerUploadResponse.file_path;
                } catch (uploadError) {
                    console.error("Banner upload failed:", uploadError);
                    message.error(t('eventEdit.messages.bannerUploadFailed'));
                }
            }

            const uploadedMediaItems = [];
            const newMediaFilesToUpload = mediaFiles.filter(f => f.originFileObj);
            for (const file of newMediaFilesToUpload) {
                try {
                    const mediaUploadResponse = await organizationService.uploadEventMedia(orgIdToUse, currentEventId, file.originFileObj);
                    uploadedMediaItems.push({ file_path: mediaUploadResponse.file_path, file_type: file.type, name: file.name });
                } catch (uploadError) { console.error(`Media ${file.name} upload failed:`, uploadError); }
            }

            const newVideoFilesToUpload = videoFiles.filter(f => f.originFileObj);
            for (const file of newVideoFilesToUpload) {
                try {
                    const videoUploadResponse = await organizationService.uploadEventMedia(orgIdToUse, currentEventId, file.originFileObj);
                    uploadedMediaItems.push({ file_path: videoUploadResponse.file_path, file_type: file.type, name: file.name });
                } catch (uploadError) { console.error(`Video ${file.name} upload failed:`, uploadError); }
            }

            const finalBannerUrls = uploadedBannerUrl
                ? uploadedBannerUrl
                : (coverImageFileList.length > 0 && coverImageFileList[0].url ? coverImageFileList[0].url : '');

            const existingMediaItems = (isEditMode && event?.media_items) ? event.media_items.filter(item => {
                // Skip items that match the cover image
                const coverImageUrl = coverImageFileList.length > 0 ? coverImageFileList[0].url : null;
                if (item.file_path === coverImageUrl) return false;

                // Skip removed media files by path
                if (removedFilePaths.includes(item.file_path)) return false;

                // Skip removed media files by ID
                if (item.id && removedFileIds.includes(item.id)) return false;

                return true;
            }) : [];

            const finalMediaItems = [...existingMediaItems, ...uploadedMediaItems];

            // Construct final API payload with all fields
            const finalApiPayload = constructApiPayload(fieldsValue, targetStatus, finalBannerUrls, finalMediaItems);
            // 确保final payload中不包含verification_type_keys，因为我们已经单独处理了
            delete finalApiPayload.verification_type_keys;

            if (isEditMode) {
                // Create a single array with cover image as first element followed by other media items
                const allMediaPaths = [];

                // Add cover image as first element if it exists
                if (finalBannerUrls) {
                    allMediaPaths.push(finalBannerUrls);
                }

                // Add all other media item paths
                finalApiPayload.media_items.forEach(item => {
                    // Don't duplicate the cover image if it's already included
                    if (item.file_path !== finalBannerUrls) {
                        allMediaPaths.push(item.file_path);
                    }
                });

                console.log('payload检查', finalApiPayload);

                await eventService.updateEventInOrg(orgIdToUse, currentEventId, {
                    mediaeventitem: allMediaPaths
                });
                message.success(t('eventEdit.messages.updateSuccess'));
            } else {
                // For creating a new event, we need to update the event with the media items after creation
                const allMediaPaths = [];

                // Add cover image as first element if it exists
                if (finalBannerUrls) {
                    allMediaPaths.push(finalBannerUrls);
                }

                // Add all other media item paths
                finalApiPayload.media_items.forEach(item => {
                    // Don't duplicate the cover image if it's already included
                    if (item.file_path !== finalBannerUrls) {
                        allMediaPaths.push(item.file_path);
                    }
                });

                // Update the payload to use the proper format for mediaeventitem
                const updatedPayload = {
                    ...finalApiPayload,
                    mediaeventitem: allMediaPaths
                };
                // Remove processed_image_url since we're using mediaeventitem instead
                delete updatedPayload.processed_image_url;
                delete updatedPayload.media_items;

                await eventService.updateEventInOrg(orgIdToUse, currentEventId, updatedPayload);
                message.success(t('eventEdit.messages.createSuccess'));
            }

            navigateAfterSubmit(currentEventId);

        } catch (error) {
            console.error('Error submitting event:', error.response?.data || error.message || error);
            const errorMsg = error.response?.data?.detail || (isEditMode ? t('eventEdit.messages.updateError') : t('eventEdit.messages.createError'));
            message.error(errorMsg);
        } finally {
            setSubmitting(false);
        }
    };

    const constructApiPayload = (fieldsValue, targetStatus, bannerUrl, mediaItems) => {
        const dateRange = fieldsValue['startDate'];
        let startDateISO, endDateISO;
        if (dateRange && dateRange[0]) {
            // Interpret the picker's date/time as Asia/Hong_Kong, then convert to UTC ISO string
            startDateISO = dayjs(dateRange[0]).tz(DEFAULT_TIMEZONE, true).utc().format();
        }
        if (dateRange && dateRange[1]) {
            // Interpret the picker's date/time as Asia/Hong_Kong, then convert to UTC ISO string
            endDateISO = dayjs(dateRange[1]).tz(DEFAULT_TIMEZONE, true).utc().format();
        }

        // Process published_at with explicit timezone handling
        let publishedAtISO = undefined;
        if (fieldsValue.published_at) {
            // Convert the selected local date to the server's expected format with timezone information
            publishedAtISO = dayjs(fieldsValue.published_at)
                .tz(DEFAULT_TIMEZONE, true) // true keeps the same time but changes the timezone
                .utc()                      // convert to UTC for storage
                .format();                  // format as ISO string

            // If scheduled publishing is set, force status to draft
            if (targetStatus === 'published') {
                targetStatus = 'draft';
                message.info(t('eventEdit.messages.scheduledAsDraft'));
            }
        }

        const verificationTypeKeys = Array.isArray(fieldsValue.verification_type_keys)
            ? fieldsValue.verification_type_keys
            : [];
        const currentFundingTypes = Array.isArray(fieldsValue.government_funding_keys) ? fieldsValue.government_funding_keys : [];
        let finalStatus = targetStatus;
        if (fieldsValue.hidden_api_checkbox) finalStatus = 'hidden';

        const tagId = fieldsValue.type && fieldsValue.type.value ? fieldsValue.type.value : undefined;

        const payload = {
            title: fieldsValue.title,
            jsonContent: editorRef.current?.getJSON() || {},
            location_type: fieldsValue.location_type || 'physical',
            location_full_address: (fieldsValue.location_type === 'physical' || fieldsValue.location_type === 'hybrid') ? fieldsValue.location : undefined,
            location_online_url: (fieldsValue.location_type === 'online' || fieldsValue.location_type === 'hybrid') ? fieldsValue.location_online_url : undefined,
            start_time: startDateISO,
            end_time: endDateISO,
            price: fieldsValue.applicationFee !== undefined ? String(fieldsValue.applicationFee) : undefined,
            participant_limit: fieldsValue.maxAttendees,
            waitlist_limit: fieldsValue.maxWaitingList,
            tag_ids: tagId ? [tagId] : [],
            verification_type_keys: verificationTypeKeys.length > 0 ? verificationTypeKeys : undefined,
            government_funding_keys: currentFundingTypes.length > 0 ? currentFundingTypes : undefined,
            processed_image_url: bannerUrl,
            media_items: mediaItems,
            requires_approval_for_registration: false,
            status: finalStatus,
            published_at: publishedAtISO,
        };

        if (!isEditMode) {
            Object.keys(payload).forEach(key => {
                if (payload[key] === undefined) delete payload[key];
            });
        }
        return payload;
    };

    const handlePublish = async () => {
        try {
            // Validate form fields first
            await form.validateFields();

            // Validate cover image
            if (!validateCoverImage()) {
                return;
            }

            // Only show modal if validation passes
            Modal.confirm({
                title: t('eventEdit.confirmations.publish.title'),
                icon: <ExclamationCircleOutlined />,
                content: isHiddenApiCheckbox ? t('eventEdit.confirmations.publish.contentHidden') : t('eventEdit.confirmations.publish.content'),
                okText: t('common.publish'),
                cancelText: t('common.cancel'),
                onOk: () => handleSubmit('published'),
            });
        } catch (error) {

        }
    };

    const handleCancelEventStatus = () => {
        if (!isEditMode && event && event.organization_id && eventId) {
            Modal.confirm({
                title: t('eventEdit.confirmations.cancelEvent.title'),
                icon: <ExclamationCircleOutlined />,
                content: t('eventEdit.confirmations.cancelEvent.content'),
                cancelText: t('common.goBack'),
                onOk: async () => {
                    setSubmitting(true);
                    try {
                        await eventService.updateEventStatus(event.organization_id, eventId, { new_status: "cancelled" });
                        message.success(t('eventEdit.messages.cancelSuccess'));
                        setOriginalEventStatus("cancelled");
                        navigate(`/events/${eventId}`);
                    } catch (error) {
                        message.error(t('eventEdit.messages.cancelError'));
                        console.error("Error cancelling event:", error);
                    } finally {
                        setSubmitting(false);
                    }
                },
            });
        }
    };

    const onHiddenCheckboxChange = (e) => {
        setIsHiddenApiCheckbox(e.target.checked);
        form.setFieldsValue({ hidden_api_checkbox: e.target.checked });
    };

    // Publish time restriction functions
    const disabledPublishDate = (current) => {
        // Cannot select days before today
        return current && current < dayjs().startOf('day');
    };

    // Validate and clear past publish times
    const validatePublishDate = (date) => {
        if (!date) return date;
        const currentTime = dayjs();
        // If the selected date is in the past, return null to clear the field
        if (date.isBefore(currentTime)) {
            message.warning(t('eventEdit.form.publishAt.pastDateWarning'));
            return null;
        }
        console.log('validatePublishDate检查', date);
        return date;
    };

    // Handle DatePicker change to validate the selected date
    const handlePublishDateChange = (date) => {
        const validDate = validatePublishDate(date);
        form.setFieldsValue({ published_at: validDate });
    };

    const disabledPublishTime = (date) => {
        if (!date) return {};

        const currentTime = dayjs();
        // If selected date is today, check if time is at least 5 minutes in the future
        if (date.isSame(currentTime, 'day')) {
            const currentHour = currentTime.hour();
            const currentMinute = currentTime.minute();
            const futureTime = currentTime.add(5, 'minute');
            const futureHour = futureTime.hour();
            const futureMinute = futureTime.minute();

            return {
                disabledHours: () => {
                    // Disable hours before current hour
                    return Array.from({ length: currentHour }, (_, i) => i);
                },
                disabledMinutes: (selectedHour) => {
                    // If selected hour is the current hour, disable minutes before future minute
                    if (selectedHour === currentHour) {
                        return Array.from({ length: futureMinute }, (_, i) => i);
                    }
                    // If selected hour is the future hour (when we cross hour boundary), 
                    // disable minutes before the future minute
                    if (currentHour !== futureHour && selectedHour === futureHour) {
                        return Array.from({ length: futureMinute }, (_, i) => i);
                    }
                    return [];
                }
            };
        }

        return {};
    };

    // Update the navigation after successful submit
    const navigateAfterSubmit = (eventId) => {
        if (returnTo) {
            // Replace any event ID placeholders in the returnTo URL
            const finalReturnTo = returnTo.replace(':eventId', eventId);
            navigate(finalReturnTo);
        } else {
            navigate(isEditMode ? `/events/${eventId}` : '/events');
        }
    };

    // Handle tag creation from TagManagerModal
    const handleTagCreated = (newTag) => {
        // Add the new tag to available tags list
        setAvailableEventTags(prev => [newTag, ...prev]);

        // Auto-select the newly created tag
        const newTagValue = {
            value: newTag.id,
            label: getTagName(newTag, i18n.language)
        };
        form.setFieldsValue({ type: newTagValue });
    };

    // Handle tag deletion from TagManagerModal
    const handleTagDeleted = (deletedTagId) => {
        // Remove from availableEventTags
        setAvailableEventTags(prev => prev.filter(tag => tag.id !== deletedTagId));

        // Clear form selection if the deleted tag was selected
        const currentValue = form.getFieldValue('type');
        if (currentValue && currentValue.value === deletedTagId) {
            form.setFieldsValue({ type: undefined });
        }
    };

    if (loading && !event && !isEditMode) {
        return (
            <div className="max-w-5xl mx-auto px-4 pb-8">
                <FormSkeleton />
            </div>
        );
    }

    return (
        <div className="max-w-5xl mx-auto px-4 pb-8">
            <Form
                form={form}
                layout="vertical"
                initialValues={initialEventData}
                onFinish={(values) => {
                    // Form validation should handle cover image requirement now
                    onFinish(values);
                }}

                className="max-w-4xl mx-auto"
            >
                <Divider orientation="left" style={{ borderColor: '#bdbdbd' }}>{t('eventEdit.form.info.label')}</Divider>

                <Row gutter={[24, 16]}>
                    <Col xs={24} md={8}>
                        <Form.Item
                            name="title"
                            label={t('eventEdit.form.title.label')}
                            rules={[{
                                required: true, message: t('eventEdit.form.title.required'),
                            }, {
                                min: 5, message: t('eventEdit.form.title.minLength'),
                            }, {
                                max: 30, message: t('eventEdit.form.title.maxLength'),
                            }]}
                        >
                            <Input placeholder={t('eventEdit.form.title.placeholder')} />
                        </Form.Item>
                    </Col>

                    <Col xs={12} md={8}>
                        <Form.Item
                            name="type"
                            label={t('eventEdit.form.type.label')}
                            rules={[{ required: true, message: t('eventEdit.form.type.required') }]}
                        >
                            <Select
                                labelInValue
                                placeholder={t('eventEdit.form.type.placeholder')}
                                loading={loadingTags}
                                showSearch
                                optionFilterProp="children"
                                filterOption={(input, option) =>
                                    option.children.toLowerCase().includes(input.toLowerCase())
                                }
                                dropdownRender={(menu) => (
                                    <>
                                        {menu}
                                        <Divider style={{ margin: '8px 0' }} />
                                        <>
                                            <Button
                                                type="text"
                                                icon={<PlusOutlined />}
                                                onClick={() => setTagManagerModalVisible(true)}
                                                style={{ width: '100%' }}
                                            >
                                                {t('eventEdit.form.type.manageAll')}
                                            </Button>
                                        </>
                                    </>
                                )}
                            >
                                {availableEventTags.map(tag => (
                                    <Option key={tag.id} value={tag.id}>
                                        {getTagName(tag, i18n.language)}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>

                    <Col xs={12} md={8}>
                        <Form.Item
                            name="hidden_api_checkbox"
                            label={t('eventEdit.form.hidden.label')}
                            valuePropName="checked"
                        >
                            <Checkbox checked={isHiddenApiCheckbox} onChange={onHiddenCheckboxChange}>
                                {t('eventEdit.form.hidden.checkboxLabel')}
                            </Checkbox>
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={[24, 16]}>
                    <Col xs={24} md={6}>
                        <Form.Item
                            name="location_type"
                            label={t('eventEdit.form.locationType.label')}
                            rules={[{ required: true, message: t('eventEdit.form.locationType.required') }]}
                        >
                            <Select onChange={(value) => form.setFieldsValue({ location_type: value })}>
                                <Option value="physical">{t('eventEdit.form.locationType.options.physical')}</Option>
                                <Option value="online">{t('eventEdit.form.locationType.options.online')}</Option>
                                <Option value="hybrid">{t('eventEdit.form.locationType.options.hybrid')}</Option>
                            </Select>
                        </Form.Item>
                    </Col>

                    <Col xs={24} md={9}>
                        <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) => prevValues.location_type !== currentValues.location_type}
                        >
                            {({ getFieldValue }) => {
                                const locType = getFieldValue('location_type');
                                if (locType === 'physical' || locType === 'hybrid') {
                                    return (
                                        <Form.Item
                                            name="location"
                                            label={t('eventEdit.form.location.label')}
                                            rules={[{ required: true, message: t('eventEdit.form.location.required') }]}
                                        >
                                            <Input placeholder={t('eventEdit.form.location.placeholder')} />
                                        </Form.Item>
                                    );
                                }
                                return null;
                            }}
                        </Form.Item>
                    </Col>

                    <Col xs={24} md={9}>
                        <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) => prevValues.location_type !== currentValues.location_type}
                        >
                            {({ getFieldValue }) => {
                                const locType = getFieldValue('location_type');
                                if (locType === 'online' || locType === 'hybrid') {
                                    return (
                                        <Form.Item
                                            name="location_online_url"
                                            label={t('eventEdit.form.locationOnlineUrl.label')}
                                            rules={[
                                                { required: true, message: t('eventEdit.form.locationOnlineUrl.required') },
                                                { type: 'url', message: t('eventEdit.form.locationOnlineUrl.invalid') }
                                            ]}
                                        >
                                            <Input placeholder={t('eventEdit.form.locationOnlineUrl.placeholder')} />
                                        </Form.Item>
                                    );
                                }
                                return null;
                            }}
                        </Form.Item>
                    </Col>

                    <Col xs={24} md={12}>
                        <Form.Item
                            name="startDate"
                            label={t('eventEdit.form.dateRange.label')}
                            rules={[{ required: true, message: t('eventEdit.form.dateRange.required') }]}
                        >
                            <RangePicker
                                presets={rangePresets}
                                showTime={{ format: 'HH:mm' }}
                                format="YYYY/MM/DD HH:mm"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>

                    <Col xs={24} md={12}>
                        <Form.Item
                            name="published_at"
                            label={t('eventEdit.form.publishAt.label')}
                            tooltip={t('eventEdit.form.publishAt.tooltip')}
                        >
                            <DatePicker
                                showTime
                                format="YYYY/MM/DD HH:mm"
                                style={{ width: '100%' }}
                                needConfirm={false}
                                showNow={false}
                                placeholder={t('eventEdit.form.publishAt.placeholder')}
                                disabledDate={disabledPublishDate}
                                disabledTime={disabledPublishTime}
                                onChange={handlePublishDateChange}
                                suffixIcon={<ClockCircleOutlined />}
                            />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={[24, 16]}>
                    <Col xs={12} md={8}>
                        <Form.Item
                            name="maxAttendees"
                            label={t('eventEdit.form.maxAttendees.label')}
                            rules={[{ required: true, message: t('eventEdit.form.maxAttendees.required') }]}
                        >
                            <InputNumber min={1} style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>

                    <Col xs={12} md={8}>
                        <Form.Item
                            name="maxWaitingList"
                            label={t('eventEdit.form.maxWaitingList.label')}
                            tooltip={t('eventEdit.form.maxWaitingList.tip')}
                            rules={[{ required: true, message: t('eventEdit.form.maxWaitingList.required') }]}
                        >
                            <InputNumber min={0} style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>

                    <Col xs={12} md={8}>
                        <Form.Item
                            name="applicationFee"
                            label={t('eventEdit.form.applicationFee.label')}
                            rules={[{ required: true, message: t('eventEdit.form.applicationFee.required') }]}
                        >
                            <InputNumber
                                min={0}
                                step={0.01}
                                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value.replace(/\$\s?|(,*)/g, '')}
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={[24, 16]}>
                    <Col span={24}>
                        <Form.Item
                            name="verification_type_keys"
                            label={t('eventEdit.form.documents.label')}
                            tooltip={t('eventEdit.form.documents.tooltip')}
                        >
                            <Select
                                mode="multiple"
                                placeholder={t('eventEdit.form.documents.placeholder')}
                                style={{ width: '100%' }}
                                showSearch={false}
                                loading={loadingVerificationTypes}
                            >
                                {availableVerificationTypes.map(type => (
                                    <Option key={type.key} value={type.key}>{type.name}</Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={[24, 16]}>
                    <Col span={24}>
                        <Form.Item
                            name="government_funding_keys"
                            label={t('eventEdit.form.governmentFundingType.label')}
                            tooltip={t('eventEdit.form.governmentFundingType.tooltip')}
                        >
                            <Select
                                mode="multiple"
                                placeholder={t('eventEdit.form.governmentFundingType.placeholder')}
                                style={{ width: '100%' }}
                                showSearch={false}
                                loading={loadingGovernmentFundingTypes}
                                optionFilterProp="children"
                                filterOption={(input, option) =>
                                    option.children.toLowerCase().includes(input.toLowerCase())
                                }
                            >
                                {availableGovernmentFundingTypes.map(type => (
                                    <Option key={type.key} value={type.key}>{type.name}</Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                </Row>

                <Divider orientation="left" style={{ borderColor: '#bdbdbd' }}>{t('eventEdit.form.description.label')}</Divider>
                <div className="mb-6">
                    <Form.Item
                        rules={[{ required: true, message: t('eventEdit.form.description.required') }]}
                    >
                        <div style={{
                            position: 'relative',
                            pointerEvents: !isHidden ? 'auto' : 'none',
                            opacity: !isHidden ? 1 : 0.6
                        }}>
                            <TipTapEditor
                                ref={editorRef}
                                initialContent={description}
                                editable={!(originalEventStatus === 'cancelled' && !isEditMode)}
                            />
                        </div>
                    </Form.Item>
                </div>

                <Divider orientation="left" style={{ borderColor: '#bdbdbd' }}>{t('eventEdit.form.coverImage.label')}</Divider>
                <div className="mb-6">
                    <Form.Item
                        name="coverImage"
                        rules={[
                            {
                                required: true,
                                message: t('eventEdit.form.coverImage.required'),
                                validator: (_, value) => {
                                    if (coverImageFileList.length > 0) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(new Error(t('eventEdit.form.coverImage.required')));
                                }
                            }
                        ]}
                    >
                        <Upload {...coverImageUploadProps}>
                            {coverImageFileList.length === 0 && (
                                <div className="upload-card">
                                    <PlusOutlined />
                                    <div style={{ marginTop: 8 }}>{t('eventEdit.form.coverImage.upload')}</div>
                                </div>
                            )}
                        </Upload>
                        <Typography.Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
                            {t('eventEdit.form.coverImage.tip')}
                        </Typography.Text>
                    </Form.Item>
                </div>

                <Divider orientation="left" style={{ borderColor: '#bdbdbd' }}>{t('eventEdit.form.attachments.label')}</Divider>
                <Row gutter={[24, 16]}>
                    <Col xs={24} md={12}>
                        <Form.Item
                            label={t('eventEdit.form.media.image.label')}
                        >
                            <Upload.Dragger {...mediaUploadProps}>
                                <p className="ant-upload-drag-icon">
                                    <InboxOutlined />
                                </p>
                                <p className="ant-upload-hint">
                                    {t('eventEdit.form.media.image.hint')}
                                </p>
                            </Upload.Dragger>
                        </Form.Item>
                    </Col>

                    <Col xs={24} md={12}>
                        <Form.Item
                            label={t('eventEdit.form.media.video.label')}
                        >
                            <Upload.Dragger {...videoUploadProps}>
                                <p className="ant-upload-drag-icon">
                                    <InboxOutlined />
                                </p>
                                <p className="ant-upload-hint">
                                    {t('eventEdit.form.media.video.hint')}
                                </p>
                            </Upload.Dragger>
                        </Form.Item>
                    </Col>
                </Row>

                <Divider style={{ borderColor: '#bdbdbd' }} />
                <div className="flex justify-end gap-4 mt-8 flex-wrap">
                    <Button
                        type="default"
                        onClick={() => returnTo ? navigate(returnTo.replace(':eventId', eventId)) : navigate(isEditMode ? `/events/${eventId}` : '/events')}
                        disabled={submitting}
                    >
                        {t('common.cancel')}
                    </Button>

                    {isEditMode && originalEventStatus === 'published' && (
                        <Button danger onClick={handleCancelEventStatus} loading={submitting} disabled={submitting}>
                            {t('eventEdit.buttons.cancelEvent')}
                        </Button>
                    )}

                    <Button icon={<SaveOutlined />} onClick={() => handleSubmit('draft')} loading={submitting} disabled={submitting || (originalEventStatus === 'cancelled' && !isEditMode)}>
                        {t('eventEdit.buttons.saveAsDraft')}
                    </Button>
                    <Button type="primary" onClick={handlePublish} loading={submitting} disabled={submitting || (originalEventStatus === 'cancelled' && !isEditMode)}>
                        {t('common.publish')}
                    </Button>
                </div>
            </Form>

            {/* Event Tag Manager Modal */}
            <TagManagerModal
                visible={tagManagerModalVisible}
                onCancel={() => setTagManagerModalVisible(false)}
                onTagCreated={handleTagCreated}
                onTagDeleted={handleTagDeleted}
                getTagName={getTagName}
                type="event"
            />
        </div>
    );
};

export default EventEdit;