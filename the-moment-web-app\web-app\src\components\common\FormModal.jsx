import React from 'react';
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Spin, Alert } from 'antd';
import { useTranslation } from 'react-i18next';

/**
 * A reusable form modal component that handles common modal patterns
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {Function} props.onCancel - Function to call when the modal is canceled
 * @param {Function} props.onSubmit - Function to call when the form is submitted
 * @param {boolean} props.isLoading - Whether the form is currently loading
 * @param {boolean} props.isSubmitting - Whether the form is currently submitting
 * @param {string} props.title - Title of the modal
 * @param {string} props.submitText - Text for the submit button
 * @param {string} props.cancelText - Text for the cancel button
 * @param {Object} props.initialValues - Initial values for the form
 * @param {Array} props.formItems - Form items to render
 * @param {Object} props.error - Error object to display
 * @param {string} props.width - Width of the modal
 * @param {React.ReactNode} props.children - Children elements to render inside the form
 */
const FormModal = ({
  visible,
  onCancel,
  onSubmit,
  isLoading = false,
  isSubmitting = false,
  title,
  submitText,
  cancelText,
  initialValues = {},
  error = null,
  width = 600,
  children,
  form,
  formProps = {},
  ...rest
}) => {
  const { t } = useTranslation();
  const [internalForm] = Form.useForm();
  const formInstance = form || internalForm;

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const values = await formInstance.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  return (
    <Modal
      open={visible}
      title={title}
      onCancel={onCancel}
      width={width}
      maskClosable={!isSubmitting}
      closable={!isSubmitting}
      footer={[
        <Button key="cancel" onClick={onCancel} disabled={isSubmitting}>
          {cancelText || t('common.buttons.cancel')}
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          loading={isSubmitting}
          disabled={isLoading || isSubmitting}
        >
          {submitText || t('common.buttons.submit')}
        </Button>,
      ]}
      {...rest}
    >
      <Spin spinning={isLoading}>
        {error && (
          <Alert
            message={error.message || t('common.errors.general')}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        <Form
          form={formInstance}
          layout="vertical"
          initialValues={initialValues}
          {...formProps}
        >
          {children}
        </Form>
      </Spin>
    </Modal>
  );
};

export default FormModal; 