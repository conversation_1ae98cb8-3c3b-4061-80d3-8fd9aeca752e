// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: event_counts.sql

package db

import (
	"context"

	"github.com/google/uuid"
)

const getEventAttendedCount = `-- name: GetEventAttendedCount :one
SELECT COUNT(*)
FROM event_registrations
WHERE event_id = $1 AND status = 'attended'
`

func (q *Queries) GetEventAttendedCount(ctx context.Context, eventID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getEventAttendedCount, eventID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getEventRegisteredCount = `-- name: GetEventRegisteredCount :one
SELECT COUNT(*)
FROM event_registrations
WHERE event_id = $1 AND status = 'registered'
`

func (q *Queries) GetEventRegisteredCount(ctx context.Context, eventID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getEventRegisteredCount, eventID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getEventWaitlistedCount = `-- name: GetEventWaitlistedCount :one
SELECT COUNT(*)
FROM event_registrations
WHERE event_id = $1 AND status = 'waitlisted'
`

func (q *Queries) GetEventWaitlistedCount(ctx context.Context, eventID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getEventWaitlistedCount, eventID)
	var count int64
	err := row.Scan(&count)
	return count, err
}
