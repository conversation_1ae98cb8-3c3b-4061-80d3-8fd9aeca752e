import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Input, Button, Popconfirm, Skeleton, App, Row, Col, Pagination, Empty, Tag, Spin, Select, DatePicker, Space } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import { EditOutlined, DeleteOutlined, CalendarOutlined, UserOutlined, SearchOutlined, EyeInvisibleOutlined, PictureOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import '../../styles/PostListPage.css';
import { useAuth } from '../../contexts/AuthContext';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { postService } from '../../services/postService';
import { organizationService } from '../../services/organizationService';
import { formatSimpleDateTime } from '../../utils/dateFormatter';
import dayjs from 'dayjs';
const { RangePicker } = DatePicker;
const { Option } = Select;

const PostListPage = () => {
    const { message } = App.useApp();
    const { user } = useAuth();
    const { currentOrganization } = useOrganization();
    const isAdmin = user?.role === 'admin' || user?.role === 'super_admin';
    const isSuperAdmin = user?.role === 'super_admin';
    const [posts, setPosts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [dataFetched, setDataFetched] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [imageLoadingStates, setImageLoadingStates] = useState({});
    const [imageErrorStates, setImageErrorStates] = useState({});
    const navigate = useNavigate();
    const { t, i18n } = useTranslation();
    const pageSize = 12; // 12 cards per page
    const isFirstRender = useRef(true);

    // Filter states
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const [dateRange, setDateRange] = useState(null);
    const [availableTags, setAvailableTags] = useState([]);
    const [loadingTags, setLoadingTags] = useState(false);
    const [selectedTag, setSelectedTag] = useState(null);

    // Helper function to get tag name based on current language
    const getTagName = (tag, language) => {
        if (!tag) return '';
        if (language.startsWith('zh')) {
            if (language.includes('HK')) {
                return tag.name_zh_hk || tag.name_en || '';
            } else {
                return tag.name_zh_cn || tag.name_en || '';
            }
        } else {
            return tag.name_en || '';
        }
    };

    // Debounce search term - using ResourceList.js approach
    useEffect(() => {
        const timerId = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
            setCurrentPage(1); // Reset to first page when search term changes
        }, 500);

        return () => clearTimeout(timerId);
    }, [searchTerm]);

    // Combined filters state
    const [filters, setFilters] = useState({
        searchText: '',
        dateRange: null,
        tagIds: [],
        status: 'published',
    });

    const organizationId = currentOrganization ? currentOrganization.id : ALL_ORGANIZATION_ID;

    // Generate available tags for the filter dropdown from API
    const generateAvailableFilterTags = useCallback(async () => {
        setLoadingTags(true);
        try {
            const tagsResponse = await postService.listPostTags();
            const tagsArray = Array.isArray(tagsResponse) ? tagsResponse : [];
            
            const newTags = tagsArray
                .filter(tag => tag.id && (tag.name_en || tag.name_zh_hk || tag.name_zh_cn))
                .map(tag => ({
                    key: tag.id,
                    value: tag.id,
                    label: getTagName(tag, i18n.language),
                }))
                .filter((tag, index, self) => // Deduplicate by label
                    index === self.findIndex((t) => t.label === tag.label)
                )
                .sort((a, b) => a.label.localeCompare(b.label)); // Sort alphabetically

            setAvailableTags(newTags);
        } catch (error) {
            console.error("Failed to fetch post tags for filter dropdown:", error);
            message.error("Failed to load tags for filtering."); 
            setAvailableTags([]);
        } finally {
            setLoadingTags(false);
        }
    }, [i18n.language, message]);

    // Generate tags on component mount or when language changes
    useEffect(() => {
        generateAvailableFilterTags();
    }, [generateAvailableFilterTags]);

    // Update filters when individual filter values change
    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        setFilters({
            searchText: debouncedSearchTerm,
            dateRange: dateRange ? [dayjs(dateRange[0]).startOf('day'), dayjs(dateRange[1]).endOf('day')] : null,
            tagIds: selectedTag ? [selectedTag] : [],
            status: 'published',
        });
    }, [debouncedSearchTerm, dateRange, selectedTag]);

    const fetchPosts = useCallback(async () => {
        setLoading(true);
        setDataFetched(false);
        try {
            const apiParams = {
                limit: pageSize,
                offset: (currentPage - 1) * pageSize,
                ...(filters.searchText && { search_term: filters.searchText }),
                ...(filters.dateRange && filters.dateRange[0] && { start_date: filters.dateRange[0].toISOString() }),
                ...(filters.dateRange && filters.dateRange[1] && { end_date: filters.dateRange[1].toISOString() }),
                ...(filters.tagIds && filters.tagIds.length > 0 && { tag_ids: filters.tagIds.join(',') }),
                status: 'published',
            };

            if (organizationId) {
                if (organizationId === ALL_ORGANIZATION_ID) {
                    if (isSuperAdmin) {
                        // For super_admin, if "All Organizations" is selected, pass the ALL_ORGANIZATION_ID
                        apiParams.organization_id = ALL_ORGANIZATION_ID; // Using org_id to be consistent with EventList.js
                    }
                   
                    // For other roles (or if backend handles missing org_id as 'all'),
                    // do not add org_id, or ensure postService handles it.
                } else {
                    // A specific organization is selected
                    apiParams.organization_id = organizationId; // Using org_id to be consistent with EventList.js
                    
                    // For non-super admin users, add org_id_2 to include posts from ALL_ORGANIZATION_ID as well
                    if (!isSuperAdmin) {
                        apiParams.organization_id2 = ALL_ORGANIZATION_ID;
                    }
                }
            }
            const response = await postService.listPublicPosts(apiParams);

            const fetchedPostsResult = response.posts || [];
            const fetchedTotal = response.total || 0;

            if (!Array.isArray(fetchedPostsResult)) {
                console.error('Error: Fetched posts data is not an array:', fetchedPostsResult);
                message.error(t('messages.fetchError'));
                setPosts([]);
                setTotal(0);
            } else {
                // Filter out hidden posts for non-admin users
                const filteredPosts = isAdmin ? fetchedPostsResult : fetchedPostsResult.filter(post => post.status !== 'hidden');
                setPosts(filteredPosts);
                setTotal(fetchedTotal);
            }
        } catch (error) {
            message.error(t('messages.fetchError'));
            console.error('Error fetching posts:', error);
            setPosts([]);
            setTotal(0);
        } finally {
            setLoading(false);
            setDataFetched(true);
        }
    }, [currentPage, filters, message, t, pageSize, isAdmin, organizationId, isSuperAdmin]);

    useEffect(() => {
        fetchPosts();
    }, [currentPage, filters, fetchPosts]);

    const handleDateRangeChange = useCallback((dates) => {
        setDateRange(dates);
        setCurrentPage(1); // Reset to first page when filter changes
    }, []);

    const handleTagChange = useCallback((value) => {
        setSelectedTag(value);
        setCurrentPage(1); // Reset to first page when filter changes
    }, []);

    // Define date presets
    const datePresets = [
        { label: t('postList.dateFilter.allDates'), value: null },
        { label: t('postList.dateFilter.last7Days'), value: [dayjs().subtract(7, 'day'), dayjs()] },
        { label: t('postList.dateFilter.last30Days'), value: [dayjs().subtract(30, 'day'), dayjs()] },
        { label: t('postList.dateFilter.thisMonth'), value: [dayjs().startOf('month'), dayjs().endOf('month')] },
        { label: t('postList.dateFilter.lastMonth'), value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] },
    ];

    // Add delete handler
    const handleDelete = async (postId, postOrgId) => {
        if (!isAdmin) {
            message.warning(t('postList.messages.deleteUnauthorized'));
            return;
        }
        if (!postOrgId) {
            message.error(t('postList.messages.orgIdMissingForDelete'));
            console.error("Cannot delete post without its organization_id", postId);
            return;
        }
        try {
            await organizationService.deletePost(postOrgId, postId);
            message.success(t('postList.messages.deleteSuccess'));
            fetchPosts(); // Refresh the list after deletion
        } catch (error) {
            message.error(t('postList.messages.deleteError') + (error.response?.data?.message ? `: ${error.response.data.message}` : ''));
            console.error('Error deleting post:', error);
        }
    };

    const handleImageLoad = useCallback((postId) => {
        setImageLoadingStates(prev => ({
            ...prev,
            [postId]: false
        }));
    }, []);

    const handleImageError = useCallback((postId, e) => {
        // Instead of setting fallback image, mark the image as having an error
        setImageErrorStates(prev => ({
            ...prev,
            [postId]: true
        }));
        setImageLoadingStates(prev => ({
            ...prev,
            [postId]: false
        }));
    }, []);

    const renderPostImage = (post) => {
        // Initialize loading state for this image if not exists
        if (imageLoadingStates[post.id] === undefined) {
            setImageLoadingStates(prev => ({
                ...prev,
                [post.id]: true
            }));
        }

        const imageUrl = post.media_items && post.media_items.length > 0 && post.media_items[0].file_path
            ? post.media_items[0].file_path
            : '';

        return (
            <div className="post-image-container">
                {imageLoadingStates[post.id] && (
                    <div className="post-image-skeleton">
                        <Skeleton.Image active className="post-skeleton" />
                    </div>
                )}
                
                {imageErrorStates[post.id] ? (
                    <div className="h-full w-full flex items-center justify-center bg-gray-200 rounded-t-lg">
                        <div className="text-center">
                            <PictureOutlined className="text-3xl text-gray-400" />
                            <div className="text-gray-500 mt-2">{t('common.noImage')}</div>
                        </div>
                    </div>
                ) : (
                    <img
                        alt={post.title}
                        src={imageUrl}
                        className="post-image"
                        style={{ display: imageLoadingStates[post.id] ? 'none' : 'block' }}
                        onLoad={() => handleImageLoad(post.id)}
                        onError={(e) => handleImageError(post.id, e)}
                    />
                )}
            </div>
        );
    };

    const cardClick = useCallback((postId) => {
        navigate(`/posts/${postId}`);
    }, [navigate]);

    const renderPostCards = () => {
        if (loading || !dataFetched) {
            return (
                <div className="flex justify-center items-center min-h-[400px] w-full">
                    <Spin size="large" />
                </div>
            );
        }

        if (!posts.length && dataFetched) {
            return (
                <div className="flex justify-center items-center min-h-[400px] w-full bg-gray-50 rounded-lg">
                    <Empty description={t('postList.noPosts')} />
                </div>
            );
        }

        return (
            <>
                <Row gutter={[16, 16]} className="w-full">
                    {posts.map((post) => (
                        <Col xs={24} sm={12} md={8} lg={6} key={post.id}>
                            <Card
                                hoverable
                                className={`post-list-card ${post.status === 'hidden' ? 'border-gray-800 bg-gray-800' : ''}`}
                                cover={
                                    <div className={`relative ${post.status === 'hidden' ? 'grayscale' : ''} overflow-hidden rounded-t-lg`}>
                                        {renderPostImage(post)}
                                        {post.status === 'hidden' && (
                                            <div className="absolute inset-0 bg-gray-900/50 flex items-center justify-center">
                                                <div className="text-white text-sm flex items-center gap-1.5">
                                                    <EyeInvisibleOutlined />
                                                    <span>{t('postList.hiddenPost')}</span>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                }
                                onClick={() => cardClick(post.id)}
                                actions={isAdmin && post.organization_id && 
                                    // Hide edit actions for non-super_admin users when post belongs to ALL_ORGANIZATION_ID
                                    !(post.organization_id === ALL_ORGANIZATION_ID && user?.role !== 'super_admin') ? [
                                    <Link to={`/posts/${post.id}/edit?returnTo=list`} onClick={e => e.stopPropagation()}>
                                        <EditOutlined key="edit" className="text-gray-500 hover:text-blue-500" />
                                    </Link>,
                                    <Popconfirm
                                        title={t('postList.deleteConfirm.title')}
                                        onConfirm={(e) => {
                                            e.stopPropagation();
                                            handleDelete(post.id, post.organization_id);
                                        }}
                                        onCancel={e => e.stopPropagation()}
                                        okText={t('postList.deleteConfirm.okText')}
                                        cancelText={t('postList.deleteConfirm.cancelText')}
                                    >
                                        <DeleteOutlined
                                            key="delete"
                                            onClick={e => e.stopPropagation()}
                                            className="text-gray-500 hover:text-red-500"
                                        />
                                    </Popconfirm>
                                ] : undefined}
                            >
                                <Card.Meta
                                    title={
                                        <div className={`text-lg font-bold truncate ${post.status === 'hidden' ? 'text-gray-600' : 'text-gray-800'}`}>
                                            {post.title}
                                        </div>
                                    }
                                    description={
                                        <div className={`space-y-2 mt-2 ${post.status === 'hidden' ? 'text-gray-500' : 'text-gray-600'}`}>
                                            <div className="flex items-center text-sm">
                                                <CalendarOutlined className="mr-2" />
                                                <span className="truncate">{formatSimpleDateTime(post.published_at || post.created_at, i18n.language)}</span>
                                            </div>
                                            <div className="flex items-center text-sm">
                                                <UserOutlined className="mr-2" />
                                                <span className="truncate">{post.author_display_name || t('common.unknown')}</span>
                                            </div>
                                            {post.tag_ids && post.tag_ids.length > 0 && (
                                                <div className="mt-2">
                                                    {post.tag_ids.map(tagId => {
                                                        const postTag = post.tags?.find(t => t.id === tagId);
                                                        return <Tag key={tagId} color="blue">{getTagName(postTag, i18n.language)}</Tag>;
                                                    })}
                                                </div>
                                            )}
                                        </div>
                                    }
                                />
                            </Card>
                        </Col>
                    ))}
                </Row>

                {/* Only show pagination if there are posts */}
                <div className="flex justify-center mt-8">
                    <Pagination
                        current={currentPage}
                        align="center"
                        showTotal={total => t('common.totalItems', { total: total })}
                        pageSize={pageSize}
                        total={total}
                        onChange={(page) => setCurrentPage(page)}
                    />
                </div>
            </>
        );
    };

    return (
        <div className="py-6 px-4 md:px-6 bg-white">
            <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
                <div className="w-full md:w-auto flex-grow">
                    <Space wrap size={8} className="w-full">
                        <Input
                            placeholder={t('postList.search.placeholder')}
                            prefix={<SearchOutlined />}
                            onChange={e => setSearchTerm(e.target.value)}
                            style={{ width: '100%', maxWidth: 300 }}
                            size='large'
                            allowClear
                        />
                        <RangePicker
                            presets={datePresets}
                            value={dateRange}
                            onChange={handleDateRangeChange}
                            allowClear
                            size="large"
                            format="YYYY-MM-DD"
                        />
                        <Select
                            allowClear
                            showSearch={false}
                            style={{ width: 240 }}
                            placeholder={t('postList.tags.placeholder')}
                            onChange={handleTagChange}
                            value={selectedTag}
                            loading={loadingTags}
                            size="large"
                            optionLabelProp="label"
                            options={availableTags.map(option => ({ // Ensure options are correctly formatted for AntD v5+
                                key: option.key,
                                value: option.value,
                                label: option.label
                            }))}
                        />
                    </Space>
                </div>
            </div>
            
            {renderPostCards()}

        </div>
    );
};

export default PostListPage;