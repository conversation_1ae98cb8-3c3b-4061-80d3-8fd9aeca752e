-- Create a user_role ENUM type
CREATE TYPE user_role AS ENUM ('superadmin', 'admin', 'user');

-- Add the role column to the users table
ALTER TABLE users ADD COLUMN role user_role NOT NULL DEFAULT 'user';

-- Update the roles based on existing data
-- 1. Set role to 'admin' for all staff members first
UPDATE users SET role = 'admin' WHERE is_staff = TRUE;

-- 2. Set role to 'superadmin' for the owner of the default organization
UPDATE users
SET role = 'superadmin'
WHERE id = (
  SELECT owner_user_id
  FROM organizations
  WHERE is_default_org = TRUE
  LIMIT 1
);

-- Add an index on the new role column
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
