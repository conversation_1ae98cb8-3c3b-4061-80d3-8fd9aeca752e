version: "2"
sql:
  - schema: "migrations"
    queries: "queries"
    engine: "postgresql"
    gen:
      go:
        package: "db"
        out: "."
        sql_package: "pgx/v5"
        emit_interface: true
        emit_json_tags: true
        emit_empty_slices: true
        emit_exact_table_names: false
        emit_prepared_queries: true
        omit_unused_structs: true
        emit_db_tags: true
        emit_all_enum_values: true
        emit_enum_valid_method: true
        sql_driver: "github.com/jackc/pgx/v5" # Use pgx/v5 for PostgreSQL
        emit_pointers_for_null_types: true
        overrides:
          - db_type: "uuid"
            go_type: "github.com/google/uuid.UUID"
          - db_type: "uuid"
            go_type:
              import: "github.com/google/uuid"
              type: "UUID"
              pointer: true
            nullable: true
          - db_type: "timestamptz"
            go_type:
              import: "time"
              type: "Time"
          - db_type: "timestamptz"
            go_type:
              import: "time"
              type: "Time"
              pointer: true
            nullable: true
          - db_type: "date"
            go_type:
              import: "time"
              type: "Time"
          - db_type: "date"
            go_type:
              import: "time"
              type: "Time"
            nullable: true
          # - db_type: "pg_catalog.int4"
          #   nullable: true
          #   go_type:
          #     type:  "int"
        #   - db_type: "text"
        #     go_type:
        #       import: "github.com/jackc/pgx/v5/pgtype"
        #       type: "Text"
        #     nullable: true
        # Add override for post_status enum if sqlc doesn't handle it correctly by default
        # - db_type: "post_status" 
        #   go_type: 
          # Add override for post_status enum if sqlc doesn't handle it correctly by default
          # - db_type: "post_status" 
          #   go_type: 
          #     import: "Membership-SAAS-System-Backend/internal/models" # Assuming your enum is here
          #     type: "PostStatus" 