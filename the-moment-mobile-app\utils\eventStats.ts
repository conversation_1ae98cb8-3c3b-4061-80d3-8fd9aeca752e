// import { mockEventsResponse } from '@/mockData/mockEvents'; // Removed unused import
import { userProfileStore, userStatisticsStore } from 'stores/user_store';
import { ProfileResponse, StatisticsPullResponse, MonthlyAttendedEventPayload, TopAttendedEventPayload } from '@/api/api_config';

interface EventStats {
  totalEvents: number;
  totalHours: number;
  volunteerEvents: number;
  joinedDays: number;
  monthlyEvents: Array<{
    month: string;
    count: number;
  }>;
  categories: Array<{
    name: string;
    count: number;
    icon: string;
  }>;
}

// 类别对应的图标映射
// 使用 MaterialCommunityIcons 的图标名称
const CATEGORY_ICONS: { [key: string]: string } = {
  community: 'account-group',    // 社区服务 - 群组图标
  technology: 'laptop',          // 科技创新 - 笔记本电脑图标
  education: 'school',           // 教育发展 - 学校图标
  culture: 'palette',           // 文化艺术 - 调色板图标
  environment: 'leaf',          // 环境保护 - 树叶图标
  health: 'heart-pulse',        // 健康医疗 - 心跳图标
  business: 'briefcase',        // 商业活动 - 公文包图标
  design: 'pencil-ruler',       // 设计创作 - 尺规图标
  marketing: 'trending-up',     // 市场营销 - 上升趋势图标
  infrastructure: 'city',       // 基础建设 - 城市图标
  youth: 'human-capacity-increase', // 青年发展 - 人物成长图标
};

export function calculateEventStats(): EventStats | null {
  const userProfile = userProfileStore.getState().profile;
  const userStats = userStatisticsStore.getState().statistics;

  if (!userProfile || !userStats) {
    // console.log('User profile or stats not available');
    return null; // Or handle this case as appropriate for your application
  }

  const now = new Date();
  const joinDate = new Date(userProfile.created_at); // Use created_at from ProfileResponse
  const joinedDays = Math.floor((now.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24));

  // Data from userStats
  const totalEvents = userStats.totalEvents;
  const volunteerEvents = userStats.volunteerEvents;
  // totalHours is not directly available in StatisticsPullResponse, 
  // you might need to calculate it differently if it's required, or remove it.
  // For now, let's assume it's not strictly needed or will be handled elsewhere.
  // const totalHours = completedEvents.reduce((total, event) => total + (event.actualHours || (event.status === 'completed' ? 2 : 0)), 0);
  // For now, setting totalHours to 0 as it's not directly available in StatisticsPullResponse.
  // You might need to adjust this based on how you want to calculate or display totalHours.
  const totalHours = 0;


  // Calculate monthly events using data from userStats
  const monthlyEvents = calculateMonthlyEvents(userStats.monthlyAttendedEvents);

  // Calculate category distribution using data from userStats
  const categories = calculateCategoryDistribution(userStats.topAttendedEventTags);

  return {
    totalEvents,
    totalHours, // This will be 0 for now
    volunteerEvents,
    joinedDays,
    monthlyEvents,
    categories,
  };
}

function calculateMonthlyEvents(monthlyData: MonthlyAttendedEventPayload[]): Array<{ month: string; count: number }> {
  const now = new Date();
  const sixMonthsAgo = new Date(now);
  sixMonthsAgo.setMonth(now.getMonth() - 5);

  const months: Array<{ name: string; date: Date }> = [];
  for (let i = 0; i < 6; i++) {
    const date = new Date(now);
    date.setMonth(now.getMonth() - i);
    months.unshift({
      name: date.toLocaleString('en-US', { month: 'short' }),
      date: date
    });
  }

  const monthCounts = new Map<string, number>();
  months.forEach(month => monthCounts.set(month.name, 0));

  monthlyData.forEach(event => {
    // Assuming event.month is a string like "Jan", "Feb", etc.
    // And event.year is a number for the year
    // The API sends month and year, let's ensure we handle this correctly.
    // The current logic expects month names like "Jan". If API sends full month names or numbers, adjust accordingly.
    // For now, assuming event.month is directly usable or can be mapped.
    // We also need to filter by year if the data spans multiple years.
    // The API sends "month": "YYYY-MM", so we need to parse it.
    const eventDate = new Date(event.month + "-01"); // Create a date object from "YYYY-MM"
    if (eventDate >= sixMonthsAgo && eventDate <= now) {
      const monthName = eventDate.toLocaleString('en-US', { month: 'short' });
      if (monthCounts.has(monthName)) {
        monthCounts.set(monthName, (monthCounts.get(monthName) || 0) + event.count);
      }
    }
  });
  
  return months.map(month => ({
    month: month.name,
    count: monthCounts.get(month.name) || 0,
  }));
}

function calculateCategoryDistribution(categoryData: TopAttendedEventPayload[]): Array<{ name: string; count: number; icon: string }> {
  return categoryData
    .map(category => ({
      name: category.name,
      count: category.count,
      icon: CATEGORY_ICONS[category.name.toLowerCase() as keyof typeof CATEGORY_ICONS] || 'help-circle', // Ensure lowercase for matching
    }))
    .sort((a, b) => b.count - a.count);
} 