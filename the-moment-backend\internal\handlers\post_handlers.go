package handlers

import (
	"Membership-SAAS-System-Backend/internal/authn" // Added for GetUserIDFromContext
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services" // Correct import path
	utils "Membership-SAAS-System-Backend/internal/utils"
	"fmt"
	"net/http"
	"strings"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

const DefaultPostsLimit = 10
const MaxPostsLimit = 100

// parseTagIDsQueryParam parses a comma-separated string of tag IDs from query params.
func parseTagIDsQueryParam(c echo.Context, paramName string) ([]uuid.UUID, error) {
	tagIDsStr := c.QueryParam(paramName)
	if tagIDsStr == "" {
		return nil, nil
	}

	idStrs := strings.Split(tagIDsStr, ",")
	tagIDs := make([]uuid.UUID, 0, len(idStrs))
	for _, idStr := range idStrs {
		trimmedIDStr := strings.TrimSpace(idStr)
		if trimmedIDStr == "" {
			continue
		}
		id, err := uuid.Parse(trimmedIDStr)
		if err != nil {
			return nil, fmt.Errorf("invalid UUID format for tag ID '%s': %w", trimmedIDStr, err)
		}
		tagIDs = append(tagIDs, id)
	}
	return tagIDs, nil
}

type PostHandler struct {
	Service   *services.PostService
	Validator *utils.RequestValidator
	BaseURL   string
}

func NewPostHandler(service *services.PostService, validator *utils.RequestValidator, baseURL string) *PostHandler {
	return &PostHandler{
		Service:   service,
		Validator: validator,
		BaseURL:   baseURL,
	}
}

// CreatePost godoc
// @Summary Create a new post within an organization
// @Description Author ID is automatically set from the authenticated user's token. The status of the post can be 'draft', 'published', or 'hidden'. If media items are included and the first one is an image, its URL might be used as the post's cover_image_url by the service.
// @Tags Content Management (Posts - Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param body body payloads.CreatePostRequest true "Post content, status, tag IDs, and optional media items"
// @Success 201 {object} payloads.PostResponse "Newly created post with media items and cover image URL"
// @Failure 400 {object} utils.ErrorResponse "Invalid request body or organization ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized - User ID not found in token"
// @Failure 500 {object} utils.ErrorResponse "Failed to create post or retrieve full details after creation"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/posts [post]
func (h *PostHandler) CreatePost(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	var req payloads.CreatePostRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body", err)
	}
	req.OrganizationID = orgID

	if err := h.Validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	// Get UserID from the JWT token in the context
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		log.Ctx(c.Request().Context()).Warn().Msg("CreatePost: UserID not found in context or is Nil.")
		return utils.HandleError(c, http.StatusUnauthorized, "User ID not found in token or invalid.", nil)
	}

	dbPost, err := h.Service.CreatePost(c.Request().Context(), userID, req) // Pass userID to the service
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to create post")
		// Check for specific errors from service if needed, e.g., "author ID (userID) cannot be nil"
		if strings.Contains(err.Error(), "author ID (userID) cannot be nil") {
			return utils.HandleError(c, http.StatusBadRequest, "Author ID is required and was not found.", err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to create post", err)
	}

	// Fetch the created post with media (even if empty initially) for consistent response
	createdPostResp, err := h.Service.GetPostByID(c.Request().Context(), dbPost.ID)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to fetch newly created post for response")
		return utils.HandleError(c, http.StatusInternalServerError, "Post created but failed to retrieve full details", err)
	}

	// Prepend base URL to media file paths
	if createdPostResp != nil {
		h.prependBaseURLToMediaPaths(createdPostResp)
	}

	return c.JSON(http.StatusCreated, createdPostResp)
}

// GetOrgPost godoc
// @Summary Get details of a specific post (admin view, includes media)
// @Description Retrieves full details of a specific post belonging to an organization, including associated media items. Does not filter by status (shows drafts, hidden, etc.).
// @Tags Content Management (Posts - Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param postId path string true "Post ID (UUID)" Format(uuid)
// @Success 200 {object} payloads.PostResponse "Post details with media items"
// @Failure 400 {object} utils.ErrorResponse "Invalid post ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Post not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve post"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/posts/{postId} [get]
func (h *PostHandler) GetOrgPost(c echo.Context) error {
	// orgIDStr := c.Param("orgId") // orgId is in path but not strictly needed if postId is global
	postIDStr := c.Param("postId")
	postID, err := uuid.Parse(postIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid post ID format", err)
	}

	postResp, err := h.Service.GetPostByID(c.Request().Context(), postID)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Post not found", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to get post by ID")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve post", err)
	}
	h.prependBaseURLToMediaPaths(postResp)
	return c.JSON(http.StatusOK, postResp)
}

// UpdatePost godoc
// @Summary Update an existing post
// @Description Updates the content, status, tags, or other attributes of an existing post. Requires user to be the author or have appropriate permissions.
// @Tags Content Management (Posts - Admin)
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param postId path string true "Post ID (UUID)" Format(uuid)
// @Param body body payloads.UpdatePostRequest true "Post content, status, tag IDs, etc. to update"
// @Success 200 {object} payloads.PostResponse "Updated post details"
// @Failure 400 {object} utils.ErrorResponse "Invalid request body or ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized - User cannot update this post"
// @Failure 404 {object} utils.ErrorResponse "Post not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to update post or retrieve details after update"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/posts/{postId} [put]
func (h *PostHandler) UpdatePost(c echo.Context) error {
	// orgIDStr := c.Param("orgId") // Validate orgId owns postId if necessary
	postIDStr := c.Param("postId")
	postID, err := uuid.Parse(postIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid post ID format", err)
	}

	var req payloads.UpdatePostRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request body", err)
	}

	if err := h.Validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	// Get UserID from the JWT token in the context
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		log.Ctx(c.Request().Context()).Warn().Msg("UpdatePost: UserID not found in context or is Nil.")
		return utils.HandleError(c, http.StatusUnauthorized, "User ID not found in token or invalid for update.", nil)
	}

	_, err = h.Service.UpdatePost(c.Request().Context(), postID, userID, req) // Pass userID
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Post not found for update", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to update post")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to update post", err)
	}

	updatedPostResp, err := h.Service.GetPostByID(c.Request().Context(), postID)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to fetch updated post for response")
		return utils.HandleError(c, http.StatusInternalServerError, "Post updated but failed to retrieve full details", err)
	}
	h.prependBaseURLToMediaPaths(updatedPostResp)
	return c.JSON(http.StatusOK, updatedPostResp)
}

// DeletePost godoc
// @Summary Delete a post
// @Description Deletes a specific post. Requires user to be the author or have appropriate permissions.
// @Tags Content Management (Posts - Admin)
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param postId path string true "Post ID (UUID)" Format(uuid)
// @Success 204 "No Content"
// @Failure 400 {object} utils.ErrorResponse "Invalid post ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized - User cannot delete this post"
// @Failure 404 {object} utils.ErrorResponse "Post not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to delete post"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/posts/{postId} [delete]
func (h *PostHandler) DeletePost(c echo.Context) error {
	// orgIDStr := c.Param("orgId") // Validate orgId owns postId if necessary
	postIDStr := c.Param("postId")
	postID, err := uuid.Parse(postIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid post ID format", err)
	}

	err = h.Service.DeletePost(c.Request().Context(), postID)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Post not found for deletion", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to delete post")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to delete post", err)
	}

	return c.NoContent(http.StatusNoContent)
}

// ListOrgPosts godoc
// @Summary List posts for an organization (admin view, includes drafts)
// @Description Retrieves a list of posts belonging to a specific organization. Includes drafts and hidden posts. Supports pagination and filtering by tag IDs and status.
// @Tags Content Management (Posts - Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param limit query int false "Limit number of results per page" default(10) minimum(1) maximum(100)
// @Param offset query int false "Offset for pagination" default(0) minimum(0)
// @Param tag_ids query string false "Comma-separated list of Tag IDs (UUIDs) to filter by"
// @Param status query string false "Filter by post status (e.g., 'draft', 'published', 'hidden')" Enums(draft, published, hidden)
// @Param start_date query string false "Filter posts created on or after this date (RFC3339 format, e.g., 2023-01-01T00:00:00Z)" Format(date-time)
// @Param end_date query string false "Filter posts created on or before this date (RFC3339 format, e.g., 2023-01-01T23:59:59Z)" Format(date-time)
// @Param search_term query string false "Search term to filter posts by title or content (case-insensitive)"
// @Success 200 {array} payloads.PostResponse "List of posts belonging to the organization"
// @Header 200 {string} X-Total-Count "Total number of posts matching the query"
// @Failure 400 {object} utils.ErrorResponse "Invalid organization ID format or query parameters"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve posts"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/posts [get]
func (h *PostHandler) ListOrgPosts(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgId_str", orgIDStr).Msg("ListOrgPosts: Invalid organization ID format")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	var listParams payloads.ListPostsParams
	// Bind query parameters that match ListPostsParams fields.
	// This will handle start_date, end_date, search_term, and potentially limit, offset, status if not overridden.
	if err := c.Bind(&listParams); err != nil {
		// Log the bind error but proceed, as some parameters are handled manually with defaults.
		log.Ctx(c.Request().Context()).Warn().Err(err).Msg("ListOrgPosts: Failed to bind query params; will proceed with manual parsing and defaults where applicable.")
	}

	// Manual parsing for limit and offset with defaults (overrides anything from bind if names conflicted, or sets if not bound)
	limit := utils.ParseQueryInt(c, "limit", DefaultPostsLimit)
	offset := utils.ParseQueryInt(c, "offset", 0)
	if limit <= 0 || limit > MaxPostsLimit {
		limit = DefaultPostsLimit
	}
	if offset < 0 {
		offset = 0
	}
	listParams.Limit = limit
	listParams.Offset = offset

	// Manual parsing for tag_ids
	tagIDs, err := parseTagIDsQueryParam(c, "tag_ids")
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("ListOrgPosts: Invalid 'tag_ids' query parameter")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid 'tag_ids' query parameter", err)
	}
	listParams.TagIDs = tagIDs

	// Manual parsing and validation for status
	statusStr := c.QueryParam("status")
	var statusPtr *string
	if statusStr != "" {
		// Basic validation, can be enhanced with enum check if PostStatus is an enum type
		if statusStr != "draft" && statusStr != "published" && statusStr != "hidden" {
			log.Ctx(c.Request().Context()).Warn().Str("status", statusStr).Msg("ListOrgPosts: Invalid 'status' query parameter value")
			return utils.HandleError(c, http.StatusBadRequest, "Invalid 'status' query parameter value. Allowed values: draft, published, hidden", nil)
		}
		statusPtr = &statusStr
	}
	listParams.Status = statusPtr

	// StartDate, EndDate, and SearchTerm are expected to be populated by c.Bind(&listParams) above.
	// If c.Bind fails for these, they will remain nil in listParams, which is the desired behavior for optional filters.

	log.Ctx(c.Request().Context()).Debug().
		Str("org_id", orgID.String()).
		Int("limit", listParams.Limit).
		Int("offset", listParams.Offset).
		Interface("tag_ids", listParams.TagIDs).
		Interface("status", listParams.Status).
		Interface("start_date", listParams.StartDate).   // Value from Bind
		Interface("end_date", listParams.EndDate).       // Value from Bind
		Interface("search_term", listParams.SearchTerm). // Value from Bind
		Msg("ListOrgPosts: Parsed parameters")

	// listParams struct is now fully populated.
	// Note: listParams.OrganizationID might be populated by c.Bind if 'organization_id' is in query.
	// However, the service call explicitly uses the orgID from the path.
	posts, totalCount, err := h.Service.ListPostsByOrganization(c.Request().Context(), orgID, listParams)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("org_id", orgID.String()).Msg("ListOrgPosts: Failed to list organization posts from service")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve posts for organization", err)
	}

	log.Ctx(c.Request().Context()).Debug().
		Str("org_id", orgID.String()).
		Int("returned_post_count", len(posts)).
		Int64("total_count_from_service", totalCount).
		Msg("ListOrgPosts: Received response from service")

	for idx := range posts {
		h.prependBaseURLToMediaPaths(&posts[idx])
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, posts)
}

// ListPublicOrgPosts godoc
func (h *PostHandler) ListPublicOrgPosts(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	limit := utils.ParseQueryInt(c, "limit", DefaultPostsLimit)
	offset := utils.ParseQueryInt(c, "offset", 0)
	if limit <= 0 || limit > MaxPostsLimit {
		limit = DefaultPostsLimit
	}

	tagIDs, err := parseTagIDsQueryParam(c, "tag_ids")
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid 'tag_ids' query parameter", err)
	}

	listParams := payloads.ListPostsParams{
		Limit:  limit,
		Offset: offset,
		TagIDs: tagIDs,
	}

	posts, totalCount, err := h.Service.ListPublishedPostsByOrganization(c.Request().Context(), orgID, listParams)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to list published organization posts")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve posts for organization", err)
	}

	for idx := range posts {
		h.prependBaseURLToMediaPaths(&posts[idx])
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, posts)
}

// --- Public Post Endpoints ---

// ListPublicPosts godoc
// @Summary List all published posts across organizations
// @Description Retrieves a paginated list of all posts that are marked as 'published'. Supports filtering by organization ID, tag IDs, a date range based on 'published_at', and a search term for the title.
// @Tags Content Management (Posts - Public)
// @Produce json
// @Param limit query int false "Limit number of results per page" default(10) minimum(1) maximum(100)
// @Param offset query int false "Offset for pagination" default(0) minimum(0)
// @Param org_id query string false "Filter by Organization ID (UUID)"
// @Param org_id2 query string false "Filter by a second Organization ID (UUID)"
// @Param tag_ids query []string false "Filter by Tag IDs (UUIDs) - comma separated"
// @Param start_date query string false "Filter posts published on or after this date (RFC3339 format, e.g., 2023-01-01T00:00:00Z)" Format(date-time)
// @Param end_date query string false "Filter posts published on or before this date (RFC3339 format, e.g., 2023-01-01T23:59:59Z)" Format(date-time)
// @Param search_term query string false "Search term to filter posts by title (case-insensitive)"
// @Success 200 {array} payloads.PostResponse "List of published posts"
// @Header 200 {string} X-Total-Count "Total number of published posts matching the query"
// @Failure 400 {object} utils.ErrorResponse "Invalid query parameter format"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve published posts"
// @Router /posts [get]
func (h *PostHandler) ListPublicPosts(c echo.Context) error {
	ctx := c.Request().Context()
	var listParams payloads.ListPostsParams

	// Bind query parameters to listParams
	if err := c.Bind(&listParams); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to bind query params for ListPublicPosts")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid query parameters", err)
	}

	// Validate common list parameters (Limit, Offset)
	if listParams.Limit <= 0 {
		listParams.Limit = DefaultPostsLimit
	}
	if listParams.Limit > MaxPostsLimit {
		listParams.Limit = MaxPostsLimit
	}
	if listParams.Offset < 0 {
		listParams.Offset = 0
	}

	// Parse org_id separately as it's a string in query but UUID in struct.
	// c.Bind handles OrganizationID if it's *uuid.UUID and query param is string.
	// If listParams.OrganizationID is nil after bind, it means org_id was not provided or was empty.

	// Parse tag_ids separately as it's comma-separated.
	// c.Bind might handle []uuid.UUID if query param is `tag_ids=uuid1,uuid2`
	// but let's ensure robust parsing using our utility.
	tagIDs, err := parseTagIDsQueryParam(c, "tag_ids")
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Invalid tag_ids format in ListPublicPosts")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid 'tag_ids' format", err)
	}
	listParams.TagIDs = tagIDs

	// Note: StartDate, EndDate, and SearchTerm are already *time.Time and *string in ListPostsParams,
	// so c.Bind should handle them directly if the query parameters are named correctly
	// (start_date, end_date, search_term) and dates are in RFC3339 format.

	if err := h.Validator.ValidateStruct(listParams); err != nil {
		log.Ctx(ctx).Warn().Err(err).Interface("params", listParams).Msg("ListPublicPosts parameter validation failed")
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	posts, totalCount, err := h.Service.ListPublishedPosts(ctx, listParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list published posts")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve published posts", err)
	}

	// Prepend base URL to media file paths for each post
	for i := range posts {
		h.prependBaseURLToMediaPaths(&posts[i])
	}

	c.Response().Header().Set("X-Total-Count", fmt.Sprintf("%d", totalCount))
	return c.JSON(http.StatusOK, posts)
}

// GetPostBySlug godoc
// @Summary Get details of a specific published post by ID or slug
// @Description Retrieves details of a specific publicly visible post. The post can be identified by its UUID or its unique slug. The cover image URL is derived from the first media item if available.
// @Tags Content Management (Posts - Public)
// @Produce json
// @Param postIdOrSlug path string true "Post ID (UUID) or Slug"
// @Success 200 {object} payloads.PostResponse "Published post details with media items"
// @Failure 404 {object} utils.ErrorResponse "Post not found or not published"
// @Failure 500 {object} utils.ErrorResponse "Failed to retrieve post"
// @Router /posts/{postIdOrSlug} [get]
func (h *PostHandler) GetPostBySlug(c echo.Context) error {
	postIDOrSlug := c.Param("postIdOrSlug")

	// Try parsing as UUID first. If it works, get by ID. Otherwise, assume it's a slug.
	// This allows the same endpoint to serve both, though typically slugs are preferred for public URLs.
	postID, err := uuid.Parse(postIDOrSlug)
	var postResp *payloads.PostResponse

	if err == nil {
		// It's a UUID, try to get by ID. Ensure it's published.
		tempPostResp, errGet := h.Service.GetPostByID(c.Request().Context(), postID)
		if errGet != nil {
			if errGet == utils.ErrNotFound {
				return utils.HandleError(c, http.StatusNotFound, "Post not found", errGet)
			}
			log.Ctx(c.Request().Context()).Error().Err(errGet).Msg("Failed to get post by ID (when slug was UUID)")
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve post", errGet)
		}

		postResp = tempPostResp
	} else {
		// Not a UUID, assume it's a slug
		postResp, err = h.Service.GetPostBySlug(c.Request().Context(), postIDOrSlug)
		if err != nil {
			if err == utils.ErrNotFound {
				return utils.HandleError(c, http.StatusNotFound, "Post not found or not published", err)
			}
			log.Ctx(c.Request().Context()).Error().Err(err).Str("slug", postIDOrSlug).Msg("Failed to get post by slug")
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve post by slug", err)
		}
	}
	h.prependBaseURLToMediaPaths(postResp)
	return c.JSON(http.StatusOK, postResp)
}

// --- Post Media Endpoints ---

// UploadPostMedia godoc
// @Summary Upload media for a post
// @Description Uploads an image, video, or PDF file and associates it with a specific post. Requires appropriate permissions.
// @Tags Content Management (Posts - Admin)
// @Accept multipart/form-data
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param postId path string true "Post ID (UUID)" Format(uuid)
// @Param file formData file true "Media file to upload"
// @Success 201 {object} payloads.PostMediaItemResponse "Details of the uploaded media item"
// @Failure 400 {object} utils.ErrorResponse "Invalid post ID format, file upload error, or file type/size issue"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Post not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to process or save uploaded media"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/posts/{postId}/media [post]
func (h *PostHandler) AddPostMediaItemHandler(c echo.Context) error {
	// orgIDStr := c.Param("orgId") // Validate orgId if necessary
	postIDStr := c.Param("postId")
	postID, err := uuid.Parse(postIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid post ID format", err)
	}

	file, err := c.FormFile("file")
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "File upload error: "+err.Error(), err)
	}

	isBannerStr := c.FormValue("is_banner")
	isBanner := false
	if isBannerStr == "true" {
		isBanner = true
	}

	// dbMediaItem, err := h.Service.UploadPostMedia(c.Request().Context(), postID, file)
	mediaItemResp, err := h.Service.AddPostMediaItem(c.Request().Context(), postID, file, isBanner)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Post not found for media upload", err)
		} else if strings.Contains(err.Error(), "exceeds max upload size") || strings.Contains(err.Error(), "failed to open") {
			return utils.HandleError(c, http.StatusBadRequest, err.Error(), err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to upload post media")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to process or save uploaded media", err)
	}

	// resp := payloads.ToPostMediaItemResponse(dbMediaItem)
	// The service now returns *payloads.PostMediaItemResponse directly
	if mediaItemResp.FilePath != "" && !strings.HasPrefix(mediaItemResp.FilePath, "http") { // Check if FilePath is not empty and not already a full URL
		mediaItemResp.FilePath = h.BaseURL + "/" + strings.TrimPrefix(mediaItemResp.FilePath, "./")
	}

	return c.JSON(http.StatusCreated, mediaItemResp)
}

// DeletePostMedia godoc
// @Summary Delete media from a post
// @Description Deletes a specific media item associated with a post. Requires appropriate permissions.
// @Tags Content Management (Posts - Admin)
// @Param orgId path string true "Organization ID (UUID)" Format(uuid)
// @Param postId path string true "Post ID (UUID)" Format(uuid)
// @Param mediaItemId path string true "Media Item ID (UUID)" Format(uuid)
// @Success 204 "No Content"
// @Failure 400 {object} utils.ErrorResponse "Invalid media item ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 404 {object} utils.ErrorResponse "Media item not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to delete media item"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/posts/{postId}/media/{mediaItemId} [delete]
func (h *PostHandler) DeletePostMedia(c echo.Context) error {
	mediaIDStr := c.Param("mediaItemId") // Changed from mediaId to mediaItemId
	mediaID, err := uuid.Parse(mediaIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid media item ID format", err)
	}

	err = h.Service.DeletePostMediaItem(c.Request().Context(), mediaID)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Media item not found for deletion", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to delete post media")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to delete media item", err)
	}

	return c.NoContent(http.StatusNoContent)
}

// SetPostBannerMediaItemHandler godoc
// @Summary Designate a post media item as banner
// @Description Sets a specific media item as the banner image for a post. If another item was already the banner, it will be unmarked.
// @Tags Content Management (Posts - Admin)
// @Produce json
// @Param orgId path string true "Organization ID (UUID)" Format(uuid) // Assuming posts are org-scoped for this endpoint for consistency
// @Param postId path string true "Post ID (UUID)" Format(uuid)
// @Param mediaItemId path string true "Media Item ID (UUID) to set as banner" Format(uuid)
// @Success 200 {object} payloads.PostMediaItemResponse "Details of the media item, now marked as banner"
// @Failure 400 {object} utils.ErrorResponse "Invalid ID format"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden - Media item does not belong to post or other permission issue"
// @Failure 404 {object} utils.ErrorResponse "Post or Media item not found"
// @Failure 500 {object} utils.ErrorResponse "Failed to set banner media item"
// @Security ApiKeyAuth
// @Router /organizations/{orgId}/posts/{postId}/media/{mediaItemId}/set-banner [patch]
func (h *PostHandler) SetPostBannerMediaItemHandler(c echo.Context) error {
	postIDStr := c.Param("postId")
	postID, err := uuid.Parse(postIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid post ID format", err)
	}

	mediaItemIDStr := c.Param("mediaItemId")
	mediaItemID, err := uuid.Parse(mediaItemIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid media item ID format", err)
	}

	// Optional: Add orgId check here if needed, e.g., ensure post belongs to orgId from path
	// orgIDStr := c.Param("orgId")

	updatedMediaItem, err := h.Service.SetBannerForPostMediaItem(c.Request().Context(), postID, mediaItemID)
	if err != nil {
		if err == utils.ErrNotFound {
			return utils.HandleError(c, http.StatusNotFound, "Post or media item not found", err)
		} else if err == utils.ErrForbidden {
			return utils.HandleError(c, http.StatusForbidden, "Media item does not belong to the specified post or action forbidden", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to set post banner media item")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to set banner media item", err)
	}

	if updatedMediaItem.FilePath != "" && !strings.HasPrefix(updatedMediaItem.FilePath, "http") {
		updatedMediaItem.FilePath = h.BaseURL + "/" + strings.TrimPrefix(updatedMediaItem.FilePath, "./")
	}

	return c.JSON(http.StatusOK, updatedMediaItem)
}

// Helper function to prepend base URL to media file paths in a PostResponse
func (h *PostHandler) prependBaseURLToMediaPaths(postResp *payloads.PostResponse) {
	if postResp == nil {
		return
	}
	// if postResp.CoverImageURL != nil && *postResp.CoverImageURL != "" && !strings.HasPrefix(*postResp.CoverImageURL, "http://") && !strings.HasPrefix(*postResp.CoverImageURL, "https://") { // Removed
	// 	*postResp.CoverImageURL = h.BaseURL + "/" + strings.TrimPrefix(*postResp.CoverImageURL, "./") // Removed
	// } // Removed
	for i := range postResp.MediaItems {
		if postResp.MediaItems[i].FilePath != "" && !strings.HasPrefix(postResp.MediaItems[i].FilePath, "http://") && !strings.HasPrefix(postResp.MediaItems[i].FilePath, "https://") {
			postResp.MediaItems[i].FilePath = h.BaseURL + "/" + strings.TrimPrefix(postResp.MediaItems[i].FilePath, "./")
		}
	}
}
