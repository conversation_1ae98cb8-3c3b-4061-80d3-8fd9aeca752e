-- Revert the chk_flow_type CHECK constraint to its previous state
ALTER TABLE auth_flows
DROP CONSTRAINT IF EXISTS chk_flow_type,
ADD CONSTRAINT chk_flow_type CHECK (flow_type IN ('phone_otp', 'email_password', 'phone_registration'));

-- Revert the chk_flow_data CHECK constraint to its previous state
ALTER TABLE auth_flows
DROP CONSTRAINT IF EXISTS chk_flow_data,
ADD CONSTRAINT chk_flow_data CHECK (
    ((flow_type = 'phone_otp' OR flow_type = 'phone_registration') AND phone IS NOT NULL AND email IS NULL) OR
    (flow_type = 'email_password' AND email IS NOT NULL AND phone IS NULL)
);