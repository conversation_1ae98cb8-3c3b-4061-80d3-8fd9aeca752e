import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Button, Card, Timeline, Input, Tag, Space, Divider, App, Typography, Row, Col, Descriptions, Statistic, Image, Tooltip, Progress, Spin } from 'antd';
import {
  UserOutlined, CheckCircleOutlined, CloseCircleOutlined,
  CalendarOutlined, FileTextOutlined, ClockCircleOutlined,
  PaperClipOutlined, HeartOutlined, EnvironmentOutlined,
  TeamOutlined, AimOutlined, ArrowUpOutlined, UsergroupAddOutlined, LoadingOutlined,
  MenuOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { volunteerService } from '../../services/volunteerService';
import { formatSimpleDateTime, formatDateOnly, formatTimeRange } from '../../utils/dateFormatter';
import { useAuth } from '../../contexts/AuthContext';
import moment from 'moment';
import ErrorPage from '../ErrorPage';
import approveIcon from '../../assets/images/approve.svg';
import ApprovalSidebar from '../../components/ApprovalSidebar';
import { ProgressCircle, ProgressBar } from '@tremor/react';

const { Title } = Typography;


// Common Components
const UserInfoCard = ({ personalInfo }) => {
  const { t } = useTranslation();
  
  const getInitials = (name) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  const hasAvatar = personalInfo.avatarUrl || personalInfo.profile_picture_url;
  const initials = getInitials(personalInfo.fullName || personalInfo.username);

  return (
    <Card className="mb-6">
      <Row gutter={16} align="middle">
        <Col xs={24} sm={6} md={6} lg={4}>
          <div className="w-[120px] h-[120px] mx-auto">
            {hasAvatar ? (
              <Image
                src={personalInfo.avatarUrl || personalInfo.profile_picture_url}
                alt="avatar"
                className="w-full h-full rounded-full object-cover border-2 border-gray-100"
                preview={false}
              />
            ) : (
              <div className="w-full h-full rounded-full flex items-center justify-center text-4xl font-bold bg-blue-100 text-blue-600 border-2 border-gray-100">
                {initials}
              </div>
            )}
          </div>
        </Col>
        <Col xs={24} sm={24} md={18} lg={20}>
          <div className="mb-4">
            <div className="flex items-baseline gap-2">
              <Title level={3}>@ {personalInfo.username}</Title>
            </div>
          </div>
          <Descriptions column={{ xs: 1, sm: 2, lg: 2 }}>
            <Descriptions.Item label={t('volunteerApproval.personalInfo.fullName')}>
              {personalInfo.fullName}
            </Descriptions.Item>
            <Descriptions.Item label={t('volunteerApproval.personalInfo.phoneNumber')}>
              {personalInfo.phoneNumber}
            </Descriptions.Item>
          </Descriptions>
        </Col>
      </Row>
    </Card>
  );
};

const EventRegistrationStats = ({ statistics }) => {
  const { t } = useTranslation();

  const totalParticipantEvents = statistics.totalParticipantEvents || 0;
  const totalVolunteerEvents = statistics.totalVolunteerEvents || 0;
  const absentParticipantEvents = statistics.absentParticipantEvents || 0;
  const absentVolunteerEvents = statistics.absentVolunteerEvents || 0;

  // 出席活動總數是參與者活動數和義工活動數的總和
  const totalEvents = totalParticipantEvents + totalVolunteerEvents;
  const totalHours = statistics.totalParticipantHours + statistics.totalVolunteerHours;

  // 全部活動總數是總和
  const allEvents = totalParticipantEvents + totalVolunteerEvents + absentParticipantEvents + absentVolunteerEvents;

  // 計算參與者活動和缺席參與者活動的百分比
  const totalParticipantEventsPercent = Math.round((totalParticipantEvents / allEvents) * 100) || 0;
  const absentParticipantEventsPercent = Math.round((absentParticipantEvents / allEvents) * 100) || 0;

  // 計算義工活動和缺席義工活動的百分比
  const totalVolunteerEventsPercent = Math.round((totalVolunteerEvents / allEvents) * 100) || 0;
  const absentVolunteerEventsPercent = Math.round((absentVolunteerEvents / allEvents) * 100) || 0;


  const statsData = [
    {
      title: t('volunteerApproval.statistics.totalParticipantEvents'),
      value: statistics.totalParticipantEvents,
      hours: statistics.totalParticipantHours,
      color: 'bg-blue-500',
      percent: totalParticipantEventsPercent
    },
    {
      title: t('volunteerApproval.statistics.totalVolunteerEvents'),
      value: statistics.totalVolunteerEvents,
      hours: statistics.totalVolunteerHours,
      color: 'bg-pink-500',
      percent: totalVolunteerEventsPercent
    },
    {
      title: t('volunteerApproval.statistics.absentParticipantEvents'),
      value: statistics.absentParticipantEvents,
      hours: statistics.absentHours,
      color: 'bg-red-500',
      percent: absentParticipantEventsPercent
    },
    {
      title: t('volunteerApproval.statistics.absentVolunteerEvents'),
      value: statistics.absentVolunteerEvents,
      hours: statistics.absentVolunteerHours,
      color: 'bg-orange-500',
      percent: absentVolunteerEventsPercent
    }
  ];

  return (
    <Card className="mb-4">
      <div className="mx-auto max-w-[1000px]">
        {/* Total Events */}
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">
            {t('volunteerApproval.statistics.totalEvents')}
          </h3>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-gray-900">
              {totalEvents}
            </span>
            <span className="ml-2 text-lg font-medium text-gray-500">
              / {totalHours}h
            </span>
          </div>
        </div>

        {/* Event Participation Label */}
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm text-gray-500 m-0">
            {t('volunteerApproval.statistics.eventParticipation')}
          </h3>
        </div>

        {/* Status bar */}
        <div className="h-2 w-full rounded-full overflow-hidden bg-gray-100 mb-4">
          <div className="flex h-full">
            <div className="bg-blue-500 h-full mr-px" style={{ width: `${totalParticipantEventsPercent}%` }}></div>
            <div className="bg-pink-500 h-full mr-px" style={{ width: `${totalVolunteerEventsPercent}%` }}></div>
            <div className="bg-red-500 h-full mr-px" style={{ width: `${absentParticipantEventsPercent}%` }}></div>
            <div className="bg-orange-500 h-full" style={{ width: `${absentVolunteerEventsPercent}%` }}></div>
          </div>
        </div>


        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {statsData.map((item) => (
            <div key={item.title} className="border border-gray-100 p-3 rounded">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className={`${item.color} h-3 w-3 rounded-sm flex-shrink-0`} aria-hidden="true" />
                  <dt className="text-sm text-gray-500 truncate">
                    {item.title}
                  </dt>
                </div>
                <span className="text-sm font-medium text-gray-500">
                  {item.percent}%
                </span>
              </div>

              <div className="mt-2 pl-5 flex items-center">
                <span className="text-lg font-semibold text-gray-900">{item.value}</span>
                {item?.hours && (
                  <span className="ml-2 text-sm text-gray-500">/ {item.hours}h</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

const StatisticsCards = ({ statistics, type = 'qualification' }) => {
  const { t } = useTranslation();

  if (type === 'eventRegistration') {
    return <EventRegistrationStats statistics={statistics} />;
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <Card>
        <div className="flex space-x-3">
          <div className={`w-1 shrink-0 rounded bg-blue-500`} />
          <dt className="flex w-full items-center justify-between space-x-3 truncate text-gray-600">
            <span className="truncate">
              {t('volunteerApproval.statistics.totalParticipantEvents')}
            </span>
          </dt>
        </div>
        <div className="mt-2 pl-4">
          <dd className="text-2xl font-semibold text-gray-900">
            {statistics.totalParticipantEvents}
          </dd>
        </div>
      </Card>

      <Card>
        <div className="flex space-x-3">
          <div className={`w-1 shrink-0 rounded bg-violet-500`} />
          <dt className="flex w-full items-center justify-between space-x-3 truncate text-gray-600">
            {t('volunteerApproval.statistics.totalHours')}
          </dt>
        </div>
        <div className="mt-2 pl-4">
          <dd className="text-2xl font-semibold text-gray-900">
            {statistics.totalParticipantHours}h
          </dd>
        </div>
      </Card>

      <Card>
        <div className="flex space-x-3">
          <div className={`w-1 shrink-0 rounded bg-red-500`} />
          <dt className="flex w-full items-center justify-between space-x-3 truncate text-gray-600">
            <span className="truncate">
              {t('volunteerApproval.statistics.absentParticipantEvents')}
            </span>
          </dt>
        </div>
        <div className="mt-2 pl-4">
          <dd className="text-2xl font-semibold text-gray-900">
            {statistics.absentParticipantEvents}
          </dd>
        </div>
      </Card>
    </div>
  );
};

// AccountHistory component has been moved to the ApprovalSidebar component

const VolunteerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const { message } = App.useApp();
  const { user } = useAuth();
  const { type = 'qualification', orgId: orgIdFromState, eventId } = location.state || {};
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [volunteerData, setVolunteerData] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [sidebarVisible, setSidebarVisible] = useState(false);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch volunteer details
  useEffect(() => {
    const fetchDetails = async () => {
      setLoading(true);
      setError(null);
      try {
        let fetchedDataResponse;
        if (type === 'qualification') {
          if (!orgIdFromState || !id) {
            message.error(t('messages.missingIdsForQualification'));
            setError(400); 
            setLoading(false);
            return;
          }
          fetchedDataResponse = await volunteerService.getOrgVolunteerApplicationDetail(orgIdFromState, id);
        } else if (type === 'eventRegistration') {
          if (!orgIdFromState || !eventId || !id) {
            message.error(t('messages.missingIdsForEventRegistration'));
            setError(400);
            setLoading(false);
            return;
          }
          fetchedDataResponse = await volunteerService.getEventVolunteerApplicationDetail(orgIdFromState, eventId, id); 
        } else {
          message.error(t('messages.unknownApplicationType'));
          setError(400);
          setLoading(false);
          return;
        }
        setVolunteerData(fetchedDataResponse); 
      } catch (err) {
        console.error('Error fetching volunteer details:', err);
        message.error(t('messages.fetchError'));
        setError(err.response?.status || 500);
      } finally {
        setLoading(false);
      }
    };

    if (id && type) { // Ensure id and type are present
        fetchDetails();
    }
  }, [id, type, orgIdFromState, eventId, t, message]); // Added orgIdFromState, eventId, t, message

  const handleStatusChange = async (newStatus) => {
    const reasonForReview = rejectReason;
    
    // Map 'withdrawn' UI action to 'pending' status for the backend
    const apiStatus = newStatus === 'withdrawn' ? 'pending' : newStatus;

    if (newStatus !== 'approved' && newStatus !== 'withdrawn' && !reasonForReview.trim()) {
      message.warning(t('volunteerApproval.messages.reasonRequired'));
      return;
    }

    try {
      setActionLoading(true);
      let response;

      const reviewPayload = {
        status: apiStatus,
        adminNotes: reasonForReview,
      };

      if (type === 'qualification') {
        if (!orgIdFromState || !id) {
          message.error(t('messages.missingIdsForQualificationAction'));
          setActionLoading(false);
          return;
        }
        response = await volunteerService.reviewOrgVolunteerApplication(
          orgIdFromState, 
          id, 
          reviewPayload // Pass as an object
        );
      } else if (type === 'eventRegistration') {
        if (!orgIdFromState || !eventId || !id) {
          message.error(t('messages.missingIdsForEventRegistrationAction'));
          setActionLoading(false);
          return;
        }
        response = await volunteerService.reviewEventVolunteerApplication(
          orgIdFromState, 
          eventId,
          id, 
          reviewPayload // Pass as an object
        );
      } else {
        message.error(t('messages.unknownApplicationTypeToAction'));
        setActionLoading(false);
        return;
      }

      const messageKey = `${newStatus}Success`; // Use original newStatus for success message
      message.success(t(`volunteerApproval.messages.${messageKey}`));
      setRejectReason(''); // Clear reason after successful submission
      navigate(`/volunteers-approval?tab=${type}`);
    } catch (error) {
      console.error(`Error updating volunteer status to ${newStatus} (API status: ${apiStatus}):`, error);
      message.error(t(`volunteerApproval.messages.${newStatus}Error`) + (error.response?.data?.message ? `: ${error.response.data.message}` : ''));
    } finally {
      setActionLoading(false);
    }
  };

  // Maintain compatibility with ApprovalSidebar props
  const handleApprove = () => handleStatusChange('approved');
  const handleReject = () => handleStatusChange('rejected');

  // Approval panel has been moved to the ApprovalSidebar component

  if (loading) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-72px)]">
        <Spin size="large" />
      </div>
    );
  }

  console.log('volunteerData Details', volunteerData);
  if (error || !volunteerData) {
    return <ErrorPage status={error || '500'} />;
  }

  // 渲染资格审核详情
  const renderQualificationDetails = () => {
    // Construct personalInfo object from volunteerData for UserInfoCard
    const personalInfoForCard = {
      username: volunteerData.applicant_display_name || '-', 
      fullName: volunteerData.applicant_display_name || '-',
      phoneNumber: volunteerData.applicant_phone || '-',
      profile_picture_url: volunteerData.profile_picture_url
    };

    return (
    <>
      <UserInfoCard personalInfo={personalInfoForCard} />

      <Card className="mb-6">
        <Title level={4} className="mb-4">{t('volunteerApproval.applicationDetailsTitle', "Application Details")}</Title>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">{t('volunteerApproval.table.columns.applicationTime')}</div>
            <div className="text-base font-medium">
              {volunteerData.application_date ? formatSimpleDateTime(volunteerData.application_date) : '-'}
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">{t('common.organization')}</div>
            <div className="text-base font-medium">
              {volunteerData.organization_name || '-'}
            </div>
          </div>
        </div>
      </Card>
    </>
  )};

  // Event Registration Details Section
  const renderEventRegistrationDetails = () => {
    // Construct personalInfo for UserInfoCard from volunteerData
    const personalInfo = {
      username: volunteerData.user_display_name || '-',
      fullName: volunteerData.user_display_name || '-',
      phoneNumber: volunteerData.user_phone || '-',
      // Assuming profile_picture_url might be available, or add a default/placeholder
      profile_picture_url: volunteerData.user_profile_picture_url || null 
    };

    // Construct eventDetails from volunteerData
    // Ensure all accessed fields have fallbacks if not present in volunteerData
    const eventDetails = {
      title: volunteerData.event_title || t('common.notAvailable', 'N/A'),
      startTime: volunteerData.event_start_time || null,
      endTime: volunteerData.event_end_time || null, // Assuming event_end_time might exist
      location: volunteerData.event_location || t('common.notAvailable', 'N/A'), // Assuming event_location might exist
      participantsCount: volunteerData.event_participants_count || 0, // Assuming this field exists
      maxParticipants: volunteerData.event_max_participants || 0, // Assuming this field exists
      volunteerCount: volunteerData.event_volunteer_count || 0, // Assuming this field exists
      // Add any other event-specific details that are used by the child components
      // For example, if ProgressCircle or ProgressBar need more specific event data
    };

    // Fallback for statistics if not present
    const statistics = volunteerData.statistics || {
        totalParticipantEvents: 0,
        totalVolunteerEvents: 0,
        absentParticipantEvents: 0,
        absentVolunteerEvents: 0,
        totalParticipantHours: 0,
        totalVolunteerHours: 0,
        absentHours: 0, // Ensure all fields used by EventRegistrationStats are present
        absentVolunteerHours: 0,
    };


    return (
    <>
      <UserInfoCard personalInfo={personalInfo} />

      <StatisticsCards statistics={statistics} type={type} />

      {/* Event Details Card */}
      <Card
        title={
          <div className="flex items-center gap-2">
            <CalendarOutlined />
            <span>{t('volunteerApproval.eventDetails.title')}</span>
          </div>
        }
        className="mb-6"
      >
        <div className="space-y-6">
          <div>
            <Title level={4}>{eventDetails.title}</Title>
            <div className="mt-3 flex flex-wrap gap-4 text-gray-500">
              {eventDetails.startTime && (
                <div className="flex items-center">
                  <CalendarOutlined className="mr-2" />
                  <span>{formatDateOnly(eventDetails.startTime, i18n.language)}</span>
                </div>
              )}
              {eventDetails.startTime && eventDetails.endTime && (
                <div className="flex items-center">
                  <ClockCircleOutlined className="mr-2" />
                  <span>{formatTimeRange(eventDetails.startTime, eventDetails.endTime)}</span>
                </div>
              )}
              {eventDetails.location !== t('common.notAvailable', 'N/A') && (
                <div className="flex items-center">
                  <EnvironmentOutlined className="mr-2" />
                  <span>{eventDetails.location}</span>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <Card className="h-full shadow-sm">
                <Title level={5} className="flex items-center gap-2 mb-6">
                  <TeamOutlined />
                  <span>{t('volunteerApproval.eventDetails.participants')}</span>
                </Title>
                <div className="flex flex-col items-center">
                  <ProgressCircle
                    value={eventDetails.maxParticipants > 0 ? Math.min(100, Math.round((eventDetails.participantsCount / eventDetails.maxParticipants) * 100)) : 0}
                    size="lg"
                    color={eventDetails.participantsCount >= eventDetails.maxParticipants && eventDetails.maxParticipants > 0 ? "red" : "blue"}
                  >
                    <div className="flex flex-col items-center">
                      <span className="text-2xl font-bold text-gray-900">
                        {eventDetails.participantsCount}
                      </span>
                      <span className="text-xs text-gray-500">/ {eventDetails.maxParticipants > 0 ? eventDetails.maxParticipants : t('common.unlimited', 'Unlimited')}</span>
                    </div>
                  </ProgressCircle>
                  <div className="mt-4 text-sm font-medium text-gray-700 text-center">
                    {eventDetails.maxParticipants > 0 && eventDetails.participantsCount <= eventDetails.maxParticipants ? (
                      <>{t('volunteerApproval.eventDetails.registered')}: {eventDetails.participantsCount}</>
                    ) : eventDetails.maxParticipants > 0 && eventDetails.participantsCount > eventDetails.maxParticipants ? (
                      <>
                        {t('volunteerApproval.eventDetails.waitingList')}: {eventDetails.participantsCount - eventDetails.maxParticipants}
                      </>
                    ) : (
                       <>{t('volunteerApproval.eventDetails.registered')}: {eventDetails.participantsCount}</> // Handle unlimited case
                    )}
                  </div>
                </div>
              </Card>
            </div>

            <div>
              <Card className="h-full shadow-sm">
                <Title level={5} className="flex items-center gap-2 mb-6">
                  <AimOutlined />
                  <span>{t('volunteerApproval.eventDetails.estimatedVolunteers')}</span>
                </Title>

                <div className="px-2 py-4 mt-4">
                  <div className="flex justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700">
                      <UsergroupAddOutlined className="mr-2" />
                      {t('volunteerApproval.eventDetails.volunteerEstimation')}
                    </span>
                    <span className="text-sm font-bold text-gray-900">
                      {eventDetails.volunteerCount} / {Math.ceil(eventDetails.participantsCount * 0.05) > 0 ? Math.ceil(eventDetails.participantsCount * 0.05) : 1 /* Ensure denominator is not 0 */}
                    </span>
                  </div>
                  <ProgressBar
                    value={Math.ceil(eventDetails.participantsCount * 0.05) > 0 ? Math.min(100, Math.round((eventDetails.volunteerCount / Math.ceil(eventDetails.participantsCount * 0.05)) * 100)) : (eventDetails.volunteerCount > 0 ? 100 : 0) }
                    color="green"
                    className="h-8"
                  />
                  <div className="mt-4 text-sm text-gray-500 text-center">
                    {t('volunteerApproval.eventDetails.basedOnAttendees')}
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </Card>
    </>
  )};

  // Format verification requests for ApprovalSidebar
  const formatVerificationRequests = (requests) => {
    if (!requests || !Array.isArray(requests)) return [];
    
    return requests.map(req => ({
      action: t(`documents.${req.verification_type}`) + ' ' + t(`proofDetails.verificationStatus.${req.status}`),
      status: req.status,
      timestamp: req.reviewed_at || req.submitted_at,
      reviewer: req.reviewer_display_name,
      comments: req.admin_notes
    }));
  };

  return (
    <div className="bg-white">
      {/* Mobile Action Button */}
      {isMobile && (
        <div className="fixed bottom-4 right-4 z-50">
          <button
            onClick={() => setSidebarVisible(true)}
            className="w-14 h-14 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center shadow-lg hover:bg-blue-100 transition-colors"
          >
            <img src={approveIcon} alt="Approve" className="w-7 h-7" />
          </button>
        </div>
      )}

      <div className={`max-w-[1600px] mx-auto ${isMobile ? 'block' : 'flex'}`}>
        {/* Main Content */}
        <div className={`${isMobile ? 'w-full' : 'flex-1'} p-4 md:p-6 ${!isMobile && 'border-r border-gray-200'}`}>
          {type === 'qualification' ? renderQualificationDetails() : renderEventRegistrationDetails()}
        </div>

        {/* Approval Sidebar Component */}
        <ApprovalSidebar
          status={volunteerData.status}
          reason={rejectReason}
          setReason={setRejectReason}
          actionLoading={actionLoading}
          accountHistory={formatVerificationRequests(volunteerData.verification_requests) || volunteerData.accountHistory}
          isMobile={isMobile}
          sidebarVisible={sidebarVisible}
          setSidebarVisible={setSidebarVisible}
          onApprove={handleApprove}
          onReject={handleReject}
          onStatusChange={handleStatusChange}
          type={type}
        />
      </div>
    </div>
  );
};

export default VolunteerDetails;