import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

// Helper to map JS-style camelCase params to API-style snake_case for query params
const mapToApiQueryParams = (params, mapping) => {
  const apiParams = {};
  if (params) {
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        const mappedKey = mapping[key] || key;
        apiParams[mappedKey] = params[key];
      }
    }
  }
  return apiParams;
};

// Normalize successful empty list responses
const normalizeEmptyListResponse = (response) => {
  // Priority 1: If response.data itself is an array from the API, wrap it.
  if (response && Array.isArray(response.data)) {
    return { ...response, data: { data: response.data, total: response.data.length } };
  }
  // Priority 2: Backend indicates success with no items by data: { data: null, total: 0 }
  if (response && response.data && response.data.data === null && response.data.total === 0) {
    return { ...response, data: { ...response.data, data: [] } };
  }
  // Priority 3: Backend indicates success with no items by returning overall response.data as null
  if (response && response.data === null) {
    // Preserve other response properties like status by spreading the original response
    return { ...response, data: { data: [], total: 0 } };
  }
  return response;
};

// No specific mappings defined yet for admin verification lists as api.md doesn't specify query params like pagination for them.
// If they are added, mappings can be placed here.
const verificationsListParamsMapping = {
  orgId: 'org_id', // Map client-side orgId to API's org_id
  // status will be passed as is, assuming API expects 'status'
  // New mappings for event registrations:
  userId: 'user_id',
  searchName: 'search_name',
  paymentStatus: 'payment_status',
  // limit and offset are typically handled by the API client directly or are already snake_case
};

export const verificationService = {
  // User-facing verification actions (submit, list own) are now primarily handled in profileService.js
  // This service would be for admin-level verification processes if those were in scope.

  // --- Admin-level verification functionalities (Example Stubs - Not Implemented as per current scope) ---
  // The following methods would correspond to admin-scoped endpoints from api.md 
  // (e.g., /admin/verifications/*), which are currently commented out in apiEndpoints.js 
  // as they are considered staff-only.

  // Example: Get pending verifications (Admin action)
  // getPendingVerifications: async (params = {}) => {
  //   try {
  //     // const response = await api.get(API_ENDPOINTS.ADMIN.GET_PENDING_VERIFICATIONS, { params });
  //     // return response;
  //     throw new Error('Admin endpoint not implemented in this service as per current scope.');
  //   } catch (error) {
  //     console.error('Error in Admin:getPendingVerifications:', error);
  //     throw error;
  //   }
  // },

  // Example: Get all verification requests (Admin action)
  // getAllVerificationRequests: async (params = {}) => {
  //   try {
  //     // const response = await api.get(API_ENDPOINTS.ADMIN.GET_ALL_VERIFICATIONS_ADMIN, { params });
  //     // return response;
  //     throw new Error('Admin endpoint not implemented in this service as per current scope.');
  //   } catch (error) {
  //     console.error('Error in Admin:getAllVerificationRequests:', error);
  //     throw error;
  //   }
  // },

  // Example: Get specific verification request details (Admin action)
  // getAdminVerificationRequestDetail: async (reqId) => {
  //   try {
  //     // const response = await api.get(API_ENDPOINTS.ADMIN.GET_VERIFICATION_DETAIL_ADMIN(reqId));
  //     // return response;
  //     throw new Error('Admin endpoint not implemented in this service as per current scope.');
  //   } catch (error) {
  //     console.error(`Error in Admin:getAdminVerificationRequestDetail for ${reqId}:`, error);
  //     throw error;
  //   }
  // },

  // Example: Review a verification request (Admin action)
  // reviewVerificationRequest: async (reqId, reviewData) => {
  //   // reviewData should match payloads.ReviewVerificationRequestInput { Status, AdminNotes? }
  //   try {
  //     // const response = await api.patch(API_ENDPOINTS.ADMIN.REVIEW_VERIFICATION_ADMIN(reqId), reviewData);
  //     // return response;
  //     throw new Error('Admin endpoint not implemented in this service as per current scope.');
  //   } catch (error) {
  //     console.error(`Error in Admin:reviewVerificationRequest for ${reqId}:`, error);
  //     throw error;
  //   }
  // },

  // Example: Get a verification document (Admin action)
  // getAdminVerificationDocument: async (docId) => {
  //   try {
  //     // const response = await api.get(API_ENDPOINTS.ADMIN.GET_VERIFICATION_DOCUMENT_ADMIN(docId), {
  //     //   responseType: 'blob',
  //     // });
  //     // return response;
  //     throw new Error('Admin endpoint not implemented in this service as per current scope.');
  //   } catch (error) {
  //     console.error(`Error in Admin:getAdminVerificationDocument for ${docId}:`, error);
  //     throw error;
  //   }
  // }
  listAllVerifications: async (clientParams = {}) => {
    // api.md GET /admin/verifications/all
    // clientParams can include { status, orgId, limit, offset }
    // status should be API-ready ('pending', 'approved', 'rejected')
    const apiParams = mapToApiQueryParams(clientParams, verificationsListParamsMapping);
    try {
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ADMIN.LIST_ALL_VERIFICATIONS, { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      
      let verifications = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.data)) {
        verifications = responseData.data;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || verifications.length;
      } else if (Array.isArray(responseData)) {
        verifications = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || verifications.length;
      } else if (responseData === null) {
        verifications = [];
        total = 0;
      }
      
      return { verifications, total };
    } catch (error) {
      console.error('Error fetching all verifications:', error);
      throw error;
    }
  },

  listPendingVerifications: async (clientParams = {}) => {
    // GET /admin/verifications with status=pending filter
    // Add status filter to get only pending verifications
    const paramsWithStatus = { ...clientParams, status: 'pending' };
    const apiParams = mapToApiQueryParams(paramsWithStatus, verificationsListParamsMapping);
    try {
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ADMIN.LIST_ALL_VERIFICATIONS, { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;

      let verifications = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.data)) {
        verifications = responseData.data;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || verifications.length;
      } else if (Array.isArray(responseData)) {
        verifications = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || verifications.length;
      } else if (responseData === null) {
        verifications = [];
        total = 0;
      }
      
      return { verifications, total };
    } catch (error) {
      console.error('Error fetching pending verifications:', error);
      throw error;
    }
  },

  listApprovedVerifications: async (clientParams = {}) => {
    // GET /admin/verifications with status=approved filter
    // Add status filter to get only approved verifications
    const paramsWithStatus = { ...clientParams, status: 'approved' };
    const apiParams = mapToApiQueryParams(paramsWithStatus, verificationsListParamsMapping);
    try {
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ADMIN.LIST_ALL_VERIFICATIONS, { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;

      let verifications = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.data)) {
        verifications = responseData.data;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || verifications.length;
      } else if (Array.isArray(responseData)) {
        verifications = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || verifications.length;
      } else if (responseData === null) {
        verifications = [];
        total = 0;
      }
      
      return { verifications, total };
    } catch (error) {
      console.error('Error fetching approved verifications:', error);
      throw error;
    }
  },

  listEventRegistrationsAdmin: async (clientParams = {}) => {
    // New function for // @Router /events/{eventId}/registrations [get]
    // clientParams should include eventId.
    // Other params: { limit, offset, status, userId, searchName, paymentStatus }
    const { eventId, ...otherParams } = clientParams;

    if (!eventId) {
      console.error('Error in listEventRegistrationsAdmin: eventId is required.');
      throw new Error('eventId is required to list event registrations.');
    }

    const apiParams = mapToApiQueryParams(otherParams, verificationsListParamsMapping);
    try {
      // Use the new endpoint from API_ENDPOINTS.ADMIN
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ADMIN.LIST_EVENT_REGISTRATIONS_ADMIN(eventId), { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      
      let registrations = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.data)) {
        registrations = responseData.data;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || registrations.length;
      } else if (Array.isArray(responseData)) {
        registrations = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || registrations.length;
      } else if (responseData === null) {
        registrations = [];
        total = 0;
      } else if (typeof responseData === 'object' && !Array.isArray(responseData)) {
        // If API returns object but not array, convert it to array of values
        registrations = Object.values(responseData);
        total = parseInt(responseHeaders['x-total-count'], 10) || registrations.length;
      }
      
      return { registrations, total };
    } catch (error) {
      console.error(`Error fetching event registrations for event ${eventId}:`, error);
      throw error;
    }
  },

  getAdminVerificationDetail: async (reqId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ADMIN.GET_VERIFICATION_DETAIL(reqId));
      return response; // Expected: UserVerificationRequestResponse
    } catch (error) {
      console.error(`Error fetching admin verification detail for ${reqId}:`, error);
      throw error;
    }
  },

  reviewVerification: async (reqId, reviewData) => {
    // reviewData must match payloads.ReviewVerificationRequestInput: { status: string, admin_notes?: string }
    // e.g., { status: 'approved', admin_notes: 'Looks good.' }
    // Caller must ensure correct keys.
    const payload = {
      status: reviewData.status, // 'approved' or 'rejected' as per db.VerificationStatusEnum values used in ReviewVerificationRequestInput
      ...(reviewData.adminNotes && { admin_notes: reviewData.adminNotes }),
    };
    try {
      const response = await api.instance.post(API_ENDPOINTS.ADMIN.REVIEW_VERIFICATION(reqId), payload);
      return response; // Expected: UserVerificationRequestResponse
    } catch (error) {
      console.error(`Error reviewing verification for ${reqId}:`, error);
      throw error;
    }
  },

  getAdminVerificationDocument: async (docId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ADMIN.GET_VERIFICATION_DOCUMENT(docId), {
        responseType: 'blob', // Important for file downloads
      });
      return response; // Expected: File blob
    } catch (error) {
      console.error(`Error fetching admin verification document ${docId}:`, error);
      throw error;
    }
  },

  // Get verifications for a specific user
  getUserVerifications: async (userId, clientParams = {}) => {
    // GET /admin/verifications with user_id filter
    // Add user_id filter to get verifications for specific user
    const paramsWithUserId = { ...clientParams, user_id: userId };
    const apiParams = mapToApiQueryParams(paramsWithUserId, verificationsListParamsMapping);
    try {
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ADMIN.LIST_ALL_VERIFICATIONS, { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;

      let verifications = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.data)) {
        verifications = responseData.data;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || verifications.length;
      } else if (Array.isArray(responseData)) {
        verifications = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || verifications.length;
      } else if (responseData === null) {
        verifications = [];
        total = 0;
      }
      
      return { verifications, total };
    } catch (error) {
      console.error(`Error fetching verifications for user ${userId}:`, error);
      throw error;
    }
  }
};