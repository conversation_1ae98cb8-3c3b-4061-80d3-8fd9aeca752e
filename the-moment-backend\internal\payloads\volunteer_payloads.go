package payloads

import "time"

// ApplyVolunteerRequest is the payload for a user applying to be a volunteer.
type ApplyVolunteerRequest struct {
	Motivation string `json:"motivation" form:"motivation"`
}

// VolunteerApplicationResponse represents a single volunteer application.
type VolunteerApplicationResponse struct {
	ID                  string     `json:"id"`
	UserID              string     `json:"user_id"` // Kept for consistency, though often redundant if it's /me/
	OrganizationID      string     `json:"organization_id"`
	OrganizationName    string     `json:"organization_name,omitempty"`
	ApplicationDate     time.Time  `json:"application_date"`
	Status              string     `json:"status"`
	Motivation          *string    `json:"motivation,omitempty"`
	ReviewedByUserID    *string    `json:"reviewed_by_user_id,omitempty"`
	ReviewerDisplayName *string    `json:"reviewer_display_name,omitempty"` // For admin views
	ReviewDate          *time.Time `json:"review_date,omitempty"`
	AdminNotes          *string    `json:"admin_notes,omitempty"`
	CreatedAt           time.Time  `json:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at"`

	// Fields primarily for admin views of applications
	ApplicantDisplayName *string `json:"applicant_display_name,omitempty"`
	ApplicantEmail       *string `json:"applicant_email,omitempty"`
	ApplicantPhone       *string `json:"applicant_phone,omitempty"`
}

// VolunteerQualificationResponse represents a user's volunteer qualification for an organization.
type VolunteerQualificationResponse struct {
	ID                string    `json:"id"`
	UserID            string    `json:"user_id"`
	OrganizationID    string    `json:"organization_id"`
	OrganizationName  string    `json:"organization_name"`
	ApplicationID     string    `json:"application_id"`
	QualificationDate time.Time `json:"qualification_date"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// AdminReviewVolunteerApplicationRequest is the payload for an admin to review an application.
type AdminReviewVolunteerApplicationRequest struct {
	Status     string  `json:"status" form:"status" validate:"required,oneof=pending approved rejected withdrawn" example:"approved"`
	AdminNotes *string `json:"admin_notes,omitempty" form:"admin_notes" example:"Looks good."`
}

// WithdrawApplicationRequest is the payload for a user to withdraw their volunteer application.
type WithdrawApplicationRequest struct {
	// No body needed for withdrawal, but can add optional notes if required by product.
	// Notes string `json:"notes,omitempty"`
}
