import React, { useMemo } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
// import { useAuth } from '../contexts/AuthContext'; // isLoggedIn might still be needed, or derived from currentUser
import { useUser } from '../contexts/UserContext'; // Import useUser
import { useOrganization, ALL_ORGANIZATION_ID } from '../contexts/OrganizationContext'; // Import organization context
import { Spin } from 'antd';

// Helper function to check if user has required role
const checkUserPermission = (userRole, requiredRoles) => {
    if (!requiredRoles || requiredRoles.length === 0) return true;
    if (!userRole) return false;
    return requiredRoles.includes(userRole);
};

// Cache for regex patterns to avoid recompilation
const regexCache = new Map();

// Get regex for path pattern
const getPathRegex = (path) => {
    if (regexCache.has(path)) {
        return regexCache.get(path);
    }
    const regex = new RegExp(`^${path.replace(/:[^\s/]+/g, '[^/]+')}$`);
    regexCache.set(path, regex);
    return regex;
};

// Define a map for custom access check functions
const customAccessChecks = {
    userVerificationPage: ({ currentOrganization }) => {
        // This page should only be accessible when the organization is ALL_ORGANIZATION_ID
        // (i.e., when "All Organizations" is selected)
        return currentOrganization?.id === ALL_ORGANIZATION_ID;
    },
};

// Helper function to find the deepest matching route and its required roles
const findMatchingRoute = (pathname, routes, parentPath = '', parentRoles = []) => {
    let bestMatch = null;
    
    // Create a flat array of route configurations for faster processing
    const routeQueue = Object.values(routes).map(route => ({
        route,
        fullPath: `${parentPath}/${route.path}`.replace(/\/\/+/g, '/'),
        roles: route.requiredRole 
            ? [...parentRoles, ...route.requiredRole]
            : parentRoles
    }));
    
    while (routeQueue.length > 0) {
        const { route, fullPath, roles } = routeQueue.shift();
        const pathRegex = getPathRegex(fullPath);
        
        if (pathRegex.test(pathname)) {
            bestMatch = {
                roles: roles.length > 0 ? Array.from(new Set(roles)) : roles,
                exists: true
            };
            
            // If this route has no children, it's the best possible match
            if (!route.children || Object.keys(route.children).length === 0) {
                return bestMatch;
            }
        }
        
        // Add children to the queue
        if (route.children) {
            const childRoles = route.requiredRole 
                ? [...parentRoles, ...route.requiredRole]
                : parentRoles;
                
            Object.values(route.children).forEach(childRoute => {
                routeQueue.push({
                    route: childRoute,
                    fullPath: `${fullPath}/${childRoute.path}`.replace(/\/\/+/g, '/'),
                    roles: childRoute.requiredRole 
                        ? [...childRoles, ...childRoute.requiredRole]
                        : childRoles
                });
            });
        }
    }
    
    return bestMatch;
};

const RouteMiddleware = ({ routes, children }) => {
    // const { isLoggedIn, user: authUser } = useAuth(); // Keep isLoggedIn from AuthContext for initial auth check
    const { currentUser, isLoading: isUserLoading } = useUser(); // Get currentUser from UserContext for role
    const { currentOrganization, loading: isOrgLoading } = useOrganization(); // Get organization context
    const location = useLocation();

    // Determine if the user is authenticated. 
    // Prefer currentUser for this once loaded, but isLoggedIn can be an early check.
    const isAuthenticated = !!currentUser; // Or combine with original isLoggedIn from useAuth if needed for phased loading

    // Memoize route requirements calculation to avoid recalculating on every render
    const routeRequirements = useMemo(() => 
        findMatchingRoute(location.pathname, routes),
        [location.pathname, routes]
    );

    // Fast path for root or non-existent routes
    if (location.pathname === '/' || !routeRequirements) {
        // If route doesn't exist in our config and not at root, redirect to 404
        if (!routeRequirements && location.pathname !== '/') {
            return <Navigate to="/error/404" replace />;
        }
        return children;
    }

    const { roles: requiredRoles } = routeRequirements;

    // Combined loading state
    const isLoading = isUserLoading;

    // Handle loading state for user profile/role
    if (requiredRoles && requiredRoles.length > 0 && isLoading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Spin size="large" />
            </div>
        );
    }

    // Authentication check
    if (requiredRoles && requiredRoles.length > 0 && !isAuthenticated && !isUserLoading) {
        const returnUrl = encodeURIComponent(location.pathname + location.search);
        return <Navigate to={`/login?returnUrl=${returnUrl}`} replace />;
    }

    // Role permission check
    if (requiredRoles && requiredRoles.length > 0 && isAuthenticated && !isUserLoading) {
        if (!checkUserPermission(currentUser?.role, requiredRoles)) {
            return <Navigate to="/error/403" replace state={{ reason: 'You do not have permission to access this page based on your role.' }} />;
        }
    }

    // If all checks pass, render the route
    return children;
};

export default RouteMiddleware; 