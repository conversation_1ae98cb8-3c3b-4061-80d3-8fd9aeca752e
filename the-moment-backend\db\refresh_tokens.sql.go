// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: refresh_tokens.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const createRefreshToken = `-- name: CreateRefreshToken :one
INSERT INTO refresh_tokens (
    user_id,
    token_hash,
    expires_at
) VALUES (
    $1, $2, $3
) RETURNING id, user_id, token_hash, expires_at, created_at
`

type CreateRefreshTokenParams struct {
	UserID    uuid.UUID `db:"user_id" json:"user_id"`
	TokenHash string    `db:"token_hash" json:"token_hash"`
	ExpiresAt time.Time `db:"expires_at" json:"expires_at"`
}

func (q *Queries) CreateRefreshToken(ctx context.Context, arg CreateRefreshTokenParams) (RefreshToken, error) {
	row := q.db.QueryRow(ctx, createRefreshToken, arg.UserID, arg.TokenHash, arg.ExpiresAt)
	var i RefreshToken
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.TokenHash,
		&i.ExpiresAt,
		&i.CreatedAt,
	)
	return i, err
}

const deleteRefreshTokenByTokenHash = `-- name: DeleteRefreshTokenByTokenHash :exec
DELETE FROM refresh_tokens
WHERE token_hash = $1
`

func (q *Queries) DeleteRefreshTokenByTokenHash(ctx context.Context, tokenHash string) error {
	_, err := q.db.Exec(ctx, deleteRefreshTokenByTokenHash, tokenHash)
	return err
}

const getRefreshTokenByTokenHash = `-- name: GetRefreshTokenByTokenHash :one
SELECT id, user_id, token_hash, expires_at, created_at FROM refresh_tokens
WHERE token_hash = $1
`

func (q *Queries) GetRefreshTokenByTokenHash(ctx context.Context, tokenHash string) (RefreshToken, error) {
	row := q.db.QueryRow(ctx, getRefreshTokenByTokenHash, tokenHash)
	var i RefreshToken
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.TokenHash,
		&i.ExpiresAt,
		&i.CreatedAt,
	)
	return i, err
}
