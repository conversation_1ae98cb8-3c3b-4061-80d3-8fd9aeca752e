/* EventCalendar.css */

.event-calendar-container {
  max-width: 1200px;
  margin: 20px;
}

.calendar-card,
.events-list-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.calendar-title {
  color: #1890ff;
  margin-bottom: 24px;
  text-align: center;
}

.dots-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.event-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 1px;
}

.selected-date-events {
  margin-top: 20px;
}

.selected-date-events .ant-list-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

/* Replace direct ant-list-item styles with scoped class names */
.event-list-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.event-list-meta {
  align-items: center;
  width: 100%;
}

.event-list-meta .ant-list-item-meta-content {
  width: 100%;
  overflow: hidden;
}

.event-list-meta .ant-list-item-meta-avatar {
  margin-right: 16px;
}

.event-list-meta .ant-list-item-meta-title {
  font-weight: bold;
}

.event-list-meta .ant-list-item-meta-description {
  color: gray;
}

.event-list-item img {
  border-radius: 8px;
  object-fit: cover;
}

.event-tag {
  position: absolute;
  top: 16px;
  right: 16px;
}

.event-calendar-layout {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.events-list-section {
  flex: 2;
  min-width: 300px;
}

.calendar-section {
  flex: 1;
  min-width: 300px;
}

/* Add specific class names to scope the styles */
.event-calendar-list-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.event-calendar-list-item .ant-list-item-meta {
  align-items: center;
  width: 100%;
}

.event-calendar-list-item .ant-list-item-meta-content {
  width: 100%;
  overflow: hidden;
}

.event-calendar-list-item .ant-list-item-meta-avatar {
  margin-right: 16px;
}

.event-calendar-list-item .ant-list-item-meta-title {
  font-weight: bold;
}

.event-calendar-list-item .ant-list-item-meta-description {
  color: gray;
}

/* Simplify media query */
@media (max-width: 962px) {
  .event-calendar-container {
    padding: 0;
  }

  .ant-list-item,
  .event-calendar-list-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .ant-list-item-meta-avatar,
  .event-calendar-list-item .ant-list-item-meta-avatar {
    margin-bottom: 8px;
  }

  .event-tag {
    top: 8px;
    right: 8px;
  }

  .event-calendar-layout {
    flex-direction: column;
  }

  .events-list-section,
  .calendar-section {
    width: 100%;
  }

  /* Simplify text container widths */
  .ant-list-item-meta-title .ant-typography,
  .ant-list-item-meta-description .ant-typography,
  .event-calendar-list-item .ant-list-item-meta-title .ant-typography,
  .event-calendar-list-item .ant-list-item-meta-description .ant-typography {
    width: 100% !important;
    max-width: 100% !important;
  }

  .event-calendar-list-item .ant-list-item-meta-content {
    padding-right: 60px; /* Space for tag */
  }
}

/* 隐藏 month/year radio button*/
.ant-picker-calendar-mode-switch {
    display: none !important;
}
