-- name: GetEventCategoryStats :many
SELECT
    et.name_en,
    et.name_zh_hk,
    et.name_zh_cn,
    COUNT(DISTINCT e.id) AS total_events,
    COUNT(er.id) FILTER (WHERE er.status = 'registered' OR er.status = 'attended') AS total_registrations,
    COUNT(er.id) FILTER (WHERE er.status = 'attended') AS total_attendees
FROM event_tags et
JOIN event_event_tags eet ON et.id = eet.event_tag_id
JOIN events e ON eet.event_id = e.id
LEFT JOIN event_registrations er ON e.id = er.event_id
WHERE
    (sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ)
    AND (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL OR e.start_time <= sqlc.narg(end_date)::TIMESTAMPTZ)
    AND e.status = 'published'
GROUP BY et.id, et.name_en, et.name_zh_hk, et.name_zh_cn
ORDER BY total_events DESC;

-- name: GetTopEventsByParticipantCount :many
SELECT
    e.id,
    e.title,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'registered') AS participants
FROM events e
WHERE
    (
        sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NOT NULL AND e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ AND e.start_time <= sqlc.narg(end_date)::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL AND e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ)
        )
    )
    AND e.status = 'published'
ORDER BY participants DESC
LIMIT sqlc.arg(lim);

-- name: GetOrganizationEventCategoryStats :many
SELECT
    et.name_en,
    et.name_zh_hk,
    et.name_zh_cn,
    COUNT(DISTINCT e.id) AS total_events,
    COUNT(er.id) FILTER (WHERE er.status = 'registered' OR er.status = 'attended') AS total_registrations,
    COUNT(er.id) FILTER (WHERE er.status = 'attended') AS total_attendees
FROM event_tags et
JOIN event_event_tags eet ON et.id = eet.event_tag_id
JOIN events e ON eet.event_id = e.id
LEFT JOIN event_registrations er ON e.id = er.event_id
WHERE
    e.organization_id = sqlc.arg(organization_id)
    AND (sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ)
    AND (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL OR e.start_time <= sqlc.narg(end_date)::TIMESTAMPTZ)
    AND e.status = 'published'
GROUP BY et.id, et.name_en, et.name_zh_hk, et.name_zh_cn
ORDER BY total_events DESC;

-- name: GetOrganizationTopEventsByParticipantCount :many
SELECT
    e.id,
    e.title,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'registered') AS participants
FROM events e
WHERE
    e.organization_id = sqlc.arg(organization_id)
    AND (
        sqlc.narg(start_date)::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NOT NULL AND e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ AND e.start_time <= sqlc.narg(end_date)::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            (sqlc.narg(end_date)::TIMESTAMPTZ IS NULL AND e.start_time >= sqlc.narg(start_date)::TIMESTAMPTZ)
        )
    )
    AND e.status = 'published'
ORDER BY participants DESC
LIMIT sqlc.arg(lim);

-- Single Event Statistics --

-- name: GetEventStatistics :one
SELECT
    COUNT(DISTINCT er.id)::INT AS participants_count,
    COUNT(DISTINCT eva.id)::INT AS volunteers_count,
    e.participant_limit AS max_participants
FROM events e
LEFT JOIN event_registrations er ON e.id = er.event_id AND er.status IN ('registered', 'attended')
LEFT JOIN event_volunteer_applications eva ON e.id = eva.event_id AND eva.status = 'approved'
WHERE e.id = $1
GROUP BY e.id;

-- name: GetEventParticipantsByAge :many
SELECT
    CASE
        WHEN age BETWEEN 0 AND 10 THEN '0-10'
        WHEN age BETWEEN 11 AND 20 THEN '11-20'
        WHEN age BETWEEN 21 AND 30 THEN '21-30'
        WHEN age BETWEEN 31 AND 40 THEN '31-40'
        WHEN age BETWEEN 41 AND 50 THEN '41-50'
        WHEN age BETWEEN 51 AND 60 THEN '51-60'
        ELSE '61+'
    END AS age_range,
    COUNT(*)::INT AS count
FROM (
    SELECT
        EXTRACT(YEAR FROM AGE(to_date(COALESCE(v_hkid.date_of_birth, v_cnid.date_of_birth), 'YYYY-MM-DD'))) AS age
    FROM event_registrations er
    JOIN users u ON er.user_id = u.id
    LEFT JOIN user_verification_requests uvr ON u.id = uvr.user_id AND uvr.id = (
        SELECT id FROM user_verification_requests
        WHERE user_id = u.id
          AND status = 'approved'
          AND verification_type IN ('hk_id_card', 'mainland_china_id_card')
        ORDER BY submitted_at DESC
        LIMIT 1
    )
    LEFT JOIN verification_hk_id_cards v_hkid ON uvr.id = v_hkid.verification_request_id
    LEFT JOIN verification_mainland_china_id_cards v_cnid ON uvr.id = v_cnid.verification_request_id
    WHERE er.event_id = $1
    AND er.status IN ('registered', 'attended')
    AND COALESCE(v_hkid.date_of_birth, v_cnid.date_of_birth) IS NOT NULL
) AS ages
GROUP BY age_range
ORDER BY age_range;

-- name: GetEventParticipantsByGender :many
SELECT
    COALESCE(LOWER(gender), 'undefined') as gender,
    COUNT(*)::INT AS count
FROM (
    SELECT
        COALESCE(v_hkid.sex, v_cnid.sex) as gender
    FROM event_registrations er
    JOIN users u ON er.user_id = u.id
    LEFT JOIN user_verification_requests uvr ON u.id = uvr.user_id AND uvr.id = (
        SELECT id FROM user_verification_requests
        WHERE user_id = u.id
          AND status = 'approved'
          AND verification_type IN ('hk_id_card', 'mainland_china_id_card')
        ORDER BY submitted_at DESC
        LIMIT 1
    )
    LEFT JOIN verification_hk_id_cards v_hkid ON uvr.id = v_hkid.verification_request_id
    LEFT JOIN verification_mainland_china_id_cards v_cnid ON uvr.id = v_cnid.verification_request_id
    WHERE er.event_id = $1
    AND er.status IN ('registered', 'attended')
) as genders
GROUP BY gender;

-- name: GetEventVolunteersByAge :many
SELECT
    CASE
        WHEN age BETWEEN 0 AND 10 THEN '0-10'
        WHEN age BETWEEN 11 AND 20 THEN '11-20'
        WHEN age BETWEEN 21 AND 30 THEN '21-30'
        WHEN age BETWEEN 31 AND 40 THEN '31-40'
        WHEN age BETWEEN 41 AND 50 THEN '41-50'
        WHEN age BETWEEN 51 AND 60 THEN '51-60'
        ELSE '61+'
    END AS age_range,
    COUNT(*)::INT AS count
FROM (
    SELECT
        EXTRACT(YEAR FROM AGE(to_date(COALESCE(v_hkid.date_of_birth, v_cnid.date_of_birth), 'YYYY-MM-DD'))) AS age
    FROM event_volunteer_applications eva
    JOIN users u ON eva.user_id = u.id
    LEFT JOIN user_verification_requests uvr ON u.id = uvr.user_id AND uvr.id = (
        SELECT id FROM user_verification_requests
        WHERE user_id = u.id
          AND status = 'approved'
          AND verification_type IN ('hk_id_card', 'mainland_china_id_card')
        ORDER BY submitted_at DESC
        LIMIT 1
    )
    LEFT JOIN verification_hk_id_cards v_hkid ON uvr.id = v_hkid.verification_request_id
    LEFT JOIN verification_mainland_china_id_cards v_cnid ON uvr.id = v_cnid.verification_request_id
    WHERE eva.event_id = $1
    AND eva.status = 'approved'
    AND COALESCE(v_hkid.date_of_birth, v_cnid.date_of_birth) IS NOT NULL
) AS ages
GROUP BY age_range
ORDER BY age_range;

-- name: GetEventVolunteersByGender :many
SELECT
    COALESCE(LOWER(gender), 'undefined') as gender,
    COUNT(*)::INT AS count
FROM (
    SELECT
        COALESCE(v_hkid.sex, v_cnid.sex) as gender
    FROM event_volunteer_applications eva
    JOIN users u ON eva.user_id = u.id
    LEFT JOIN user_verification_requests uvr ON u.id = uvr.user_id AND uvr.id = (
        SELECT id FROM user_verification_requests
        WHERE user_id = u.id
          AND status = 'approved'
          AND verification_type IN ('hk_id_card', 'mainland_china_id_card')
        ORDER BY submitted_at DESC
        LIMIT 1
    )
    LEFT JOIN verification_hk_id_cards v_hkid ON uvr.id = v_hkid.verification_request_id
    LEFT JOIN verification_mainland_china_id_cards v_cnid ON uvr.id = v_cnid.verification_request_id
    WHERE eva.event_id = $1
    AND eva.status = 'approved'
) as genders
GROUP BY gender;

-- name: GetEventParticipantsByDate :many
SELECT
    DATE(created_at)::TEXT as date,
    COUNT(*)::INT AS count
FROM event_registrations
WHERE event_id = $1 AND status IN ('registered', 'attended')
GROUP BY DATE(created_at)
ORDER BY DATE(created_at);

-- name: GetEventVolunteersByDate :many
SELECT
    DATE(created_at)::TEXT as date,
    COUNT(*)::INT AS count
FROM event_volunteer_applications
WHERE event_id = $1 AND status = 'approved'
GROUP BY DATE(created_at)
ORDER BY DATE(created_at); 