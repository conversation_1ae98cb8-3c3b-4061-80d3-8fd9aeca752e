// EventFilter.js
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Input,
  Space,
  Select,
  Checkbox,
  DatePicker,
  Button,
  Tag,
} from 'antd';
import {
  FireOutlined,
  SearchOutlined,
  AppstoreOutlined,
  CoffeeOutlined,
  TeamOutlined,
  ExperimentOutlined,
  ShopOutlined,
  ReadOutlined,
  RocketOutlined,
  CommentOutlined,
  CodeOutlined,
  ClusterOutlined,
  FlagOutlined,
  StarOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import '../styles/EventFilter.css';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from 'react-responsive';
import { eventService } from '../services/eventService';

const { Option } = Select;
const { RangePicker } = DatePicker;

// Helper function to get tag name based on current language
const getTagName = (tag, language) => {
  if (!tag) return '';
  
  if (language.startsWith('zh')) {
    if (language.includes('HK')) {
      return tag.name_zh_hk || tag.name_en || '';
    } else {
      return tag.name_zh_cn || tag.name_en || '';
    }
  } else {
    return tag.name_en || '';
  }
};

// Debounce helper function
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    // Set debouncedValue to value after the specified delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    // Cancel the timeout if value changes or unmounts
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
};

const EventFilter = ({ onFilter, showCreateButton = false, onCreateEvent = () => {}, initialFilters = {} }) => {
  const { t, i18n } = useTranslation();
  const [searchText, setSearchText] = useState(initialFilters.searchText || '');
  const debouncedSearchText = useDebounce(searchText, 500);
  const [dateRange, setDateRange] = useState(initialFilters.dateRange || null);
  const [selectedEventType, setSelectedEventType] = useState(initialFilters.eventTypes?.[0] || null);
  const [availableTags, setAvailableTags] = useState([]);
  const [loadingTags, setLoadingTags] = useState(false);
  const [availableVerificationTypes, setAvailableVerificationTypes] = useState([]);
  const [loadingVerificationTypes, setLoadingVerificationTypes] = useState(false);
  const [selectedVerificationTypes, setSelectedVerificationTypes] = useState(initialFilters.verificationTypes || []);
  const [availableFundingTypes, setAvailableFundingTypes] = useState([]);
  const [loadingFundingTypes, setLoadingFundingTypes] = useState(false);
  const [selectedFundingTypes, setSelectedFundingTypes] = useState(initialFilters.fundingTypes || []);
  const isFirstRender = useRef(true);
  const prevFiltersRef = useRef(null);
  const isMobile = useMediaQuery({ maxWidth: 768 });

  const fetchTags = useCallback(async () => {
    setLoadingTags(true);
    try {
      // API now returns tags with name_en, name_zh_hk, name_zh_cn format, no need for lang_code parameter
      const tagsResponse = await eventService.listEventTags();
      const tags = Array.isArray(tagsResponse) ? tagsResponse : (tagsResponse.data || tagsResponse.items || []);
      setAvailableTags(tags.filter(tag => tag.id && (tag.name_en || tag.name_zh_hk || tag.name_zh_cn)));
    } catch (error) {
      console.error("Failed to fetch event tags for filter", error);
      setAvailableTags([]);
    } finally {
      setLoadingTags(false);
    }
  }, []); // Removed i18n.language dependency since API returns all languages

  // Fetch event tags (event types)
  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  // Fetch verification types
  useEffect(() => {
    const fetchVerificationTypes = async () => {
        setLoadingVerificationTypes(true);
        try {
            const lang_code = i18n.language.replace('-', '_');
            const response = await eventService.listVerificationTypes({ lang_code });
            const types = Array.isArray(response) ? response : (response.data || response.items || []);
            setAvailableVerificationTypes(types.filter(type => type.key && type.name));
        } catch (error) {
            console.error("Failed to fetch verification types for filter", error);
            setAvailableVerificationTypes([]);
        } finally {
            setLoadingVerificationTypes(false);
        }
    };
    fetchVerificationTypes();
  }, [i18n.language]); // Fetch once

  // Fetch government funding types
  useEffect(() => {
    const fetchFundingTypes = async () => {
        setLoadingFundingTypes(true);
        try {
            const lang_code = i18n.language.replace('-', '_');
            const response = await eventService.listGovernmentFundingTypes({ lang_code });
            const types = Array.isArray(response) ? response : (response.data || response.items || []);
            // Assuming type has 'key' and 'name' for government funding types
            setAvailableFundingTypes(types.filter(type => type.key && type.name)); 
        } catch (error) {
            console.error("Failed to fetch government funding types for filter", error);
            setAvailableFundingTypes([]);
        } finally {
            setLoadingFundingTypes(false);
        }
    };
    fetchFundingTypes();
  }, [i18n.language]); // Fetch once

  // Effect to synchronize selectedEventType when language changes
  useEffect(() => {
    // Check if selected tag still exists in available tags
    if (selectedEventType && availableTags.length > 0) {
      const stillExists = availableTags.some(tag => tag.id === selectedEventType);
      if (!stillExists) {
        setSelectedEventType(null);
      }
    }
  }, [availableTags, selectedEventType]);

  // Update filters when any filter value changes
  useEffect(() => {
    // Skip initial render to avoid triggering unnecessary updates
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Create the new filter object
    const newFilters = {
      searchText: debouncedSearchText,
      dateRange: dateRange,
      eventTypes: selectedEventType ? [selectedEventType] : [],
      verificationTypes: selectedVerificationTypes,
      fundingTypes: selectedFundingTypes,
    };
    
    // Simple comparison check (not perfect but avoids the ref error)
    const hasChanged = JSON.stringify(prevFiltersRef.current) !== JSON.stringify(newFilters);
    
    // Only call onFilter if filters have actually changed
    if (hasChanged) {
      // Prevent effect from running multiple times on cleanup
      let isMounted = true;
      if (isMounted) {
        onFilter(newFilters);
        prevFiltersRef.current = newFilters;
      }
      
      // Cleanup function to prevent memory leaks and late filter updates
      return () => {
        isMounted = false;
      };
    }
  }, [debouncedSearchText, dateRange, selectedEventType, selectedVerificationTypes, selectedFundingTypes, onFilter]);

  // Add a new effect that runs once on mount to pass initial filters
  useEffect(() => {
    // Pass initial default filters to parent component immediately on mount
    const initialFilterObject = {
      searchText: debouncedSearchText,
      dateRange: dateRange,
      eventTypes: selectedEventType ? [selectedEventType] : [],
      verificationTypes: selectedVerificationTypes,
      fundingTypes: selectedFundingTypes,
    };
    
    // Save initial filters as previous state
    prevFiltersRef.current = initialFilterObject;
    
    // Send initial filters to parent component
    onFilter(initialFilterObject);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);  // Empty dependency array means this runs once on mount

  const handleSearchTextChange = useCallback((e) => {
    setSearchText(e.target.value);
  }, []);

  const handleDateRangeChange = useCallback((dates) => {
    setDateRange(dates);
  }, []);

  const handleSelectedTypesChange = useCallback((value) => {
    setSelectedEventType(value);
  }, []);

  const handleVerificationTypesChange = useCallback((value) => {
    setSelectedVerificationTypes(value);
  }, []);

  const handleFundingTypesChange = useCallback((value) => {
    setSelectedFundingTypes(value);
  }, []);

  // Define date presets (reverted to original simple style if any)
  const datePresets = [
    { label: t('eventFilter.dateFilter.allDates'), value: null },
    { label: t('eventFilter.dateFilter.thisWeek'), value: [dayjs().startOf('week'), dayjs().endOf('week')] },
    { label: t('eventFilter.dateFilter.nextWeek'), value: [dayjs().add(1, 'week').startOf('week'), dayjs().add(1, 'week').endOf('week')] },
    { label: t('eventFilter.dateFilter.thisMonth'), value: [dayjs().startOf('month'), dayjs().endOf('month')] },
    { label: t('eventFilter.dateFilter.nextMonth'), value: [dayjs().add(1, 'month').startOf('month'), dayjs().add(1, 'month').endOf('month')] },
    { label: t('eventFilter.dateFilter.next30Days'), value: [dayjs().startOf('day'), dayjs().add(30, 'day').endOf('day')] },
  ];

  return (
    <div className="filter-section">
      <div className="filter-section-left">
        <Space wrap size={8}>
          <Input
            placeholder={t('eventFilter.search.placeholder')}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={handleSearchTextChange}
            style={{ minWidth: 200, maxWidth: 300 }}
            size={isMobile ? 'middle' : 'large'}
            allowClear
          />
          <RangePicker
            presets={datePresets}
            value={dateRange}
            onChange={handleDateRangeChange}
            allowClear
            style={{ width: 280 }}
            size={isMobile ? 'middle' : 'large'}
            format="YYYY-MM-DD"
          />
          <Select
            allowClear
            showSearch={false}
            style={{ width: 240 }}
            placeholder={t('eventFilter.eventTypes.placeholder')}
            onChange={handleSelectedTypesChange}
            value={selectedEventType}
            loading={loadingTags}
            size={isMobile ? 'middle' : 'large'}
            optionLabelProp="label"
          >
            {availableTags.map(tag => (
              <Option key={tag.id} value={tag.id} label={getTagName(tag, i18n.language)}>
                {getTagName(tag, i18n.language)}
              </Option>
            ))}
          </Select>
          <Select
            mode="multiple"
            allowClear
            showSearch={false}
            style={{ width: 240 }}
            placeholder={t('eventFilter.verificationTypes.placeholder')}
            onChange={handleVerificationTypesChange}
            value={selectedVerificationTypes}
            loading={loadingVerificationTypes}
            maxTagCount="responsive"
            size={isMobile ? 'middle' : 'large'}
            optionLabelProp="label"
          >
            {availableVerificationTypes.map(type => (
              <Option key={type.key} value={type.key} label={type.name}>
                {type.name}
              </Option>
            ))}
          </Select>
          <Select
            mode="multiple"
            allowClear
            showSearch={false}
            style={{ width: 240 }}
            placeholder={t('eventFilter.fundingTypes.placeholder')}
            onChange={handleFundingTypesChange}
            value={selectedFundingTypes}
            loading={loadingFundingTypes}
            maxTagCount="responsive"
            size={isMobile ? 'middle' : 'large'}
            optionLabelProp="label"
          >
            {availableFundingTypes.map(type => (
              <Option key={type.key} value={type.key} label={type.name}>
                {type.name}
              </Option>
            ))}
          </Select>
        </Space>
      </div>
      <div className="filter-section-right">
        {showCreateButton && (
          <Button
            type="dashed"
            size={isMobile ? 'middle' : 'large'}
            onClick={onCreateEvent}
            style={{ marginLeft: isMobile ? 0 : 8, marginTop: isMobile ? 8 : 0 }}
          >
            {t('eventFilter.buttons.createEvent')}
          </Button>
        )}
      </div>
    </div>
  );
};

export default React.memo(EventFilter);
