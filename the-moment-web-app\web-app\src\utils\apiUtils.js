/**
 * Utility functions for API calls and error handling
 */

/**
 * Executes an API call with standardized error handling
 * @param {Function} apiCall - The API function to call (e.g., api.get, api.post)
 * @param {string} errorMessage - Error message to log
 * @param {boolean} returnDataOnly - If true, returns just the data; if false, returns {data, headers, totalCount}
 * @returns {Promise} - The data from the API call
 */
export const executeApiCall = async (apiCall, errorMessage, returnDataOnly = true) => {
  try {
    const response = await apiCall();
    
    if (returnDataOnly) {
      return response.data;
    }
    
    // Return a standardized response object with data and metadata
    return {
      data: response.data,
      headers: response.headers,
      totalCount: parseInt(response.headers['x-total-count'], 10) || 0,
      status: response.status
    };
  } catch (error) {
    console.error(errorMessage, error);
    throw error.isError ? error : { 
      data: null, 
      message: error.message, 
      isNetworkError: true, 
      status: error.status || 500 
    };
  }
};

/**
 * Creates a FormData object from a file
 * @param {File} file - The file to upload
 * @param {string} fieldName - The field name to use in the form data
 * @returns {FormData} - The FormData object
 */
export const createFormDataFromFile = (file, fieldName = 'file') => {
  const formData = new FormData();
  formData.append(fieldName, file);
  return formData;
};

/**
 * Generates a base path for resource endpoints
 * @param {string} resourceType - The type of resource (e.g., 'resources', 'events')
 * @param {string} orgId - The organization ID
 * @returns {string} - The base path
 */
export const getResourceBasePath = (resourceType, orgId) => {
  return `/api/v1/organizations/${orgId}/${resourceType}`;
}; 