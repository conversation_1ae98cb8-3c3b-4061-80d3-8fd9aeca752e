import React from 'react';
import { Typography, Layout, Space, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import '../../styles/UserAgreement.css';

const { Title, Text, Paragraph } = Typography;
const { Content } = Layout;

const PrivacyPolicy = () => {
    const navigate = useNavigate();
    const { t } = useTranslation();

    const sections = [
        'dataCollection',
        'dataUsage',
        'dataProtection',
        'dataSharing',
        'yourRights'
    ];

    return (
        <Layout className="agreement-layout">
            <Content className="agreement-content">
                <div className="agreement-container">
                    <Title level={2}>{t('privacyPolicy.title')}</Title>
                    <Text type="secondary">{t('privacyPolicy.updatedAt')}</Text>

                    <Space direction="vertical" size="large" className="sections">
                        {sections.map((section) => (
                            <div key={section} className="section">
                                <Title level={4}>
                                    {t(`privacyPolicy.sections.${section}.title`)}
                                </Title>
                                <Paragraph>
                                    {t(`privacyPolicy.sections.${section}.content`)}
                                </Paragraph>
                            </div>
                        ))}
                    </Space>

                    <Paragraph className="footer">
                        {t('privacyPolicy.footer')}
                    </Paragraph>
                </div>
            </Content>
        </Layout>
    );
};

export default PrivacyPolicy; 