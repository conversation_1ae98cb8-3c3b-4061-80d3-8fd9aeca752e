# Membership SAAS System Backend

This document provides an overview of the backend system for the Membership SAAS platform.

## Project Overview

The backend API services the frontend application, providing functionalities for user management, authentication, event management, content posting, and more.

## Technologies Used

- **Language:** Go (version 1.24.2)
- **Framework:** Echo (v4.13.3)
- **Database:** PostgreSQL (v17-alpine, interacted via pgx/v5 v5.7.4)
- **Migrations:** golang-migrate/migrate (v4.18.3)
- **ORM/Query Builder:** sqlc (inferred from `db/sqlc.yaml` and `*.sql.go` files)
- **Authentication:** JWT (golang-jwt/jwt/v5 v5.2.2, labstack/echo-jwt/v4 v4.3.1)
- **Validation:** go-playground/validator (v10.26.0)
- **Logging:** zerolog (v1.34.0)
- **Environment Variables:** godotenv (v1.5.1)
- **UUID Generation:** google/uuid (v1.6.0)
- **Slug Generation:** gosimple/slug (v1.15.0)
- **WebSocket:** gorilla/websocket (v1.5.3)
- **SMS/OTP:** Twilio (twilio/twilio-go v1.26.0)
- **API Documentation:** OpenAPI (docs generated using `kohkimakimoto/echo-openapidocs` v0.2.0, spec at `docs/swagger.yaml`)
- **Containerization:** Docker (`Dockerfile` using `golang:1.24-alpine`, `docker-compose.yml`)
- **Live Reload:** Air (`github.com/air-verse/air`)

## Project Structure

```
.
├── .air.toml
├── .env.example
├── .gitignore
├── Dockerfile
├── README.md
├── admin-webapp-resources-integration-guide.md
├── admin_events_integration_guide.md
├── admin_volunteer_management_integration_guide.md
├── assets/
│   ├── init/
│   │   └── seed_data.json
│   └── orgs_logos/
│       ├── blue-logo.jpeg
│       ├── default-logo.png
│       ├── pink-logo.png
│       ├── project-tomorrow.png
│       └── shatin-west.jpg
├── cmd/
│   └── api/
│       └── main.go
├── db/
│   ├── migrations/
│   │   ├── 000001_initial_schema.down.sql
│   │   ├── 000001_initial_schema.up.sql
│   │   ├── ... (more migration files up to 000031_add_check_in_details_to_event_registrations.up.sql)
│   │   └── 000031_add_check_in_details_to_event_registrations.up.sql
│   ├── queries/
│   │   ├── auth_flows.sql
│   │   ├── ... (more sql query files for sqlc)
│   │   └── volunteer_management.sql
│   ├── admin_token.json
│   ├── auth_flows.sql.go
│   ├── db.go
│   ├── event_conflict_check.sql.go
│   ├── event_management.sql.go
│   ├── event_registration_queries.sql.go
│   ├── generate.go
│   ├── jobs.sql.go
│   ├── models.go
│   ├── organizations.sql.go
│   ├── otp_attempts.sql.go
│   ├── post_tags.sql.go
│   ├── posts.sql.go
│   ├── querier.go
│   ├── refresh_tokens.sql.go
│   ├── resources.sql.go
│   ├── sqlc.yaml
│   ├── user_verifications.sql.go
│   ├── user.sql.go
│   ├── users.sql.go
│   └── volunteer_management.sql.go
├── docker-compose.yml
├── docs/
│   ├── docs.go
│   ├── swagger.json
│   └── swagger.yaml
├── frontend_post_integration_guide.md
├── go.mod
├── go.sum
├── internal/
│   ├── authn/
│   ├── constants/
│   ├── handlers/
│   ├── middleware/
│   ├── payloads/
│   ├── services/
│   ├── token/
│   ├── twilio_service/
│   └── utils/
├── main (executable, if built locally)
├── mobile_app_integration_guide.md
├── plan3.md
├── swaggo_task.md
├── token.sh
├── token.txt
├── tmp/ (typically contains build artifacts or logs like air_build.log)
│   ├── air_build.log
│   └── app (air build output)
└── uploads/
    ├── dummy_passport.txt
    ├── event-media/
    └── post-media/
```
*(Note: Some generated files, cache directories, and local user-specific files are omitted for brevity as they are typically not part of the core deployable project structure.)*

## Naming Conventions

- **`authn`**: Refers to authentication (verifying who a user is).
- **`authz`**: Refers to authorization (determining what a user is allowed to do).

## Authentication Flows (authn)

The system supports two primary PKCE-based authentication flows:

### 1. Regular User Authentication (Phone OTP)

- **Trigger:** User enters their phone number.
- **Process:**
    1. Frontend sends the phone number to `POST /api/v1/authn/phone/check`.
    2. Backend checks if the phone number exists in the `users` table.
    3. If exists: Backend confirms, and frontend initiates a PKCE flow by calling `POST /api/v1/authn/phone/otp/initiate` (providing `code_challenge`, `state`, `phone_otp_channel: "sms"|"whatsapp"` (optional), etc.).
    4. If not exists: Backend informs the frontend, which then prompts the user with a registration flow (see below).
    5. User receives OTP, submits it with `code_verifier` and `state` to `POST /api/v1/authn/phone/otp/verify`.
    6. Backend verifies OTP (with Twilio), PKCE, and issues tokens.
- **Access Level:** Grants access to regular user APIs.

### 2. New User Registration (Phone OTP)

- **Trigger:** User enters phone number, `/authn/phone/check` returns `exists: false`.
- **Process:**
    1. Frontend initiates registration PKCE flow by calling `POST /api/v1/authn/register/phone/initiate` (providing phone, `code_challenge`, `code_challenge_method`, `state`, `phone_otp_channel: "sms"|"whatsapp"` (optional), and `client_id` (e.g., `"default_client"`)).
    2. User receives OTP, submits it with `code_verifier`, `state`, `display_name`, and `client_id` (and other optional profile info) to `POST /api/v1/authn/register/phone/verify`.
    3. Backend verifies OTP (with Twilio), PKCE, creates the user record, and issues tokens.
- **Access Level:** Grants access to regular user APIs.

### 3. Staff Authentication (Email + Password)

- **Trigger:** Staff member (admin/manager) uses the email and password login option.
- **Process:**
    1. Frontend generates a `code_verifier`, `code_challenge` (S256), and a unique `state` string.
    2. Frontend calls `POST /api/v1/authn/staff/login/initiate` with `email`, `client_id`, `code_challenge`, `code_challenge_method`, and the client-generated `state` in the JSON payload.
    3. Backend returns a `flow_id` and echoes the `state`.
    4. Frontend calls `POST /api/v1/authn/staff/login/verify` with the `state` (received from step 3, which should match the client's originally generated state), `email`, `password`, and `code_verifier`.
    5. Backend verifies credentials, PKCE, and issues JWT tokens.
- **Access Level:** Grants access to management APIs.

**Note:** Staff members can also use the regular phone OTP login if they have a verified phone number. However, this will only grant them access to regular user APIs, not management APIs.

## Organizations Management

The system will allow users to be part of one or more organizations. This enables features specific to organizational contexts, such as shared resources, events, or content within an organization.

- **Default Organization**: Upon registration, users will automatically be added to a global "Default Organization". This organization primarily serves as a placeholder and ensures every user belongs to at least one organization initially. It is not intended for specific collaborative features.
- **Joining Organizations**: After registration, the frontend will typically prompt users to discover and join other relevant organizations.
- **Organization Branding and Status**: Organizations can have an `image_url` (for a logo/banner) and a `theme_color`. They also have a `status` field, which can be `pending_setup`, `active`, or `suspended`.
- **Activation Flow**: Typically, an organization is created with a `status` of `pending_setup`. An administrator then uploads an image for the organization (logo), which sets the `image_url` and transitions the organization's `status` to `active`.
- **Organization CRUD**: Authenticated users will be able to manage organizations. Initial permission checks for updating and deleting organizations are role-based (requiring "owner" or "admin" role within that specific organization). More granular permissions via SpiceDB are planned for later.
- **Membership**: Users can be members of multiple organizations with varying roles/permissions within each. Initial roles include "member", "admin", and "owner".

## Database Schema

The database schema is managed via migrations located in `db/migrations`. Key tables include:

- `users`
- `auth_flows`
- `organizations` (Includes `name`, `description`, `owner_user_id`, `is_default_org`, `image_url TEXT`, `theme_color VARCHAR(50)`, `status VARCHAR(50)`, `created_at`, `updated_at`)
- `user_organization_memberships`
- `user_verification_requests`
- `verification_documents`
- `verification_address_proof`
- `verification_hk_id_card`
- `verification_hk_youth_plus`
- `verification_mainland_china_id_cards`
- `verification_mainland_travel_permits`
- `verification_passports`
- `verification_student_ids`
- `user_volunteer_applications` (Stores applications from users to become volunteers for an organization. An application with `status = 'approved'` signifies an organization-level volunteer qualification.)
- `events` (Note: `location_address_line1`, `city`, etc. consolidated into `location_full_address`; `is_government_funded` removed)
- `event_tags`
- `event_event_tags`
- `event_required_verification_types`
- `event_registrations` (NEW: `check_in_by_user_id` UUID NULL REFERENCES users(id), `check_in_method` VARCHAR(50) NULL)
- `event_media_items`
- `government_funding_types` (NEW)
- `event_government_funding_types` (NEW: Many-to-many link table)
- `jobs` (NEW)
- `posts` (Note: `cover_image_url` field removed from API responses & DB, `is_hidden`, `is_for_banner` removed from DB; `author_id` is now non-nullable and populated from token; cover image derived from first `post_media_item`; `status` can include 'hidden')
- `post_media_items`
- `post_tags` (NEW - For defining post tags, e.g., name_zh)
- `post_post_tags` (NEW - Many-to-many link table for posts and post_tags)
- `resources`
- `resource_files`
- `organization_files`

(Refer to `db/migrations/*.up.sql` for detailed schema definitions, including seeding of superadmin, default org, event tags, and additional organizations/users. The latest migration is `000031_add_check_in_details_to_event_registrations.up.sql`.)

## API Endpoints

All endpoints are prefixed with `/api/v1`.

### Public API Endpoints

No authentication required.

- `POST /users/register/initiate`: Initiate user registration process.
- `POST /users/register/verify`: Verify user registration with OTP.
- `POST /users/login/initiate`: Initiate user login process.
- `POST /users/login/verify`: Verify user login with OTP.
- `POST /users/token/refresh`: Refresh JWT access token using a refresh token.
- `GET /docs/*`: Serves Swagger UI for API documentation. (Generated by `echo-openapidocs`)
- `GET /openapi.yaml`: Serves the OpenAPI 3.0 specification file.
- `GET /api/v1/swagger-ui/`: Serves Swagger UI for API documentation. (Generated by `kohkimakimoto/echo-openapidocs`)
- `GET /api/v1/swagger.yaml`: Serves the OpenAPI 3.0 specification file. (Path might vary based on actual serving setup in `main.go`)
- `GET /organizations`: List all public organizations. Supports pagination.
- `GET /organizations/:slug`: Get details of a specific organization by its slug.
- `GET /events`: List all public events. Supports filtering (e.g., by organization, date, type) and pagination.
- `GET /events/:eventId`: Get details of a specific event by its ID.
- `GET /posts`: List all public posts/articles. Supports filtering and pagination.
- `GET /posts/:slug`: Get details of a specific post by its slug.
- `GET /ws/chat/:organizationID`: WebSocket endpoint for real-time chat within an organization. (Uses `optionalJwtAuthMiddleware` for potential authenticated features within the chat)

### Authenticated API Endpoints (User)

Requires JWT Bearer Token in Authorization header. All routes are prefixed with `/auth`. Middleware: `jwtAuthMiddleware`.

- **User Profile & Management:**
    - `GET /me`: Get current authenticated user's profile details.
    - `PUT /me/profile`: Update current user's profile information.
    - `POST /me/phone/change/initiate`: Initiate phone number change for the current user.
    - `POST /me/phone/change/verify`: Verify phone number change with OTP.
    - `POST /me/deactivate`: Deactivate the current user's account.
    - `POST /logout`: Logout user (invalidates the refresh token).
- **User Organization Interactions:**
    - `GET /me/organizations`: List organizations the current user is a member of or follows.
- **Event Interactions:**
    - `GET /me/registrations`: List all event registrations for the current user.
    - `POST /events/:eventId/register`: Register the current user for a specific event.
    - `DELETE /events/:eventId/register`: Cancel the current user's registration for an event.
    - `GET /events/:eventId/qr-code`: Get a QR code for the current user's event registration (for check-in).
- **Volunteer Features:**
    - `GET /volunteer/preferences`: Get the current user's volunteer preferences.
    - `POST /volunteer/preferences`: Set or update the current user's volunteer preferences.
    - `GET /volunteer/opportunities`: List available volunteer opportunities based on user preferences or general availability.
    - `POST /volunteer/assignments/:opportunityId/apply`: Apply for a specific volunteer opportunity.
    - `GET /volunteer/assignments`: List the current user's volunteer assignments (applied, accepted, completed).

### Admin API Endpoints

Requires JWT Bearer Token + Admin privileges. All routes are prefixed with `/admin`. Middleware: `jwtAuthMiddleware`, `adminRequiredMiddleware`.

- **Organization Management:**
    - `POST /organizations`: Create a new organization.
    - `PUT /organizations/:orgId`: Update an existing organization's details.
    - `POST /organizations/:orgId/logo`: Upload or update an organization's logo.
    - `GET /organizations/:orgId/members`: List members of a specific organization.
    - `POST /organizations/:orgId/members`: Add a new member to an organization (e.g., assign a role).
    - `DELETE /organizations/:orgId/members/:userId`: Remove a member from an organization.
    - `PUT /organizations/:orgId/members/:userId/role`: Update an organization member's role.
- **User Management (Global Admin Scope):**
    - `GET /users`: List all users in the system. Supports pagination and filtering.
    - `GET /users/:userId`: Get detailed information for a specific user.
    - `PUT /users/:userId/status`: Update a user's status (e.g., activate, deactivate, ban).
- **Event Management (Typically Organization-Scoped Admin):**
    - `POST /organizations/:orgId/events`: Create a new event for a specific organization.
    - `PUT /organizations/:orgId/events/:eventId`: Update an existing event.
    - `DELETE /organizations/:orgId/events/:eventId`: Delete an event.
    - `GET /organizations/:orgId/events/:eventId/registrations`: List all registrations for a specific event.
    - `POST /organizations/:orgId/events/:eventId/registrations/:registrationId/check-in`: Manually check-in a user for an event. (Payload: `{ "check_in_by_user_id": "uuid", "check_in_method": "manual/qr_scan" }`)
    - `GET /organizations/:orgId/events/:eventId/attendance-report`: Generate and retrieve an attendance report for an event.
- **Volunteer Opportunity Management (Organization-Scoped Admin):**
    - `POST /organizations/:orgId/volunteer/opportunities`: Create a new volunteer opportunity for an organization.
    - `PUT /organizations/:orgId/volunteer/opportunities/:opportunityId`: Update an existing volunteer opportunity.
    - `DELETE /organizations/:orgId/volunteer/opportunities/:opportunityId`: Delete a volunteer opportunity.
    - `GET /organizations/:orgId/volunteer/opportunities/:opportunityId/applicants`: List applicants for a volunteer opportunity.
    - `PUT /organizations/:orgId/volunteer/opportunities/:opportunityId/applicants/:applicationId/status`: Update the status of an application (e.g., accept, reject).
- **Content Management (Posts/Articles - Organization-Scoped Admin):**
    - `POST /organizations/:orgId/posts`: Create a new post for an organization.
    - `PUT /organizations/:orgId/posts/:postId`: Update an existing post.
    - `DELETE /organizations/:orgId/posts/:postId`: Delete a post.
    - `POST /organizations/:orgId/posts/:postId/media`: Upload media (images, attachments) for a post.
- **Resource Management (Admin):**
    - `POST /resources`: Create a new shared resource (e.g., document template, guide).
    - `GET /resources`: List all shared resources.
    - `GET /resources/:resourceId`: Get details of a specific resource.
    - `PUT /resources/:resourceId`: Update a resource.
    - `DELETE /resources/:resourceId`: Delete a resource.
- **System & Monitoring (Global Admin Scope):**
    - `GET /jobs`: List background jobs and their statuses.
    - `GET /jobs/:jobId`: Get details of a specific background job.
- **Volunteer Application Management (Admin Views):**
    - `GET /api/v1/events/{event_id}/volunteer-applications/pending-review`: List pending volunteer applications for a specific event. (Requires JWT Auth; user should typically have rights to view event details or be an admin/org admin).
    - `GET /api/v1/organizations/{organization_id}/volunteer-applications/pending-review`: List pending volunteer applications for all events within a specific organization. (Requires JWT Auth + Staff role).
    - `GET /api/v1/admin/volunteer-applications/pending-review`: List all pending volunteer applications across the system. (Requires JWT Auth + Staff role, typically for System Admin).

### User Profile (`/users/me`) - Requires Auth

- `GET /users/me`: Retrieve current user's profile.
- `PATCH /users/me`: Update current user's profile (display name, notification settings, language preferences, or directly set `profile_picture_url`). For new image uploads, prefer `POST /users/me/profile-picture`.
- `POST /users/me/profile-picture`: Upload a new profile picture for the current user.
    - **Authentication**: Required (Bearer Token).
    - **Request**: `multipart/form-data` with a `file` field containing the image (e.g., JPEG, PNG, GIF, WebP; max 5MB).
    - **Response**: `200 OK` with the updated user profile object, including the new `profile_picture_url`.
- `POST /users/me/phone/initiate-change`: Start the process to change the user's phone number. [Tested]
- `POST /users/me/phone/verify-change`: Verify the OTP for the new phone number. [Tested]
- `GET /users/me/organizations`: List organizations the current user is a member of. [Implemented]
- `GET /users/me/stats`: Retrieve statistics for the current user (total events, user registration timestamp, volunteer events, monthly attended events, top event tags). [NEW]

#### Get My Organizations

-   **Endpoint**: `GET /users/me/organizations`
-   **Description**: Retrieves a list of organizations that the currently authenticated user is an active member of.
-   **Authentication**: Required (Bearer Token).
-   **Response**: `200 OK` with a JSON array of organization objects. Each object includes:
    -   `id` (UUID): The organization's ID.
    -   `name` (string): The organization's name.
    -   `description` (string, nullable): A description of the organization.
    -   `owner_user_id` (UUID): The ID of the user who owns the organization.
    -   `is_default_org` (boolean): Indicates if this is the default global organization.
    -   `image_url` (string, nullable): URL for the organization's logo/image.
    -   `theme_color` (string, nullable): A theme color for the organization (e.g., hex code).
    -   `status` (string): The current status of the organization (e.g., `pending_setup`, `active`).
    -   `created_at` (timestamp): The time the organization was created.
    -   `updated_at` (timestamp): The time the organization was last updated.
    ```json
    [
        {
            "id": "00000000-0000-0000-0000-000000000002",
            "name": "Default Organization",
            "description": "The global default organization for all users.",
            "owner_user_id": "00000000-0000-0000-0000-000000000001",
            "is_default_org": true,
            "image_url": "https://example.com/default_org_logo.png",
            "theme_color": "#CCCCCC",
            "status": "active",
            "created_at": "2023-01-01T12:00:00Z",
            "updated_at": "2023-01-01T12:00:00Z"
        }
        // ... other organizations the user is a member of
    ]
    ```

### Organizations (`/organizations`) - Requires Auth

- `POST /organizations`: Create a new organization (user becomes owner).
    - Request body can include `name` (required), `description` (optional), `image_url` (optional), `theme_color` (optional).
    - The organization is created with `status: "pending_setup"` by default.
    - Response includes the new organization object with `id`, `name`, `description`, `owner_user_id`, `is_default_org`, `image_url`, `theme_color`, `status`, `created_at`, `updated_at`.
- `GET /organizations`: List all organizations (visibility might depend on user role/membership, or require Admin/System permission. Paginated). Response items include `image_url`, `theme_color`, and `status`.
- `GET /organizations/{orgId}`: Get details of a specific organization. Response includes `image_url`, `theme_color`, and `status`.
- `PUT /organizations/{orgId}`: Update an organization (requires admin/owner role in that org).
    - Request body can include `name`, `description`, `image_url`, `theme_color`, and `status` (all optional).
    - Response includes the updated organization object.
- `DELETE /organizations/{orgId}`: Delete an organization (requires owner role in that org).
- `POST /organizations/{orgId}/logo`: Upload a logo for the specified organization.
    - **Description**: Uploads a logo (image file) for the organization. This action updates the organization's `image_url` and sets its `status` to `active`.
    - **Authentication**: Required (Bearer Token). User must have appropriate permissions for the organization (e.g., admin or owner).
    - **Request**: `multipart/form-data` with a `file` field containing the image.
    - **Response**: `200 OK` with the updated organization object, including the new `image_url` and `status: "active"`.
- `POST /organizations/{orgId}/join`: Join a specific organization as a member.
- `DELETE /organizations/{orgId}/leave`: Leave a specific organization.

### Events (Organization Context) (`/organizations/{orgId}/events`) - Requires Org Auth

- `POST /organizations/{orgId}/events`: Create a new event within an organization. Payload includes details like `title`, `JsonContent`, `location_type`, `location_full_address` (if physical/hybrid), `location_online_url` (if online/hybrid), `start_time`, `end_time`, `timezone`, `participant_limit`, `waitlist_limit`, `requires_approval_for_registration`, `tag_ids`, `government_funding_type_ids`, etc. [Tested]
- `GET /organizations/{orgId}/events`: List events managed by the organization (pagination, potentially admin-only filters). Response includes `totalCount` in `X-Total-Count` header. [Tested]
- `GET /organizations/{orgId}/events/{eventId}`: Get detailed information about a specific event within the organization (includes admin details like counts). [Tested]
- `PUT /organizations/{orgId}/events/{eventId}`: Update details of an existing event. Payload similar to create, with optional fields. Updates `location_full_address`, `government_funding_type_ids`, etc. [Tested]
- `PATCH /organizations/{orgId}/events/{eventId}/status`: Update the status of an event (e.g., 'draft', 'published', 'past', 'cancelled'). [Tested]
    *   **Important Note**: This `PATCH` endpoint is the required method for changing an event's status. The `PUT .../events/{eventId}` endpoint may not update the status field. The payload for this `PATCH` request must use the key `"new_status"`, e.g., `{"new_status": "published"}`.
- `DELETE /organizations/{orgId}/events/{eventId}`: Delete an event.
- `POST /organizations/{orgId}/events/{eventId}/tags/{tagId}`: Add a tag to an event. (Note: path includes tagId)
- `DELETE /organizations/{orgId}/events/{eventId}/tags/{tagId}`: Remove a tag from an event.
- `GET /organizations/{orgId}/events/{eventId}/tags`: List tags for a specific event.
- `POST /organizations/{orgId}/events/{eventId}/verification-types/{typeKey}`: Add a required verification type to an event.
- `DELETE /organizations/{orgId}/events/{eventId}/verification-types/{typeKey}`: Remove a required verification type from an event.
- `GET /organizations/{orgId}/events/{eventId}/verification-types`: List required verification types for an event.
- `GET /organizations/{orgId}/events/{eventId}/registrations`: List registrations for a specific event (admin view, includes user details). Response includes `totalCount` in `X-Total-Count` header.
- `PATCH /organizations/{orgId}/events/{eventId}/registrations/{registrationId}/status`: (Admin) Update status of a specific registration (e.g., approve, reject, mark absent).
- `POST /organizations/{orgId}/events/{eventId}/registrations/{registrationId}/attendance`: (Admin/Staff) Mark attendance for a specific registration.
- `PATCH /organizations/{orgId}/events/{eventId}/registrations/{registrationId}/payment`: (Admin/Staff) Update payment status for a specific registration.
- `GET /organizations/{orgId}/events/{eventId}/volunteer-applications`: List volunteer applications for a specific event (admin view). Response includes `totalCount` in `X-Total-Count` header.
- `PATCH /organizations/{orgId}/events/{eventId}/volunteer-applications/{applicationId}/review`: (Admin) Review a volunteer application for an event.
- `POST /organizations/{orgId}/events/{eventId}/media`: Add a media item (e.g., image, video) to an event (file upload).
- `GET /organizations/{orgId}/events/{eventId}/media`: List media items for an event.
- `DELETE /organizations/{orgId}/events/{eventId}/media/{itemId}`: Delete a media item from an event.

### Events (Public) (`/events`) - Public Access

- `GET /events`: List public events. Supports filtering by `orgId` (organization UUID), `startDate`, `endDate`, `searchTerm` (searches title and `location_full_address`), `tagIds`, and `status` (e.g., 'published', 'past'). Supports sorting via `sortBy` (e.g., `popularity_desc` for most popular, `start_time_asc` for earliest start time). Supports pagination (`limit`, `offset`). Response includes `totalCount` in `X-Total-Count` header. [Tested]
    - **Example Filtering Request**:
      ```
      GET /api/v1/events?orgId=your-org-uuid&status=upcoming&tagIds=tagId1,tagId2&searchTerm=Community%20Meetup&startDate=2024-08-01T00:00:00Z&limit=10&offset=0
      ```
    - **Example Sorting Request (Popular Events for an Org)**:
      ```
      GET /api/v1/events?orgId=your-org-uuid&sortBy=popularity_desc&limit=5
      ```
- `GET /events/{eventId}`: Get public details of a specific event. [Tested]
    - **Example Request**:
      ```
      GET /api/v1/events/your-event-uuid
      ```
      *(Replace `your-event-uuid` with the actual event ID.)*
- `GET /events/{eventId}/registration`: (User) Get the current user's registration details for a specific event. Requires auth.
    - **Example Request**:
      ```
      GET /api/v1/events/your-event-uuid/registration
      Authorization: Bearer <user_jwt_token>
      ```
      *(Replace `your-event-uuid` with the actual event ID. Requires user authentication.)*

### Event Registrations (`/event-registrations`) - Requires Auth

- `POST /event-registrations/check-in`: Check in the authenticated user to the closest upcoming event they are registered for.
    - **Description**: Automatically identifies the closest eligible event within the check-in window (±30 minutes) and marks the user as attended.
    - **Authentication**: Required (Bearer Token).
    - **Request Body**: Empty (`{}`).
    - **Response**: `200 OK` with registration details on success, or appropriate error status (404 if no eligible event, 409 if already checked in).
    - **Implementation Status**: ⚠️ Implemented but encountering enum type mismatches in the database

- `GET /event-registrations/me`: List all event registrations for the current user.
    - **Description**: Returns paginated list of the user's event registrations with extensive filtering options.
    - **Authentication**: Required (Bearer Token).
    - **Query Parameters**: 
        - `limit`, `offset`: Pagination controls.
        - `start_date`, `end_date`: Filter by event date range (ISO 8601 format).
        - `status`: Filter by event status (`upcoming`, `active`, `past`, `cancelled`).
        - `sort`: Sort order (`date_asc`, `date_desc`).
        - `role`: Filter by user's role (`attendee`, `volunteer`).
        - `organization_id`: Filter by organization UUID.
    - **Response**: `200 OK` with array of registration details, includes `X-Total-Count` header.
    - **Implementation Status**: ✅ Basic functionality working, returns empty array if no registrations

- `POST /event-registrations`: Register for an event.
    - **Description**: Registers the authenticated user for a specific event, with validation for time conflicts.
    - **Authentication**: Required (Bearer Token).
    - **Request Body**: `{ "event_id": "uuid" }`.
    - **Response**: `201 Created` with registration details on success, or appropriate error status.
    - **Implementation Status**: ⚠️ Implemented but encountering enum type mismatches in the database

- `GET /event-registrations/{registrationId}`: Get details of a specific registration.
    - **Description**: Retrieves details of a specific event registration owned by the user.
    - **Authentication**: Required (Bearer Token).
    - **Response**: `200 OK` with registration details or `404 Not Found`.
    - **Implementation Status**: 🚧 Stub implementation

- `DELETE /event-registrations/{registrationId}`: Cancel a registration.
    - **Description**: Cancels the user's own registration for an event.
    - **Authentication**: Required (Bearer Token).
    - **Response**: `200 OK` with updated registration details (status: cancelled).
    - **Implementation Status**: 🚧 Stub implementation

- `PATCH /event-registrations/{registrationId}/payment-status`: Update payment status.
    - **Description**: Updates the payment status of a registration (public endpoint).
    - **Authentication**: None required.
    - **Request Body**: `{ "new_payment_status": "string", "staff_id": "uuid" }`.
    - **Response**: `200 OK` with updated registration details.
    - **Implementation Status**: 🚧 Stub implementation

### Organization Event Registrations (`/organizations/{orgId}/event-registrations`) - Requires Auth

- `GET /organizations/{orgId}/event-registrations`: List event registrations for an organization.
    - **Description**: Admin view of all registrations for the organization, with filtering options.
    - **Authentication**: Required (Bearer Token + Admin role).
    - **Query Parameters**: 
        - `limit`, `offset`: Pagination controls.
        - `event_id`: Filter by specific event.
        - `start_date`, `end_date`: Filter by date range.
        - `status`: Filter by registration status.
        - `user_id`: Filter by specific user.
        - `search_name`: Search by user name.
        - `payment_status`: Filter by payment status.
    - **Response**: `200 OK` with array of registration details, includes `X-Total-Count` header.
    - **Implementation Status**: 🚧 Planned

- `PATCH /organizations/{orgId}/event-registrations/{registrationId}/status`: Update registration status.
    - **Description**: Admin action to update a registration's status (approve, reject, mark attendance).
    - **Authentication**: Required (Bearer Token + Admin role).
    - **Request Body**: `{ "new_status": "string", "admin_notes": "string" }`.
    - **Response**: `200 OK` with updated registration details.
    - **Implementation Status**: 🚧 Planned

### User Verification (`/users/me/verifications`, `/admin/verifications`) - Requires Auth

**User-Facing (`/users/me/verifications`)**

- `POST /`: Submit a new verification request (multipart/form-data).
- `GET /`: List all verification requests for the current user.
- `GET /{reqID}`: Get details of a specific verification request.
- `DELETE /{reqID}`: Request deletion of verification data associated with a request.
- `GET /documents/{docID}`: Download the document associated with a user's request.

**Admin-Facing (`/admin/verifications`)** (Requires JWT + Staff privileges)

- `GET /pending`: List all verification requests with status `pending_review`.
- `GET /{reqID}`: Get full details of a specific verification request (including user info).
- `PATCH /{reqID}/review`: Approve or reject a verification request.
- `GET /documents/{docID}`: Download the document associated with any verification request.
- `GET /all`: (Testing Only) List all verification requests regardless of status.

### Volunteer Management - Requires Auth

**User-Facing**

- `GET /users/me/volunteer/applications`: List current user's volunteer applications across all orgs.
- `GET /users/me/volunteer/applications/{appId}`: Get details of a specific volunteer application owned by the user.
- `GET /users/me/volunteer/qualifications`: List organizations where the user has an approved volunteer application (i.e., is qualified).
- `POST /organizations/{orgId}/volunteer/apply`: Apply for volunteer qualification in a specific organization.
- `GET /organizations/{orgId}/volunteer/status`: Check if the user has an approved volunteer application for a specific organization (i.e., their qualification status).

**Admin-Facing (`/admin/organizations/{orgId}/volunteer`)** (Requires JWT + Staff privileges + Org Check TBD)

- `GET /applications/pending`: List pending volunteer applications for a specific organization.
- `GET /applications/{appId}`: Get details of a specific volunteer application for admin review (includes user info and verification docs).
- `PATCH /applications/{appId}/review`: Approve or reject a volunteer application.

### Post Tags Management (`/post-tags`)

Endpoints for managing post tags.

#### Create Post Tag

-   **Endpoint**: `POST /api/v1/post-tags`
-   **Description**: Creates a new post tag.
-   **Authentication**: Admin required.
-   **Request Body**:
    ```json
    {
        "name_en": "Technology",
        "name_zh": "技術"
    }
    ```
    *   `name_en` (string, required): English name of the tag.
    *   `name_zh` (string, required): Chinese name of the tag.
-   **Response Body**: `201 Created`
    ```json
    {
        "id": "uuid-string-for-tag",
        "name_en": "Technology",
        "name_zh": "技術",
        "created_at": "timestamp",
        "updated_at": "timestamp"
    }
    ```

#### List All Post Tags

-   **Endpoint**: `GET /api/v1/post-tags`
-   **Description**: Retrieves a list of all post tags.
-   **Authentication**: Public.
-   **Response Body**: `200 OK`
    ```json
    [
        {
            "id": "uuid-string-for-tag-1",
            "name_en": "Technology",
            "name_zh": "技術",
            "created_at": "timestamp",
            "updated_at": "timestamp"
        },
        {
            "id": "uuid-string-for-tag-2",
            "name_en": "News",
            "name_zh": "新聞",
            "created_at": "timestamp",
            "updated_at": "timestamp"
        }
    ]
    ```

#### Get Post Tag by ID

-   **Endpoint**: `GET /api/v1/post-tags/{tagId}`
-   **Description**: Retrieves a specific post tag by its ID.
-   **Authentication**: Public.
-   **Response Body**: `200 OK`
    ```json
    {
        "id": "uuid-string-for-tag",
        "name_en": "Technology",
        "name_zh": "技術",
        "created_at": "timestamp",
        "updated_at": "timestamp"
    }
    ```

#### Update Post Tag

-   **Endpoint**: `PUT /api/v1/post-tags/{tagId}`
-   **Description**: Updates an existing post tag.
-   **Authentication**: Admin required.
-   **Request Body**:
    ```json
    {
        "name_en": "Updated Technology",
        "name_zh": "更新技術"
    }
    ```
    *   `name_en` (string, optional): New English name of the tag.
    *   `name_zh` (string, optional): New Chinese name of the tag.
-   **Response Body**: `200 OK`
    ```json
    {
        "id": "uuid-string-for-tag",
        "name_en": "Updated Technology",
        "name_zh": "更新技術",
        "created_at": "timestamp",
        "updated_at": "timestamp"
    }
    ```

#### Delete Post Tag

-   **Endpoint**: `DELETE /api/v1/post-tags/{tagId}`
-   **Description**: Deletes a post tag.
-   **Authentication**: Admin required.
-   **Response Body**: `204 No Content`

### Post-Tag Associations (`/organizations/{orgId}/posts/{postId}/tags`)

Endpoints for managing the association between posts and tags.

#### Add Tag to Post

-   **Endpoint**: `POST /api/v1/organizations/{orgId}/posts/{postId}/tags/{tagId}`
-   **Description**: Adds an existing tag to a specific post.
-   **Authentication**: Requires organization authentication (e.g., admin/editor role for the post).
-   **Response Body**: `204 No Content`

#### Remove Tag from Post

-   **Endpoint**: `DELETE /api/v1/organizations/{orgId}/posts/{postId}/tags/{tagId}`
-   **Description**: Removes a tag from a specific post.
-   **Authentication**: Requires organization authentication (e.g., admin/editor role for the post).
-   **Response Body**: `204 No Content`

#### List Tags for a Post

-   **Endpoint**: `GET /api/v1/organizations/{orgId}/posts/{postId}/tags`
-   **Description**: Retrieves all tags associated with a specific post.
-   **Authentication**: Requires organization authentication.
-   **Response Body**: `200 OK`
    ```json
    [
        {
            "id": "uuid-string-for-tag-1",
            "name_en": "Technology",
            "name_zh": "技術",
            "created_at": "timestamp",
            "updated_at": "timestamp"
        }
        // ... other tags for the post
    ]
    ```
### Content Management (Posts) (`/organizations/{orgId}/posts`, `/posts`)

**Organization Specific (Management - Requires Auth)**

- `POST /organizations/{orgId}/posts`: Create a new post within an organization. (Author ID is derived from token. `cover_image_url`, `is_hidden`, `is_for_banner` are not part of the request. `status` can be 'draft', 'published', or 'hidden'. Cover image derived from first media item in response.) [Tested]
- `PUT /organizations/{orgId}/posts/{postId}`: Update an existing post. (`cover_image_url`, `is_hidden`, `is_for_banner` are not part of the request. `status` can be 'draft', 'published', or 'hidden'. Cover image derived from first media item in response.) [Tested]
- `DELETE /organizations/{orgId}/posts/{postId}`: Delete a post. [Tested]
- `GET /organizations/{orgId}/posts`: List posts for an organization (admin view, includes drafts). Each post item in the response now includes a `tags` array (`[]PostTagResponse`). Supports filtering by `tag_ids` (comma-separated UUIDs, e.g., `?tag_ids=uuid1,uuid2`).
- `GET /organizations/{orgId}/posts/{postId}`: Get details of a specific post (admin view, includes media and `tags []PostTagResponse`). [Tested]
- `POST /organizations/{orgId}/posts/{postId}/media`: Upload media (image, video, PDF) for a post. [Tested]
- `DELETE /organizations/{orgId}/posts/{postId}/media/{mediaId}`: Delete media from a post. [Tested]

**Public Access (No Auth)**

- `GET /posts`: List all published posts (supports filtering by organization, pagination, and `tag_ids` (comma-separated UUIDs, e.g., `?tag_ids=uuid1,uuid2`)). Each post item in the response now includes a `tags` array (`[]PostTagResponse`). (`cover_image_url` field not present; cover image is derived from the first `media_items` entry if available.)
- `GET /posts/{postIdOrSlug}`: Get details of a specific published post. The response now includes a `tags` array (`[]PostTagResponse`). (`cover_image_url` field not present; cover image is derived from the first `media_items` entry if available.) [Tested]

### Content Management (Resources & Files) (`/organizations/{orgId}/resources`, `/resources`, `/organizations/{orgId}/files`)

**Resources (Management - Requires Auth)**

- `POST /organizations/{orgId}/resources`: Create a new resource entry. [Tested]
- `PUT /organizations/{orgId}/resources/{resourceId}`: Update a resource entry. [Tested]
- `DELETE /organizations/{orgId}/resources/{resourceId}`: Delete a resource entry. [Tested]
- `GET /organizations/{orgId}/resources`: List resources for an organization (admin view, includes drafts). [Tested]
- `GET /organizations/{orgId}/resources/{resourceId}`: Get details of a specific resource (admin view). [Tested]
- `POST /organizations/{orgId}/resources/{resourceId}/files`: Upload a file to a specific resource. [Tested]
- `DELETE /organizations/{orgId}/resources/{resourceId}/files/{fileId}`: Delete a file from a resource. [Tested]

**Resources (Public Access)**

- `GET /resources`: List all published resources (supports filtering by organization, visibility, pagination). [Tested]
- `GET /organizations/{orgId}/resources/{resourceIdOrSlug}/public`: Get public details of a specific published resource by ID or slug (requires org context for slug lookup). [Tested]
- `GET /resources/download/{orgId}/{resourceIdOrSlug}/{fileIdOrName}`: Download a specific file from a resource (public access checks performed). [Tested]

**Organization File Management (Management - Requires Auth)**

- `POST /organizations/{orgId}/files/upload`: Upload a file directly to an organization's folder. [Tested]
- `POST /organizations/{orgId}/files/folder`: Create a new folder within an organization's file storage. (Optional `parent_folder_id` in payload). [Tested]
- `GET /organizations/{orgId}/files`: List files and folders for an organization. (Supports `parent_folder_id` query param to list contents of a subfolder). [Tested]
- `PUT /organizations/{orgId}/files/{fileOrFolderId}`: Rename a file or folder, or move it (using `parent_folder_id` in payload). [Tested]
- `DELETE /organizations/{orgId}/files/{fileOrFolderId}`: Delete a file or folder (recursively if folder). [Tested]
- `GET /organizations/{orgId}/files/{fileId}/download`: Download a file directly from organization storage. [Tested]


## WebSocket (`/ws`) - Requires Auth

- `GET /ws`: Upgrade connection to WebSocket for real-time notifications. [Tested]

### User Management (`/users`)

- `GET /events/tags`: Lists all available event tags.
- `POST /events/tags`: Creates a new event tag (admin/staff).

### Organization Specific Endpoints

-   `POST /organizations/{orgId}/files`: Uploads a file for the organization (e.g., logo, documents).
-   `GET /organizations/{orgId}/files`: Lists files for the organization.
- `DELETE /organizations/{orgId}/files/{fileId}`: Deletes a file for the organization.

### Posts (`/posts`) (Work in Progress / Planned)

- `GET /api/v1/popular-events`: Implemented and tested.
- `GET /api/v1/organizations/{orgId}/popular-events`: Implemented and tested.
- Admin/Staff event management (`/admin/events`, `/admin/events/{id}`, etc.): Partially covered by generic event endpoints, but might need dedicated admin views.
- User event registration (`POST /events/{id}/register`, `DELETE /events/{id}/registration`): Basic registration implemented.

## Progress & TODOs

### Current Progress

- **Core User Authentication:** Implemented (Registration, Login, Token Refresh, Logout, Phone Change, Deactivation).
- **Organization Management:** Basic CRUD for organizations, logo upload, member management (add, remove, role update) by admins.
- **Event Management:** Basic CRUD for events by organization admins. User registration and cancellation for events. QR code generation for event check-in. Manual check-in endpoint for admins. Attendance report generation.
- **Event Check-in:** Implemented `check_in_by_user_id` and `check_in_method` in `event_registrations` table. Admin endpoint for manual check-in.
- **Public Information:** Endpoints for listing public organizations, events, and posts.
- **Volunteer Features:** User preferences (get/set), listing opportunities, applying for opportunities, listing assignments.
- **Volunteer Opportunity Management (Admin):** CRUD for volunteer opportunities, listing applicants, updating application status.
- **Content Management (Posts - Admin):** CRUD for posts, media upload for posts.
- **Resource Management (Admin):** CRUD for shared resources.
- **Real-time Chat:** Basic WebSocket endpoint for organization chat (supports optional authentication).
- **API Documentation:** OpenAPI spec (`openapi.yaml`) and Swagger UI (`/docs`) are set up.
- **Database Migrations:** System in place, latest migration adds event check-in details.
- **Containerization:** Docker setup for development and production-like environment.
- **Live Reload:** `Air` configured for development.

## Testing Notes & Results

### Organization-Level Volunteer Application Flow (Tested 2025-05-14)

The core flow for a user applying for organization-level volunteer status and an admin approving it was tested successfully. This confirms the refactoring to use the `user_volunteer_applications` table with an `approved` status as the indicator of qualification.

**Test Steps Performed:**

1.  **New User Registration:**
    *   `POST /api/v1/authn/register/phone/initiate` (Payload: `phone`, `code_challenge`, `code_challenge_method`, `state`, `phone_otp_channel`, `client_id`) - Successful.
        *   *Note: `client_id` (e.g., `"default_client"`) was found to be a required field.*
    *   `POST /api/v1/authn/register/phone/verify` (Payload: `flow_id`, `otp`, `code_verifier`, `state`, `display_name`, `client_id`) - Successful, user created and tokens received.
2.  **User Applies for Organization Volunteer Status:**
    *   User (with token) calls `POST /api/v1/organizations/{orgId}/volunteer/apply` (Payload: `motivation`) for the Default Organization. - Successful (201), application ID received.
3.  **Admin Login:**
    *   `POST /api/v1/authn/staff/login/initiate` - Successful.
    *   `POST /api/v1/authn/staff/login/verify` - Successful, admin tokens received.
4.  **Admin Approves Volunteer Application:**
    *   Admin (with token) calls `PATCH /api/v1/admin/organizations/{orgId}/volunteer/applications/{appId}/review` (Payload: `status: "approved"`, `admin_notes`). - Successful (200), application status updated to approved.
5.  **User Checks Qualification Status:**
    *   User (with token) calls `GET /api/v1/organizations/{orgId}/volunteer/status`. - Successful (200), returned approved application details.
6.  **User Lists Their Qualifications:**
    *   User (with token) calls `GET /api/v1/users/me/volunteer/qualifications`. - Successful (200), returned a list containing the approved application.

**Conclusion:** The fundamental API endpoints for organization-level volunteer application, review, and status checking are functioning as expected after the recent refactoring.

### Event Check-in by Scanner (Participant: Test Participant, Scanner: Test Volunteer - Tested 2025-05-14)

This section outlines the intended functionality and current status of the event check-in process, particularly when the check-in is performed by a volunteer using a scanner (e.g., a mobile app).

**Intended Flow:**

1.  A participant (User A) has registered for an event and has an `event_registrations` record.
2.  A volunteer (User B) is assigned to help with check-in for that event. This volunteer must:
    *   Be a qualified volunteer for the organization hosting the event (i.e., have an approved `user_volunteer_applications` record for the organization).
    *   Be specifically approved/assigned to volunteer for *this particular event* (i.e., have an `event_volunteer_applications` record for this `event_id` and `user_id` with `status = 'approved'`).
3.  The volunteer (User B) uses a scanner application, authenticated with their own user credentials (JWT).
4.  The scanner app calls `POST /api/v1/event-registrations/check-in`.
    *   **Note:** This endpoint does *not* take `eventId` in the path. The service backend identifies the relevant event for the participant (e.g., closest upcoming/active event they are registered for).
    *   **Payload**: `{ "user_id": "<User A's UUID>", "scanned_at": "<timestamp>" }`
    *   **Authorization**: Bearer token of the volunteer (User B).
5.  The backend API (`CheckInParticipantByScanner` handler in `event_registration_handler.go`):
    *   Verifies the participant (User A) is registered for an eligible (e.g., currently active and published) event and not yet checked in.
    *   **Crucially, verifies the authority of the scanner (User B):**
        *   Checks if User B (volunteer) has an *approved* `event_volunteer_applications` record for the specific event the participant is being checked into.
        *   If not (or if the record is not `approved`), the check-in is denied (e.g., HTTP 403 Forbidden).
    *   If all checks pass, the participant's `event_registrations.checked_in_at` and `event_registrations.checked_in_by_user_id` (with User B's ID) fields are updated.

**Testing Steps & Observations (2025-05-14):**

1.  Initial attempts using `/api/v1/events/{eventId}/registrations/check-in-by-scanner` resulted in `404 Not Found` as this endpoint path was incorrect.
2.  Corrected endpoint `POST /api/v1/event-registrations/check-in` was used.
3.  Event ("Tech Meetup Spring 2025") was created, and "Test Participant" was registered.
4.  Initial check-in attempt with the correct endpoint failed with `404 Not Found - Participant not registered for an eligible event.` This was because the event's start time was in the future and its status was `draft`.
5.  Event start/end times were updated to be current, and event status was updated to `published` using admin endpoints.
6.  Subsequent check-in attempt by "Test Volunteer" (who is an approved *organization* volunteer but not yet specifically for this *event*) then resulted in `403 Forbidden - scanner not authorized for this action.`

**Current Status & Next Steps:**

*   The `CheckInParticipantByScanner` handler and its endpoint `POST /api/v1/event-registrations/check-in` are functioning.
*   The service correctly identifies eligible events for the participant based on time and status (e.g., event must be current and published).
*   **Crucially, the service correctly performs an authorization check on the scanning volunteer, requiring them to have an *approved event-specific volunteer application* (`event_volunteer_applications` record) for the target event. The 403 error received confirms this check is in place.**
*   The organization-level volunteer application and approval process (managing `user_volunteer_applications`) is functional and tested.
*   **Key Missing Piece**: The API endpoints and associated service logic for:
    *   Users (who are qualified org volunteers) to apply to volunteer for *specific events*.
    *   Admins to review and approve/assign these event-specific volunteer applications (i.e., creating and managing `event_volunteer_applications` records with an `approved` status).
*   Without these event-specific application/approval APIs, the `event_volunteer_applications` table cannot be populated through the application flow in a way that allows the scanner check-in to succeed for a volunteer. For current testing of the scanner logic itself (if a volunteer is performing the scan and is expected to be authorized), an `event_volunteer_applications` record would need to be manually inserted into the database with an `approved` status for the scanning volunteer and the specific event.

This means while the scanner *endpoint logic* is ready and correctly checks volunteer authorization for a specific event, the full *workflow* to get a volunteer authorized for that event via API calls is incomplete and requires the development of event-specific volunteer application management features.

**Event Volunteer Application Management (Admin Views for Pending Review):**
   - `GET /api/v1/events/{event_id}/event-applications/pending-review`: List pending volunteer applications for a specific event. (Requires JWT Auth; user should typically have rights to view event details or be an admin/org admin).
   - `GET /api/v1/organizations/{organization_id}/event-applications/pending-review`: List pending volunteer applications for all events within a specific organization. (Requires JWT Auth + Staff role).
   - `GET /api/v1/admin/event-applications/pending-review`: List all pending volunteer applications (for events) across the system. (Requires JWT Auth + Staff role, typically for System Admin).