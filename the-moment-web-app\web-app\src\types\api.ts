export interface UserDetailResponse {
    id: string;
    display_name: string;
    email: string;
    email_verified_at?: string | null;
    phone?: string | null;
    phone_verified_at?: string | null;
    profile_picture_url?: string | null;
    is_staff: boolean;
    verification_status?: {
        [key: string]: boolean; // doc_key: true (verified), false/undefined (not verified/submitted)
    };
}

export interface EventDetailsResponse {
    // TODO: Define other event fields based on actual API response
    id: string;
    organization_id: string; // Crucial for orgId
    title?: string; // Changed from name to title, and matches API data
    description?: string; // Example field
    start_time?: string;
    end_time?: string;
    event_verification_type_key?: string[]; // Array of strings like 'hk_id_card', 'address_proof'
    // ... more fields
}

export interface EventRegistrationResponse {
  id: string;
  event_id: string;
  user_id: string;
  organization_id: string; // Added, as it's an org-level fetch context
  status: EventRegistrationStatusType; // "registered", "attended", "cancelled", etc.
  registration_role: string; // "participant", "volunteer", etc.
  registered_at: string;
  updated_at: string; // Common to have update timestamps
  // Optional fields based on typical registration data:
  name_on_registration?: string; // If different from user's display_name
  email_on_registration?: string; // If different from user's email
  phone_on_registration?: string; // If different
  notes?: string | null; // User-provided notes during registration
  admin_notes?: string | null; // Admin notes on the registration
  payment_status?: string | null; // e.g., "paid", "unpaid", "refunded"
  checked_in_at?: string | null;
  // Add other registration specific fields as per your actual API
}

/**
 * Represents the status of an event registration.
 * These are example statuses and should be aligned with the backend API.
 */
export type EventRegistrationStatusType =
  | 'registered'
  | 'attended'
  | 'cancelled'
  | 'waitlisted'
  | 'pending_approval'
  | 'rejected';

// Example of how you might define an enum if preferred, though string literals are common.
// export enum EventRegistrationStatusEnum {
//   REGISTERED = 'registered',
//   ATTENDED = 'attended',
//   CANCELLED = 'cancelled',
//   // ... etc.
// } 