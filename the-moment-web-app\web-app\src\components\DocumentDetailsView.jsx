import React from 'react';
import { Typography } from 'antd';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

/**
 * DocumentDetailsView - Renders document details based on document type
 * @param {Object} props
 * @param {string} props.documentType - Type of document (e.g., 'hk_id_card', 'passport', 'mainland_travel_permit')
 * @param {Object} props.specifics - Document specific data
 * @param {Object} props.userData - Basic user data like user_display_name
 */
const DocumentDetailsView = ({ documentType, specifics, userData }) => {
  const { t } = useTranslation();

  // Helper function to render a field row
  const renderField = (labelKey, value, formatter = null) => {
    let displayValue;

    if (
      value === undefined ||
      value === null
    ) {
      displayValue = '-';
    } else {
      displayValue = formatter ? formatter(value) : value;
    }

    return (
      <div className="flex justify-between">
        <Text className="text-gray-500">{t(labelKey)}</Text>
        <Text strong>{displayValue}</Text>
      </div>
    );
  };

  // Helper function to render a long text field like address
  const renderLongField = (labelKey, value) => {
    let displayValue = value;

    if (
      value === undefined ||
      value === null
    ) {
      displayValue = '-';
    }

    return (
      <div className="space-y-2">
        <Text className="text-gray-500 block">{t(labelKey)}</Text>
        <div className="p-3 bg-gray-50 rounded border border-gray-200">
          <Text strong className="whitespace-pre-wrap break-words">{displayValue}</Text>
        </div>
      </div>
    );
  };

  // Render different fields based on document type
  switch (documentType) {
    case 'hk_id_card':
      return (
        <div className="space-y-3">
          {renderField('proofDetails.sections.userInfo.fields.displayName', userData?.user_display_name)}
          {renderField('proofDetails.sections.documentReview.fields.fullname', specifics?.english_name)}
          {renderField('proofDetails.sections.documentReview.fields.chineseName', specifics?.chinese_name)}
          {renderField('proofDetails.sections.documentReview.fields.gender', 
            specifics?.sex ? (specifics.sex === 'M' ? t('common.male') : t('common.female')) : null
          )}
          {renderField('proofDetails.sections.documentReview.fields.dateOfBirth', specifics?.date_of_birth)}
          {renderField('proofDetails.sections.documentReview.fields.idNumber', specifics?.hk_id_number)}
          {renderField('proofDetails.sections.userInfo.fields.is_permanent', 
            specifics?.is_permanent_resident !== undefined ? 
              (specifics.is_permanent_resident ? t('common.yes') : t('common.no')) : null
          )}
        </div>
      );

    case 'passport':
      return (
        <div className="space-y-3">
          {renderField('proofDetails.sections.userInfo.fields.displayName', userData?.user_display_name)}
          {renderField('proofDetails.sections.userInfo.fields.passportNumber', specifics?.passport_number)}
          {renderField('proofDetails.sections.userInfo.fields.issuingCountry', specifics?.issuing_country)}
          {renderField('proofDetails.sections.userInfo.fields.issueDate', specifics?.issue_date)}
          {renderField('proofDetails.sections.userInfo.fields.expiryDate', specifics?.expiry_date)}
        </div>
      );

    case 'mainland_travel_permit':
      return (
        <div className="space-y-3">
          {renderField('proofDetails.sections.userInfo.fields.displayName', userData?.user_display_name)}
          {renderField('proofDetails.sections.userInfo.fields.permitNumber', specifics?.permit_number)}
          {renderField('proofDetails.sections.userInfo.fields.issueDate', specifics?.issue_date)}
          {renderField('proofDetails.sections.userInfo.fields.expiryDate', specifics?.expiry_date)}
        </div>
      );
    
    case 'mainland_china_id_card':
      return (
        <div className="space-y-3">
          {renderField('proofDetails.sections.userInfo.fields.displayName', userData?.user_display_name)}
          {renderField('proofDetails.sections.documentReview.fields.chineseName', specifics?.chinese_name)}
          {renderField('proofDetails.sections.documentReview.fields.gender', 
            specifics?.sex ? (specifics.sex === 'M' ? t('common.male') : t('common.female')) : null
          )}
          {renderField('proofDetails.sections.documentReview.fields.dateOfBirth', specifics?.date_of_birth)}
          {renderField('proofDetails.sections.userInfo.fields.mainlandIdNumber', specifics?.mainland_id_number)}
          {renderField('proofDetails.sections.userInfo.fields.validUntil', specifics?.valid_until)}
        </div>
      );
      
    case 'address_proof': {
      let displayAddress = specifics?.full_address;
      if (typeof displayAddress === 'string') {
        const parts = displayAddress.split(',');
        if (parts.length > 1) {
          const lastPart = parts[parts.length - 1].trim();
          parts[parts.length - 1] = ' ' + t(`districts.${lastPart.toLowerCase()}`);
          displayAddress = parts.join(',');
        } 
      }

      return (
        <div className="space-y-3">
          {renderField('proofDetails.sections.userInfo.fields.displayName', userData?.user_display_name)}
          {renderLongField('proofDetails.sections.documentReview.fields.completeAddress', displayAddress)}
        </div>
      );
    }

    case 'hk_youth_plus':
      return (
        <div className="space-y-3">
          {renderField('proofDetails.sections.userInfo.fields.displayName', userData?.user_display_name)}
          {renderField('proofDetails.sections.userInfo.fields.memberNumber', specifics?.member_number)}
        </div>
      );

    case 'student_id':
      return (
        <div className="space-y-3">
          {renderField('proofDetails.sections.userInfo.fields.displayName', userData?.user_display_name)}
          {renderField('proofDetails.sections.userInfo.fields.schoolName', specifics?.school_name)}
          {renderField('proofDetails.sections.userInfo.fields.grade', specifics?.grade)}
          {renderField('proofDetails.sections.userInfo.fields.expiryDate', specifics?.expiry_date)}
        </div>
      );

    default:
      // Only render user display name for unsupported document types
      return (
        <div className="space-y-3">
          {renderField('proofDetails.sections.userInfo.fields.displayName', userData?.user_display_name)}
        </div>
      );
  }
};

export default DocumentDetailsView; 