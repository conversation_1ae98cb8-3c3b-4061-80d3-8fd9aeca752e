-- Drop user_organization_memberships table first due to foreign key constraints
DROP TABLE IF EXISTS user_organization_memberships;

-- Drop the unique index for default organization
DROP INDEX IF EXISTS unique_default_organization_idx;

-- Drop organizations table
DROP TABLE IF EXISTS organizations;

-- The trigger update_organizations_updated_at is dropped automatically when the table organizations is dropped.
-- The update_updated_at_column function itself should remain as it might be used by other tables. 