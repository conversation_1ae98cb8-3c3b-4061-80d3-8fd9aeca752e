import { useState, useEffect } from 'react';
import { Typography, Form, Button, Input, Divider, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useUser } from '../../contexts/UserContext';
import { useNavigate } from 'react-router-dom';
import { RightOutlined } from '@ant-design/icons';
import { profileService } from '../../services/profileService';

const { Text } = Typography;

const formatPhoneNumber = (phone) => {
    if (!phone) return '';
    // Remove any non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    // Format as XXXX XXXX
    return cleaned.replace(/(\d{4})(\d{4})/, '$1 $2');
};

const UserInfoTab = () => {
    const [form] = Form.useForm();
    const [usernameForm] = Form.useForm();
    const { currentUser, fetchUserProfile } = useUser();
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [isEditingUsername, setIsEditingUsername] = useState(false);
    const [usernameAvailable, setUsernameAvailable] = useState(null);
    const [checkingUsername, setCheckingUsername] = useState(false);
    const [isUpdatingUsername, setIsUpdatingUsername] = useState(false);

    // Fetch user profile when tab is focused
    // useEffect(() => {
    //     fetchUserProfile();
    // }, [fetchUserProfile]);

    // Check username availability
    const checkUsernameAvailability = async (username) => {
        if (!username || username === currentUser?.display_name) {
            setUsernameAvailable(null);
            return;
        }

        setCheckingUsername(true);
        // Simulate API call to check username availability
        setTimeout(() => {
            // This is a placeholder - in a real app, you would call an API
            const isAvailable = username.length >= 4 && !['admin', 'root', 'system'].includes(username);
            setUsernameAvailable(isAvailable);
            setCheckingUsername(false);
        }, 600);
    };

    const handleUsernameSubmit = async (values) => {
        const { username } = values;
        try {
            setIsUpdatingUsername(true);
            // Use updateUserProfile with display_name field in snake_case format
            await profileService.updateUserProfile({ display_name: username });
            
            // Refresh user profile data
            await fetchUserProfile();

            message.success(t('userSettings.username.messages.updateSuccess'));
            setIsEditingUsername(false);
        } catch (error) {
            message.error(t('userSettings.username.messages.updateError'));
            console.error(error);
        } finally {
            setIsUpdatingUsername(false);
        }
    };

    return (
        <>
            
            {/* Username Edit Section */}
            <div className="settings-tab-content">
                <div className="settings-tab-sidebar">
                    <div className="settings-section-header">
                        <span className="settings-section-title">{t('userSettings.username.title')}</span>
                        <span className="settings-section-subtitle">{t('userSettings.username.subtitle')}</span>
                    </div>
                </div>
                <div className="settings-tab-main">
                    <div className="settings-subsection">
                        <div className="settings-subsection-header">
                            <span className="settings-subsection-title">{t('userSettings.username.usernameEdit.title')}</span>
                            <span className="settings-subsection-subtitle">{t('userSettings.username.usernameEdit.subtitle')}</span>
                        </div>
                        <div className="contact-info-display">
                            <div className="contact-info-row">
                                <div className="contact-info-label-value">
                                    <Text>{t('userSettings.username.usernameEdit.current')}</Text>
                                    {isEditingUsername ? (
                                        <Form 
                                            form={usernameForm}
                                            initialValues={{ username: currentUser?.display_name || '' }}
                                            onFinish={handleUsernameSubmit}
                                            style={{ display: 'inline-flex', alignItems: 'center' }}
                                        >
                                            <Form.Item
                                                name="username"
                                                style={{ marginBottom: 0 }}
                                                rules={[
                                                    { required: true, message: t('userSettings.username.validation.required') },
                                                    { min: 4, message: t('userSettings.username.validation.minLength') },
                                                    { max: 12, message: t('userSettings.username.validation.maxLength') },
                                                    { pattern: /^[a-zA-Z0-9_-]+$/, message: t('userSettings.username.validation.format') }
                                                ]}
                                            >
                                                <Input
                                                    prefix="@"
                                                    style={{ width: '200px' }}
                                                    onPressEnter={() => usernameForm.submit()}
                                                    onBlur={() => {
                                                        const values = usernameForm.getFieldsValue();
                                                        if (!values.username) {
                                                            setIsEditingUsername(false);
                                                        }
                                                    }}
                                                    autoFocus
                                                    maxLength={12}
                                                    disabled={isUpdatingUsername}
                                                />
                                            </Form.Item>
                                        </Form>
                                    ) : (
                                        <>
                                            <Text>
                                                <span>@</span>
                                                {currentUser?.display_name || '-'}
                                            </Text>
                                            <Button
                                                type="text"
                                                className="ml-4 text-gray-500"
                                                onClick={() => setIsEditingUsername(true)}
                                            >
                                                {t('userSettings.username.usernameEdit.change')} <RightOutlined />
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* <Divider /> */}

            {/* Personal Info Section */}
            {/* <div className="settings-tab-content">
                <div className="settings-tab-sidebar">
                    <div className="settings-section-header">
                        <span className="settings-section-title">{t('userSettings.personalInfo.title')}</span>
                        <span className="settings-section-subtitle">{t('userSettings.personalInfo.subtitle')}</span>
                    </div>
                </div>
                <div className="settings-tab-main">
                    <Alert
                        description={
                            <Trans
                                i18nKey="userSettings.personalInfo.restrictedAlert"
                                components={{
                                    recoveryLink: <Link className="text-blue-500" to="/account-recovery" />
                                }}
                            />
                        }
                        type="warning"
                        showIcon
                        style={{ marginBottom: '24px' }}
                    />
                    <div className="settings-subsection">
                        <div className="settings-subsection-header grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex flex-col">
                                <span className="settings-subsection-title">{t('userSettings.identityInfo.name')}</span>
                                <span className="settings-subsection-subtitle">{currentUser?.display_name || '-'}</span>
                            </div>
                            <div className="flex flex-col">
                                <span className="settings-subsection-title">{t('userSettings.identityInfo.chineseName')}</span>
                                <span className="settings-subsection-subtitle">{currentUser?.chinese_name || '-'}</span>
                            </div>
                            <div className="flex flex-col">
                                <span className="settings-subsection-title">{t('userSettings.identityInfo.gender')}</span>
                                <span className="settings-subsection-subtitle">{currentUser?.gender ? genderFormatter(currentUser.gender) : '-'}</span>
                            </div>
                            <div className="flex flex-col">
                                <span className="settings-subsection-title">{t('userSettings.identityInfo.dateOfBirth')}</span>
                                <span className="settings-subsection-subtitle">{currentUser?.date_of_birth ? formatSimpleDate(currentUser.date_of_birth) : '-'}</span>
                            </div>
                            <div className="flex flex-col">
                                <span className="settings-subsection-title">{t('userSettings.identityInfo.phoneNumber')}</span>
                                <span className="settings-subsection-subtitle">{currentUser?.phone ? `+852 ${formatPhoneNumber(currentUser.phone.startsWith('+852') ? currentUser.phone.substring(4) : currentUser.phone)}` : '-'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div> */}

            {/* Contact Verification Section */}
            <Divider />
            <div className="settings-tab-content">
                <div className="settings-tab-sidebar">
                    <div className="settings-section-header">
                        <span className="settings-section-title">{t('userSettings.contactVerification.title')}</span>
                        <span className="settings-section-subtitle">{t('userSettings.contactVerification.subtitle')}</span>
                    </div>
                </div>
                <div className="settings-tab-main">
                    <div className="settings-subsection">
                        <div className="settings-subsection-header">
                            <span className="settings-subsection-title">{t('userSettings.contactVerification.phone.title')}</span>
                            <span className="settings-subsection-subtitle">{t('userSettings.contactVerification.phone.description')}</span>
                        </div>
                        <div className="contact-info-display">
                            <div className="contact-info-row">
                                <div className="contact-info-label-value">
                                    <Text>{t('userSettings.contactVerification.phone.current')}</Text>
                                    <Text>
                                        {currentUser?.phone ? `+852 ${formatPhoneNumber(currentUser.phone.startsWith('+852') ? currentUser.phone.substring(4) : currentUser.phone)}` : '-'}
                                    </Text>
                                    <Button
                                        type="text"
                                        className="change-button"
                                        onClick={() => navigate('/settings/change-contact', { state: { type: 'phone' } })}
                                    >
                                        {t('userSettings.contactVerification.buttons.change')} <RightOutlined />
                                    </Button>
                                </div>
                            </div>
                        </div>
                        <Divider />
                    </div>
                </div>
            </div>
        </>
    );
};

export default UserInfoTab; 