import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import en from './locales/en.json';
import zhHK from './locales/zh-HK.json';

// Get saved language preference or default to zh-HK
const savedLanguage = localStorage.getItem('language') || 'zh-HK';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      'en': {
        translation: en,
      },
      'zh-HK': {
        translation: zhHK,
      },
    },
    lng: savedLanguage,
    fallbackLng: 'zh-HK',
    interpolation: {
      escapeValue: false,
    },
  });

// Save language preference when it changes
i18n.on('languageChanged', (lng) => {
  localStorage.setItem('language', lng);
});

export default i18n; 