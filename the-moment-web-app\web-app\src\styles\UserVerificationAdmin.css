.verification-admin,
.user-verification-admin,
.volunteer-approval-admin {
  padding: 0 42px 42px;
  background-color: #ffffff;
  min-height: 600px;
  max-width: 1400px;
  margin: 0 auto;
  border-radius: 8px;
}

.verification-title {
  color: #1f1f1f;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.verification-table-container {
  margin-top: 20px;
}

.verification-table-container .ant-table-wrapper,
.user-verification-admin .ant-table-wrapper,
.volunteer-approval-admin .ant-table-wrapper {
  background-color: #ffffff;
  margin-top: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.user-verification-admin,
.volunteer-approval-admin {
  padding: 42px;
  background-color: #ffffff;
  min-height: 600px;
  max-width: 1400px;
  margin: 0 auto;
  border-radius: 8px;
}

.user-verification-admin .ant-typography,
.volunteer-approval-admin .ant-typography {
  color: #1f1f1f;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 8px;
}

.verification-tabs {
  flex-grow: 1;
  width: 100%;
}

.verification-table-container .ant-table-wrapper,
.user-verification-admin .ant-table-wrapper,
.volunteer-approval-admin .ant-table-wrapper {
  background-color: #ffffff;
  margin-top: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ant-table {
  border: none;
}

.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 16px;
  font-size: 14px;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #1f1f1f;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
}

.ant-table-tbody > tr > td {
  color: #595959;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
  cursor: pointer;
}

.ant-tag {
  border-radius: 4px;
  padding: 4px 8px;
  font-weight: 500;
}

.verification-tabs .ant-tabs-nav {
  margin-bottom: 16px;
}

.verification-tabs .ant-tabs-tab {
  font-size: 16px;
  display: flex;
  align-items: center;
}

.verification-tabs .ant-tabs-tab svg {
  margin-right: 8px;
}

.info-category-cell {
  display: flex;
  align-items: center;
}

.info-category-cell .anticon {
  margin-right: 8px;
}

.ant-table-expanded-row > td {
  background-color: #fafafa;
  padding: 16px 24px;
}

@media (max-width: 768px) {
  .verification-admin {
    padding: 0;
    margin: 0;
  }

  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 12px;
    font-size: 12px;
  }

  .verification-tabs .ant-tabs-tab {
    font-size: 14px;
  }

  .info-category-cell .anticon {
    margin-right: 4px;
  }

  .ant-table-expanded-row > td {
    padding: 12px 16px;
  }
}

.proof-details-container {
  padding: 16px 32px;
  margin: 0 auto;
  background-color: #ffffff;
}

.proof-details-title {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.section {
  margin-bottom: 32px;
}

.section .ant-typography {
  margin-bottom: 16px;
}

.info-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
}

.info-card .ant-card-head {
  background-color: #fafafa;
}

.info-card .ant-card-head-title {
  font-size: 18px;
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #fafafa;
  font-weight: 500;
}

.file-card {
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 2px;
  padding: 16px;
  height: 100%;
}

.file-card h3 {
  margin-bottom: 16px;
}

.file-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 2px;
}

.file-placeholder img {
  max-width: 100px;
  max-height: 100px;
  margin-bottom: 16px;
}

.action-buttons {
  text-align: right;
  margin-top: 24px;
}

.action-buttons .ant-btn {
  min-width: 120px;
  margin-left: 16px;
}

@media (max-width: 768px) {
  .proof-details-container {
    padding: 16px;
  }

  .section {
    margin-bottom: 24px;
  }

  .action-buttons {
    text-align: center;
  }

  .action-buttons .ant-btn {
    margin: 8px;
    width: calc(50% - 16px);
  }
}

.document-review {
  margin-bottom: 24px;
}

.pdf-viewer {
  border: 1px solid #f0f0f0;
  border-radius: 2px;
  height: 600px;
  overflow: hidden;
}

.pdf-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #fafafa;
  font-size: 18px;
  color: #999;
}

.verification-info .ant-descriptions {
  height: 100%;
}

@media (max-width: 1600px) {
  .pdf-viewer {
    height: 500px;
  }
}

@media (max-width: 992px) {
  .pdf-viewer {
    height: 400px;
  }
}

.verification-info-col {
  margin-bottom: 24px;
}
