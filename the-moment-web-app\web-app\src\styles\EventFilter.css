.filter-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    flex-wrap: wrap;
}

.filter-section-left {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    flex: 1;
    min-width: 280px;
}

.filter-section-right {
    flex-shrink: 0;
}

@media screen and (max-width: 576px) {
    .filter-section {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-section-left {
        width: 100%;
    }

    .filter-section-left .ant-input-affix-wrapper,
    .filter-section-left .ant-btn {
        width: 100% !important;
        max-width: none;
    }

    .filter-section-right {
        width: 100%;
    }

    .filter-section-right .ant-btn {
        width: 100%;
    }
} 