package payloads

import (
	"encoding/json"
	"log"
	"mime/multipart"
	"time"

	"Membership-SAAS-System-Backend/db"

	"github.com/google/uuid"
)

// Post Related Payloads

type CreatePostRequest struct {
	Title          string          `json:"title" form:"title" validate:"required,min=3,max=255"`
	Content        json.RawMessage `json:"content" form:"content" validate:"omitempty" swagger:"type:object"`
	Status         string          `json:"status" form:"status" validate:"omitempty,oneof=draft published hidden"` // Added 'hidden'
	Slug           string          `json:"slug" form:"slug" validate:"omitempty,alphanumdash,max=255"`
	PublishedAt    *time.Time      `json:"published_at,omitempty" form:"published_at,omitempty"`
	OrganizationID uuid.UUID       `json:"-"` // Set from path
	TagIDs         *[]uuid.UUID    `json:"tag_ids,omitempty" form:"tag_ids,omitempty" validate:"omitempty,dive,uuid"`
}

type UpdatePostRequest struct {
	Title       *string         `json:"title,omitempty" form:"title,omitempty" validate:"omitempty,min=3,max=255"`
	Content     json.RawMessage `json:"content,omitempty" form:"content,omitempty" swagger:"type:object"`
	Status      *string         `json:"status,omitempty" form:"status,omitempty" validate:"omitempty,oneof=draft published hidden"` // Added 'hidden'
	Slug        *string         `json:"slug,omitempty" form:"slug,omitempty" validate:"omitempty,alphanumdash,max=255"`
	PublishedAt *time.Time      `json:"published_at,omitempty" form:"published_at,omitempty"`
	TagIDs      *[]uuid.UUID    `json:"tag_ids,omitempty" form:"tag_ids,omitempty" validate:"omitempty,dive,uuid"`
}

type PostResponse struct {
	ID                uuid.UUID               `json:"id"`
	OrganizationID    uuid.UUID               `json:"organization_id"`
	AuthorDisplayName string                  `json:"author_display_name"`
	Title             string                  `json:"title"`
	Slug              string                  `json:"slug"`
	Content           json.RawMessage         `json:"content,omitempty" swagger:"type:object"`
	Status            string                  `json:"status"`
	PublishedAt       *time.Time              `json:"published_at,omitempty"`
	CreatedAt         time.Time               `json:"created_at"`
	UpdatedAt         time.Time               `json:"updated_at"`
	MediaItems        []PostMediaItemResponse `json:"media_items,omitempty"`
	Tags              []PostTagResponse       `json:"tags,omitempty"`
}

type PostMediaItemResponse struct {
	ID         uuid.UUID `json:"id"`
	PostID     uuid.UUID `json:"post_id"`
	FileName   string    `json:"file_name"`
	FilePath   string    `json:"file_path"` // Public URL
	FileType   string    `json:"file_type"`
	FileSize   int64     `json:"file_size"`
	UploadedAt time.Time `json:"uploaded_at"`
	IsBanner   bool      `json:"is_banner"`
}

type UploadPostMediaRequest struct {
	File *multipart.FileHeader `form:"file" validate:"required"`
}

type PostTagResponse struct {
	ID              uuid.UUID `json:"id"`
	NameEn          string    `json:"name_en"`
	NameZhHk        string    `json:"name_zh_hk"`
	NameZhCn        string    `json:"name_zh_cn"`
	DescriptionEn   *string   `json:"description_en,omitempty"`
	DescriptionZhHk *string   `json:"description_zh_hk,omitempty"`
	DescriptionZhCn *string   `json:"description_zh_cn,omitempty"`
}

type CreatePostTagRequest struct {
	NameEn          string  `json:"name_en" validate:"required,min=1,max=100"`
	NameZhHk        string  `json:"name_zh_hk" validate:"required,min=1,max=100"`
	NameZhCn        string  `json:"name_zh_cn" validate:"required,min=1,max=100"`
	DescriptionEn   *string `json:"description_en,omitempty"`
	DescriptionZhHk *string `json:"description_zh_hk,omitempty"`
	DescriptionZhCn *string `json:"description_zh_cn,omitempty"`
}

type UpdatePostTagRequest struct {
	NameEn          *string `json:"name_en,omitempty" validate:"omitempty,min=1,max=100"`
	NameZhHk        *string `json:"name_zh_hk,omitempty" validate:"omitempty,min=1,max=100"`
	NameZhCn        *string `json:"name_zh_cn,omitempty" validate:"omitempty,min=1,max=100"`
	DescriptionEn   *string `json:"description_en,omitempty"`
	DescriptionZhHk *string `json:"description_zh_hk,omitempty"`
	DescriptionZhCn *string `json:"description_zh_cn,omitempty"`
}

// PostTagsListResponse defines the structure for a list of tags associated with a post.
type PostTagsListResponse struct {
	Tags []PostTagResponse `json:"tags"`
}

// ToPostResponse converts various post data types (including those with tags) into a PostResponse.
// Media items and tags are passed separately or extracted from postData if available.
func ToPostResponse(postData interface{}, mediaItems []PostMediaItemResponse, tagsData json.RawMessage) PostResponse {
	var basePost db.Post
	var authorDisplayName string
	var content json.RawMessage
	var tags []PostTagResponse

	switch p := postData.(type) {
	case db.Post:
		basePost = p
		content = p.Content
		authorDisplayName = ""
	case db.ListPublishedPostsWithAuthorRow:
		basePost.ID = p.ID
		basePost.OrganizationID = p.OrganizationID
		basePost.AuthorID = p.AuthorID
		basePost.Title = p.Title
		basePost.Slug = p.Slug
		content = p.Content
		basePost.Status = p.Status
		basePost.PublishedAt = p.PublishedAt
		basePost.CreatedAt = p.CreatedAt
		basePost.UpdatedAt = p.UpdatedAt
		authorDisplayName = p.AuthorDisplayName
		// Tags are now part of ListPublishedPostsWithAuthorRow
		if p.Tags != nil {
			if tagsJSON, ok := p.Tags.(json.RawMessage); ok {
				if err := json.Unmarshal(tagsJSON, &tags); err != nil {
					log.Printf("Error unmarshalling tags (json.RawMessage) for post %s: %v. JSON: %s", p.ID, err, string(tagsJSON))
				} else {
					log.Printf("PostID %s (ListPublishedPostsWithAuthorRow): Successfully unmarshalled tags from json.RawMessage. Count: %d", p.ID, len(tags))
				}
			} else if tagsBytes, ok := p.Tags.([]byte); ok {
				if json.Valid(tagsBytes) {
					tagsJSONToUnmarshal := json.RawMessage(tagsBytes)
					if err := json.Unmarshal(tagsJSONToUnmarshal, &tags); err != nil {
						log.Printf("Error unmarshalling tags (from []byte) for post %s: %v. JSON: %s", p.ID, err, string(tagsJSONToUnmarshal))
					} else {
						log.Printf("PostID %s (ListPublishedPostsWithAuthorRow): Successfully unmarshalled tags from []byte. Count: %d", p.ID, len(tags))
					}
				} else {
					log.Printf("Invalid JSON in tags (from []byte) for post %s. Data: %s", p.ID, string(tagsBytes))
				}
			} else if tagsInterfaceSlice, ok := p.Tags.([]interface{}); ok {
				log.Printf("PostID %s (ListPublishedPostsWithAuthorRow): p.Tags is []interface{}. Attempting to marshal and unmarshal. Count: %d", p.ID, len(tagsInterfaceSlice))
				if len(tagsInterfaceSlice) > 0 {
					jsonBytes, err := json.Marshal(tagsInterfaceSlice)
					if err != nil {
						log.Printf("Error marshalling []interface{} to JSON for post %s: %v", p.ID, err)
					} else {
						if err := json.Unmarshal(jsonBytes, &tags); err != nil {
							log.Printf("Error unmarshalling marshalled JSON from []interface{} for post %s: %v. JSON: %s", p.ID, err, string(jsonBytes))
						} else {
							log.Printf("PostID %s (ListPublishedPostsWithAuthorRow): Successfully unmarshalled tags from []interface{}. Count: %d", p.ID, len(tags))
						}
					}
				} else {
					log.Printf("PostID %s (ListPublishedPostsWithAuthorRow): p.Tags is an empty []interface{}. Tags count will be 0.", p.ID)
				}
			} else {
				log.Printf("Tags field in ListPublishedPostsWithAuthorRow for post %s is not json.RawMessage, []byte, or []interface{}. Actual type: %T", p.ID, p.Tags)
			}
		} else {
			log.Printf("PostID %s (ListPublishedPostsWithAuthorRow): p.Tags IS nil.", p.ID) // Diagnostic log
		}
	case db.GetPostDetailsWithTagsRow:
		basePost.ID = p.ID
		basePost.OrganizationID = p.OrganizationID
		basePost.AuthorID = p.AuthorID
		basePost.Title = p.Title
		basePost.Slug = p.Slug
		content = p.Content
		basePost.Status = p.Status
		basePost.PublishedAt = p.PublishedAt
		basePost.CreatedAt = p.CreatedAt
		basePost.UpdatedAt = p.UpdatedAt
		authorDisplayName = p.AuthorDisplayName
		if p.Tags != nil {
			log.Printf("PostID %s (GetPostDetailsWithTagsRow): p.Tags is NOT nil. Type: %T, Value: %v", p.ID, p.Tags, p.Tags)
			if tagsJSON, ok := p.Tags.(json.RawMessage); ok {
				if err := json.Unmarshal(tagsJSON, &tags); err != nil {
					log.Printf("Error unmarshalling tags (json.RawMessage) for post %s: %v. JSON: %s", p.ID, err, string(tagsJSON))
				} else {
					log.Printf("PostID %s (GetPostDetailsWithTagsRow): Successfully unmarshalled tags from json.RawMessage. Count: %d", p.ID, len(tags))
				}
			} else if tagsBytes, ok := p.Tags.([]byte); ok {
				if json.Valid(tagsBytes) {
					tagsJSONToUnmarshal := json.RawMessage(tagsBytes)
					if err := json.Unmarshal(tagsJSONToUnmarshal, &tags); err != nil {
						log.Printf("Error unmarshalling tags (from []byte) for post %s: %v. JSON: %s", p.ID, err, string(tagsJSONToUnmarshal))
					} else {
						log.Printf("PostID %s (GetPostDetailsWithTagsRow): Successfully unmarshalled tags from []byte. Count: %d", p.ID, len(tags))
					}
				} else {
					log.Printf("Invalid JSON in tags (from []byte) for post %s. Data: %s", p.ID, string(tagsBytes))
				}
			} else if tagsInterfaceSlice, ok := p.Tags.([]interface{}); ok {
				log.Printf("PostID %s (GetPostDetailsWithTagsRow): p.Tags is []interface{}. Attempting to marshal and unmarshal. Count: %d", p.ID, len(tagsInterfaceSlice))
				if len(tagsInterfaceSlice) > 0 {
					jsonBytes, err := json.Marshal(tagsInterfaceSlice)
					if err != nil {
						log.Printf("Error marshalling []interface{} to JSON for post %s: %v", p.ID, err)
					} else {
						if err := json.Unmarshal(jsonBytes, &tags); err != nil {
							log.Printf("Error unmarshalling marshalled JSON from []interface{} for post %s: %v. JSON: %s", p.ID, err, string(jsonBytes))
						} else {
							log.Printf("PostID %s (GetPostDetailsWithTagsRow): Successfully unmarshalled tags from []interface{}. Count: %d", p.ID, len(tags))
						}
					}
				} else {
					log.Printf("PostID %s (GetPostDetailsWithTagsRow): p.Tags is an empty []interface{}. Tags count will be 0.", p.ID)
				}
			} else {
				log.Printf("Tags field in GetPostDetailsWithTagsRow for post %s is not json.RawMessage, []byte, or []interface{}. Actual type: %T", p.ID, p.Tags)
			}
		} else {
			log.Printf("PostID %s (GetPostDetailsWithTagsRow): p.Tags IS nil.", p.ID)
		}
	case db.ListPostsByOrganizationWithTagsRow:
		basePost.ID = p.ID
		basePost.OrganizationID = p.OrganizationID
		basePost.AuthorID = p.AuthorID
		basePost.Title = p.Title
		basePost.Slug = p.Slug
		content = p.Content
		basePost.Status = p.Status
		basePost.PublishedAt = p.PublishedAt
		basePost.CreatedAt = p.CreatedAt
		basePost.UpdatedAt = p.UpdatedAt
		authorDisplayName = p.AuthorDisplayName
		if p.Tags != nil {
			log.Printf("PostID %s (ListPostsByOrganizationWithTagsRow): p.Tags is NOT nil. Type: %T, Value: %v", p.ID, p.Tags, p.Tags)
			if tagsJSON, ok := p.Tags.(json.RawMessage); ok {
				if err := json.Unmarshal(tagsJSON, &tags); err != nil {
					log.Printf("Error unmarshalling tags (json.RawMessage) for post %s: %v. JSON: %s", p.ID, err, string(tagsJSON))
				} else {
					log.Printf("PostID %s (ListPostsByOrganizationWithTagsRow): Successfully unmarshalled tags from json.RawMessage. Count: %d", p.ID, len(tags))
				}
			} else if tagsBytes, ok := p.Tags.([]byte); ok {
				if json.Valid(tagsBytes) {
					tagsJSONToUnmarshal := json.RawMessage(tagsBytes)
					if err := json.Unmarshal(tagsJSONToUnmarshal, &tags); err != nil {
						log.Printf("Error unmarshalling tags (from []byte) for post %s: %v. JSON: %s", p.ID, err, string(tagsJSONToUnmarshal))
					} else {
						log.Printf("PostID %s (ListPostsByOrganizationWithTagsRow): Successfully unmarshalled tags from []byte. Count: %d", p.ID, len(tags))
					}
				} else {
					log.Printf("Invalid JSON in tags (from []byte) for post %s. Data: %s", p.ID, string(tagsBytes))
				}
			} else if tagsInterfaceSlice, ok := p.Tags.([]interface{}); ok {
				log.Printf("PostID %s (ListPostsByOrganizationWithTagsRow): p.Tags is []interface{}. Attempting to marshal and unmarshal. Count: %d", p.ID, len(tagsInterfaceSlice))
				if len(tagsInterfaceSlice) > 0 {
					jsonBytes, err := json.Marshal(tagsInterfaceSlice)
					if err != nil {
						log.Printf("Error marshalling []interface{} to JSON for post %s: %v", p.ID, err)
					} else {
						if err := json.Unmarshal(jsonBytes, &tags); err != nil {
							log.Printf("Error unmarshalling marshalled JSON from []interface{} for post %s: %v. JSON: %s", p.ID, err, string(jsonBytes))
						} else {
							log.Printf("PostID %s (ListPostsByOrganizationWithTagsRow): Successfully unmarshalled tags from []interface{}. Count: %d", p.ID, len(tags))
						}
					}
				} else {
					log.Printf("PostID %s (ListPostsByOrganizationWithTagsRow): p.Tags is an empty []interface{}. Tags count will be 0.", p.ID)
				}
			} else {
				log.Printf("Tags field in ListPostsByOrganizationWithTagsRow for post %s is not json.RawMessage, []byte, or []interface{}. Actual type: %T", p.ID, p.Tags)
			}
		} else {
			log.Printf("PostID %s (ListPostsByOrganizationWithTagsRow): p.Tags IS nil.", p.ID)
		}
	default:
		log.Printf("ToPostResponse: Unknown postData type: %T", postData)
		return PostResponse{}
	}

	// If tagsData was passed directly and not handled by a specific type case
	// For ListOrgPosts, tagsData is nil, so this block won't execute for that flow if p.Tags was handled above.
	if len(tags) == 0 && tagsData != nil {
		log.Printf("PostID %s: p.Tags was empty or nil, attempting to use provided tagsData. Length of tagsData: %d", basePost.ID, len(tagsData))
		if err := json.Unmarshal(tagsData, &tags); err != nil {
			log.Printf("Error unmarshalling tagsData for post %s: %v. JSON: %s", basePost.ID, err, string(tagsData))
		} else {
			log.Printf("PostID %s: Successfully unmarshalled tags from tagsData. Count: %d", basePost.ID, len(tags))
		}
	} else if len(tags) > 0 {
		log.Printf("PostID %s: Tags successfully populated from p.Tags. Count: %d", basePost.ID, len(tags))
	} else {
		log.Printf("PostID %s: Tags remain empty. p.Tags was nil or empty, and tagsData was nil.", basePost.ID)
	}

	return PostResponse{
		ID:                basePost.ID,
		OrganizationID:    basePost.OrganizationID,
		AuthorDisplayName: authorDisplayName,
		Title:             basePost.Title,
		Slug:              basePost.Slug,
		Content:           content,
		Status:            basePost.Status,
		PublishedAt:       basePost.PublishedAt,
		CreatedAt:         basePost.CreatedAt,
		UpdatedAt:         basePost.UpdatedAt,
		MediaItems:        mediaItems,
		Tags:              tags,
	}
}

// ToPostMediaItemResponse converts a db.PostMediaItem to a PostMediaItemResponse.
func ToPostMediaItemResponse(mediaItem db.PostMediaItem) PostMediaItemResponse {
	return PostMediaItemResponse{
		ID:         mediaItem.ID,
		PostID:     mediaItem.PostID,
		FileName:   mediaItem.FileName,
		FilePath:   mediaItem.FilePath, // This should be the public URL
		FileType:   mediaItem.FileType,
		FileSize:   mediaItem.FileSize,
		UploadedAt: mediaItem.UploadedAt,
		IsBanner:   mediaItem.IsBanner,
	}
}

// ToPostMediaItemListResponse converts a slice of db.PostMediaItem to a slice of PostMediaItemResponse.
func ToPostMediaItemListResponse(dbMediaItems []db.PostMediaItem) []PostMediaItemResponse {
	responses := make([]PostMediaItemResponse, 0, len(dbMediaItems))
	for _, item := range dbMediaItems {
		responses = append(responses, ToPostMediaItemResponse(item))
	}
	return responses
}

// ToPostListResponse converts a slice of db.Post to a slice of PostResponse.
func ToPostListResponse(posts []db.Post) []PostResponse {
	responses := make([]PostResponse, len(posts))
	for i, post := range posts {
		responses[i] = ToPostResponse(post, nil, nil) // Pass nil for mediaItems and tags
	}
	return responses
}

// ToPostResponseFromGetPostWithMediaRow converts db.GetPostWithMediaRow to PostResponse
func ToPostResponseFromGetPostWithMediaRow(rows []db.GetPostWithMediaRow) *PostResponse {
	if len(rows) == 0 {
		return nil
	}

	postData := rows[0].Post // This is db.Post from sqlc.embed(p)
	var mediaItems []PostMediaItemResponse

	for _, row := range rows {
		if row.PostMediaItemID != nil {
			mediaItem := db.PostMediaItem{}
			mediaItem.ID = *row.PostMediaItemID
			if row.PostMediaItemPostID != nil {
				mediaItem.PostID = *row.PostMediaItemPostID
			} else {
				mediaItem.PostID = postData.ID
			}
			if row.PostMediaItemFileName != nil {
				mediaItem.FileName = *row.PostMediaItemFileName
			}
			if row.PostMediaItemFilePath != nil {
				mediaItem.FilePath = *row.PostMediaItemFilePath
			}
			if row.PostMediaItemFileType != nil {
				mediaItem.FileType = *row.PostMediaItemFileType
			}
			if row.PostMediaItemFileSize != nil {
				mediaItem.FileSize = *row.PostMediaItemFileSize
			}
			if row.PostMediaItemUploadedAt != nil {
				mediaItem.UploadedAt = *row.PostMediaItemUploadedAt
			}
			mediaItems = append(mediaItems, ToPostMediaItemResponse(mediaItem))
		}
	}

	// Tags are not part of GetPostWithMediaRow, so pass nil.
	response := ToPostResponse(postData, mediaItems, nil)
	return &response
}

// ToPostListResponseFromListOrgPostsWithMediaRows processes rows from ListOrgPostsWithMedia query.
func ToPostListResponseFromListOrgPostsWithMediaRows(rows []db.ListOrgPostsWithMediaRow) []PostResponse {
	if len(rows) == 0 {
		return []PostResponse{}
	}

	postMap := make(map[uuid.UUID]*PostResponse)
	var orderedPostIDs []uuid.UUID

	for _, row := range rows {
		postID := row.ID
		if _, exists := postMap[postID]; !exists {
			postDataForResponse := db.ListPublishedPostsWithAuthorRow{
				ID:                row.ID,
				OrganizationID:    row.OrganizationID,
				AuthorID:          row.AuthorID,
				Title:             row.Title,
				Slug:              row.Slug,
				Content:           row.Content,
				Status:            row.Status,
				PublishedAt:       row.PublishedAt,
				CreatedAt:         row.CreatedAt,
				UpdatedAt:         row.UpdatedAt,
				AuthorDisplayName: row.AuthorDisplayName,
			}
			// Tags are not part of ListOrgPostsWithMediaRow, so pass nil for tagsData.
			initialPostResp := ToPostResponse(postDataForResponse, nil, nil)
			initialPostResp.MediaItems = []PostMediaItemResponse{}
			postMap[postID] = &initialPostResp
			orderedPostIDs = append(orderedPostIDs, postID)
		}

		if row.PostMediaItemID != nil {
			alreadyAdded := false
			for _, mi := range postMap[postID].MediaItems {
				if mi.ID == *row.PostMediaItemID {
					alreadyAdded = true
					break
				}
			}
			if !alreadyAdded {
				mediaItem := PostMediaItemResponse{
					ID:         *row.PostMediaItemID,
					PostID:     postID,
					FileName:   *row.PostMediaItemFileName,
					FilePath:   *row.PostMediaItemFilePath,
					FileType:   *row.PostMediaItemFileType,
					FileSize:   *row.PostMediaItemFileSize,
					UploadedAt: *row.PostMediaItemUploadedAt,
				}
				postMap[postID].MediaItems = append(postMap[postID].MediaItems, mediaItem)
			}
		}
	}

	finalResponses := make([]PostResponse, len(orderedPostIDs))
	for i, id := range orderedPostIDs {
		finalResponses[i] = *postMap[id]
	}

	return finalResponses
}

// ToPostListResponseFromListPublishedPostsWithMediaRows processes rows from ListPublishedPostsByOrganizationWithMedia query.
func ToPostListResponseFromListPublishedPostsWithMediaRows(rows []db.ListPublishedPostsByOrganizationWithMediaRow) []PostResponse {
	if len(rows) == 0 {
		return []PostResponse{}
	}

	postMap := make(map[uuid.UUID]*PostResponse)
	var orderedPostIDs []uuid.UUID

	for _, row := range rows {
		postID := row.Post.ID
		if _, exists := postMap[postID]; !exists {
			// Tags are not part of ListPublishedPostsByOrganizationWithMediaRow, so pass nil for tagsData.
			// row.Post is db.Post, which is handled by ToPostResponse
			initialPostResp := ToPostResponse(row.Post, nil, nil)
			initialPostResp.MediaItems = []PostMediaItemResponse{}
			postMap[postID] = &initialPostResp
			orderedPostIDs = append(orderedPostIDs, postID)
		}

		if row.PostMediaItemID != nil {
			alreadyAdded := false
			for _, mi := range postMap[postID].MediaItems {
				if mi.ID == *row.PostMediaItemID {
					alreadyAdded = true
					break
				}
			}
			if !alreadyAdded {
				mediaItem := PostMediaItemResponse{
					ID:         *row.PostMediaItemID,
					PostID:     postID,
					FileName:   *row.PostMediaItemFileName,
					FilePath:   *row.PostMediaItemFilePath,
					FileType:   *row.PostMediaItemFileType,
					FileSize:   *row.PostMediaItemFileSize,
					UploadedAt: *row.PostMediaItemUploadedAt,
				}
				postMap[postID].MediaItems = append(postMap[postID].MediaItems, mediaItem)
			}
		}
	}

	finalResponses := make([]PostResponse, len(orderedPostIDs))
	for i, id := range orderedPostIDs {
		finalResponses[i] = *postMap[id]
	}

	return finalResponses
}

// ToPostListResponseFromListPostsByOrganizationWithTagsRows processes rows from ListPostsByOrganizationWithTags query.
func ToPostListResponseFromListPostsByOrganizationWithTagsRows(rows []db.ListPostsByOrganizationWithTagsRow) []PostResponse {
	responses := make([]PostResponse, len(rows))
	for i, row := range rows {
		// Media items are not directly available in ListPostsByOrganizationWithTagsRow.
		// The ToPostResponse function's case for db.ListPostsByOrganizationWithTagsRow
		// will handle unmarshalling p.Tags. So we pass nil for the third argument (tagsData).
		responses[i] = ToPostResponse(row, nil, nil)
	}
	return responses
}

// ListPostsParams defines parameters for listing posts with optional filters and pagination.
// It includes filters for organization ID, tags, status, and now date range and search term.
type ListPostsParams struct {
	OrganizationID  *uuid.UUID  `query:"organization_id" validate:"omitempty,uuid"`
	OrganizationID2 *uuid.UUID  `query:"organization_id2" validate:"omitempty,uuid"`
	TagIDs          []uuid.UUID `query:"tag_ids" validate:"omitempty,dive,uuid"`
	Limit           int         `query:"limit" default:"10"`
	Offset          int         `query:"offset" default:"0"`
	Status          *string     `query:"status" validate:"omitempty,oneof=draft published hidden"`
	StartDate       *time.Time  `query:"start_date" form:"start_date"`
	EndDate         *time.Time  `query:"end_date" form:"end_date"`
	SearchTerm      *string     `query:"search_term" form:"search_term"`
}

type AuthorResponse struct {
	Name      string  `json:"name"`
	AvatarURL *string `json:"avatarUrl,omitempty"`
}

type AttachmentResponse struct {
	Type string `json:"type"` // "video", "pdf", "doc", "image"
	URL  string `json:"url"`  // Full URL
	Name string `json:"name"` // Original filename
}

// PostTag defines the structure for a post tag.
// ... existing code ...
