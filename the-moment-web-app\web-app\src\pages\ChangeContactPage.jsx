import React, { useState } from 'react';
import { Card, Typography, Steps, Form, Input, Button, message, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { SendOutlined, CheckOutlined, EditOutlined, ArrowLeftOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import '../styles/ChangeContact.css';
import PhoneNumberInput from '../components/PhoneNumberInput';

const { Title, Text } = Typography;

const ChangeContactPage = () => {
    const { t } = useTranslation();
    const { user, updateUserInfo } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();
    const [form] = Form.useForm();
    
    // Get type from URL state (either 'phone' or 'email')
    const { type } = location.state || { type: 'phone' };
    const [currentStep, setCurrentStep] = useState(0);
    const [verificationCode, setVerificationCode] = useState('');
    const [currentStepCountdown, setCurrentStepCountdown] = useState(0);
    const [newEmailCountdown, setNewEmailCountdown] = useState(0);
    const [isNewEmailCodeSent, setIsNewEmailCodeSent] = useState(false);
    
    const formatPhoneNumber = (phone) => {
        if (!phone) return '';
        const cleaned = phone.replace(/\D/g, '');
        return cleaned.replace(/(\d{4})(\d{4})/, '$1 $2');
    };

    const startCountdown = (setCountdownFn) => {
        setCountdownFn(60);
        const timer = setInterval(() => {
            setCountdownFn(prev => {
                if (prev <= 1) {
                    clearInterval(timer);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
    };

    const handleSendCode = async () => {
        try {
            // API call to send verification code
            message.success(t('changeContact.messages.codeSent'));
            startCountdown(setCurrentStepCountdown);
        } catch (error) {
            message.error(t('changeContact.messages.sendCodeError'));
        }
    };

    const handleSendNewEmailCode = async () => {
        try {
            // API call to send verification code to new email
            message.success(t('changeContact.messages.codeSent'));
            setIsNewEmailCodeSent(true);
            startCountdown(setNewEmailCountdown);
        } catch (error) {
            message.error(t('changeContact.messages.sendCodeError'));
        }
    };

    const handleVerify = async () => {
        try {
            // API call to verify code
            setCurrentStep(1);
            // Reset the countdown for the new email step
            setNewEmailCountdown(0);
        } catch (error) {
            message.error(t('changeContact.messages.verificationError'));
        }
    };

    const handleSubmit = async (values) => {
        try {
            // API call to update contact info
            message.success(t('changeContact.messages.updateSuccess'));
            navigate('/settings');
        } catch (error) {
            message.error(t('changeContact.messages.updateError'));
        }
    };

    const handleVerificationCodeChange = (e) => {
        const value = e.target.value.replace(/\D/g, '').slice(0, 6);
        setVerificationCode(value);
    };

    const renderEmailVerificationStep = () => (
        <div className="verification-section">
            <div className="current-contact">
                <Text className="contact-label">
                    {t('changeContact.currentContact')}
                </Text>
                <Text strong className="contact-value">
                    {user?.email}
                </Text>
            </div>

            <Form layout="vertical" size="large">
                <Form.Item
                    label={t('changeContact.verificationCode')}
                    required
                >
                    <Space.Compact style={{ width: '100%' }}>
                        <Input
                            maxLength={6}
                            placeholder={t('changeContact.codePlaceholder')}
                            size="large"
                            value={verificationCode}
                            onChange={handleVerificationCodeChange}
                            autoFocus
                        />
                        <Button
                            type="primary"
                            onClick={handleSendCode}
                            disabled={currentStepCountdown > 0}
                            size="large"
                        >
                            {currentStepCountdown > 0 
                                ? t('changeContact.buttons.resend', { seconds: currentStepCountdown })
                                : t('changeContact.buttons.sendCode')}
                        </Button>
                    </Space.Compact>
                </Form.Item>
                <Form.Item>
                    <Button 
                        type="primary" 
                        size="large"
                        onClick={handleVerify}
                        block
                        icon={<CheckOutlined />}
                        disabled={verificationCode.length !== 6}
                    >
                        {t('changeContact.buttons.verify')}
                    </Button>
                </Form.Item>
            </Form>
        </div>
    );

    const renderEmailUpdateStep = () => (
        <div className="update-section">
            <Form 
                form={form}
                layout="vertical"
                size="large"
                onFinish={handleSubmit}
            >
                <Form.Item
                    name="email"
                    label={t('changeContact.email.newAddress')}
                    rules={[
                        { required: true, message: t('changeContact.validation.required') },
                        { type: 'email', message: t('changeContact.validation.emailFormat') }
                    ]}
                >
                    <Space.Compact style={{ width: '100%' }}>
                        <Input
                            prefix={<MailOutlined style={{ marginRight: '8px' }} />}
                            placeholder={t('changeContact.email.placeholder')}
                            size="large"
                        />
                        <Button
                            type="primary"
                            onClick={handleSendNewEmailCode}
                            disabled={newEmailCountdown > 0}
                            size="large"
                        >
                            {newEmailCountdown > 0 
                                ? t('changeContact.buttons.resend', { seconds: newEmailCountdown })
                                : t('changeContact.buttons.sendCode')}
                        </Button>
                    </Space.Compact>
                </Form.Item>

                {isNewEmailCodeSent && (
                    <Form.Item
                        name="newVerificationCode"
                        label={t('changeContact.verificationCode')}
                        rules={[
                            { required: true, message: t('changeContact.validation.required') },
                            { len: 6, message: t('changeContact.validation.codeLength') }
                        ]}
                    >
                        <Input
                            maxLength={6}
                            placeholder={t('changeContact.codePlaceholder')}
                            size="large"
                            onChange={(e) => {
                                const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                                form.setFieldsValue({ newVerificationCode: value });
                            }}
                            autoFocus
                        />
                    </Form.Item>
                )}

                <Form.Item>
                    <Button 
                        type="primary" 
                        size="large"
                        htmlType="submit" 
                        block
                        disabled={!isNewEmailCodeSent}
                        icon={<SendOutlined />}
                    >
                        {t('changeContact.buttons.submit')}
                    </Button>
                </Form.Item>
            </Form>
        </div>
    );

    const renderPhoneVerificationStep = () => (
        <div className="verification-section">
            <div className="current-contact">
                <Text className="contact-label">
                    {t('changeContact.currentContact')}
                </Text>
                <Text strong className="contact-value">
                    <span>+852 </span>{formatPhoneNumber(user?.phoneNumber)}
                </Text>
            </div>

            <Form layout="vertical" size="large">
                <Form.Item
                    label={t('changeContact.verificationCode')}
                    required
                >
                    <Space.Compact style={{ width: '100%' }}>
                        <Input
                            maxLength={6}
                            placeholder={t('changeContact.codePlaceholder')}
                            size="large"
                            value={verificationCode}
                            onChange={handleVerificationCodeChange}
                            autoFocus
                        />
                        <Button
                            type="primary"
                            onClick={handleSendCode}
                            disabled={currentStepCountdown > 0}
                            size="large"
                        >
                            {currentStepCountdown > 0 
                                ? t('changeContact.buttons.resend', { seconds: currentStepCountdown })
                                : t('changeContact.buttons.sendCode')}
                        </Button>
                    </Space.Compact>
                </Form.Item>
                <Form.Item>
                    <Button 
                        type="primary" 
                        size="large"
                        onClick={handleVerify}
                        block
                        icon={<CheckOutlined />}
                        disabled={verificationCode.length !== 6}
                    >
                        {t('changeContact.buttons.verify')}
                    </Button>
                </Form.Item>
            </Form>
        </div>
    );

    const renderPhoneUpdateStep = () => (
        <div className="update-section">
            <Form 
                form={form}
                layout="vertical"
                size="large"
                onFinish={handleSubmit}
            >
                <Form.Item
                    name="phoneNumber"
                    label={t('changeContact.phone.newNumber')}
                    rules={[
                        { required: true, message: t('changeContact.validation.required') },
                        { len: 8, message: t('changeContact.validation.phoneFormat') },
                    ]}
                >
                    <Space.Compact style={{ width: '100%' }}>
                        <PhoneNumberInput onChange={(value) => form.setFieldsValue({ phoneNumber: value })}
                        />
                        <Button
                            type="primary"
                            onClick={handleSendNewEmailCode}
                            disabled={newEmailCountdown > 0}
                            size="large"
                        >
                            {newEmailCountdown > 0 
                                ? t('changeContact.buttons.resend', { seconds: newEmailCountdown })
                                : t('changeContact.buttons.sendCode')}
                        </Button>
                    </Space.Compact>
                </Form.Item>

                {isNewEmailCodeSent && (
                    <Form.Item
                        name="newVerificationCode"
                        label={t('changeContact.verificationCode')}
                        rules={[
                            { required: true, message: t('changeContact.validation.required') },
                            { len: 6, message: t('changeContact.validation.codeLength') }
                        ]}
                    >
                        <Input
                            maxLength={6}
                            placeholder={t('changeContact.codePlaceholder')}
                            size="large"
                            onChange={(e) => {
                                const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                                form.setFieldsValue({ newVerificationCode: value });
                            }}
                            autoFocus
                        />
                    </Form.Item>
                )}

                <Form.Item>
                    <Button 
                        type="primary" 
                        size="large"
                        htmlType="submit" 
                        block
                        disabled={!isNewEmailCodeSent}
                        icon={<SendOutlined />}
                    >
                        {t('changeContact.buttons.submit')}
                    </Button>
                </Form.Item>
            </Form>
        </div>
    );

    return (
        <div className="change-contact-container">
            <Card className="change-contact-card">
                <Button 
                    type="text" 
                    icon={<ArrowLeftOutlined />} 
                    onClick={() => navigate('/settings')}
                    className="back-button"
                >
                    {t('changeContact.buttons.back')}
                </Button>

                <div className="change-contact-header">
                    <Title level={2}>
                        {t(type === 'phone' 
                            ? 'changeContact.phone.title' 
                            : 'changeContact.email.title')}
                    </Title>
                    <Text type="secondary" className="description-text">
                        {t(type === 'phone' 
                            ? 'changeContact.phone.description' 
                            : 'changeContact.email.description')}
                    </Text>
                </div>

                <Steps 
                    current={currentStep} 
                    className="change-contact-steps"
                    items={[
                        {
                            title: t('changeContact.steps.verify'),
                            icon: currentStep === 0 ? <LockOutlined /> : <CheckOutlined />
                        },
                        {
                            title: t('changeContact.steps.update'),
                            icon: currentStep === 1 ? <EditOutlined /> : <LockOutlined />
                        }
                    ]}
                />

                <div className="step-content-wrapper">
                    {type === 'email' ? (
                        currentStep === 0 ? renderEmailVerificationStep() : renderEmailUpdateStep()
                    ) : (
                        currentStep === 0 ? renderPhoneVerificationStep() : renderPhoneUpdateStep()
                    )}
                </div>
            </Card>
        </div>
    );
};

export default ChangeContactPage; 