package payloads

import (
	"Membership-SAAS-System-Backend/db"
	"time"

	"github.com/google/uuid"
)

// CreateOrganizationRequest defines the input for creating an organization.
// @Description Request body for creating a new organization.
type CreateOrganizationRequest struct {
	// Name of the organization
	Name string `json:"name" validate:"required,min=2,max=100" example:"My Awesome Org"`
	// Optional description of the organization
	Description string `json:"description,omitempty" validate:"omitempty,max=500" example:"We do great things!"`
	// Optional URL for the organization's image/logo
	ImageURL string `json:"image_url,omitempty" validate:"omitempty,url,max=512" example:"http://example.com/logo.png"`
	// Optional theme color for the organization (e.g., hex code)
	ThemeColor  string    `json:"theme_color,omitempty" validate:"omitempty,max=50" example:"#FF5733"`
	OwnerUserID uuid.UUID `json:"owner_user_id" validate:"required,uuid" example:"123e4567-e89b-12d3-a456-************"`
	IsDefault   bool      `json:"is_default" example:"false"`
}

// UpdateOrganizationRequest defines the input for updating an organization.
// @Description Request body for updating an existing organization. All fields are optional.
type UpdateOrganizationRequest struct {
	// New name for the organization
	Name *string `json:"name,omitempty" validate:"omitempty,min=2,max=100" example:"Updated Org Name"`
	// New description for the organization
	Description *string `json:"description,omitempty" validate:"omitempty,max=500" example:"We updated our description!"`
	// New URL for the organization's image/logo
	ImageURL *string `json:"image_url,omitempty" validate:"omitempty,url,max=512" example:"http://example.com/new_logo.jpg"`
	// New theme color for the organization
	ThemeColor *string `json:"theme_color,omitempty" validate:"omitempty,max=50" example:"#33FF57"`
	// New status for the organization (pending_setup, active, suspended)
	Status      *string    `json:"status,omitempty" validate:"omitempty,oneof=pending_setup active suspended" example:"active"`
	OwnerUserID *uuid.UUID `json:"owner_user_id,omitempty"` // Ownership change should be a separate, more controlled process.
}

// CheckUserOrganizationRoleOptions defines options for checking a user's role in an organization.
// Moved from services package.
type CheckUserOrganizationRoleOptions struct {
	UserID         uuid.UUID
	OrganizationID uuid.UUID
	RequiredRoles  []string // e.g., ["owner", "admin"]
}

// AddUserToOrganizationInput defines input for adding a user to an organization.
// Moved from services package.
type AddUserToOrganizationInput struct {
	UserID               uuid.UUID `json:"user_id" validate:"required"`
	OrganizationID       uuid.UUID `json:"org_id" validate:"required"`
	Role                 string    `json:"role" validate:"required,min=3,max=50"`
	IsActive             bool      // Defaults for new members often set in service
	NotificationsEnabled bool      // Defaults for new members often set in service
}

// UpdateMembershipInput defines input for updating a user's membership details in an organization.
// Moved from services package.
type UpdateMembershipInput struct {
	Role                 *string `json:"role,omitempty" validate:"omitempty,min=3,max=50"`
	IsActive             *bool   `json:"is_active,omitempty"`
	NotificationsEnabled *bool   `json:"notifications_enabled,omitempty"`
}

// OrganizationResponse is a full representation of an organization.
// @Description Full details of an organization.
type OrganizationResponse struct {
	// Unique identifier for the organization
	ID uuid.UUID `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	// Name of the organization
	Name string `json:"name" example:"Example Community"`
	// Optional description of the organization
	Description *string `json:"description,omitempty" example:"A community for developers."`
	// Indicates if this is a default organization (e.g., for system use)
	IsDefaultOrg bool `json:"is_default_org" example:"false"`
	// URL of the organization's logo or banner image
	ImageURL *string `json:"image_url,omitempty" example:"http://example.com/logo.png"`
	// Primary theme color for the organization (e.g., 'red', 'blue', '#FF0000')
	ThemeColor *string `json:"theme_color,omitempty" example:"#007bff"`
	// The current status of the organization (e.g., pending_setup, active, suspended).
	Status string `json:"status" example:"active"`
	// Timestamp when the organization was created
	CreatedAt time.Time `json:"created_at"`
	// Timestamp when the organization was last updated
	UpdatedAt time.Time `json:"updated_at"`
}

// OrgSlimResponse is a minimal representation of an organization.
// @Description Minimal details of an organization.
type OrgSlimResponse struct {
	// Unique identifier for the organization
	ID uuid.UUID `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	// Name of the organization
	Name string `json:"name" example:"Example Community"`
}

func ToOrganizationResponse(org db.Organization) OrganizationResponse {
	return OrganizationResponse{
		ID:           org.ID,
		Name:         org.Name,
		Description:  org.Description,
		IsDefaultOrg: org.IsDefaultOrg,
		ImageURL:     org.ImageUrl,   // Note: Case difference due to sqlc generation
		ThemeColor:   org.ThemeColor, // Note: Case difference due to sqlc generation
		Status:       org.Status,
		CreatedAt:    org.CreatedAt,
		UpdatedAt:    org.UpdatedAt,
	}
}

// ToOrgSlimResponse converts a db.Organization to an OrgSlimResponse.
func ToOrgSlimResponse(org db.Organization) *OrgSlimResponse {
	return &OrgSlimResponse{
		ID:   org.ID,
		Name: org.Name,
	}
}

func ToOrganizationResponses(orgs []db.Organization) []OrganizationResponse {
	responses := make([]OrganizationResponse, len(orgs))
	for i, org := range orgs {
		responses[i] = ToOrganizationResponse(org)
	}
	return responses
}

func ToUserOrganizationResponse(org db.Organization) OrganizationResponse {
	return OrganizationResponse{
		ID:           org.ID,
		Name:         org.Name,
		Description:  org.Description,
		IsDefaultOrg: org.IsDefaultOrg,
		ImageURL:     org.ImageUrl,   // Note: Case difference due to sqlc generation
		ThemeColor:   org.ThemeColor, // Note: Case difference due to sqlc generation
		Status:       org.Status,
		CreatedAt:    org.CreatedAt,
		UpdatedAt:    org.UpdatedAt,
	}
}

func ToUserOrganizationResponses(orgs []db.Organization) []OrganizationResponse {
	responses := make([]OrganizationResponse, len(orgs))
	for i, org := range orgs {
		responses[i] = ToUserOrganizationResponse(org)
	}
	return responses
}
