import React, { useState, useEffect, useRef } from 'react';
import {
    Form,
    Input,
    Button,
    Select,
    Space,
    Spin,
    message,
    Card,
    Tooltip,
    Upload,
    Switch,
    Divider,
    Row,
    Col,
    Checkbox,
    DatePicker,
} from 'antd';
import {
    PlusOutlined,
    UploadOutlined,
    SaveOutlined,
    ClockCircleOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import '../../styles/PostPage.css';
import TipTapEditor from '../../components/TipTapEditor';
import TagManagerModal from '../../components/TagManagerModal';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { organizationService } from '../../services/organizationService';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { useAuth } from '../../contexts/AuthContext';
import { postService } from '../../services/postService';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// Initialize dayjs plugins for timezone handling
dayjs.extend(utc);
dayjs.extend(timezone);

// Set default timezone to Hong Kong (matching what's used in the event form)
const DEFAULT_TIMEZONE = 'Asia/Hong_Kong';

const { Option } = Select;
const { Dragger } = Upload;

const CreatePostPage = () => {
    const { postId } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const [form] = Form.useForm();
    const [pageLoading, setPageLoading] = useState(!!postId);
    const [submittingPublish, setSubmittingPublish] = useState(false);
    const [submittingDraft, setSubmittingDraft] = useState(false);
    const [initialData, setInitialData] = useState(null);
    const [postOrgId, setPostOrgId] = useState(null);
    const [coverImageFileList, setCoverImageFileList] = useState([]);
    const [attachmentFileList, setAttachmentFileList] = useState([]);
    const [removedFileIds, setRemovedFileIds] = useState([]);
    const editorRef = useRef(null);
    const { t, i18n } = useTranslation();
    const { currentOrganization } = useOrganization();
    const [availablePostTags, setAvailablePostTags] = useState([]);
    const [loadingTags, setLoadingTags] = useState(false);
    const [isHiddenApiCheckbox, setIsHiddenApiCheckbox] = useState(false);
    const { user } = useAuth();
    const [publishedAtMoment, setPublishedAtMoment] = useState(null);
    const [tagManagerModalVisible, setTagManagerModalVisible] = useState(false);

    const isEditMode = !!postId;

    // Fetch tags based on current language
    useEffect(() => {
        const fetchTagsForEditor = async () => {
            setLoadingTags(true);
            try {
                const tagsResponse = await postService.listPostTags();
                // API now returns array with format: { id, name_en, name_zh_hk, name_zh_cn }
                const tagsArray = Array.isArray(tagsResponse) ? tagsResponse : [];
                setAvailablePostTags(tagsArray.filter(tag =>
                    tag.id &&
                    (tag.name_en || tag.name_zh_hk || tag.name_zh_cn)
                ));
            } catch (error) {
                console.error("Failed to fetch post tags:", error);
                message.error(t('createPost.errors.fetchTagsError'));
                setAvailablePostTags([]);
            } finally {
                setLoadingTags(false);
            }
        };

        fetchTagsForEditor();
    }, [i18n.language, t]);

    // Helper function to get tag name based on current language
    const getTagName = (tag, language) => {
        if (!tag) return '';
        if (language.startsWith('zh')) {
            if (language.includes('HK')) {
                return tag.name_zh_hk || tag.name_en || '';
            } else {
                return tag.name_zh_cn || tag.name_en || '';
            }
        } else {
            return tag.name_en || '';
        }
    };

    // Handle tag creation from TagManagerModal
    const handleTagCreated = (newTag) => {
        // Add the new tag to available tags list
        setAvailablePostTags(prev => [...prev, newTag]);

        // Auto-select the newly created tag
        const newTagValue = {
            value: newTag.id,
            label: getTagName(newTag, i18n.language)
        };
        form.setFieldsValue({ type: newTagValue });
    };

    // Handle tag deletion from TagManagerModal
    const handleTagDeleted = (deletedTagId) => {
        // Remove from availablePostTags
        setAvailablePostTags(prev => prev.filter(tag => tag.id !== deletedTagId));

        // Clear form selection if the deleted tag was selected
        const currentValue = form.getFieldValue('type');
        if (currentValue && currentValue.value === deletedTagId) {
            form.setFieldsValue({ type: undefined });
        }
    };

    // 加载帖子数据 - 编辑模式下的useEffect
    useEffect(() => {
        if (!isEditMode) {
            // 创建模式下的初始化
            form.setFieldsValue({
                title: '',
                hidden_api_checkbox: false,
                coverImage: undefined,
                type: undefined,
                published_at: null,
            });
            if (currentOrganization) {
                setPostOrgId(currentOrganization.id);
            }
            setRemovedFileIds([]);
            return;
        }

        const fetchPostDetails = async () => {
            setPageLoading(true);
            try {
                const postDetailsFromApi = await postService.getPublicPostDetail(postId);
                
                if (!postDetailsFromApi || typeof postDetailsFromApi.title === 'undefined') {
                    console.error("Error fetching post details: Response data is missing or malformed.", postDetailsFromApi);
                    message.error(t('messages.fetchError') + (postDetailsFromApi ? '' : ' - No data returned'));
                    navigate('/posts');
                    return;
                }

                const organization_id = postDetailsFromApi.organization_id;
                if (!organization_id) {
                    message.error(t('createPost.errors.missingOrgIdFromApi'));
                    console.error("Organization ID not found in API response for editing post.");
                    navigate('/posts');
                    return;
                }

                // 检查帖子是否属于ALL_ORGANIZATION_ID，且用户不是super_admin
                if (organization_id === ALL_ORGANIZATION_ID && user?.role !== 'super_admin') {
                    setPageLoading(false);
                    navigate('/error/403');
                    return;
                }

                setPostOrgId(organization_id);

                const { title, content, status, media_items, published_at, id, tags } = postDetailsFromApi;

                setIsHiddenApiCheckbox(status === 'hidden');

                // Handle published_at date if it exists
                let publishedAtData = null;
                if (published_at) {
                    try {
                        // Parse the date with timezone awareness
                        publishedAtData = dayjs(published_at).tz(DEFAULT_TIMEZONE);
                        if (!publishedAtData.isValid()) publishedAtData = null;

                        // Clear published_at if it's in the past to avoid showing invalid past dates
                        if (publishedAtData && publishedAtData.isBefore(dayjs())) {
                            publishedAtData = null;
                            // We don't show a message here to avoid confusion when just opening the form
                        }

                        setPublishedAtMoment(publishedAtData);
                    } catch (err) {
                        console.error('Date parsing error for published_at:', err);
                        publishedAtData = null;
                    }
                }

                // Handle tag for edit mode
                let tagValue = undefined;
                if (tags && tags.length > 0) {
                    // Use the first tag's ID and get its name using the new format
                    const firstTag = tags[0];
                    tagValue = {
                        value: firstTag.id,
                        label: getTagName(firstTag, i18n.language)
                    };
                }

                form.setFieldsValue({
                    title: title,
                    hidden_api_checkbox: status === 'hidden',
                    content: content,
                    coverImage: media_items?.find(item => item.file_type?.startsWith('image/')) ? true : undefined,
                    type: tagValue,
                    published_at: publishedAtData,
                });

                const processedMediaItems = (media_items || []).map(item => ({
                    id: item.id,
                    uid: item.id || `media-${item.file_name}-${Date.now()}`,
                    name: item.file_name,
                    status: 'done',
                    url: item.file_path,
                    thumbUrl: item.file_type?.startsWith('image/') ? item.file_path : undefined,
                    type: item.file_type,
                    size: item.file_size,
                    originFileObj: null
                }));

                let coverImageToPreFill = null;
                let attachmentsToPreFill = [];

                const firstImageMedia = processedMediaItems.find(item => item.type?.startsWith('image/'));

                if (firstImageMedia) {
                    coverImageToPreFill = firstImageMedia;
                    setCoverImageFileList([coverImageToPreFill]);
                    attachmentsToPreFill = processedMediaItems.filter(item => item.id !== firstImageMedia.id);
                } else {
                    setCoverImageFileList([]);
                    attachmentsToPreFill = processedMediaItems;
                }
                setAttachmentFileList(attachmentsToPreFill);

                setInitialData({
                    id: id,
                    title: title,
                    content: content,
                    status: status,
                    isHidden: status === 'hidden',
                    publishedAt: published_at,
                    published_at: published_at,
                    organizationId: organization_id,
                    attachments: processedMediaItems.map(item => ({
                        id: item.id,
                        fileName: item.name,
                        url: item.url,
                        type: item.type,
                    })),
                    tag_ids: tags ? tags.map(tag => tag.id) : [],
                });
            } catch (error) {
                message.error(t('messages.fetchError'));
                console.error("Error fetching post details: ", error);
                navigate('/posts');
            } finally {
                setPageLoading(false);
            }
        };

        fetchPostDetails();

    }, [postId, form, isEditMode, navigate, t, i18n.language, currentOrganization]);

    // Configuration for cover image upload
    const coverImageUploadProps = {
        name: 'coverImage',
        multiple: false,
        listType: 'picture-card',
        fileList: coverImageFileList,
        action: '/api/upload',
        accept: '.jpg,.jpeg,.png,.gif,.webp',
        maxCount: 1,
        showUploadList: {
            showPreviewIcon: false,
            showRemoveIcon: true
        },
        beforeUpload: (file) => {
            const isImage = /image\/(jpeg|png|gif|jpg|webp)/.test(file.type);
            if (!isImage) {
                message.error(t('createPost.form.attachments.validation.imageType'));
                return Upload.LIST_IGNORE;
            }

            const isLessThan5M = file.size / 1024 / 1024 < 5;
            if (!isLessThan5M) {
                message.error(t('createPost.form.attachments.validation.imageSize'));
                return Upload.LIST_IGNORE;
            }
            return false;
        },
        onChange: ({ fileList }) => {
            const newList = fileList.map(f => f.originFileObj && !f.url ? { ...f, originFileObj: f.originFileObj || f } : f);
            setCoverImageFileList(newList.slice(-1));
        },
        onRemove: (file) => {
            setCoverImageFileList([]);
            if (file.id) {
                setRemovedFileIds(prev => [...prev, file.id]);
            }
        },
    };

    // Validation for form submission
    const validateCoverImage = () => {
        if (coverImageFileList.length > 0) {
            const file = coverImageFileList[0];
            if (file.status === 'error') {
                message.error(t('createPost.form.coverImage.uploadError'));
                return false;
            }
        }
        return true;
    };

    const onHiddenCheckboxChange = (e) => {
        setIsHiddenApiCheckbox(e.target.checked);
        form.setFieldsValue({ hidden_api_checkbox: e.target.checked });
    };

    // Publish time restriction functions
    const disabledPublishDate = (current) => {
        // Cannot select days before today
        return current && current < dayjs().startOf('day');
    };

    // Validate and clear past publish times
    const validatePublishDate = (date) => {
        if (!date) return date;

        const currentTime = dayjs();
        // If the selected date is in the past, return null to clear the field
        if (date.isBefore(currentTime)) {
            message.warning(t('createPost.form.publishAt.pastDateWarning', 'Cannot select a time in the past.'));
            return null;
        }

        return date;
    };

    // Handle DatePicker change to validate the selected date
    const handlePublishDateChange = (date) => {
        const validDate = validatePublishDate(date);
        form.setFieldsValue({ published_at: validDate });
    };

    const disabledPublishTime = (date) => {
        if (!date) return {};

        const currentTime = dayjs();
        // If selected date is today, check if time is at least 5 minutes in the future
        if (date.isSame(currentTime, 'day')) {
            const currentHour = currentTime.hour();
            const currentMinute = currentTime.minute();
            const futureTime = currentTime.add(5, 'minute');
            const futureHour = futureTime.hour();
            const futureMinute = futureTime.minute();

            return {
                disabledHours: () => {
                    // Disable hours before current hour
                    return Array.from({ length: currentHour }, (_, i) => i);
                },
                disabledMinutes: (selectedHour) => {
                    // If selected hour is the current hour, disable minutes before future minute
                    if (selectedHour === currentHour) {
                        return Array.from({ length: futureMinute }, (_, i) => i);
                    }
                    // If selected hour is the future hour (when we cross hour boundary), 
                    // disable minutes before the future minute
                    if (currentHour !== futureHour && selectedHour === futureHour) {
                        return Array.from({ length: futureMinute }, (_, i) => i);
                    }
                    return [];
                }
            };
        }

        return {};
    };

    const handleSubmit = async (values, intendedSubmitType) => {
        // Cover image validation is now handled by form rules
        if (!values.coverImage && coverImageFileList.length === 0) {
            message.error(t('createPost.form.coverImage.required'));
            return;
        }

        if (!validateCoverImage()) {
            return;
        }

        const currentOrgId = postOrgId || (currentOrganization ? currentOrganization.id : null);
        if (!currentOrgId) {
            message.error('missing Organization ID');
            if (intendedSubmitType === 'published') setSubmittingPublish(false);
            if (intendedSubmitType === 'draft') setSubmittingDraft(false);
            return;
        }

        if (intendedSubmitType === 'published') setSubmittingPublish(true);
        if (intendedSubmitType === 'draft') setSubmittingDraft(true);

        const editorContent = editorRef.current?.getJSON();
        if (!editorContent || (editorContent.type === 'doc' && (!editorContent.content || editorContent.content.length === 0 || (editorContent.content.length === 1 && !editorContent.content[0].content)))) {
            message.error(t('createPost.form.content.required'));
            if (intendedSubmitType === 'published') setSubmittingPublish(false);
            if (intendedSubmitType === 'draft') setSubmittingDraft(false);
            return;
        }

        // Get tag ID directly from the form value
        const tagId = values.type && values.type.value ? values.type.value : undefined;
        const finalTagId = tagId ? [tagId] : [];

        let finalStatus = intendedSubmitType;
        if (values.hidden_api_checkbox) finalStatus = 'hidden';

        // Process published_at with explicit timezone handling
        let publishedAtISO = undefined;
        if (values.published_at) {
            // Convert the selected local date to the server's expected format with timezone information
            publishedAtISO = dayjs(values.published_at)
                .tz(DEFAULT_TIMEZONE, true) // true keeps the same time but changes the timezone
                .utc()                      // convert to UTC for storage
                .format();                  // format as ISO string

            // If scheduled publishing is set, force status to draft
            if (finalStatus === 'published') {
                finalStatus = 'draft';
                message.info(t('createPost.messages.scheduledAsDraft', 'Post will be saved as draft and published automatically at the scheduled time.'));
            }
        }

        const payload = {
            title: values.title,
            content: editorContent,
            status: finalStatus,
            is_hidden: values.hidden_api_checkbox,
            tag_ids: finalTagId,
            published_at: publishedAtISO,
        };

        try {
            let actualPostId = isEditMode ? postId : null;
            let hasMetadataChanges = false;
            const updatePayload = {};

            if (isEditMode) {
                if (!initialData) {
                    message.error(t('createPost.errors.missingInitialData'));
                    if (intendedSubmitType === 'published') setSubmittingPublish(false);
                    if (intendedSubmitType === 'draft') setSubmittingDraft(false);
                    return;
                }
                if (initialData.title !== values.title) {
                    updatePayload.title = values.title;
                    hasMetadataChanges = true;
                }
                if (JSON.stringify(initialData.content) !== JSON.stringify(editorContent)) {
                    updatePayload.content = editorContent;
                    hasMetadataChanges = true;
                }
                if (initialData.status !== finalStatus) {
                    updatePayload.status = finalStatus;
                    hasMetadataChanges = true;
                }
                if (JSON.stringify(initialData.tag_ids) !== JSON.stringify(finalTagId)) {
                    updatePayload.tag_ids = finalTagId;
                    hasMetadataChanges = true;
                }
                if (initialData.isHidden !== values.hidden_api_checkbox) {
                    updatePayload.is_hidden = values.hidden_api_checkbox;
                    hasMetadataChanges = true;
                }
                // Add published_at checking logic with proper timezone handling
                const initialPublishedAt = initialData.published_at ? dayjs(initialData.published_at) : null;
                const currentPublishedAt = values.published_at ? dayjs(values.published_at) : null;

                // Check if published_at has changed
                if (
                    (initialPublishedAt && !currentPublishedAt) ||
                    (!initialPublishedAt && currentPublishedAt) ||
                    (initialPublishedAt && currentPublishedAt && !initialPublishedAt.isSame(currentPublishedAt))
                ) {
                    updatePayload.published_at = publishedAtISO;
                    hasMetadataChanges = true;
                }
            } else {
                const response = await organizationService.createPost(currentOrgId, payload);
                actualPostId = response.data?.id || response.id || null;
                if (!actualPostId) {
                    throw new Error("Post ID not available after create operation.");
                }
            }

            // --- Media Change Detection --- Find files that are new (have originFileObj and no server ID)
            const newCoverImageFile = coverImageFileList.find(f => f.originFileObj && !f.id);
            const newAttachmentFilesToUpload = attachmentFileList.filter(f => f.originFileObj && !f.id);

            let mediaIdsToDelete = [];
            if (isEditMode && initialData?.attachments) {
                const currentMediaOnFormIds = [
                    ...coverImageFileList.filter(f => f.id).map(f => f.id),
                    ...attachmentFileList.filter(f => f.id).map(f => f.id)
                ].filter(Boolean);
                const initialServerMediaIds = initialData.attachments.map(a => a.id).filter(Boolean);

                // Also include any files that were explicitly removed via the UI
                mediaIdsToDelete = [
                    ...initialServerMediaIds.filter(id => !currentMediaOnFormIds.includes(id)),
                    ...removedFileIds
                ];

                // Remove duplicates
                mediaIdsToDelete = [...new Set(mediaIdsToDelete)];
            }
            const hasMediaChanges = !!newCoverImageFile || newAttachmentFilesToUpload.length > 0 || mediaIdsToDelete.length > 0;

            // --- Perform Update or Check for No Changes (Edit Mode Specific) ---
            if (isEditMode) {
                if (!hasMetadataChanges && !hasMediaChanges) {
                    message.info(t('createPost.messages.noChanges'));
                    if (intendedSubmitType === 'published') setSubmittingPublish(false);
                    if (intendedSubmitType === 'draft') setSubmittingDraft(false);
                    return; // Stop if no changes at all
                }
                if (hasMetadataChanges && Object.keys(updatePayload).length > 0) {
                    // Only send fields that actually changed for PATCH
                    await organizationService.updatePost(currentOrgId, actualPostId, updatePayload);
                }
            }

            if (!actualPostId) { // Should be set in create mode by now, or is postId in edit mode
                throw new Error("Post ID is missing before media handling.");
            }

            // --- Media Handling (Proceeds if created, or if in edit mode with any changes) ---
            if (mediaIdsToDelete.length > 0) {
                for (const mediaId of mediaIdsToDelete) {
                    try {
                        await organizationService.deletePostMedia(currentOrgId, actualPostId, mediaId);
                    } catch (deleteError) {
                        console.error(`Failed to delete media ${mediaId}:`, deleteError);
                        message.error(t('createPost.errors.deleteMediaError', { mediaId }));
                    }
                }
            }

            if (newCoverImageFile && newCoverImageFile.originFileObj) {
                try {
                    // Pass true for isCover if your service needs it
                    await organizationService.uploadPostMedia(currentOrgId, actualPostId, newCoverImageFile.originFileObj, true);
                } catch (uploadError) {
                    console.error('Failed to upload cover image:', uploadError);
                    message.error(t('createPost.errors.uploadCoverError'));
                }
            }

            for (const newFile of newAttachmentFilesToUpload) {
                if (newFile.originFileObj) {
                    try {
                        // Pass false for isCover if your service needs it
                        await organizationService.uploadPostMedia(currentOrgId, actualPostId, newFile.originFileObj, false);
                    } catch (uploadError) {
                        console.error(`Failed to upload attachment ${newFile.name}:`, uploadError);
                        message.error(t('createPost.errors.uploadAttachmentError', { fileName: newFile.name }));
                    }
                }
            }
            // --- End Media Handling ---

            message.success(t(`createPost.messages.${isEditMode ? 'updateSuccess' : (intendedSubmitType === 'published' ? 'createSuccess' : 'draftSuccess')}`));

            // 使用query参数决定返回位置
            const searchParams = new URLSearchParams(location.search);
            const returnTo = searchParams.get('returnTo');

            if (returnTo === 'management') {
                navigate('/posts-management');
            } else if (returnTo === 'details' && isEditMode) {
                navigate(`/posts/${postId}`);
            } else {
                navigate('/posts');
            }
        } catch (error) {
            console.error('Error submitting post:', error);
            message.error(t(`createPost.messages.${isEditMode ? 'updateError' : (intendedSubmitType === 'published' ? 'createError' : 'draftError')}`) + (error.response?.data?.message ? `: ${error.response.data.message}` : ''));
        } finally {
            if (intendedSubmitType === 'published') setSubmittingPublish(false);
            if (intendedSubmitType === 'draft') setSubmittingDraft(false);
        }
    };

    const onFinish = (values) => {
        handleSubmit(values, values.hidden_api_checkbox ? 'hidden' : 'published');
    };

    const onSaveAsDraft = () => {
        form.validateFields()
            .then(values => {
                handleSubmit(values, 'draft');
            })
            .catch(info => {
                message.warning(t('messages.validationWarning'));
            });
    };

    // Configuration for attachments upload
    const attachmentUploadProps = {
        name: 'attachment',
        multiple: true,
        listType: 'text',
        fileList: attachmentFileList,
        action: '/api/upload',
        accept: '.jpg,.jpeg,.png,.gif,.webp,.mp4,.mov,.avi,.webm,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx',
        beforeUpload: (file) => {
            const isImage = /image\/(jpeg|png|gif|jpg|webp)/.test(file.type);
            const isVideo = /video\/(mp4|quicktime|x-msvideo|webm)/.test(file.type);
            const isDocument = /(pdf|msword|vnd.openxmlformats-officedocument.*|vnd.ms-*)/i.test(file.type);

            if (!isImage && !isVideo && !isDocument) {
                message.error(t('createPost.form.attachments.validation.fileType'));
                return Upload.LIST_IGNORE;
            }

            if (isImage) {
                const isLessThan10M = file.size / 1024 / 1024 < 10;
                if (!isLessThan10M) {
                    message.error(t('createPost.form.attachments.validation.imageSize'));
                    return Upload.LIST_IGNORE;
                }
            }

            if (isVideo) {
                const isLessThan100M = file.size / 1024 / 1024 < 100;
                if (!isLessThan100M) {
                    message.error(t('createPost.form.attachments.validation.videoSize'));
                    return Upload.LIST_IGNORE;
                }
            }

            if (isDocument) {
                const isLessThan20M = file.size / 1024 / 1024 < 20;
                if (!isLessThan20M) {
                    message.error(t('createPost.form.attachments.validation.documentSize'));
                    return Upload.LIST_IGNORE;
                }
            }

            if (attachmentFileList.length >= 10) {
                message.error(t('createPost.form.attachmentUpload.maxError'));
                return Upload.LIST_IGNORE;
            }

            file.customType = isVideo ? 'video' : isImage ? 'image' : 'document';

            return false;
        },
        onChange: ({ fileList }) => {
            const newFileList = fileList.map(f => {
                if (f.originFileObj && !f.url) {
                    const isImage = /image\/(jpeg|png|gif|jpg|webp)/.test(f.type);
                    const isVideo = /video\/(mp4|quicktime|x-msvideo|webm)/.test(f.type);
                    f.customType = isVideo ? 'video' : isImage ? 'image' : 'document';
                    return { ...f, originFileObj: f.originFileObj || f };
                }
                return f;
            });
            setAttachmentFileList(newFileList.slice(0, 10));
        },
        onRemove: (file) => {
            setAttachmentFileList(prev => prev.filter(item => item.uid !== file.uid));
            if (file.id) {
                setRemovedFileIds(prev => [...prev, file.id]);
            }
        },
    };

    const getButtonText = () => {
        if (!isEditMode) return t('createPost.buttons.publish');
        if (initialData?.status === 'published') return t('createPost.buttons.update');
        return t('createPost.buttons.publish');
    };

    return (
        <div className="w-full max-w-4xl mx-auto px-4 py-6">
            {pageLoading ? (
                <div className="flex justify-center items-center h-64">
                    <Spin size="large" />
                </div>
            ) : (
                <Form
                    form={form}
                    layout="vertical"
                    className="w-full mx-auto"
                    onFinish={onFinish}
                    initialValues={{
                        hidden_api_checkbox: isEditMode ? (initialData?.isHidden || false) : false,
                        published_at: null
                    }}
                >
                    <Row gutter={[24, 16]}>
                        <Col xs={24} md={12}>
                            <Form.Item
                                label={t('createPost.form.title.label')}
                                name="title"
                                rules={[
                                    { required: true, message: t('createPost.form.title.required') },
                                    { min: 5, message: t('createPost.form.title.minLength') || 'Title must be at least 5 characters' },
                                    { max: 30, message: t('createPost.form.title.maxLength') || 'Title cannot exceed 30 characters' }
                                ]}
                            >
                                <Input placeholder={t('createPost.form.title.placeholder')} size="large" />
                            </Form.Item>
                        </Col>

                        <Col xs={12} md={6}>
                            <Form.Item
                                label={t('createPost.form.tags.label', "新聞標籤")}
                                name="type"
                                rules={[{ required: true, message: t('createPost.form.tags.required') }]}
                            >
                                <Select
                                    labelInValue
                                    placeholder={t('createPost.form.tags.placeholder')}
                                    size="large"
                                    loading={loadingTags}
                                    allowClear
                                    showSearch
                                    optionFilterProp="children"
                                    filterOption={(input, option) =>
                                        option.children.toLowerCase().includes(input.toLowerCase())
                                    }
                                    dropdownRender={(menu) => (
                                        <>
                                            {menu}
                                            <Divider style={{ margin: '8px 0' }} />
                                            <Button
                                                type="text"
                                                icon={<PlusOutlined />}
                                                onClick={() => setTagManagerModalVisible(true)}
                                                style={{ width: '100%' }}
                                            >
                                                {t('createPost.form.tags.manageAll', '管理标签')}
                                            </Button>
                                        </>
                                    )}
                                >
                                    {availablePostTags.map(tag => (
                                        <Option key={tag.id} value={tag.id}>
                                            {getTagName(tag, i18n.language)}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>

                        <Col xs={12} md={6}>
                            <Form.Item
                                name="hidden_api_checkbox"
                                label={t('createPost.form.hidden.label')}
                                valuePropName="checked"
                            >
                                <Checkbox
                                    checked={isHiddenApiCheckbox}
                                    onChange={onHiddenCheckboxChange}
                                >
                                    {t('createPost.form.hidden.checkboxLabel')}
                                </Checkbox>
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* Schedule Publication Row */}
                    <Row gutter={[24, 16]}>
                        <Col xs={24} md={12}>
                            <Form.Item
                                label={t('createPost.form.publishAt.label', "定時發布於")}
                                name="published_at"
                                tooltip={t('createPost.form.publishAt.tooltip', "設置一個定時發布時間，文章將在指定時間自動發布。所有時間均以香港時間為準。")}
                            >
                                <DatePicker
                                    showTime={{ format: 'HH:mm' }}
                                    format="YYYY-MM-DD HH:mm"
                                    needConfirm={false}
                                    size="large"
                                    showNow={false}
                                    placeholder={t('createPost.form.publishAt.placeholder', "選擇發布日期和時間")}
                                    style={{ width: '100%' }}
                                    disabledDate={disabledPublishDate}
                                    disabledTime={disabledPublishTime}
                                    suffixIcon={<ClockCircleOutlined />}
                                    onChange={handlePublishDateChange}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="content"
                        label={t('createPost.form.content.label')}
                        rules={[{ required: true, message: t('createPost.form.content.required') }]}
                        className="mb-8"
                    >
                        <TipTapEditor
                            ref={editorRef}
                            initialContent={initialData?.content || ''}
                            placeholder={t('createPost.form.content.placeholder')}
                        />
                    </Form.Item>

                    <Divider orientation="left">{t('createPost.form.attachments.label')}</Divider>

                    <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-8">
                        <div className="space-y-8">
                            {/* Cover Image Upload Section */}
                            <div>
                                <Form.Item
                                    label={t('createPost.form.coverImage.label')}
                                    className="mb-2"
                                    name="coverImage"
                                    rules={[
                                        {
                                            required: true,
                                            message: t('createPost.form.coverImage.required'),
                                            validator: (_, value) => {
                                                if (coverImageFileList.length > 0) {
                                                    return Promise.resolve();
                                                }
                                                return Promise.reject(new Error(t('createPost.form.coverImage.required')));
                                            }
                                        }
                                    ]}
                                >
                                    <Upload {...coverImageUploadProps}>
                                        {coverImageFileList.length === 0 && (
                                            <div className="upload-card">
                                                <PlusOutlined />
                                                <div style={{ marginTop: 8 }}>{t('createPost.form.coverImage.upload')}</div>
                                            </div>
                                        )}
                                    </Upload>
                                    <p className="text-xs text-gray-500 mt-2">
                                        {t('createPost.form.coverImage.tip')}
                                    </p>
                                </Form.Item>
                            </div>

                            {/* Attachments Upload Section */}
                            <div>
                                <Form.Item
                                    label={t('createPost.form.attachmentUpload.label')}
                                    className="mb-2"
                                >
                                    <Upload {...attachmentUploadProps}>
                                        {attachmentFileList.length < 10 && (
                                            <Button icon={<UploadOutlined />}>
                                                {t('createPost.form.attachmentUpload.upload')}
                                            </Button>
                                        )}
                                    </Upload>
                                    <p className="text-xs text-gray-500 mt-2">
                                        {t('createPost.form.attachmentUpload.tip')}
                                    </p>
                                </Form.Item>
                            </div>
                        </div>
                    </div>

                    <Divider style={{ borderColor: '#bdbdbd' }} />
                    <div className="flex justify-end gap-4 mt-8 flex-wrap">
                        <Button
                            onClick={() => {
                                const searchParams = new URLSearchParams(location.search);
                                const returnTo = searchParams.get('returnTo');

                                if (returnTo === 'management') {
                                    navigate('/posts-management');
                                } else if (returnTo === 'details' && isEditMode) {
                                    navigate(`/posts/${postId}`);
                                } else {
                                    navigate('/posts');
                                }
                            }}
                            disabled={submittingPublish || submittingDraft}
                        >
                            {t('createPost.buttons.cancel')}
                        </Button>
                        <Button
                            icon={<SaveOutlined />}
                            onClick={onSaveAsDraft}
                            loading={submittingDraft}
                            disabled={submittingPublish}
                        >
                            {t('createPost.buttons.saveDraft')}
                        </Button>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={submittingPublish}
                            disabled={submittingDraft}
                        >
                            {getButtonText()}
                        </Button>
                    </div>
                </Form>
            )}

            {/* Tag Manager Modal */}
            <TagManagerModal
                visible={tagManagerModalVisible}
                onCancel={() => setTagManagerModalVisible(false)}
                onTagCreated={handleTagCreated}
                onTagDeleted={handleTagDeleted}
                getTagName={getTagName}
                type="post"
            />
        </div>
    );
};

export default CreatePostPage;