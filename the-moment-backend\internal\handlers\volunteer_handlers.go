package handlers

import (
	"Membership-SAAS-System-Backend/internal/authn"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services"
	"Membership-SAAS-System-Backend/internal/utils"
	"errors"
	"net/http"

	"Membership-SAAS-System-Backend/db"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

const (
	DefaultVolAppPageLimit  = 10
	MaxVolAppPageLimit      = 100
	DefaultVolAppPageOffset = 0
)

// VolunteerHandler handles API requests related to volunteer management.
type VolunteerHandler struct {
	VolunteerSvc    *services.VolunteerService
	VerificationSvc *services.UserVerificationService // Needed for admin details endpoint
	OrgSvc          *services.OrganizationService     // Added for permission checks
}

// NewVolunteerHandler creates a new VolunteerHandler.
func NewVolunteerHandler(volSvc *services.VolunteerService, verSvc *services.UserVerificationService, orgSvc *services.OrganizationService) *VolunteerHandler {
	return &VolunteerHandler{
		VolunteerSvc:    volSvc,
		VerificationSvc: verSvc,
		OrgSvc:          orgSvc,
	}
}

// ApplyForVolunteerQualificationHandler godoc
// @Summary Apply for volunteer status in a specific organization
// @Description Allows the authenticated user to submit an application to become a volunteer for an organization. This creates a user_volunteer_applications record with status 'pending'.
// @Tags Volunteer Management (User), Organizations
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param body body payloads.ApplyVolunteerRequest true "Application details (e.g., motivation letter)"
// @Success 201 {object} map[string]string "Application submitted successfully (includes application_id). Status is 'pending'."
// @Failure 400 {object} payloads.ErrorResponse "Invalid request (e.g., invalid orgId format, bad request body)"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (e.g., user is not a member of the organization)"
// @Failure 409 {object} payloads.ErrorResponse "Conflict (user already has an 'approved' application, or another 'pending' application exists for this organization)"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/volunteer/apply [post]
func (h *VolunteerHandler) ApplyForVolunteerQualificationHandler(c echo.Context) error {
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "User ID not found in token")
	}

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	req := new(payloads.ApplyVolunteerRequest)
	if err := c.Bind(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}

	// Validation (basic, more can be added)
	// No explicit validation needed for ApplyVolunteerRequest as motivation is optional string

	application, err := h.VolunteerSvc.ApplyForVolunteerQualification(c.Request().Context(), userID, orgID, *req)
	if err != nil {
		switch {
		case errors.Is(err, services.ErrNotMemberOfOrg):
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		case errors.Is(err, services.ErrAlreadyApprovedApplication):
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		case errors.Is(err, services.ErrPendingApplicationExists):
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		default:
			log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to apply for volunteer qualification")
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to submit application")
		}
	}

	// Return 201 Created with just the new application ID
	responsePayload := map[string]string{
		"message":        "Volunteer application submitted successfully.",
		"application_id": application.ID.String(),
	}

	return c.JSON(http.StatusCreated, responsePayload)
}

// ListUserVolunteerApplicationsHandler godoc
// @Summary List current user's volunteer applications across all orgs
// @Description Retrieves a list of volunteer applications submitted by the authenticated user across all organizations. Can be filtered by status.
// @Tags Volunteer Management (User), User Profile
// @Produce json
// @Param status query string false "Filter by application status" Enums(pending, approved, rejected, withdrawn)
// @Success 200 {array} payloads.VolunteerApplicationResponse "List of user's volunteer applications"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Router /users/me/volunteer/applications [get]
func (h *VolunteerHandler) ListUserVolunteerApplicationsHandler(c echo.Context) error {
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "User ID not found in token")
	}

	// Read status from query parameter
	status := c.QueryParam("status")
	var statusPtr *string
	if status != "" {
		statusPtr = &status
	}

	apps, err := h.VolunteerSvc.ListUserVolunteerApplications(c.Request().Context(), userID, statusPtr)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to list user volunteer applications")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve applications")
	}

	return c.JSON(http.StatusOK, apps)
}

// GetUserVolunteerApplicationDetailsHandler godoc
// @Summary Get details of a specific volunteer application owned by the user
// @Description Retrieves details for a specific volunteer application submitted by the authenticated user.
// @Tags Volunteer Management (User), User Profile
// @Produce json
// @Param appId path string true "Application ID (UUID)"
// @Success 200 {object} payloads.VolunteerApplicationResponse "Application details"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not owner of application)"
// @Failure 404 {object} payloads.ErrorResponse "Application not found"
// @Router /users/me/volunteer/applications/{appId} [get]
func (h *VolunteerHandler) GetUserVolunteerApplicationDetailsHandler(c echo.Context) error {
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "User ID not found in token")
	}

	appIDStr := c.Param("appId")
	appID, err := uuid.Parse(appIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid application ID format")
	}

	appDetails, err := h.VolunteerSvc.GetUserVolunteerApplicationDetails(c.Request().Context(), userID, appID)
	if err != nil {
		if errors.Is(err, services.ErrApplicationNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to get user volunteer application details")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve application details")
	}

	return c.JSON(http.StatusOK, appDetails)
}

// ListUserVolunteerQualificationsHandler godoc
// @Summary List organizations the user is a qualified volunteer for
// @Description Retrieves a list of the user's volunteer applications that have a status of 'approved'. An approved application signifies the user is qualified to volunteer for that organization.
// @Tags Volunteer Management (User), User Profile
// @Produce json
// @Success 200 {array} payloads.VolunteerApplicationResponse "List of approved volunteer applications (representing qualifications)"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /users/me/volunteer/qualifications [get]
func (h *VolunteerHandler) ListUserVolunteerQualificationsHandler(c echo.Context) error {
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "User ID not found in token")
	}

	quals, err := h.VolunteerSvc.ListUserVolunteerQualifications(c.Request().Context(), userID)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to list user volunteer qualifications")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve qualifications")
	}

	return c.JSON(http.StatusOK, quals)
}

// GetVolunteerQualificationStatusForOrgHandler godoc
// @Summary Check volunteer qualification status for the user in a specific organization
// @Description Retrieves the user's volunteer application for the specified organization if its status is 'approved'. This indicates the user is qualified to volunteer for that organization.
// @Tags Volunteer Management (User), Organizations
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Success 200 {object} payloads.VolunteerApplicationResponse "Approved volunteer application. A 404 is returned if no 'approved' application exists."
// @Failure 400 {object} payloads.ErrorResponse "Invalid organization ID format"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 404 {object} payloads.ErrorResponse "No approved volunteer application (qualification) found for this user in this organization."
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /organizations/{orgId}/volunteer/status [get]
func (h *VolunteerHandler) GetVolunteerQualificationStatusForOrgHandler(c echo.Context) error {
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "User ID not found in token")
	}

	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	qualStatus, err := h.VolunteerSvc.GetVolunteerQualificationStatusForOrg(c.Request().Context(), userID, orgID)
	if err != nil {
		if errors.Is(err, services.ErrApplicationNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Approved application (qualification) not found for this user in this organization")
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgId", orgIDStr).Msg("Failed to get volunteer qualification status for org")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve qualification status")
	}

	return c.JSON(http.StatusOK, qualStatus)
}

// WithdrawOrganizationApplicationHandler godoc
// @Summary Withdraw an organization volunteer application
// @Description Allows the authenticated user to withdraw their own volunteer application for an organization. The application status must typically be 'pending' or 'approved'.
// @Tags Volunteer Management (User)
// @Produce json
// @Param appId path string true "Application ID (UUID) of the volunteer application to withdraw"
// @Success 200 {object} payloads.VolunteerApplicationResponse "Application withdrawn successfully, returns the updated application."
// @Failure 400 {object} payloads.ErrorResponse "Invalid application ID format"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (e.g., application not owned by user, or application not in a withdrawable state)"
// @Failure 404 {object} payloads.ErrorResponse "Application not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /users/me/volunteer/applications/{appId} [patch]
func (h *VolunteerHandler) WithdrawOrganizationApplicationHandler(c echo.Context) error {
	userID := authn.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "User ID not found in token")
	}

	appIDStr := c.Param("appId")
	appID, err := uuid.Parse(appIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid application ID format")
	}

	// No request body is expected for withdrawal as per plan, but if one were added (e.g. for notes):
	// req := new(payloads.WithdrawApplicationRequest)
	// if err := c.Bind(req); err != nil {
	// 	 return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	// }
	// if err := c.Validate(req); err != nil { // Assuming validator is set up in Echo
	// 	 return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	// }

	updatedApp, err := h.VolunteerSvc.WithdrawOrganizationApplication(c.Request().Context(), userID, appID)
	if err != nil {
		switch {
		case errors.Is(err, services.ErrApplicationNotFound):
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		case errors.Is(err, services.ErrApplicationNotWithdrawable):
			// Potentially could be a 403 Forbidden or 409 Conflict depending on precise meaning
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		default:
			log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to withdraw organization volunteer application")
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to withdraw application")
		}
	}

	return c.JSON(http.StatusOK, updatedApp)
}

// ---- Admin Handlers ----

// ListOrgVolunteerApplicationsHandler godoc
// @Summary List all volunteer applications for an organization (Admin)
// @Description Retrieves all volunteer applications for a specific organization, with optional status filter, limit, and offset. Requires staff privileges with 'admin', 'owner', or 'manager' role within the organization.
// @Tags Volunteer Management (Admin), Organizations
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param status query string false "Filter by application status (e.g., 'pending', 'approved', 'rejected')"
// @Param limit query int false "Number of applications to return per page" default(10)
// @Param offset query int false "Offset for pagination (number of items to skip)" default(0)
// @Success 200 {object} payloads.PaginatedResponse[payloads.VolunteerApplicationResponse] "Paginated list of volunteer applications"
// @Failure 400 {object} payloads.ErrorResponse "Invalid organization ID format or invalid status value"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized (admin token missing or invalid)"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (admin not staff in organization or lacks required role)"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /admin/organizations/{orgId}/volunteer/applications [get]
func (h *VolunteerHandler) ListOrgVolunteerApplicationsHandler(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	// Superadmin bypass
	if claims.Role != string(db.UserRoleSuperadmin) {
		isAuthorized, err := h.OrgSvc.CheckUserOrganizationRole(c.Request().Context(), payloads.CheckUserOrganizationRoleOptions{
			UserID:         claims.UserID,
			OrganizationID: orgID,
			RequiredRoles:  []string{"admin", "owner", "manager"},
		})
		if err != nil {
			if errors.Is(err, payloads.ErrNotMember) {
				log.Ctx(c.Request().Context()).Warn().Err(err).Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Admin user not a member of the organization")
				return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You are not a member of this organization.")
			}
			log.Ctx(c.Request().Context()).Error().Err(err).Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Error checking admin user role in organization")
			return echo.NewHTTPError(http.StatusInternalServerError, "Error checking permissions")
		}
		if !isAuthorized {
			log.Ctx(c.Request().Context()).Warn().Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Admin user does not have required role for listing org volunteer applications")
			return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You do not have the required role in this organization.")
		}
	}

	statusQuery := c.QueryParam("status")
	var statusPtr *string
	if statusQuery != "" {
		// TODO: Add validation for statusQuery against db.ApplicationStatusEnum values
		statusPtr = &statusQuery
	}

	limit := utils.ParseQueryInt(c, "limit", DefaultVolAppPageLimit)
	offset := utils.ParseQueryInt(c, "offset", DefaultVolAppPageOffset)

	if limit <= 0 {
		limit = DefaultVolAppPageLimit
	}
	if limit > MaxVolAppPageLimit {
		limit = MaxVolAppPageLimit
	}
	if offset < 0 {
		offset = DefaultVolAppPageOffset
	}

	apps, totalCount, err := h.VolunteerSvc.ListOrgVolunteerApplications(c.Request().Context(), orgID, statusPtr, int32(limit), int32(offset))
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgId", orgIDStr).Msg("Failed to list organization volunteer applications")
		// Consider returning the actual error message for better debugging if it's not sensitive
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve applications: "+err.Error())
	}

	// Use page numbers for PaginatedResponse calculation, assuming offset is a multiple of limit for typical page-based UI
	// If offset is not a multiple of limit, currentPage calculation might be off for traditional pagers.
	// For offset/limit based pagination, current page is more like (offset/limit) + 1
	currentPage := (offset / limit) + 1
	if offset%limit != 0 { // If offset isn't a clean multiple, it could imply a non-standard scenario or direct offset usage
		// For simplicity, we'll still calculate based on direct division. UI might need to handle non-standard offsets.
	}

	paginatedResponse := payloads.NewPaginatedResponse(
		apps,               // data
		int32(currentPage), // currentPage
		int32(limit),       // pageSize
		totalCount,         // totalItems
	)

	return c.JSON(http.StatusOK, paginatedResponse)
}

// ListPendingVolunteerApplicationsForOrgHandler godoc
// @Summary List pending volunteer applications for an organization (Admin)
// @Description Retrieves all volunteer applications with status 'pending' for a specific organization. Requires staff privileges with 'admin', 'owner', or 'manager' role within the organization.
// @Tags Volunteer Management (Admin), Organizations
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Success 200 {array} payloads.VolunteerApplicationResponse "List of pending applications with status 'pending'"
// @Failure 400 {object} payloads.ErrorResponse "Invalid organization ID format"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized (admin token missing or invalid)"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (admin not staff in organization or lacks required role)"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /admin/organizations/{orgId}/volunteer/applications/pending [get]
func (h *VolunteerHandler) ListPendingVolunteerApplicationsForOrgHandler(c echo.Context) error {
	// Admin check middleware should already be applied to this route
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	// Superadmin bypass
	if claims.Role != string(db.UserRoleSuperadmin) {
		// Permission check: Does the admin user belong to this org with appropriate role?
		isAuthorized, err := h.OrgSvc.CheckUserOrganizationRole(c.Request().Context(), payloads.CheckUserOrganizationRoleOptions{
			UserID:         claims.UserID,
			OrganizationID: orgID,
			RequiredRoles:  []string{"admin", "owner", "manager"}, // Define appropriate roles
		})
		if err != nil {
			if errors.Is(err, payloads.ErrNotMember) {
				log.Ctx(c.Request().Context()).Warn().Err(err).Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Admin user not a member of the organization")
				return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You are not a member of this organization.")
			}
			log.Ctx(c.Request().Context()).Error().Err(err).Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Error checking admin user role in organization")
			return echo.NewHTTPError(http.StatusInternalServerError, "Error checking permissions")
		}
		if !isAuthorized {
			log.Ctx(c.Request().Context()).Warn().Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Admin user does not have required role for listing pending applications")
			return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You do not have the required role in this organization.")
		}
	}

	pendingApps, err := h.VolunteerSvc.ListPendingVolunteerApplicationsForOrganization(c.Request().Context(), orgID)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgId", orgIDStr).Msg("Failed to list pending volunteer applications for org")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve pending applications")
	}

	return c.JSON(http.StatusOK, pendingApps)
}

// GetVolunteerApplicationDetailsForAdminHandler godoc
// @Summary Get details of a volunteer application for admin review
// @Description Retrieves full details of a specific volunteer application, including user info and associated verification documents, for admin review. Requires staff privileges within the organization.
// @Tags Volunteer Management (Admin), Organizations
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param appId path string true "Application ID (UUID)"
// @Success 200 {object} services.EnhancedVolunteerApplicationDetails "Application details with user info and verifications"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (not staff in organization)"
// @Failure 404 {object} payloads.ErrorResponse "Organization or Application not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /admin/organizations/{orgId}/volunteer/applications/{appId} [get]
func (h *VolunteerHandler) GetVolunteerApplicationDetailsForAdminHandler(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	appIDStr := c.Param("appId")
	appID, err := uuid.Parse(appIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid application ID format")
	}

	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	// Superadmin bypass
	if claims.Role != string(db.UserRoleSuperadmin) {
		// Permission check: Does the admin user belong to this org with appropriate role?
		isAuthorized, err := h.OrgSvc.CheckUserOrganizationRole(c.Request().Context(), payloads.CheckUserOrganizationRoleOptions{
			UserID:         claims.UserID,
			OrganizationID: orgID,
			RequiredRoles:  []string{"admin", "owner", "manager"}, // Define appropriate roles
		})
		if err != nil {
			if errors.Is(err, payloads.ErrNotMember) {
				log.Ctx(c.Request().Context()).Warn().Err(err).Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Admin user not a member of the organization")
				return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You are not a member of this organization.")
			}
			log.Ctx(c.Request().Context()).Error().Err(err).Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Error checking admin user role in organization")
			return echo.NewHTTPError(http.StatusInternalServerError, "Error checking permissions")
		}
		if !isAuthorized {
			log.Ctx(c.Request().Context()).Warn().Str("adminUserID", claims.UserID.String()).Str("orgID", orgID.String()).Msg("Admin user does not have required role for viewing volunteer application")
			return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You do not have the required role in this organization.")
		}
	}

	appDetails, err := h.VolunteerSvc.GetVolunteerApplicationDetailsForAdmin(c.Request().Context(), orgID, appID, h.VerificationSvc)
	if err != nil {
		if errors.Is(err, services.ErrApplicationNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgId", orgIDStr).Str("appId", appIDStr).Msg("Failed to get volunteer application details for admin")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve application details")
	}

	return c.JSON(http.StatusOK, appDetails)
}

// ReviewVolunteerApplicationHandler godoc
// @Summary Review a volunteer application (Admin)
// @Description Allows an admin to review a volunteer application, updating its status (e.g., to 'approved' or 'rejected') and optionally providing feedback. Uses the consolidated application_status_enum for status values.
// @Tags Volunteer Management (Admin), Organizations
// @Accept json
// @Produce json
// @Param orgId path string true "Organization ID (UUID)"
// @Param appId path string true "Volunteer Application ID (UUID)"
// @Param body body payloads.AdminReviewVolunteerApplicationRequest true "Review details (new status using application_status_enum, and optional admin_feedback)"
// @Success 200 {object} payloads.VolunteerApplicationResponse "Application reviewed and status updated"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request (e.g., invalid ID format, invalid status value from enum, bad request body)"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized (admin token missing or invalid)"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden (admin not staff in organization or lacks required role, or trying to review an application not belonging to the org)"
// @Failure 404 {object} payloads.ErrorResponse "Application not found"
// @Failure 409 {object} payloads.ErrorResponse "Conflict (e.g., application is not in a reviewable state like 'pending', or invalid status transition)"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /admin/organizations/{orgId}/volunteer/applications/{appId}/review [patch]
func (h *VolunteerHandler) ReviewVolunteerApplicationHandler(c echo.Context) error {
	orgIDStr := c.Param("orgId")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid organization ID format")
	}

	appIDStr := c.Param("appId")
	appID, err := uuid.Parse(appIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid application ID format")
	}

	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}
	adminUserID := claims.UserID

	// Superadmin bypass
	if claims.Role != string(db.UserRoleSuperadmin) {
		// Permission Check: User must be a staff member (admin, owner, manager) of the organization.
		isAuthorized, err := h.OrgSvc.CheckUserOrganizationRole(c.Request().Context(), payloads.CheckUserOrganizationRoleOptions{
			UserID:         adminUserID,
			OrganizationID: orgID,
			RequiredRoles:  []string{"admin", "owner", "manager"},
		})
		if err != nil {
			if errors.Is(err, payloads.ErrNotMember) {
				log.Ctx(c.Request().Context()).Warn().Err(err).Str("adminUserID", adminUserID.String()).Str("orgID", orgID.String()).Msg("Admin user not a member of the organization for review")
				return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You are not a member of this organization.")
			}
			log.Ctx(c.Request().Context()).Error().Err(err).Str("adminUserID", adminUserID.String()).Str("orgID", orgID.String()).Msg("Error checking admin user role for review")
			return echo.NewHTTPError(http.StatusInternalServerError, "Error checking permissions for review")
		}
		if !isAuthorized {
			log.Ctx(c.Request().Context()).Warn().Str("adminUserID", adminUserID.String()).Str("orgID", orgID.String()).Msg("Admin user does not have required role for reviewing applications")
			return echo.NewHTTPError(http.StatusForbidden, "Forbidden: You do not have the required role to review applications.")
		}
	}

	req := new(payloads.AdminReviewVolunteerApplicationRequest)
	if err := c.Bind(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}
	if err := c.Validate(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// The service layer will validate if the status string is a valid ApplicationStatusEnum value

	updatedApp, err := h.VolunteerSvc.ReviewVolunteerApplication(c.Request().Context(), orgID, appID, adminUserID, *req)
	if err != nil {
		if errors.Is(err, services.ErrApplicationNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgId", orgIDStr).Str("appId", appIDStr).Msg("Failed to review volunteer application")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to review application")
	}

	return c.JSON(http.StatusOK, updatedApp)
}
