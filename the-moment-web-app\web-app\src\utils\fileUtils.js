/**
 * Formats a file size in bytes to a human-readable string (e.g., "2.5 MB")
 * @param {number} sizeInBytes - The file size in bytes
 * @returns {string} Formatted file size with appropriate unit
 */
export const formatFileSize = (sizeInBytes) => {
  if (sizeInBytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(sizeInBytes) / Math.log(k));
  const size = parseFloat((sizeInBytes / Math.pow(k, i)).toFixed(2));
  
  // Remove trailing zeros after decimal point
  const sizeString = size.toString();
  const finalSize = sizeString.includes('.')
    ? sizeString.replace(/\.0+$/, '').replace(/(\.\d*?)0+$/, '$1')
    : sizeString;
    
  return `${finalSize} ${sizes[i]}`;
}; 