import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, App as AntApp, Spin } from 'antd';
import zhHK from 'antd/locale/zh_HK';
import enUS from 'antd/locale/en_US';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-hk';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import MainLayout from './pages/MainLayout';
import { AuthProvider } from './contexts/AuthContext';
import { OrganizationProvider, useOrganization } from './contexts/OrganizationContext';
import './i18n/config';
import ScrollToTop from './components/ScrollToTop';
import { useTranslation } from 'react-i18next';
import { publicRoutes, protectedRoutes } from './routes/routeConfig';
import ErrorPage from './pages/ErrorPage';
import RouteMiddleware from './middleware/RouteMiddleware';
import ChangeContactPage from './pages/ChangeContactPage';
import { UserProvider } from './contexts/UserContext';

// Loading fallback component for lazy-loaded routes
const LoadingFallback = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh' 
  }}>
    <Spin size="large" />
  </div>
);

// Wrap the main app content with theme configuration
const ThemedApp = () => {
  const { i18n } = useTranslation();
  const { themeConfig } = useOrganization();
  
  // Set antd locale based on current i18n language
  const antdLocale = i18n.language === 'zh-HK' ? zhHK : enUS;
  
  // Set dayjs locale
  dayjs.locale(i18n.language === 'zh-HK' ? 'zh-hk' : 'en');

  // Update antd locale when language changes
  React.useEffect(() => {
    dayjs.locale(i18n.language === 'zh-HK' ? 'zh-hk' : 'en');
  }, [i18n.language]);

  // Helper function to render routes recursively
  const renderRoutes = (routes) => {
    return Object.values(routes).map((route) => {
      const RouteComponent = route.component;
      
      if (route.children) {
        return (
          <Route key={route.path} path={route.path}>
            {route.component && (
              <Route
                index
                element={
                  <Suspense fallback={<LoadingFallback />}>
                    <RouteComponent />
                  </Suspense>
                }
              />
            )}
            {renderRoutes(route.children)}
          </Route>
        );
      }

      return (
        <Route
          key={route.path}
          path={route.path}
          element={
            <Suspense fallback={<LoadingFallback />}>
              <RouteComponent />
            </Suspense>
          }
        />
      );
    });
  };

  return (
    <ConfigProvider locale={antdLocale} theme={themeConfig}>
      <AntApp>
        <ScrollToTop />
        <Suspense fallback={<LoadingFallback />}>
          <Routes>
            {/* Public routes - accessible without login */}
            {renderRoutes(publicRoutes)}
            
            {/* Change contact route - special case */}
            <Route path="/settings/change-contact" element={<ChangeContactPage />} />
            
            {/* Error routes */}
            <Route path="/error/:type" element={<ErrorPage />} />
            
            {/* Protected routes wrapped in MainLayout and RouteMiddleware */}
            <Route element={
              <RouteMiddleware routes={protectedRoutes}>
                <MainLayout />
              </RouteMiddleware>
            }>
              {renderRoutes(protectedRoutes)}
            </Route>
            
            {/* Catch-all route */}
            <Route path="*" element={<ErrorPage />} />
          </Routes>
        </Suspense>
      </AntApp>
    </ConfigProvider>
  );
};

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <OrganizationProvider>
          <UserProvider>
            <Router>
              <ThemedApp />
            </Router>
          </UserProvider>
        </OrganizationProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
