/* EventDetails.css */

/* Event Description - TipTap Editor Content Styling */
.event-description {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #1f2937; /* text-gray-800 equivalent - darker text */
  line-height: 1.5;
  font-size: 1rem; /* Standard text size to match rest of the page */
}

/* Headings */
.event-description h1 {
  font-size: 1.5rem; /* Matches text-xl used in section headings */
  margin: 0.75em 0 0.5em;
  font-weight: 600;
  line-height: 1.3;
  color: #1f2937; /* text-gray-800 equivalent */
}

.event-description h2 {
  font-size: 1.25rem; /* Slightly smaller than h1 */
  margin: 0.75em 0 0.5em;
  font-weight: 600;
  line-height: 1.3;
  color: #1f2937; /* text-gray-800 equivalent */
}

.event-description h3 {
  font-size: 1.125rem; /* Slightly smaller than h2 */
  margin: 0.75em 0 0.5em;
  font-weight: 600;
  line-height: 1.3;
  color: #1f2937; /* text-gray-800 equivalent */
}

/* Paragraphs and spacing */
.event-description p {
  margin: 0.5em 0;
  font-size: 1rem;
  line-height: 1.5;
  color: #1f2937; /* text-gray-800 equivalent - darker text */
}

/* Lists - Enhanced styles */
.event-description ul {
  list-style-type: disc;
  padding-left: 1.5em;
  margin: 0.75em 0;
}

.event-description ol {
  list-style-type: decimal;
  padding-left: 1.5em;
  margin: 0.75em 0;
}

.event-description ul > li,
.event-description ol > li {
  margin: 0.25em 0;
  padding-left: 0.25em;
}

.event-description ul > li::marker {
  color: #6b7280; /* text-gray-500 equivalent */
}

.event-description ol > li::marker {
  color: #6b7280; /* text-gray-500 equivalent */
}

/* Nested lists */
.event-description ul ul,
.event-description ul ol,
.event-description ol ul,
.event-description ol ol {
  margin: 0.5em 0;
}

/* Links */
.event-description a {
  color: #1890ff;
  text-decoration: none;
}

.event-description a:hover {
  text-decoration: underline;
}

/* Text Styles */
.event-description strong {
  font-weight: 600;
  color: #111827; /* text-gray-900 equivalent */
}

.event-description em {
  font-style: italic;
}

/* Text Alignment */
.event-description [style*="text-align: center"] {
  text-align: center;
}

.event-description [style*="text-align: right"] {
  text-align: right;
}

/* Blockquotes */
.event-description blockquote {
  border-left: 3px solid #e5e7eb; /* border-gray-200 equivalent */
  padding-left: 0.75em;
  margin: 0.75em 0;
  color: #6b7280; /* text-gray-500 equivalent */
  font-style: italic;
}

/* Code blocks */
.event-description pre {
  background-color: #f3f4f6; /* bg-gray-100 equivalent */
  border-radius: 4px;
  padding: 12px;
  overflow: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
  line-height: 1.4;
  margin: 0.75em 0;
}

.event-description code {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
}

/* Tables */
.event-description table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.75em 0;
  overflow-x: auto;
  display: block;
}

.event-description table th,
.event-description table td {
  border: 1px solid #e5e7eb; /* border-gray-200 equivalent */
  padding: 6px 10px;
}

.event-description table th {
  background-color: #f9fafb; /* bg-gray-50 equivalent */
  font-weight: 600;
}

.event-description table tr:nth-child(even) {
  background-color: #f9fafb; /* bg-gray-50 equivalent */
}

/* Images */
.event-description img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 0.75em 0;
}

/* Horizontal rule */
.event-description hr {
  border: none;
  border-top: 1px solid #e5e7eb; /* border-gray-200 equivalent */
  margin: 1em 0;
}

/* Section Divider Styling */
.section-divider .ant-divider-inner-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937; /* text-gray-800 equivalent */
}

/* Responsive Design */
@media (max-width: 768px) {
  .event-description {
    font-size: 0.9375rem;
  }
  
  .event-description h1 {
    font-size: 1.375rem;
  }
  
  .event-description h2 {
    font-size: 1.25rem;
  }
  
  .event-description h3 {
    font-size: 1.125rem;
  }
  
  .event-description p {
    font-size: 0.9375rem;
  }
  
  .section-divider .ant-divider-inner-text {
    font-size: 1.125rem;
  }
}