-- name: Create<PERSON>ob :one
INSERT INTO jobs (job_type, payload, run_at, max_retries)
VALUES ($1, $2, CURRENT_TIMESTAMP + ($3::TEXT || ' seconds')::INTERVAL, $4)
RETURNING *;

-- name: GetNextPendingJob :one
SELECT id, job_type, payload, attempts, max_retries
FROM jobs
WHERE status = 'pending' AND run_at <= CURRENT_TIMESTAMP AND attempts < max_retries
ORDER BY created_at ASC
LIMIT 1
FOR UPDATE SKIP LOCKED;

-- name: UpdateJobStatus :one
UPDATE jobs
SET status = $2, last_error = $3, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING *;

-- name: IncrementJobAttempts :one
UPDATE jobs
SET attempts = attempts + 1, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING *;

-- name: DeleteJob :exec
DELETE FROM jobs WHERE id = $1;

-- name: RetryJob :one
UPDATE jobs
SET status = 'pending',
    attempts = attempts + 1,
    last_error = $2,
    run_at = CURRENT_TIMESTAMP + ($3::TEXT || ' seconds')::INTERVAL, -- $3 is delay in seconds
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING *;

-- name: MarkJobRunning :one
UPDATE jobs
SET status = 'running', attempts = attempts + 1, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND status = 'pending' -- Ensure it was pending before marking as running
RETURNING *; 