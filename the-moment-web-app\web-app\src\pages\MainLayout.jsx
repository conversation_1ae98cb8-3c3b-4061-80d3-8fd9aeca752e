import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { Layout, Modal } from 'antd';
import {
    HomeOutlined,
    SettingOutlined,
    UserOutlined,
    ScheduleOutlined,
    SignatureOutlined,
    LogoutOutlined,
    CheckCircleOutlined,
    CreditCardOutlined,
    SolutionOutlined,
    FileTextOutlined,
    ReadOutlined,
    AuditOutlined,
    FileSearchOutlined,
    TeamOutlined,
    ProductOutlined,
    UnorderedListOutlined,
    FileOutlined,
} from '@ant-design/icons';
import { useMediaQuery } from 'react-responsive';
import { useTranslation } from 'react-i18next';
import '../styles/MainLayout.css';
import { useAuth } from '../contexts/AuthContext';
import { useUser } from '../contexts/UserContext';
import { useOrganization } from '../contexts/OrganizationContext';
import MainHeader from '../components/MainHeader';
import Sidebar from '../components/Sidebar';
import { findRouteMetaInfo } from '../routes/routeConfig';
import { AuthProvider } from '../contexts/AuthContext';
import { UserProvider } from '../contexts/UserContext';
import { ALL_ORGANIZATION_ID } from '../contexts/OrganizationContext';

const { Content } = Layout;

const MainLayout = () => {
    const [collapsed, setCollapsed] = useState(false);
    const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
    const [pageTitle, setPageTitle] = useState('');
    const { isLoggedIn, logout } = useAuth();
    const { currentUser, isLoading: isUserLoading } = useUser();
    const { currentOrganization, handleOrganizationChange, organizations } = useOrganization();
    const navigate = useNavigate();
    const location = useLocation();
    const isTablet = useMediaQuery({ maxWidth: 992 });
    const isMobile = useMediaQuery({ maxWidth: 768 });
    const { t, i18n } = useTranslation();

    const filteredItems = useMemo(() => {
        // If user is not loaded yet, return empty array
        if (isUserLoading || !currentUser) return [];
        
        const role = currentUser.role;
        const isDefaultOrgSelected = currentOrganization && currentOrganization.id === ALL_ORGANIZATION_ID;
        const isSuperAdmin = role === 'super_admin';
        const isAdmin = role === 'admin';
        
        // Define all menu items
        const allMenuItems = [
            {
                key: 'home',
                icon: <HomeOutlined />,
                label: t('mainLayout.menu.home'),
                roles: ['user', 'super_admin', 'admin', 'guest'],
            },
            {
                key: 'events-group',
                icon: <ProductOutlined />,
                label: t('mainLayout.menu.events.group'),
                roles: ['user', 'super_admin', 'admin', 'guest'],
                children: [
                    {
                        key: 'events',
                        label: t('mainLayout.menu.events.list'),
                        icon: <UnorderedListOutlined />,
                        roles: ['user', 'super_admin', 'admin', 'guest'],
                    },
                    {
                        key: 'my-events',
                        icon: <ScheduleOutlined />,
                        label: t('mainLayout.menu.events.myEvents'),
                        roles: ['user'],
                    },
                    {
                        key: 'events-management',
                        label: t('mainLayout.menu.events.management'),
                        icon: <FileSearchOutlined />,
                        roles: ['super_admin', 'admin'],
                    }
                ]
            },
            {
                key: 'user-management-group',
                icon: <SolutionOutlined />,
                label: t('mainLayout.menu.userManagement.title'),
                roles: ['super_admin', 'admin'],
                children: [
                    {
                        key: 'volunteers-approval',
                        label: t('mainLayout.menu.userManagement.approveVolunteers'),
                        icon: <CheckCircleOutlined />,
                        roles: ['super_admin', 'admin'],
                        visibleWhen: {
                            roles: ['super_admin'],
                            customRule: (role, isDefaultOrgSelected) => {
                                return role === 'super_admin' || (role === 'admin' && !isDefaultOrgSelected);
                            }
                        }
                    },
                    {
                        key: 'verification',
                        label: t('mainLayout.menu.userManagement.verification'),
                        icon: <AuditOutlined />,
                        roles: ['super_admin']
                    },
                    {
                        key: 'user-management',
                        label: t('mainLayout.menu.userManagement.userManagement'),
                        icon: <TeamOutlined />,
                        roles: ['super_admin', 'admin'],
                        visibleWhen: {
                            roles: ['super_admin', 'admin'],
                            customRule: (role, isDefaultOrgSelected) => {
                                return role === 'super_admin' || (role === 'admin' && !isDefaultOrgSelected);
                            }
                        }
                    },
                    // {
                    //     key: 'edit-managers',
                    //     label: t('mainLayout.menu.userManagement.editManagers'),
                    //     icon: <TeamOutlined />,
                    //     roles: ['super_admin'],
                    // }
                ]
            },
            {
                key: 'organization-settings',
                icon: <TeamOutlined />,
                label: t('mainLayout.menu.organizationSettings'),
                roles: ['super_admin', 'admin'],
                visibleWhen: {
                    roles: ['super_admin', 'admin'],
                    customRule: (role, isDefaultOrgSelected) => {
                        return role === 'super_admin' || (role === 'admin' && !isDefaultOrgSelected);
                    }
                },
            },
            {
                key: 'news-group',
                icon: <ReadOutlined />,
                label: t('mainLayout.menu.news.groupTitle'),
                roles: ['user', 'super_admin', 'admin', 'guest'],
                children: [
                    {
                        key: 'posts',
                        label: t('mainLayout.menu.news.publishedPosts'),
                        icon: <SignatureOutlined />,
                        roles: ['user', 'super_admin', 'admin', 'guest'],
                    },
                    {
                        key: 'posts-management',
                        label: t('mainLayout.menu.news.postManagement'),
                        icon: <FileSearchOutlined />,
                        roles: ['super_admin', 'admin'],
                    }
                ]
            },
            {
                key: 'resources-group',
                icon: <FileTextOutlined />,
                label: t('mainLayout.menu.resources.groupTitle'),
                roles: ['user', 'super_admin', 'admin', 'guest'],
                children: [
                    {
                        key: 'resources',
                        label: t('mainLayout.menu.resources.publishedResources'),
                        icon: <UnorderedListOutlined />,
                        roles: ['user', 'super_admin', 'admin', 'guest'],
                    },
                    {
                        key: 'resources-management',
                        label: t('mainLayout.menu.resources.resourceManagement'),
                        icon: <AuditOutlined />,
                        roles: ['super_admin', 'admin'],
                    }
                ]
            },
        ];
        
        // 检查菜单项是否对当前用户可见
        const isMenuItemVisible = (item) => {
            // 检查基本角色权限
            const hasRoleAccess = !item.roles || item.roles.includes(role);
            if (!hasRoleAccess) return false;
            
            // 检查自定义可见性规则
            if (item.visibleWhen?.customRule) {
                return item.visibleWhen.customRule(role, isDefaultOrgSelected);
            }
            
            // 特殊处理超级管理员和管理员角色在默认组织下的权限
            if (isDefaultOrgSelected) {
                const isAdminOnlyItem = item.roles && 
                    item.roles.length > 0 && 
                    item.roles.every(r => r === 'admin' || r === 'super_admin');
                
                if (isAdminOnlyItem && !isSuperAdmin) {
                    return false;
                }
            }
            
            return true;
        };
        
        // 处理菜单项及其子项
        const processMenuItem = (item) => {
            // 检查当前菜单项是否可见
            if (!isMenuItemVisible(item)) return null;
            
            // 创建一个干净的菜单项，移除所有辅助属性
            const { roles, visibleWhen, ...cleanItem } = item;
            
            // 处理子菜单项
            if (item.children) {
                const filteredChildren = item.children
                    .map(processMenuItem)
                    .filter(Boolean); // 移除空值
                
                if (filteredChildren.length === 0) {
                    return null; // 如果没有可见的子项，不显示父菜单
                }
                
                // 如果只有一个子项，将其提升到父级
                if (filteredChildren.length === 1) {
                    return {
                        ...filteredChildren[0],
                        icon: filteredChildren[0].icon || cleanItem.icon
                    };
                }
                
                return {
                    ...cleanItem,
                    children: filteredChildren
                };
            }
            
            return cleanItem;
        };
        
        // 处理所有顶级菜单项
        return allMenuItems
            .map(processMenuItem)
            .filter(Boolean); // 过滤掉所有为null的项
    }, [currentUser, currentOrganization, isUserLoading, t]);

    useEffect(() => {
        const handleResize = () => {
            if (isTablet) {
                setCollapsed(true);
            }
            if (isMobile) {
                setMobileDrawerOpen(false);
            }
        };
        window.addEventListener('resize', handleResize);
        handleResize();
        return () => window.removeEventListener('resize', handleResize);
    }, [isTablet, isMobile]);

    // Close drawer when route changes
    useEffect(() => {
        setMobileDrawerOpen(false);
    }, [location.pathname]);

    const handleMenuClick = (e) => {
        if (e.key === 'home') {
            navigate('/');
        } else {
            navigate('/' + e.key);
        }
    };

    const handleLogout = useCallback(() => {
        Modal.confirm({
            title: t('mainLayout.confirmLogout.title'),
            onOk: () => {
                logout();
                navigate('/');
            }
        });
    }, [t, logout, navigate]);

    const userMenuItems = useMemo(() => {
        const items = [
            {
                key: 'profile',
                label: t('mainLayout.userMenu.profile'),
                icon: <UserOutlined />,
                onClick: () => currentUser && navigate(`/user-profile/${currentUser.id}`),
                // This item will be filtered out for admin/super_admin
            },
            {
                key: 'settings',
                label: t('mainLayout.menu.application'),
                icon: <FileOutlined />,
                onClick: () => navigate('/settings'),
                // This item will be filtered out for admin/super_admin
            },
            {
                key: 'logout',
                label: t('mainLayout.userMenu.logout'),
                icon: <LogoutOutlined />,
                onClick: handleLogout,
            },
        ];

        if (currentUser && (currentUser.role === 'admin' || currentUser.role === 'super_admin')) {
            return items.filter(item => item.key !== 'settings' && item.key !== 'profile');
        }

        // For items not filtered out, apply original disabled logic
        const profileItem = items.find(item => item.key === 'profile');
        if (profileItem) {
            profileItem.disabled = !currentUser || isUserLoading || !currentUser.id || currentUser.id === 'guest-user-placeholder';
        }

        const settingsItem = items.find(item => item.key === 'settings');
        if (settingsItem) {
            settingsItem.disabled = !currentUser || isUserLoading;
        }

        return items;
    }, [t, navigate, currentUser, isUserLoading, handleLogout]);

    // Add this function to get the selected keys based on current path
    const getSelectedKeys = () => {
        const path = location.pathname;

        // Remove trailing slash and get the base path
        const normalizedPath = path.endsWith('/') ? path.slice(0, -1) : path;
        const basePath = normalizedPath.split('/')[1] || 'home';

        // Special handling for nested routes
        if (path.includes('/events/')) {
            return ['events'];
        }
        if (path.includes('/my-events/')) {
            return ['my-events'];
        }
        if (path.includes('/verification/')) {
            return ['verification'];
        }

        return [basePath];
    };

    // Get the current page title
    const getPageTitle = useCallback(() => {
        const path = location.pathname;
        const normalizedPath = path.endsWith('/') ? path.slice(0, -1) : path;
        const routeInfo = findRouteMetaInfo(normalizedPath);
        return routeInfo.translationKey ? t(routeInfo.translationKey) : t('mainLayout.breadcrumbs.home');
    }, [location.pathname, t]);

    useEffect(() => {
        setPageTitle(getPageTitle());
    }, [location.pathname, i18n.language, getPageTitle]);

    // Get the logical parent route
    const getParentRoute = () => {
        const path = location.pathname;
        const normalizedPath = path.endsWith('/') ? path.slice(0, -1) : path;
        const routeInfo = findRouteMetaInfo(normalizedPath);
        return routeInfo.parent;
    };

    return (
        <Layout style={{ minHeight: '100vh' }}>
            <Sidebar
                collapsed={collapsed}
                setCollapsed={setCollapsed}
                currentOrganization={currentOrganization}
                handleOrganizationChange={handleOrganizationChange}
                organizations={organizations}
                isTablet={isTablet}
                isMobile={isMobile}
                mobileDrawerOpen={mobileDrawerOpen}
                onMobileDrawerClose={() => setMobileDrawerOpen(false)}
                filteredItems={filteredItems}
                getSelectedKeys={getSelectedKeys}
                handleMenuClick={handleMenuClick}
                user={currentUser}
                role={currentUser?.role}
                userMenuItems={userMenuItems}
            />
            <Layout style={{ 
                marginLeft: isMobile ? 0 : (collapsed ? 80 : 250), 
                backgroundColor: '#FFFFFF',
                transition: 'margin-left 0.2s'
            }}>
                <MainHeader
                    collapsed={collapsed}
                    pageTitle={pageTitle}
                    parentRoute={getParentRoute()}
                    isLoggedIn={isLoggedIn}
                    user={currentUser}
                    userMenuItems={userMenuItems}
                    isMobile={isMobile}
                    onMobileMenuClick={() => setMobileDrawerOpen(true)}
                    role={currentUser?.role}
                />
                <Content style={{ margin: '72px 0 0' }}>
                    <Outlet />
                </Content>
            </Layout>
        </Layout>
    );
}

export default MainLayout;
