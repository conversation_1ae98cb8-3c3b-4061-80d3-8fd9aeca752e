import React, { useEffect, useState, useCallback } from 'react';
import { Spin, Row, Col, Empty, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { PictureOutlined, CalendarOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { homeService } from '../services/homeService';
import { organizationService } from '../services/organizationService';
import { useOrganization, ALL_ORGANIZATION_ID } from '../contexts/OrganizationContext';
import { useAuth } from '../contexts/AuthContext';
import defaultLogo from '../assets/logo/default-logo.png';
import fallbackImage from '../assets/images/picture-loading-failed.svg';
import '../styles/Home.css';
import { formatDateOnly, formatDate as formatDateTime, formatToShortMonth, formatToDayOfMonth, formatSimpleDateTime, formatShortDatetime } from '../utils/dateFormatter';

const Home = () => {
  const { t, i18n } = useTranslation();
  const { 
    organizations, 
    currentOrganization,
    loading: orgContextLoading, 
    error: orgContextError 
  } = useOrganization();
  const { user } = useAuth();
  const isSuperAdmin = user?.role === 'super_admin';

  const [uiOrganizationDetails, setUiOrganizationDetails] = useState(null);
  const [posts, setPosts] = useState([]);
  const [events, setEvents] = useState([]);
  const [loadingOrgDetails, setLoadingOrgDetails] = useState(false);
  const [loadingPosts, setLoadingPosts] = useState(false);
  const [loadingEvents, setLoadingEvents] = useState(false);
  const [imageLoadingStates, setImageLoadingStates] = useState({});
  const [imageErrorStates, setImageErrorStates] = useState({});

  useEffect(() => {
    setLoadingOrgDetails(orgContextLoading);
    if (orgContextError) {
      message.error(t('messages.fetchError'));
      setUiOrganizationDetails(null);
      return;
    }

    if (currentOrganization) {
      setUiOrganizationDetails(currentOrganization);
    } else if (!orgContextLoading && !currentOrganization) {
      setUiOrganizationDetails({
        id: 'all',
        name: t('home.allOrganizationName'),
        logo_url: defaultLogo,
        theme: '#ff4d4f', 
      });
    } else if (!orgContextLoading && organizations && organizations.length === 0) {
      message.error(t('messages.fetchError'));
      setUiOrganizationDetails(null);
    }
  }, [currentOrganization, orgContextLoading, orgContextError, t, organizations]);

  const fetchHomeData = useCallback(async () => {
    setLoadingPosts(true);
    setLoadingEvents(true);

    const commonParams = { limit: 5, offset: 0 };

    try {
      let postApiParams = { ...commonParams, status: 'published' };
      if (currentOrganization) {
        if (currentOrganization.id === ALL_ORGANIZATION_ID) {
          if (isSuperAdmin) {
            // For super_admin, if "All Organizations" is selected, pass the ALL_ORGANIZATION_ID
            postApiParams.organization_id = ALL_ORGANIZATION_ID;
          }
          // For non-super admins, don't set organization_id for ALL_ORGANIZATION_ID
        } else {
          // A specific organization is selected
          postApiParams.organization_id = currentOrganization.id;
          
          // For non-super admin users, add organization_id2 to include posts from ALL_ORGANIZATION_ID as well
          if (!isSuperAdmin) {
            postApiParams.organization_id2 = ALL_ORGANIZATION_ID;
          }
        }
      }
      const postsData = await homeService.listPublicPostsForHome(postApiParams);
      setPosts(postsData || []); 
    } catch (err) {
      console.error("Failed to fetch posts for home:", err);
      message.error(t('home.messages.latestPostsError'));
      setPosts([]); 
    } finally {
      setLoadingPosts(false);
    }

    try {
      let eventApiParams = { ...commonParams, status: 'published' };
      if (currentOrganization) {
        if (currentOrganization.id === ALL_ORGANIZATION_ID) {
          if (isSuperAdmin) {
            // For super_admin, if "All Organizations" is selected, pass the ALL_ORGANIZATION_ID
            eventApiParams.org_id = ALL_ORGANIZATION_ID;
          }
          // For non-super admins, don't set org_id for ALL_ORGANIZATION_ID
        } else {
          // A specific organization is selected
          eventApiParams.org_id = currentOrganization.id;
          
          // For non-super admin users, add org_id2 to include events from ALL_ORGANIZATION_ID as well
          if (!isSuperAdmin) {
            eventApiParams.org_id2 = ALL_ORGANIZATION_ID;
          }
        }
      }

      const today = new Date();
      const sevenDaysLater = new Date();
      sevenDaysLater.setDate(today.getDate() + 7);

      eventApiParams.start_date = today.toISOString();
      eventApiParams.end_date = sevenDaysLater.toISOString();
      
      const eventsData = await homeService.listPublicEventsForHome(eventApiParams);
      console.log("eventsData", eventsData)
      setEvents(eventsData || []);
    } catch (err) {
      console.error("Failed to fetch events for home:", err);
      message.error(t('home.messages.upcomingError'));
      setEvents([]);
    } finally {
      setLoadingEvents(false);
    }
  }, [currentOrganization, t, isSuperAdmin]);

  useEffect(() => {
    if (!orgContextLoading) {
      fetchHomeData();
    }
  }, [orgContextLoading, fetchHomeData]);

  const handleImageLoad = useCallback((itemId) => {
    setImageLoadingStates(prev => ({
      ...prev,
      [itemId]: false
    }));
  }, []);

  const handleImageError = useCallback((itemId) => {
    setImageErrorStates(prev => ({
      ...prev,
      [itemId]: true
    }));
    setImageLoadingStates(prev => ({
      ...prev,
      [itemId]: false
    }));
  }, []);

  const calculateDaysRemaining = (eventDate) => {
    const today = new Date();
    const eventDay = new Date(eventDate);

    // Reset time components to compare dates only
    today.setHours(0, 0, 0, 0);
    eventDay.setHours(0, 0, 0, 0);

    const diffTime = eventDay - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  const renderPostImage = (post) => {
    // Initialize loading state for this image if not exists
    if (imageLoadingStates[post.id] === undefined) {
      setImageLoadingStates(prev => ({
        ...prev,
        [post.id]: true
      }));
    }

    // Find banner image first, or fallback to first image
    let imageUrl = '';
    if (post.media_items && post.media_items.length > 0) {
      const bannerImage = post.media_items.find(item => item.is_banner);
      const selectedImage = bannerImage || post.media_items[0];
      imageUrl = selectedImage.file_path || '';
    }

    if (imageErrorStates[post.id]) {
      return (
        <div className="w-full h-full flex items-center justify-center bg-gray-200">
          <div className="text-center">
            <PictureOutlined className="text-3xl text-gray-400" />
            <div className="text-gray-500 mt-2">{t('common.noImage')}</div>
          </div>
        </div>
      );
    }

    return (
      <img 
        src={imageUrl} 
        alt={post.title}
        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
        style={{ display: imageLoadingStates[post.id] ? 'none' : 'block' }}
        onLoad={() => handleImageLoad(post.id)}
        onError={() => handleImageError(post.id)}
      />
    );
  };

  // Similar to renderPostImage but for events
  const renderEventImage = (event) => {
    // Initialize loading state for this image if not exists
    if (imageLoadingStates[event.id] === undefined) {
      setImageLoadingStates(prev => ({
        ...prev,
        [event.id]: true
      }));
    }

    const imageBase = import.meta.env.VITE_MEDIA_BASE;
    const imagePath = event.media_items[0]?.file_path ?? '';
    const imageUrl = imagePath && imagePath.startsWith("http")? imagePath: (new URL(imagePath, imageBase)).toString();

    if (imageErrorStates[event.id]) {
      return (
        <div className="w-full h-full flex items-center justify-center bg-gray-200">
          <div className="text-center">
            <PictureOutlined className="text-xl text-gray-400" />
            <div className="text-gray-500 text-xs mt-1">{t('common.noImage')}</div>
          </div>
        </div>
      );
    }

    return (
      <img 
        src={imageUrl} 
        alt={event.title}
        className="news-item-image"
        style={{ display: imageLoadingStates[event.id] ? 'none' : 'block' }}
        onLoad={() => handleImageLoad(event.id)}
        onError={() => handleImageError(event.id)}
      />
    );
  };

  if (orgContextLoading && !uiOrganizationDetails) {
    return (
      <div className="flex justify-center items-center h-[400px]">
        <Spin size="large" />
      </div>
    );
  }

  // Render post card for desktop view
  const renderPostCard = (post) => (
    <div key={post.id} className="h-full">
      <Link to={`/posts/${post.id || post.slug}`} className="group block h-full">
        <div className="border border-gray-100 rounded-lg overflow-hidden transition-all duration-300 h-full">
          <div className="h-48 overflow-hidden">
            {renderPostImage(post)}
          </div>
          <div className="px-4 py-2">
            <h3 className="text-lg font-semibold text-gray-800 line-clamp-2">
              {post.title}
            </h3>
            <div className="text-sm text-gray-500 mt-2 space-y-1">
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span className="truncate">{post.author_display_name || t('common.unknown')}</span>
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span>{formatDateOnly(post.updated_at || post.published_at, i18n.language)}</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );

  // Render post item for mobile view (without card styling)
  const renderMobilePostItem = (post) => (
    <Link key={post.id} to={`/posts/${post.id || post.slug}`} className="block group py-4">
      <div className="flex">
        <div className="mr-4 w-24 h-24 flex-shrink-0 overflow-hidden rounded-md">
          {renderPostImage(post)}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
            {post.title}
          </h3>
          <div className="text-sm text-gray-500">
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              <span className="truncate">{post.author_display_name || t('common.unknown')}</span>
            </div>
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <span>{formatDateOnly(post.updated_at || post.published_at, i18n.language)}</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );

  // Render event item with new styling from restored_home.js
  const renderEventItem = (event) => {
    const daysRemaining = calculateDaysRemaining(event.start_time);
    const isUrgent = daysRemaining < 3;
    
    return (
      <Link key={event.id} to={`/events/${event.id}`} className="block group">
        <div className="news-item news-item-larger">
          <div className="news-item-content">
            {/* Simple Glass Calendar with Days Remaining */}
            <div className={`simple-glass-calendar larger ${isUrgent ? 'urgent' : ''}`}>
              <div className="calendar-number">{daysRemaining}</div>
              <div className="calendar-days-text">
                {daysRemaining === 1 ? t('home.day') : t('home.days')}
              </div>
            </div>

            <div className="news-item-text">
              <div className="news-item-title">{event.title}</div>
              <div className="news-item-meta">
                <CalendarOutlined className="mr-1" /> {formatShortDatetime(event.start_time, i18n.language)}
              </div>
              <div className="news-item-meta mt-1">
                <EnvironmentOutlined className="mr-1" /> 
                <span className="truncate">
                  {event.location_short || event.location_full_address || t('common.unknown')}
                </span>
              </div>
            </div>
          </div>
          <div className="news-item-image-container larger">
            {renderEventImage(event)}
          </div>
        </div>
      </Link>
    );
  };

  // Render mobile event item (simplified)
  const renderMobileEventItem = (event) => (
    <Link key={event.id} to={`/events/${event.id}`} className="block group py-4">
      <div className="flex">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
            {event.title}
          </h3>
          <div className="text-sm text-gray-500 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>{formatSimpleDateTime(event.start_time)}</span>
            <span className="mx-1">•</span>
            <span className="truncate">{event.location_short || event.location_full_address || t('common.unknown')}</span>
          </div>
        </div>
      </div>
    </Link>
  );

  return (
    <div>
      {/* Organization Header */}
      {uiOrganizationDetails && (
        <div className="w-full bg-white border-b border-gray-100 sticky top-0 z-10">
          <div className="container px-6 py-3">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-800 truncate">{uiOrganizationDetails.description}</h1>
            </div>
          </div>
        </div>
      )}

      {/* Desktop Layout - Side by Side */}
      <div className="hidden lg:flex lg:flex-1 lg:overflow-hidden bg-white">
        {/* Latest Posts Section - Takes 2/3 width */}
        <div className="w-2/3 h-full">
          <div className="h-full flex flex-col">
            <div className="border-b border-gray-100 px-6 py-3 flex-shrink-0 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">{t('home.sections.latestPosts')}</h2>
              <Link to="/posts" className="text-blue-600 text-sm font-medium hover:underline">
                {t('home.buttons.viewAll')}
              </Link>
            </div>
            
            {/* Scrollable content area for posts */}
            <div className="flex-1 overflow-y-auto custom-scrollbar p-4">
              {/* Posts loading state */}
              {loadingPosts && (
                <div className="flex justify-center items-center p-10">
                  <Spin />
                </div>
              )}

              {/* No posts state (also shown on error as posts array will be empty) */}
              {!loadingPosts && posts.length === 0 && (
                <div className="p-10 text-center">
                  <Empty />
                </div>
              )}

              {/* Posts grid using Ant Design Row/Col system */}
              {!loadingPosts && posts.length > 0 && (
                <div className="mb-4">
                  <Row gutter={[16, 16]}>
                    {posts.map(post => (
                      <Col md={24} lg={12} xl={8} key={post.id}>
                        {renderPostCard(post)}
                      </Col>
                    ))}
                  </Row>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Upcoming Events Section - Takes 1/3 width */}
        <div className="w-1/3 h-full border-l border-gray-100">
          <div className="h-full flex flex-col">
            <div className="border-b border-gray-100 px-6 py-3 flex-shrink-0 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">{t('home.sections.upcomingEvents')}</h2>
              <Link to="/events" className="text-blue-600 text-sm font-medium hover:underline">
                {t('home.buttons.viewAll')}
              </Link>
            </div>
            
            {/* Scrollable content area for events */}
            <div className="flex-1 overflow-y-auto custom-scrollbar">
              {/* Events loading state */}
              {loadingEvents && (
                <div className="flex justify-center items-center p-10">
                  <Spin />
                </div>
              )}

              {/* No events state (also shown on error as events array will be empty) */}
              {!loadingEvents && events.length === 0 && (
                <div className="p-10 text-center">
                  <Empty />
                </div>
              )}

              {/* Events list with updated styling */}
              {!loadingEvents && events.length > 0 && (
                <div className="upcoming-events-list">
                  {events.map(event => renderEventItem(event))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout - Linear Top to Bottom */}
      <div className="lg:hidden bg-white">
        {/* Mobile Posts Section */}
        <div className="mt-4 mx-4">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-bold text-gray-800">{t('home.sections.latestPosts')}</h2>
            <Link to="/posts" className="text-blue-600 text-sm font-medium">
              {t('home.buttons.viewAll')}
            </Link>
          </div>
          
          {/* Posts loading state */}
          {loadingPosts && (
            <div className="flex justify-center items-center py-8">
              <Spin />
            </div>
          )}

          {/* No posts state (also shown on error as posts array will be empty) */}
          {!loadingPosts && posts.length === 0 && (
            <div className="py-8 text-center">
              <Empty />
            </div>
          )}

          {/* Posts list */}
          {!loadingPosts && posts.length > 0 && (
            <div className="divide-y divide-gray-100">
              {posts.map(post => renderMobilePostItem(post))}
            </div>
          )}
        </div>

        {/* Mobile Events Section */}
        <div className="mt-8 mx-4 mb-6">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-bold text-gray-800">{t('home.sections.upcomingEvents')}</h2>
            <Link to="/events" className="text-blue-600 text-sm font-medium">
              {t('home.buttons.viewAll')}
            </Link>
          </div>
          
          {/* Events loading state */}
          {loadingEvents && (
            <div className="flex justify-center items-center py-8">
              <Spin />
            </div>
          )}

          {/* No events state (also shown on error as events array will be empty) */}
          {!loadingEvents && events.length === 0 && (
            <div className="py-8 text-center">
              <Empty />
            </div>
          )}

          {/* Events list */}
          {!loadingEvents && events.length > 0 && (
            <div className="divide-y divide-gray-100">
              {events.map(event => renderMobileEventItem(event))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;
