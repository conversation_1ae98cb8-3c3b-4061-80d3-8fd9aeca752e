import {
  useMutation,
  // useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import axios from 'axios';
import {
  ApiConfig,
  type PhoneCheckRequest,
  type PhoneCheckResponse,
  type ExistingPhoneOtpInitiateRequest,
  type ExistingPhoneOtpVerifyRequest,
  type NewPhoneOtpInitiateRequest,
  type NewPhoneOtpVerifyRequest,
  type AccessTokenRefreshRequest,
  type LogoutRequest,
  type PhoneOtpInitiateResponse,
  type PhoneOtpVerifyResponse,
  type AccessTokenRefreshResponse,
  type LogoutResponse,
} from '@/api/api_config';
import { authenticationStore } from 'stores/authentication_store';
import * as Crypto from 'expo-crypto';
import { encode as btoa } from 'base-64';

// Phone Check
export const usePhoneCheck = () => {
  return useMutation<PhoneCheckResponse, Error, PhoneCheckRequest>({
    mutationFn: async (payload: PhoneCheckRequest): Promise<PhoneCheckResponse> => {
      try {
        const response = await axiosInstance.request<PhoneCheckResponse>({
          url: ApiConfig.authentication.phone_check.endpoint,
          method: ApiConfig.authentication.phone_check.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: PhoneCheckResponse) => {
      // console.log(`[authentication_services] usePhoneCheck successful, response: ${JSON.stringify(data)}`);
      // Phone check doesn't usually alter auth state directly unless it returns a token
    },
    onError: (error: Error, variables: PhoneCheckRequest) => {
      console.error(`[authentication_services] usePhoneCheck error, error: ${error}, request: ${JSON.stringify(variables)}`);
    },
  });
};

// Existing Phone OTP Initiate
export const useExistingPhoneOtpInitiate = () => {
  return useMutation<PhoneOtpInitiateResponse, Error, ExistingPhoneOtpInitiateRequest>({
    mutationFn: async (payload: ExistingPhoneOtpInitiateRequest): Promise<PhoneOtpInitiateResponse> => {
      try {
        const response = await axiosInstance.request<PhoneOtpInitiateResponse>({
          url: ApiConfig.authentication.existing_phone_otp_initiate.endpoint,
          method: ApiConfig.authentication.existing_phone_otp_initiate.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: PhoneOtpInitiateResponse) => {
      // console.log(`[authentication_services] useExistingPhoneOtpInitiate successful, response: ${JSON.stringify(data)}`);
    },
    onError: (error: Error, variables: ExistingPhoneOtpInitiateRequest) => {
      console.error(`[authentication_services] useExistingPhoneOtpInitiate error, error: ${error}, request: ${JSON.stringify(variables)}`);
    },
  });
};

// Existing Phone OTP Verify
export const useExistingPhoneOtpVerify = () => {
  const queryClient = useQueryClient();
  return useMutation<PhoneOtpVerifyResponse, Error, ExistingPhoneOtpVerifyRequest>({
    mutationFn: async (payload: ExistingPhoneOtpVerifyRequest): Promise<PhoneOtpVerifyResponse> => {
      try {
        const response = await axiosInstance.request<PhoneOtpVerifyResponse>({
          url: ApiConfig.authentication.existing_phone_otp_verify.endpoint,
          method: ApiConfig.authentication.existing_phone_otp_verify.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: PhoneOtpVerifyResponse) => {
      // console.log(`[authentication_services] useExistingPhoneOtpVerify successful, response: ${JSON.stringify(data)}`);
      if (data.access_token && data.refresh_token) {
        authenticationStore.getState().setAccessToken(data.access_token);
        authenticationStore.getState().setRefreshToken(data.refresh_token);
        authenticationStore.getState().setIsAuthorized(true);
        queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      }
    },
    onError: (error: Error, variables: ExistingPhoneOtpVerifyRequest) => {
      // console.log(`[authentication_services] useExistingPhoneOtpVerify error, error: ${error}, request: ${JSON.stringify(variables)}`);
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        authenticationStore.getState().setAccessToken('');
        authenticationStore.getState().setRefreshToken('');
        authenticationStore.getState().setIsAuthorized(false);
      }
    },
  });
};

// New Phone OTP Initiate
export const useNewPhoneOtpInitiate = () => {
  return useMutation<PhoneOtpInitiateResponse, Error, NewPhoneOtpInitiateRequest>({
    mutationFn: async (payload: NewPhoneOtpInitiateRequest): Promise<PhoneOtpInitiateResponse> => {
      try {
        const response = await axiosInstance.request<PhoneOtpInitiateResponse>({
          url: ApiConfig.authentication.new_phone_otp_initiate.endpoint,
          method: ApiConfig.authentication.new_phone_otp_initiate.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: PhoneOtpInitiateResponse) => {
      // console.log(`[authentication_services] useNewPhoneOtpInitiate successful, response: ${JSON.stringify(data)}`);
    },
    onError: (error: Error, variables: NewPhoneOtpInitiateRequest) => {
      // console.log(`[authentication_services] useNewPhoneOtpInitiate error, error: ${error}, request: ${JSON.stringify(variables)}`);
    },
  });
};

// New Phone OTP Verify
export const useNewPhoneOtpVerify = () => {
  const queryClient = useQueryClient();
  return useMutation<PhoneOtpVerifyResponse, Error, NewPhoneOtpVerifyRequest>({
    mutationFn: async (payload: NewPhoneOtpVerifyRequest): Promise<PhoneOtpVerifyResponse> => {
      try {
        const response = await axiosInstance.request<PhoneOtpVerifyResponse>({
          url: ApiConfig.authentication.new_phone_otp_verify.endpoint,
          method: ApiConfig.authentication.new_phone_otp_verify.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: PhoneOtpVerifyResponse) => {
      // console.log(`[authentication_services] useNewPhoneOtpVerify successful, response: ${JSON.stringify(data)}`);
      if (data.access_token && data.refresh_token) {
        authenticationStore.getState().setAccessToken(data.access_token);
        authenticationStore.getState().setRefreshToken(data.refresh_token);
        authenticationStore.getState().setIsAuthorized(true);
        queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      }
    },
    onError: (error: Error, variables: NewPhoneOtpVerifyRequest) => {
      // console.log(`[authentication_services] useNewPhoneOtpVerify error, error: ${error}, request: ${JSON.stringify(variables)}`);
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        authenticationStore.getState().setAccessToken('');
        authenticationStore.getState().setRefreshToken('');
        authenticationStore.getState().setIsAuthorized(false);
      }
    },
  });
};

// Access Token Refresh
export const useAccessTokenRefresh = () => {
  return useMutation<AccessTokenRefreshResponse, Error, AccessTokenRefreshRequest>({
    mutationFn: async (payload: AccessTokenRefreshRequest): Promise<AccessTokenRefreshResponse> => {
      try {
        const response = await axiosInstance.request<AccessTokenRefreshResponse>({
          url: ApiConfig.authentication.access_token_refresh.endpoint,
          method: ApiConfig.authentication.access_token_refresh.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: AccessTokenRefreshResponse) => {
      // console.log(`[authentication_services] useAccessTokenRefresh successful, response: ${JSON.stringify(data)}`);
      if (data.access_token && data.refresh_token) {
        authenticationStore.getState().setAccessToken(data.access_token);
        authenticationStore.getState().setRefreshToken(data.refresh_token);
        authenticationStore.getState().setIsAuthorized(true);
      }
    },
    onError: (error: Error, variables: AccessTokenRefreshRequest) => {
      console.error(`[authentication_services] useAccessTokenRefresh error, error: ${error}, request: ${JSON.stringify(variables)}`);
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        authenticationStore.getState().setAccessToken('');
        authenticationStore.getState().setRefreshToken('');
        authenticationStore.getState().setIsAuthorized(false);
      }
    },
  });
};

// Logout
export const useLogout = () => {
  return useMutation<LogoutResponse, Error, LogoutRequest>({
    mutationFn: async (payload: LogoutRequest): Promise<LogoutResponse> => {
      try {
        const response = await axiosInstance.request<LogoutResponse>({
          url: ApiConfig.authentication.logout.endpoint,
          method: ApiConfig.authentication.logout.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: LogoutResponse) => {
      // console.log(`[authentication_services] useLogout successful, response: ${JSON.stringify(data)}`);
      authenticationStore.getState().setAccessToken('');
      authenticationStore.getState().setRefreshToken('');
      authenticationStore.getState().setIsAuthorized(false);
    },
    onError: (error: Error, variables: LogoutRequest) => {
      console.error(`[authentication_services] useLogout error, error: ${error}, request: ${JSON.stringify(variables)}`);
      authenticationStore.getState().setAccessToken('');
      authenticationStore.getState().setRefreshToken('');
      authenticationStore.getState().setIsAuthorized(false);
    },
  });
};

// Function to generate PKCE credentials
export async function generatePKCECredentials(): Promise<{ codeVerifier: string; codeChallenge: string; state: string }> {
    const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const codeVerifier = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const codeChallenge = btoa(await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, codeVerifier)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    return { codeVerifier, codeChallenge, state };
}
