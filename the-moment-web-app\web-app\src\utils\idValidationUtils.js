/**
 * Utility functions for validating and formatting ID documents
 */
import { t } from 'i18next';

/**
 * Validates a Hong Kong ID card number
 * @param {string} hkid - The HKID string to validate
 * @returns {Object} - { valid: boolean, message: string }
 */
export const validateHKID = (hkid) => {
  if (!hkid) {
    return {
      valid: false,
      message: t('common.validation.hkid')
    };
  }

  // Remove all formatting
  const cleanHkid = removeHKIDFormat(hkid);
  
  // Basic regex check (e.g., "A123456(7)" becomes "A1234567")
  const hkidPattern = /^[A-Z]{1,2}[0-9]{6}[0-9A]$/i;
  if (!hkidPattern.test(cleanHkid)) {
    return {
      valid: false,
      message: t('common.validation.hkidFormat')
    };
  }

  // Checksum validation
  try {
    const isValid = verifyHKIDChecksum(cleanHkid);
    return {
      valid: isValid,
      message: isValid ? '' : t('common.validation.hkidInvalid')
    };
  } catch (error) {
    return {
      valid: false,
      message: t('common.validation.hkidInvalid')
    };
  }
};

/**
 * Calculates the checksum for HKID validation
 * @param {string} cleanHkid - The cleaned HKID string (no formatting)
 * @returns {boolean} - Whether the HKID is valid
 */
export const verifyHKIDChecksum = (cleanHkid) => {
  let charPart = cleanHkid.slice(0, -7).toUpperCase(); // Get letter(s)
  const numPart = cleanHkid.slice(-7, -1); // Get 6 digits
  const checkDigit = cleanHkid.slice(-1).toUpperCase(); // Get check digit
  
  // Pad the character part to a fixed length of 2
  if (charPart.length === 1) {
    charPart = charPart.padStart(2, ' ');
  }

  // Convert characters to numeric values
  let sum = 0;
  const charValues = { ' ': 36 }; // Space is 36
  
  // Generate values for A-Z (A=10, B=11, etc.)
  for (let i = 0; i < 26; i++) {
    charValues[String.fromCharCode(65 + i)] = i + 10; // 'A' is 65 in ASCII
  }
  
  // Calculate sum: (X_1 × 9 + X_2 × 8 + X_3 × 7 + ... + X_8 × 1)
  sum += charValues[charPart[0]] * 9;
  sum += charValues[charPart[1]] * 8;
  
  for (let i = 0; i < 6; i++) {
    sum += parseInt(numPart[i], 10) * (7 - i);
  }
  
  // Calculate remainder when divided by 11
  const remainder = sum % 11;
  
  // Calculate check digit
  const calculatedCheckDigit = remainder === 0 ? '0' : remainder === 1 ? 'A' : String(11 - remainder);
  
  // Compare with the provided check digit
  return checkDigit === calculatedCheckDigit;
};

/**
 * Formats an HKID string to a standardized format
 * @param {string} hkid - The HKID string to format
 * @returns {Object} - { success: boolean, formattedID: string }
 */
export const formatHKID = (hkid) => {
  if (!hkid) {
    return { success: false, formattedID: '' };
  }

  // Remove existing formatting
  const cleanHkid = removeHKIDFormat(hkid);
  
  // Apply formatting based on the HKID pattern
  // Pattern: Letter(s) + 6 digits + (check digit)
  const letterPattern = /^[A-Z]{1,2}/i;
  const letterMatch = cleanHkid.match(letterPattern);
  
  if (!letterMatch) {
    return { success: false, formattedID: hkid };
  }
  
  const letters = letterMatch[0].toUpperCase();
  const rest = cleanHkid.slice(letters.length);
  const digits = rest.match(/\d{1,6}/);
  
  if (!digits) {
    return { success: false, formattedID: hkid };
  }
  
  const checkDigit = rest.slice(digits[0].length);
  
  // Build the formatted ID
  let formattedID = letters + digits[0];
  
  // Add parentheses around the check digit if it exists
  if (checkDigit.length > 0) {
    formattedID += `(${checkDigit})`;
  }
  
  return { success: true, formattedID };
};

/**
 * Removes formatting from an HKID string
 * @param {string} hkid - The HKID string to clean
 * @returns {string} - The cleaned HKID string
 */
export const removeHKIDFormat = (hkid) => {
  if (!hkid) return '';
  
  // Remove spaces, parentheses, and any other non-alphanumeric characters
  return hkid.replace(/[^a-zA-Z0-9]/g, '');
}; 