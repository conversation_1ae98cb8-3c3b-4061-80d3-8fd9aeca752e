import React, { useState, useEffect } from 'react';
import { Table, Tag, App, Space } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  // FileDoneOutlined, // No longer used, can be removed
  // IdcardOutlined, // No longer used, can be removed
  // HomeOutlined // No longer used, can be removed
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { fetchVerificationsList } from '../../hooks/useVerificationQueries';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext'; // Import context
import { groupVerificationsByUser, getUserVerificationStatus } from '../../utils/verificationUtils';
import '../../styles/UserVerificationAdmin.css';
import '../../styles/TableBorder.css';

const UserVerificationList = () => {
  const [activeFilter, setActiveFilter] = useState('pending');
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 30,
    position: ['bottomCenter'],
    showTotal: total => t('common.totalItems', { total: total })
  });
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { message } = App.useApp();
  const { currentOrganization } = useOrganization(); // Get current organization

  // State for managing data, loading, and error manually
  const [verifications, setVerifications] = useState([]);
  const [groupedVerifications, setGroupedVerifications] = useState([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Destructure pagination values for useEffect dependency array
  const { current: currentPage, pageSize } = pagination;

  useEffect(() => {
    const loadVerifications = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const orgIdToUse = currentOrganization?.id;

        const result = await fetchVerificationsList(
          orgIdToUse,
          activeFilter,
          currentPage,
          pageSize
        );

        // Store original verifications data for reference
        const verificationsArray = Array.isArray(result.verifications) ? result.verifications : [];
        setVerifications(verificationsArray);
        
        // Group verifications by user_id for display
        const grouped = groupVerificationsByUser(verificationsArray);
        setGroupedVerifications(grouped);
        
        setTotal(result.total || 0);
      } catch (err) {
        setError(err);
        message.error(t('messages.fetchError') + (err.message ? `: ${err.message}` : ''));
        console.error('Fetch Error in UserVerificationList:', err);
      } finally {
        setIsLoading(false);
      }
    };

    // Trigger fetch when component mounts or dependencies change
    // Important: ensure currentOrganization is stable or orgIdToUse is correctly handled
    // if currentOrganization can be null/undefined initially.
    // If currentOrganization can be undefined and API expects 'all' implicitly,
    // ensure fetchVerificationsList handles undefined orgId correctly (which it does).
    loadVerifications();

  }, [currentOrganization?.id, activeFilter, currentPage, pageSize, message, t]); // Updated dependencies

  const handleTableChange = (newPagination) => {
    setPagination(prev => ({
      ...prev,
      current: newPagination.current
    }));
  };

  // Handle filter button click
  const handleFilterChange = (status) => {
    setActiveFilter(status);
    setPagination(prev => ({
      ...prev,
      current: 1 // Reset to first page when filter changes
    }));
  };

  const getStatusTag = (status, isInteractive = false) => {
    // API might return 'pending_review', UI shows 'pending'
    const displayStatus = status?.toLowerCase() === 'pending_review' ? 'pending' : status?.toLowerCase();

    const statusConfig = {
      pending: {
        color: 'default',
        icon: <ClockCircleOutlined />,
      },
      approved: {
        color: 'success',
        icon: <CheckCircleOutlined />,
      },
      rejected: {
        color: 'error',
        icon: <CloseCircleOutlined />,
      }
    };

    const config = statusConfig[displayStatus] || statusConfig.pending;
    const tagProps = {
      color: config.color,
      icon: config.icon,
    };

    if (isInteractive) {
      tagProps.onClick = (e) => {
        e.stopPropagation(); // Prevent row click if the tag is clicked
        handleFilterChange(displayStatus || 'pending');
      };
      tagProps.style = { ...tagProps.style, cursor: 'pointer' };
    }

    return (
      <Tag {...tagProps}>
        {t(`common.${displayStatus || 'pending'}`)}
      </Tag>
    );
  };

  // Document details columns for the nested table
  const documentColumns = [
    {
      title: t('userVerificationAdmin.table.columns.documents.type'),
      dataIndex: 'verification_type',
      key: 'type',
      render: (type) => type ? t(`documents.${type.toLowerCase()}`) || type : '-'
    },
    {
      title: t('userVerificationAdmin.table.columns.documents.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status) // Re-use status tag for consistency
    },
    {
      title: t('userVerificationAdmin.table.columns.documents.submissionTime'),
      dataIndex: 'submitted_at', 
      key: 'submitted_at',
      render: (time) => time ? moment(time).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: t('userVerificationAdmin.table.columns.documents.reviewedAt'),
      dataIndex: 'reviewed_at',
      key: 'reviewed_at',
      render: (time) => {
        // Check for null or "0001-01-01T00:00:00Z" which represents null/unset value
        if (!time || time === "0001-01-01T00:00:00Z") {
          return '-';
        }
        return moment(time).format('YYYY-MM-DD HH:mm');
      }
    },
    {
      title: t('userVerificationAdmin.table.columns.documents.reviewerName'),
      dataIndex: 'reviewer_display_name',
      key: 'reviewer_display_name',
      render: (name) => name || '-'
    }
  ];

  // Expanded row render function to show documents for a user
  const expandedRowRender = (record) => {
    if (!record.documents || record.documents.length === 0) {
      return <p>{t('userVerificationAdmin.noDocumentDetailsInList')}</p>;
    }
    
    return (
      <Table
        columns={documentColumns}
        dataSource={record.documents}
        pagination={false}
        size="small"
        rowKey="request_id"
      />
    );
  };

  // Handle row expansion
  const handleRowExpand = (expanded, record) => {
    setExpandedRowKeys(expanded ? [record.user_id] : []);
  };

  // Handle row click for expansion
  const handleRowClick = (record) => {
    // If user has only one document, directly open details instead of expanding
    if (record.documents?.length === 1) {
      const doc = record.documents[0];
      const documentData = [{
        reqId: doc.request_id,
        type: doc.verification_type,
        status: doc.status || 'pending'
      }];
      
      // Navigate directly to ProofDetails with state instead of URL params
      navigate('/verification/user', {
        state: { documentData }
      });
      return;
    }
    
    // For multiple documents, toggle expansion as before
    const isExpanded = expandedRowKeys.includes(record.user_id);
    setExpandedRowKeys(isExpanded ? [] : [record.user_id]);
  };

  // Navigate to proof details with user's verifications
  const handleViewAllDetails = (e, record) => {
    e.stopPropagation(); // Prevent row expansion
  
      // Create data with all document reqIds, types, and statuses
  const documentData = record.documents.map(doc => ({
    reqId: doc.request_id,
    type: doc.verification_type,
    status: doc.status || 'pending'
  }));
    
    // Navigate to ProofDetails with state instead of URL params
    navigate('/verification/user', {
      state: { documentData }
    });
  };

  // Get document type display for the main table
  const getDocumentTypeDisplay = (record) => {
    if (!record.documents || record.documents.length === 0) {
      return '-';
    }
    
    // If only one document, show its type
    if (record.documents.length === 1) {
      const docType = record.documents[0].verification_type;
      return docType ? t(`documents.${docType.toLowerCase()}`) || docType : '-';
    }
    
    // If multiple documents, show "Multiple documents"
    return t('userVerificationAdmin.table.columns.multipleDocuments');
  };

  const columns = [
    {
      title: t('userVerificationAdmin.table.columns.name'),
      dataIndex: 'user_display_name',
      key: 'user_display_name',
      width: 150,
    },
    // Removing Phone Number column as it's not in the provided API response sample
    // {
    //   title: t('userVerificationAdmin.table.columns.phoneNumber'),
    //   dataIndex: 'applicant_phone', 
    //   key: 'applicant_phone',
    //   width: 150,
    // },
    {
      title: t('userVerificationAdmin.table.columns.submissionTime'),
      dataIndex: 'submitted_at',
      key: 'submitted_at',
      width: 180,
      render: (time) => time ? moment(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: t('userVerificationAdmin.table.columns.verification_type'),
      key: 'verification_type',
      width: 150,
      render: (_, record) => {
        if (!record.documents || record.documents.length === 0) {
          return '-';
        }
        if (record.documents.length === 1) {
          const docType = record.documents[0].verification_type;
          return docType ? t(`documents.${docType.toLowerCase()}`) || docType : '-';
        }
        return t('userVerificationAdmin.table.columns.multipleDocuments');
      },
    },
    {
      title: t('userVerificationAdmin.table.columns.status'),
      key: 'status',
      width: 150,
      render: (_, record) => {
        const status = getUserVerificationStatus(record.documents);
        return getStatusTag(status, true);
      },
    },
    {
      title: t('userVerificationAdmin.table.columns.documentCount'),
      key: 'documentCount',
      width: 150,
      render: (_, record) => record.documents?.length || 0
    },
    {
      title: t('userVerificationAdmin.table.columns.actions'),
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          <button
            onClick={(e) => {
              e.stopPropagation();
              const documentData = record.documents.map(doc => ({
                reqId: doc.request_id,
                type: doc.verification_type,
                status: doc.status || 'pending'
              }));
              navigate('/verification/user', { state: { documentData } });
            }}
            className="text-blue-600 hover:text-blue-800 font-medium text-sm"
          >
            {t('common.viewAllDetails')}
          </button>
        </Space>
      ),
    }
  ];

  const FilterButtons = () => {
    const buttonStyles = {
      pending: {
        active: 'border-gray-500 text-gray-600 bg-gray-100',
        default: 'border-gray-200 text-gray-600 hover:border-gray-500 hover:text-gray-600'
      },
      approved: {
        active: 'border-green-500 text-green-600 bg-green-50',
        default: 'border-gray-200 text-gray-600 hover:border-green-500 hover:text-green-600'
      },
      rejected: {
        active: 'border-red-500 text-red-600 bg-red-50',
        default: 'border-gray-200 text-gray-600 hover:border-red-500 hover:text-red-600'
      }
    };

    return (
      <div className="grid grid-cols-3 sm:flex sm:flex-wrap gap-2 mb-6">
        {Object.entries({
          pending: { icon: <ClockCircleOutlined />, label: t('userVerificationAdmin.buttons.pending') },
          approved: { icon: <CheckCircleOutlined />, label: t('userVerificationAdmin.buttons.approved') },
          rejected: { icon: <CloseCircleOutlined />, label: t('userVerificationAdmin.buttons.rejected') }
        }).map(([status, { icon, label }]) => (
          <button
            key={status}
            onClick={() => handleFilterChange(status)}
            className={`
              px-2 sm:px-4 py-2 
              border rounded-md
              font-medium text-xs sm:text-sm
              transition-all duration-200
              flex items-center gap-1 sm:gap-2
              justify-center
              ${activeFilter === status ? buttonStyles[status].active : buttonStyles[status].default}
            `}
          >
            <div className="flex items-center">
              <span className="text-lg sm:text-base">{icon}</span>
              <span className="inline ml-1">{label}</span>
            </div>
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen p-2 lg:p-6">
      <div className="max-w-7xl mx-auto">
        <div className="border rounded-lg p-4">
          <FilterButtons />
          <div className="border rounded-lg overflow-hidden">
            <Table
              columns={columns}
              dataSource={groupedVerifications}
              loading={isLoading}
              rowKey="user_id"
              pagination={{
                ...pagination,
                total: total
              }}
              onChange={handleTableChange}
              scroll={{ x: 800 }}
              expandable={{
                expandedRowRender,
                expandedRowKeys,
                onExpand: handleRowExpand,
                rowExpandable: record => record.documents?.length > 1,
              }}
              onRow={(record) => ({
                onClick: () => handleRowClick(record),
                style: { cursor: 'pointer' }
              })}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserVerificationList;
