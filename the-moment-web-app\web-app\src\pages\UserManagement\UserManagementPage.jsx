/*
 * UserManagementPage - 用户管理页面
 * 
 * 后端API已完成改进：
 * 1. 支持后端过滤：使用role参数区分管理员和普通用户
 * 2. 真正的分页：支持page, limit参数
 * 3. 返回总数信息：total_count
 * 
 * 前端改进已完成：
 * - 每次切换tab时调用不同的API参数
 * - 实现真正的后端分页
 * - 移除前端过滤逻辑
 * - 优化数据加载性能
 * - 分离用户表和管理员表的显示配置
 */

import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import {
    closestCenter,
    DndContext,
    DragOverlay,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import {
    arrayMove,
    horizontalListSortingStrategy,
    SortableContext,
    useSortable,
} from '@dnd-kit/sortable';
import { Table, Button, Radio, Select, DatePicker, Typography, App, Spin, Space, Switch, Tabs } from 'antd';
import {
    DownloadOutlined,
    InfoCircleOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
} from '@ant-design/icons';
import '../../styles/ReportPage.css';
import CustomizedDropdown from '../../components/CustomizedDropdown';
import { useTranslation } from 'react-i18next';
import { formatSimpleDateTime, formatToYYYYMMDD } from '../../utils/dateFormatter';
import { useOrganization } from '../../contexts/OrganizationContext';
import { useNavigate } from 'react-router-dom';
import { exportData } from '../../utils/exportUtils';
import { adminService } from '../../services/adminService';
import { verificationService } from '../../services/verificationService';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// Create a context for drag state
const DragIndexContext = createContext({
    active: -1,
    over: -1,
});

// Define styles for draggable elements
const dragActiveStyle = (dragState, id) => {
    const { active, over, direction } = dragState;

    let style = {};
    if (active && active === id) {
        style = {
            backgroundColor: 'gray',
            opacity: 0.5,
        };
    } else if (over && id === over && active !== over) {
        style =
            direction === 'right'
                ? {
                    borderRight: '1px dashed gray',
                }
                : {
                    borderLeft: '1px dashed gray',
                };
    }
    return style;
};

// Table header cell component with drag-and-drop functionality
const TableHeaderCell = (props) => {
    const dragState = useContext(DragIndexContext);
    const { attributes, listeners, setNodeRef, isDragging } = useSortable({
        id: props.id,
    });

    // Return plain th for expand control column and actions column
    if (props.className?.includes('ant-table-row-expand-icon-cell') ||
        props.id === 'actions') {
        return <th {...props} />;
    }

    const style = {
        ...props.style,
        cursor: 'move',
        ...(isDragging
            ? {
                position: 'relative',
                zIndex: 999,
                userSelect: 'none',
            }
            : {}),
        ...dragActiveStyle(dragState, props.id),
    };
    return <th {...props} ref={setNodeRef} style={style} {...attributes} {...listeners} />;
};

const UserManagementPage = () => {
    const { t } = useTranslation();
    const { message } = App.useApp();
    const navigate = useNavigate();
    const { currentOrganization } = useOrganization();

    // State variables
    const [exportFormat, setExportFormat] = useState('csv');

    // Separate checked lists for different table types
    const [userCheckedList, setUserCheckedList] = useState([
        'displayName',
        'phone',
        'createdAt',
        'verificationCount',
        'actions'
    ]);
    const [adminCheckedList, setAdminCheckedList] = useState([
        'displayName',
        'email',
        'createdAt'
    ]);

    // Add a list of fixed columns that should always be visible and not toggleable in dropdown
    const [fixedColumns] = useState(['actions']);
    
    // Separate data state for users and admins
    const [userData, setUserData] = useState({
        users: [],
        total: 0,
        page: 1,
        limit: 50
    });
    const [adminData, setAdminData] = useState({
        users: [],
        total: 0,
        page: 1,
        limit: 50
    });
    
    const [loading, setLoading] = useState(false);
    const [exportLoading, setExportLoading] = useState(false);
    const [expandedRowKeys, setExpandedRowKeys] = useState([]);
    const [activeTab, setActiveTab] = useState('users'); // 'users' or 'admins'
    const [allUserVerifications, setAllUserVerifications] = useState({}); // Store all verifications by user ID
    
    // Separate pagination state for users and admins
    const [userPagination, setUserPagination] = useState({
        current: 1,
        pageSize: 50,
        total: 0
    });
    const [adminPagination, setAdminPagination] = useState({
        current: 1,
        pageSize: 50,
        total: 0
    });

    // State for drag-and-drop indices - separate for each table type
    const [userDragIndex, setUserDragIndex] = useState({
        active: -1,
        over: -1,
        direction: null
    });
    const [adminDragIndex, setAdminDragIndex] = useState({
        active: -1,
        over: -1,
        direction: null
    });



    // Define Users Table Columns (no email, has phone and verification features)
    const userBaseColumns = useMemo(
        () => [
            {
                title: t('userManagement.columns.displayName'),
                dataIndex: 'display_name',
                key: 'displayName',
                width: 150,
                onHeaderCell: () => ({ id: 'displayName' }),
                onCell: () => ({ id: 'displayName' }),
                render: (name) => name
            },
            {
                title: t('userManagement.columns.phone'),
                dataIndex: 'phone',
                key: 'phone',
                width: 150,
                onHeaderCell: () => ({ id: 'phone' }),
                onCell: () => ({ id: 'phone' }),
                render: (phone) => phone || '-',
            },
            {
                title: t('userManagement.columns.createdAt'),
                dataIndex: 'created_at',
                key: 'createdAt',
                width: 180,
                onHeaderCell: () => ({ id: 'createdAt' }),
                onCell: () => ({ id: 'createdAt' }),
                render: (date) => date ? formatSimpleDateTime(date) : '-',
            },
            {
                title: t('userManagement.columns.verificationCount'),
                key: 'verificationCount',
                width: 150,
                onHeaderCell: () => ({ id: 'verificationCount' }),
                onCell: () => ({ id: 'verificationCount' }),
                render: (_, record) => {
                    // Get verification count from record or cached data
                    const userVerifications = record.verifications || allUserVerifications[record.id] || [];
                    const count = userVerifications.length;
                    return count === 0 ?
                        <span className="text-gray-400">0</span> :
                        <span className="text-blue-600 font-medium">{count}</span>;
                }
            },
            {
                title: t('userManagement.columns.actions'),
                key: 'actions',
                width: 150,
                fixed: 'right',
                onHeaderCell: () => ({ id: 'actions' }),
                onCell: () => ({ id: 'actions' }),
                render: (_, record) => {
                    // Get verifications from record or cached data
                    const userVerifications = record.verifications || allUserVerifications[record.id] || [];
                    const hasVerifications = userVerifications.length > 0;
                    
                    return (
                        <Space size="middle">
                            {hasVerifications ? (
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        // Create verification data for navigation
                                        const verificationData = userVerifications.map(verification => ({
                                            reqId: verification.request_id || verification.id,
                                            type: verification.verification_type,
                                            status: verification.status || 'pending'
                                        }));
                                        navigate(`/verification`, {
                                            state: { verificationData }
                                        });
                                    }}
                                    className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                                >
                                    {t('common.viewAllDetails')}
                                </button>
                            ) : (
                                <span className="text-gray-400 text-sm">
                                    {t('userManagement.noVerifications') || 'No verifications'}
                                </span>
                            )}
                        </Space>
                    );
                },
            }
        ],
        [t, allUserVerifications, navigate]
    );

    // Define Admin Table Columns (has email, no phone, no verification features, no actions)
    const adminBaseColumns = useMemo(
        () => [
            {
                title: t('userManagement.columns.displayName'),
                dataIndex: 'display_name',
                key: 'displayName',
                width: 150,
                onHeaderCell: () => ({ id: 'displayName' }),
                onCell: () => ({ id: 'displayName' }),
                render: (name) => name
            },
            {
                title: t('userManagement.columns.email'),
                dataIndex: 'email',
                key: 'email',
                width: 200,
                onHeaderCell: () => ({ id: 'email' }),
                onCell: () => ({ id: 'email' }),
                render: (email) => email || '-',
            },
            {
                title: t('userManagement.columns.createdAt'),
                dataIndex: 'created_at',
                key: 'createdAt',
                width: 180,
                onHeaderCell: () => ({ id: 'createdAt' }),
                onCell: () => ({ id: 'createdAt' }),
                render: (date) => date ? formatSimpleDateTime(date) : '-',
            }
        ],
        [t]
    );

    // Separate column states for each table type
    const [userColumns, setUserColumns] = useState(userBaseColumns);
    const [adminColumns, setAdminColumns] = useState(adminBaseColumns);

    // Get current table configuration based on active tab
    const getCurrentTableConfig = () => {
        if (activeTab === 'admins') {
            return {
                columns: adminColumns,
                setColumns: setAdminColumns,
                baseColumns: adminBaseColumns,
                checkedList: adminCheckedList,
                setCheckedList: setAdminCheckedList,
                dragIndex: adminDragIndex,
                setDragIndex: setAdminDragIndex,
                hasExpandableRows: false,
                hasVerifications: false,
                data: adminData,
                pagination: adminPagination
            };
        } else {
            return {
                columns: userColumns,
                setColumns: setUserColumns,
                baseColumns: userBaseColumns,
                checkedList: userCheckedList,
                setCheckedList: setUserCheckedList,
                dragIndex: userDragIndex,
                setDragIndex: setUserDragIndex,
                hasExpandableRows: true,
                hasVerifications: true,
                data: userData,
                pagination: userPagination
            };
        }
    };

    // Get current columns with visibility based on active tab
    const columnsWithVisibility = useMemo(() => {
        const config = getCurrentTableConfig();
        return config.columns.map((item) => ({
            ...item,
            hidden: !config.checkedList.includes(item.key),
        }));
    }, [userColumns, adminColumns, userCheckedList, adminCheckedList, activeTab]);

    // Load data for specific role (user or admin)
    const loadRoleData = useCallback(async (role, page = 1, pageSize = 50) => {
        try {
            // Prepare API parameters
            const apiParams = {
                page: page,
                limit: pageSize,
                role: role
            };

            // Add organization filter if currentOrganization is available
            if (currentOrganization?.id) {
                apiParams.organization_id = currentOrganization.id;
            }

            const response = await adminService.listUsers(apiParams);

            // Handle response structure based on actual API response format
            let users = [];
            let total = 0;

            // Extract users array from response - based on backend API structure
            if (response && response.users && Array.isArray(response.users)) {
                users = response.users.map(user => ({
                    // Map API response fields to expected component fields
                    id: user.id,
                    display_name: user.display_name || '',
                    email: user.email || null,
                    phone: user.phone || null,
                    created_at: user.created_at,
                    updated_at: user.updated_at,
                    role: user.role, // Use role instead of is_staff
                    // Keep original user data for potential future use
                    ...user
                }));
                total = response.total_count || response.total || users.length;
            } else if (response && response.data && response.data.users && Array.isArray(response.data.users)) {
                users = response.data.users.map(user => ({
                    id: user.id,
                    display_name: user.display_name || '',
                    email: user.email || null,
                    phone: user.phone || null,
                    created_at: user.created_at,
                    updated_at: user.updated_at,
                    role: user.role,
                    ...user
                }));
                total = response.data.total_count || response.data.total || users.length;
            }

            // Update appropriate state based on role
            if (role === 'admin') {
                setAdminData({
                    users: users,
                    total: total,
                    page: page,
                    limit: pageSize
                });

                setAdminPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize: pageSize,
                    total: total
                }));
            } else {
                // For users, load their verification data immediately
                const usersWithVerifications = await loadVerificationsForUsers(users);
                
                setUserData({
                    users: usersWithVerifications,
                    total: total,
                    page: page,
                    limit: pageSize
                });

                setUserPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize: pageSize,
                    total: total
                }));
            }

            return { users, total };

        } catch (error) {
            console.error(`Error loading ${role} data:`, error);
            message.error(t('messages.fetchError'));
            
            const emptyData = {
                users: [],
                total: 0,
                page: page,
                limit: pageSize
            };

            // Update appropriate state based on role
            if (role === 'admin') {
                setAdminData(emptyData);
                setAdminPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize: pageSize,
                    total: 0
                }));
            } else {
                setUserData(emptyData);
                setUserPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize: pageSize,
                    total: 0
                }));
            }

            return { users: [], total: 0 };
        }
    }, [currentOrganization?.id, message, t]);

    // Load both user and admin data simultaneously
    const loadAllData = useCallback(async () => {
        try {
            setLoading(true);
            
            // Load both user and admin data in parallel
            await Promise.all([
                loadRoleData('user', 1, 50),
                loadRoleData('admin', 1, 50)
            ]);
        } catch (error) {
            console.error('Error loading all data:', error);
        } finally {
            setLoading(false);
        }
    }, [loadRoleData]);

    // Load verifications for a list of users
    const loadVerificationsForUsers = useCallback(async (users) => {
        try {
            // Load verifications for all users in parallel
            const verificationPromises = users.map(async (user) => {
                try {
                    const { verifications } = await verificationService.getUserVerifications(user.id);
                    
                    // Store in global verification cache
                    setAllUserVerifications(prev => ({
                        ...prev,
                        [user.id]: verifications || []
                    }));
                    
                    return {
                        ...user,
                        verifications: verifications || []
                    };
                } catch (error) {
                    console.error(`Error loading verifications for user ${user.id}:`, error);
                    
                    // Store empty array for failed requests
                    setAllUserVerifications(prev => ({
                        ...prev,
                        [user.id]: []
                    }));
                    
                    return {
                        ...user,
                        verifications: []
                    };
                }
            });

            const usersWithVerifications = await Promise.all(verificationPromises);
            return usersWithVerifications;
        } catch (error) {
            console.error('Error loading verifications for users:', error);
            // Return users with empty verifications if there's an error
            return users.map(user => ({
                ...user,
                verifications: []
            }));
        }
    }, []);

    // Load data for current tab's pagination change
    const loadCurrentTabData = useCallback(async (page, pageSize) => {
        try {
            setLoading(true);
            if (activeTab === 'admins') {
                await loadRoleData('admin', page, pageSize);
            } else {
                await loadRoleData('user', page, pageSize);
            }
        } catch (error) {
            console.error('Error loading current tab data:', error);
        } finally {
            setLoading(false);
        }
    }, [activeTab, loadRoleData]);

    // Load all data when component mounts
    useEffect(() => {
        loadAllData();
    }, [loadAllData]);

    // Reset expanded rows when switching tabs
    useEffect(() => {
        setExpandedRowKeys([]);
        // Note: We keep allUserVerifications cache across tab switches for better performance
    }, [activeTab]);

    // Handle pagination change
    const handleTableChange = (pagination) => {
        loadCurrentTabData(pagination.current, pagination.pageSize);
    };

    // Effect to update columns when baseColumns (translations) change for both table types
    useEffect(() => {
        setUserColumns(currentColumns =>
            currentColumns.map(colInOrder => {
                const newBaseColDef = userBaseColumns.find(bc => bc.key === colInOrder.key);
                if (newBaseColDef) {
                    return { ...newBaseColDef };
                }
                return colInOrder;
            })
        );

        setAdminColumns(currentColumns =>
            currentColumns.map(colInOrder => {
                const newBaseColDef = adminBaseColumns.find(bc => bc.key === colInOrder.key);
                if (newBaseColDef) {
                    return { ...newBaseColDef };
                }
                return colInOrder;
            })
        );
    }, [userBaseColumns, adminBaseColumns]);

    // Sensors for drag-and-drop
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 1,
            },
        })
    );

    // Handler for when drag ends
    const onDragEnd = ({ active, over }) => {
        if (active.id !== over?.id) {
            const config = getCurrentTableConfig();
            config.setColumns((prevColumns) => {
                const activeIndex = prevColumns.findIndex((i) => i.key === active.id);
                const overIndex = prevColumns.findIndex((i) => i.key === over.id);
                return arrayMove(prevColumns, activeIndex, overIndex);
            });
        }
        const config = getCurrentTableConfig();
        config.setDragIndex({
            active: -1,
            over: -1,
        });
    };

    // Handler for when an item is dragged over another
    const onDragOver = ({ active, over }) => {
        const config = getCurrentTableConfig();
        const activeIndex = config.columns.findIndex((i) => i.key === active.id);
        const overIndex = config.columns.findIndex((i) => i.key === over?.id);
        config.setDragIndex({
            active: active.id,
            over: over?.id,
            direction: overIndex > activeIndex ? 'right' : 'left',
        });
    };

    // Only export columns that are checked AND not fixed (like 'actions')
    const handleExport = async () => {
        try {
            setExportLoading(true);

            // Get current table configuration
            const config = getCurrentTableConfig();

            // Get all data for export (not just current page)
            const exportParams = {
                page: 1,
                limit: 1000, // Large limit to get all data for export
            };

            if (activeTab === 'admins') {
                exportParams.role = 'admin';
            } else {
                exportParams.role = 'user';
            }

            if (currentOrganization?.id) {
                exportParams.organization_id = currentOrganization.id;
            }

            const exportResponse = await adminService.listUsers(exportParams);
            const allUsersForExport = exportResponse?.users || [];

            // Get filtered data based on current tab with different field mapping
            const dataToExport = allUsersForExport.map(user => {
                // For users, get verification data first to ensure consistency
                const userVerifications = activeTab === 'users' ? (user.verifications || allUserVerifications[user.id] || []) : [];
                
                // Base user data (parent fields) - different for admin vs user tables
                const baseData = {};

                if (activeTab === 'admins') {
                    // Admin table fields
                    baseData.display_name = user.display_name || '';
                    baseData.email = user.email || '-';
                    baseData.created_at = user.created_at ? formatSimpleDateTime(user.created_at) : '-';
                } else {
                    // User table fields
                    baseData.display_name = user.display_name || '';
                    baseData.phone = user.phone || '-';
                    baseData.created_at = user.created_at ? formatSimpleDateTime(user.created_at) : '-';
                    // Use the userVerifications calculated above
                    baseData.verificationCount = userVerifications.length;
                }

                // Only include selected columns for the parent data
                // Filter out fixedColumns (like 'actions') from export - only applies to users table
                const exportableCheckedList = activeTab === 'users'
                    ? config.checkedList.filter(col => !fixedColumns.includes(col))
                    : config.checkedList;
                const filteredParentData = {};
                exportableCheckedList.forEach(key => {
                    if (key === 'displayName') filteredParentData.display_name = baseData.display_name;
                    if (key === 'email' && activeTab === 'admins') filteredParentData.email = baseData.email;
                    if (key === 'phone' && activeTab === 'users') filteredParentData.phone = baseData.phone;
                    if (key === 'createdAt') filteredParentData.created_at = baseData.created_at;
                    if (key === 'verificationCount' && activeTab === 'users') filteredParentData.verificationCount = baseData.verificationCount;
                });

                // Include verifications as a sub-array only for user table
                if (activeTab === 'users') {
                    if (userVerifications.length > 0) {
                        filteredParentData.verifications = userVerifications.map(verification => ({
                            type: verification.verification_type ? t(`documents.${verification.verification_type.toLowerCase()}`) || verification.verification_type : '-',
                            status: verification.status ? t(`common.${verification.status.toLowerCase() === 'pending_review' ? 'pending' : verification.status.toLowerCase()}`) : '-',
                            submitted_at: verification.submitted_at ? formatSimpleDateTime(verification.submitted_at) : '-',
                            reviewed_at: verification.reviewed_at && !verification.reviewed_at.includes('0001-01-01') ? formatSimpleDateTime(verification.reviewed_at) : '-'
                        }));
                    } else {
                        filteredParentData.verifications = [];
                    }
                }

                return filteredParentData;
            });

            // Generate filename with tab type
            const tabType = activeTab === 'admins' ? 'admins' : 'users';
            const filename = `${tabType}-${currentOrganization?.name || 'organization'}-${formatToYYYYMMDD(new Date())}`;

            // Create column header translations map
            const headerMap = {
                // Parent column headers - common
                display_name: t('userManagement.columns.displayName'),
                created_at: t('userManagement.columns.createdAt'),

                // Parent column headers - specific to table type
                ...(activeTab === 'admins' ? {
                    email: t('userManagement.columns.email'),
                } : {
                    phone: t('userManagement.columns.phone'),
                    verificationCount: t('userManagement.columns.verificationCount'),
                }),

                // Child column headers (verification details) - only for user table
                ...(activeTab === 'users' ? {
                    type: t('userManagement.verificationDetails.type'),
                    status: t('userManagement.verificationDetails.status'),
                    submitted_at: t('userManagement.verificationDetails.submittedAt'),
                    reviewed_at: t('userManagement.verificationDetails.reviewedAt'),
                } : {})
            };

            // Define parent fields based on checkedList and table type
            const parentFieldsMap = activeTab === 'admins' ? {
                'displayName': 'display_name',
                'email': 'email',
                'createdAt': 'created_at'
            } : {
                'displayName': 'display_name',
                'phone': 'phone',
                'createdAt': 'created_at',
                'verificationCount': 'verificationCount'
            };

            // Only include fields that are checked in the dropdown AND not fixed (only for users table)
            const exportableCheckedList = activeTab === 'users'
                ? config.checkedList.filter(col => !fixedColumns.includes(col))
                : config.checkedList;
            const parentFields = exportableCheckedList
                .map(key => parentFieldsMap[key])
                .filter(Boolean);

            const childFields = activeTab === 'users' ? ['type', 'status', 'submitted_at', 'reviewed_at'] : [];

            // Export options with translations and hierarchical structure
            const exportOptions = {
                title: t('userManagement.export.title'),
                orientation: 'landscape',
                headerMap: headerMap,
                exportDateLabel: t('common.exportDate') || 'Export date:',
                preserveHierarchy: activeTab === 'users', // Only for user table
                parentFields: parentFields,
                childFields: childFields,
                useTableRendering: true,
                // Add column ordering information
                columnOrder: config.columns,
                checkedList: config.checkedList,
                fixedColumns: fixedColumns,
                parentFieldsMap: parentFieldsMap
            };

            // For PNG export using screenshot approach (fallback)
            const tableElement = exportFormat === 'png' ? document.querySelector('.ant-table-container') : null;

            // Export based on format
            await exportData(dataToExport, exportFormat, filename, exportOptions, tableElement);

            message.success(t('userManagement.export.success'));
        } catch (error) {
            console.error('Export error:', error);
            message.error(t('userManagement.export.error'));
        } finally {
            setExportLoading(false);
        }
    };

    // Handle row expansion (only for users table)
    const handleRowExpand = (expanded, record) => {
        if (expanded) {
            setExpandedRowKeys([...expandedRowKeys, record.id]);
        } else {
            setExpandedRowKeys(expandedRowKeys.filter(key => key !== record.id));
        }
    };

    // Handle row click (only for users table)
    const handleRowClick = (record) => {
        // Only handle click if row is expandable and we're on users tab
        if (activeTab === 'users') {
            const userVerifications = record.verifications || allUserVerifications[record.id] || [];
            if (userVerifications.length > 0) {
                const isExpanded = expandedRowKeys.includes(record.id);
                if (isExpanded) {
                    setExpandedRowKeys(expandedRowKeys.filter(key => key !== record.id));
                } else {
                    setExpandedRowKeys([...expandedRowKeys, record.id]);
                }
            }
        }
    };

    // Column definitions for expanded row (only for users table)
    const verificationColumns = [
        {
            title: t('userManagement.verificationDetails.type'),
            dataIndex: 'verification_type',
            key: 'verification_type',
            render: (type) => type ? t(`documents.${type.toLowerCase()}`) || type : '-'
        },
        {
            title: t('userManagement.verificationDetails.status'),
            dataIndex: 'status',
            key: 'status',
            render: (status) => {
                const displayStatus = status?.toLowerCase() === 'pending_review' ? 'pending' : status?.toLowerCase();
                return t(`common.${displayStatus || 'pending'}`);
            }
        },
        {
            title: t('userManagement.verificationDetails.submittedAt'),
            dataIndex: 'submitted_at',
            key: 'submitted_at',
            render: (time) => time ? formatSimpleDateTime(time) : '-'
        },
        {
            title: t('userManagement.verificationDetails.reviewedAt'),
            dataIndex: 'reviewed_at',
            key: 'reviewed_at',
            render: (time) => {
                if (!time || time.includes('0001-01-01')) {
                    return '-';
                }
                return formatSimpleDateTime(time);
            }
        }
    ];

    // Get filtered users based on current tab
    const filteredUsers = useMemo(() => {
        if (activeTab === 'admins') {
            return adminData.users;
        } else {
            return userData.users;
        }
    }, [activeTab, userData.users, adminData.users]);

    // Get counts for each tab from current data
    const userCounts = useMemo(() => {
        return { 
            adminCount: adminData.total, 
            regularUserCount: userData.total 
        };
    }, [userData.total, adminData.total]);

    // Render expanded row content (only for users table)
    const expandedRowRender = (record) => {
        const userVerifications = record.verifications || allUserVerifications[record.id] || [];
        
        return (
            <Table
                columns={verificationColumns}
                dataSource={userVerifications}
                pagination={false}
                size="small"
                rowKey={(record) => record.request_id || record.id}
            />
        );
    };

    // Create tab extra content (header actions)
    const tabExtraContent = {
        right: (
            <div className="flex items-center gap-3">
                <Radio.Group
                    options={[
                        { label: 'CSV', value: 'csv' },
                        { label: 'PDF', value: 'pdf' },
                        { label: 'PNG', value: 'png' },
                    ]}
                    onChange={(e) => setExportFormat(e.target.value)}
                    value={exportFormat}
                    disabled={exportLoading}
                    className="mr-1"
                />
                <Button
                    type="primary"
                    onClick={handleExport}
                    loading={exportLoading}
                    disabled={filteredUsers.length === 0}
                >
                    <DownloadOutlined /> {t('userManagement.export.title')}
                </Button>
                <CustomizedDropdown
                    allColumns={getCurrentTableConfig().baseColumns}
                    checkedList={getCurrentTableConfig().checkedList}
                    setCheckedList={getCurrentTableConfig().setCheckedList}
                    disabled={loading}
                    fixedColumns={activeTab === 'users' ? fixedColumns : []}
                />
            </div>
        )
    };

    // Render table component with pagination and different configurations
    const renderTable = () => {
        const config = getCurrentTableConfig();

        return (
            <DndContext
                sensors={sensors}
                modifiers={[restrictToHorizontalAxis]}
                onDragEnd={onDragEnd}
                onDragOver={onDragOver}
                collisionDetection={closestCenter}
            >
                <SortableContext
                    items={columnsWithVisibility.filter(col => !col.hidden).map((i) => i.key)}
                    strategy={horizontalListSortingStrategy}
                >
                    <DragIndexContext.Provider value={config.dragIndex}>
                        <div className="border rounded-lg overflow-hidden" style={{ minWidth: '100%' }}>
                            <Table
                                rowKey="id"
                                columns={columnsWithVisibility.filter(col => !col.hidden)}
                                dataSource={filteredUsers}
                                loading={loading}
                                components={{
                                    header: {
                                        cell: TableHeaderCell,
                                    }
                                }}
                                scroll={{
                                    x: columnsWithVisibility.filter(col => !col.hidden)
                                        .reduce((total, col) => total + (col.width || 150), 0)
                                }}
                                pagination={{
                                    current: config.pagination.current,
                                    pageSize: 50,
                                    total: config.pagination.total,
                                    position: ['bottomCenter'],
                                    showTotal: (total) => t('common.totalItems', { total: total }),
                                }}
                                onChange={handleTableChange}
                                virtual={false}
                                style={{ height: '100%' }}
                                // Conditional expandable functionality - only for users table
                                {...(config.hasExpandableRows ? {
                                    expandable: {
                                        expandedRowRender,
                                        expandedRowKeys,
                                        onExpand: handleRowExpand,
                                        rowExpandable: record => {
                                            const userVerifications = record.verifications || allUserVerifications[record.id] || [];
                                            return userVerifications.length > 0;
                                        },
                                    }
                                } : {})}
                                onRow={(record) => ({
                                    onClick: () => handleRowClick(record),
                                    style: {
                                        cursor: (() => {
                                            if (config.hasExpandableRows) {
                                                const userVerifications = record.verifications || allUserVerifications[record.id] || [];
                                                return userVerifications.length > 0 ? 'pointer' : 'default';
                                            }
                                            return 'default';
                                        })()
                                    }
                                })}
                            />
                        </div>
                    </DragIndexContext.Provider>
                </SortableContext>
                <DragOverlay>
                    <table>
                        <thead>
                            <tr>
                                <th
                                    style={{
                                        backgroundColor: 'gray',
                                        padding: 16,
                                    }}
                                >
                                    {config.columns.find((i) => i.key === config.dragIndex?.active)?.title}
                                </th>
                            </tr>
                        </thead>
                    </table>
                </DragOverlay>
            </DndContext>
        );
    };

    // Update tab items to show proper counts
    const tabItems = [
        {
            key: 'users',
            label: `${t('userManagement.userRoles.user')} (${userCounts.regularUserCount})`,
            children: renderTable(),
        },
        {
            key: 'admins',
            label: `${t('userManagement.userRoles.admin')} (${userCounts.adminCount})`,
            children: renderTable(),
        },
    ];

    return (
        <div style={{ padding: '10px 24px 42px' }}>
            <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                items={tabItems}
                tabBarExtraContent={tabExtraContent}
                style={{ marginTop: '10px' }}
            />
        </div>
    );
};

export default UserManagementPage; 