import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

// Helper function to convert relative URL to absolute
const getAbsoluteUrl = (relativePath, baseUrl) => {
  if (relativePath && typeof relativePath === 'string' && !relativePath.startsWith('http') && !relativePath.startsWith('blob:')) {
    // Ensure baseUrl doesn't have /api/v1 if image paths are from domain root
    const domainBaseUrl = baseUrl.includes('/api/v1') ? baseUrl.replace('/api/v1', '') : baseUrl;
    return `${domainBaseUrl}${relativePath.startsWith('/') ? '' : '/'}${relativePath}`;
  }
  return relativePath;
};

// Helper function to process image URLs within an event object
const processEventImageUrls = (event, baseUrl) => {
  if (!event) return event;

  const processedEvent = { ...event };

  // Process banner_image_urls
  if (processedEvent.banner_image_urls && Array.isArray(processedEvent.banner_image_urls)) {
    processedEvent.banner_image_urls = processedEvent.banner_image_urls.map(url => getAbsoluteUrl(url, baseUrl));
  }

  // Process media_items
  if (processedEvent.media_items && Array.isArray(processedEvent.media_items)) {
    processedEvent.media_items = processedEvent.media_items.map(item => {
      if (item && item.file_path) {
        return { ...item, file_path: getAbsoluteUrl(item.file_path, baseUrl) };
      }
      return item;
    });
  }

  // Determine a primary display image URL (processed_image_url)
  let primaryImageUrl;
  if (processedEvent.banner_image_urls && processedEvent.banner_image_urls.length > 0) {
    primaryImageUrl = processedEvent.banner_image_urls[0];
  } else if (processedEvent.media_items && processedEvent.media_items.length > 0 && processedEvent.media_items[0]?.file_path) {
    // Ensure the first media item is an image type if possible, or just take its path
     primaryImageUrl = processedEvent.media_items[0].file_path;
  }
  processedEvent.processed_image_url = primaryImageUrl;
  
  // Populate an 'images' array for general gallery use (e.g., by EventIntroduction.js)
  // This array will contain all unique, processed image URLs from banners and image-type media items.
  const allImageUrls = new Set();
  if (processedEvent.banner_image_urls) {
    processedEvent.banner_image_urls.forEach(url => allImageUrls.add(url));
  }
  if (processedEvent.media_items) {
    processedEvent.media_items.forEach(item => {
      // Add to gallery if it's an image (heuristic: file_type starts with 'image' or has common image extension)
      // This relies on media_items having a file_type or a file_name to infer type
      const isImage = item?.file_type?.startsWith('image/') || /\.(jpeg|jpg|gif|png|webp)$/i.test(item?.file_name || '');
      if (item && item.file_path && isImage) {
        allImageUrls.add(item.file_path);
      }
    });
  }
  processedEvent.images = Array.from(allImageUrls);


  return processedEvent;
};

// Helper to map JS-style camelCase params to API-style snake_case for query params
// Example mapping: { searchTerm: 'foo', startDate: '2023-01-01' } -> { search_term: 'foo', start_date: '2023-01-01' }
const mapToApiParams = (params, mapping) => {
  const apiParams = {};
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) { // only include defined, non-null params
      const mappedKey = mapping[key] || key; // if no mapping, use original key (e.g. limit, offset)
      // Special handling for array parameters to convert to comma-separated string
      if ((key === 'tagIds' || key === 'eventVerificationTypeKeys' || key === 'governmentFundingTypeIds') && Array.isArray(params[key])) {
        apiParams[mappedKey] = params[key].join(',');
      } else {
        apiParams[mappedKey] = params[key];
      }
    }
  }
  return apiParams;
};

const baseEventParamsMapping = {
  limit: 'limit',
  offset: 'offset',
  searchTerm: 'search_term',
  startDate: 'start_date',
  endDate: 'end_date',
  tagIds: 'tag_ids',
  status: 'status',
  eventVerificationTypeKeys: 'event_verification_type_key', // Corrected: For filtering by verification requirements
  governmentFundingTypeIds: 'government_funding_keys', // For filtering by funding types
  organizationId: 'org_id', // Added for filtering by organization
  // Add any other common filter params here, ensure client passes them with these camelCase keys
};

const listPublicEventsParamsMapping = {
  ...baseEventParamsMapping,
  // No specific additions for listPublicEvents beyond base at the moment
  // organizationId is now inherited from baseEventParamsMapping
};

const listOrgAdminEventListingParamsMapping = {
  ...baseEventParamsMapping,
  isAdminView: 'isAdminView', // Add the isAdminView parameter
};

const listUserRegistrationsParamsMapping = {
  startDate: 'start_date',
  endDate: 'end_date',
  organizationId: 'organization_id',
  // status, limit, offset, sort, role can be used directly
};

export const eventService = {
  // Get paginated public events list with filters
  listPublicEvents: async (clientParams = {}) => {
    // clientParams: { limit, offset, searchTerm, startDate, endDate, tagIds, status, organizationId, etc. }
    try {
      const apiParams = mapToApiParams(clientParams, listPublicEventsParamsMapping);
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.EVENTS.LIST_PUBLIC_EVENTS, { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      let events = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.events)) { // Assuming response is { events: [], total: X, ... }
        events = responseData.events.map(event => processEventImageUrls(event, api.BASE_URL));
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || events.length;
      } else if (Array.isArray(responseData)) { // If API returns array directly
        events = responseData.map(event => processEventImageUrls(event, api.BASE_URL));
        total = parseInt(responseHeaders['x-total-count'], 10) || events.length;
      }
      return { events, total };
    } catch (error) {
      console.error('Error fetching public events:', error);
      throw error;
    }
  },
  
  // Get public event details by event ID
  getPublicEventDetail: async (eventId) => {
    try {
      const eventData = await api.get(API_ENDPOINTS.EVENTS.GET_PUBLIC_EVENT_DETAIL(eventId));
      return processEventImageUrls(eventData, api.BASE_URL);
    } catch (error) {
      console.error(`Error fetching public event details for event ${eventId}:`, error);
      throw error;
    }
  },

  // List event tags (publicly accessible)
  listEventTags: async (clientParams = {}) => {
    // API expects direct parameters: { limit, offset } (lang_code and approved are no longer needed)
    // API returns array of tags: [{ id, name_en, name_zh_hk, name_zh_cn, is_globally_approved }, ...]
    try {
      const response = await api.get(API_ENDPOINTS.EVENTS.LIST_EVENT_TAGS, { params: clientParams });
      return response; // API returns array directly, no mapping needed
    } catch (error) {
      console.error('Error fetching event tags:', error);
      throw error;
    }
  },

  // Create a new event tag (potentially admin/moderated)
  createEventTag: async (tagData) => {
    // tagData should be: { tag_name: String, language_code: String, description?: String }
    // This assumes tagData is already in the correct format from the caller.
    // If tagData comes as { tagName, languageCode, description }, it needs mapping.
    // For now, assuming correct format or direct pass-through.
    // Example mapping if needed:
    // const apiPayload = {
    //   tag_name: tagData.tagName,
    //   language_code: tagData.languageCode,
    //   ...(tagData.description && { description: tagData.description }),
    // };
    try {
      const response = await api.post(API_ENDPOINTS.EVENTS.CREATE_EVENT_TAG, tagData);
      return response;
    } catch (error) {
      console.error('Error creating event tag:', error);
      throw error;
    }
  },

  // Delete an event tag (admin/moderated)
  deleteEventTag: async (tagId) => {
    try {
      const response = await api.delete(API_ENDPOINTS.EVENTS.DELETE_EVENT_TAG(tagId));
      return response;
    } catch (error) {
      console.error('Error deleting event tag:', error);
      throw error;
    }
  },

  // --- User Event Registrations ---
  // Get user's registered events
  listMyRegistrations: async (clientParams = {}) => {
    // clientParams: { limit, offset, startDate, endDate, status, sort, role, organizationId }
    // API expects: { limit, offset, start_date, end_date, status, sort, role, organization_id }
    try {
      const apiParams = mapToApiParams(clientParams, listUserRegistrationsParamsMapping);
      const response = await api.get(API_ENDPOINTS.EVENT_REGISTRATIONS.LIST_MY_REGISTRATIONS, { params: apiParams });
      return response; // Expects array of EventRegistrationResponse
    } catch (error) {
      console.error('Error fetching user registered events:', error);
      throw error;
    }
  },

  // Get details of a specific event registration for the user
  getMyRegistrationDetail: async (registrationId) => {
    try {
      // Corrected endpoint from apiEndpoints.js might be GET_REGISTRATION_DETAIL
      const response = await api.get(API_ENDPOINTS.EVENT_REGISTRATIONS.GET_REGISTRATION_DETAIL(registrationId));
      return response;
    } catch (error) {
      console.error(`Error fetching user event registration details for ${registrationId}:`, error);
      throw error;
    }
  },

  // Register for an event
  registerForEvent: async (eventId) => {
    // API Payload: { event_id: uuid.UUID }
    try {
      const response = await api.post(API_ENDPOINTS.EVENT_REGISTRATIONS.REGISTER_FOR_EVENT, { event_id: eventId });
      return response;
    } catch (error) {
      console.error(`Error registering for event ${eventId}:`, error);
      throw error;
    }
  },

  // Cancel event registration
  cancelMyRegistration: async (registrationId) => {
    try {
      // Corrected endpoint from apiEndpoints.js might be CANCEL_REGISTRATION
      const response = await api.delete(API_ENDPOINTS.EVENT_REGISTRATIONS.CANCEL_REGISTRATION(registrationId));
      return response;
    } catch (error) {
      console.error(`Error cancelling event registration ${registrationId}:`, error);
      throw error;
    }
  },

  // Apply to be a volunteer for a specific event
  applyToVolunteerForEvent: async (eventId, notes = null) => {
    // API Payload: { notes?: string }
    try {
      const payload = notes ? { notes } : {}; // omitempty handled by {} if notes is null/undefined
      const response = await api.post(API_ENDPOINTS.EVENTS.APPLY_TO_VOLUNTEER_FOR_EVENT(eventId), payload);
      return response;
    } catch (error) {
      console.error(`Error applying as volunteer for event ${eventId}:`, error);
      throw error;
    }
  },

  // --- Organization Event Management ---
  // Create an event within an organization
  createEventInOrg: async (orgId, eventData) => {
    // eventData must match CreateEventRequest from api.md (e.g., jsonContent, location_type, start_time)
    // The caller (e.g., EventEdit.js) is responsible for formatting eventData correctly.
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.CREATE_EVENT(orgId), eventData);
      return response;
    } catch (error) {
      console.error(`Error creating event in organization ${orgId}:`, error);
      throw error;
    }
  },

  // Update an event within an organization
  updateEventInOrg: async (orgId, eventId, eventData) => {
    // eventData must match UpdateEventRequest from api.md (fields are optional pointers)
    // The caller (e.g., EventEdit.js) is responsible for formatting eventData correctly.
    // HTTP method should be PATCH as per api.md for updating events.
    try {

      const response = await api.patch(API_ENDPOINTS.ORGANIZATIONS.UPDATE_EVENT(orgId, eventId), eventData);
      return response;
    } catch (error) {
      console.error(`Error updating event ${eventId} in organization ${orgId}:`, error);
      throw error;
    }
  },

  // Check-in to an event (user initiated or by admin with user ID)
  eventCheckIn: async (userId) => {
    // API Payload: { user_id: string }
    try {
      const response = await api.post(API_ENDPOINTS.EVENT_REGISTRATIONS.CHECK_IN, { user_id: userId });
      return response;
    } catch (error) {
      console.error(`Error checking in user ${userId} to event:`, error);
      throw error;
    }
  },

  // --- New methods based on updated apiEndpoints.js and api.md --
  
  // List events for a specific organization (Admin View)
  listOrgEvents: async (orgId, clientParams = {}) => {
    // clientParams: { limit, offset, searchTerm, startDate, endDate, tagIds, status, isAdminView, etc. }
    if (!orgId) {
      console.error('listOrgEvents: Organization ID is required.');
      return { events: [], total: 0 };
    }
    try {
      const apiParams = mapToApiParams(clientParams, listOrgAdminEventListingParamsMapping);
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ORGANIZATIONS.LIST_ORG_EVENTS(orgId), { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      let events = [];
      let total = 0;

      if (responseData && Array.isArray(responseData.events)) { 
        events = responseData.events.map(event => processEventImageUrls(event, api.BASE_URL));
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || events.length;
      } else if (Array.isArray(responseData)) { 
         events = responseData.map(event => processEventImageUrls(event, api.BASE_URL));
         total = parseInt(responseHeaders['x-total-count'], 10) || events.length;
      } else {
        console.warn(`[eventService.listOrgEvents for ${orgId}] Response data.events was not an array or response was not array. Response data:`, responseData);
      }
      return { events, total };
    } catch (error) {
      console.error(`Error fetching org events for org ${orgId}:`, error);
      throw error;
    }
  },

  // List all events with admin-level visibility (SuperAdmin/Admin, cross-org or specific filtering)
  listAllEventsAsAdmin: async (clientParams = {}) => {
    // clientParams: { limit, offset, searchTerm, startDate, endDate, tagIds, status, organizationId, etc. }
    try {
      const apiParams = mapToApiParams(clientParams, listOrgAdminEventListingParamsMapping); // Can reuse mapping
      console.log('[eventService.listAllEventsAsAdmin] API Params:', apiParams);
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.EVENTS.LIST_ALL_EVENTS_AS_ADMIN, { params: apiParams });

      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      
      let events = [];
      let total = 0;

      if (responseData && Array.isArray(responseData.events)) {
        events = responseData.events.map(event => processEventImageUrls(event, api.BASE_URL));
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || events.length;
      } else if (Array.isArray(responseData)) {
         events = responseData.map(event => processEventImageUrls(event, api.BASE_URL));
         total = parseInt(responseHeaders['x-total-count'], 10) || events.length;
      } else {
        console.warn('[eventService.listAllEventsAsAdmin] Response data.events was not an array or response was not array. Response data:', responseData);
      }
      return { events, total };
    } catch (error) {
      console.error('Error fetching all events as admin:', error);
      throw error;
    }
  },

  getOrgEventDetail: async (orgId, eventId) => {
    try {
      const eventData = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_ORG_EVENT_DETAIL(orgId, eventId));
      return processEventImageUrls(eventData, api.BASE_URL);
    } catch (error) {
      console.error(`Error fetching event details for event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },

  updateEventStatusInOrg: async (orgId, eventId, newStatus) => {
    // API Payload: UpdateEventStatusRequest { new_status: string }
    try {
      const response = await api.patch(API_ENDPOINTS.ORGANIZATIONS.UPDATE_EVENT_STATUS(orgId, eventId), { new_status: newStatus });
      return processEventImageUrls(response, api.BASE_URL);
    } catch (error) {
      console.error(`Error updating status for event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },

  deleteEventInOrg: async (orgId, eventId) => {
    try {
      await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_EVENT(orgId, eventId));
      // No response body expected for DELETE typically
    } catch (error) {
      console.error(`Error deleting event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },
  
  // Placeholder for media, tags, verification types for org events as per apiEndpoints and api.md
  // These would require more specific payload handling (e.g., FormData for uploads)

  // Example for listing event registrations for an event in an org (admin view)
  listOrgEventRegistrations: async (orgId, eventId, clientParams = {}) => {
    // api.md for GET /organizations/:orgId/events/:eventId/registrations has PageRequest
    try {
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ORGANIZATIONS.LIST_ORG_EVENT_REGISTRATIONS(orgId, eventId), { params: clientParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      let registrations = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.registrations)) {
        registrations = responseData.registrations;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || registrations.length;
      } else if (Array.isArray(responseData)) {
        registrations = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || registrations.length;
      }
      return { registrations, total };
    } catch (error) {
      console.error(`Error fetching registrations for event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },

  // Get specific event statistics
  getSpecificEventStatistics: async (orgId, eventId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_SPECIFIC_EVENT_STATISTICS(orgId, eventId));
      return response;
    } catch (error) {
      console.error(`Error fetching statistics for event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },
  
  // Update general registration status (Admin for Org)
  updateOrgRegistrationStatus: async (orgId, registrationId, newStatus, adminNotes = null) => {
    // API Payload: UpdateRegistrationStatusRequest { new_status: string, admin_notes?: string }
    // Endpoint: PATCH /organizations/:orgId/registrations/:registrationId/status
    const payload = { new_status: newStatus };
    if (adminNotes !== null) {
      payload.admin_notes = adminNotes;
    }
    try {
      const response = await api.patch(API_ENDPOINTS.ORGANIZATIONS.UPDATE_REGISTRATION_STATUS(orgId, registrationId), payload);
      return response;
    } catch (error) {
      console.error(`Error updating registration ${registrationId} status in org ${orgId}:`, error);
      throw error;
    }
  },

  // Update specific org event registration status (Admin for Org, different status set)
  updateOrgEventRegistrationStatusDetailed: async (orgId, registrationId, newStatus, adminNotes = '') => {
    // API Payload: UpdateOrgEventRegistrationStatusRequest { new_status: string, admin_notes: string }
    // Endpoint: PATCH /organizations/:orgId/event-registrations/:registrationId/status
    const payload = { new_status: newStatus, admin_notes: adminNotes };
    try {
      const response = await api.patch(API_ENDPOINTS.ORGANIZATIONS.UPDATE_ORG_EVENT_REGISTRATION_STATUS(orgId, registrationId), payload);
      return response;
    } catch (error) {
      console.error(`Error updating detailed event registration ${registrationId} status in org ${orgId}:`, error);
      throw error;
    }
  },

  // --- General Lookup Services ---
  listVerificationTypes: async (clientParams = {}) => {
    try {
      const response = await api.get(API_ENDPOINTS.UTILS.LIST_VERIFICATION_TYPES, { params: clientParams });
      return response; // Assuming response is an array of VerificationType
    } catch (error) {
      console.error('Error fetching verification types:', error);
      throw error;
    }
  },

  listGovernmentFundingTypes: async (clientParams = {}) => {
    try {
      const response = await api.get(API_ENDPOINTS.UTILS.LIST_GOVERNMENT_FUNDING_TYPES, { params: clientParams });
      return response; // Assuming response is an array of GovernmentFundingType
    } catch (error) {
      console.error('Error fetching government funding types:', error);
      throw error;
    }
  },

  // Add a verification type to an event
  addVerificationTypeToEvent: async (orgId, eventId, typeKey) => {
    try {
      const response = await api.post(API_ENDPOINTS.ORGANIZATIONS.ADD_EVENT_VERIFICATION_TYPE(orgId, eventId, typeKey));
      return response;
    } catch (error) {
      console.error(`Error adding verification type ${typeKey} to event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },

  // Remove a verification type from an event
  removeVerificationTypeFromEvent: async (orgId, eventId, typeKey) => {
    try {
      const response = await api.delete(API_ENDPOINTS.ORGANIZATIONS.DELETE_EVENT_VERIFICATION_TYPE(orgId, eventId, typeKey));
      return response;
    } catch (error) {
      console.error(`Error removing verification type ${typeKey} from event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },

  getEventRoster: async (orgId, eventId, clientParams = {}) => {
    // clientParams like { limit, offset, query (search term), registration_status, payment_status, role }
    const apiParams = mapToApiParams(clientParams, {}); // No specific mapping defined for roster here, pass as is.
    try {
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ORGANIZATIONS.GET_EVENT_ROSTER(orgId, eventId), { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      let roster = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.roster)) {
        roster = responseData.roster;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || roster.length;
      } else if (Array.isArray(responseData)) {
        roster = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || roster.length;
      }
      return { roster, total };
    } catch (error) {
      console.error(`Error fetching event roster for event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },

  // Get event participant user IDs
  /**
   * Fetches participant user IDs for a given event, optionally filtered by status.
   * @param {string} eventId - The ID of the event.
   * @param {string | null} [status=null] - Optional registration status to filter by.
   * @returns {Promise<string[]>} - Promise resolving to an array of user IDs.
   */
  getEventParticipantUserIDs: async (eventId, status = null) => {
    try {
      const params = {};
      if (status) {
        params.status = status;
      }
      // Assuming the API returns an array of strings (user IDs)
      const response = await api.get(API_ENDPOINTS.EVENTS.GET_EVENT_PARTICIPANT_USER_IDS(eventId), { params });
      return response; // Should be string[] as per plan
    } catch (error) {
      console.error(`Error fetching participant user IDs for event ${eventId}:`, error);
      throw error;
    }
  },
};

export const eventStatisticsService = {
  getAllEventStatistics: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.EVENTS.GET_EVENT_STATISTICS);
      return response;
    } catch (error) {
      console.error('Error fetching all event statistics:', error);
      throw error;
    }
  },

  getOrganizationEventStatistics: async (orgId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_ORG_EVENT_STATISTICS(orgId));
      return response;
    } catch (error) {
      console.error(`Error fetching event statistics for organization ${orgId}:`, error);
      throw error;
    }
  },

  getSpecificEventStatistics: async (orgId, eventId) => {
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.GET_SPECIFIC_EVENT_STATISTICS(orgId, eventId));
      return response;
    } catch (error) {
      console.error(`Error fetching statistics for event ${eventId} in org ${orgId}:`, error);
      throw error;
    }
  },
}; 