import React, { useState, useEffect } from 'react';
import { Form, Checkbox, message, Divider, Switch, Typography, Radio, Button, Row, Col, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import { useUser } from '../../contexts/UserContext';
import { profileService } from '../../services/profileService';

const { Title, Text } = Typography;
const { Option } = Select;

const PreferencesTab = () => {
    const { t, i18n } = useTranslation();
    const [form] = Form.useForm();
    const { currentUser, fetchUserProfile } = useUser();

    // Default preferences state
    const [preferences, setPreferences] = useState({
        notificationEnabled: true,
        appNotifications: true,
        whatsappNotifications: false,
        smsNotifications: false,
    });

    // Language state
    const [currentLanguage, setCurrentLanguage] = useState(i18n.language);
    // Accessibility mode state
    // const [accessibilityMode, setAccessibilityMode] = useState('normal'); // 'normal' or 'senior'

    // Fetch user profile when tab is activated
    
    // useEffect(() => {
    //     fetchUserProfile();
    // }, [fetchUserProfile]);

    useEffect(() => {
        if (currentUser?.notification_settings) {
            const settings = currentUser.notification_settings;
            form.setFieldsValue({
                notificationEnabled: settings.enable_all_notifications,
                appNotifications: settings.enable_app_notifications,
                whatsappNotifications: settings.enable_whatsapp_notifications,
                smsNotifications: settings.enable_sms_notifications,
            });
            setPreferences({
                notificationEnabled: settings.enable_all_notifications,
                appNotifications: settings.enable_app_notifications,
                whatsappNotifications: settings.enable_whatsapp_notifications,
                smsNotifications: settings.enable_sms_notifications,
            });
        }

        // Set initial language and accessibility preferences from user data if available
        if (currentUser?.preferences) {
            if (currentUser.preferences.language) {
                // Convert underscore format to hyphen format if needed
                let language = currentUser.preferences.language;
                if (language === 'zh_HK') {
                    language = 'zh-HK';
                }
                setCurrentLanguage(language);
            }
            // if (currentUser.preferences.accessibility_mode) {
            //     setAccessibilityMode(currentUser.preferences.accessibility_mode);
            // }
        }
    }, [currentUser, form]);

    const handleFinish = async (values) => {
        console.log('Preferences to be updated:', values);
        try {
            // Format the notification settings payload in snake_case
            await profileService.updateUserProfile({
                notification_settings: {
                    enable_all_notifications: values.notificationEnabled,
                    enable_app_notifications: values.appNotifications,
                    enable_whatsapp_notifications: values.whatsappNotifications,
                    enable_sms_notifications: values.smsNotifications,
                }
            });
            
            // Refresh user profile data
            await fetchUserProfile();
            
            message.success(t('userSettings.preferences.messages.updateSuccess'));
        } catch (error) {
            console.error('Error updating preferences:', error);
            message.error(t('userSettings.preferences.messages.updateError'));
        }
    };
    
    const handleMasterToggle = (checked) => {
        const newPrefs = {
            notificationEnabled: checked,
            appNotifications: checked ? preferences.appNotifications : false,
            whatsappNotifications: checked ? preferences.whatsappNotifications : false,
            smsNotifications: checked ? preferences.smsNotifications : false,
        };
        form.setFieldsValue(newPrefs);
        setPreferences(newPrefs);
    };

    const handleChannelToggle = (channel, checked) => {
        // Handle mutual exclusivity between WhatsApp and SMS
        if (channel === 'whatsappNotifications' && checked) {
            const newPrefs = {
                ...preferences,
                whatsappNotifications: true,
                smsNotifications: false  // Turn off SMS if WhatsApp is turned on
            };
            form.setFieldsValue(newPrefs);
            setPreferences(newPrefs);
        } else if (channel === 'smsNotifications' && checked) {
            const newPrefs = {
                ...preferences,
                smsNotifications: true,
                whatsappNotifications: false  // Turn off WhatsApp if SMS is turned on
            };
            form.setFieldsValue(newPrefs);
            setPreferences(newPrefs);
        } else {
            // For other channels, just update normally
            const newPrefs = {
                ...preferences,
                [channel]: checked
            };
            form.setFieldsValue(newPrefs);
            setPreferences(newPrefs);
        }
    };

    const handleLanguageChange = (e) => {
        const value = e.target.value;
        setCurrentLanguage(value);
        i18n.changeLanguage(value);
        
        // Save language preference to user profile
        try {
            // Convert hyphen format to underscore format if needed for API consistency
            let storageLanguage = value;
            if (value === 'zh-HK') {
                storageLanguage = 'zh_HK';
            }
            
            profileService.updateUserProfile({
                preferences: {
                    language: storageLanguage
                }
            });

            // message.success(t('userSettings.preferences.language.messages.switchSuccess'));
        } catch (error) {
            console.error('Error updating language preference:', error);
            message.error(t('userSettings.preferences.language.messages.switchError'));
        }
    };

    // Custom Form Item with label and switch layout
    const CustomFormItem = ({ name, label, help, valuePropName, disabled, children }) => (
        <div style={{ marginBottom: '20px' }}>
            <Row align="middle">
                <Col span={16}>
                    <div>
                        <Text strong style={{ fontSize: '15px' }}>{label}</Text>
                        {help && (
                            <div style={{ marginTop: '4px' }}>
                                <Text type="secondary">{help}</Text>
                            </div>
                        )}
                    </div>
                </Col>
                <Col span={8} style={{ textAlign: 'left' }}>
                    <Form.Item 
                        name={name}
                        valuePropName={valuePropName}
                        style={{ margin: 0 }}
                    >
                        {children}
                    </Form.Item>
                </Col>
            </Row>
        </div>
    );

    return (
        <>
            {/* Language Settings Section */}
            <div className="settings-tab-content">
                <div className="settings-tab-sidebar">
                    <div className="settings-section-header">
                        <span className="settings-section-title">{t('userSettings.preferences.language.title')}</span>
                        <span className="settings-section-subtitle">{t('userSettings.preferences.language.subtitle')}</span>
                    </div>
                </div>
                <div className="settings-tab-main">
                    <div className="settings-subsection">
                        <div className="settings-subsection-header">
                            <span className="settings-subsection-title">{t('userSettings.preferences.language.title')}</span>
                        </div>
                        <Radio.Group 
                            onChange={handleLanguageChange} 
                            value={currentLanguage}
                            style={{ display: 'flex', gap: '20px' }}
                        >
                            <Radio value="en">{t('userSettings.preferences.language.english')}</Radio>
                            <Radio value="zh-HK">{t('userSettings.preferences.language.traditionalChinese')}</Radio>
                            {/* <Radio value="zh-CN">{t('userSettings.preferences.language.simplifiedChinese')}</Radio> */}
                        </Radio.Group>
                    </div>
                </div>
            </div>

            <Divider />

            {/* Accessibility Mode Section */}
            {/*
            <div className="settings-tab-content">
                <div className="settings-tab-sidebar">
                    <div className="settings-section-header">
                        <span className="settings-section-title">{t('userSettings.preferences.accessibility.title')}</span>
                        <span className="settings-section-subtitle">{t('userSettings.preferences.accessibility.subtitle')}</span>
                    </div>
                </div>
                <div className="settings-tab-main">
                    <div className="settings-subsection">
                        <div className="settings-subsection-header">
                            <span className="settings-subsection-title">{t('userSettings.preferences.accessibility.title')}</span>
                        </div>
                        <Radio.Group 
                            onChange={handleAccessibilityModeChange} 
                            value={accessibilityMode}
                            style={{ display: 'flex', gap: '20px' }}
                        >
                            <Radio value="normal">{t('userSettings.preferences.accessibility.normalMode')}</Radio>
                            <Radio value="senior">{t('userSettings.preferences.accessibility.seniorMode')}</Radio>
                        </Radio.Group>
                        {accessibilityMode === 'senior' && (
                            <Text type="secondary" style={{ marginTop: '8px', display: 'block' }}>
                                {t('userSettings.preferences.accessibility.seniorModeActiveNote')}
                            </Text>
                        )}
                    </div>
                </div>
            </div>
            <Divider />
            */}

            {/* Notification Settings Section */}
            <div className="settings-tab-content">
                <div className="settings-tab-sidebar">
                    <div className="settings-section-header">
                        <span className="settings-section-title">{t('userSettings.preferences.title')}</span>
                        <span className="settings-section-subtitle">{t('userSettings.preferences.subtitle')}</span>
                    </div>
                </div>
                <div className="settings-tab-main">
                    <Form
                        form={form}
                        layout="vertical"
                        initialValues={preferences}
                        onValuesChange={(changedValues, allValues) => {
                            if (changedValues.hasOwnProperty('notificationEnabled')) {
                                handleMasterToggle(changedValues.notificationEnabled);
                            } else if (changedValues.hasOwnProperty('whatsappNotifications') || 
                                       changedValues.hasOwnProperty('smsNotifications')) {
                                // For mutual exclusivity between WhatsApp and SMS
                                const channel = Object.keys(changedValues)[0];
                                handleChannelToggle(channel, changedValues[channel]);
                            } else {
                                setPreferences(allValues);
                            }
                        }}
                        onFinish={handleFinish}
                    >
                        {/* General Notifications Section */}
                        <div className="settings-subsection">
                            <CustomFormItem
                                name="notificationEnabled"
                                label={t('userSettings.preferences.notifications.general.enableAll')}
                                help={t('userSettings.preferences.notifications.general.enableDescription')}
                                valuePropName="checked"
                            >
                                <Switch />
                            </CustomFormItem>
                        </div>
                        <Divider />

                        {/* Notification Channels Section */}
                        <div className="settings-subsection">
                            <CustomFormItem
                                name="appNotifications"
                                label={t('userSettings.preferences.notifications.channels.inApp')}
                                help={t('userSettings.preferences.notifications.channels.inAppDescription')}
                                valuePropName="checked"
                                disabled={!preferences.notificationEnabled}
                            >
                                <Switch disabled={!preferences.notificationEnabled} />
                            </CustomFormItem>
                            
                            <CustomFormItem
                                name="whatsappNotifications"
                                label={t('userSettings.preferences.notifications.channels.whatsapp')}
                                help={t('userSettings.preferences.notifications.channels.whatsappDescription')}
                                valuePropName="checked"
                                disabled={!preferences.notificationEnabled}
                            >
                                <Switch disabled={!preferences.notificationEnabled} />
                            </CustomFormItem>
                            
                            <CustomFormItem
                                name="smsNotifications"
                                label={t('userSettings.preferences.notifications.channels.sms')}
                                help={t('userSettings.preferences.notifications.channels.smsDescription')}
                                valuePropName="checked"
                                disabled={!preferences.notificationEnabled}
                            >
                                <Switch disabled={!preferences.notificationEnabled} />
                            </CustomFormItem>
                        </div>
                        
                        <Row>
                            <Col span={16}>
                                {/* Empty space for alignment */}
                            </Col>
                            <Col span={8} style={{ textAlign: 'left' }}>
                                <Form.Item style={{ margin: '24px 0' }}>
                                    <Button type="primary" htmlType="submit">
                                        {t('userSettings.preferences.buttons.saveChanges')}
                                    </Button>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form>
                </div>
            </div>
        </>
    );
};

export default PreferencesTab; 