package payloads

import (
	"mime/multipart"
	"time"

	"Membership-SAAS-System-Backend/db"

	"github.com/google/uuid"
)

// Resource Related Payloads

type CreateResourceRequest struct {
	Title          string     `json:"title" form:"title" validate:"required,min=3,max=255"`
	Description    string     `json:"description" form:"description" validate:"omitempty"`
	Visibility     string     `json:"visibility" form:"visibility" validate:"omitempty,oneof=public org_only"`
	Status         string     `json:"status" form:"status" validate:"omitempty,oneof=draft published"`
	Slug           string     `json:"slug" form:"slug" validate:"omitempty,alphanumdash,max=255"` // Optional, can be auto-generated
	PublishedAt    *time.Time `json:"published_at,omitempty" form:"published_at,omitempty"`
	OrganizationID uuid.UUID  `json:"-"` // Set from path
}

type UpdateResourceRequest struct {
	Title       *string    `json:"title,omitempty" form:"title,omitempty" validate:"omitempty,min=3,max=255"`
	Description *string    `json:"description,omitempty" form:"description,omitempty"`
	Visibility  *string    `json:"visibility,omitempty" form:"visibility,omitempty" validate:"omitempty,oneof=public org_only"`
	Status      *string    `json:"status,omitempty" form:"status,omitempty" validate:"omitempty,oneof=draft published"`
	Slug        *string    `json:"slug,omitempty" form:"slug,omitempty" validate:"omitempty,alphanumdash,max=255"`
	PublishedAt *time.Time `json:"published_at,omitempty" form:"published_at,omitempty"`
}

type ResourceResponse struct {
	ID             uuid.UUID              `json:"id"`
	OrganizationID uuid.UUID              `json:"organization_id"`
	Title          string                 `json:"title"`
	Slug           string                 `json:"slug"`
	Description    string                 `json:"description,omitempty"`
	Visibility     string                 `json:"visibility"`
	Status         string                 `json:"status"`
	PublishedAt    *time.Time             `json:"published_at,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	Files          []ResourceFileResponse `json:"files,omitempty"`
	Organization   *OrgSlimResponse       `json:"organization,omitempty"` // For public listings
}

type ResourceFileResponse struct {
	ID          uuid.UUID `json:"id"`
	ResourceID  uuid.UUID `json:"resource_id"`
	FileName    string    `json:"file_name"`
	FilePath    string    `json:"file_path"` // Public URL
	FileType    string    `json:"file_type"`
	FileSize    int64     `json:"file_size"`
	UploadedAt  time.Time `json:"uploaded_at"`
	Description string    `json:"description,omitempty" example:"This is a detailed description of the file."` // Optional description of the file.
}

type UploadResourceFileRequest struct {
	File *multipart.FileHeader `form:"file" validate:"required"`
}

// ToResourceResponse converts a db.Resource and its files to a ResourceResponse
func ToResourceResponse(resource db.Resource, files []db.ResourceFile, organization *db.Organization) *ResourceResponse {
	resp := &ResourceResponse{
		ID:             resource.ID,
		OrganizationID: resource.OrganizationID,
		Title:          resource.Title,
		Slug:           resource.Slug,
		Visibility:     resource.Visibility,
		Status:         resource.Status,
		CreatedAt:      resource.CreatedAt,
		UpdatedAt:      resource.UpdatedAt,
		Files:          make([]ResourceFileResponse, len(files)),
	}

	if resource.Description != nil {
		resp.Description = *resource.Description
	} else {
		resp.Description = "" // Explicitly set to empty string if nil
	}

	if resource.PublishedAt != nil {
		resp.PublishedAt = resource.PublishedAt // It's already a pointer
	}

	for i, file := range files {
		resp.Files[i] = ToResourceFileResponse(file)
	}
	if organization != nil {
		resp.Organization = ToOrgSlimResponse(*organization)
	}
	return resp
}

// ToResourceFileResponse converts a db.ResourceFile to a ResourceFileResponse
func ToResourceFileResponse(file db.ResourceFile) ResourceFileResponse {
	resp := ResourceFileResponse{
		ID:         file.ID,
		ResourceID: file.ResourceID,
		FileName:   file.FileName,
		FilePath:   file.FilePath, // Will be prepended with BaseURL in handler
		FileType:   file.FileType,
		FileSize:   file.FileSize,
		UploadedAt: file.UploadedAt,
	}
	if file.Description != nil { // db.ResourceFile.Description is *string
		resp.Description = *file.Description
	}
	return resp
}

// ToResourceListResponse converts a slice of db.Resource to a slice of ResourceResponse
func ToResourceListResponse(resources []db.Resource, resourcesFiles map[uuid.UUID][]db.ResourceFile, orgsData map[uuid.UUID]db.Organization) []ResourceResponse {
	responses := make([]ResourceResponse, len(resources))
	for i, resource := range resources {
		files := []db.ResourceFile{}
		if resourcesFiles != nil {
			if f, ok := resourcesFiles[resource.ID]; ok {
				files = f
			}
		}
		var org *db.Organization
		if orgsData != nil {
			if o, ok := orgsData[resource.OrganizationID]; ok {
				org = &o
			}
		}
		responses[i] = *ToResourceResponse(resource, files, org)
	}
	return responses
}

// Helper for converting GetResourceWithFilesRow to ResourceResponse
func ToResourceResponseFromGetResourceWithFilesRow(rows []db.GetResourceWithFilesRow) *ResourceResponse {
	if len(rows) == 0 {
		return nil
	}

	resourceData := rows[0].Resource
	var files []db.ResourceFile

	for _, row := range rows {
		if row.ResourceFileID != nil {
			file := db.ResourceFile{}
			file.ID = *row.ResourceFileID
			if row.ResourceFileResourceID != nil {
				file.ResourceID = *row.ResourceFileResourceID
			} else {
				file.ResourceID = resourceData.ID
			}
			if row.ResourceFileFileName != nil {
				file.FileName = *row.ResourceFileFileName
			}
			if row.ResourceFileFilePath != nil {
				file.FilePath = *row.ResourceFileFilePath
			}
			if row.ResourceFileFileType != nil {
				file.FileType = *row.ResourceFileFileType
			}
			if row.ResourceFileFileSize != nil {
				file.FileSize = *row.ResourceFileFileSize
			}
			if row.ResourceFileUploadedAt != nil {
				file.UploadedAt = *row.ResourceFileUploadedAt
			}
			if row.ResourceFileDescription != nil { // Added for description
				file.Description = row.ResourceFileDescription // Already *string from sqlc row
			}
			files = append(files, file)
		}
	}
	return ToResourceResponse(resourceData, files, nil)
}

// Helper for converting GetResourceByOrgAndSlugWithFilesRow to ResourceResponse
func ToResourceResponseFromGetResourceByOrgAndSlugWithFilesRow(rows []db.GetResourceByOrgAndSlugWithFilesRow) *ResourceResponse {
	if len(rows) == 0 {
		return nil
	}

	resourceData := rows[0].Resource
	var files []db.ResourceFile

	for _, row := range rows {
		if row.ResourceFileID != nil {
			file := db.ResourceFile{}
			file.ID = *row.ResourceFileID
			if row.ResourceFileResourceID != nil {
				file.ResourceID = *row.ResourceFileResourceID
			} else {
				file.ResourceID = resourceData.ID
			}
			if row.ResourceFileFileName != nil {
				file.FileName = *row.ResourceFileFileName
			}
			if row.ResourceFileFilePath != nil {
				file.FilePath = *row.ResourceFileFilePath
			}
			if row.ResourceFileFileType != nil {
				file.FileType = *row.ResourceFileFileType
			}
			if row.ResourceFileFileSize != nil {
				file.FileSize = *row.ResourceFileFileSize
			}
			if row.ResourceFileUploadedAt != nil {
				file.UploadedAt = *row.ResourceFileUploadedAt
			}
			if row.ResourceFileDescription != nil { // Added for description
				file.Description = row.ResourceFileDescription // Already *string from sqlc row
			}
			files = append(files, file)
		}
	}
	return ToResourceResponse(resourceData, files, nil)
}

// Organization File Management Payloads
type CreateOrganizationFolderRequest struct {
	FolderName     string    `json:"folder_name" form:"folder_name" validate:"required,min=1,max=255"`
	ParentFolderID uuid.UUID `json:"parent_folder_id" form:"parent_folder_id"` // Optional
}

type UploadOrganizationFileRequest struct {
	File           *multipart.FileHeader `form:"file" validate:"required"`
	ParentFolderID uuid.UUID             `form:"parent_folder_id"` // Optional
}

type UpdateOrganizationFileRequest struct {
	NewName        *string    `json:"new_name,omitempty" form:"new_name,omitempty" validate:"omitempty,min=1,max=255"`
	ParentFolderID *uuid.UUID `json:"parent_folder_id,omitempty" form:"parent_folder_id,omitempty"` // For moving
}

type OrganizationFileResponse struct {
	ID             uuid.UUID `json:"id"`
	OrganizationID uuid.UUID `json:"organization_id"`
	FileName       string    `json:"file_name"`
	FilePath       string    `json:"file_path"` // Public URL or identifier
	FileType       *string   `json:"file_type,omitempty"`
	FileSize       *int64    `json:"file_size,omitempty"`
	IsFolder       bool      `json:"is_folder"`
	ParentFolderID uuid.UUID `json:"parent_folder_id,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

func ToOrganizationFileResponse(file db.OrganizationFile) OrganizationFileResponse {
	resp := OrganizationFileResponse{
		ID:             file.ID,
		OrganizationID: file.OrganizationID,
		FileName:       file.FileName,
		FilePath:       file.FilePath,
		IsFolder:       file.IsFolder,
		CreatedAt:      file.CreatedAt,
		UpdatedAt:      file.UpdatedAt,
	}
	if file.FileType != nil {
		resp.FileType = file.FileType // It's already *string
	}
	if file.FileSize != nil {
		resp.FileSize = file.FileSize // It's already *int64
	}
	if file.ParentFolderID != nil { // file.ParentFolderID is *uuid.UUID
		resp.ParentFolderID = *file.ParentFolderID // Dereference to assign to uuid.UUID field
	}
	return resp
}

func ToOrganizationFileListResponse(files []db.OrganizationFile) []OrganizationFileResponse {
	responses := make([]OrganizationFileResponse, len(files))
	for i, file := range files {
		responses[i] = ToOrganizationFileResponse(file)
	}
	return responses
}

// ToOrganizationFileListResponseFromRows converts rows from ListOrganizationFilesInRoot/Folder queries
func ToOrganizationFileListResponseFromRows(rows []db.ListOrganizationFilesInRootRow) []OrganizationFileResponse {
	responses := make([]OrganizationFileResponse, len(rows))
	for i, row := range rows {
		// Map db.ListOrganizationFilesInRootRow to payloads.OrganizationFileResponse
		fileResp := OrganizationFileResponse{
			ID:             row.ID,
			OrganizationID: row.OrganizationID,
			FileName:       row.FileName,
			FilePath:       row.FilePath, // This is the physical path for org files
			IsFolder:       row.IsFolder,
			CreatedAt:      row.CreatedAt,
			UpdatedAt:      row.UpdatedAt,
		}
		if row.FileType != nil { // Check if pointer is not nil
			fileResp.FileType = row.FileType // Assign pointer directly
		}
		if row.FileSize != nil { // Check if pointer is not nil
			fileResp.FileSize = row.FileSize // Assign pointer directly
		}
		if row.ParentFolderID != nil { // Check if pointer is not nil
			fileResp.ParentFolderID = *row.ParentFolderID // Dereference to assign to non-pointer uuid.UUID
		}
		responses[i] = fileResp
	}
	return responses
}

type ListResourcesParams struct {
	OrganizationID  *uuid.UUID `query:"organization_id" validate:"omitempty,uuid"`
	OrganizationID2 *uuid.UUID `query:"organization_id2" validate:"omitempty,uuid"`
	Visibility      *string    `query:"visibility" validate:"omitempty,oneof=public org_only"`
	Limit           int        `query:"limit" default:"10"`
	Offset          int        `query:"offset" default:"0"`
	Status          *string    `query:"status" validate:"omitempty,oneof=draft published"`
	SearchTerm      *string    `query:"search_term" form:"search_term" validate:"omitempty,max=255"`
}

type ListOrgFilesParams struct {
	ParentFolderID *uuid.UUID `query:"parent_folder_id"` // If nil, lists root
	Path           *string    `query:"path"`             // Alternative to ParentFolderID for UX
	Limit          int        `query:"limit" default:"50"`
	Offset         int        `query:"offset" default:"0"`
}

// ToResourceListResponseFromListOrgResourcesWithFilesRows processes rows from ListPublishedResourcesByOrganizationWithFiles query.
func ToResourceListResponseFromListOrgResourcesWithFilesRows(rows []db.ListPublishedResourcesByOrganizationWithFilesRow) []ResourceResponse {
	if len(rows) == 0 {
		return []ResourceResponse{}
	}

	resourceMap := make(map[uuid.UUID]*ResourceResponse)
	var orderedResourceIDs []uuid.UUID // Maintain original query order

	for _, row := range rows {
		resourceID := row.Resource.ID
		if _, exists := resourceMap[resourceID]; !exists {
			desc := ""
			if row.Resource.Description != nil {
				desc = *row.Resource.Description
			}
			resourceMap[resourceID] = &ResourceResponse{
				ID:             row.Resource.ID,
				OrganizationID: row.Resource.OrganizationID,
				Title:          row.Resource.Title,
				Slug:           row.Resource.Slug,
				Description:    desc,
				Visibility:     row.Resource.Visibility,
				Status:         row.Resource.Status,
				PublishedAt:    row.Resource.PublishedAt,
				CreatedAt:      row.Resource.CreatedAt,
				UpdatedAt:      row.Resource.UpdatedAt,
				Files:          []ResourceFileResponse{},
			}
			orderedResourceIDs = append(orderedResourceIDs, resourceID)
		}

		// Add file item if it exists for this row
		if row.ResourceFileID != nil {
			fileItem := ResourceFileResponse{
				ID:         *row.ResourceFileID,
				ResourceID: resourceID,
				FileName:   *row.ResourceFileFileName,
				FilePath:   *row.ResourceFileFilePath,
				FileType:   *row.ResourceFileFileType,
				FileSize:   *row.ResourceFileFileSize,
				UploadedAt: *row.ResourceFileUploadedAt,
			}
			resourceMap[resourceID].Files = append(resourceMap[resourceID].Files, fileItem)
		}
	}

	// Convert map to slice, maintaining order
	finalResponses := make([]ResourceResponse, len(orderedResourceIDs))
	for i, id := range orderedResourceIDs {
		finalResponses[i] = *resourceMap[id]
	}

	return finalResponses
}

// ToResourceListResponseFromListPublishedResourcesWithFilesRows processes rows from ListPublishedResourcesWithFiles query.
func ToResourceListResponseFromListPublishedResourcesWithFilesRows(rows []db.ListPublishedResourcesWithFilesRow) []ResourceResponse {
	if len(rows) == 0 {
		return []ResourceResponse{}
	}

	resourceMap := make(map[uuid.UUID]*ResourceResponse)
	resourceOrder := []uuid.UUID{} // To maintain original order from query

	for _, row := range rows {
		resourceID := row.Resource.ID
		var orgSlim *OrgSlimResponse
		// Assuming db.ListPublishedResourcesWithFilesRow.Resource.Organization is not populated
		// If Organization data is needed here, the query and row struct would need to include it, or a separate fetch would be needed.

		if _, ok := resourceMap[resourceID]; !ok {
			// First time seeing this resource
			resp := &ResourceResponse{
				ID:             row.Resource.ID,
				OrganizationID: row.Resource.OrganizationID,
				Title:          row.Resource.Title,
				Slug:           row.Resource.Slug,
				Visibility:     row.Resource.Visibility,
				Status:         row.Resource.Status,
				PublishedAt:    row.Resource.PublishedAt,
				CreatedAt:      row.Resource.CreatedAt,
				UpdatedAt:      row.Resource.UpdatedAt,
				Files:          []ResourceFileResponse{},
				Organization:   orgSlim,
			}
			if row.Resource.Description != nil {
				resp.Description = *row.Resource.Description
			}
			resourceMap[resourceID] = resp
			resourceOrder = append(resourceOrder, resourceID)
		}

		// Add file to this resource if a file is present in the row
		if row.ResourceFileID != nil {
			fileItem := ResourceFileResponse{
				ID:         *row.ResourceFileID,
				ResourceID: resourceID, // This should match row.Resource.ID
			}
			if row.ResourceFileFileName != nil {
				fileItem.FileName = *row.ResourceFileFileName
			}
			if row.ResourceFileFilePath != nil {
				fileItem.FilePath = *row.ResourceFileFilePath // Will be prepended with BaseURL in handler
			}
			if row.ResourceFileFileType != nil {
				fileItem.FileType = *row.ResourceFileFileType
			}
			if row.ResourceFileFileSize != nil {
				fileItem.FileSize = *row.ResourceFileFileSize
			}
			if row.ResourceFileUploadedAt != nil {
				fileItem.UploadedAt = *row.ResourceFileUploadedAt
			}
			if row.ResourceFileDescription != nil { // Added for description
				fileItem.Description = *row.ResourceFileDescription
			}

			resourceMap[resourceID].Files = append(resourceMap[resourceID].Files, fileItem)
		}
	}

	// Convert map to slice, preserving order
	finalResponses := make([]ResourceResponse, len(resourceOrder))
	for i, id := range resourceOrder {
		finalResponses[i] = *resourceMap[id]
	}

	return finalResponses
}
