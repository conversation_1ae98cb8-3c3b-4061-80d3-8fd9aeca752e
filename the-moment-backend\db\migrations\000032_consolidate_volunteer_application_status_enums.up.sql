-- Create a new common enum type for application statuses
CREATE TYPE application_status_enum AS ENUM (
    'pending',
    'approved',
    'rejected'
);

-- Alter user_volunteer_applications table
-- Step 1: Add a new temporary column with the new enum type
ALTER TABLE user_volunteer_applications ADD COLUMN status_new application_status_enum;

-- Step 2: Copy and cast data from the old status column to the new one
UPDATE user_volunteer_applications SET status_new = status::text::application_status_enum;

-- Step 3: Drop the old status column
ALTER TABLE user_volunteer_applications DROP COLUMN status;

-- Step 4: Rename the new column to 'status'
ALTER TABLE user_volunteer_applications RENAME COLUMN status_new TO status;

-- Step 5: Make the new status column NOT NULL and set default if needed (already implicitly handled by previous default)
ALTER TABLE user_volunteer_applications ALTER COLUMN status SET NOT NULL;
ALTER TABLE user_volunteer_applications ALTER COLUMN status SET DEFAULT 'pending';

-- Recreate index that was dropped with the column (if it used the old enum type directly in its definition, which is likely for specific values like pending)
-- The index `idx_uq_user_org_pending_application` depends on the status column. We need to drop and recreate it.
DROP INDEX IF EXISTS idx_uq_user_org_pending_application;
CREATE UNIQUE INDEX idx_uq_user_org_pending_application
ON user_volunteer_applications (user_id, organization_id)
WHERE status = 'pending';

-- Alter event_volunteer_applications table
-- Step 1: Add a new temporary column with the new enum type
ALTER TABLE event_volunteer_applications ADD COLUMN status_new application_status_enum;

-- Step 2: Copy and cast data from the old status column to the new one
UPDATE event_volunteer_applications SET status_new = status::text::application_status_enum;

-- Step 3: Drop the old status column
ALTER TABLE event_volunteer_applications DROP COLUMN status;

-- Step 4: Rename the new column to 'status'
ALTER TABLE event_volunteer_applications RENAME COLUMN status_new TO status;

-- Step 5: Make the new status column NOT NULL (default was set on original table creation)
ALTER TABLE event_volunteer_applications ALTER COLUMN status SET NOT NULL;
-- The default value for event_volunteer_applications.status was not explicitly set in its CREATE TABLE, assume it was 'pending' or handle if different.
-- If it was 'pending', add: ALTER TABLE event_volunteer_applications ALTER COLUMN status SET DEFAULT 'pending';
-- For now, assuming it was set or the existing default mechanism works with the new type.

-- Drop the old enum types (if they are no longer used by any other columns)
DROP TYPE IF EXISTS volunteer_application_status;
DROP TYPE IF EXISTS event_volunteer_application_status_type; 