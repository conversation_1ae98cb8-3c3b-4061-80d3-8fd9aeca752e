// Generating data from August 1, 2024, to February 12, 2025, with daily entries
export const trendChartData = [];
const startDate = new Date('2024-12-01');
const endDate = new Date('2025-03-31');
const oneDay = 24 * 60 * 60 * 1000;

for (let date = startDate; date <= endDate; date = new Date(date.getTime() + oneDay)) {
    const participants = Math.floor(Math.random() * 100) + 50 + (date.getDate() % 10) * 10; // Incremental participants
    const volunteers = Math.floor(participants * 0.2); // Volunteers should be much fewer than participants
    trendChartData.push({
        date: date.toISOString().split('T')[0],
        'Participants': participants,
        'Volunteers': volunteers,
    });
}

// Generate mock data for single event from January 13, 2025 to February 13, 2025
export const singleEventTrendData = [];
const singleEventStart = new Date('2025-01-13');
const singleEventEnd = new Date('2025-02-13');

// Base numbers for natural growth simulation
let baseParticipants = 0;
let baseVolunteers = 0;
// const dailyGrowthRate = 1.15; // 15% daily growth to reach target
const targetParticipants = 1250; // Target number to match KPI card
const targetVolunteers = 35;    // Target number to match KPI card

for (let date = singleEventStart; date <= singleEventEnd; date = new Date(date.getTime() + oneDay)) {
    // Add some randomness to the growth but ensure we reach our target
    const daysTotal = Math.floor((singleEventEnd - singleEventStart) / oneDay);
    const currentDay = Math.floor((date - singleEventStart) / oneDay);
    const progressRatio = currentDay / daysTotal;
    
    // Use sigmoid-like growth curve for more natural progression
    const sigmoid = 1 / (1 + Math.exp(-10 * (progressRatio - 0.5)));
    
    // Calculate current day's numbers
    baseParticipants = Math.floor(targetParticipants * sigmoid);
    baseVolunteers = Math.floor(targetVolunteers * sigmoid);
    
    // Add small random variation
    const randomFactor = 0.95 + Math.random() * 0.1; // Random factor between 0.95 and 1.05
    baseParticipants = Math.floor(baseParticipants * randomFactor);
    baseVolunteers = Math.floor(baseVolunteers * randomFactor);
    
    singleEventTrendData.push({
        date: date.toISOString().split('T')[0],
        'Participants': baseParticipants,
        'Volunteers': baseVolunteers,
    });
}