-- name: CreateOrganization :one
INSERT INTO organizations (
    name,
    description,
    is_default_org,
    image_url,
    theme_color,
    status
) VALUES (
    $1, $2, $3, $4, $5, $6
)
RETURNING *;

-- name: GetOrganizationByID :one
SELECT * FROM organizations
WHERE id = $1 LIMIT 1;

-- name: GetOrganizationByName :one
SELECT * FROM organizations
WHERE name = $1 LIMIT 1;

-- name: GetDefaultOrganization :one
SELECT * FROM organizations
WHERE is_default_org = TRUE LIMIT 1;

-- name: UpdateOrganization :one
UPDATE organizations
SET
    name = $2,
    description = $3,
    image_url = $4,
    theme_color = $5,
    status = $6,
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdateOrganizationLogoURL :one
UPDATE organizations
SET
    image_url = $2,
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: DeleteOrganization :exec
DELETE FROM organizations
WHERE id = $1;

-- name: ListOrganizations :many
SELECT *, COUNT(*) OVER() as total_count
FROM organizations
ORDER BY created_at ASC
LIMIT $1 OFFSET $2;
-- Consider adding LIMIT and OFFSET for pagination later

-- name: AddUserToOrganization :one
INSERT INTO user_organization_memberships (
    user_id,
    organization_id,
    role,
    is_active,
    notifications_enabled
) VALUES (
    $1, $2, $3, $4, $5
)
RETURNING *;

-- name: GetUserOrganizationMembership :one
SELECT * FROM user_organization_memberships
WHERE user_id = $1 AND organization_id = $2
LIMIT 1;

-- name: ListUserOrganizations :many
SELECT o.*
FROM organizations o
JOIN user_organization_memberships uom ON o.id = uom.organization_id
WHERE uom.user_id = $1 AND uom.is_active = TRUE
ORDER BY o.name;

-- name: ListOrganizationMembers :many
SELECT u.*, uom.role, uom.joined_at, uom.is_active AS membership_is_active, uom.notifications_enabled AS membership_notifications_enabled
FROM users u
JOIN user_organization_memberships uom ON u.id = uom.user_id
WHERE uom.organization_id = $1
ORDER BY u.display_name;

-- name: UpdateUserOrganizationMembershipRole :one
UPDATE user_organization_memberships
SET role = $3
WHERE user_id = $1 AND organization_id = $2
RETURNING *;

-- name: UpdateUserOrganizationMembershipDetails :one
UPDATE user_organization_memberships
SET 
    role = $3,
    is_active = $4,
    notifications_enabled = $5
WHERE user_id = $1 AND organization_id = $2
RETURNING *;

-- name: RemoveUserFromOrganization :exec
DELETE FROM user_organization_memberships
WHERE user_id = $1 AND organization_id = $2;

-- name: IsUserMemberOfOrganization :one
SELECT EXISTS(
    SELECT 1
    FROM user_organization_memberships
    WHERE user_id = $1 AND organization_id = $2 AND is_active = TRUE
);

-- name: GetUserOrganizationRole :one
SELECT role FROM user_organization_memberships
WHERE user_id = $1 AND organization_id = $2;

-- name: CountUserOwnedOrganizations :one
SELECT count(*) FROM user_organization_memberships
WHERE user_id = $1 AND role = 'owner';

-- name: ListOrganizationsForUser :many
SELECT
    o.id,
    o.name,
    o.description,
    o.is_default_org,
    o.image_url,
    o.theme_color,
    o.status,
    o.created_at,
    o.updated_at
FROM organizations o
JOIN user_organization_memberships uom ON o.id = uom.organization_id
WHERE uom.user_id = $1
ORDER BY o.name;

-- name: UpdateOrganizationMemberRole :exec
UPDATE user_organization_memberships
SET role = $3, updated_at = NOW()
WHERE user_id = $1 AND organization_id = $2;

-- name: GetOrganizationWithMemberCount :one
SELECT
    o.id,
    o.name,
    o.description,
    o.is_default_org,
    o.image_url,
    o.theme_color,
    o.status,
    o.created_at,
    o.updated_at,
    COUNT(uom.user_id) AS member_count
FROM organizations o
JOIN user_organization_memberships uom ON o.id = uom.organization_id
GROUP BY o.id
ORDER BY o.name;
-- name: GetCurrentOrganizationOwner :one
SELECT user_id FROM user_organization_memberships
WHERE organization_id = $1 AND role = 'owner'
LIMIT 1;
-- name: ListOrganizationsWhereUserIsOwner :many
SELECT o.*
FROM organizations o
JOIN user_organization_memberships uom ON o.id = uom.organization_id
WHERE uom.user_id = $1 AND uom.role = 'owner'
ORDER BY o.name ASC;