import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

// Helper to map JS-style camelCase params to API-style snake_case for query params
const mapToApiQueryParams = (params, mapping) => {
  const apiParams = {};
  if (params) {
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        const mappedKey = mapping[key] || key;
        apiParams[mappedKey] = params[key];
      }
    }
  }
  return apiParams;
};

// Re-using mappings similar to other services for consistency if applicable
const listPublicEventsParamsMapping = {
  searchTerm: 'search_term',
  startDate: 'start_date',
  endDate: 'end_date',
  tagIds: 'tag_ids',
  // status, limit, offset can be used directly if names match
};

const listPublicPostsParamsMapping = {
  tagIds: 'tag_ids',
  organizationId: 'organization_id',
  // limit, offset can be used directly if names match
};

export const homeService = {
  // Get a list of organizations for the home page/dashboard (publicly listable ones)
  listOrganizationsForHome: async (clientParams = {}) => {
    // GET /organizations - api.md doesn't list specific query params for general list.
    // Assuming potential PageRequest { limit, offset } is passed directly in clientParams.
    const apiParams = mapToApiQueryParams(clientParams, {}); // No specific mapping beyond direct keys
    try {
      const response = await api.get(API_ENDPOINTS.ORGANIZATIONS.LIST_ORGANIZATIONS, { params: apiParams });
      return response;
    } catch (error) {
      console.error('Error fetching organizations for home page:', error);
      throw error;
    }
  },

  // Get public events for the home page/dashboard
  listPublicEventsForHome: async (clientParams = {}) => {
    // clientParams: { limit, offset, searchTerm, startDate, endDate, tagIds, status }
    // API expects: { limit, offset, search_term, start_date, end_date, tag_ids, status }
    const apiParams = mapToApiQueryParams(clientParams, listPublicEventsParamsMapping);
    try {
      const response = await api.get(API_ENDPOINTS.EVENTS.LIST_PUBLIC_EVENTS, { params: apiParams });
      // Assuming response may need image processing similar to eventService
      // This service is simpler; for full processing, use eventService.
      return response;
    } catch (error) {
      console.error('Error fetching public events for home page:', error);
      throw error;
    }
  },

  // Get public posts for the home page/dashboard
  listPublicPostsForHome: async (clientParams = {}) => {
    // clientParams: { organizationId?, limit?, offset?, tagIds? }
    // API expects query: { organization_id?, limit?, offset?, tag_ids? }
    const apiParams = mapToApiQueryParams(clientParams, listPublicPostsParamsMapping);
    try {
      const response = await api.get(API_ENDPOINTS.POSTS.LIST_PUBLIC_POSTS, { params: apiParams });
      return response;
    } catch (error) {
      console.error('Error fetching public posts for home page:', error);
      throw error;
    }
  },
}; 