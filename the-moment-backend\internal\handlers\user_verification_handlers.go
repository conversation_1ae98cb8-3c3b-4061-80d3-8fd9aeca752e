package handlers

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/authn"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services"
	utils "Membership-SAAS-System-Backend/internal/utils"
	"encoding/json"
	"errors"
	"fmt"
	"mime/multipart"
	"net/http"
	"strings"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

type UserVerificationHandler struct {
	Service   *services.UserVerificationService
	Validator *utils.RequestValidator // Assuming you have a validator utility
}

func NewUserVerificationHandler(service *services.UserVerificationService, validator *utils.RequestValidator) *UserVerificationHandler {
	return &UserVerificationHandler{Service: service, Validator: validator}
}

// SubmitVerificationRequest godoc
// @Summary Submit a new verification request
// @Description Submits documents for user verification (e.g., ID, address proof) or registers a verification type like home_visit that does not require a document. This is a multipart form request.
// @Tags User Verification
// @Accept multipart/form-data
// @Produce json
// @Param type formData string true "Type of verification (e.g., hk_id_card, passport, home_visit, hk_youth_plus)"
// @Param document formData file false "Document file for the verification type. Required for types like 'hk_id_card', 'passport', 'hk_youth_plus'. Not required for 'home_visit'."
// @Param document2 formData file false "Second document file (e.g., back of ID card). Only for 'hk_id_card', 'mainland_china_id_card', 'mainland_travel_permit'."
// @Param hk_id_card_full_name_eng formData string false "Full name in English (required for hk_id_card)"
// @Param hk_id_card_full_name_chn formData string false "Full name in Chinese (required for hk_id_card)"
// @Param hk_id_card_dob formData string false "Date of Birth (YYYY-MM-DD, required for hk_id_card)"
// @Param hk_id_card_sex formData string false "Sex (M, F, O, required for hk_id_card)"
// @Param hk_id_card_number formData string false "HKID Card Number (required for hk_id_card)"
// @Param hk_id_card_is_permanent formData boolean false "Is HK permanent resident (required for hk_id_card)"
// @Param hk_id_card_commercial_code formData string false "HKID Card Commercial Code"
// @Param mainland_id_name_chn formData string false "Mainland ID Name (Chinese, required for mainland_china_id_card)"
// @Param mainland_id_sex formData string false "Mainland ID Sex (M, F, required for mainland_china_id_card)"
// @Param mainland_id_dob formData string false "Mainland ID Date of Birth (YYYY-MM-DD, required for mainland_china_id_card)"
// @Param mainland_id_number formData string false "Mainland ID Number (required for mainland_china_id_card)"
// @Param mainland_id_valid_from formData string false "Mainland ID Valid From (YYYY-MM-DD, required for mainland_china_id_card)"
// @Param mainland_id_valid_until formData string false "Mainland ID Valid Until (YYYY-MM-DD, required for mainland_china_id_card)"
// @Param mainland_travel_permit_number formData string false "Mainland Travel Permit Number (required for mainland_travel_permit)"
// @Param mainland_travel_permit_issue_date formData string false "Mainland Travel Permit Issue Date (YYYY-MM-DD, required for mainland_travel_permit)"
// @Param mainland_travel_permit_expiry_date formData string false "Mainland Travel Permit Expiry Date (YYYY-MM-DD, required for mainland_travel_permit)"
// @Param passport_number formData string false "Passport Number (required for passport)"
// @Param passport_issue_date formData string false "Passport Issue Date (YYYY-MM-DD, required for passport)"
// @Param passport_expiry_date formData string false "Passport Expiry Date (YYYY-MM-DD, required for passport)"
// @Param address_proof_full_name formData string false "Full Name on Address Proof (required for address_proof)"
// @Param address_proof_address formData string false "Address on Address Proof (required for address_proof)"
// @Param student_id_school_name formData string false "School Name (required for student_id)"
// @Param student_id_grade formData string false "Grade (required for student_id)"
// @Param student_id_expiry_date formData string false "Student ID Expiry Date (YYYY-MM-DD, required for student_id)"
// @Param hk_youth_plus_member_number formData string false "HK Youth Plus Member Number (required for hk_youth_plus if type is hk_youth_plus)"
// @Success 201 {object} payloads.UserVerificationRequestResponse "Verification request submitted"
// @Failure 400 {object} utils.ErrorResponse "Invalid request or file type"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Router /users/me/verifications [post]
func (h *UserVerificationHandler) SubmitVerificationRequest(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid token claims", err)
	}
	userID := claims.UserID
	// Print request details including form data
	form, err := c.MultipartForm()
	if err != nil {
		log.Ctx(c.Request().Context()).Info().
			Str("userID", userID.String()).
			Str("method", c.Request().Method).
			Str("path", c.Request().URL.Path).
			Str("contentType", c.Request().Header.Get("Content-Type")).
			Str("error", "Could not parse multipart form").
			Msg("Received verification request")
	} else {
		formData := make(map[string]interface{})
		for key, values := range form.Value {
			if len(values) == 1 {
				formData[key] = values[0]
			} else {
				formData[key] = values
			}
		}

		fileNames := make(map[string]string)
		for key, files := range form.File {
			if len(files) > 0 {
				fileNames[key] = files[0].Filename
			}
		}

		log.Ctx(c.Request().Context()).Info().
			Str("userID", userID.String()).
			Str("method", c.Request().Method).
			Str("path", c.Request().URL.Path).
			Str("contentType", c.Request().Header.Get("Content-Type")).
			Interface("formData", formData).
			Interface("files", fileNames).
			Msg("Received verification request")
	}

	// Bind general request data first (verification_type)
	var generalReq payloads.SubmitVerificationRequest
	if err := c.Bind(&generalReq); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request format", err)
	}
	log.Ctx(c.Request().Context()).Info().Str("verificationType", generalReq.VerificationType).Msg("Verification type")

	if err := h.Validator.ValidateStruct(generalReq); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	verificationTypeEnum, err := payloads.GetVerificationTypeEnumFromString(generalReq.VerificationType)
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Str("verificationType", generalReq.VerificationType).Str("error", err.Error()).Msg("Invalid verification type provided")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid verification type provided", err)
	}

	// Handle file upload
	var fileHeader *multipart.FileHeader
	var errFileAccess error
	var fileHeader2 *multipart.FileHeader // For the second document
	var errFile2Access error

	// Attempt to get the first document file
	fileHeader, errFileAccess = c.FormFile("document")

	if errFileAccess != nil {
		if errors.Is(errFileAccess, http.ErrMissingFile) {
			// File is missing. Only allow this for 'home_visit'.
			if verificationTypeEnum != db.VerificationTypeEnumHomeVisit {
				return utils.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Missing document file, which is required for verification type: %s", generalReq.VerificationType), errFileAccess)
			}
			// For home_visit, fileHeader remains nil, which is intended.
		} else {
			// Another error occurred when trying to access the file.
			return utils.HandleError(c, http.StatusBadRequest, "Failed to read document file", errFileAccess)
		}
	} else {
		// File header is present for the first document.
	}

	// Conditionally attempt to get the second document file
	switch verificationTypeEnum {
	case db.VerificationTypeEnumHkIDCard,
		db.VerificationTypeEnumMainlandChinaIDCard,
		db.VerificationTypeEnumMainlandTravelPermit:
		fileHeader2, errFile2Access = c.FormFile("document_2")
		if errFile2Access != nil {
			if errors.Is(errFile2Access, http.ErrMissingFile) {
				// For these types, document2 is optional, so missing is not an error here.
				fileHeader2 = nil
			} else {
				// Another error occurred when trying to access the second file.
				return utils.HandleError(c, http.StatusBadRequest, "Failed to read second document file", errFile2Access)
			}
		}
	}

	// Collect all form data for specific payload binding/parsing by the service
	formValues, err := c.FormParams()
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Could not parse form data", err)
	}
	formDataMap := make(map[string]string)
	for k, v := range formValues {
		if len(v) > 0 {
			formDataMap[k] = v[0] // Take the first value for simplicity
		}
	}

	// Validate specific payload based on type (this happens in service for now or could be here too)
	// For simplicity, we rely on service to parse formDataMap and handle specific validations implicitly or explicitly

	createdRequest, serviceErr := h.Service.SubmitVerificationRequest(c.Request().Context(), userID, verificationTypeEnum, fileHeader, fileHeader2, formDataMap)
	if serviceErr != nil {
		switch {
		case errors.As(serviceErr, new(*payloads.FileProcessingError)):
			return utils.HandleError(c, http.StatusBadRequest, serviceErr.Error(), serviceErr)
		case errors.As(serviceErr, new(*payloads.IllegalArgumentError)):
			return utils.HandleError(c, http.StatusBadRequest, serviceErr.Error(), serviceErr)
		default:
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to submit verification request", serviceErr)
		}
	}

	return c.JSON(http.StatusCreated, createdRequest)
}

// GetUserVerificationRequests godoc
// @Summary List all verification requests for the current user
// @Description Retrieves a list of verification requests submitted by the authenticated user.
// @Tags User Verification
// @Produce json
// @Success 200 {array} payloads.UserVerificationRequestResponse "List of user's verification requests"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /users/me/verifications [get]
func (h *UserVerificationHandler) GetUserVerificationRequests(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized", err)
	}
	requests, serviceErr := h.Service.GetUserVerificationRequests(c.Request().Context(), claims.UserID)
	if serviceErr != nil {
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve verification requests", serviceErr)
	}
	return c.JSON(http.StatusOK, requests)
}

// GetUserVerificationRequestDetails godoc
// @Summary Get details of a specific verification request
// @Description Retrieves details for a specific verification request owned by the user.
// @Tags User Verification
// @Produce json
// @Param reqID path string true "Verification Request ID"
// @Success 200 {object} payloads.UserVerificationRequestResponse "Verification request details"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not owner)"
// @Failure 404 {object} utils.ErrorResponse "Request not found"
// @Router /users/me/verifications/{reqID} [get]
func (h *UserVerificationHandler) GetUserVerificationRequestDetails(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized", err)
	}
	reqIDStr := c.Param("reqID")
	reqID, err := uuid.Parse(reqIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid verification request ID format", err)
	}

	details, serviceErr := h.Service.GetFullVerificationDetails(c.Request().Context(), reqID)
	if serviceErr != nil {
		if errors.As(serviceErr, new(*payloads.NotFoundError)) {
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve verification details", serviceErr)
	}

	// Parse details.UserID string to UUID for comparison
	detailsUserIDUUID, parseErr := uuid.Parse(details.UserID)
	if parseErr != nil {
		log.Ctx(c.Request().Context()).Error().Err(parseErr).Str("requestID", reqIDStr).Str("details_user_id", details.UserID).Msg("Failed to parse UserID from verification details payload")
		// Decide how to handle - return internal error or forbidden? Internal error seems safer.
		return utils.HandleError(c, http.StatusInternalServerError, "Error processing verification details", parseErr)
	}

	if detailsUserIDUUID != claims.UserID { // Compare UUIDs
		return utils.HandleError(c, http.StatusForbidden, "User does not have permission to view this request", nil)
	}

	return c.JSON(http.StatusOK, details)
}

// GetUserVerificationDocument godoc
// @Summary Get a user's verification document
// @Description Retrieves a specific verification document submitted by the authenticated user.
// @Tags User Verification
// @Produce application/octet-stream
// @Param docID path string true "Verification Document ID"
// @Success 200 {file} byte "Verification document file"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not owner)"
// @Failure 404 {object} utils.ErrorResponse "Document not found or access denied"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /users/me/verifications/documents/{docID} [get]
func (h *UserVerificationHandler) GetUserVerificationDocument(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized", err)
	}
	docIDStr := c.Param("docID")
	docID, err := uuid.Parse(docIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid document ID format", err)
	}

	fileData, mimeType, fileName, serviceErr := h.Service.GetVerificationDocument(c.Request().Context(), docID, claims.UserID)
	if serviceErr != nil {
		if errors.As(serviceErr, new(*payloads.NotFoundError)) {
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		}
		if errors.As(serviceErr, new(*payloads.ForbiddenError)) { // Service might check ownership more deeply
			return utils.HandleError(c, http.StatusForbidden, serviceErr.Error(), serviceErr)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve document", serviceErr)
	}

	c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=\"%s\"", fileName))
	return c.Blob(http.StatusOK, mimeType, fileData)
}

// DeleteUserVerificationData godoc
// @Summary Request deletion of verification data
// @Description Allows user to request deletion of their verification data associated with a request.
// @Tags User Verification
// @Param reqID path string true "Verification Request ID"
// @Success 204 "No Content"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not owner)"
// @Failure 404 {object} utils.ErrorResponse "Request not found"
// @Router /users/me/verifications/{reqID} [delete]
func (h *UserVerificationHandler) DeleteUserVerificationData(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized", err)
	}
	reqIDStr := c.Param("reqID")
	reqID, err := uuid.Parse(reqIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid verification request ID format", err)
	}

	serviceErr := h.Service.UserDeleteVerificationData(c.Request().Context(), reqID, claims.UserID)
	if serviceErr != nil {
		switch {
		case errors.As(serviceErr, new(*payloads.NotFoundError)):
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		case errors.As(serviceErr, new(*payloads.ForbiddenError)):
			return utils.HandleError(c, http.StatusForbidden, serviceErr.Error(), serviceErr)
		default:
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to delete verification data", serviceErr)
		}
	}
	return c.NoContent(http.StatusNoContent)
}

// --- Admin-Facing Handlers ---

// ListPendingVerifications godoc
// @Summary List pending verification requests (Admin)
// @Description Retrieves all verification requests with status 'pending'. Requires staff privileges.
// @Tags Admin Verification
// @Produce json
// @Success 200 {array} db.UserVerificationRequest "List of pending verification requests"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not staff)"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /admin/verifications/pending [get]
func (h *UserVerificationHandler) ListPendingVerifications(c echo.Context) error {
	// Admin check middleware should be applied to this route
	requests, serviceErr := h.Service.ListPendingVerificationRequests(c.Request().Context())
	if serviceErr != nil {
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve pending requests", serviceErr)
	}
	return c.JSON(http.StatusOK, requests)
}

// GetVerificationDetailsForAdmin godoc
// @Summary Get full details of a verification request (Admin)
// @Description Retrieves full details of a specific verification request, including user info. Requires staff privileges.
// @Tags Admin Verification
// @Produce json
// @Param reqID path string true "Verification Request ID"
// @Success 200 {object} db.UserVerificationRequest "Full verification request details"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not staff)"
// @Failure 404 {object} utils.ErrorResponse "Request not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /admin/verifications/{reqID} [get]
func (h *UserVerificationHandler) GetVerificationDetailsForAdmin(c echo.Context) error {
	reqIDStr := c.Param("reqID")
	reqID, err := uuid.Parse(reqIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid verification request ID format", err)
	}
	details, serviceErr := h.Service.GetFullVerificationDetails(c.Request().Context(), reqID)
	if serviceErr != nil {
		if errors.As(serviceErr, new(*payloads.NotFoundError)) {
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve verification details", serviceErr)
	}
	return c.JSON(http.StatusOK, details)
}

// ReviewVerificationRequest godoc
// @Summary Approve or reject a verification request (Admin)
// @Description Allows staff to approve or reject a pending verification request.
// @Tags Admin Verification
// @Accept json
// @Produce json
// @Param reqID path string true "Verification Request ID"
// @Param body body payloads.AdminReviewVerificationRequest true "Review action (approve/reject) and comments"
// @Success 200 {object} db.UserVerificationRequest "Updated verification request"
// @Failure 400 {object} utils.ErrorResponse "Invalid request or action"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not staff)"
// @Failure 404 {object} utils.ErrorResponse "Request not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /admin/verifications/{reqID}/review [patch]
func (h *UserVerificationHandler) ReviewVerificationRequest(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)                                                                   // Need admin user ID for audit/reviewed_by
	if err != nil || (claims.Role != string(db.UserRoleAdmin) && claims.Role != string(db.UserRoleSuperadmin)) { // Check for admin or superadmin role
		return utils.HandleError(c, http.StatusForbidden, "Forbidden: Admin access required", err)
	}
	adminUserID := claims.UserID

	reqIDStr := c.Param("reqID")
	reqID, err := uuid.Parse(reqIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid verification request ID format", err)
	}

	var reviewPayload payloads.AdminReviewVerificationRequest
	if err := c.Bind(&reviewPayload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid review payload", err)
	}

	if err := h.Validator.ValidateStruct(reviewPayload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed for review payload", err)
	}

	reviewStatusEnum, err := payloads.GetAdminReviewStatusEnumFromString(reviewPayload.Status)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid review status value", err)
	}

	reviewInput := payloads.ReviewVerificationRequestInput{
		Status:     reviewStatusEnum,
		AdminNotes: reviewPayload.AdminNotes,
	}

	updatedRequest, serviceErr := h.Service.ReviewVerificationRequest(c.Request().Context(), adminUserID, reqID, reviewInput)
	if serviceErr != nil {
		switch {
		case errors.As(serviceErr, new(*payloads.NotFoundError)):
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		case errors.As(serviceErr, new(*payloads.IllegalArgumentError)):
			return utils.HandleError(c, http.StatusBadRequest, serviceErr.Error(), serviceErr)
		default:
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to review verification request", serviceErr)
		}
	}
	return c.JSON(http.StatusOK, updatedRequest)
}

// GetVerificationDocumentForAdmin godoc
// @Summary Download any verification document (Admin)
// @Description Allows staff to download any submitted verification document.
// @Tags Admin Verification
// @Produce octet-stream
// @Param docID path string true "Document ID"
// @Success 200 {file} binary "Verification document file"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not staff)"
// @Failure 404 {object} utils.ErrorResponse "Document not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /admin/verifications/documents/{docID} [get]
func (h *UserVerificationHandler) GetVerificationDocumentForAdmin(c echo.Context) error {
	// Admin check middleware should be applied
	docIDStr := c.Param("docID")
	docID, err := uuid.Parse(docIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid document ID format", err)
	}

	fileData, mimeType, fileName, serviceErr := h.Service.AdminGetVerificationDocumentData(c.Request().Context(), docID)
	if serviceErr != nil {
		if errors.As(serviceErr, new(*payloads.NotFoundError)) {
			return utils.HandleError(c, http.StatusNotFound, serviceErr.Error(), serviceErr)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve document for admin", serviceErr)
	}
	c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=\"%s\"", fileName))
	return c.Blob(http.StatusOK, mimeType, fileData)
}

// AdminListAllVerifications godoc
// @Summary List all verification requests regardless of status (Admin - Testing Only)
// @Description Retrieves all verification requests. For testing purposes. Requires staff privileges.
// @Description Can be filtered by status (pending, approved, rejected) and/or organization ID.
// @Tags Admin Verification
// @Produce json
// @Param status query string false "Filter by verification status (e.g., pending, approved, rejected)" Enums(pending, approved, rejected)
// @Param org_id query string false "Filter by organization ID (UUID)" Format(uuid)
// @Success 200 {array} db.UserVerificationRequest "List of all verification requests"
// @Failure 400 {object} utils.ErrorResponse "Bad request (e.g., invalid status or org_id)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (not staff)"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /admin/verifications/ [get]
func (h *UserVerificationHandler) AdminListAllVerifications(c echo.Context) error {
	// Admin check middleware should be applied
	log.Warn().Msg("Accessed AdminListAllVerifications testing endpoint.")

	status := c.QueryParam("status")
	orgIDStr := c.QueryParam("orgId")

	// Optional: Validate status if provided
	if status != "" && !(status == "pending" || status == "approved" || status == "rejected") {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid status parameter. Allowed values: pending, approved, rejected.", nil)
	}

	// Optional: Validate org_id if provided (basic UUID check, can be more robust)
	var orgIDPtr *uuid.UUID
	if orgIDStr != "" {
		parsedOrgID, err := uuid.Parse(orgIDStr)
		if err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid org_id parameter. Must be a valid UUID.", err)
		}
		orgIDPtr = &parsedOrgID
	}

	requests, serviceErr := h.Service.AdminListAllVerificationRequests(c.Request().Context(), status, orgIDPtr)
	if serviceErr != nil {
		// Determine if the error is due to bad input vs internal
		if strings.Contains(serviceErr.Error(), "invalid input") { // Example check, adjust based on actual service errors
			return utils.HandleError(c, http.StatusBadRequest, "Failed to retrieve requests due to invalid parameters.", serviceErr)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve all requests (testing endpoint)", serviceErr)
	}
	return c.JSON(http.StatusOK, requests)
}

// AdminListUserVerifications godoc
// @Summary List user verification requests (Admin)
// @Description Retrieves a paginated list of user verification requests with filtering capabilities for admins.
// @Tags Admin - User Verification
// @Accept json
// @Produce json
// @Param user_id query string false "Filter by User ID (UUID)"
// @Param event_id query string false "Filter by Event ID (UUID)"
// @Param verification_type query string false "Filter by verification type (e.g., hk_id_card, student_id)"
// @Param status query string false "Filter by status (e.g., pending, approved, rejected)"
// @Param org_id query string false "Filter by Organization ID (UUID)"
// @Param page query int false "Page number for pagination" default(1)
// @Param limit query int false "Number of items per page" default(10)
// @Success 200 {object} payloads.PaginatedUserVerificationRequestsResponse "Paginated list of verification requests"
// @Failure 400 {object} utils.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /admin/verifications [get]
func (h *UserVerificationHandler) AdminListUserVerifications(c echo.Context) error {
	ctx := c.Request().Context()

	filters := payloads.AdminListVerificationFilters{
		Page:  utils.ParseQueryInt(c, "page", 1),
		Limit: utils.ParseQueryInt(c, "limit", 10),
	}

	if filters.Page < 1 {
		filters.Page = 1
	}
	if filters.Limit < 1 {
		filters.Limit = 10
	} else if filters.Limit > 100 { // Max limit
		filters.Limit = 100
	}

	userIDStr := c.QueryParam("user_id")
	if userIDStr != "" {
		parsedUserID, err := uuid.Parse(userIDStr)
		if err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid user_id format", err)
		}
		filters.UserID = &parsedUserID
	}

	eventIDStr := c.QueryParam("event_id")
	if eventIDStr != "" {
		parsedEventID, err := uuid.Parse(eventIDStr)
		if err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid event_id format", err)
		}
		filters.EventID = &parsedEventID
	}

	orgIDStr := c.QueryParam("org_id")
	if orgIDStr != "" {
		parsedOrgID, err := uuid.Parse(orgIDStr)
		if err != nil {
			return utils.HandleError(c, http.StatusBadRequest, "Invalid org_id format", err)
		}
		filters.OrgID = &parsedOrgID
	}

	filters.VerificationType = c.QueryParam("verification_type")
	filters.Status = c.QueryParam("status")

	// Validate verification_type if provided
	if filters.VerificationType != "" {
		isValid := false
		for _, enumVal := range db.AllVerificationTypeEnumValues() {
			if string(enumVal) == filters.VerificationType {
				isValid = true
				break
			}
		}
		if !isValid {
			return utils.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Invalid verification_type value: %s", filters.VerificationType), nil)
		}
	}

	// Validate status if provided
	if filters.Status != "" {
		isValid := false
		for _, enumVal := range db.AllVerificationStatusEnumValues() {
			if string(enumVal) == filters.Status {
				isValid = true
				break
			}
		}
		if !isValid {
			return utils.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Invalid status value: %s", filters.Status), nil)
		}
	}

	paginatedResponse, err := h.Service.AdminListUserVerificationsFiltered(ctx, filters)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list user verifications from service")
		// Specific error handling can be done here if service returns typed errors
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve verification requests", err)
	}

	return c.JSON(http.StatusOK, paginatedResponse)
}

// VolunteerGetUserVerificationRequests godoc
// @Summary List all verification requests for a specific user (Volunteer/Admin)
// @Description Retrieves a list of verification requests for a given user ID. Requires admin/volunteer privileges.
// @Tags Volunteer
// @Produce json
// @Param user_id query string true "User ID"
// @Success 200 {array} payloads.UserVerificationRequestResponse "List of user's verification requests"
// @Failure 400 {object} utils.ErrorResponse "Bad Request - Invalid User ID"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /volunteers/users/verifications [get]
func (h *UserVerificationHandler) VolunteerGetUserVerificationRequests(c echo.Context) error {
	var payload payloads.VolunteerGetUserVerificationRequestsPayload
	if err := c.Bind(&payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request query parameters", err)
	}
	if err := c.Validate(&payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	requests, serviceErr := h.Service.GetUserVerificationRequests(c.Request().Context(), payload.UserID)
	if serviceErr != nil {
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve verification requests", serviceErr)
	}

	return c.JSON(http.StatusOK, requests)
}

// VolunteerSubmitVerificationRequest godoc
// @Summary Submit a verification request for a user (Volunteer/Admin)
// @Description Submits a new verification request on behalf of a specified user. Requires admin/volunteer privileges.
// @Tags Volunteer
// @Accept mpfd
// @Produce json
// @Param data formData string true "JSON string with user_id and verification_type. e.g. `{\"user_id\":\"...\", \"verification_type\":\"...\"}`"
// @Param document formData file false "The primary document file (e.g., front of ID card)."
// @Param document2 formData file false "The secondary document file (e.g., back of ID card)."
// @Success 201 {object} db.UserVerificationRequest "Successfully created verification request"
// @Failure 400 {object} utils.ErrorResponse "Bad Request"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /volunteers/users/verifications [post]
func (h *UserVerificationHandler) VolunteerSubmitVerificationRequest(c echo.Context) error {
	dataField := c.FormValue("data")
	if dataField == "" {
		return utils.HandleError(c, http.StatusBadRequest, "Missing 'data' field in form", nil)
	}

	var payload payloads.VolunteerSubmitVerificationRequest
	if err := json.Unmarshal([]byte(dataField), &payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid JSON in 'data' field", err)
	}

	if err := c.Validate(payload); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid data payload", err)
	}

	verificationTypeEnum := db.VerificationTypeEnum(payload.VerificationType)
	if !verificationTypeEnum.Valid() {
		return utils.HandleError(c, http.StatusBadRequest, fmt.Sprintf("Invalid verification type: %s", payload.VerificationType), nil)
	}

	fileHeader, _ := c.FormFile("document")
	fileHeader2, _ := c.FormFile("document2")

	formValues, err := c.FormParams()
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Could not parse form data", err)
	}
	formDataMap := make(map[string]string)
	for k, v := range formValues {
		if len(v) > 0 {
			formDataMap[k] = v[0]
		}
	}

	createdRequest, serviceErr := h.Service.SubmitVerificationRequest(c.Request().Context(), payload.UserID, verificationTypeEnum, fileHeader, fileHeader2, formDataMap)
	if serviceErr != nil {
		switch {
		case errors.As(serviceErr, new(*payloads.FileProcessingError)):
			return utils.HandleError(c, http.StatusBadRequest, serviceErr.Error(), serviceErr)
		case errors.As(serviceErr, new(*payloads.IllegalArgumentError)):
			return utils.HandleError(c, http.StatusBadRequest, serviceErr.Error(), serviceErr)
		default:
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to submit verification request", serviceErr)
		}
	}

	return c.JSON(http.StatusCreated, createdRequest)
}
