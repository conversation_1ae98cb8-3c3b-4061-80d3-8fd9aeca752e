.change-contact-container {
    max-width: 600px;
    margin: 40px auto;
    padding: 0 16px;
}

.change-contact-card {
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    padding: 24px;
}

.back-button {
    margin-bottom: 24px;
    padding-left: 0;
}

.change-contact-header {
    text-align: center;
    margin-bottom: 40px;
}

.change-contact-header h2 {
    margin-bottom: 16px;
    font-weight: 600;
}

.description-text {
    font-size: 16px;
    display: block;
    max-width: 480px;
    margin: 0 auto;
}

.change-contact-steps {
    margin: 48px 0;
    padding: 0 24px;
}

.step-content-wrapper {
    animation: fadeIn 0.3s ease-in-out;
}

.verification-section,
.update-section {
    max-width: 400px;
    margin: 0 auto;
}

.current-contact {
    background: #fafafa;
    padding: 24px;
    border-radius: 12px;
    margin-bottom: 32px;
    border: 1px solid #f0f0f0;
}

.current-contact:hover {
    border-color: #f0f0f0;
    box-shadow: none;
}

.contact-label {
    display: block;
    margin-bottom: 8px;
    color: rgba(0, 0, 0, 0.45);
}

.contact-value {
    font-size: 18px;
    display: block;
}

.verification-input-group {
    display: flex;
    gap: 12px;
}

.verification-input {
    letter-spacing: 4px;
    font-size: 18px;
}

.phone-input,
.email-input {
    height: 48px;
    font-size: 16px;
}

.send-code-button {
    min-width: 120px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .change-contact-card {
        padding: 16px;
    }

    .change-contact-steps {
        padding: 0;
        margin: 32px 0;
    }

    .verification-input-group {
        flex-direction: column;
    }

    .send-code-button {
        width: 100%;
    }
} 