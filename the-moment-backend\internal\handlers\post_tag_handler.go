package handlers

import (
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services"
	"net/http"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

type PostTagHandler struct {
	Service *services.PostTagService
}

func NewPostTagHandler(service *services.PostTagService) *PostTagHandler {
	return &PostTagHandler{Service: service}
}

// HandleCreatePostTag godoc
// @Summary Create a new post tag
// @Description Creates a new post tag with i18n names and optional descriptions.
// @Tags Post Tags
// @Accept json
// @Produce json
// @Param tag body payloads.CreatePostTagRequest true "Post Tag Create Payload"
// @Success 201 {object} payloads.PostTagResponse
// @Failure 400 {object} echo.Map "Invalid request format or validation error"
// @Failure 500 {object} echo.Map "Internal server error"
// @Router /tags/posts [post]
func (h *PostTagHandler) HandleCreatePostTag(c echo.Context) error {
	var req payloads.CreatePostTagRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid request: " + err.Error()})
	}
	if err := c.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": err.Error()})
	}
	tag, err := h.Service.CreatePostTag(c.Request().Context(), req)
	if err != nil {
		// TODO: Check for specific DB errors, e.g., unique constraint violation
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "failed to create post tag: " + err.Error()})
	}
	return c.JSON(http.StatusCreated, tag)
}

// HandleGetPostTag godoc
// @Summary Get a specific post tag by ID
// @Description Retrieves details of a post tag using its UUID.
// @Tags Post Tags
// @Accept json
// @Produce json
// @Param tagId path string true "Post Tag ID (UUID)"
// @Success 200 {object} payloads.PostTagResponse
// @Failure 400 {object} echo.Map "Invalid tag ID format"
// @Failure 404 {object} echo.Map "Tag not found"
// @Failure 500 {object} echo.Map "Internal server error"
// @Router /tags/posts/{tagId} [get]
func (h *PostTagHandler) HandleGetPostTag(c echo.Context) error {
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid tag id"})
	}
	tag, err := h.Service.GetPostTag(c.Request().Context(), tagID)
	if err != nil {
		// Consider using a more specific error check, e.g., if errors.Is(err, sql.ErrNoRows)
		return c.JSON(http.StatusNotFound, echo.Map{"error": "tag not found"})
	}
	return c.JSON(http.StatusOK, tag)
}

// HandleListPostTags godoc
// @Summary List all post tags
// @Description Retrieves a list of all available post tags.
// @Tags Post Tags
// @Accept json
// @Produce json
// @Success 200 {array} payloads.PostTagResponse
// @Failure 500 {object} echo.Map "Internal server error"
// @Router /tags/posts [get]
func (h *PostTagHandler) HandleListPostTags(c echo.Context) error {
	tags, err := h.Service.ListPostTags(c.Request().Context())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "failed to list post tags: " + err.Error()})
	}
	return c.JSON(http.StatusOK, tags)
}

// HandleUpdatePostTag godoc
// @Summary Update an existing post tag
// @Description Updates an existing post tag's i18n names or descriptions.
// @Tags Post Tags
// @Accept json
// @Produce json
// @Param tagId path string true "Post Tag ID (UUID)"
// @Param tag body payloads.UpdatePostTagRequest true "Post Tag Update Payload"
// @Success 200 {object} payloads.PostTagResponse
// @Failure 400 {object} echo.Map "Invalid request format or validation error / Invalid tag ID"
// @Failure 404 {object} echo.Map "Tag not found"
// @Failure 500 {object} echo.Map "Internal server error"
// @Router /tags/posts/{tagId} [put]
func (h *PostTagHandler) HandleUpdatePostTag(c echo.Context) error {
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid tag id"})
	}
	var req payloads.UpdatePostTagRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid request: " + err.Error()})
	}
	if err := c.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": err.Error()})
	}
	tag, err := h.Service.UpdatePostTag(c.Request().Context(), tagID, req)
	if err != nil {
		// TODO: Check for specific DB errors or if tag not found prior to update attempt
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "failed to update post tag: " + err.Error()})
	}
	return c.JSON(http.StatusOK, tag)
}

// HandleDeletePostTag godoc
// @Summary Delete a post tag
// @Description Deletes a post tag by its UUID. Also removes associations from posts.
// @Tags Post Tags
// @Accept json
// @Produce json
// @Param tagId path string true "Post Tag ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} echo.Map "Invalid tag ID format"
// @Failure 500 {object} echo.Map "Internal server error / Failed to delete tag"
// @Router /tags/posts/{tagId} [delete]
func (h *PostTagHandler) HandleDeletePostTag(c echo.Context) error {
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid tag id"})
	}
	err = h.Service.DeletePostTag(c.Request().Context(), tagID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "failed to delete post tag: " + err.Error()})
	}
	return c.NoContent(http.StatusNoContent)
}

// HandleAddTagToPost godoc
// @Summary Add a tag to a post
// @Description Associates an existing post tag with an existing post.
// @Tags Posts, Post Tags
// @Accept json
// @Produce json
// @Param postId path string true "Post ID (UUID)"
// @Param tagId path string true "Post Tag ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} echo.Map "Invalid post ID or tag ID format"
// @Failure 500 {object} echo.Map "Internal server error / Failed to add tag to post"
// @Router /posts/{postId}/tags/{tagId} [post]
func (h *PostTagHandler) HandleAddTagToPost(c echo.Context) error {
	postID, err := uuid.Parse(c.Param("postId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid post id"})
	}
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid tag id"})
	}
	err = h.Service.AddTagToPost(c.Request().Context(), postID, tagID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "failed to add tag to post: " + err.Error()})
	}
	return c.NoContent(http.StatusNoContent)
}

// HandleRemoveTagFromPost godoc
// @Summary Remove a tag from a post
// @Description Disassociates a post tag from a post.
// @Tags Posts, Post Tags
// @Accept json
// @Produce json
// @Param postId path string true "Post ID (UUID)"
// @Param tagId path string true "Post Tag ID (UUID)"
// @Success 204 "No Content"
// @Failure 400 {object} echo.Map "Invalid post ID or tag ID format"
// @Failure 500 {object} echo.Map "Internal server error / Failed to remove tag from post"
// @Router /posts/{postId}/tags/{tagId} [delete]
func (h *PostTagHandler) HandleRemoveTagFromPost(c echo.Context) error {
	postID, err := uuid.Parse(c.Param("postId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid post id"})
	}
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid tag id"})
	}
	err = h.Service.RemoveTagFromPost(c.Request().Context(), postID, tagID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "failed to remove tag from post: " + err.Error()})
	}
	return c.NoContent(http.StatusNoContent)
}

// HandleGetTagsForPost godoc
// @Summary Get all tags for a specific post
// @Description Retrieves a list of all tags associated with a given post UUID.
// @Tags Posts, Post Tags
// @Accept json
// @Produce json
// @Param postId path string true "Post ID (UUID)"
// @Success 200 {array} payloads.PostTagResponse
// @Failure 400 {object} echo.Map "Invalid post ID format"
// @Failure 500 {object} echo.Map "Internal server error / Failed to get tags for post"
// @Router /posts/{postId}/tags [get]
func (h *PostTagHandler) HandleGetTagsForPost(c echo.Context) error {
	postID, err := uuid.Parse(c.Param("postId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid post id"})
	}
	tags, err := h.Service.GetTagsForPost(c.Request().Context(), postID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "failed to get tags for post: " + err.Error()})
	}
	return c.JSON(http.StatusOK, tags)
}
