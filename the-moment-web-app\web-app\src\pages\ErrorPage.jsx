import React from 'react';
import { Button, Result } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const ErrorPage = ({ type = 'general', showBackHome = true, className }) => {
    const navigate = useNavigate();
    const { t } = useTranslation();
    const routeType = useParams().type; // Get type from route if used as a page

    // Use provided type prop or route parameter
    const errorType = routeType || type;

    // Map error types to their corresponding translation keys and status
    const errorConfig = {
        '403': {
            translationKey: 'forbidden',
            status: '403'
        },
        '404': {
            translationKey: 'notFound',
            status: '404'
        },
        '500': {
            translationKey: 'serverError',
            status: '500'
        },
        'general': {
            translationKey: 'generalError',
            status: 'error'
        }
    };

    const { translationKey, status } = errorConfig[errorType] || errorConfig.general;

    const goToHome = () => {
        navigate('/');
    };

    return (
        <div className={className}>
            <Result
                status={status}
                title={t(`${translationKey}.title`)}
                subTitle={t(`${translationKey}.subTitle`)}
                extra={showBackHome && (
                    <Button type="primary" onClick={goToHome}>
                        {t(`${translationKey}.backHome`)}
                    </Button>
                )}
            />
        </div>
    );
}

export default ErrorPage; 