import React, { createContext, useState, useEffect, useContext, useCallback, useRef, useMemo } from 'react';
import { profileService } from '../services/profileService';
import { useAuth } from './AuthContext'; // Assuming AuthContext exports useAuth

const UserContext = createContext(null);

export const UserProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user: authUser, isAuthenticated, isLoading: isAuthLoading } = useAuth(); // Get auth state

  const guestUser = useMemo(() => ({ 
    id: 'guest-user-placeholder', 
    role: 'guest', 
    // Add any other default properties a guest might need, e.g., preferences
    personalInfo: { 
      username: 'Guest',
      display_name: 'Guest'
    }
  }), []);

  // Ref to track the previous authenticated state to detect actual changes
  const prevIsAuthenticatedRef = useRef(undefined);

  const fetchUserProfile = useCallback(async () => {
    // This check is crucial: only proceed if genuinely authenticated.
    if (!isAuthenticated()) {
      // This case should ideally be handled by the useEffect that sets guestUser directly.
      // If somehow called, ensure guest state.
      setCurrentUser(guestUser);
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const userProfile = await profileService.getUserProfile();
      setCurrentUser(userProfile);
    } catch (err) {
      console.error('Failed to fetch user profile in UserContext:', err);
      setError(err);
      setCurrentUser(null); // On error for an authenticated user, set to null to indicate a problem.
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, guestUser]); // guestUser is stable, isAuthenticated is the key driver

  useEffect(() => {
    // Only proceed if auth loading is complete
    if (isAuthLoading) {
      setIsLoading(true); // Reflect that UserContext is also waiting for auth
      return;
    }

    const currentIsAuthenticated = isAuthenticated();

    // If auth state has meaningfully changed or it's the first run after auth is resolved
    if (prevIsAuthenticatedRef.current !== currentIsAuthenticated) {
      if (currentIsAuthenticated) {
        fetchUserProfile();
      } else {
        setCurrentUser(guestUser);
        setIsLoading(false); // Explicitly set loading to false for guest state
      }
    } else if (prevIsAuthenticatedRef.current === undefined && !currentIsAuthenticated) {
        // Handles the very first load case where user is not authenticated
        setCurrentUser(guestUser);
        setIsLoading(false);
    }

    // Update the ref to the current auth state for the next render
    prevIsAuthenticatedRef.current = currentIsAuthenticated;

  }, [isAuthLoading, isAuthenticated, guestUser]); // Removed fetchUserProfile from dependencies

  const clearUser = () => {
    setCurrentUser(guestUser); 
    setError(null);
    prevIsAuthenticatedRef.current = false; // After logout, auth state is false
  };

  // We can add functions to update user settings/profile here later
  // For example:
  // const updateProfile = async (profileData) => {
  //   setIsLoading(true);
  //   try {
  //     const updatedUser = await profileService.updateUserProfile(profileData);
  //     // The role should ideally be re-fetched or confirmed if it can change
  //     // For now, let's assume updateUserProfile returns the full updated profile with role
  //     // Or we call fetchUserProfile() again.
  //     const refreshedUserProfile = await profileService.getUserProfile();
  //     setCurrentUser(refreshedUserProfile);
  //     setIsLoading(false);
  //     return refreshedUserProfile;
  //   } catch (err) {
  //     console.error('Failed to update user profile:', err);
  //     setError(err);
  //     setIsLoading(false);
  //     throw err;
  //   }
  // };


  const value = {
    currentUser,
    isLoading,
    error,
    fetchUserProfile, // Expose refetch function
    clearUser,
    // updateProfile, // if added
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined || context === null) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}; 