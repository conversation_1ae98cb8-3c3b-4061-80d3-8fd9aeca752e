// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: event_statistics.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const getEventCategoryStats = `-- name: GetEventCategoryStats :many
SELECT
    et.name_en,
    et.name_zh_hk,
    et.name_zh_cn,
    COUNT(DISTINCT e.id) AS total_events,
    COUNT(er.id) FILTER (WHERE er.status = 'registered' OR er.status = 'attended') AS total_registrations,
    COUNT(er.id) FILTER (WHERE er.status = 'attended') AS total_attendees
FROM event_tags et
JOIN event_event_tags eet ON et.id = eet.event_tag_id
JOIN events e ON eet.event_id = e.id
LEFT JOIN event_registrations er ON e.id = er.event_id
WHERE
    ($1::TIMESTAMPTZ IS NULL OR e.start_time >= $1::TIMESTAMPTZ)
    AND ($2::TIMESTAMPTZ IS NULL OR e.start_time <= $2::TIMESTAMPTZ)
    AND e.status = 'published'
GROUP BY et.id, et.name_en, et.name_zh_hk, et.name_zh_cn
ORDER BY total_events DESC
`

type GetEventCategoryStatsParams struct {
	StartDate *time.Time `db:"start_date" json:"start_date"`
	EndDate   *time.Time `db:"end_date" json:"end_date"`
}

type GetEventCategoryStatsRow struct {
	NameEn             string `db:"name_en" json:"name_en"`
	NameZhHk           string `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn           string `db:"name_zh_cn" json:"name_zh_cn"`
	TotalEvents        int64  `db:"total_events" json:"total_events"`
	TotalRegistrations int64  `db:"total_registrations" json:"total_registrations"`
	TotalAttendees     int64  `db:"total_attendees" json:"total_attendees"`
}

func (q *Queries) GetEventCategoryStats(ctx context.Context, arg GetEventCategoryStatsParams) ([]GetEventCategoryStatsRow, error) {
	rows, err := q.db.Query(ctx, getEventCategoryStats, arg.StartDate, arg.EndDate)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventCategoryStatsRow{}
	for rows.Next() {
		var i GetEventCategoryStatsRow
		if err := rows.Scan(
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.TotalEvents,
			&i.TotalRegistrations,
			&i.TotalAttendees,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventParticipantsByAge = `-- name: GetEventParticipantsByAge :many
SELECT
    CASE
        WHEN age BETWEEN 0 AND 10 THEN '0-10'
        WHEN age BETWEEN 11 AND 20 THEN '11-20'
        WHEN age BETWEEN 21 AND 30 THEN '21-30'
        WHEN age BETWEEN 31 AND 40 THEN '31-40'
        WHEN age BETWEEN 41 AND 50 THEN '41-50'
        WHEN age BETWEEN 51 AND 60 THEN '51-60'
        ELSE '61+'
    END AS age_range,
    COUNT(*)::INT AS count
FROM (
    SELECT
        EXTRACT(YEAR FROM AGE(to_date(COALESCE(v_hkid.date_of_birth, v_cnid.date_of_birth), 'YYYY-MM-DD'))) AS age
    FROM event_registrations er
    JOIN users u ON er.user_id = u.id
    LEFT JOIN user_verification_requests uvr ON u.id = uvr.user_id AND uvr.id = (
        SELECT id FROM user_verification_requests
        WHERE user_id = u.id
          AND status = 'approved'
          AND verification_type IN ('hk_id_card', 'mainland_china_id_card')
        ORDER BY submitted_at DESC
        LIMIT 1
    )
    LEFT JOIN verification_hk_id_cards v_hkid ON uvr.id = v_hkid.verification_request_id
    LEFT JOIN verification_mainland_china_id_cards v_cnid ON uvr.id = v_cnid.verification_request_id
    WHERE er.event_id = $1
    AND er.status IN ('registered', 'attended')
    AND COALESCE(v_hkid.date_of_birth, v_cnid.date_of_birth) IS NOT NULL
) AS ages
GROUP BY age_range
ORDER BY age_range
`

type GetEventParticipantsByAgeRow struct {
	AgeRange string `db:"age_range" json:"age_range"`
	Count    int32  `db:"count" json:"count"`
}

func (q *Queries) GetEventParticipantsByAge(ctx context.Context, eventID uuid.UUID) ([]GetEventParticipantsByAgeRow, error) {
	rows, err := q.db.Query(ctx, getEventParticipantsByAge, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventParticipantsByAgeRow{}
	for rows.Next() {
		var i GetEventParticipantsByAgeRow
		if err := rows.Scan(&i.AgeRange, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventParticipantsByDate = `-- name: GetEventParticipantsByDate :many
SELECT
    DATE(created_at)::TEXT as date,
    COUNT(*)::INT AS count
FROM event_registrations
WHERE event_id = $1 AND status IN ('registered', 'attended')
GROUP BY DATE(created_at)
ORDER BY DATE(created_at)
`

type GetEventParticipantsByDateRow struct {
	Date  string `db:"date" json:"date"`
	Count int32  `db:"count" json:"count"`
}

func (q *Queries) GetEventParticipantsByDate(ctx context.Context, eventID uuid.UUID) ([]GetEventParticipantsByDateRow, error) {
	rows, err := q.db.Query(ctx, getEventParticipantsByDate, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventParticipantsByDateRow{}
	for rows.Next() {
		var i GetEventParticipantsByDateRow
		if err := rows.Scan(&i.Date, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventParticipantsByGender = `-- name: GetEventParticipantsByGender :many
SELECT
    COALESCE(LOWER(gender), 'undefined') as gender,
    COUNT(*)::INT AS count
FROM (
    SELECT
        COALESCE(v_hkid.sex, v_cnid.sex) as gender
    FROM event_registrations er
    JOIN users u ON er.user_id = u.id
    LEFT JOIN user_verification_requests uvr ON u.id = uvr.user_id AND uvr.id = (
        SELECT id FROM user_verification_requests
        WHERE user_id = u.id
          AND status = 'approved'
          AND verification_type IN ('hk_id_card', 'mainland_china_id_card')
        ORDER BY submitted_at DESC
        LIMIT 1
    )
    LEFT JOIN verification_hk_id_cards v_hkid ON uvr.id = v_hkid.verification_request_id
    LEFT JOIN verification_mainland_china_id_cards v_cnid ON uvr.id = v_cnid.verification_request_id
    WHERE er.event_id = $1
    AND er.status IN ('registered', 'attended')
) as genders
GROUP BY gender
`

type GetEventParticipantsByGenderRow struct {
	Gender interface{} `db:"gender" json:"gender"`
	Count  int32       `db:"count" json:"count"`
}

func (q *Queries) GetEventParticipantsByGender(ctx context.Context, eventID uuid.UUID) ([]GetEventParticipantsByGenderRow, error) {
	rows, err := q.db.Query(ctx, getEventParticipantsByGender, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventParticipantsByGenderRow{}
	for rows.Next() {
		var i GetEventParticipantsByGenderRow
		if err := rows.Scan(&i.Gender, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventStatistics = `-- name: GetEventStatistics :one

SELECT
    COUNT(DISTINCT er.id)::INT AS participants_count,
    COUNT(DISTINCT eva.id)::INT AS volunteers_count,
    e.participant_limit AS max_participants
FROM events e
LEFT JOIN event_registrations er ON e.id = er.event_id AND er.status IN ('registered', 'attended')
LEFT JOIN event_volunteer_applications eva ON e.id = eva.event_id AND eva.status = 'approved'
WHERE e.id = $1
GROUP BY e.id
`

type GetEventStatisticsRow struct {
	ParticipantsCount int32  `db:"participants_count" json:"participants_count"`
	VolunteersCount   int32  `db:"volunteers_count" json:"volunteers_count"`
	MaxParticipants   *int32 `db:"max_participants" json:"max_participants"`
}

// Single Event Statistics --
func (q *Queries) GetEventStatistics(ctx context.Context, id uuid.UUID) (GetEventStatisticsRow, error) {
	row := q.db.QueryRow(ctx, getEventStatistics, id)
	var i GetEventStatisticsRow
	err := row.Scan(&i.ParticipantsCount, &i.VolunteersCount, &i.MaxParticipants)
	return i, err
}

const getEventVolunteersByAge = `-- name: GetEventVolunteersByAge :many
SELECT
    CASE
        WHEN age BETWEEN 0 AND 10 THEN '0-10'
        WHEN age BETWEEN 11 AND 20 THEN '11-20'
        WHEN age BETWEEN 21 AND 30 THEN '21-30'
        WHEN age BETWEEN 31 AND 40 THEN '31-40'
        WHEN age BETWEEN 41 AND 50 THEN '41-50'
        WHEN age BETWEEN 51 AND 60 THEN '51-60'
        ELSE '61+'
    END AS age_range,
    COUNT(*)::INT AS count
FROM (
    SELECT
        EXTRACT(YEAR FROM AGE(to_date(COALESCE(v_hkid.date_of_birth, v_cnid.date_of_birth), 'YYYY-MM-DD'))) AS age
    FROM event_volunteer_applications eva
    JOIN users u ON eva.user_id = u.id
    LEFT JOIN user_verification_requests uvr ON u.id = uvr.user_id AND uvr.id = (
        SELECT id FROM user_verification_requests
        WHERE user_id = u.id
          AND status = 'approved'
          AND verification_type IN ('hk_id_card', 'mainland_china_id_card')
        ORDER BY submitted_at DESC
        LIMIT 1
    )
    LEFT JOIN verification_hk_id_cards v_hkid ON uvr.id = v_hkid.verification_request_id
    LEFT JOIN verification_mainland_china_id_cards v_cnid ON uvr.id = v_cnid.verification_request_id
    WHERE eva.event_id = $1
    AND eva.status = 'approved'
    AND COALESCE(v_hkid.date_of_birth, v_cnid.date_of_birth) IS NOT NULL
) AS ages
GROUP BY age_range
ORDER BY age_range
`

type GetEventVolunteersByAgeRow struct {
	AgeRange string `db:"age_range" json:"age_range"`
	Count    int32  `db:"count" json:"count"`
}

func (q *Queries) GetEventVolunteersByAge(ctx context.Context, eventID uuid.UUID) ([]GetEventVolunteersByAgeRow, error) {
	rows, err := q.db.Query(ctx, getEventVolunteersByAge, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventVolunteersByAgeRow{}
	for rows.Next() {
		var i GetEventVolunteersByAgeRow
		if err := rows.Scan(&i.AgeRange, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventVolunteersByDate = `-- name: GetEventVolunteersByDate :many
SELECT
    DATE(created_at)::TEXT as date,
    COUNT(*)::INT AS count
FROM event_volunteer_applications
WHERE event_id = $1 AND status = 'approved'
GROUP BY DATE(created_at)
ORDER BY DATE(created_at)
`

type GetEventVolunteersByDateRow struct {
	Date  string `db:"date" json:"date"`
	Count int32  `db:"count" json:"count"`
}

func (q *Queries) GetEventVolunteersByDate(ctx context.Context, eventID uuid.UUID) ([]GetEventVolunteersByDateRow, error) {
	rows, err := q.db.Query(ctx, getEventVolunteersByDate, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventVolunteersByDateRow{}
	for rows.Next() {
		var i GetEventVolunteersByDateRow
		if err := rows.Scan(&i.Date, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventVolunteersByGender = `-- name: GetEventVolunteersByGender :many
SELECT
    COALESCE(LOWER(gender), 'undefined') as gender,
    COUNT(*)::INT AS count
FROM (
    SELECT
        COALESCE(v_hkid.sex, v_cnid.sex) as gender
    FROM event_volunteer_applications eva
    JOIN users u ON eva.user_id = u.id
    LEFT JOIN user_verification_requests uvr ON u.id = uvr.user_id AND uvr.id = (
        SELECT id FROM user_verification_requests
        WHERE user_id = u.id
          AND status = 'approved'
          AND verification_type IN ('hk_id_card', 'mainland_china_id_card')
        ORDER BY submitted_at DESC
        LIMIT 1
    )
    LEFT JOIN verification_hk_id_cards v_hkid ON uvr.id = v_hkid.verification_request_id
    LEFT JOIN verification_mainland_china_id_cards v_cnid ON uvr.id = v_cnid.verification_request_id
    WHERE eva.event_id = $1
    AND eva.status = 'approved'
) as genders
GROUP BY gender
`

type GetEventVolunteersByGenderRow struct {
	Gender interface{} `db:"gender" json:"gender"`
	Count  int32       `db:"count" json:"count"`
}

func (q *Queries) GetEventVolunteersByGender(ctx context.Context, eventID uuid.UUID) ([]GetEventVolunteersByGenderRow, error) {
	rows, err := q.db.Query(ctx, getEventVolunteersByGender, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventVolunteersByGenderRow{}
	for rows.Next() {
		var i GetEventVolunteersByGenderRow
		if err := rows.Scan(&i.Gender, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOrganizationEventCategoryStats = `-- name: GetOrganizationEventCategoryStats :many
SELECT
    et.name_en,
    et.name_zh_hk,
    et.name_zh_cn,
    COUNT(DISTINCT e.id) AS total_events,
    COUNT(er.id) FILTER (WHERE er.status = 'registered' OR er.status = 'attended') AS total_registrations,
    COUNT(er.id) FILTER (WHERE er.status = 'attended') AS total_attendees
FROM event_tags et
JOIN event_event_tags eet ON et.id = eet.event_tag_id
JOIN events e ON eet.event_id = e.id
LEFT JOIN event_registrations er ON e.id = er.event_id
WHERE
    e.organization_id = $1
    AND ($2::TIMESTAMPTZ IS NULL OR e.start_time >= $2::TIMESTAMPTZ)
    AND ($3::TIMESTAMPTZ IS NULL OR e.start_time <= $3::TIMESTAMPTZ)
    AND e.status = 'published'
GROUP BY et.id, et.name_en, et.name_zh_hk, et.name_zh_cn
ORDER BY total_events DESC
`

type GetOrganizationEventCategoryStatsParams struct {
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	StartDate      *time.Time `db:"start_date" json:"start_date"`
	EndDate        *time.Time `db:"end_date" json:"end_date"`
}

type GetOrganizationEventCategoryStatsRow struct {
	NameEn             string `db:"name_en" json:"name_en"`
	NameZhHk           string `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn           string `db:"name_zh_cn" json:"name_zh_cn"`
	TotalEvents        int64  `db:"total_events" json:"total_events"`
	TotalRegistrations int64  `db:"total_registrations" json:"total_registrations"`
	TotalAttendees     int64  `db:"total_attendees" json:"total_attendees"`
}

func (q *Queries) GetOrganizationEventCategoryStats(ctx context.Context, arg GetOrganizationEventCategoryStatsParams) ([]GetOrganizationEventCategoryStatsRow, error) {
	rows, err := q.db.Query(ctx, getOrganizationEventCategoryStats, arg.OrganizationID, arg.StartDate, arg.EndDate)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetOrganizationEventCategoryStatsRow{}
	for rows.Next() {
		var i GetOrganizationEventCategoryStatsRow
		if err := rows.Scan(
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.TotalEvents,
			&i.TotalRegistrations,
			&i.TotalAttendees,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOrganizationTopEventsByParticipantCount = `-- name: GetOrganizationTopEventsByParticipantCount :many
SELECT
    e.id,
    e.title,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'registered') AS participants
FROM events e
WHERE
    e.organization_id = $1
    AND (
        $2::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            ($3::TIMESTAMPTZ IS NOT NULL AND e.start_time >= $2::TIMESTAMPTZ AND e.start_time <= $3::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            ($3::TIMESTAMPTZ IS NULL AND e.start_time >= $2::TIMESTAMPTZ)
        )
    )
    AND e.status = 'published'
ORDER BY participants DESC
LIMIT $4
`

type GetOrganizationTopEventsByParticipantCountParams struct {
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	StartDate      *time.Time `db:"start_date" json:"start_date"`
	EndDate        *time.Time `db:"end_date" json:"end_date"`
	Lim            int32      `db:"lim" json:"lim"`
}

type GetOrganizationTopEventsByParticipantCountRow struct {
	ID           uuid.UUID `db:"id" json:"id"`
	Title        string    `db:"title" json:"title"`
	Participants int64     `db:"participants" json:"participants"`
}

func (q *Queries) GetOrganizationTopEventsByParticipantCount(ctx context.Context, arg GetOrganizationTopEventsByParticipantCountParams) ([]GetOrganizationTopEventsByParticipantCountRow, error) {
	rows, err := q.db.Query(ctx, getOrganizationTopEventsByParticipantCount,
		arg.OrganizationID,
		arg.StartDate,
		arg.EndDate,
		arg.Lim,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetOrganizationTopEventsByParticipantCountRow{}
	for rows.Next() {
		var i GetOrganizationTopEventsByParticipantCountRow
		if err := rows.Scan(&i.ID, &i.Title, &i.Participants); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTopEventsByParticipantCount = `-- name: GetTopEventsByParticipantCount :many
SELECT
    e.id,
    e.title,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'registered') AS participants
FROM events e
WHERE
    (
        $1::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            ($2::TIMESTAMPTZ IS NOT NULL AND e.start_time >= $1::TIMESTAMPTZ AND e.start_time <= $2::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            ($2::TIMESTAMPTZ IS NULL AND e.start_time >= $1::TIMESTAMPTZ)
        )
    )
    AND e.status = 'published'
ORDER BY participants DESC
LIMIT $3
`

type GetTopEventsByParticipantCountParams struct {
	StartDate *time.Time `db:"start_date" json:"start_date"`
	EndDate   *time.Time `db:"end_date" json:"end_date"`
	Lim       int32      `db:"lim" json:"lim"`
}

type GetTopEventsByParticipantCountRow struct {
	ID           uuid.UUID `db:"id" json:"id"`
	Title        string    `db:"title" json:"title"`
	Participants int64     `db:"participants" json:"participants"`
}

func (q *Queries) GetTopEventsByParticipantCount(ctx context.Context, arg GetTopEventsByParticipantCountParams) ([]GetTopEventsByParticipantCountRow, error) {
	rows, err := q.db.Query(ctx, getTopEventsByParticipantCount, arg.StartDate, arg.EndDate, arg.Lim)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetTopEventsByParticipantCountRow{}
	for rows.Next() {
		var i GetTopEventsByParticipantCountRow
		if err := rows.Scan(&i.ID, &i.Title, &i.Participants); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
