package token

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"os"
	"sync"
	"time"

	"Membership-SAAS-System-Backend/db" // CORRECTED Path to your sqlc generated db package

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log" // Added for logging
)

// AppClaims defines the custom claims for our JWTs.
type AppClaims struct {
	UserID uuid.UUID `json:"user_id"`
	Role   string    `json:"role"` // Added Role
	jwt.RegisteredClaims
}

// ReAuthClaims defines the claims for a short-lived re-authentication token.
type ReAuthClaims struct {
	UserID  uuid.UUID `json:"user_id"`
	Purpose string    `json:"purpose"`
	jwt.RegisteredClaims
}

type JWTConfig struct {
	AccessTokenSecret      []byte
	RefreshTokenSecret     []byte
	AccessTokenDuration    time.Duration
	RefreshTokenDuration   time.Duration
	RefreshSessionDuration time.Duration
}

var (
	jwtConfig      *JWTConfig
	jwtConfigError error
	once           sync.Once
)

// LoadJWTConfig loads configuration from environment variables once.
// It stores any error encountered during loading, to be returned by GetJWTConfig.
func LoadJWTConfig() {
	once.Do(func() {
		accessSecret := os.Getenv("ACCESS_TOKEN_SECRET")
		refreshSecret := os.Getenv("REFRESH_TOKEN_SECRET")

		if accessSecret == "" {
			jwtConfigError = errors.New("ACCESS_TOKEN_SECRET environment variable not set")
			return
		}
		if refreshSecret == "" {
			jwtConfigError = errors.New("REFRESH_TOKEN_SECRET environment variable not set")
			return
		}

		accessDurationStr := os.Getenv("ACCESS_TOKEN_DURATION")
		if accessDurationStr == "" {
			accessDurationStr = "15m" // Default duration
		}
		accessDur, err := time.ParseDuration(accessDurationStr)
		if err != nil {
			jwtConfigError = fmt.Errorf("failed to parse ACCESS_TOKEN_DURATION: %w", err)
			return
		}

		refreshDurationStr := os.Getenv("REFRESH_TOKEN_DURATION")
		if refreshDurationStr == "" {
			refreshDurationStr = "7d" // Default duration (7 days)
		}
		refreshDur, err := time.ParseDuration(refreshDurationStr)
		if err != nil {
			jwtConfigError = fmt.Errorf("failed to parse REFRESH_TOKEN_DURATION: %w", err)
			return
		}

		refreshSessionDurationStr := os.Getenv("REFRESH_SESSION_DURATION")
		if refreshSessionDurationStr == "" {
			refreshSessionDurationStr = "2160h" // Default session duration (90 days * 24 hours)
		}
		refreshSessDur, err := time.ParseDuration(refreshSessionDurationStr)
		if err != nil {
			jwtConfigError = fmt.Errorf("failed to parse REFRESH_SESSION_DURATION: %w", err)
			return
		}

		jwtConfig = &JWTConfig{
			AccessTokenSecret:      []byte(accessSecret),
			RefreshTokenSecret:     []byte(refreshSecret),
			AccessTokenDuration:    accessDur,
			RefreshTokenDuration:   refreshDur,
			RefreshSessionDuration: refreshSessDur,
		}
	})
}

// GetJWTConfig returns the loaded JWT configuration or an error if loading failed or not loaded.
func GetJWTConfig() (*JWTConfig, error) {
	// Call LoadJWTConfig to ensure it has been attempted.
	// once.Do inside LoadJWTConfig handles multiple calls safely.
	LoadJWTConfig()

	if jwtConfig == nil {
		if jwtConfigError != nil {
			return nil, fmt.Errorf("JWT configuration loading failed: %w", jwtConfigError)
		}
		// This case should ideally not be reached if LoadJWTConfig sets jwtConfigError on failure.
		return nil, errors.New("JWT configuration is nil after loading attempt without specific error. Check LoadJWTConfig logic.")
	}
	return jwtConfig, nil
}

// GenerateAccessToken creates a new JWT access token for the given user ID.
// It now fetches the user's is_staff status from the database.
func GenerateAccessToken(ctx context.Context, queries *db.Queries, userID uuid.UUID) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config for access token: %w", err)
	}
	if queries == nil {
		return "", errors.New("database queries instance is nil for GenerateAccessToken")
	}

	// Fetch user to get IsStaff status
	user, err := queries.GetUserByID(ctx, userID) // Assuming GetUserByID returns the full User struct including IsStaff
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Msg("Failed to get user by ID for IsStaff claim")
		// Decide if token generation should fail or proceed with IsStaff: false
		// For security, it's better to fail if user lookup fails for an essential claim.
		return "", fmt.Errorf("failed to retrieve user details for token generation: %w", err)
	}

	expiresAt := time.Now().Add(config.AccessTokenDuration)
	claims := AppClaims{
		UserID: userID,
		Role:   string(user.Role), // Populate Role from the fetched user record
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaas",
			Subject:   userID.String(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(config.AccessTokenSecret)
}

// GenerateAccessTokenWithCustomExpiry generates a new JWT access token for a given user ID with a custom expiry.
// This is primarily intended for testing purposes. It will also fetch IsStaff.
func GenerateAccessTokenWithCustomExpiry(ctx context.Context, queries *db.Queries, userID uuid.UUID, expiryDuration time.Duration) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config for custom expiry access token: %w", err)
	}
	if queries == nil {
		return "", errors.New("database queries instance is nil for GenerateAccessTokenWithCustomExpiry")
	}

	user, err := queries.GetUserByID(ctx, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Msg("Failed to get user by ID for IsStaff claim in custom expiry token")
		return "", fmt.Errorf("failed to retrieve user details for custom expiry token generation: %w", err)
	}

	expiresAt := time.Now().Add(expiryDuration)
	claims := AppClaims{
		UserID: userID,
		Role:   string(user.Role),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaaS-TestToken",
			Subject:   userID.String(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(config.AccessTokenSecret)
}

// HashToken computes the SHA256 hash of a token string.
func HashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// GenerateRefreshToken creates a new JWT refresh token for the given user ID.
// It now also fetches user's IsStaff status.
func GenerateRefreshToken(ctx context.Context, queries *db.Queries, userID uuid.UUID) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config for refresh token: %w", err)
	}
	if queries == nil {
		return "", errors.New("database queries instance is nil for GenerateRefreshToken")
	}

	user, err := queries.GetUserByID(ctx, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Msg("Failed to get user by ID for IsStaff claim in refresh token")
		return "", fmt.Errorf("failed to retrieve user details for refresh token generation: %w", err)
	}

	expiryTime := time.Now().Add(config.RefreshTokenDuration)
	claims := AppClaims{
		UserID: userID,
		Role:   string(user.Role), // Populate Role
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiryTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaaS-Refresh",
			Subject:   userID.String(),
			ID:        uuid.NewString(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString(config.RefreshTokenSecret)
	if err != nil {
		return "", fmt.Errorf("failed to sign refresh token: %w", err)
	}

	hashedToken := HashToken(signedToken)

	_, err = queries.CreateRefreshToken(ctx, db.CreateRefreshTokenParams{
		UserID:    userID,
		TokenHash: hashedToken,
		ExpiresAt: expiryTime,
	})
	if err != nil {
		return "", fmt.Errorf("failed to store refresh token: %w", err)
	}

	return signedToken, nil
}

// GenerateReAuthToken creates a short-lived JWT for re-authentication purposes.
func GenerateReAuthToken(userID uuid.UUID, purpose string) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config for re-auth token: %w", err)
	}

	// Re-auth tokens are short-lived, e.g., 5 minutes.
	expiresAt := time.Now().Add(5 * time.Minute)
	claims := ReAuthClaims{
		UserID:  userID,
		Purpose: purpose,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaaS-ReAuth",
			Subject:   userID.String(),
			ID:        uuid.NewString(),
		},
	}
	// A re-auth token should use the same strong secret as the access token.
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(config.AccessTokenSecret)
}

// ParseToken validates the given token string and returns the claims if valid.
func ParseToken(tokenString string, secret []byte) (*AppClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &AppClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Don't forget to validate the alg is what you expect:
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return secret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*AppClaims); ok && token.Valid {
		return claims, nil
	}
	return nil, errors.New("invalid token")
}

// ParseReAuthToken validates a re-authentication token.
func ParseReAuthToken(tokenString string) (*ReAuthClaims, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get JWT config for parsing re-auth token: %w", err)
	}

	token, err := jwt.ParseWithClaims(tokenString, &ReAuthClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return config.AccessTokenSecret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse re-auth token: %w", err)
	}

	if claims, ok := token.Claims.(*ReAuthClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid re-auth token")
}

// TODO: Add ParseToken function for validating tokens when protecting routes.
