package handlers

import (
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services"
	"Membership-SAAS-System-Backend/internal/utils"
	"errors"
	"net/http"
	"strings"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

const (
	DefaultVolAppPage     = 1
	DefaultVolAppPageSize = 20
	MaxVolAppPageSize     = 100
)

// VolunteerApplicationHandler handles HTTP requests for volunteer applications.
type VolunteerApplicationHandler struct {
	eventService services.EventService
}

// NewVolunteerApplicationHandler creates a new VolunteerApplicationHandler.
func NewVolunteerApplicationHandler(eventService services.EventService) *VolunteerApplicationHandler {
	return &VolunteerApplicationHandler{eventService: eventService}
}

// HandleListPendingReviewVolunteerApplicationsForEvent godoc
// @Summary List pending volunteer applications for an event
// @Description Retrieves a paginated list of volunteer applications with 'pending' status for a specific event.
// @Tags Events VolunteerApplications
// @Accept json
// @Produce json
// @Param event_id path string true "Event ID (UUID format)" format(uuid)
// @Param page query int false "Page number for pagination" default(1) minimum(1)
// @Param page_size query int false "Number of items per page" default(20) minimum(1) maximum(100)
// @Success 200 {object} payloads.PaginatedResponse{data=[]db.ListPendingReviewEventVolunteerApplicationsForEventRow} "Successfully retrieved applications"
// @Failure 400 {object} utils.ErrorResponse "Invalid request parameters (e.g., invalid UUID, non-positive page/page_size)"
// @Failure 404 {object} utils.ErrorResponse "Event not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /events/{event_id}/event-applications/pending-review [get]
// @Security BearerAuth
func (h *VolunteerApplicationHandler) HandleListPendingReviewVolunteerApplicationsForEvent(c echo.Context) error {
	eventIDStr := c.Param("event_id")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid Event ID format", err)
	}

	page := utils.ParseQueryInt(c, "page", DefaultVolAppPage)
	pageSize := utils.ParseQueryInt(c, "page_size", DefaultVolAppPageSize)

	if page <= 0 {
		page = DefaultVolAppPage
	}
	if pageSize <= 0 {
		pageSize = DefaultVolAppPageSize
	} else if pageSize > MaxVolAppPageSize {
		pageSize = MaxVolAppPageSize
	}

	apps, totalCount, err := h.eventService.ListPendingReviewVolunteerApplicationsForEvent(c.Request().Context(), eventID, int32(page), int32(pageSize))
	if err != nil {
		if errors.Is(err, services.ErrEventNotFound) { // Use errors.Is for specific service errors
			return utils.HandleError(c, http.StatusNotFound, "Event not found or no pending applications for this event", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Str("eventID", eventIDStr).Msg("Failed to list pending volunteer applications for event")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve applications", err)
	}

	paginatedResponse := payloads.NewPaginatedResponse(apps, int32(page), int32(pageSize), totalCount)
	return c.JSON(http.StatusOK, paginatedResponse)
}

// HandleListPendingReviewVolunteerApplicationsForOrganization godoc
// @Summary List pending volunteer applications for an organization
// @Description Retrieves a paginated list of 'pending' volunteer applications for all events within a given organization.
// @Tags Organizations VolunteerApplications
// @Accept json
// @Produce json
// @Param organization_id path string true "Organization ID (UUID format)" format(uuid)
// @Param page query int false "Page number for pagination" default(1) minimum(1)
// @Param page_size query int false "Number of items per page" default(20) minimum(1) maximum(100)
// @Success 200 {object} payloads.PaginatedResponse{data=[]db.ListPendingReviewEventVolunteerApplicationsForOrganizationRow} "Successfully retrieved applications"
// @Failure 400 {object} utils.ErrorResponse "Invalid request parameters"
// @Failure 404 {object} utils.ErrorResponse "Organization not found or no pending applications"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /organizations/{organization_id}/event-applications/pending-review [get]
// @Security BearerAuth // Placeholder for org admin/manager auth
func (h *VolunteerApplicationHandler) HandleListPendingReviewVolunteerApplicationsForOrganization(c echo.Context) error {
	orgIDStr := c.Param("organization_id")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid Organization ID format", err)
	}

	page := utils.ParseQueryInt(c, "page", DefaultVolAppPage)
	pageSize := utils.ParseQueryInt(c, "page_size", DefaultVolAppPageSize)

	if page <= 0 {
		page = DefaultVolAppPage
	}
	if pageSize <= 0 {
		pageSize = DefaultVolAppPageSize
	} else if pageSize > MaxVolAppPageSize {
		pageSize = MaxVolAppPageSize
	}

	apps, totalCount, err := h.eventService.ListPendingReviewVolunteerApplicationsForOrganization(c.Request().Context(), orgID, int32(page), int32(pageSize))
	if err != nil {
		// TODO: Define and use a specific ErrOrganizationNotFound in services layer
		if strings.Contains(strings.ToLower(err.Error()), "organization not found") { // Temporary check
			return utils.HandleError(c, http.StatusNotFound, "Organization not found or no pending applications for this organization", err)
		}
		log.Ctx(c.Request().Context()).Error().Err(err).Str("orgID", orgIDStr).Msg("Failed to list pending volunteer applications for organization")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve applications", err)
	}

	paginatedResponse := payloads.NewPaginatedResponse(apps, int32(page), int32(pageSize), totalCount)
	return c.JSON(http.StatusOK, paginatedResponse)
}

// HandleListAllPendingReviewVolunteerApplications godoc
// @Summary List all pending volunteer applications (System Admin)
// @Description Retrieves a paginated list of all 'pending' volunteer applications across the system. For System Admin use.
// @Tags Admin VolunteerApplications
// @Accept json
// @Produce json
// @Param page query int false "Page number for pagination" default(1) minimum(1)
// @Param page_size query int false "Number of items per page" default(20) minimum(1) maximum(100)
// @Success 200 {object} payloads.PaginatedResponse{data=[]db.ListAllPendingReviewEventVolunteerApplicationsRow} "Successfully retrieved applications"
// @Failure 400 {object} utils.ErrorResponse "Invalid request parameters"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /admin/event-applications/pending-review [get]
// @Security BearerAuth // Placeholder for system admin auth
func (h *VolunteerApplicationHandler) HandleListAllPendingReviewVolunteerApplications(c echo.Context) error {
	page := utils.ParseQueryInt(c, "page", DefaultVolAppPage)
	pageSize := utils.ParseQueryInt(c, "page_size", DefaultVolAppPageSize)

	if page <= 0 {
		page = DefaultVolAppPage
	}
	if pageSize <= 0 {
		pageSize = DefaultVolAppPageSize
	} else if pageSize > MaxVolAppPageSize {
		pageSize = MaxVolAppPageSize
	}

	apps, totalCount, err := h.eventService.ListAllPendingReviewVolunteerApplications(c.Request().Context(), int32(page), int32(pageSize))
	if err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to list all pending volunteer applications")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve applications", err)
	}

	paginatedResponse := payloads.NewPaginatedResponse(apps, int32(page), int32(pageSize), totalCount)
	return c.JSON(http.StatusOK, paginatedResponse)
}
