// @title Membership SAAS System API
// @version 1.0
// @description This is the API for the Membership SAAS System backend.
// @termsOfService http://example.com/terms/
// @contact.name API Support
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>
// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html
// @host localhost:8080
// @BasePath /api/v1
// @schemes http https
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.
package main

import (
	"context" // Added for errors.Is, though primarily used in authn package now
	// Required for database/sql compatible NopCloser

	"errors"
	"fmt"

	// "log" // Replaced by zerolog
	"net/http"
	"net/url" // For parsing DATABASE_URL
	"os"
	"strings"
	"time" // For retry logic

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/authn" // Import the new authn package

	// Added for new middleware
	"Membership-SAAS-System-Backend/internal/services" // Added for OrganizationService

	// Added for new handlers
	"Membership-SAAS-System-Backend/internal/utils" // Added for validator functions
	// For RequestValidator & HandleError
	"Membership-SAAS-System-Backend/internal/token"          // Import the new token package
	"Membership-SAAS-System-Backend/internal/twilio_service" // Import new twilio_service

	// Import middleware package
	"Membership-SAAS-System-Backend/internal/middleware" // Import middleware package

	"github.com/go-playground/validator/v10" // Import for validator
	// Added for NewClaimsFunc
	"github.com/google/uuid"

	// Alias to avoid conflict if needed, though echo-jwt/v4 uses v5
	"Membership-SAAS-System-Backend/internal/handlers"

	"github.com/golang-jwt/jwt/v5"
	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/pgx/v5" // pgx/v5 driver
	_ "github.com/golang-migrate/migrate/v4/source/file"     // file source driver
	"github.com/jackc/pgx/v5"                                // For pgx.Connect
	"github.com/jackc/pgx/v5/pgconn"                         // For pgconn.PgError
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"                // Added for loading .env file
	echojwt "github.com/labstack/echo-jwt/v4" // Import echo-jwt
	"github.com/labstack/echo/v4"
	echomiddleware "github.com/labstack/echo/v4/middleware" // Standard Echo middleware
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	oed "github.com/kohkimakimoto/echo-openapidocs" // For serving OpenAPI 3.0 UI

	"Membership-SAAS-System-Backend/internal/scheduler" // Import the scheduler package
	// Add cron import
)

var (
	pool                     *pgxpool.Pool
	queries                  *db.Queries
	twilioClient             *twilio_service.TwilioService
	authnService             *authn.AuthnService
	orgService               *services.OrganizationService
	userVerificationService  *services.UserVerificationService
	userService              *services.UserService
	volunteerService         *services.VolunteerService
	notificationService      services.NotificationService
	eventService             services.EventService
	eventRegistrationService services.EventRegistrationService
	// Handlers
	userHandler              *handlers.UserHandler
	userVerificationHandler  *handlers.UserVerificationHandler
	volunteerHandler         *handlers.VolunteerHandler
	eventHandler             *handlers.EventHandler
	eventRegistrationHandler *handlers.EventRegistrationHandler
	requestValidator         *utils.RequestValidator

	// Content Management
	postService     *services.PostService
	resourceService *services.ResourceService
	postHandler     *handlers.PostHandler
	resourceHandler *handlers.ResourceHandler
	// New Handler for Volunteer Applications
	volunteerApplicationHandler *handlers.VolunteerApplicationHandler

	// Global variable for base URL (consider a config struct for better practice)
	appBaseURL string

	// Instantiate PostTagService and PostTagHandler
	postTagService *services.PostTagService
	postTagHandler *handlers.PostTagHandler

	// Scheduler Service
	schedulerService *scheduler.SchedulerService // Changed type to scheduler.SchedulerService
)

// CustomValidator wraps the go-playground/validator and implements echo.Validator.
type CustomValidator struct {
	validator *validator.Validate
}

// Validate implements the echo.Validator interface.
func (cv *CustomValidator) Validate(i interface{}) error {
	if err := cv.validator.Struct(i); err != nil {
		return err
	}
	return nil
}

// ZerologLoggerContext creates a middleware that injects the global zerolog logger
// into the request's context.
func ZerologLoggerContext(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		logger := log.Logger

		ctxWithLogger := logger.WithContext(c.Request().Context())
		newReq := c.Request().WithContext(ctxWithLogger)
		c.SetRequest(newReq)
		return next(c)
	}
}

func parseDatabaseURL(dbURL string) (dbName string, adminDBURL string, err error) {
	parsedURL, err := url.Parse(dbURL)
	if err != nil {
		return "", "", fmt.Errorf("failed to parse DATABASE_URL: %w", err)
	}
	dbName = strings.TrimPrefix(parsedURL.Path, "/")
	if dbName == "" {
		return "", "", errors.New("database name missing in DATABASE_URL")
	}
	parsedURL.Path = "postgres"
	adminDBURL = parsedURL.String()
	return dbName, adminDBURL, nil
}

func handleDatabaseSetup(dbURL string) {
	dbName, adminDBURL, err := parseDatabaseURL(dbURL)
	if err != nil {
		log.Fatal().Err(err).Msg("Error parsing DATABASE_URL for migration setup")
	}
	// By default, assume set to true in .env file
	autoMigrateReset := os.Getenv("AUTO_MIGRATE_RESET_DB")
	if autoMigrateReset == "true" {
		log.Info().Str("database_name", dbName).Msg("AUTO_MIGRATE_RESET_DB is true. Attempting to drop and recreate database")
		ctx := context.Background()
		adminPgxConn, err := pgx.Connect(ctx, adminDBURL)
		if err != nil {
			log.Fatal().Err(err).Str("admin_db_url", adminDBURL).Msg("Failed to connect to admin database for reset using pgx")
		}
		defer adminPgxConn.Close(ctx)
		dropDBSQL := fmt.Sprintf(`DROP DATABASE IF EXISTS "%s" WITH (FORCE);`, dbName)
		_, err = adminPgxConn.Exec(ctx, dropDBSQL)
		if err != nil {
			log.Fatal().Err(err).Str("database_name", dbName).Msg("Failed to drop database")
		}
		log.Info().Str("database_name", dbName).Msg("Database dropped successfully or did not exist.")
		createDBSQL := fmt.Sprintf(`CREATE DATABASE "%s";`, dbName)
		_, err = adminPgxConn.Exec(ctx, createDBSQL)
		if err != nil {
			log.Fatal().Err(err).Str("database_name", dbName).Msg("Failed to create database")
		}
		log.Info().Str("database_name", dbName).Msg("Database created successfully.")
	}
	log.Info().Msg("Attempting to run database migrations...")
	migrationsPath := "file://db/migrations"
	migrateDSN := strings.Replace(dbURL, "postgres://", "pgx5://", 1)
	log.Info().Str("migrate_dsn", migrateDSN).Msg("Using DSN for migrations")
	m, err := migrate.New(migrationsPath, migrateDSN)
	if err != nil {
		log.Fatal().Err(err).Str("dsn", migrateDSN).Str("migrations_path", migrationsPath).Msg("Failed to initialize migrate instance")
	}
	maxRetries := 5
	for i := 0; i < maxRetries; i++ {
		err = m.Up()
		if err == nil {
			log.Info().Msg("Database migrations applied successfully.")
			break
		}
		if errors.Is(err, migrate.ErrNoChange) {
			log.Info().Msg("No database migrations to apply.")
			break
		}
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "3D000" {
			log.Warn().Err(err).Str("database_name", dbName).Int("attempt", i+1).Int("max_attempts", maxRetries).Int("retry_in_seconds", (i+1)*2).Msgf("Migration failed as database might not be fully ready. Retrying...")
			time.Sleep(time.Duration(i+1) * 2 * time.Second)
			continue
		}
		if i == maxRetries-1 {
			log.Fatal().Err(err).Int("retries", maxRetries).Msg("Failed to apply database migrations after multiple retries")
		}
		log.Warn().Err(err).Int("attempt", i+1).Int("max_attempts", maxRetries).Int("retry_in_seconds", (i+1)*2).Msg("Migration attempt failed. Retrying...")
		time.Sleep(time.Duration(i+1) * 2 * time.Second)
	}
}

func main() {
	envLoadErr := godotenv.Load()
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnixMs
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr, TimeFormat: time.RFC3339Nano})
	if envLoadErr != nil {
		log.Warn().Msg(".env file not found, relying on environment variables directly.")
	}

	token.LoadJWTConfig()
	jwtActualConfig, jwtConfigErr := token.GetJWTConfig()
	if jwtConfigErr != nil {
		log.Fatal().Err(jwtConfigErr).Msg("Failed to load critical JWT configuration")
	}
	log.Info().Msg("JWT configuration loaded.")

	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		log.Fatal().Msg("DATABASE_URL environment variable not set")
	}
	handleDatabaseSetup(dbURL)

	var err error
	pool, err = pgxpool.New(context.Background(), dbURL)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create database connection pool")
	}
	defer pool.Close()
	err = pool.Ping(context.Background())
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to ping database")
	}
	queries = db.New(pool)
	log.Info().Msg("Database connection pool established and queries initialized.")

	// Initialize Twilio Service
	twilioClient, err = twilio_service.NewTwilioService()
	if err != nil {
		log.Warn().Err(err).Msg("Failed to initialize Twilio client or it's not configured. OTP/Notification features may be impaired.")
		twilioClient = nil
	} else if twilioClient != nil {
		log.Info().Msg("Twilio client initialized successfully.")
	}

	// Initialize Notification Service
	notificationService = services.NewNotificationService(twilioClient, queries)
	log.Info().Msg("WebSocket/Notification service initialized.")

	// Initialize Request Validator
	requestValidator = utils.NewRequestValidator()
	log.Info().Msg("Request validator initialized.")

	// Initialize Job Service
	jobService := services.NewJobService(queries, pool)
	log.Info().Msg("Job service initialized.")

	// Initialize Base URL first as some services might need it
	appBaseURL = os.Getenv("APP_BASE_URL")
	if appBaseURL == "" {
		appBaseURL = "http://localhost:8080" // Default if not set
		log.Warn().Str("default", appBaseURL).Msg("APP_BASE_URL not set, using default.")
	} else {
		log.Info().Str("url", appBaseURL).Msg("APP_BASE_URL loaded.")
	}
	appBaseURL = strings.TrimSuffix(appBaseURL, "/")

	// Initialize AuthnService first as other services (like UserService) depend on it.
	orgService = services.NewOrganizationService(queries, pool)

	// Ensure default organization and super admin exist or are created
	superAdminID := ensureDefaultOrgAndSuperAdmin(context.Background(), queries, orgService)

	authnService = authn.NewAuthnService(queries, twilioClient, orgService, superAdminID)
	log.Info().Msg("AuthnService initialized.")

	// Initialize other Services
	userVerificationService = services.NewUserVerificationService(queries, pool, notificationService)
	userService = services.NewUserService(queries, pool, twilioClient, authnService, notificationService, userVerificationService, orgService) // Added orgService
	volunteerService = services.NewVolunteerService(queries, orgService)
	eventService = services.NewEventService(queries, pool, notificationService, jobService)
	log.Info().Msg("EventService initialized.")

	// Create the event registration service with dependencies including the event service
	eventRegService, err := services.NewEventRegistrationService(
		queries,
		pool,
		notificationService,
		jobService,
		eventService,
		appBaseURL,
		30,
		24,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create event registration service")
	}

	eventRegistrationService = eventRegService // Assign to global var
	eventRegistrationHandler = handlers.NewEventRegistrationHandler(eventRegistrationService, requestValidator)
	log.Info().Msg("EventRegistrationHandler initialized.")

	// Initialize PostTagService first as PostService depends on it
	postTagService = services.NewPostTagService(queries)
	log.Info().Msg("PostTagService initialized.")

	// PostService now requires PostTagService
	postService = services.NewPostService(queries, postTagService, pool)
	log.Info().Msg("PostService initialized.")

	resourceService = services.NewResourceService(queries)
	log.Info().Msg("ResourceService initialized.")

	// Initialize Handlers
	userHandler = handlers.NewUserHandler(userService, requestValidator)
	log.Info().Msg("UserHandler initialized.")

	userVerificationHandler = handlers.NewUserVerificationHandler(userVerificationService, requestValidator)
	log.Info().Msg("UserVerificationHandler initialized.")

	volunteerHandler = handlers.NewVolunteerHandler(volunteerService, userVerificationService, orgService)
	log.Info().Msg("VolunteerHandler initialized.")

	eventHandler = handlers.NewEventHandler(eventService, orgService, requestValidator)
	log.Info().Msg("EventHandler initialized.")

	// Initialize PostTagHandler - assuming it only needs PostTagService
	// The error message indicates NewPostTagHandler expects only one argument.
	postTagHandler = handlers.NewPostTagHandler(postTagService)
	log.Info().Msg("PostTagHandler initialized.")

	// PostHandler initialization remains the same
	postHandler = handlers.NewPostHandler(postService, requestValidator, appBaseURL)
	log.Info().Msg("PostHandler initialized.")

	resourceHandler = handlers.NewResourceHandler(resourceService, requestValidator, appBaseURL)
	log.Info().Msg("ResourceHandler initialized.")

	// Initialize VolunteerApplicationHandler
	volunteerApplicationHandler = handlers.NewVolunteerApplicationHandler(eventService) // eventService should already be initialized
	log.Info().Msg("VolunteerApplicationHandler initialized.")

	log.Info().Msg("Handlers initialized.") // General log after all handlers

	// Start Job Worker(s) in background
	// TODO: Make worker count and interval configurable
	go jobService.StartWorker(context.Background(), "worker-1", 5*time.Second, eventService, notificationService)
	log.Info().Msg("Background job worker started.")

	// Initialize Scheduler Service for Event Reminders (plan13.md)
	schedulerService = scheduler.NewSchedulerService(notificationService, queries)
	schedulerService.Start() // Start the scheduler
	log.Info().Msg("New event reminder scheduler service started.")

	e := echo.New()
	e.HideBanner = true
	e.Validator = &CustomValidator{validator: validator.New()} // Use CustomValidator which uses go-playground/validator
	// e.HTTPErrorHandler = utils.CustomHTTPErrorHandler // Uncomment if you have this

	e.Use(ZerologLoggerContext)
	e.Use(echomiddleware.Recover())
	e.Use(echomiddleware.RequestID())
	e.Use(echomiddleware.CORSWithConfig(echomiddleware.CORSConfig{
		AllowOrigins:  []string{"http://localhost:3000", "*"},
		AllowMethods:  []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodPatch, http.MethodOptions},
		AllowHeaders:  []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization},
		ExposeHeaders: []string{"X-Total-Count"},
	}))

	e.GET("/health", healthCheck)
	apiV1 := e.Group("/api/v1")

	// Serve the OpenAPI spec file itself
	apiV1.File("/swagger.yaml", "docs/swagger.yaml")

	// JWT Middleware configuration
	echoJwtConfig := echojwt.Config{
		SigningKey:    []byte(jwtActualConfig.AccessTokenSecret), // Use the secret from loaded config, ensure it's []byte
		NewClaimsFunc: func(c echo.Context) jwt.Claims { return new(token.AppClaims) },
		ContextKey:    "user", // Default context key where claims are stored
		ErrorHandler: func(c echo.Context, err error) error {
			if errors.Is(err, echojwt.ErrJWTMissing) {
				return utils.HandleError(c, http.StatusUnauthorized, "Authentication token is missing.", err)
			}
			return utils.HandleError(c, http.StatusUnauthorized, "Invalid or expired authentication token.", err)
		},
		Skipper: func(c echo.Context) bool {
			// Path for the specific GET endpoint for volunteer application details
			// This path is relative to the group the middleware is applied to.
			// If jwtAuthMiddleware is applied to apiV1 group, this path is correct.
			targetPath := "/organizations/:orgId/events/:eventId/volunteer-applications/:appId"
			if c.Request().Method == http.MethodGet && c.Path() == targetPath {
				log.Ctx(c.Request().Context()).Info().Str("path", c.Path()).Msgf("Skipping JWT authentication for GET %s", targetPath)
				return true // Skip JWT authentication for this specific path and method
			}
			return false // Otherwise, do not skip
		},
	}
	jwtAuthMiddleware := echojwt.WithConfig(echoJwtConfig)

	// Optional JWT Middleware (attempts auth, doesn't fail if no token)
	optionalJwtAuthMiddleware := echojwt.WithConfig(echojwt.Config{
		SigningKey: []byte(jwtActualConfig.AccessTokenSecret), // Ensure []byte
		NewClaimsFunc: func(c echo.Context) jwt.Claims {
			return new(token.AppClaims)
		},
		TokenLookup: "header:Authorization:Bearer ", // Standard lookup
		Skipper: func(c echo.Context) bool {
			// If no Authorization header, or not bearer, skip JWT validation
			authHeader := c.Request().Header.Get(echo.HeaderAuthorization)
			if authHeader == "" {
				return true
			}
			// Check if the Authorization header starts with "Bearer " (case-insensitive)
			if !strings.HasPrefix(strings.ToLower(authHeader), "bearer ") {
				// If it's not a Bearer token, but some other Authorization header,
				// we might want to skip JWT processing too, or log it.
				// For "optional" JWT, if it's not a bearer token, we should skip.
				return true
			}
			// If it is "Bearer ", but empty (e.g. "Bearer "), also skip.
			if strings.TrimSpace(strings.TrimPrefix(strings.ToLower(authHeader), "bearer ")) == "" {
				return true
			}
			return false // Otherwise, attempt to process the token
		},
		ErrorHandler: func(c echo.Context, err error) error {
			log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Optional JWT Middleware: Error encountered")

			// For genuinely malformed tokens, return an error.
			if errors.Is(err, jwt.ErrTokenMalformed) {
				return utils.HandleError(c, http.StatusBadRequest, "Malformed token.", err)
			}

			// If the error is due to an invalid token (expired, signature mismatch, not yet valid etc.),
			// or if it's the generic echojwt.ErrJWTInvalid,
			// we still want to proceed with the request, but without authenticated user claims.
			if errors.Is(err, echojwt.ErrJWTInvalid) || // General invalid token error from echojwt
				errors.Is(err, jwt.ErrTokenExpired) ||
				errors.Is(err, jwt.ErrTokenNotValidYet) ||
				errors.Is(err, jwt.ErrTokenSignatureInvalid) {
				c.Set("user", nil) // Ensure no stale user context
				return nil         // Proceed to the next handler without erroring out
			}

			// For any other unexpected JWT processing errors, log it but still proceed without auth.
			// This makes it truly "optional" in the face of various token issues beyond simple absence.
			log.Ctx(c.Request().Context()).Error().Err(err).Msg("Optional JWT Middleware: Unhandled JWT error, proceeding without auth context.")
			c.Set("user", nil)
			return nil
		},
	})
	log.Info().Msg("Optional JWT auth middleware configured.")

	// Middleware for Admin/Staff role check
	adminRequiredMiddleware := createAdminRequiredMiddleware()
	orgAdminOrSuperAdminRequiredMiddleware := middleware.OrgAdminOrSuperAdminRequired(orgService)

	// Register Routes
	registerAuthnRoutes(apiV1, authnService, jwtAuthMiddleware)
	userSpecificGroup := apiV1.Group("/users")                                          // Create a /api/v1/users group
	registerUserRoutes(userSpecificGroup, userHandler, authnService, jwtAuthMiddleware) // Pass this new group

	log.Info().Msg("Handlers initialized.")

	// Setup organization group
	// NEW SETUP:
	// Group for general /organizations routes (list all, create)
	organizationsBaseGroup := apiV1.Group("/organizations")
	registerOrganizationRoutes(organizationsBaseGroup, authnService, jwtAuthMiddleware, adminRequiredMiddleware)

	// Route for applying to be a volunteer for an org.
	// This logically belongs with org routes, but handler is on VolunteerHandler, not AuthnService.
	apiV1.POST("/organizations/:orgId/volunteer/apply", volunteerHandler.ApplyForVolunteerQualificationHandler, jwtAuthMiddleware)

	registerVerificationRoutes(apiV1, userVerificationHandler, jwtAuthMiddleware, adminRequiredMiddleware)
	registerVolunteerRoutes(apiV1, volunteerHandler, userVerificationHandler, jwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware, createSuperAdminRequiredMiddleware())
	registerAdminUserRoutes(apiV1, userHandler, jwtAuthMiddleware, adminRequiredMiddleware)

	// Admin routes for managing organization managers
	// Path: /api/v1/admin/users/organizations/:orgId/manager
	orgManagerAdminRoutes := apiV1.Group("/admin/users/organizations/:orgId", jwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware)
	orgManagerAdminRoutes.POST("/manager", userHandler.AssignOrganizationManagerHandler)   // Placeholder
	orgManagerAdminRoutes.DELETE("/manager", userHandler.RemoveOrganizationManagerHandler) // Placeholder
	log.Info().Msg("Admin organization manager routes registered under /api/v1/admin/users/organizations/:orgId/manager")

	registerEventRoutes(apiV1, eventHandler, eventService, jwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware, optionalJwtAuthMiddleware)

	// User's event-specific volunteer applications
	apiV1.GET("/users/me/event-volunteer-applications", eventHandler.ListUserEventVolunteerApplications, jwtAuthMiddleware)
	apiV1.GET("/users/me/event-volunteer-applications/:appId", eventHandler.GetUserEventVolunteerApplicationDetailsHandler, jwtAuthMiddleware)

	// User's general volunteer applications and qualifications
	userVolunteerRoutes := apiV1.Group("/users/me/volunteer", jwtAuthMiddleware)
	{
		userVolunteerRoutes.GET("/applications", volunteerHandler.ListUserVolunteerApplicationsHandler)
		userVolunteerRoutes.GET("/applications/:appId", volunteerHandler.GetUserVolunteerApplicationDetailsHandler)
		userVolunteerRoutes.PATCH("/applications/:appId/withdraw", volunteerHandler.WithdrawOrganizationApplicationHandler)
		userVolunteerRoutes.GET("/qualifications", volunteerHandler.ListUserVolunteerQualificationsHandler)
	}

	// New Withdrawal Routes
	apiV1.PATCH("/event/applications/:appId/withdraw", eventHandler.WithdrawEventApplicationHandler, jwtAuthMiddleware)

	// Register WebSocket routes
	registerWebSocketRoutes(apiV1, notificationService, jwtAuthMiddleware)

	// Register Event Statistics Route (Public)
	apiV1.GET("/event-statistics", eventHandler.GetEventStatistics)

	// Register Event related routes (creation, listing, management)
	registerEventRoutes(apiV1, eventHandler, eventService, jwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware, optionalJwtAuthMiddleware)

	// Setup OpenAPI UI using echo-openapidocs
	// This will serve Swagger UI at /swagger-ui/
	// It will fetch the spec from /api/v1/swagger.yaml (which we serve above)
	echoOpenAPIDocsPath := "/api/v1/swagger-ui"
	oed.SwaggerUIDocuments(e, echoOpenAPIDocsPath, oed.SwaggerUIConfig{
		SpecUrl: "/api/v1/swagger.yaml", // Corrected field name
	})

	// --- Ensure Upload Directory Exists ---
	uploadDir := "./uploads/event-media"
	if err := os.MkdirAll(uploadDir, os.ModePerm); err != nil {
		log.Fatal().Err(err).Str("path", uploadDir).Msg("Failed to create upload directory")
	}
	log.Info().Str("path", uploadDir).Msg("Upload directory ensured.")

	// --- Serve Static Files ---
	// Serve files from the 'uploads' directory at the '/uploads' path
	e.Static("/uploads", "uploads")
	log.Info().Msg("Serving static files from ./uploads at /uploads")

	registerEventRegistrationRoutes(apiV1, eventRegistrationHandler, jwtAuthMiddleware, adminRequiredMiddleware, optionalJwtAuthMiddleware)

	// Register Content Routes (Posts, Resources, etc.)
	registerContentRoutes(apiV1, postHandler, resourceHandler, jwtAuthMiddleware, optionalJwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware)

	// Register Volunteer Application Routes
	registerVolunteerApplicationRoutes(apiV1, volunteerApplicationHandler, jwtAuthMiddleware, adminRequiredMiddleware, orgAdminOrSuperAdminRequiredMiddleware)

	// New route for organization-specific event statistics
	apiV1.GET("/organizations/:orgId/event-statistics", eventHandler.GetEventStatisticsForOrganization, jwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware)

	// Instantiate PostTagService and PostTagHandler
	postTagService = services.NewPostTagService(queries)
	postTagHandler = handlers.NewPostTagHandler(postTagService)

	// Register post tag routes
	apiV1.POST("/post-tags", postTagHandler.HandleCreatePostTag, jwtAuthMiddleware, adminRequiredMiddleware)
	apiV1.GET("/post-tags", postTagHandler.HandleListPostTags)
	apiV1.GET("/post-tags/:tagId", postTagHandler.HandleGetPostTag)
	apiV1.PATCH("/post-tags/:tagId", postTagHandler.HandleUpdatePostTag, jwtAuthMiddleware, adminRequiredMiddleware)
	apiV1.DELETE("/post-tags/:tagId", postTagHandler.HandleDeletePostTag, jwtAuthMiddleware, adminRequiredMiddleware)

	// Post-tag association routes (under organization context)
	orgPostsGroup := apiV1.Group("/organizations/:orgId/posts/:postId")
	orgPostsGroup.POST("/tags/:tagId", postTagHandler.HandleAddTagToPost, jwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware)
	orgPostsGroup.DELETE("/tags/:tagId", postTagHandler.HandleRemoveTagFromPost, jwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware)
	orgPostsGroup.GET("/tags", postTagHandler.HandleGetTagsForPost)

	// New public endpoint for listing verification types
	apiV1.GET("/verification-types", handlers.GetVerificationTypes)

	// New public endpoint for listing government funding types
	apiV1.GET("/government-funding-types", handlers.GetGovernmentFundingTypesHandler)

	// Publicly list event tags
	apiV1.GET("/event-tags", eventHandler.ListEventTags)
	apiV1.GET("/event-tags/:tagId", eventHandler.GetEventTag)

	// Global Event Tag routes (e.g. for admin to create/list all tags)
	eventTagsGlobal := apiV1.Group("/event-tags", jwtAuthMiddleware, adminRequiredMiddleware)
	eventTagsGlobal.POST("", eventHandler.CreateEventTag)
	eventTagsGlobal.PATCH("/:tagId", eventHandler.UpdateEventTag)
	eventTagsGlobal.DELETE("/:tagId", eventHandler.DeleteEventTag)

	// Admin route for listing event volunteer applications for a specific organization with filters
	adminOrgEventVolunteerApps := apiV1.Group("/admin/organizations/:orgId", jwtAuthMiddleware, orgAdminOrSuperAdminRequiredMiddleware)
	adminOrgEventVolunteerApps.GET("/event-volunteer-applications", eventHandler.ListOrgEventVolunteerApplicationsHandler)

	// Publicly accessible event routes (no auth needed by default for GET)
	publicEventGroup := apiV1.Group("/events")
	{
		publicEventGroup.GET("", eventHandler.ListPublicEvents)                                // List all public events with filters
		publicEventGroup.GET("/:eventId", eventHandler.GetPublicEventDetails)                  // Get specific public event details
		publicEventGroup.GET("/:eventId/volunteer-count", eventHandler.GetEventVolunteerCount) // Get volunteer application count for an event

		// GET /events/:eventId/registrations - to get registration configuration (public)
		// POST /events/:eventId/registrations - to register for an event (user auth required, handled in handler)
	}

	// Admin Me routes
	adminMeGroup := apiV1.Group("/admin/me", jwtAuthMiddleware, adminRequiredMiddleware)
	adminMeGroup.GET("/organizations", userHandler.GetAdminOwnedOrganizationsHandler)

	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}
	serverAddress := fmt.Sprintf("0.0.0.0:%s", port)
	log.Info().Msgf("Starting server on %s", serverAddress)
	log.Info().Msgf("Swagger UI will be available at /swagger/index.html if docs are generated.")
	log.Info().Msgf("API documentation (swagger.yaml) should be at /api/v1/swagger.yaml if docs are generated and served.")
	if err := e.Start(serverAddress); err != nil && !errors.Is(err, http.ErrServerClosed) {
		log.Fatal().Err(err).Msg("Server failed to start")
	}
}

// healthCheck godoc
// @Summary Health check
// @Description Checks the health of the API.
// @Tags Health
// @Produce json
// @Success 200 {object} map[string]string "OK"
// @Router /health [get]
func healthCheck(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]string{"status": "ok"})
}

// registerAuthnRoutes sets up all authentication related endpoints.
func registerAuthnRoutes(apiV1 *echo.Group, authnSvc *authn.AuthnService, jwtAuthMiddleware echo.MiddlewareFunc) {
	authnRoutes := apiV1.Group("/authn")

	authnRoutes.POST("/phone/check", authnSvc.CheckPhoneHandler)
	authnRoutes.POST("/phone/otp/initiate", authnSvc.InitiatePhoneOTPHandler)
	authnRoutes.POST("/phone/otp/verify", authnSvc.VerifyPhoneOTPHandler)
	authnRoutes.POST("/register/phone/initiate", authnSvc.InitiatePhoneRegistrationHandler)
	authnRoutes.POST("/register/phone/verify", authnSvc.VerifyPhoneRegistrationHandler)
	authnRoutes.POST("/staff/email/check", authnSvc.CheckStaffEmailHandler)
	authnRoutes.POST("/staff/login/initiate", authnSvc.InitiateStaffLoginHandler)
	authnRoutes.POST("/staff/login/verify", authnSvc.VerifyStaffLoginHandler)
	authnRoutes.POST("/token/refresh", authnSvc.RefreshTokenHandler)

	// Authenticated Authn Route
	authnRoutes.POST("/logout", authnSvc.LogoutHandler, jwtAuthMiddleware)
	log.Info().Msg("Authn routes registered.")
}

func registerUserRoutes(group *echo.Group, h *handlers.UserHandler, authnSvc *authn.AuthnService, jwtAuth echo.MiddlewareFunc) {
	// User Profile Endpoints (Authenticated)
	// The 'group' here is already /api/v1/users

	// Current user operations (implicitly /users/me based on how claims are used)
	meGroup := group.Group("/me") // This creates /api/v1/users/me
	meGroup.Use(jwtAuth)          // Apply JWT authentication middleware
	meGroup.GET("", h.GetUserProfileHandler)
	meGroup.PATCH("", h.UpdateUserProfileHandler)
	meGroup.POST("/profile-picture", h.UploadProfilePictureHandler)
	meGroup.POST("/phone/initiate-change", h.InitiatePhoneNumberChangeHandler)
	meGroup.POST("/phone/verify-change", h.VerifyPhoneNumberChangeHandler)
	meGroup.GET("/organizations", h.GetUserOrganizationsHandler)
	meGroup.GET("/stats", h.GetUserStats)     // NEW: GET /users/me/stats
	meGroup.GET("/uuid", h.HandleGetUserUUID) // NEW: GET /users/me/uuid

	// Re-authentication routes for sensitive actions
	meGroup.POST("/reauth/otp/initiate", h.InitiateReAuthOTPHandler)
	meGroup.POST("/reauth/otp/verify", h.VerifyReAuthOTPHandler)

	// User Verification Routes
	meGroup.POST("/verifications", userVerificationHandler.SubmitVerificationRequest)
	meGroup.GET("/verifications", userVerificationHandler.GetUserVerificationRequests)
	meGroup.GET("/verifications/:reqID", userVerificationHandler.GetUserVerificationRequestDetails)
	meGroup.GET("/verifications/documents/:docID", userVerificationHandler.GetUserVerificationDocument)
	meGroup.DELETE("/verifications/:reqID", userVerificationHandler.DeleteUserVerificationData)

	// Add other user-specific routes here
	log.Info().Msg("User routes registered.")
}

func registerOrganizationRoutes(group *echo.Group, authnSvc *authn.AuthnService, jwtAuth echo.MiddlewareFunc, adminRequired echo.MiddlewareFunc) {
	// Routes for creating, listing all orgs (might need admin or just auth)
	group.POST("", authnSvc.CreateOrganizationHandler, jwtAuth) // Create requires auth
	group.GET("", authnSvc.ListOrganizationsHandler)            // List might require just auth, or be public

	// Routes for specific organization by ID
	orgSpecific := group.Group("/:orgId", jwtAuth) // All specific org routes require auth
	orgSpecific.GET("", authnSvc.GetOrganizationByIDHandler)
	orgSpecific.PUT("", authnSvc.UpdateOrganizationHandler)    // Updating requires admin/owner roles within org (checked in handler)
	orgSpecific.DELETE("", authnSvc.DeleteOrganizationHandler) // Deleting requires owner role (checked in handler)

	// New route for uploading organization logo
	orgSpecific.POST("/logo", authnSvc.UploadOrganizationLogoHandler) // jwtAuth is inherited from orgSpecific group

	// Membership routes (relative to /api/v1/organizations/{orgId}/...)
	// These are often on AuthnService because they involve the current authenticated user
	orgSpecific.POST("/join", authnSvc.JoinOrganizationHandler)
	orgSpecific.DELETE("/leave", authnSvc.LeaveOrganizationHandler)

	// List user's own organizations (this is typically under /users/me/organizations)
	// This was defined in main before. If it's handled by AuthnService, adjust:
	// apiV1.GET("/users/me/organizations", authnSvc.ListMyOrganizationsHandler, jwtAuth) -- Assuming ListMyOrganizationsHandler exists on authnSvc
	// For now, keeping distinct from group to match previous structure.
	// This means a separate top-level route or ensure AuthnService has ListMyOrganizationsHandler for /users/me
	log.Info().Msg("Organization routes registered.")
}

func registerVerificationRoutes(apiV1 *echo.Group, h *handlers.UserVerificationHandler, jwtAuth echo.MiddlewareFunc, adminRequired echo.MiddlewareFunc) {
	// User-facing verification routes (already under /users/me/verifications via registerUserRoutes)
	// No, this function is for /api/v1 base, so we define full paths for admin here.

	adminVerificationGroup := apiV1.Group("/admin/verifications", jwtAuth, adminRequired) // Group for admin verification endpoints
	{
		// GET /admin/verifications - New endpoint to list all verifications with filters
		adminVerificationGroup.GET("", h.AdminListUserVerifications)

		// GET /admin/verifications/pending - Moved to /admin/verifications?status=pending if desired, or keep if distinct logic
		// For now, assuming the new endpoint covers this with filters. If distinct logic, uncomment and adjust.
		// adminVerificationGroup.GET("/pending", h.ListPendingVerifications)

		// GET /admin/verifications/:reqID - Existing endpoint to get specific details
		adminVerificationGroup.GET("/:reqID", h.GetVerificationDetailsForAdmin)

		// POST /admin/verifications/:reqID/review - Existing endpoint to review a request
		adminVerificationGroup.POST("/:reqID/review", h.ReviewVerificationRequest)
		// Add PATCH support for the same review endpoint
		adminVerificationGroup.PATCH("/:reqID/review", h.ReviewVerificationRequest)

		// GET /admin/verifications/documents/:docID - Existing endpoint to get a document
		adminVerificationGroup.GET("/documents/:docID", h.GetVerificationDocumentForAdmin)

		// GET /admin/verifications/all - This was the old listing endpoint. The new /admin/verifications (GET "") supersedes it.
		// If AdminListAllVerifications handler is still distinct and needed, it could be mounted here too.
		// For now, assuming new endpoint handles all. If AdminListAllVerifications is to be kept for a specific purpose:
		// adminVerificationGroup.GET("/all", h.AdminListAllVerifications)
	}

	log.Info().Msg("Admin verification routes registered.")
}

func registerVolunteerRoutes(apiV1 *echo.Group, volunteerHandler *handlers.VolunteerHandler, userVerificationHandler *handlers.UserVerificationHandler, jwtAuth echo.MiddlewareFunc, orgAdminOrSuperAdminRequired echo.MiddlewareFunc, superadminRequired echo.MiddlewareFunc) {
	// General volunteer actions (e.g., withdrawing an application)
	// These might not fit neatly under an org-specific path if they use IDs from the token.
	// apiV1.POST("/volunteer/applications/:appId/withdraw", volunteerHandler.WithdrawVolunteerApplication, jwtAuth) //TODO: Implement WithdrawVolunteerApplication

	// Admin-level review of organization-wide volunteer applications
	orgAdminVolunteerGroup := apiV1.Group("/admin/organizations/:orgId/volunteer/applications", jwtAuth, orgAdminOrSuperAdminRequired)
	{
		orgAdminVolunteerGroup.GET("", volunteerHandler.ListOrgVolunteerApplicationsHandler)
		orgAdminVolunteerGroup.GET("/pending", volunteerHandler.ListPendingVolunteerApplicationsForOrgHandler)
		orgAdminVolunteerGroup.GET("/:appId", volunteerHandler.GetVolunteerApplicationDetailsForAdminHandler)
		orgAdminVolunteerGroup.PATCH("/:appId/review", volunteerHandler.ReviewVolunteerApplicationHandler)
	}

	// Admin-level listing of event-specific volunteer applications
	// apiV1.GET("/admin/organizations/:orgId/event-volunteer-applications", volunteerHandler.ListAllEventVolunteerApplicationsForOrg, jwtAuth, adminRequired) // TODO: Implement ListAllEventVolunteerApplicationsForOrg

	// New routes for volunteers/admins to manage user verifications
	volunteersGroup := apiV1.Group("/volunteers", jwtAuth, orgAdminOrSuperAdminRequired) // Protected section for volunteers/admins
	{
		usersGroup := volunteersGroup.Group("/users")
		{
			usersGroup.POST("/verifications", userVerificationHandler.VolunteerSubmitVerificationRequest)
			usersGroup.GET("/verifications", userVerificationHandler.VolunteerGetUserVerificationRequests)
		}
	}
}

func registerWebSocketRoutes(group *echo.Group, notifySvc services.NotificationService, jwtAuth echo.MiddlewareFunc) {
	wsGroup := group.Group("/ws", jwtAuth)
	wsGroup.GET("", notifySvc.ServeWs)
}

func registerEventRoutes(apiV1 *echo.Group, h *handlers.EventHandler, eventSvc services.EventService, jwtAuth echo.MiddlewareFunc, orgAdminOrSuperAdminRequired echo.MiddlewareFunc, optionalJwtAuthMiddleware echo.MiddlewareFunc) {
	eventsGroup := apiV1.Group("/events")
	// Public Event Endpoints
	eventsGroup.GET("", h.ListPublicEvents)               // List all public events with filters
	eventsGroup.GET("/:eventId", h.GetPublicEventDetails) // Get specific public event details

	// Event Registration (User-specific, requires auth)
	eventsGroup.POST("/:eventId/volunteer-applications", h.ApplyForEventVolunteering, jwtAuth) // Apply for volunteering

	// NEW ENDPOINT: List registrations for a specific event (orgId is implicitly optional via path)
	// Mirrors functionality of /api/v1/organizations/:orgId/events/:eventId/registrations
	// Handler h.ListEventRegistrations (EventHandler.ListEventRegistrations) must be able to differentiate
	// based on the presence/absence of an orgId from the path (e.g., c.Param("orgId")).
	// If orgId is not in the path, the endpoint is accessible to authenticated users.
	// The handler is responsible for authorizing access (e.g., for public events or events the user has permission to view)
	// and fetching data accordingly. For staff/admin users, it might list for all relevant orgs or based on broader permissions.
	eventsGroup.GET("/:eventId/registrations", h.ListEventRegistrations, jwtAuth)

	// Organization-specific Event Management
	// Group for routes under /organizations/{orgId}/events
	orgEvents := apiV1.Group("/organizations/:orgId/events", jwtAuth, orgAdminOrSuperAdminRequired) // Requires at least org admin
	orgEvents.POST("", h.CreateEvent)                                                               // POST /organizations/{orgId}/events
	orgEvents.GET("", h.ListOrganizationEvents)                                                     // GET /organizations/{orgId}/events
	orgEvents.PATCH("/:eventId", h.PatchEventDetails)                                               // PATCH /organizations/{orgId}/events/{eventId}
	orgEvents.GET("/:eventId", h.GetOrganizationEventDetails)
	orgEvents.GET("/statistics", h.GetOrganizationEventStatistics)
	orgEvents.GET("/:eventId/statistics", h.GetSpecificEventStatistics)
	// Add other admin-only event endpoints for an organization here
	orgEvents.POST("/:eventId/media", h.AddEventMediaItem)
	orgEvents.DELETE("/:eventId/media/:mediaItemId", h.DeleteEventMediaItem)

	// Event Volunteer Application Management (Admin View for an event)
	// These routes must be nested under a specific event ID.
	eventSpecificVolunteerApps := orgEvents.Group("/:eventId/volunteer-applications")
	eventSpecificVolunteerApps.GET("", h.ListEventVolunteerApplicationsForEvent, jwtAuth, orgAdminOrSuperAdminRequired)
	eventSpecificVolunteerApps.PATCH("/:applicationId/review", h.ReviewEventVolunteerApplication, jwtAuth, orgAdminOrSuperAdminRequired)

	// Event Media (Banner/Gallery) Management
	// These routes are under /organizations/{orgId}/events/{eventId}/media
	orgEventMedia := orgEvents.Group("/:eventId/media")
	orgEventMedia.POST("", h.AddEventMediaItem, jwtAuth, orgAdminOrSuperAdminRequired)                                       // TODO: Add specific permissions (e.g., orgAdminOrSuperAdminRequired or event creator)
	orgEventMedia.GET("", h.ListEventMediaItems, jwtAuth)                                                                    // Can be viewed by org members with event access
	orgEventMedia.DELETE("/:itemId", h.DeleteEventMediaItem, jwtAuth, orgAdminOrSuperAdminRequired)                          // TODO: Add specific permissions
	orgEventMedia.PATCH("/:mediaItemId/set-banner", h.SetEventBannerMediaItemHandler, jwtAuth, orgAdminOrSuperAdminRequired) // New route

	// Event Tags - linking/unlinking tags for a specific event within an organization
	// These routes are under /organizations/{orgId}/events/{eventId}/tags
	// Original code had orgAdminOrSuperAdminRequired on the group. If that's intended, jwtAuth should also be there or on parent.
	// Forcing jwtAuth before orgAdminOrSuperAdminRequired on specific actions.
	orgEventTagsGroup := orgEvents.Group("/:eventId/tags")
	orgEventTagsGroup.POST("/:tagId", h.AddTagToEvent, jwtAuth, orgAdminOrSuperAdminRequired)        // Assuming AddTagToEvent was covered by group's orgAdminOrSuperAdminRequired
	orgEventTagsGroup.DELETE("/:tagId", h.RemoveTagFromEvent, jwtAuth, orgAdminOrSuperAdminRequired) // Assuming RemoveTagFromEvent was covered by group's orgAdminOrSuperAdminRequired
	// Listing tags for an event can be less restrictive, depends on requirements
	orgEvents.GET("/:eventId/tags", h.ListTagsForEvent) // List tags for a specific event (public or optionally authenticated)

	// Event Required Verification Types - managing for a specific event within an organization
	// These routes are under /organizations/{orgId}/events/{eventId}/verification-types
	// Similar logic to tags regarding group-level orgAdminOrSuperAdminRequired
	orgEventVerificationTypesGroup := orgEvents.Group("/:eventId/verification-types")
	orgEventVerificationTypesGroup.POST("/:typeKey", h.AddEventRequiredVerificationType, jwtAuth, orgAdminOrSuperAdminRequired)
	orgEventVerificationTypesGroup.DELETE("/:typeKey", h.RemoveEventRequiredVerificationType, jwtAuth, orgAdminOrSuperAdminRequired)
	// Listing verification types for an event
	orgEvents.GET("/:eventId/verification-types", h.ListRequiredVerificationTypesForEvent) // (public or optionally authenticated)

	// Event Registrations Management (Admin for listing all, user for their own)
	// Route for listing all registrations for a specific event (Admin)
	orgEvents.GET("/:eventId/registrations", h.ListEventRegistrations, jwtAuth, orgAdminOrSuperAdminRequired)
	// Event Statistics (Admin for organization-wide)
	orgEvents.GET("/statistics", h.GetOrganizationEventStatistics, jwtAuth, orgAdminOrSuperAdminRequired) // Get event statistics for an organization

	// User's own event volunteer applications (list all)
	// User's event statistics
	userStatsGroup := apiV1.Group("/users/me/event-statistics", jwtAuth)
	userStatsGroup.GET("", h.GetUserEventStatistics) // Get user's event related statistics

	// Admin route for listing event volunteer applications for a specific organization with filters
	adminOrgEventVolunteerApps := apiV1.Group("/admin/organizations/:orgId", jwtAuth, orgAdminOrSuperAdminRequired)
	adminOrgEventVolunteerApps.GET("/event-volunteer-applications", h.ListOrgEventVolunteerApplicationsHandler)

	// ADD THE ROUTE DIRECTLY TO APIV1 HERE
	apiV1.GET("/organizations/:orgId/events/:eventId/volunteer-applications/:appId", h.GetEventVolunteerApplicationDetails)
}

// registerContentRoutes registers routes related to content management (posts, resources, files).
func registerContentRoutes(apiV1 *echo.Group, authedPostHandler *handlers.PostHandler, resourceH *handlers.ResourceHandler, jwtAuthMiddleware echo.MiddlewareFunc, optionalJwtAuthMiddleware echo.MiddlewareFunc, orgAdminOrSuperAdminRequired echo.MiddlewareFunc) {
	// Public listing of an organization's posts - NO JWT by default on these routes.
	// The handlers themselves (ListOrgPosts, GetOrgPost) would determine what content is shown based on auth (if any via optionalJwtAuthMiddleware) or by being public-only.
	// User requested: apiV1.GET("/organizations/:orgId/posts", authedPostHandler.ListOrgPosts)
	apiV1.GET("/organizations/:orgId/posts", authedPostHandler.ListOrgPosts)
	// User requested: apiV1.GET("/organizations/:orgId/posts/:postId", authedPostHandler.GetOrgPost)
	apiV1.GET("/organizations/:orgId/posts/:postId", authedPostHandler.GetOrgPost) // Changed :postIdOrSlug to :postId to match GetOrgPost handler

	// --- Posts (Admin/Org Management) ---
	// These are explicitly JWT protected.
	orgsGroup := apiV1.Group("/organizations/:orgId", jwtAuthMiddleware)
	{
		orgPostsGroup := orgsGroup.Group("/posts")
		{
			orgPostsGroup.POST("", authedPostHandler.CreatePost)
			orgPostsGroup.PUT("/:postId", authedPostHandler.UpdatePost)
			orgPostsGroup.DELETE("/:postId", authedPostHandler.DeletePost)
			orgPostsGroup.POST("/:postId/media", authedPostHandler.AddPostMediaItemHandler)
			orgPostsGroup.DELETE("/:postId/media/:mediaItemId", authedPostHandler.DeletePostMedia)
			orgPostsGroup.PUT("/:postId/media/:mediaItemId/banner", authedPostHandler.SetPostBannerMediaItemHandler)
		}

		// --- Resources (Admin/Org Management) ---
		resourcesOrgGroup := orgsGroup.Group("/resources")
		// middleware can be added here: resourcesOrgGroup.Use(jwtAuthMiddleware, adminForOrgMiddleware)
		{
			resourcesOrgGroup.POST("", resourceH.CreateResource)
			resourcesOrgGroup.GET("", resourceH.ListOrgResources) // List resources for an org (admin view)
			// GET /organizations/:orgId/resources/:resourceId is handled below with optional JWT
			// PUT /organizations/:orgId/resources/:resourceId is handled below with optional JWT
			// DELETE /organizations/:orgId/resources/:resourceId is handled below with optional JWT

			// Resource Files Management (nested under a specific resource of an org)
			resourcesOrgGroup.POST("/:resourceId/files", resourceH.UploadResourceFile)
			resourcesOrgGroup.DELETE("/:resourceId/files/:fileId", resourceH.DeleteResourceFile)
			// Note: Download for org-specific resource files could be here if needed with auth
			// e.g., resourcesOrgGroup.GET("/:resourceId/files/:fileId/download", resourceH.DownloadResourceFile) // Needs org auth
		}

		// Specific resource routes with optional JWT. Defined outside the main orgsGroup
		// to allow for optional authentication, as these might be viewable publicly
		// or editable with a token.
		apiV1.GET("/organizations/:orgId/resources/:resourceId", resourceH.GetOrgResource, optionalJwtAuthMiddleware)
		apiV1.PUT("/organizations/:orgId/resources/:resourceId", resourceH.UpdateResource, optionalJwtAuthMiddleware)
		apiV1.DELETE("/organizations/:orgId/resources/:resourceId", resourceH.DeleteResource, optionalJwtAuthMiddleware)
	}

	// --- Posts (Public) ---
	// These are global public post routes, NOT under JWT group.
	// They use methods from the main PostHandler instance.
	postsPublicGroup := apiV1.Group("/posts")
	{
		postsPublicGroup.GET("", authedPostHandler.ListPublicPosts)
		postsPublicGroup.GET("/:postIdOrSlug", authedPostHandler.GetPostBySlug)
	}

	// --- Resources (Public) ---
	resourcesPublicGroup := apiV1.Group("/resources")
	{
		resourcesPublicGroup.GET("", resourceH.ListPublicResources) // List all public resources
		// Path for GetPublicResourceBySlug handler: /organizations/{orgId}/resources/{resourceIdOrSlug}/public
		// This needs orgId, so it should be grouped under /organizations for clarity or handler adjusted.
		// For now, creating a more explicit route for public access by slug which includes org context:
		apiV1.GET("/organizations/:orgId/resources/:resourceIdOrSlug/public", resourceH.GetPublicResourceBySlug)

		// Public Download for Resource Files.
		// The handler DownloadResourceFile expects orgId, resourceIdOrSlug, and fileIdOrName.
		// Path: /resources/download/{orgId}/{resourceIdOrSlug}/{fileIdOrName}
		resourcesPublicGroup.GET("/download/:orgId/:resourceIdOrSlug/:fileIdOrName", resourceH.DownloadResourceFile)
	}

	// --- Organization File Management (Direct file/folder manipulation for an org) ---
	orgFilesGroup := apiV1.Group("/organizations/:orgId/files")
	// ADDED: Explicitly apply jwtAuthMiddleware to ensure these routes remain authenticated.
	orgFilesGroup.Use(jwtAuthMiddleware)
	// If adminForOrgMiddleware was also intended (as per commented out line originally), it could be added:
	// orgFilesGroup.Use(jwtAuthMiddleware, adminForOrgMiddleware)
	{
		orgFilesGroup.POST("/folder", resourceH.CreateOrganizationFolder)
		orgFilesGroup.POST("/upload", resourceH.UploadOrganizationFile)
		orgFilesGroup.GET("", resourceH.ListOrganizationFiles)                  // List files/folders in a directory
		orgFilesGroup.PUT("/:fileOrFolderId", resourceH.UpdateOrganizationFile) // Rename/move
		orgFilesGroup.DELETE("/:fileOrFolderId", resourceH.DeleteOrganizationFile)
		orgFilesGroup.GET("/:fileId/download", resourceH.DownloadOrganizationDirectFile)
	}
}

// ensureDefaultOrgAndSuperAdmin
func ensureDefaultOrgAndSuperAdmin(ctx context.Context, q *db.Queries, orgSvc *services.OrganizationService) uuid.UUID {
	superAdminEmail := "<EMAIL>"
	superAdminUser, err := q.GetUserByEmail(ctx, &superAdminEmail)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Warn().Str("email", superAdminEmail).Msg("Superadmin user not found, assuming seeded by migration.")
			// Attempt to fetch again just in case, or rely on migration seed.
			// If critical, could attempt creation here, but migrations are preferred.
			superAdminUser, err = q.GetUserByEmail(ctx, &superAdminEmail) // Retry fetch
			if err != nil {
				log.Fatal().Err(err).Str("email", superAdminEmail).Msg("Failed to find superadmin user even after check.")
			}
		} else {
			log.Fatal().Err(err).Str("email", superAdminEmail).Msg("Failed to query for superadmin user")
		}
	}
	log.Info().Str("user_id", superAdminUser.ID.String()).Msg("Superadmin user ensured.")

	// Call GetOrCreateDefaultOrganization with superAdminUser ID
	_, err = orgSvc.GetOrCreateDefaultOrganization(ctx, superAdminUser.ID) // Corrected call
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to ensure default organization.")
	}
	log.Info().Str("default_org_owner_id", superAdminUser.ID.String()).Msg("Default organization ensured.")

	return superAdminUser.ID
}

// createAdminRequiredMiddleware creates a middleware function that checks for staff/admin claims.
func createAdminRequiredMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			claims, err := authn.GetValidatedClaims(c)
			if err != nil {
				return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
			}
			// Allow both 'admin' and 'superadmin'
			if claims.Role != string(db.UserRoleAdmin) && claims.Role != string(db.UserRoleSuperadmin) {
				return utils.HandleError(c, http.StatusForbidden, "Forbidden: Admin or Superadmin role required", nil)
			}
			return next(c)
		}
	}
}

// createSuperAdminRequiredMiddleware creates a middleware function that checks for superadmin claims.
func createSuperAdminRequiredMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			claims, err := authn.GetValidatedClaims(c)
			if err != nil {
				return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
			}
			if claims.Role != string(db.UserRoleSuperadmin) {
				return utils.HandleError(c, http.StatusForbidden, "Forbidden: Superadmin role required", nil)
			}
			return next(c)
		}
	}
}

// New function to register event registration routes
// It now accepts an optionalJwtAuthMiddleware.
func registerEventRegistrationRoutes(apiV1 *echo.Group, h *handlers.EventRegistrationHandler, jwtAuthMiddleware echo.MiddlewareFunc, adminRequiredMiddleware echo.MiddlewareFunc, optionalJwtAuthMiddleware echo.MiddlewareFunc) {
	// Group for event registrations specific to an organization, now using optional JWT auth.
	orgEventRegGroup := apiV1.Group("/organizations/:orgId/event-registrations", optionalJwtAuthMiddleware)

	// GET /organizations/:orgId/event-registrations - List registrations for an org's events.
	// No adminRequiredMiddleware, authentication is optional. Authorization removed from service.
	orgEventRegGroup.GET("", h.ListOrganizationEventRegistrations)

	// PATCH /organizations/:orgId/event-registrations/:registrationId/status - Update registration status.
	// No adminRequiredMiddleware, authentication is optional. Authorization removed from service.
	orgEventRegGroup.PATCH("/:registrationId/status", h.UpdateRegistrationStatus)

	// Group for event registrations related to the current user (requires standard JWT auth)
	// These routes were previously under a general /event-registrations group.
	// To maintain similar functionality and pathing as before for user-specific actions:
	userEventRegGroup := apiV1.Group("/me/event-registrations", jwtAuthMiddleware)
	userEventRegGroup.POST("", h.RegisterForEvent)                                // User registers for an event
	userEventRegGroup.GET("", h.ListUserRegistrations)                            // List current user's registrations (formerly /event-registrations/me)
	userEventRegGroup.GET("/:registrationId", h.GetUserEventRegistration)         // User gets details of their specific registration (formerly /event-registrations/:registrationId)
	userEventRegGroup.PATCH("/:registrationId/cancel", h.CancelEventRegistration) // User cancels their registration (formerly DELETE /event-registrations/:registrationId, now PATCH for consistency with update operations)

	// Scanner check-in route (requires standard JWT auth for scanner ID)
	// This was previously POST /event-registrations/check-in
	apiV1.POST("/event-registrations/check-in", h.CheckInParticipantByScanner, jwtAuthMiddleware)

	// Public endpoint for payment status update (outside of jwtAuth group)
	// Path: /api/v1/event-registrations/:registrationId/payment-status
	apiV1.PATCH("/event-registrations/:registrationId/payment-status", h.UpdatePaymentStatus)

	// Routes that caused linter errors and need further review for correct handlers/paths:
	// eventCheckInGroup := apiV1.Group("/events/:eventId/registrations")
	// GET /events/:eventId/registrations/:registrationId/status - Get check-in status for a registration (for staff/admin)
	// eventCheckInGroup.GET("/:registrationId/status", h.GetRegistrationCheckInStatus, jwtAuthMiddleware, adminRequiredMiddleware) // Requires admin/staff

	// Admin/Staff route to list registrations for a specific event (potentially for check-in app)
	// This route will be protected by adminRequiredMiddleware as it's for event management.
	// apiV1.GET("/events/:eventId/registrations-for-checkin", h.ListEventRegistrationsForCheckIn, jwtAuthMiddleware, adminRequiredMiddleware)

	log.Info().Msg("EventRegistration routes registered.")
}

// New function to register volunteer application routes
func registerVolunteerApplicationRoutes(apiV1 *echo.Group, vaHandler *handlers.VolunteerApplicationHandler, jwtAuth echo.MiddlewareFunc, adminRequired echo.MiddlewareFunc, orgAdminOrSuperAdminRequired echo.MiddlewareFunc) {
	// GET /api/v1/events/{event_id}/event-applications/pending-review
	// Middleware: jwtAuth - further checks if user can view event might be in service or if event is public
	apiV1.GET("/events/:event_id/event-applications/pending-review", vaHandler.HandleListPendingReviewVolunteerApplicationsForEvent, jwtAuth)

	// GET /api/v1/organizations/{organization_id}/event-applications/pending-review
	// Middleware: jwtAuth, orgAdminOrSuperAdminRequired (org admin/staff)
	// Note: orgAdminOrSuperAdminRequired checks for general staff status. Org-specific admin check would be more complex or done in handler/service.
	orgEventAppsGroup := apiV1.Group("/organizations/:orgId/event-applications", jwtAuth, orgAdminOrSuperAdminRequired) // Group for org-specific event VA routes
	orgEventAppsGroup.GET("/pending-review", vaHandler.HandleListPendingReviewVolunteerApplicationsForOrganization)

	// GET /api/v1/admin/event-applications/pending-review
	// Middleware: jwtAuth, adminRequired (system admin)
	adminEventAppsGroup := apiV1.Group("/admin/event-applications", jwtAuth, adminRequired) // Group for admin event VA routes
	adminEventAppsGroup.GET("/pending-review", vaHandler.HandleListAllPendingReviewVolunteerApplications)

	log.Info().Msg("Event Volunteer Application routes registered.")
}

// registerAdminUserRoutes registers admin-specific user management routes.
func registerAdminUserRoutes(apiV1 *echo.Group, h *handlers.UserHandler, jwtAuth echo.MiddlewareFunc, adminRequired echo.MiddlewareFunc) {
	adminUserGroup := apiV1.Group("/admin/users", jwtAuth, adminRequired)
	adminUserGroup.GET("", h.ListUsersHandler)
	adminUserGroup.POST("", h.CreateStaffUserHandler)
	adminUserGroup.GET("/email/:email", h.GetUserByEmailHandler)
	adminUserGroup.GET("/phone/:phone", h.GetUserByPhoneHandler)
	adminUserGroup.GET("/:userId", h.GetUserByIDHandler) // New route for GetUserByIDHandler
	adminUserGroup.DELETE("/:userId", h.DeleteUserHandler)
	log.Info().Msg("Admin user routes registered under /api/v1/admin/users.")
}

func registerPostTagRoutes(apiV1 *echo.Group, h *handlers.PostTagHandler, jwtAuth echo.MiddlewareFunc, superadminRequired echo.MiddlewareFunc) {
	postTagsGroup := apiV1.Group("/post-tags")
	{
		postTagsGroup.POST("", h.HandleCreatePostTag)
		postTagsGroup.GET("", h.HandleListPostTags)
		postTagsGroup.GET("/:tagId", h.HandleGetPostTag)
		postTagsGroup.PUT("/:tagId", h.HandleUpdatePostTag)
		postTagsGroup.DELETE("/:tagId", h.HandleDeletePostTag)
	}
}

/*
func registerStaffRoutes(apiV1 *echo.Group, authnSvc *authn.AuthnService, staffHandler *handlers.StaffHandler, jwtAuth echo.MiddlewareFunc) {
	staffAuthRoutes := apiV1.Group("/authn/staff")
	staffAuthRoutes.POST("/email/check", authnSvc.CheckStaffEmailHandler)
	staffAuthRoutes.POST("/login/initiate", authnSvc.InitiateStaffLoginHandler)
	staffAuthRoutes.POST("/login/verify", authnSvc.VerifyStaffLoginHandler)
}
*/
