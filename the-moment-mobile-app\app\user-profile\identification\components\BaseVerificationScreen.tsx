import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { useFetchUserVerifications, useSubmitVerification } from '@/api/user_services';
import { Button } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { VerificationTypeEnum, VerificationStatusEnum } from 'types/enums';
import type { MD3Theme as CustomTheme } from 'react-native-paper';
import { createTheme } from 'theme/index';

interface BaseVerificationScreenProps {
  type: VerificationTypeEnum;
  title: string;
  description: string;
  requirements: string[];
  sampleImage?: any;
  additionalInstructions?: string;
}

export default function BaseVerificationScreen({
  type,
  title,
  description,
  requirements,
  sampleImage,
  additionalInstructions,
}: BaseVerificationScreenProps) {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  
  const { data: verificationsData, refetch: refetchVerifications, isLoading: isLoadingVerifications } = useFetchUserVerifications();
  const submitVerificationMutation = useSubmitVerification();

  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    refetchVerifications();
  }, [type, refetchVerifications]);

  const getVerificationByType = (verificationType: VerificationTypeEnum) => {
    return verificationsData?.find(v => v.verification_type === verificationType.toString());
  };

  const verification = getVerificationByType(type);
  const isPending = verification?.status === VerificationStatusEnum.Pending;
  const isRejected = verification?.status === VerificationStatusEnum.Rejected;

  const handleSelectImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('verification.permissions.gallery'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.7,
        allowsMultipleSelection: true,
      });

      if (!result.canceled && result.assets) {
        setSelectedImages(result.assets.map(asset => asset.uri));
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert(t('verification.errors.imageSelection'));
    }
  };

  const handleSubmit = async () => {
    if (selectedImages.length === 0) {
      Alert.alert(t('verification.errors.noImages'));
      return;
    }

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('verification_type', type);

      selectedImages.forEach((uri, index) => {
        const fileName = uri.split('/').pop() || `${type}_${index}.jpg`;
        const fileType = 'image/jpeg';
        formData.append('files', {
          uri: uri,
          name: fileName,
          type: fileType,
        } as any);
      });

      await submitVerificationMutation.mutateAsync(formData);
      await refetchVerifications();

      Alert.alert(
        t('verification.success.title'),
        t('verification.success.message'),
        [{ text: t('common.ok') }]
      );
      setSelectedImages([]);
    } catch (error) {
      console.error('Error submitting verification:', error);
      Alert.alert(
        t('verification.errors.submission'),
        t('verification.errors.tryAgain'),
        [{ text: t('common.ok') }]
      );
    } finally {
      setUploading(false);
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.system.text }]}>{title}</Text>
      <Text style={[styles.description, { color: theme.system.secondaryText }]}>
        {description}
      </Text>

      {verification && (
        <View style={[styles.statusContainer, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.statusTitle, { color: theme.system.text }]}>
            {t('verification.status.title')}
          </Text>
          <Text style={[styles.statusText, { color: getStatusColor(verification.status as VerificationStatusEnum, theme) }]}>
            {t(`verification.status.${verification.status}`)}
          </Text>
          {verification.admin_notes && (
            <Text style={[styles.adminNotes, { color: theme.system.secondaryText }]}>
              {verification.admin_notes}
            </Text>
          )}
        </View>
      )}

      <View style={styles.requirementsContainer}>
        <Text style={[styles.requirementsTitle, { color: theme.system.text }]}>
          {t('verification.requirements')}
        </Text>
        {requirements.map((req, index) => (
          <View key={index} style={styles.requirementItem}>
            <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />
            <Text style={[styles.requirementText, { color: theme.system.text }]}>{req}</Text>
          </View>
        ))}
      </View>

      {sampleImage && (
        <View style={styles.sampleContainer}>
          <Text style={[styles.sampleTitle, { color: theme.system.text }]}>
            {t('verification.sample')}
          </Text>
          <Image source={sampleImage} style={styles.sampleImage} resizeMode="contain" />
        </View>
      )}

      {additionalInstructions && (
        <Text style={[styles.additionalInstructions, { color: theme.system.secondaryText }]}>
          {additionalInstructions}
        </Text>
      )}

      {!isPending && (
        <>
          <View style={styles.imageContainer}>
            {selectedImages.map((uri, index) => (
              <View key={index} style={styles.imageWrapper}>
                <Image source={{ uri }} style={styles.selectedImage} />
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => setSelectedImages(prev => prev.filter((_, i) => i !== index))}
                >
                  <Ionicons name="close-circle" size={24} color={theme.system.error} />
                </TouchableOpacity>
              </View>
            ))}
          </View>

          <View style={styles.buttonContainer}>
            <Button
              mode="outlined"
              onPress={handleSelectImage}
              style={styles.button}
              icon="image"
            >
              {t('verification.selectImages')}
            </Button>
            <Button
              mode="contained"
              onPress={handleSubmit}
              style={styles.button}
              loading={uploading}
              disabled={selectedImages.length === 0 || uploading}
            >
              {t('verification.submit')}
            </Button>
          </View>
        </>
      )}
    </ScrollView>
  );
}

const getStatusColor = (status: VerificationStatusEnum, theme: CustomTheme) => {
  switch (status) {
    case VerificationStatusEnum.Approved:
      return theme.system.success;
    case VerificationStatusEnum.Pending:
      return theme.system.info;
    case VerificationStatusEnum.Rejected:
      return theme.system.error;
    default:
      return theme.system.secondaryText;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    marginBottom: 24,
  },
  statusContainer: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  statusTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 8,
  },
  statusText: {
    fontSize: 15,
    fontWeight: '500',
  },
  adminNotes: {
    fontSize: 14,
    marginTop: 8,
  },
  requirementsContainer: {
    marginBottom: 24,
  },
  requirementsTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 12,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  requirementText: {
    fontSize: 15,
    marginLeft: 8,
    flex: 1,
  },
  sampleContainer: {
    marginBottom: 24,
  },
  sampleTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 12,
  },
  sampleImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
  },
  additionalInstructions: {
    fontSize: 14,
    marginBottom: 24,
  },
  imageContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  imageWrapper: {
    position: 'relative',
  },
  selectedImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  buttonContainer: {
    gap: 12,
    marginBottom: 24,
  },
  button: {
    borderRadius: 8,
  },
}); 