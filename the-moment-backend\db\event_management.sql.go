// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: event_management.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const addEventRequiredVerificationType = `-- name: AddEventRequiredVerificationType :exec
INSERT INTO event_required_verification_types (
    event_id, verification_type_key
) VALUES (
    $1, $2
) ON CONFLICT (event_id, verification_type_key) DO NOTHING
`

type AddEventRequiredVerificationTypeParams struct {
	EventID             uuid.UUID `db:"event_id" json:"event_id"`
	VerificationTypeKey string    `db:"verification_type_key" json:"verification_type_key"`
}

func (q *Queries) AddEventRequiredVerificationType(ctx context.Context, arg AddEventRequiredVerificationTypeParams) error {
	_, err := q.db.Exec(ctx, addEventRequiredVerificationType, arg.EventID, arg.VerificationTypeKey)
	return err
}

const addTagToEvent = `-- name: AddTagToEvent :exec
INSERT INTO event_event_tags (
    event_id,
    event_tag_id
) VALUES (
    $1, $2
) ON CONFLICT (event_id, event_tag_id) DO NOTHING
`

type AddTagToEventParams struct {
	EventID    uuid.UUID `db:"event_id" json:"event_id"`
	EventTagID uuid.UUID `db:"event_tag_id" json:"event_tag_id"`
}

func (q *Queries) AddTagToEvent(ctx context.Context, arg AddTagToEventParams) error {
	_, err := q.db.Exec(ctx, addTagToEvent, arg.EventID, arg.EventTagID)
	return err
}

const cancelEventRegistrationByUser = `-- name: CancelEventRegistrationByUser :one
UPDATE event_registrations
SET status = 'cancelled_by_user',
    cancellation_reason_by_user = $2
WHERE id = $1 AND user_id = $3 -- Ensure user can only cancel their own
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method
`

type CancelEventRegistrationByUserParams struct {
	ID                       uuid.UUID `db:"id" json:"id"`
	CancellationReasonByUser *string   `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	UserID                   uuid.UUID `db:"user_id" json:"user_id"`
}

func (q *Queries) CancelEventRegistrationByUser(ctx context.Context, arg CancelEventRegistrationByUserParams) (EventRegistration, error) {
	row := q.db.QueryRow(ctx, cancelEventRegistrationByUser, arg.ID, arg.CancellationReasonByUser, arg.UserID)
	var i EventRegistration
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CheckInByUserID,
		&i.CheckInMethod,
	)
	return i, err
}

const countAllPendingReviewEventVolunteerApplications = `-- name: CountAllPendingReviewEventVolunteerApplications :one
SELECT COUNT(*)
FROM event_volunteer_applications eva
WHERE eva.status = 'pending'
`

func (q *Queries) CountAllPendingReviewEventVolunteerApplications(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, countAllPendingReviewEventVolunteerApplications)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countEventsByOrganization = `-- name: CountEventsByOrganization :one
SELECT COUNT(*) FROM events e
WHERE e.organization_id = $1
    AND ($2::text[] IS NULL OR e.status::text = ANY($2::text[]))
    AND e.end_time > COALESCE($3::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE($4::TIMESTAMPTZ, 'infinity')
    AND ($5::text IS NULL OR (
        e.title ILIKE '%' || $5::text || '%'
        OR e.location_full_address ILIKE '%' || $5::text || '%'
    ))
    AND ($6::text[] IS NULL OR e.government_funding_keys && $6::text[])
    AND (
        $7::text[] IS NULL
        OR array_length($7::text[], 1) IS NULL
        OR EXISTS (
            SELECT 1
            FROM event_required_verification_types ervt
            WHERE ervt.event_id = e.id
            AND ervt.verification_type_key = ANY($7::text[])
        )
    )
    AND (
        $8::uuid[] IS NULL
        OR array_length($8::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY($8::uuid[])
        )
    )
`

type CountEventsByOrganizationParams struct {
	OrganizationID            uuid.UUID   `db:"organization_id" json:"organization_id"`
	Statuses                  []string    `db:"statuses" json:"statuses"`
	StartDate                 *time.Time  `db:"start_date" json:"start_date"`
	EndDate                   *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm                *string     `db:"search_term" json:"search_term"`
	GovernmentFundingKeys     []string    `db:"government_funding_keys" json:"government_funding_keys"`
	EventVerificationTypeKeys []string    `db:"event_verification_type_keys" json:"event_verification_type_keys"`
	TagIds                    []uuid.UUID `db:"tag_ids" json:"tag_ids"`
}

func (q *Queries) CountEventsByOrganization(ctx context.Context, arg CountEventsByOrganizationParams) (int64, error) {
	row := q.db.QueryRow(ctx, countEventsByOrganization,
		arg.OrganizationID,
		arg.Statuses,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.GovernmentFundingKeys,
		arg.EventVerificationTypeKeys,
		arg.TagIds,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countOrgEventVolunteerApplicationsWithFilters = `-- name: CountOrgEventVolunteerApplicationsWithFilters :one
SELECT COUNT(*)
FROM event_volunteer_applications eva
WHERE eva.organization_id = $1
AND eva.status = COALESCE($2::application_status_enum, eva.status)
`

type CountOrgEventVolunteerApplicationsWithFiltersParams struct {
	OrganizationID uuid.UUID                 `db:"organization_id" json:"organization_id"`
	Status         NullApplicationStatusEnum `db:"status" json:"status"`
}

func (q *Queries) CountOrgEventVolunteerApplicationsWithFilters(ctx context.Context, arg CountOrgEventVolunteerApplicationsWithFiltersParams) (int64, error) {
	row := q.db.QueryRow(ctx, countOrgEventVolunteerApplicationsWithFilters, arg.OrganizationID, arg.Status)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPendingReviewEventVolunteerApplicationsForEvent = `-- name: CountPendingReviewEventVolunteerApplicationsForEvent :one
SELECT COUNT(*)
FROM event_volunteer_applications eva
WHERE eva.event_id = $1 AND eva.status = 'pending'
`

func (q *Queries) CountPendingReviewEventVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, countPendingReviewEventVolunteerApplicationsForEvent, eventID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPendingReviewEventVolunteerApplicationsForOrganization = `-- name: CountPendingReviewEventVolunteerApplicationsForOrganization :one
SELECT COUNT(*)
FROM event_volunteer_applications eva
WHERE eva.organization_id = $1 AND eva.status = 'pending'
`

func (q *Queries) CountPendingReviewEventVolunteerApplicationsForOrganization(ctx context.Context, organizationID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, countPendingReviewEventVolunteerApplicationsForOrganization, organizationID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPublicEvents = `-- name: CountPublicEvents :one
SELECT COUNT(*)
FROM events e
WHERE
    e.status::text = ANY($1::text[]) -- Cast e.status to text
    AND e.end_time > COALESCE($2::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE($3::TIMESTAMPTZ, 'infinity')
    AND ($4::text IS NULL OR (
        e.title ILIKE ('%' || $4::text || '%')
        OR e.location_full_address ILIKE ('%' || $4::text || '%')
    ))
    AND ( -- Alternative tag filter logic
        $5::uuid[] IS NULL
        OR array_length($5::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY($5::uuid[])
        )
    )
    AND (array_length($6::text[], 1) IS NULL OR e.government_funding_keys && $6::text[])
    AND ($7::text IS NULL OR EXISTS (
        SELECT 1
        FROM event_required_verification_types ervt
        WHERE ervt.event_id = e.id
        AND ervt.verification_type_key = $7::text
    ))
    AND (
        ($8::uuid IS NULL AND $9::uuid IS NULL) OR
        ($8::uuid IS NOT NULL AND e.organization_id = $8::uuid) OR
        ($9::uuid IS NOT NULL AND e.organization_id = $9::uuid)
    )
`

type CountPublicEventsParams struct {
	Statuses                 []string    `db:"statuses" json:"statuses"`
	StartDate                *time.Time  `db:"start_date" json:"start_date"`
	EndDate                  *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm               *string     `db:"search_term" json:"search_term"`
	TagIds                   []uuid.UUID `db:"tag_ids" json:"tag_ids"`
	GovernmentFundingKeys    []string    `db:"government_funding_keys" json:"government_funding_keys"`
	EventVerificationTypeKey *string     `db:"event_verification_type_key" json:"event_verification_type_key"`
	OrganizationID           *uuid.UUID  `db:"organization_id" json:"organization_id"`
	OrganizationId2          *uuid.UUID  `db:"organization_id2" json:"organization_id2"`
}

func (q *Queries) CountPublicEvents(ctx context.Context, arg CountPublicEventsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countPublicEvents,
		arg.Statuses,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.TagIds,
		arg.GovernmentFundingKeys,
		arg.EventVerificationTypeKey,
		arg.OrganizationID,
		arg.OrganizationId2,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPublicEventsByOrganization = `-- name: CountPublicEventsByOrganization :one
SELECT COUNT(*)
FROM events e
WHERE
    e.organization_id = $1
    AND e.status::text = ANY($2::text[]) -- Cast e.status to text
    AND e.end_time > COALESCE($3::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE($4::TIMESTAMPTZ, 'infinity')
    AND ($5::text IS NULL OR (
        e.title ILIKE '%' || $5::text || '%'
        OR e.location_full_address ILIKE '%' || $5::text || '%'
    ))
    AND (
        COALESCE(array_length($6::uuid[], 1), 0) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY($6::uuid[])
        )
    )
    AND (array_length($7::text[], 1) IS NULL OR e.government_funding_keys && $7::text[])
`

type CountPublicEventsByOrganizationParams struct {
	OrganizationID        uuid.UUID   `db:"organization_id" json:"organization_id"`
	Statuses              []string    `db:"statuses" json:"statuses"`
	StartDate             *time.Time  `db:"start_date" json:"start_date"`
	EndDate               *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm            *string     `db:"search_term" json:"search_term"`
	TagIds                []uuid.UUID `db:"tag_ids" json:"tag_ids"`
	GovernmentFundingKeys []string    `db:"government_funding_keys" json:"government_funding_keys"`
}

func (q *Queries) CountPublicEventsByOrganization(ctx context.Context, arg CountPublicEventsByOrganizationParams) (int64, error) {
	row := q.db.QueryRow(ctx, countPublicEventsByOrganization,
		arg.OrganizationID,
		arg.Statuses,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.TagIds,
		arg.GovernmentFundingKeys,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countVolunteerApplicationsForEvent = `-- name: CountVolunteerApplicationsForEvent :one
SELECT COUNT(*) 
FROM event_volunteer_applications
WHERE event_id = $1
`

func (q *Queries) CountVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, countVolunteerApplicationsForEvent, eventID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createEvent = `-- name: CreateEvent :one
INSERT INTO events (
    organization_id, title, description_content, location_type, 
    location_full_address,
    location_online_url, start_time, end_time, status,
    participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at,
    price, contact_email, contact_phone, government_funding_keys
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
    $16, $17, $18
) RETURNING id, organization_id, title, location_type, location_online_url, start_time, end_time, status, participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at, government_funding_keys, created_at, updated_at, location_full_address, description_content, price, contact_email, contact_phone, is_government_funded
`

type CreateEventParams struct {
	OrganizationID                  uuid.UUID         `db:"organization_id" json:"organization_id"`
	Title                           string            `db:"title" json:"title"`
	DescriptionContent              []byte            `db:"description_content" json:"description_content"`
	LocationType                    EventLocationType `db:"location_type" json:"location_type"`
	LocationFullAddress             *string           `db:"location_full_address" json:"location_full_address"`
	LocationOnlineUrl               *string           `db:"location_online_url" json:"location_online_url"`
	StartTime                       time.Time         `db:"start_time" json:"start_time"`
	EndTime                         time.Time         `db:"end_time" json:"end_time"`
	Status                          EventStatusType   `db:"status" json:"status"`
	ParticipantLimit                *int32            `db:"participant_limit" json:"participant_limit"`
	WaitlistLimit                   *int32            `db:"waitlist_limit" json:"waitlist_limit"`
	RequiresApprovalForRegistration bool              `db:"requires_approval_for_registration" json:"requires_approval_for_registration"`
	CreatedByUserID                 uuid.UUID         `db:"created_by_user_id" json:"created_by_user_id"`
	PublishedAt                     *time.Time        `db:"published_at" json:"published_at"`
	Price                           *string           `db:"price" json:"price"`
	ContactEmail                    *string           `db:"contact_email" json:"contact_email"`
	ContactPhone                    *string           `db:"contact_phone" json:"contact_phone"`
	GovernmentFundingKeys           []string          `db:"government_funding_keys" json:"government_funding_keys"`
}

func (q *Queries) CreateEvent(ctx context.Context, arg CreateEventParams) (Event, error) {
	row := q.db.QueryRow(ctx, createEvent,
		arg.OrganizationID,
		arg.Title,
		arg.DescriptionContent,
		arg.LocationType,
		arg.LocationFullAddress,
		arg.LocationOnlineUrl,
		arg.StartTime,
		arg.EndTime,
		arg.Status,
		arg.ParticipantLimit,
		arg.WaitlistLimit,
		arg.RequiresApprovalForRegistration,
		arg.CreatedByUserID,
		arg.PublishedAt,
		arg.Price,
		arg.ContactEmail,
		arg.ContactPhone,
		arg.GovernmentFundingKeys,
	)
	var i Event
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.LocationType,
		&i.LocationOnlineUrl,
		&i.StartTime,
		&i.EndTime,
		&i.Status,
		&i.ParticipantLimit,
		&i.WaitlistLimit,
		&i.RequiresApprovalForRegistration,
		&i.CreatedByUserID,
		&i.PublishedAt,
		&i.GovernmentFundingKeys,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LocationFullAddress,
		&i.DescriptionContent,
		&i.Price,
		&i.ContactEmail,
		&i.ContactPhone,
		&i.IsGovernmentFunded,
	)
	return i, err
}

const createEventMediaItem = `-- name: CreateEventMediaItem :one
INSERT INTO event_media_items (
    event_id, file_name, file_path, file_type, file_size, is_banner
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING id, event_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner
`

type CreateEventMediaItemParams struct {
	EventID  uuid.UUID `db:"event_id" json:"event_id"`
	FileName string    `db:"file_name" json:"file_name"`
	FilePath string    `db:"file_path" json:"file_path"`
	FileType string    `db:"file_type" json:"file_type"`
	FileSize int64     `db:"file_size" json:"file_size"`
	IsBanner bool      `db:"is_banner" json:"is_banner"`
}

func (q *Queries) CreateEventMediaItem(ctx context.Context, arg CreateEventMediaItemParams) (EventMediaItem, error) {
	row := q.db.QueryRow(ctx, createEventMediaItem,
		arg.EventID,
		arg.FileName,
		arg.FilePath,
		arg.FileType,
		arg.FileSize,
		arg.IsBanner,
	)
	var i EventMediaItem
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.UploadedAt,
		&i.IsBanner,
	)
	return i, err
}

const createEventRegistration = `-- name: CreateEventRegistration :one
INSERT INTO event_registrations (
    event_id, user_id, status, payment_status, registration_role, waitlist_priority
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at
`

type CreateEventRegistrationParams struct {
	EventID          uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID           uuid.UUID                   `db:"user_id" json:"user_id"`
	Status           EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus    PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	WaitlistPriority *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
}

type CreateEventRegistrationRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
}

func (q *Queries) CreateEventRegistration(ctx context.Context, arg CreateEventRegistrationParams) (CreateEventRegistrationRow, error) {
	row := q.db.QueryRow(ctx, createEventRegistration,
		arg.EventID,
		arg.UserID,
		arg.Status,
		arg.PaymentStatus,
		arg.RegistrationRole,
		arg.WaitlistPriority,
	)
	var i CreateEventRegistrationRow
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createEventTag = `-- name: CreateEventTag :one
INSERT INTO event_tags (
    name_en, name_zh_hk, name_zh_cn, is_globally_approved, created_by_user_id
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_by_user_id, is_globally_approved, created_at, updated_at
`

type CreateEventTagParams struct {
	NameEn             string     `db:"name_en" json:"name_en"`
	NameZhHk           string     `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn           string     `db:"name_zh_cn" json:"name_zh_cn"`
	IsGloballyApproved bool       `db:"is_globally_approved" json:"is_globally_approved"`
	CreatedByUserID    *uuid.UUID `db:"created_by_user_id" json:"created_by_user_id"`
}

func (q *Queries) CreateEventTag(ctx context.Context, arg CreateEventTagParams) (EventTag, error) {
	row := q.db.QueryRow(ctx, createEventTag,
		arg.NameEn,
		arg.NameZhHk,
		arg.NameZhCn,
		arg.IsGloballyApproved,
		arg.CreatedByUserID,
	)
	var i EventTag
	err := row.Scan(
		&i.ID,
		&i.NameEn,
		&i.NameZhHk,
		&i.NameZhCn,
		&i.DescriptionEn,
		&i.DescriptionZhHk,
		&i.DescriptionZhCn,
		&i.CreatedByUserID,
		&i.IsGloballyApproved,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createEventVolunteerApplication = `-- name: CreateEventVolunteerApplication :one
INSERT INTO event_volunteer_applications (
    event_id, user_id, organization_id, status, application_notes_by_user
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING id, event_id, user_id, organization_id, application_notes_by_user, admin_review_notes, applied_at, reviewed_at, reviewed_by_user_id, created_at, updated_at, status, attended_at
`

type CreateEventVolunteerApplicationParams struct {
	EventID                uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                 uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID         uuid.UUID             `db:"organization_id" json:"organization_id"`
	Status                 ApplicationStatusEnum `db:"status" json:"status"`
	ApplicationNotesByUser *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
}

func (q *Queries) CreateEventVolunteerApplication(ctx context.Context, arg CreateEventVolunteerApplicationParams) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, createEventVolunteerApplication,
		arg.EventID,
		arg.UserID,
		arg.OrganizationID,
		arg.Status,
		arg.ApplicationNotesByUser,
	)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}

const deleteEvent = `-- name: DeleteEvent :exec
DELETE FROM events WHERE id = $1
`

func (q *Queries) DeleteEvent(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteEvent, id)
	return err
}

const deleteEventMediaItem = `-- name: DeleteEventMediaItem :exec
DELETE FROM event_media_items WHERE id = $1
`

func (q *Queries) DeleteEventMediaItem(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteEventMediaItem, id)
	return err
}

const deleteEventMediaItemsByEventID = `-- name: DeleteEventMediaItemsByEventID :exec
DELETE FROM event_media_items
WHERE event_id = $1
`

func (q *Queries) DeleteEventMediaItemsByEventID(ctx context.Context, eventID uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteEventMediaItemsByEventID, eventID)
	return err
}

const deleteEventRegistration = `-- name: DeleteEventRegistration :exec
DELETE FROM event_registrations
WHERE id = $1
`

func (q *Queries) DeleteEventRegistration(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteEventRegistration, id)
	return err
}

const deleteEventTag = `-- name: DeleteEventTag :exec
DELETE FROM event_tags
WHERE id = $1
`

func (q *Queries) DeleteEventTag(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteEventTag, id)
	return err
}

const deleteMediaItemsForEvent = `-- name: DeleteMediaItemsForEvent :exec
DELETE FROM event_media_items WHERE event_id = $1
`

func (q *Queries) DeleteMediaItemsForEvent(ctx context.Context, eventID uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteMediaItemsForEvent, eventID)
	return err
}

const getEventByID = `-- name: GetEventByID :one
SELECT id, organization_id, title, location_type, location_online_url, start_time, end_time, status, participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at, government_funding_keys, created_at, updated_at, location_full_address, description_content, price, contact_email, contact_phone, is_government_funded FROM events WHERE id = $1 LIMIT 1
`

func (q *Queries) GetEventByID(ctx context.Context, id uuid.UUID) (Event, error) {
	row := q.db.QueryRow(ctx, getEventByID, id)
	var i Event
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.LocationType,
		&i.LocationOnlineUrl,
		&i.StartTime,
		&i.EndTime,
		&i.Status,
		&i.ParticipantLimit,
		&i.WaitlistLimit,
		&i.RequiresApprovalForRegistration,
		&i.CreatedByUserID,
		&i.PublishedAt,
		&i.GovernmentFundingKeys,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LocationFullAddress,
		&i.DescriptionContent,
		&i.Price,
		&i.ContactEmail,
		&i.ContactPhone,
		&i.IsGovernmentFunded,
	)
	return i, err
}

const getEventDetailsWithCounts = `-- name: GetEventDetailsWithCounts :one
SELECT 
    e.id, e.organization_id, e.title, e.location_type, e.location_online_url, e.start_time, e.end_time, e.status, e.participant_limit, e.waitlist_limit, e.requires_approval_for_registration, e.created_by_user_id, e.published_at, e.government_funding_keys, e.created_at, e.updated_at, e.location_full_address, e.description_content, e.price, e.contact_email, e.contact_phone, e.is_government_funded,
    o.name as organization_name,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'registered') as registered_count,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'waitlisted') as waitlisted_count,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status = 'attended') as attended_count
FROM events e
JOIN organizations o ON e.organization_id = o.id
WHERE e.id = $1
`

type GetEventDetailsWithCountsRow struct {
	ID                              uuid.UUID         `db:"id" json:"id"`
	OrganizationID                  uuid.UUID         `db:"organization_id" json:"organization_id"`
	Title                           string            `db:"title" json:"title"`
	LocationType                    EventLocationType `db:"location_type" json:"location_type"`
	LocationOnlineUrl               *string           `db:"location_online_url" json:"location_online_url"`
	StartTime                       time.Time         `db:"start_time" json:"start_time"`
	EndTime                         time.Time         `db:"end_time" json:"end_time"`
	Status                          EventStatusType   `db:"status" json:"status"`
	ParticipantLimit                *int32            `db:"participant_limit" json:"participant_limit"`
	WaitlistLimit                   *int32            `db:"waitlist_limit" json:"waitlist_limit"`
	RequiresApprovalForRegistration bool              `db:"requires_approval_for_registration" json:"requires_approval_for_registration"`
	CreatedByUserID                 uuid.UUID         `db:"created_by_user_id" json:"created_by_user_id"`
	PublishedAt                     *time.Time        `db:"published_at" json:"published_at"`
	GovernmentFundingKeys           []string          `db:"government_funding_keys" json:"government_funding_keys"`
	CreatedAt                       time.Time         `db:"created_at" json:"created_at"`
	UpdatedAt                       time.Time         `db:"updated_at" json:"updated_at"`
	LocationFullAddress             *string           `db:"location_full_address" json:"location_full_address"`
	DescriptionContent              []byte            `db:"description_content" json:"description_content"`
	Price                           *string           `db:"price" json:"price"`
	ContactEmail                    *string           `db:"contact_email" json:"contact_email"`
	ContactPhone                    *string           `db:"contact_phone" json:"contact_phone"`
	IsGovernmentFunded              bool              `db:"is_government_funded" json:"is_government_funded"`
	OrganizationName                string            `db:"organization_name" json:"organization_name"`
	RegisteredCount                 int64             `db:"registered_count" json:"registered_count"`
	WaitlistedCount                 int64             `db:"waitlisted_count" json:"waitlisted_count"`
	AttendedCount                   int64             `db:"attended_count" json:"attended_count"`
}

func (q *Queries) GetEventDetailsWithCounts(ctx context.Context, id uuid.UUID) (GetEventDetailsWithCountsRow, error) {
	row := q.db.QueryRow(ctx, getEventDetailsWithCounts, id)
	var i GetEventDetailsWithCountsRow
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.LocationType,
		&i.LocationOnlineUrl,
		&i.StartTime,
		&i.EndTime,
		&i.Status,
		&i.ParticipantLimit,
		&i.WaitlistLimit,
		&i.RequiresApprovalForRegistration,
		&i.CreatedByUserID,
		&i.PublishedAt,
		&i.GovernmentFundingKeys,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LocationFullAddress,
		&i.DescriptionContent,
		&i.Price,
		&i.ContactEmail,
		&i.ContactPhone,
		&i.IsGovernmentFunded,
		&i.OrganizationName,
		&i.RegisteredCount,
		&i.WaitlistedCount,
		&i.AttendedCount,
	)
	return i, err
}

const getEventMediaItemByID = `-- name: GetEventMediaItemByID :one
SELECT id, event_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner FROM event_media_items
WHERE id = $1
`

func (q *Queries) GetEventMediaItemByID(ctx context.Context, id uuid.UUID) (EventMediaItem, error) {
	row := q.db.QueryRow(ctx, getEventMediaItemByID, id)
	var i EventMediaItem
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.UploadedAt,
		&i.IsBanner,
	)
	return i, err
}

const getEventOrganizationID = `-- name: GetEventOrganizationID :one

SELECT organization_id FROM events
WHERE id = $1
`

// Query to get Event's Organization ID --
func (q *Queries) GetEventOrganizationID(ctx context.Context, id uuid.UUID) (uuid.UUID, error) {
	row := q.db.QueryRow(ctx, getEventOrganizationID, id)
	var organization_id uuid.UUID
	err := row.Scan(&organization_id)
	return organization_id, err
}

const getEventRegistrationByUserAndEvent = `-- name: GetEventRegistrationByUserAndEvent :one
SELECT id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at FROM event_registrations
WHERE user_id = $1 AND event_id = $2
`

type GetEventRegistrationByUserAndEventParams struct {
	UserID  uuid.UUID `db:"user_id" json:"user_id"`
	EventID uuid.UUID `db:"event_id" json:"event_id"`
}

type GetEventRegistrationByUserAndEventRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
}

func (q *Queries) GetEventRegistrationByUserAndEvent(ctx context.Context, arg GetEventRegistrationByUserAndEventParams) (GetEventRegistrationByUserAndEventRow, error) {
	row := q.db.QueryRow(ctx, getEventRegistrationByUserAndEvent, arg.UserID, arg.EventID)
	var i GetEventRegistrationByUserAndEventRow
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getEventRegistrationsForUserByEventIDs = `-- name: GetEventRegistrationsForUserByEventIDs :many
SELECT er.id, er.event_id, er.user_id, er.status, er.payment_status, er.registration_role, er.registered_at, er.attended_at, er.cancellation_reason_by_user, er.admin_notes_on_registration, er.waitlist_priority, er.created_at, er.updated_at, er.check_in_by_user_id, er.check_in_method, e.title AS event_title
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
AND er.event_id = ANY($2::UUID[])
ORDER BY e.start_time DESC
`

type GetEventRegistrationsForUserByEventIDsParams struct {
	UserID   uuid.UUID   `db:"user_id" json:"user_id"`
	EventIds []uuid.UUID `db:"event_ids" json:"event_ids"`
}

type GetEventRegistrationsForUserByEventIDsRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
	CheckInByUserID          *uuid.UUID                  `db:"check_in_by_user_id" json:"check_in_by_user_id"`
	CheckInMethod            *string                     `db:"check_in_method" json:"check_in_method"`
	EventTitle               string                      `db:"event_title" json:"event_title"`
}

func (q *Queries) GetEventRegistrationsForUserByEventIDs(ctx context.Context, arg GetEventRegistrationsForUserByEventIDsParams) ([]GetEventRegistrationsForUserByEventIDsRow, error) {
	rows, err := q.db.Query(ctx, getEventRegistrationsForUserByEventIDs, arg.UserID, arg.EventIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetEventRegistrationsForUserByEventIDsRow{}
	for rows.Next() {
		var i GetEventRegistrationsForUserByEventIDsRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.Status,
			&i.PaymentStatus,
			&i.RegistrationRole,
			&i.RegisteredAt,
			&i.AttendedAt,
			&i.CancellationReasonByUser,
			&i.AdminNotesOnRegistration,
			&i.WaitlistPriority,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CheckInByUserID,
			&i.CheckInMethod,
			&i.EventTitle,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventTag = `-- name: GetEventTag :one
SELECT id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_by_user_id, is_globally_approved, created_at, updated_at FROM event_tags
WHERE id = $1
`

func (q *Queries) GetEventTag(ctx context.Context, id uuid.UUID) (EventTag, error) {
	row := q.db.QueryRow(ctx, getEventTag, id)
	var i EventTag
	err := row.Scan(
		&i.ID,
		&i.NameEn,
		&i.NameZhHk,
		&i.NameZhCn,
		&i.DescriptionEn,
		&i.DescriptionZhHk,
		&i.DescriptionZhCn,
		&i.CreatedByUserID,
		&i.IsGloballyApproved,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getEventVolunteerApplicationByID = `-- name: GetEventVolunteerApplicationByID :one
SELECT id, event_id, user_id, organization_id, application_notes_by_user, admin_review_notes, applied_at, reviewed_at, reviewed_by_user_id, created_at, updated_at, status, attended_at FROM event_volunteer_applications
WHERE id = $1
`

func (q *Queries) GetEventVolunteerApplicationByID(ctx context.Context, id uuid.UUID) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, getEventVolunteerApplicationByID, id)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}

const getEventVolunteerApplicationByUserAndEvent = `-- name: GetEventVolunteerApplicationByUserAndEvent :one
SELECT id, event_id, user_id, organization_id, application_notes_by_user, admin_review_notes, applied_at, reviewed_at, reviewed_by_user_id, created_at, updated_at, status, attended_at FROM event_volunteer_applications
WHERE user_id = $1 AND event_id = $2
`

type GetEventVolunteerApplicationByUserAndEventParams struct {
	UserID  uuid.UUID `db:"user_id" json:"user_id"`
	EventID uuid.UUID `db:"event_id" json:"event_id"`
}

func (q *Queries) GetEventVolunteerApplicationByUserAndEvent(ctx context.Context, arg GetEventVolunteerApplicationByUserAndEventParams) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, getEventVolunteerApplicationByUserAndEvent, arg.UserID, arg.EventID)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}

const getEventWithConfigurationForRegistration = `-- name: GetEventWithConfigurationForRegistration :one

SELECT 
    e.id as event_id,
    e.title as event_title,
    e.start_time as event_start_time,
    e.end_time as event_end_time,
    e.status as event_status,
    e.participant_limit as event_participant_limit,
    e.waitlist_limit as event_waitlist_limit,
    e.requires_approval_for_registration as event_requires_approval,
    e.published_at as event_published_at,
    e.government_funding_keys as event_government_funding_keys,
    o.id as organization_id,
    o.name as organization_name,
    o.image_url as organization_logo_url,
    o.theme_color as organization_theme_color
FROM
    events e
JOIN
    organizations o ON e.organization_id = o.id
WHERE
    e.id = $1
`

type GetEventWithConfigurationForRegistrationRow struct {
	EventID                    uuid.UUID       `db:"event_id" json:"event_id"`
	EventTitle                 string          `db:"event_title" json:"event_title"`
	EventStartTime             time.Time       `db:"event_start_time" json:"event_start_time"`
	EventEndTime               time.Time       `db:"event_end_time" json:"event_end_time"`
	EventStatus                EventStatusType `db:"event_status" json:"event_status"`
	EventParticipantLimit      *int32          `db:"event_participant_limit" json:"event_participant_limit"`
	EventWaitlistLimit         *int32          `db:"event_waitlist_limit" json:"event_waitlist_limit"`
	EventRequiresApproval      bool            `db:"event_requires_approval" json:"event_requires_approval"`
	EventPublishedAt           *time.Time      `db:"event_published_at" json:"event_published_at"`
	EventGovernmentFundingKeys []string        `db:"event_government_funding_keys" json:"event_government_funding_keys"`
	OrganizationID             uuid.UUID       `db:"organization_id" json:"organization_id"`
	OrganizationName           string          `db:"organization_name" json:"organization_name"`
	OrganizationLogoUrl        *string         `db:"organization_logo_url" json:"organization_logo_url"`
	OrganizationThemeColor     *string         `db:"organization_theme_color" json:"organization_theme_color"`
}

// END: Additional Event Volunteer Application Queries
func (q *Queries) GetEventWithConfigurationForRegistration(ctx context.Context, id uuid.UUID) (GetEventWithConfigurationForRegistrationRow, error) {
	row := q.db.QueryRow(ctx, getEventWithConfigurationForRegistration, id)
	var i GetEventWithConfigurationForRegistrationRow
	err := row.Scan(
		&i.EventID,
		&i.EventTitle,
		&i.EventStartTime,
		&i.EventEndTime,
		&i.EventStatus,
		&i.EventParticipantLimit,
		&i.EventWaitlistLimit,
		&i.EventRequiresApproval,
		&i.EventPublishedAt,
		&i.EventGovernmentFundingKeys,
		&i.OrganizationID,
		&i.OrganizationName,
		&i.OrganizationLogoUrl,
		&i.OrganizationThemeColor,
	)
	return i, err
}

const getEventWithOrganizationByID = `-- name: GetEventWithOrganizationByID :one
SELECT e.id, e.organization_id, e.title, e.location_type, e.location_online_url, e.start_time, e.end_time, e.status, e.participant_limit, e.waitlist_limit, e.requires_approval_for_registration, e.created_by_user_id, e.published_at, e.government_funding_keys, e.created_at, e.updated_at, e.location_full_address, e.description_content, e.price, e.contact_email, e.contact_phone, e.is_government_funded, o.name as organization_name
FROM events e
JOIN organizations o ON e.organization_id = o.id
WHERE e.id = $1
`

type GetEventWithOrganizationByIDRow struct {
	ID                              uuid.UUID         `db:"id" json:"id"`
	OrganizationID                  uuid.UUID         `db:"organization_id" json:"organization_id"`
	Title                           string            `db:"title" json:"title"`
	LocationType                    EventLocationType `db:"location_type" json:"location_type"`
	LocationOnlineUrl               *string           `db:"location_online_url" json:"location_online_url"`
	StartTime                       time.Time         `db:"start_time" json:"start_time"`
	EndTime                         time.Time         `db:"end_time" json:"end_time"`
	Status                          EventStatusType   `db:"status" json:"status"`
	ParticipantLimit                *int32            `db:"participant_limit" json:"participant_limit"`
	WaitlistLimit                   *int32            `db:"waitlist_limit" json:"waitlist_limit"`
	RequiresApprovalForRegistration bool              `db:"requires_approval_for_registration" json:"requires_approval_for_registration"`
	CreatedByUserID                 uuid.UUID         `db:"created_by_user_id" json:"created_by_user_id"`
	PublishedAt                     *time.Time        `db:"published_at" json:"published_at"`
	GovernmentFundingKeys           []string          `db:"government_funding_keys" json:"government_funding_keys"`
	CreatedAt                       time.Time         `db:"created_at" json:"created_at"`
	UpdatedAt                       time.Time         `db:"updated_at" json:"updated_at"`
	LocationFullAddress             *string           `db:"location_full_address" json:"location_full_address"`
	DescriptionContent              []byte            `db:"description_content" json:"description_content"`
	Price                           *string           `db:"price" json:"price"`
	ContactEmail                    *string           `db:"contact_email" json:"contact_email"`
	ContactPhone                    *string           `db:"contact_phone" json:"contact_phone"`
	IsGovernmentFunded              bool              `db:"is_government_funded" json:"is_government_funded"`
	OrganizationName                string            `db:"organization_name" json:"organization_name"`
}

func (q *Queries) GetEventWithOrganizationByID(ctx context.Context, id uuid.UUID) (GetEventWithOrganizationByIDRow, error) {
	row := q.db.QueryRow(ctx, getEventWithOrganizationByID, id)
	var i GetEventWithOrganizationByIDRow
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.LocationType,
		&i.LocationOnlineUrl,
		&i.StartTime,
		&i.EndTime,
		&i.Status,
		&i.ParticipantLimit,
		&i.WaitlistLimit,
		&i.RequiresApprovalForRegistration,
		&i.CreatedByUserID,
		&i.PublishedAt,
		&i.GovernmentFundingKeys,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LocationFullAddress,
		&i.DescriptionContent,
		&i.Price,
		&i.ContactEmail,
		&i.ContactPhone,
		&i.IsGovernmentFunded,
		&i.OrganizationName,
	)
	return i, err
}

const getEventsForTag = `-- name: GetEventsForTag :many
SELECT e.id, e.organization_id, e.title, e.location_type, e.location_online_url, e.start_time, e.end_time, e.status, e.participant_limit, e.waitlist_limit, e.requires_approval_for_registration, e.created_by_user_id, e.published_at, e.government_funding_keys, e.created_at, e.updated_at, e.location_full_address, e.description_content, e.price, e.contact_email, e.contact_phone, e.is_government_funded
FROM events e
JOIN event_event_tags eet ON e.id = eet.event_id
WHERE eet.event_tag_id = $1
ORDER BY e.start_time DESC
`

func (q *Queries) GetEventsForTag(ctx context.Context, eventTagID uuid.UUID) ([]Event, error) {
	rows, err := q.db.Query(ctx, getEventsForTag, eventTagID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Event{}
	for rows.Next() {
		var i Event
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.LocationType,
			&i.LocationOnlineUrl,
			&i.StartTime,
			&i.EndTime,
			&i.Status,
			&i.ParticipantLimit,
			&i.WaitlistLimit,
			&i.RequiresApprovalForRegistration,
			&i.CreatedByUserID,
			&i.PublishedAt,
			&i.GovernmentFundingKeys,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LocationFullAddress,
			&i.DescriptionContent,
			&i.Price,
			&i.ContactEmail,
			&i.ContactPhone,
			&i.IsGovernmentFunded,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOldestWaitlistedRegistration = `-- name: GetOldestWaitlistedRegistration :one
SELECT id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at FROM event_registrations
WHERE event_id = $1 AND status = 'waitlisted'
ORDER BY waitlist_priority ASC, registered_at ASC
LIMIT 1
FOR UPDATE
`

type GetOldestWaitlistedRegistrationRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
}

func (q *Queries) GetOldestWaitlistedRegistration(ctx context.Context, eventID uuid.UUID) (GetOldestWaitlistedRegistrationRow, error) {
	row := q.db.QueryRow(ctx, getOldestWaitlistedRegistration, eventID)
	var i GetOldestWaitlistedRegistrationRow
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getOrganizationEventStatistics = `-- name: GetOrganizationEventStatistics :many
SELECT 
    e.id as event_id,
    e.title as event_title,
    e.start_time as event_start_time,
    e.status as event_event_status, -- aliased to avoid conflict with registration status
    (SELECT COUNT(*) FROM event_registrations er_total WHERE er_total.event_id = e.id AND (er_total.status='registered' OR er_total.status='attended' OR er_total.status='absent')) as total_registered_or_finalized,
    (SELECT COUNT(*) FROM event_registrations er_attended WHERE er_attended.event_id = e.id AND er_attended.status = 'attended') as total_attended,
    (SELECT COUNT(*) FROM event_registrations er_paid WHERE er_paid.event_id = e.id AND er_paid.payment_status = 'paid') as total_paid_registrations
FROM events e
WHERE e.organization_id = $1
  AND e.end_time < NOW()
  AND e.end_time > COALESCE($4::TIMESTAMPTZ, '-infinity')
  AND e.start_time < COALESCE($5::TIMESTAMPTZ, 'infinity')
  -- TODO: Add event type/tag filter by joining with event_event_tags and event_tags
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3
`

type GetOrganizationEventStatisticsParams struct {
	OrganizationID  uuid.UUID  `db:"organization_id" json:"organization_id"`
	Limit           int32      `db:"limit" json:"limit"`
	Offset          int32      `db:"offset" json:"offset"`
	StartDateFilter *time.Time `db:"start_date_filter" json:"start_date_filter"`
	EndDateFilter   *time.Time `db:"end_date_filter" json:"end_date_filter"`
}

type GetOrganizationEventStatisticsRow struct {
	EventID                    uuid.UUID       `db:"event_id" json:"event_id"`
	EventTitle                 string          `db:"event_title" json:"event_title"`
	EventStartTime             time.Time       `db:"event_start_time" json:"event_start_time"`
	EventEventStatus           EventStatusType `db:"event_event_status" json:"event_event_status"`
	TotalRegisteredOrFinalized int64           `db:"total_registered_or_finalized" json:"total_registered_or_finalized"`
	TotalAttended              int64           `db:"total_attended" json:"total_attended"`
	TotalPaidRegistrations     int64           `db:"total_paid_registrations" json:"total_paid_registrations"`
}

func (q *Queries) GetOrganizationEventStatistics(ctx context.Context, arg GetOrganizationEventStatisticsParams) ([]GetOrganizationEventStatisticsRow, error) {
	rows, err := q.db.Query(ctx, getOrganizationEventStatistics,
		arg.OrganizationID,
		arg.Limit,
		arg.Offset,
		arg.StartDateFilter,
		arg.EndDateFilter,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetOrganizationEventStatisticsRow{}
	for rows.Next() {
		var i GetOrganizationEventStatisticsRow
		if err := rows.Scan(
			&i.EventID,
			&i.EventTitle,
			&i.EventStartTime,
			&i.EventEventStatus,
			&i.TotalRegisteredOrFinalized,
			&i.TotalAttended,
			&i.TotalPaidRegistrations,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRegisteredAndApprovedCountForEvent = `-- name: GetRegisteredAndApprovedCountForEvent :one

SELECT COUNT(*) FROM event_registrations
WHERE event_id = $1 AND (status = 'registered' OR status = 'pending_approval')
`

// For atomicity if promoting
func (q *Queries) GetRegisteredAndApprovedCountForEvent(ctx context.Context, eventID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getRegisteredAndApprovedCountForEvent, eventID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getRegisteredCountForEvent = `-- name: GetRegisteredCountForEvent :one
SELECT COUNT(*) FROM event_registrations
WHERE event_id = $1 AND status IN ('registered', 'pending_approval')
`

func (q *Queries) GetRegisteredCountForEvent(ctx context.Context, eventID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getRegisteredCountForEvent, eventID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getScheduledEventsToPublish = `-- name: GetScheduledEventsToPublish :many
SELECT id FROM events WHERE status = 'draft' AND published_at IS NOT NULL AND published_at <= NOW()
`

func (q *Queries) GetScheduledEventsToPublish(ctx context.Context) ([]uuid.UUID, error) {
	rows, err := q.db.Query(ctx, getScheduledEventsToPublish)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []uuid.UUID{}
	for rows.Next() {
		var id uuid.UUID
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		items = append(items, id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUpcomingEventsForUserReminders = `-- name: GetUpcomingEventsForUserReminders :many
SELECT
    id, title, start_time
FROM events
WHERE
    status = 'published' 
    AND start_time > NOW()
ORDER BY start_time ASC
LIMIT $1
`

type GetUpcomingEventsForUserRemindersRow struct {
	ID        uuid.UUID `db:"id" json:"id"`
	Title     string    `db:"title" json:"title"`
	StartTime time.Time `db:"start_time" json:"start_time"`
}

func (q *Queries) GetUpcomingEventsForUserReminders(ctx context.Context, limit int32) ([]GetUpcomingEventsForUserRemindersRow, error) {
	rows, err := q.db.Query(ctx, getUpcomingEventsForUserReminders, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetUpcomingEventsForUserRemindersRow{}
	for rows.Next() {
		var i GetUpcomingEventsForUserRemindersRow
		if err := rows.Scan(&i.ID, &i.Title, &i.StartTime); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserEventStatistics = `-- name: GetUserEventStatistics :many
SELECT 
    e.id as event_id,
    e.title as event_title,
    e.start_time as event_start_time,
    e.status as event_status,
    er.status as registration_status,
    er.payment_status as registration_payment_status,
    er.attended_at IS NOT NULL as attended
FROM events e
JOIN event_registrations er ON e.id = er.event_id
WHERE er.user_id = $1
  AND e.status = 'past'
  AND e.end_time > COALESCE($4::TIMESTAMPTZ, '-infinity')
  AND e.start_time < COALESCE($5::TIMESTAMPTZ, 'infinity')
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3
`

type GetUserEventStatisticsParams struct {
	UserID          uuid.UUID  `db:"user_id" json:"user_id"`
	Limit           int32      `db:"limit" json:"limit"`
	Offset          int32      `db:"offset" json:"offset"`
	StartDateFilter *time.Time `db:"start_date_filter" json:"start_date_filter"`
	EndDateFilter   *time.Time `db:"end_date_filter" json:"end_date_filter"`
}

type GetUserEventStatisticsRow struct {
	EventID                   uuid.UUID                   `db:"event_id" json:"event_id"`
	EventTitle                string                      `db:"event_title" json:"event_title"`
	EventStartTime            time.Time                   `db:"event_start_time" json:"event_start_time"`
	EventStatus               EventStatusType             `db:"event_status" json:"event_status"`
	RegistrationStatus        EventRegistrationStatusType `db:"registration_status" json:"registration_status"`
	RegistrationPaymentStatus PaymentStatusType           `db:"registration_payment_status" json:"registration_payment_status"`
	Attended                  interface{}                 `db:"attended" json:"attended"`
}

func (q *Queries) GetUserEventStatistics(ctx context.Context, arg GetUserEventStatisticsParams) ([]GetUserEventStatisticsRow, error) {
	rows, err := q.db.Query(ctx, getUserEventStatistics,
		arg.UserID,
		arg.Limit,
		arg.Offset,
		arg.StartDateFilter,
		arg.EndDateFilter,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetUserEventStatisticsRow{}
	for rows.Next() {
		var i GetUserEventStatisticsRow
		if err := rows.Scan(
			&i.EventID,
			&i.EventTitle,
			&i.EventStartTime,
			&i.EventStatus,
			&i.RegistrationStatus,
			&i.RegistrationPaymentStatus,
			&i.Attended,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserEventVolunteerApplicationByID = `-- name: GetUserEventVolunteerApplicationByID :one
SELECT
    eva.id, eva.event_id, eva.user_id, eva.organization_id, eva.application_notes_by_user, eva.admin_review_notes, eva.applied_at, eva.reviewed_at, eva.reviewed_by_user_id, eva.created_at, eva.updated_at, eva.status, eva.attended_at,
    e.title as event_title,
    e.start_time as event_start_time,
    e.end_time as event_end_time,
    e.description_content as event_description,
    e.location_type as event_location_type,
    e.location_full_address as event_location_full_address,
    e.location_online_url as event_location_online_url,
    e.status as event_status,
    e.organization_id as event_organization_id,
    o.name as organization_name,
    o.image_url as organization_logo_url,
    e.price as event_price,
    e.contact_email as event_contact_email,
    e.contact_phone as event_contact_phone,
    COALESCE(
        (SELECT JSON_AGG(JSON_BUILD_OBJECT(
            'id', emi.id,
            'file_name', emi.file_name,
            'file_path', emi.file_path,
            'file_type', emi.file_type,
            'file_size', emi.file_size,
            'uploaded_at', emi.uploaded_at,
            'is_banner', emi.is_banner
        ))
        FROM event_media_items emi
        WHERE emi.event_id = e.id),
        '[]'::JSON
    ) AS media_items
FROM event_volunteer_applications eva
JOIN events e ON eva.event_id = e.id
LEFT JOIN organizations o ON e.organization_id = o.id
WHERE eva.id = $1 AND eva.user_id = $2
`

type GetUserEventVolunteerApplicationByIDParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	UserID uuid.UUID `db:"user_id" json:"user_id"`
}

type GetUserEventVolunteerApplicationByIDRow struct {
	ID                       uuid.UUID             `db:"id" json:"id"`
	EventID                  uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID           uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationNotesByUser   *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
	AdminReviewNotes         *string               `db:"admin_review_notes" json:"admin_review_notes"`
	AppliedAt                time.Time             `db:"applied_at" json:"applied_at"`
	ReviewedAt               *time.Time            `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID         *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	CreatedAt                time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time             `db:"updated_at" json:"updated_at"`
	Status                   ApplicationStatusEnum `db:"status" json:"status"`
	AttendedAt               *time.Time            `db:"attended_at" json:"attended_at"`
	EventTitle               string                `db:"event_title" json:"event_title"`
	EventStartTime           time.Time             `db:"event_start_time" json:"event_start_time"`
	EventEndTime             time.Time             `db:"event_end_time" json:"event_end_time"`
	EventDescription         []byte                `db:"event_description" json:"event_description"`
	EventLocationType        EventLocationType     `db:"event_location_type" json:"event_location_type"`
	EventLocationFullAddress *string               `db:"event_location_full_address" json:"event_location_full_address"`
	EventLocationOnlineUrl   *string               `db:"event_location_online_url" json:"event_location_online_url"`
	EventStatus              EventStatusType       `db:"event_status" json:"event_status"`
	EventOrganizationID      uuid.UUID             `db:"event_organization_id" json:"event_organization_id"`
	OrganizationName         *string               `db:"organization_name" json:"organization_name"`
	OrganizationLogoUrl      *string               `db:"organization_logo_url" json:"organization_logo_url"`
	EventPrice               *string               `db:"event_price" json:"event_price"`
	EventContactEmail        *string               `db:"event_contact_email" json:"event_contact_email"`
	EventContactPhone        *string               `db:"event_contact_phone" json:"event_contact_phone"`
	MediaItems               interface{}           `db:"media_items" json:"media_items"`
}

func (q *Queries) GetUserEventVolunteerApplicationByID(ctx context.Context, arg GetUserEventVolunteerApplicationByIDParams) (GetUserEventVolunteerApplicationByIDRow, error) {
	row := q.db.QueryRow(ctx, getUserEventVolunteerApplicationByID, arg.ID, arg.UserID)
	var i GetUserEventVolunteerApplicationByIDRow
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
		&i.EventTitle,
		&i.EventStartTime,
		&i.EventEndTime,
		&i.EventDescription,
		&i.EventLocationType,
		&i.EventLocationFullAddress,
		&i.EventLocationOnlineUrl,
		&i.EventStatus,
		&i.EventOrganizationID,
		&i.OrganizationName,
		&i.OrganizationLogoUrl,
		&i.EventPrice,
		&i.EventContactEmail,
		&i.EventContactPhone,
		&i.MediaItems,
	)
	return i, err
}

const getVolunteerApplicationByUserAndEventAndOrg = `-- name: GetVolunteerApplicationByUserAndEventAndOrg :one
SELECT eva.id, eva.event_id, eva.user_id, eva.organization_id, eva.application_notes_by_user, eva.admin_review_notes, eva.applied_at, eva.reviewed_at, eva.reviewed_by_user_id, eva.created_at, eva.updated_at, eva.status, eva.attended_at
FROM event_volunteer_applications eva
WHERE eva.user_id = $1 
  AND eva.event_id = $2
  AND eva.organization_id = $3
`

type GetVolunteerApplicationByUserAndEventAndOrgParams struct {
	UserID         uuid.UUID `db:"user_id" json:"user_id"`
	EventID        uuid.UUID `db:"event_id" json:"event_id"`
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
}

func (q *Queries) GetVolunteerApplicationByUserAndEventAndOrg(ctx context.Context, arg GetVolunteerApplicationByUserAndEventAndOrgParams) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, getVolunteerApplicationByUserAndEventAndOrg, arg.UserID, arg.EventID, arg.OrganizationID)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}

const getVolunteerApplicationsForEvent = `-- name: GetVolunteerApplicationsForEvent :many
SELECT eva.id, eva.event_id, eva.user_id, eva.organization_id, eva.application_notes_by_user, eva.admin_review_notes, eva.applied_at, eva.reviewed_at, eva.reviewed_by_user_id, eva.created_at, eva.updated_at, eva.status, eva.attended_at, u.id, u.display_name, u.hashed_password, u.profile_picture_url, u.phone, u.phone_verified_at, u.email, u.email_verified_at, u.phone_otp_channel, u.interface_language, u.communication_language, u.enable_app_notifications, u.enable_whatsapp_notifications, u.enable_sms_notifications, u.enable_email_notifications, u.created_at, u.updated_at, u.role
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
WHERE eva.event_id = $1
ORDER BY eva.applied_at ASC
LIMIT $2 OFFSET $3
`

type GetVolunteerApplicationsForEventParams struct {
	EventID uuid.UUID `db:"event_id" json:"event_id"`
	Limit   int32     `db:"limit" json:"limit"`
	Offset  int32     `db:"offset" json:"offset"`
}

type GetVolunteerApplicationsForEventRow struct {
	EventVolunteerApplication EventVolunteerApplication `db:"event_volunteer_application" json:"event_volunteer_application"`
	User                      User                      `db:"user" json:"user"`
}

func (q *Queries) GetVolunteerApplicationsForEvent(ctx context.Context, arg GetVolunteerApplicationsForEventParams) ([]GetVolunteerApplicationsForEventRow, error) {
	rows, err := q.db.Query(ctx, getVolunteerApplicationsForEvent, arg.EventID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetVolunteerApplicationsForEventRow{}
	for rows.Next() {
		var i GetVolunteerApplicationsForEventRow
		if err := rows.Scan(
			&i.EventVolunteerApplication.ID,
			&i.EventVolunteerApplication.EventID,
			&i.EventVolunteerApplication.UserID,
			&i.EventVolunteerApplication.OrganizationID,
			&i.EventVolunteerApplication.ApplicationNotesByUser,
			&i.EventVolunteerApplication.AdminReviewNotes,
			&i.EventVolunteerApplication.AppliedAt,
			&i.EventVolunteerApplication.ReviewedAt,
			&i.EventVolunteerApplication.ReviewedByUserID,
			&i.EventVolunteerApplication.CreatedAt,
			&i.EventVolunteerApplication.UpdatedAt,
			&i.EventVolunteerApplication.Status,
			&i.EventVolunteerApplication.AttendedAt,
			&i.User.ID,
			&i.User.DisplayName,
			&i.User.HashedPassword,
			&i.User.ProfilePictureUrl,
			&i.User.Phone,
			&i.User.PhoneVerifiedAt,
			&i.User.Email,
			&i.User.EmailVerifiedAt,
			&i.User.PhoneOtpChannel,
			&i.User.InterfaceLanguage,
			&i.User.CommunicationLanguage,
			&i.User.EnableAppNotifications,
			&i.User.EnableWhatsappNotifications,
			&i.User.EnableSmsNotifications,
			&i.User.EnableEmailNotifications,
			&i.User.CreatedAt,
			&i.User.UpdatedAt,
			&i.User.Role,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWaitlistedCountForEvent = `-- name: GetWaitlistedCountForEvent :one

SELECT COUNT(*) FROM event_registrations
WHERE event_id = $1 AND status = 'waitlisted'
`

// Removed 'confirmed'
func (q *Queries) GetWaitlistedCountForEvent(ctx context.Context, eventID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getWaitlistedCountForEvent, eventID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const listAllPendingReviewEventVolunteerApplications = `-- name: ListAllPendingReviewEventVolunteerApplications :many
SELECT
    eva.id, eva.event_id, eva.user_id, eva.organization_id, eva.application_notes_by_user, eva.admin_review_notes, eva.applied_at, eva.reviewed_at, eva.reviewed_by_user_id, eva.created_at, eva.updated_at, eva.status, eva.attended_at,
    u.display_name AS applicant_display_name,
    u.email AS applicant_email,
    u.phone AS applicant_phone,
    e.title AS event_title,
    o.name AS organization_name
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
JOIN events e ON eva.event_id = e.id
JOIN organizations o ON eva.organization_id = o.id
WHERE eva.status = 'pending'
ORDER BY o.name ASC, e.start_time ASC, eva.applied_at ASC
LIMIT $1 OFFSET $2
`

type ListAllPendingReviewEventVolunteerApplicationsParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

type ListAllPendingReviewEventVolunteerApplicationsRow struct {
	ID                     uuid.UUID             `db:"id" json:"id"`
	EventID                uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                 uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID         uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationNotesByUser *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
	AdminReviewNotes       *string               `db:"admin_review_notes" json:"admin_review_notes"`
	AppliedAt              time.Time             `db:"applied_at" json:"applied_at"`
	ReviewedAt             *time.Time            `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID       *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	CreatedAt              time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt              time.Time             `db:"updated_at" json:"updated_at"`
	Status                 ApplicationStatusEnum `db:"status" json:"status"`
	AttendedAt             *time.Time            `db:"attended_at" json:"attended_at"`
	ApplicantDisplayName   string                `db:"applicant_display_name" json:"applicant_display_name"`
	ApplicantEmail         *string               `db:"applicant_email" json:"applicant_email"`
	ApplicantPhone         *string               `db:"applicant_phone" json:"applicant_phone"`
	EventTitle             string                `db:"event_title" json:"event_title"`
	OrganizationName       string                `db:"organization_name" json:"organization_name"`
}

func (q *Queries) ListAllPendingReviewEventVolunteerApplications(ctx context.Context, arg ListAllPendingReviewEventVolunteerApplicationsParams) ([]ListAllPendingReviewEventVolunteerApplicationsRow, error) {
	rows, err := q.db.Query(ctx, listAllPendingReviewEventVolunteerApplications, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListAllPendingReviewEventVolunteerApplicationsRow{}
	for rows.Next() {
		var i ListAllPendingReviewEventVolunteerApplicationsRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationNotesByUser,
			&i.AdminReviewNotes,
			&i.AppliedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.AttendedAt,
			&i.ApplicantDisplayName,
			&i.ApplicantEmail,
			&i.ApplicantPhone,
			&i.EventTitle,
			&i.OrganizationName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listEventMediaItems = `-- name: ListEventMediaItems :many
SELECT id, event_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner FROM event_media_items
WHERE event_id = $1
ORDER BY is_banner DESC, uploaded_at ASC
`

func (q *Queries) ListEventMediaItems(ctx context.Context, eventID uuid.UUID) ([]EventMediaItem, error) {
	rows, err := q.db.Query(ctx, listEventMediaItems, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []EventMediaItem{}
	for rows.Next() {
		var i EventMediaItem
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.UploadedAt,
			&i.IsBanner,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listEventTags = `-- name: ListEventTags :many
SELECT id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_by_user_id, is_globally_approved, created_at, updated_at FROM event_tags ORDER BY name_en
`

func (q *Queries) ListEventTags(ctx context.Context) ([]EventTag, error) {
	rows, err := q.db.Query(ctx, listEventTags)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []EventTag{}
	for rows.Next() {
		var i EventTag
		if err := rows.Scan(
			&i.ID,
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.DescriptionEn,
			&i.DescriptionZhHk,
			&i.DescriptionZhCn,
			&i.CreatedByUserID,
			&i.IsGloballyApproved,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listEventVolunteerApplicationsForEvent = `-- name: ListEventVolunteerApplicationsForEvent :many
SELECT va.id, va.event_id, va.user_id, va.organization_id, va.application_notes_by_user, va.admin_review_notes, va.applied_at, va.reviewed_at, va.reviewed_by_user_id, va.created_at, va.updated_at, va.status, va.attended_at, u.display_name as user_display_name, u.email as user_email, u.phone as user_phone
FROM event_volunteer_applications va
JOIN users u ON va.user_id = u.id
WHERE va.event_id = $1
ORDER BY va.applied_at ASC
LIMIT $2 OFFSET $3
`

type ListEventVolunteerApplicationsForEventParams struct {
	EventID uuid.UUID `db:"event_id" json:"event_id"`
	Limit   int32     `db:"limit" json:"limit"`
	Offset  int32     `db:"offset" json:"offset"`
}

type ListEventVolunteerApplicationsForEventRow struct {
	ID                     uuid.UUID             `db:"id" json:"id"`
	EventID                uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                 uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID         uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationNotesByUser *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
	AdminReviewNotes       *string               `db:"admin_review_notes" json:"admin_review_notes"`
	AppliedAt              time.Time             `db:"applied_at" json:"applied_at"`
	ReviewedAt             *time.Time            `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID       *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	CreatedAt              time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt              time.Time             `db:"updated_at" json:"updated_at"`
	Status                 ApplicationStatusEnum `db:"status" json:"status"`
	AttendedAt             *time.Time            `db:"attended_at" json:"attended_at"`
	UserDisplayName        string                `db:"user_display_name" json:"user_display_name"`
	UserEmail              *string               `db:"user_email" json:"user_email"`
	UserPhone              *string               `db:"user_phone" json:"user_phone"`
}

func (q *Queries) ListEventVolunteerApplicationsForEvent(ctx context.Context, arg ListEventVolunteerApplicationsForEventParams) ([]ListEventVolunteerApplicationsForEventRow, error) {
	rows, err := q.db.Query(ctx, listEventVolunteerApplicationsForEvent, arg.EventID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListEventVolunteerApplicationsForEventRow{}
	for rows.Next() {
		var i ListEventVolunteerApplicationsForEventRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationNotesByUser,
			&i.AdminReviewNotes,
			&i.AppliedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.AttendedAt,
			&i.UserDisplayName,
			&i.UserEmail,
			&i.UserPhone,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listEventVolunteerApplicationsForUser = `-- name: ListEventVolunteerApplicationsForUser :many
SELECT
    va.id, va.event_id, va.user_id, va.organization_id, va.application_notes_by_user, va.admin_review_notes, va.applied_at, va.reviewed_at, va.reviewed_by_user_id, va.created_at, va.updated_at, va.status, va.attended_at,
    e.title as event_title,
    e.start_time as event_start_time,
    e.end_time as event_end_time,
    e.description_content as event_description,
    e.location_type as event_location_type,
    e.location_full_address as event_location_full_address,
    e.location_online_url as event_location_online_url,
    e.status as event_status,
    e.organization_id as event_organization_id, -- This is the event's direct organization ID
    o.name as organization_name, -- Fetched via join for consistency
    e.price as event_price,
    e.contact_email as event_contact_email,
    e.contact_phone as event_contact_phone,
    COALESCE(
        (SELECT JSON_AGG(JSON_BUILD_OBJECT(
            'id', emi.id,
            'file_name', emi.file_name,
            'file_path', emi.file_path,
            'file_type', emi.file_type,
            'file_size', emi.file_size,
            'uploaded_at', emi.uploaded_at,
            'is_banner', emi.is_banner
        ))
        FROM event_media_items emi
        WHERE emi.event_id = e.id),
        '[]'::JSON
    ) AS media_items
FROM event_volunteer_applications va
JOIN events e ON va.event_id = e.id
LEFT JOIN organizations o ON e.organization_id = o.id -- Join to get organization name from event's perspective
WHERE va.user_id = $1
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3
`

type ListEventVolunteerApplicationsForUserParams struct {
	UserID uuid.UUID `db:"user_id" json:"user_id"`
	Limit  int32     `db:"limit" json:"limit"`
	Offset int32     `db:"offset" json:"offset"`
}

type ListEventVolunteerApplicationsForUserRow struct {
	ID                       uuid.UUID             `db:"id" json:"id"`
	EventID                  uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID           uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationNotesByUser   *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
	AdminReviewNotes         *string               `db:"admin_review_notes" json:"admin_review_notes"`
	AppliedAt                time.Time             `db:"applied_at" json:"applied_at"`
	ReviewedAt               *time.Time            `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID         *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	CreatedAt                time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time             `db:"updated_at" json:"updated_at"`
	Status                   ApplicationStatusEnum `db:"status" json:"status"`
	AttendedAt               *time.Time            `db:"attended_at" json:"attended_at"`
	EventTitle               string                `db:"event_title" json:"event_title"`
	EventStartTime           time.Time             `db:"event_start_time" json:"event_start_time"`
	EventEndTime             time.Time             `db:"event_end_time" json:"event_end_time"`
	EventDescription         []byte                `db:"event_description" json:"event_description"`
	EventLocationType        EventLocationType     `db:"event_location_type" json:"event_location_type"`
	EventLocationFullAddress *string               `db:"event_location_full_address" json:"event_location_full_address"`
	EventLocationOnlineUrl   *string               `db:"event_location_online_url" json:"event_location_online_url"`
	EventStatus              EventStatusType       `db:"event_status" json:"event_status"`
	EventOrganizationID      uuid.UUID             `db:"event_organization_id" json:"event_organization_id"`
	OrganizationName         *string               `db:"organization_name" json:"organization_name"`
	EventPrice               *string               `db:"event_price" json:"event_price"`
	EventContactEmail        *string               `db:"event_contact_email" json:"event_contact_email"`
	EventContactPhone        *string               `db:"event_contact_phone" json:"event_contact_phone"`
	MediaItems               interface{}           `db:"media_items" json:"media_items"`
}

func (q *Queries) ListEventVolunteerApplicationsForUser(ctx context.Context, arg ListEventVolunteerApplicationsForUserParams) ([]ListEventVolunteerApplicationsForUserRow, error) {
	rows, err := q.db.Query(ctx, listEventVolunteerApplicationsForUser, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListEventVolunteerApplicationsForUserRow{}
	for rows.Next() {
		var i ListEventVolunteerApplicationsForUserRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationNotesByUser,
			&i.AdminReviewNotes,
			&i.AppliedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.AttendedAt,
			&i.EventTitle,
			&i.EventStartTime,
			&i.EventEndTime,
			&i.EventDescription,
			&i.EventLocationType,
			&i.EventLocationFullAddress,
			&i.EventLocationOnlineUrl,
			&i.EventStatus,
			&i.EventOrganizationID,
			&i.OrganizationName,
			&i.EventPrice,
			&i.EventContactEmail,
			&i.EventContactPhone,
			&i.MediaItems,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listEventsByOrganization = `-- name: ListEventsByOrganization :many
SELECT id, organization_id, title, location_type, location_online_url, start_time, end_time, status, participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at, government_funding_keys, created_at, updated_at, location_full_address, description_content, price, contact_email, contact_phone, is_government_funded FROM events e
WHERE e.organization_id = $1
    AND ($4::text[] IS NULL OR e.status::text = ANY($4::text[]))
    AND (
        $5::TIMESTAMPTZ IS NULL OR -- No start date, so no date filter
        (
            -- Case 1: start_date is provided, end_date is provided
            ($6::TIMESTAMPTZ IS NOT NULL AND e.end_time >= $5::TIMESTAMPTZ AND e.end_time <= $6::TIMESTAMPTZ)
            OR
            -- Case 2: start_date is provided, end_date is NULL
            ($6::TIMESTAMPTZ IS NULL AND e.end_time >= $5::TIMESTAMPTZ)
        )
    )
    AND ($7::text IS NULL OR (
        e.title ILIKE '%' || $7::text || '%'
        OR e.location_full_address ILIKE '%' || $7::text || '%'
    ))
    AND ($8::text[] IS NULL OR e.government_funding_keys && $8::text[])
    AND (
        $9::text[] IS NULL
        OR array_length($9::text[], 1) IS NULL
        OR EXISTS (
            SELECT 1
            FROM event_required_verification_types ervt
            WHERE ervt.event_id = e.id
            AND ervt.verification_type_key = ANY($9::text[])
        )
    )
    AND (
        $10::uuid[] IS NULL
        OR array_length($10::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY($10::uuid[])
        )
    )
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3
`

type ListEventsByOrganizationParams struct {
	OrganizationID            uuid.UUID   `db:"organization_id" json:"organization_id"`
	Limit                     int32       `db:"limit" json:"limit"`
	Offset                    int32       `db:"offset" json:"offset"`
	Statuses                  []string    `db:"statuses" json:"statuses"`
	StartDate                 *time.Time  `db:"start_date" json:"start_date"`
	EndDate                   *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm                *string     `db:"search_term" json:"search_term"`
	GovernmentFundingKeys     []string    `db:"government_funding_keys" json:"government_funding_keys"`
	EventVerificationTypeKeys []string    `db:"event_verification_type_keys" json:"event_verification_type_keys"`
	TagIds                    []uuid.UUID `db:"tag_ids" json:"tag_ids"`
}

func (q *Queries) ListEventsByOrganization(ctx context.Context, arg ListEventsByOrganizationParams) ([]Event, error) {
	rows, err := q.db.Query(ctx, listEventsByOrganization,
		arg.OrganizationID,
		arg.Limit,
		arg.Offset,
		arg.Statuses,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.GovernmentFundingKeys,
		arg.EventVerificationTypeKeys,
		arg.TagIds,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Event{}
	for rows.Next() {
		var i Event
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.LocationType,
			&i.LocationOnlineUrl,
			&i.StartTime,
			&i.EndTime,
			&i.Status,
			&i.ParticipantLimit,
			&i.WaitlistLimit,
			&i.RequiresApprovalForRegistration,
			&i.CreatedByUserID,
			&i.PublishedAt,
			&i.GovernmentFundingKeys,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LocationFullAddress,
			&i.DescriptionContent,
			&i.Price,
			&i.ContactEmail,
			&i.ContactPhone,
			&i.IsGovernmentFunded,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listGloballyApprovedEventTags = `-- name: ListGloballyApprovedEventTags :many
SELECT id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_by_user_id, is_globally_approved, created_at, updated_at FROM event_tags
WHERE is_globally_approved = TRUE
ORDER BY name_en
`

func (q *Queries) ListGloballyApprovedEventTags(ctx context.Context) ([]EventTag, error) {
	rows, err := q.db.Query(ctx, listGloballyApprovedEventTags)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []EventTag{}
	for rows.Next() {
		var i EventTag
		if err := rows.Scan(
			&i.ID,
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.DescriptionEn,
			&i.DescriptionZhHk,
			&i.DescriptionZhCn,
			&i.CreatedByUserID,
			&i.IsGloballyApproved,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listMediaItemsByEventIDs = `-- name: ListMediaItemsByEventIDs :many
SELECT id, event_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner FROM event_media_items
WHERE event_id = ANY($1::UUID[])
ORDER BY event_id, is_banner DESC, uploaded_at ASC
`

func (q *Queries) ListMediaItemsByEventIDs(ctx context.Context, eventIds []uuid.UUID) ([]EventMediaItem, error) {
	rows, err := q.db.Query(ctx, listMediaItemsByEventIDs, eventIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []EventMediaItem{}
	for rows.Next() {
		var i EventMediaItem
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.UploadedAt,
			&i.IsBanner,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listMediaItemsForEvents = `-- name: ListMediaItemsForEvents :many
SELECT id, event_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner FROM event_media_items
WHERE event_id = ANY($1::uuid[])
`

func (q *Queries) ListMediaItemsForEvents(ctx context.Context, eventIds []uuid.UUID) ([]EventMediaItem, error) {
	rows, err := q.db.Query(ctx, listMediaItemsForEvents, eventIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []EventMediaItem{}
	for rows.Next() {
		var i EventMediaItem
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.UploadedAt,
			&i.IsBanner,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOrgEventVolunteerApplicationsWithFilters = `-- name: ListOrgEventVolunteerApplicationsWithFilters :many
SELECT
    eva.id, eva.event_id, eva.user_id, eva.organization_id, eva.application_notes_by_user, eva.admin_review_notes, eva.applied_at, eva.reviewed_at, eva.reviewed_by_user_id, eva.created_at, eva.updated_at, eva.status, eva.attended_at,
    u.display_name AS applicant_display_name,
    u.email AS applicant_email,
    u.phone AS applicant_phone_number,
    e.title AS event_title,
    o.name AS organization_name,
    reviewer.display_name as reviewer_display_name
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
JOIN events e ON eva.event_id = e.id
JOIN organizations o ON eva.organization_id = o.id
LEFT JOIN users reviewer ON eva.reviewed_by_user_id = reviewer.id
WHERE eva.organization_id = $1
AND eva.status = COALESCE($4::application_status_enum, eva.status)
ORDER BY e.start_time DESC, eva.applied_at DESC
LIMIT $2 OFFSET $3
`

type ListOrgEventVolunteerApplicationsWithFiltersParams struct {
	OrganizationID uuid.UUID                 `db:"organization_id" json:"organization_id"`
	Limit          int32                     `db:"limit" json:"limit"`
	Offset         int32                     `db:"offset" json:"offset"`
	Status         NullApplicationStatusEnum `db:"status" json:"status"`
}

type ListOrgEventVolunteerApplicationsWithFiltersRow struct {
	ID                     uuid.UUID             `db:"id" json:"id"`
	EventID                uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                 uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID         uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationNotesByUser *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
	AdminReviewNotes       *string               `db:"admin_review_notes" json:"admin_review_notes"`
	AppliedAt              time.Time             `db:"applied_at" json:"applied_at"`
	ReviewedAt             *time.Time            `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID       *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	CreatedAt              time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt              time.Time             `db:"updated_at" json:"updated_at"`
	Status                 ApplicationStatusEnum `db:"status" json:"status"`
	AttendedAt             *time.Time            `db:"attended_at" json:"attended_at"`
	ApplicantDisplayName   string                `db:"applicant_display_name" json:"applicant_display_name"`
	ApplicantEmail         *string               `db:"applicant_email" json:"applicant_email"`
	ApplicantPhoneNumber   *string               `db:"applicant_phone_number" json:"applicant_phone_number"`
	EventTitle             string                `db:"event_title" json:"event_title"`
	OrganizationName       string                `db:"organization_name" json:"organization_name"`
	ReviewerDisplayName    *string               `db:"reviewer_display_name" json:"reviewer_display_name"`
}

func (q *Queries) ListOrgEventVolunteerApplicationsWithFilters(ctx context.Context, arg ListOrgEventVolunteerApplicationsWithFiltersParams) ([]ListOrgEventVolunteerApplicationsWithFiltersRow, error) {
	rows, err := q.db.Query(ctx, listOrgEventVolunteerApplicationsWithFilters,
		arg.OrganizationID,
		arg.Limit,
		arg.Offset,
		arg.Status,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrgEventVolunteerApplicationsWithFiltersRow{}
	for rows.Next() {
		var i ListOrgEventVolunteerApplicationsWithFiltersRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationNotesByUser,
			&i.AdminReviewNotes,
			&i.AppliedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.AttendedAt,
			&i.ApplicantDisplayName,
			&i.ApplicantEmail,
			&i.ApplicantPhoneNumber,
			&i.EventTitle,
			&i.OrganizationName,
			&i.ReviewerDisplayName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPendingReviewEventVolunteerApplicationsForEvent = `-- name: ListPendingReviewEventVolunteerApplicationsForEvent :many
SELECT
    eva.id, eva.event_id, eva.user_id, eva.organization_id, eva.application_notes_by_user, eva.admin_review_notes, eva.applied_at, eva.reviewed_at, eva.reviewed_by_user_id, eva.created_at, eva.updated_at, eva.status, eva.attended_at,
    u.display_name AS applicant_display_name,
    u.email AS applicant_email,
    u.phone AS applicant_phone,
    e.title AS event_title,
    o.name AS organization_name
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
JOIN events e ON eva.event_id = e.id
JOIN organizations o ON eva.organization_id = o.id
WHERE eva.event_id = $1 AND eva.status = 'pending'
ORDER BY eva.applied_at ASC
LIMIT $2 OFFSET $3
`

type ListPendingReviewEventVolunteerApplicationsForEventParams struct {
	EventID uuid.UUID `db:"event_id" json:"event_id"`
	Limit   int32     `db:"limit" json:"limit"`
	Offset  int32     `db:"offset" json:"offset"`
}

type ListPendingReviewEventVolunteerApplicationsForEventRow struct {
	ID                     uuid.UUID             `db:"id" json:"id"`
	EventID                uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                 uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID         uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationNotesByUser *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
	AdminReviewNotes       *string               `db:"admin_review_notes" json:"admin_review_notes"`
	AppliedAt              time.Time             `db:"applied_at" json:"applied_at"`
	ReviewedAt             *time.Time            `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID       *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	CreatedAt              time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt              time.Time             `db:"updated_at" json:"updated_at"`
	Status                 ApplicationStatusEnum `db:"status" json:"status"`
	AttendedAt             *time.Time            `db:"attended_at" json:"attended_at"`
	ApplicantDisplayName   string                `db:"applicant_display_name" json:"applicant_display_name"`
	ApplicantEmail         *string               `db:"applicant_email" json:"applicant_email"`
	ApplicantPhone         *string               `db:"applicant_phone" json:"applicant_phone"`
	EventTitle             string                `db:"event_title" json:"event_title"`
	OrganizationName       string                `db:"organization_name" json:"organization_name"`
}

func (q *Queries) ListPendingReviewEventVolunteerApplicationsForEvent(ctx context.Context, arg ListPendingReviewEventVolunteerApplicationsForEventParams) ([]ListPendingReviewEventVolunteerApplicationsForEventRow, error) {
	rows, err := q.db.Query(ctx, listPendingReviewEventVolunteerApplicationsForEvent, arg.EventID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPendingReviewEventVolunteerApplicationsForEventRow{}
	for rows.Next() {
		var i ListPendingReviewEventVolunteerApplicationsForEventRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationNotesByUser,
			&i.AdminReviewNotes,
			&i.AppliedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.AttendedAt,
			&i.ApplicantDisplayName,
			&i.ApplicantEmail,
			&i.ApplicantPhone,
			&i.EventTitle,
			&i.OrganizationName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPendingReviewEventVolunteerApplicationsForOrganization = `-- name: ListPendingReviewEventVolunteerApplicationsForOrganization :many
SELECT
    eva.id, eva.event_id, eva.user_id, eva.organization_id, eva.application_notes_by_user, eva.admin_review_notes, eva.applied_at, eva.reviewed_at, eva.reviewed_by_user_id, eva.created_at, eva.updated_at, eva.status, eva.attended_at,
    u.display_name AS applicant_display_name,
    u.email AS applicant_email,
    u.phone AS applicant_phone,
    e.title AS event_title,
    o.name AS organization_name
FROM event_volunteer_applications eva
JOIN users u ON eva.user_id = u.id
JOIN events e ON eva.event_id = e.id
JOIN organizations o ON eva.organization_id = o.id
WHERE eva.organization_id = $1 AND eva.status = 'pending'
ORDER BY e.start_time ASC, eva.applied_at ASC
LIMIT $2 OFFSET $3
`

type ListPendingReviewEventVolunteerApplicationsForOrganizationParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Limit          int32     `db:"limit" json:"limit"`
	Offset         int32     `db:"offset" json:"offset"`
}

type ListPendingReviewEventVolunteerApplicationsForOrganizationRow struct {
	ID                     uuid.UUID             `db:"id" json:"id"`
	EventID                uuid.UUID             `db:"event_id" json:"event_id"`
	UserID                 uuid.UUID             `db:"user_id" json:"user_id"`
	OrganizationID         uuid.UUID             `db:"organization_id" json:"organization_id"`
	ApplicationNotesByUser *string               `db:"application_notes_by_user" json:"application_notes_by_user"`
	AdminReviewNotes       *string               `db:"admin_review_notes" json:"admin_review_notes"`
	AppliedAt              time.Time             `db:"applied_at" json:"applied_at"`
	ReviewedAt             *time.Time            `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID       *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	CreatedAt              time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt              time.Time             `db:"updated_at" json:"updated_at"`
	Status                 ApplicationStatusEnum `db:"status" json:"status"`
	AttendedAt             *time.Time            `db:"attended_at" json:"attended_at"`
	ApplicantDisplayName   string                `db:"applicant_display_name" json:"applicant_display_name"`
	ApplicantEmail         *string               `db:"applicant_email" json:"applicant_email"`
	ApplicantPhone         *string               `db:"applicant_phone" json:"applicant_phone"`
	EventTitle             string                `db:"event_title" json:"event_title"`
	OrganizationName       string                `db:"organization_name" json:"organization_name"`
}

func (q *Queries) ListPendingReviewEventVolunteerApplicationsForOrganization(ctx context.Context, arg ListPendingReviewEventVolunteerApplicationsForOrganizationParams) ([]ListPendingReviewEventVolunteerApplicationsForOrganizationRow, error) {
	rows, err := q.db.Query(ctx, listPendingReviewEventVolunteerApplicationsForOrganization, arg.OrganizationID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPendingReviewEventVolunteerApplicationsForOrganizationRow{}
	for rows.Next() {
		var i ListPendingReviewEventVolunteerApplicationsForOrganizationRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.OrganizationID,
			&i.ApplicationNotesByUser,
			&i.AdminReviewNotes,
			&i.AppliedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.AttendedAt,
			&i.ApplicantDisplayName,
			&i.ApplicantEmail,
			&i.ApplicantPhone,
			&i.EventTitle,
			&i.OrganizationName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPopularEvents = `-- name: ListPopularEvents :many
SELECT 
    e.id, 
    e.title, 
    e.start_time, 
    e.location_full_address,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status IN ('registered', 'pending_approval')) as registered_count
FROM events e
WHERE
    e.status = 'published' -- Use 'published' status based on enum
ORDER BY registered_count DESC, start_time ASC -- Sort by popularity, then time
LIMIT $1
`

type ListPopularEventsRow struct {
	ID                  uuid.UUID `db:"id" json:"id"`
	Title               string    `db:"title" json:"title"`
	StartTime           time.Time `db:"start_time" json:"start_time"`
	LocationFullAddress *string   `db:"location_full_address" json:"location_full_address"`
	RegisteredCount     int64     `db:"registered_count" json:"registered_count"`
}

func (q *Queries) ListPopularEvents(ctx context.Context, limit int32) ([]ListPopularEventsRow, error) {
	rows, err := q.db.Query(ctx, listPopularEvents, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPopularEventsRow{}
	for rows.Next() {
		var i ListPopularEventsRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.StartTime,
			&i.LocationFullAddress,
			&i.RegisteredCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPopularEventsByOrganization = `-- name: ListPopularEventsByOrganization :many
SELECT 
    e.id, 
    e.title, 
    e.start_time, 
    e.location_full_address,
    (SELECT COUNT(*) FROM event_registrations er WHERE er.event_id = e.id AND er.status IN ('registered', 'pending_approval')) as registered_count
FROM events e
WHERE
    e.organization_id = $1
    AND e.status = 'published' -- Use 'published' status based on enum
ORDER BY registered_count DESC, start_time ASC -- Sort by popularity, then time
LIMIT $2
`

type ListPopularEventsByOrganizationParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Limit          int32     `db:"limit" json:"limit"`
}

type ListPopularEventsByOrganizationRow struct {
	ID                  uuid.UUID `db:"id" json:"id"`
	Title               string    `db:"title" json:"title"`
	StartTime           time.Time `db:"start_time" json:"start_time"`
	LocationFullAddress *string   `db:"location_full_address" json:"location_full_address"`
	RegisteredCount     int64     `db:"registered_count" json:"registered_count"`
}

func (q *Queries) ListPopularEventsByOrganization(ctx context.Context, arg ListPopularEventsByOrganizationParams) ([]ListPopularEventsByOrganizationRow, error) {
	rows, err := q.db.Query(ctx, listPopularEventsByOrganization, arg.OrganizationID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPopularEventsByOrganizationRow{}
	for rows.Next() {
		var i ListPopularEventsByOrganizationRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.StartTime,
			&i.LocationFullAddress,
			&i.RegisteredCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublicEvents = `-- name: ListPublicEvents :many
SELECT id, organization_id, title, location_type, location_online_url, start_time, end_time, status, participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at, government_funding_keys, created_at, updated_at, location_full_address, description_content, price, contact_email, contact_phone, is_government_funded
FROM events e
WHERE
    e.status::text = ANY($3::text[]) -- Cast e.status to text
    AND e.end_time > COALESCE($4::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE($5::TIMESTAMPTZ, 'infinity')
    AND ($6::text IS NULL OR (
        e.title ILIKE '%' || $6::text || '%'
        OR e.location_full_address ILIKE '%' || $6::text || '%'
    ))
    AND (array_length($7::text[], 1) IS NULL OR e.government_funding_keys && $7::text[])
    AND (
        ($8::uuid IS NULL AND $9::uuid IS NULL) OR
        ($8::uuid IS NOT NULL AND e.organization_id = $8::uuid) OR
        ($9::uuid IS NOT NULL AND e.organization_id = $9::uuid)
    )
    AND ( -- Tag filter logic
        $10::uuid[] IS NULL
        OR array_length($10::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY($10::uuid[])
        )
    )
    AND ($11::text IS NULL OR EXISTS (
        SELECT 1
        FROM event_required_verification_types ervt
        WHERE ervt.event_id = e.id
        AND ervt.verification_type_key = $11::text
    ))
ORDER BY e.start_time ASC
LIMIT $1 OFFSET $2
`

type ListPublicEventsParams struct {
	Limit                    int32       `db:"limit" json:"limit"`
	Offset                   int32       `db:"offset" json:"offset"`
	Statuses                 []string    `db:"statuses" json:"statuses"`
	StartDate                *time.Time  `db:"start_date" json:"start_date"`
	EndDate                  *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm               *string     `db:"search_term" json:"search_term"`
	GovernmentFundingKeys    []string    `db:"government_funding_keys" json:"government_funding_keys"`
	OrganizationID           *uuid.UUID  `db:"organization_id" json:"organization_id"`
	OrganizationId2          *uuid.UUID  `db:"organization_id2" json:"organization_id2"`
	TagIds                   []uuid.UUID `db:"tag_ids" json:"tag_ids"`
	EventVerificationTypeKey *string     `db:"event_verification_type_key" json:"event_verification_type_key"`
}

func (q *Queries) ListPublicEvents(ctx context.Context, arg ListPublicEventsParams) ([]Event, error) {
	rows, err := q.db.Query(ctx, listPublicEvents,
		arg.Limit,
		arg.Offset,
		arg.Statuses,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.GovernmentFundingKeys,
		arg.OrganizationID,
		arg.OrganizationId2,
		arg.TagIds,
		arg.EventVerificationTypeKey,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Event{}
	for rows.Next() {
		var i Event
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.LocationType,
			&i.LocationOnlineUrl,
			&i.StartTime,
			&i.EndTime,
			&i.Status,
			&i.ParticipantLimit,
			&i.WaitlistLimit,
			&i.RequiresApprovalForRegistration,
			&i.CreatedByUserID,
			&i.PublishedAt,
			&i.GovernmentFundingKeys,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LocationFullAddress,
			&i.DescriptionContent,
			&i.Price,
			&i.ContactEmail,
			&i.ContactPhone,
			&i.IsGovernmentFunded,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublicEventsByOrganization = `-- name: ListPublicEventsByOrganization :many
SELECT e.id, e.organization_id, e.title, e.location_type, e.location_online_url, e.start_time, e.end_time, e.status, e.participant_limit, e.waitlist_limit, e.requires_approval_for_registration, e.created_by_user_id, e.published_at, e.government_funding_keys, e.created_at, e.updated_at, e.location_full_address, e.description_content, e.price, e.contact_email, e.contact_phone, e.is_government_funded -- Add other required fields if needed (org name, tags etc) via JOINs
FROM events e
WHERE
    e.organization_id = $1
    AND e.status::text = ANY($4::text[]) -- Cast e.status to text
    AND e.end_time > COALESCE($5::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE($6::TIMESTAMPTZ, 'infinity')
    AND ($7::text IS NULL OR (
        e.title ILIKE '%' || $7::text || '%'
        OR e.location_full_address ILIKE '%' || $7::text || '%'
    ))
    AND ( -- Alternative tag filter logic
        $8::uuid[] IS NULL
        OR array_length($8::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY($8::uuid[])
        )
    )
    AND (array_length($9::text[], 1) IS NULL OR e.government_funding_keys && $9::text[])
GROUP BY e.id
ORDER BY e.start_time ASC
LIMIT $2 OFFSET $3
`

type ListPublicEventsByOrganizationParams struct {
	OrganizationID        uuid.UUID   `db:"organization_id" json:"organization_id"`
	Limit                 int32       `db:"limit" json:"limit"`
	Offset                int32       `db:"offset" json:"offset"`
	Statuses              []string    `db:"statuses" json:"statuses"`
	StartDate             *time.Time  `db:"start_date" json:"start_date"`
	EndDate               *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm            *string     `db:"search_term" json:"search_term"`
	TagIds                []uuid.UUID `db:"tag_ids" json:"tag_ids"`
	GovernmentFundingKeys []string    `db:"government_funding_keys" json:"government_funding_keys"`
}

type ListPublicEventsByOrganizationRow struct {
	Event Event `db:"event" json:"event"`
}

// LEFT JOIN event_event_tags eet ON e.id = eet.event_id -- Add joins if tag filtering etc. is needed
// LEFT JOIN event_tags et ON eet.tag_id = et.id
func (q *Queries) ListPublicEventsByOrganization(ctx context.Context, arg ListPublicEventsByOrganizationParams) ([]ListPublicEventsByOrganizationRow, error) {
	rows, err := q.db.Query(ctx, listPublicEventsByOrganization,
		arg.OrganizationID,
		arg.Limit,
		arg.Offset,
		arg.Statuses,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.TagIds,
		arg.GovernmentFundingKeys,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPublicEventsByOrganizationRow{}
	for rows.Next() {
		var i ListPublicEventsByOrganizationRow
		if err := rows.Scan(
			&i.Event.ID,
			&i.Event.OrganizationID,
			&i.Event.Title,
			&i.Event.LocationType,
			&i.Event.LocationOnlineUrl,
			&i.Event.StartTime,
			&i.Event.EndTime,
			&i.Event.Status,
			&i.Event.ParticipantLimit,
			&i.Event.WaitlistLimit,
			&i.Event.RequiresApprovalForRegistration,
			&i.Event.CreatedByUserID,
			&i.Event.PublishedAt,
			&i.Event.GovernmentFundingKeys,
			&i.Event.CreatedAt,
			&i.Event.UpdatedAt,
			&i.Event.LocationFullAddress,
			&i.Event.DescriptionContent,
			&i.Event.Price,
			&i.Event.ContactEmail,
			&i.Event.ContactPhone,
			&i.Event.IsGovernmentFunded,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublicEventsWithCounts = `-- name: ListPublicEventsWithCounts :many
SELECT
    e.id,
    e.organization_id,
    e.title,
    e.description_content, -- Map to JsonContent in payload
    e.location_type,
    e.location_full_address,
    e.location_online_url,
    e.start_time,
    e.end_time,
    e.price,
    e.government_funding_keys,
    e.status,
    e.participant_limit,
    e.waitlist_limit, -- Already in events table
    e.requires_approval_for_registration,
    e.created_by_user_id,
    e.published_at,
    e.contact_email,
    e.contact_phone,
    e.is_government_funded, -- Keep if needed, or remove if not in PublicEventResponse
    o.name AS organization_name,
    COALESCE(counts.registered_count, 0) AS registered_count,
    COALESCE(counts.waitlisted_count, 0) AS waitlisted_count,
    COALESCE(counts.attended_count, 0) AS attended_count
FROM events e
JOIN organizations o ON e.organization_id = o.id
LEFT JOIN (
    SELECT
        event_id,
        COUNT(CASE WHEN status = 'registered' THEN 1 END) AS registered_count,
        COUNT(CASE WHEN status = 'waitlisted' THEN 1 END) AS waitlisted_count,
        COUNT(CASE WHEN status = 'attended' THEN 1 END) AS attended_count
    FROM event_registrations
    GROUP BY event_id
) counts ON e.id = counts.event_id
WHERE
    e.status::text = ANY($1::text[])
    AND e.end_time > COALESCE($2::TIMESTAMPTZ, '-infinity')
    AND e.start_time < COALESCE($3::TIMESTAMPTZ, 'infinity')
    AND ($4::text IS NULL OR (
        e.title ILIKE '%' || $4::text || '%'
        OR e.location_full_address ILIKE '%' || $4::text || '%'
    ))
    AND ( -- Alternative tag filter logic
        $5::uuid[] IS NULL
        OR array_length($5::uuid[], 1) = 0
        OR EXISTS (
            SELECT 1
            FROM event_event_tags eet
            WHERE eet.event_id = e.id
            AND eet.event_tag_id = ANY($5::uuid[])
        )
    )
    AND (array_length($6::text[], 1) IS NULL OR e.government_funding_keys && $6::text[])
    AND ($7::text IS NULL OR EXISTS (
        SELECT 1
        FROM event_required_verification_types ervt
        WHERE ervt.event_id = e.id
        AND ervt.verification_type_key = $7::text
    ))
    AND (
        ($8::uuid IS NULL AND $9::uuid IS NULL) OR
        ($8::uuid IS NOT NULL AND e.organization_id = $8::uuid) OR
        ($9::uuid IS NOT NULL AND e.organization_id = $9::uuid)
    )
ORDER BY e.start_time ASC -- Or based on parameters
LIMIT $11 OFFSET $10
`

type ListPublicEventsWithCountsParams struct {
	Statuses                 []string    `db:"statuses" json:"statuses"`
	StartDate                *time.Time  `db:"start_date" json:"start_date"`
	EndDate                  *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm               *string     `db:"search_term" json:"search_term"`
	TagIds                   []uuid.UUID `db:"tag_ids" json:"tag_ids"`
	GovernmentFundingKeys    []string    `db:"government_funding_keys" json:"government_funding_keys"`
	EventVerificationTypeKey *string     `db:"event_verification_type_key" json:"event_verification_type_key"`
	OrganizationID           *uuid.UUID  `db:"organization_id" json:"organization_id"`
	OrganizationId2          *uuid.UUID  `db:"organization_id2" json:"organization_id2"`
	Offset                   int32       `db:"offset" json:"offset"`
	Limit                    int32       `db:"limit" json:"limit"`
}

type ListPublicEventsWithCountsRow struct {
	ID                              uuid.UUID         `db:"id" json:"id"`
	OrganizationID                  uuid.UUID         `db:"organization_id" json:"organization_id"`
	Title                           string            `db:"title" json:"title"`
	DescriptionContent              []byte            `db:"description_content" json:"description_content"`
	LocationType                    EventLocationType `db:"location_type" json:"location_type"`
	LocationFullAddress             *string           `db:"location_full_address" json:"location_full_address"`
	LocationOnlineUrl               *string           `db:"location_online_url" json:"location_online_url"`
	StartTime                       time.Time         `db:"start_time" json:"start_time"`
	EndTime                         time.Time         `db:"end_time" json:"end_time"`
	Price                           *string           `db:"price" json:"price"`
	GovernmentFundingKeys           []string          `db:"government_funding_keys" json:"government_funding_keys"`
	Status                          EventStatusType   `db:"status" json:"status"`
	ParticipantLimit                *int32            `db:"participant_limit" json:"participant_limit"`
	WaitlistLimit                   *int32            `db:"waitlist_limit" json:"waitlist_limit"`
	RequiresApprovalForRegistration bool              `db:"requires_approval_for_registration" json:"requires_approval_for_registration"`
	CreatedByUserID                 uuid.UUID         `db:"created_by_user_id" json:"created_by_user_id"`
	PublishedAt                     *time.Time        `db:"published_at" json:"published_at"`
	ContactEmail                    *string           `db:"contact_email" json:"contact_email"`
	ContactPhone                    *string           `db:"contact_phone" json:"contact_phone"`
	IsGovernmentFunded              bool              `db:"is_government_funded" json:"is_government_funded"`
	OrganizationName                string            `db:"organization_name" json:"organization_name"`
	RegisteredCount                 int64             `db:"registered_count" json:"registered_count"`
	WaitlistedCount                 int64             `db:"waitlisted_count" json:"waitlisted_count"`
	AttendedCount                   int64             `db:"attended_count" json:"attended_count"`
}

func (q *Queries) ListPublicEventsWithCounts(ctx context.Context, arg ListPublicEventsWithCountsParams) ([]ListPublicEventsWithCountsRow, error) {
	rows, err := q.db.Query(ctx, listPublicEventsWithCounts,
		arg.Statuses,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.TagIds,
		arg.GovernmentFundingKeys,
		arg.EventVerificationTypeKey,
		arg.OrganizationID,
		arg.OrganizationId2,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPublicEventsWithCountsRow{}
	for rows.Next() {
		var i ListPublicEventsWithCountsRow
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.DescriptionContent,
			&i.LocationType,
			&i.LocationFullAddress,
			&i.LocationOnlineUrl,
			&i.StartTime,
			&i.EndTime,
			&i.Price,
			&i.GovernmentFundingKeys,
			&i.Status,
			&i.ParticipantLimit,
			&i.WaitlistLimit,
			&i.RequiresApprovalForRegistration,
			&i.CreatedByUserID,
			&i.PublishedAt,
			&i.ContactEmail,
			&i.ContactPhone,
			&i.IsGovernmentFunded,
			&i.OrganizationName,
			&i.RegisteredCount,
			&i.WaitlistedCount,
			&i.AttendedCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listRegistrationsForEvent = `-- name: ListRegistrationsForEvent :many
SELECT r.id, r.event_id, r.user_id, r.status, r.payment_status, r.registration_role, r.registered_at, r.attended_at, r.cancellation_reason_by_user, r.admin_notes_on_registration, r.waitlist_priority, r.created_at, r.updated_at, u.display_name as user_display_name, u.email as user_email, u.phone as user_phone
FROM event_registrations r
JOIN users u ON r.user_id = u.id
WHERE r.event_id = $1
ORDER BY r.registered_at ASC
LIMIT $2 OFFSET $3
`

type ListRegistrationsForEventParams struct {
	EventID uuid.UUID `db:"event_id" json:"event_id"`
	Limit   int32     `db:"limit" json:"limit"`
	Offset  int32     `db:"offset" json:"offset"`
}

type ListRegistrationsForEventRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
	UserDisplayName          string                      `db:"user_display_name" json:"user_display_name"`
	UserEmail                *string                     `db:"user_email" json:"user_email"`
	UserPhone                *string                     `db:"user_phone" json:"user_phone"`
}

func (q *Queries) ListRegistrationsForEvent(ctx context.Context, arg ListRegistrationsForEventParams) ([]ListRegistrationsForEventRow, error) {
	rows, err := q.db.Query(ctx, listRegistrationsForEvent, arg.EventID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListRegistrationsForEventRow{}
	for rows.Next() {
		var i ListRegistrationsForEventRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.Status,
			&i.PaymentStatus,
			&i.RegistrationRole,
			&i.RegisteredAt,
			&i.AttendedAt,
			&i.CancellationReasonByUser,
			&i.AdminNotesOnRegistration,
			&i.WaitlistPriority,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserDisplayName,
			&i.UserEmail,
			&i.UserPhone,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listRegistrationsForUser = `-- name: ListRegistrationsForUser :many
SELECT r.id, r.event_id, r.user_id, r.status, r.payment_status, r.registration_role, r.registered_at, r.attended_at, r.cancellation_reason_by_user, r.admin_notes_on_registration, r.waitlist_priority, r.created_at, r.updated_at, e.title as event_title, e.start_time as event_start_time, e.status as event_status
FROM event_registrations r
JOIN events e ON r.event_id = e.id
WHERE r.user_id = $1
ORDER BY e.start_time DESC
LIMIT $2 OFFSET $3
`

type ListRegistrationsForUserParams struct {
	UserID uuid.UUID `db:"user_id" json:"user_id"`
	Limit  int32     `db:"limit" json:"limit"`
	Offset int32     `db:"offset" json:"offset"`
}

type ListRegistrationsForUserRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
	EventTitle               string                      `db:"event_title" json:"event_title"`
	EventStartTime           time.Time                   `db:"event_start_time" json:"event_start_time"`
	EventStatus              EventStatusType             `db:"event_status" json:"event_status"`
}

func (q *Queries) ListRegistrationsForUser(ctx context.Context, arg ListRegistrationsForUserParams) ([]ListRegistrationsForUserRow, error) {
	rows, err := q.db.Query(ctx, listRegistrationsForUser, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListRegistrationsForUserRow{}
	for rows.Next() {
		var i ListRegistrationsForUserRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.UserID,
			&i.Status,
			&i.PaymentStatus,
			&i.RegistrationRole,
			&i.RegisteredAt,
			&i.AttendedAt,
			&i.CancellationReasonByUser,
			&i.AdminNotesOnRegistration,
			&i.WaitlistPriority,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.EventTitle,
			&i.EventStartTime,
			&i.EventStatus,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listRequiredVerificationTypesForEvent = `-- name: ListRequiredVerificationTypesForEvent :many
SELECT verification_type_key 
FROM event_required_verification_types
WHERE event_id = $1
`

func (q *Queries) ListRequiredVerificationTypesForEvent(ctx context.Context, eventID uuid.UUID) ([]string, error) {
	rows, err := q.db.Query(ctx, listRequiredVerificationTypesForEvent, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []string{}
	for rows.Next() {
		var verification_type_key string
		if err := rows.Scan(&verification_type_key); err != nil {
			return nil, err
		}
		items = append(items, verification_type_key)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listTagsByEventIDs = `-- name: ListTagsByEventIDs :many
SELECT et.id, et.name_en, et.name_zh_hk, et.name_zh_cn, et.description_en, et.description_zh_hk, et.description_zh_cn, et.is_globally_approved, eet.event_id
FROM event_tags et
JOIN event_event_tags eet ON et.id = eet.event_tag_id
WHERE eet.event_id = ANY($1::uuid[])
`

type ListTagsByEventIDsRow struct {
	ID                 uuid.UUID `db:"id" json:"id"`
	NameEn             string    `db:"name_en" json:"name_en"`
	NameZhHk           string    `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn           string    `db:"name_zh_cn" json:"name_zh_cn"`
	DescriptionEn      *string   `db:"description_en" json:"description_en"`
	DescriptionZhHk    *string   `db:"description_zh_hk" json:"description_zh_hk"`
	DescriptionZhCn    *string   `db:"description_zh_cn" json:"description_zh_cn"`
	IsGloballyApproved bool      `db:"is_globally_approved" json:"is_globally_approved"`
	EventID            uuid.UUID `db:"event_id" json:"event_id"`
}

func (q *Queries) ListTagsByEventIDs(ctx context.Context, eventIds []uuid.UUID) ([]ListTagsByEventIDsRow, error) {
	rows, err := q.db.Query(ctx, listTagsByEventIDs, eventIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListTagsByEventIDsRow{}
	for rows.Next() {
		var i ListTagsByEventIDsRow
		if err := rows.Scan(
			&i.ID,
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.DescriptionEn,
			&i.DescriptionZhHk,
			&i.DescriptionZhCn,
			&i.IsGloballyApproved,
			&i.EventID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listTagsForEvent = `-- name: ListTagsForEvent :many
SELECT et.id, et.name_en, et.name_zh_hk, et.name_zh_cn, et.description_en, et.description_zh_hk, et.description_zh_cn, et.created_by_user_id, et.is_globally_approved, et.created_at, et.updated_at
FROM event_tags et
JOIN event_event_tags eet ON et.id = eet.event_tag_id
WHERE eet.event_id = $1
ORDER BY et.name_en
`

func (q *Queries) ListTagsForEvent(ctx context.Context, eventID uuid.UUID) ([]EventTag, error) {
	rows, err := q.db.Query(ctx, listTagsForEvent, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []EventTag{}
	for rows.Next() {
		var i EventTag
		if err := rows.Scan(
			&i.ID,
			&i.NameEn,
			&i.NameZhHk,
			&i.NameZhCn,
			&i.DescriptionEn,
			&i.DescriptionZhHk,
			&i.DescriptionZhCn,
			&i.CreatedByUserID,
			&i.IsGloballyApproved,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUpcomingEvents = `-- name: ListUpcomingEvents :many
SELECT
    id, title, start_time
FROM events
WHERE
    status = 'published' 
    AND start_time > NOW()
ORDER BY start_time ASC
LIMIT $1
`

type ListUpcomingEventsRow struct {
	ID        uuid.UUID `db:"id" json:"id"`
	Title     string    `db:"title" json:"title"`
	StartTime time.Time `db:"start_time" json:"start_time"`
}

func (q *Queries) ListUpcomingEvents(ctx context.Context, limit int32) ([]ListUpcomingEventsRow, error) {
	rows, err := q.db.Query(ctx, listUpcomingEvents, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListUpcomingEventsRow{}
	for rows.Next() {
		var i ListUpcomingEventsRow
		if err := rows.Scan(&i.ID, &i.Title, &i.StartTime); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUpcomingEventsByOrganization = `-- name: ListUpcomingEventsByOrganization :many
SELECT
    id, title, start_time
FROM events
WHERE
    organization_id = $1
    AND status = 'published' 
    AND start_time > NOW()
ORDER BY start_time ASC
LIMIT $2
`

type ListUpcomingEventsByOrganizationParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Limit          int32     `db:"limit" json:"limit"`
}

type ListUpcomingEventsByOrganizationRow struct {
	ID        uuid.UUID `db:"id" json:"id"`
	Title     string    `db:"title" json:"title"`
	StartTime time.Time `db:"start_time" json:"start_time"`
}

func (q *Queries) ListUpcomingEventsByOrganization(ctx context.Context, arg ListUpcomingEventsByOrganizationParams) ([]ListUpcomingEventsByOrganizationRow, error) {
	rows, err := q.db.Query(ctx, listUpcomingEventsByOrganization, arg.OrganizationID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListUpcomingEventsByOrganizationRow{}
	for rows.Next() {
		var i ListUpcomingEventsByOrganizationRow
		if err := rows.Scan(&i.ID, &i.Title, &i.StartTime); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserRegistrationsForEvents = `-- name: ListUserRegistrationsForEvents :many
SELECT
    er.event_id,
    er.id AS registration_id,
    er.status AS registration_status,
    eva.id AS volunteer_application_id,
    eva.status AS volunteer_status
FROM events e
LEFT JOIN event_registrations er ON e.id = er.event_id AND er.user_id = $1
LEFT JOIN event_volunteer_applications eva ON e.id = eva.event_id AND eva.user_id = $1
WHERE e.id = ANY($2::uuid[])
`

type ListUserRegistrationsForEventsParams struct {
	UserID   uuid.UUID   `db:"user_id" json:"user_id"`
	EventIds []uuid.UUID `db:"event_ids" json:"event_ids"`
}

type ListUserRegistrationsForEventsRow struct {
	EventID                *uuid.UUID                      `db:"event_id" json:"event_id"`
	RegistrationID         *uuid.UUID                      `db:"registration_id" json:"registration_id"`
	RegistrationStatus     NullEventRegistrationStatusType `db:"registration_status" json:"registration_status"`
	VolunteerApplicationID *uuid.UUID                      `db:"volunteer_application_id" json:"volunteer_application_id"`
	VolunteerStatus        NullApplicationStatusEnum       `db:"volunteer_status" json:"volunteer_status"`
}

func (q *Queries) ListUserRegistrationsForEvents(ctx context.Context, arg ListUserRegistrationsForEventsParams) ([]ListUserRegistrationsForEventsRow, error) {
	rows, err := q.db.Query(ctx, listUserRegistrationsForEvents, arg.UserID, arg.EventIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListUserRegistrationsForEventsRow{}
	for rows.Next() {
		var i ListUserRegistrationsForEventsRow
		if err := rows.Scan(
			&i.EventID,
			&i.RegistrationID,
			&i.RegistrationStatus,
			&i.VolunteerApplicationID,
			&i.VolunteerStatus,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const reactivateEventVolunteerApplication = `-- name: ReactivateEventVolunteerApplication :one
UPDATE event_volunteer_applications
SET
    status = 'pending',
    application_notes_by_user = $3,
    admin_review_notes = NULL,
    reviewed_by_user_id = NULL,
    updated_at = NOW()
WHERE event_id = $1 AND user_id = $2
RETURNING id, event_id, user_id, organization_id, application_notes_by_user, admin_review_notes, applied_at, reviewed_at, reviewed_by_user_id, created_at, updated_at, status, attended_at
`

type ReactivateEventVolunteerApplicationParams struct {
	EventID                uuid.UUID `db:"event_id" json:"event_id"`
	UserID                 uuid.UUID `db:"user_id" json:"user_id"`
	ApplicationNotesByUser *string   `db:"application_notes_by_user" json:"application_notes_by_user"`
}

func (q *Queries) ReactivateEventVolunteerApplication(ctx context.Context, arg ReactivateEventVolunteerApplicationParams) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, reactivateEventVolunteerApplication, arg.EventID, arg.UserID, arg.ApplicationNotesByUser)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}

const removeAllRequiredVerificationsFromEvent = `-- name: RemoveAllRequiredVerificationsFromEvent :exec
DELETE FROM event_required_verification_types WHERE event_id = $1
`

func (q *Queries) RemoveAllRequiredVerificationsFromEvent(ctx context.Context, eventID uuid.UUID) error {
	_, err := q.db.Exec(ctx, removeAllRequiredVerificationsFromEvent, eventID)
	return err
}

const removeAllTagsFromEvent = `-- name: RemoveAllTagsFromEvent :exec
DELETE FROM event_event_tags WHERE event_id = $1
`

func (q *Queries) RemoveAllTagsFromEvent(ctx context.Context, eventID uuid.UUID) error {
	_, err := q.db.Exec(ctx, removeAllTagsFromEvent, eventID)
	return err
}

const removeEventRequiredVerificationType = `-- name: RemoveEventRequiredVerificationType :exec
DELETE FROM event_required_verification_types
WHERE event_id = $1 AND verification_type_key = $2
`

type RemoveEventRequiredVerificationTypeParams struct {
	EventID             uuid.UUID `db:"event_id" json:"event_id"`
	VerificationTypeKey string    `db:"verification_type_key" json:"verification_type_key"`
}

func (q *Queries) RemoveEventRequiredVerificationType(ctx context.Context, arg RemoveEventRequiredVerificationTypeParams) error {
	_, err := q.db.Exec(ctx, removeEventRequiredVerificationType, arg.EventID, arg.VerificationTypeKey)
	return err
}

const removeTagFromEvent = `-- name: RemoveTagFromEvent :exec
DELETE FROM event_event_tags WHERE event_id = $1 AND event_tag_id = $2
`

type RemoveTagFromEventParams struct {
	EventID    uuid.UUID `db:"event_id" json:"event_id"`
	EventTagID uuid.UUID `db:"event_tag_id" json:"event_tag_id"`
}

func (q *Queries) RemoveTagFromEvent(ctx context.Context, arg RemoveTagFromEventParams) error {
	_, err := q.db.Exec(ctx, removeTagFromEvent, arg.EventID, arg.EventTagID)
	return err
}

const setBannerForEventMediaItem = `-- name: SetBannerForEventMediaItem :one
UPDATE event_media_items
SET is_banner = TRUE
WHERE id = $1 AND event_id = $2
RETURNING id, event_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner
`

type SetBannerForEventMediaItemParams struct {
	ID      uuid.UUID `db:"id" json:"id"`
	EventID uuid.UUID `db:"event_id" json:"event_id"`
}

func (q *Queries) SetBannerForEventMediaItem(ctx context.Context, arg SetBannerForEventMediaItemParams) (EventMediaItem, error) {
	row := q.db.QueryRow(ctx, setBannerForEventMediaItem, arg.ID, arg.EventID)
	var i EventMediaItem
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.UploadedAt,
		&i.IsBanner,
	)
	return i, err
}

const setEventStatusToPublished = `-- name: SetEventStatusToPublished :exec
UPDATE events SET status = 'published', updated_at = NOW() WHERE id = $1
`

func (q *Queries) SetEventStatusToPublished(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, setEventStatusToPublished, id)
	return err
}

const setEventVolunteerApplicationStatusToWithdrawnByUser = `-- name: SetEventVolunteerApplicationStatusToWithdrawnByUser :one
UPDATE event_volunteer_applications
SET
    status = 'withdrawn',
    updated_at = NOW(),
    -- Clear review fields if necessary
    reviewed_by_user_id = NULL,
    reviewed_at = NULL,
    admin_review_notes = NULL
WHERE id = $1 AND user_id = $2 AND status IN ('pending', 'approved') -- Define withdrawable states
RETURNING id, event_id, user_id, organization_id, application_notes_by_user, admin_review_notes, applied_at, reviewed_at, reviewed_by_user_id, created_at, updated_at, status, attended_at
`

type SetEventVolunteerApplicationStatusToWithdrawnByUserParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	UserID uuid.UUID `db:"user_id" json:"user_id"`
}

func (q *Queries) SetEventVolunteerApplicationStatusToWithdrawnByUser(ctx context.Context, arg SetEventVolunteerApplicationStatusToWithdrawnByUserParams) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, setEventVolunteerApplicationStatusToWithdrawnByUser, arg.ID, arg.UserID)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}

const unsetBannerForEventMediaItems = `-- name: UnsetBannerForEventMediaItems :exec
UPDATE event_media_items
SET is_banner = FALSE
WHERE event_id = $1 AND is_banner = TRUE
`

func (q *Queries) UnsetBannerForEventMediaItems(ctx context.Context, eventID uuid.UUID) error {
	_, err := q.db.Exec(ctx, unsetBannerForEventMediaItems, eventID)
	return err
}

const updateEventDetails = `-- name: UpdateEventDetails :one
UPDATE events
SET 
    title = COALESCE($1, title),
    description_content = COALESCE($2, description_content),
    location_type = COALESCE($3, location_type),
    location_full_address = COALESCE($4, location_full_address),
    location_online_url = COALESCE($5, location_online_url),
    start_time = COALESCE($6, start_time),
    end_time = COALESCE($7, end_time),
    price = COALESCE($8, price),
    contact_email = COALESCE($9, contact_email),
    contact_phone = COALESCE($10, contact_phone),
    participant_limit = COALESCE($11, participant_limit),
    waitlist_limit = COALESCE($12, waitlist_limit),
    requires_approval_for_registration = COALESCE($13, requires_approval_for_registration),
    status = COALESCE($14, status),
    published_at = COALESCE($15, published_at),
    government_funding_keys = COALESCE($16, government_funding_keys),
    updated_at = NOW()
WHERE id = $17
RETURNING id, organization_id, title, location_type, location_online_url, start_time, end_time, status, participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at, government_funding_keys, created_at, updated_at, location_full_address, description_content, price, contact_email, contact_phone, is_government_funded
`

type UpdateEventDetailsParams struct {
	Title                           *string               `db:"title" json:"title"`
	DescriptionContent              []byte                `db:"description_content" json:"description_content"`
	LocationType                    NullEventLocationType `db:"location_type" json:"location_type"`
	LocationFullAddress             *string               `db:"location_full_address" json:"location_full_address"`
	LocationOnlineUrl               *string               `db:"location_online_url" json:"location_online_url"`
	StartTime                       *time.Time            `db:"start_time" json:"start_time"`
	EndTime                         *time.Time            `db:"end_time" json:"end_time"`
	Price                           *string               `db:"price" json:"price"`
	ContactEmail                    *string               `db:"contact_email" json:"contact_email"`
	ContactPhone                    *string               `db:"contact_phone" json:"contact_phone"`
	ParticipantLimit                *int32                `db:"participant_limit" json:"participant_limit"`
	WaitlistLimit                   *int32                `db:"waitlist_limit" json:"waitlist_limit"`
	RequiresApprovalForRegistration *bool                 `db:"requires_approval_for_registration" json:"requires_approval_for_registration"`
	Status                          NullEventStatusType   `db:"status" json:"status"`
	PublishedAt                     *time.Time            `db:"published_at" json:"published_at"`
	GovernmentFundingKeys           []string              `db:"government_funding_keys" json:"government_funding_keys"`
	ID                              uuid.UUID             `db:"id" json:"id"`
}

func (q *Queries) UpdateEventDetails(ctx context.Context, arg UpdateEventDetailsParams) (Event, error) {
	row := q.db.QueryRow(ctx, updateEventDetails,
		arg.Title,
		arg.DescriptionContent,
		arg.LocationType,
		arg.LocationFullAddress,
		arg.LocationOnlineUrl,
		arg.StartTime,
		arg.EndTime,
		arg.Price,
		arg.ContactEmail,
		arg.ContactPhone,
		arg.ParticipantLimit,
		arg.WaitlistLimit,
		arg.RequiresApprovalForRegistration,
		arg.Status,
		arg.PublishedAt,
		arg.GovernmentFundingKeys,
		arg.ID,
	)
	var i Event
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.LocationType,
		&i.LocationOnlineUrl,
		&i.StartTime,
		&i.EndTime,
		&i.Status,
		&i.ParticipantLimit,
		&i.WaitlistLimit,
		&i.RequiresApprovalForRegistration,
		&i.CreatedByUserID,
		&i.PublishedAt,
		&i.GovernmentFundingKeys,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LocationFullAddress,
		&i.DescriptionContent,
		&i.Price,
		&i.ContactEmail,
		&i.ContactPhone,
		&i.IsGovernmentFunded,
	)
	return i, err
}

const updateEventRegistrationPaymentStatus = `-- name: UpdateEventRegistrationPaymentStatus :one
UPDATE event_registrations
SET payment_status = $2
WHERE id = $1
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at
`

type UpdateEventRegistrationPaymentStatusParams struct {
	ID            uuid.UUID         `db:"id" json:"id"`
	PaymentStatus PaymentStatusType `db:"payment_status" json:"payment_status"`
}

type UpdateEventRegistrationPaymentStatusRow struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	EventID                  uuid.UUID                   `db:"event_id" json:"event_id"`
	UserID                   uuid.UUID                   `db:"user_id" json:"user_id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	PaymentStatus            PaymentStatusType           `db:"payment_status" json:"payment_status"`
	RegistrationRole         EventRegistrationRoleType   `db:"registration_role" json:"registration_role"`
	RegisteredAt             time.Time                   `db:"registered_at" json:"registered_at"`
	AttendedAt               *time.Time                  `db:"attended_at" json:"attended_at"`
	CancellationReasonByUser *string                     `db:"cancellation_reason_by_user" json:"cancellation_reason_by_user"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
	CreatedAt                time.Time                   `db:"created_at" json:"created_at"`
	UpdatedAt                time.Time                   `db:"updated_at" json:"updated_at"`
}

func (q *Queries) UpdateEventRegistrationPaymentStatus(ctx context.Context, arg UpdateEventRegistrationPaymentStatusParams) (UpdateEventRegistrationPaymentStatusRow, error) {
	row := q.db.QueryRow(ctx, updateEventRegistrationPaymentStatus, arg.ID, arg.PaymentStatus)
	var i UpdateEventRegistrationPaymentStatusRow
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateEventRegistrationStatus = `-- name: UpdateEventRegistrationStatus :one
UPDATE event_registrations
SET status = $2,
    admin_notes_on_registration = COALESCE($3, admin_notes_on_registration),
    waitlist_priority = CASE 
        WHEN $2 = 'waitlisted' THEN $4 -- Use provided priority for waitlisted
        ELSE waitlist_priority -- Keep existing priority otherwise
    END,
    updated_at = NOW()
WHERE id = $1
RETURNING id, event_id, user_id, status, payment_status, registration_role, registered_at, attended_at, cancellation_reason_by_user, admin_notes_on_registration, waitlist_priority, created_at, updated_at, check_in_by_user_id, check_in_method
`

type UpdateEventRegistrationStatusParams struct {
	ID                       uuid.UUID                   `db:"id" json:"id"`
	Status                   EventRegistrationStatusType `db:"status" json:"status"`
	AdminNotesOnRegistration *string                     `db:"admin_notes_on_registration" json:"admin_notes_on_registration"`
	WaitlistPriority         *time.Time                  `db:"waitlist_priority" json:"waitlist_priority"`
}

func (q *Queries) UpdateEventRegistrationStatus(ctx context.Context, arg UpdateEventRegistrationStatusParams) (EventRegistration, error) {
	row := q.db.QueryRow(ctx, updateEventRegistrationStatus,
		arg.ID,
		arg.Status,
		arg.AdminNotesOnRegistration,
		arg.WaitlistPriority,
	)
	var i EventRegistration
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.Status,
		&i.PaymentStatus,
		&i.RegistrationRole,
		&i.RegisteredAt,
		&i.AttendedAt,
		&i.CancellationReasonByUser,
		&i.AdminNotesOnRegistration,
		&i.WaitlistPriority,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CheckInByUserID,
		&i.CheckInMethod,
	)
	return i, err
}

const updateEventStatus = `-- name: UpdateEventStatus :one
UPDATE events
SET status = $2,
    published_at = CASE 
                       WHEN $2 = 'published'::event_status_type AND published_at IS NULL THEN CURRENT_TIMESTAMP 
                       ELSE published_at 
                   END
WHERE id = $1
RETURNING id, organization_id, title, location_type, location_online_url, start_time, end_time, status, participant_limit, waitlist_limit, requires_approval_for_registration, created_by_user_id, published_at, government_funding_keys, created_at, updated_at, location_full_address, description_content, price, contact_email, contact_phone, is_government_funded
`

type UpdateEventStatusParams struct {
	ID     uuid.UUID       `db:"id" json:"id"`
	Status EventStatusType `db:"status" json:"status"`
}

func (q *Queries) UpdateEventStatus(ctx context.Context, arg UpdateEventStatusParams) (Event, error) {
	row := q.db.QueryRow(ctx, updateEventStatus, arg.ID, arg.Status)
	var i Event
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.LocationType,
		&i.LocationOnlineUrl,
		&i.StartTime,
		&i.EndTime,
		&i.Status,
		&i.ParticipantLimit,
		&i.WaitlistLimit,
		&i.RequiresApprovalForRegistration,
		&i.CreatedByUserID,
		&i.PublishedAt,
		&i.GovernmentFundingKeys,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LocationFullAddress,
		&i.DescriptionContent,
		&i.Price,
		&i.ContactEmail,
		&i.ContactPhone,
		&i.IsGovernmentFunded,
	)
	return i, err
}

const updateEventTag = `-- name: UpdateEventTag :one
UPDATE event_tags
SET
    name_en = COALESCE($1, name_en),
    name_zh_hk = COALESCE($2, name_zh_hk),
    name_zh_cn = COALESCE($3, name_zh_cn),
    is_globally_approved = COALESCE($4, is_globally_approved)
WHERE id = $5
RETURNING id, name_en, name_zh_hk, name_zh_cn, description_en, description_zh_hk, description_zh_cn, created_by_user_id, is_globally_approved, created_at, updated_at
`

type UpdateEventTagParams struct {
	NameEn             *string   `db:"name_en" json:"name_en"`
	NameZhHk           *string   `db:"name_zh_hk" json:"name_zh_hk"`
	NameZhCn           *string   `db:"name_zh_cn" json:"name_zh_cn"`
	IsGloballyApproved *bool     `db:"is_globally_approved" json:"is_globally_approved"`
	ID                 uuid.UUID `db:"id" json:"id"`
}

func (q *Queries) UpdateEventTag(ctx context.Context, arg UpdateEventTagParams) (EventTag, error) {
	row := q.db.QueryRow(ctx, updateEventTag,
		arg.NameEn,
		arg.NameZhHk,
		arg.NameZhCn,
		arg.IsGloballyApproved,
		arg.ID,
	)
	var i EventTag
	err := row.Scan(
		&i.ID,
		&i.NameEn,
		&i.NameZhHk,
		&i.NameZhCn,
		&i.DescriptionEn,
		&i.DescriptionZhHk,
		&i.DescriptionZhCn,
		&i.CreatedByUserID,
		&i.IsGloballyApproved,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateEventVolunteerApplicationStatus = `-- name: UpdateEventVolunteerApplicationStatus :one
UPDATE event_volunteer_applications
SET status = $2,
    admin_review_notes = $3,
    reviewed_at = CURRENT_TIMESTAMP,
    reviewed_by_user_id = $4
WHERE id = $1
RETURNING id, event_id, user_id, organization_id, application_notes_by_user, admin_review_notes, applied_at, reviewed_at, reviewed_by_user_id, created_at, updated_at, status, attended_at
`

type UpdateEventVolunteerApplicationStatusParams struct {
	ID               uuid.UUID             `db:"id" json:"id"`
	Status           ApplicationStatusEnum `db:"status" json:"status"`
	AdminReviewNotes *string               `db:"admin_review_notes" json:"admin_review_notes"`
	ReviewedByUserID *uuid.UUID            `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
}

func (q *Queries) UpdateEventVolunteerApplicationStatus(ctx context.Context, arg UpdateEventVolunteerApplicationStatusParams) (EventVolunteerApplication, error) {
	row := q.db.QueryRow(ctx, updateEventVolunteerApplicationStatus,
		arg.ID,
		arg.Status,
		arg.AdminReviewNotes,
		arg.ReviewedByUserID,
	)
	var i EventVolunteerApplication
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.UserID,
		&i.OrganizationID,
		&i.ApplicationNotesByUser,
		&i.AdminReviewNotes,
		&i.AppliedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.AttendedAt,
	)
	return i, err
}
