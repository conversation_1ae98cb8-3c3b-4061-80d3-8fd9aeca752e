import React, { useState, useEffect } from 'react';
import {
    Form,
    Input,
    Button,
    Upload,
    Typography,
    Divider,
    message,
    Space,
    Select,
    Row,
    Col,
    Spin,
    Alert
} from 'antd';
import { UploadOutlined, PlusOutlined, UserAddOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';
import { useOrganization } from '../../contexts/OrganizationContext';
import { organizationService } from '../../services/organizationService';
import { adminService } from '../../services/adminService';
import { THEME_COLORS } from '../../config/themeConfig';
import CreateAdminModal from '../../components/OrganizationSettings/CreateAdminModal';
import ColorPaletteSelector from '../../components/OrganizationSettings/ColorPaletteSelector';
import { createOrgLogoUploadProps } from '../../utils/orgSettingsUploadUtils';

const { Title, Text } = Typography;
const { Option } = Select;

const CreateOrganizationPage = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { currentUser, isLoading: isUserLoading } = useUser();
    const { fetchOrganizations } = useOrganization();

    const [createForm] = Form.useForm();

    const [isCreateAdminModalVisible, setIsCreateAdminModalVisible] = useState(false);
    const [createAdminModalLoading, setCreateAdminModalLoading] = useState(false);

    const [createLoading, setCreateLoading] = useState(false);

    const [newOrgLogoFileList, setNewOrgLogoFileList] = useState([]);
    const [adminUsers, setAdminUsers] = useState([]);
    const [selectedOwnerId, setSelectedOwnerId] = useState(null);

    const isSuperAdmin = currentUser?.role === 'super_admin';

    useEffect(() => {
        if (isSuperAdmin) {
            const fetchAdminUsers = async () => {
                try {
                    // Using new API structure - backend now filters users with role 'admin'
                    const response = await adminService.listUsers({
                        page: 1,
                        limit: 100
                    });
                    if (response && response.users) {
                        // Filter admin users on frontend until backend supports role filtering
                        const adminUsers = response.users.filter(user => user.role === 'admin');
                        setAdminUsers(adminUsers || []);
                    } else if (response && Array.isArray(response)) {
                        const adminUsers = response.filter(user => user.role === 'admin');
                        setAdminUsers(adminUsers);
                    }
                } catch (error) {
                    console.error(t('organizationSettings.errors.fetchAdminUsersFailed'), error);
                    message.error(t('organizationSettings.errors.fetchAdminUsersFailed'));
                }
            };
            fetchAdminUsers();
        }
    }, [isSuperAdmin, t]);

    const handleLogoUpload = async (file, orgId) => {
        if (!orgId) {
            message.error(t('organizationSettings.errors.orgIdMissingForLogoUpload'));
            return null;
        }
        try {
            const response = await organizationService.uploadOrganizationLogo(orgId, file);
            if (response?.image_url) {
                message.success(t('organizationSettings.messages.logoUploadedSuccessfully'));
                return response.image_url;
            } else {
                message.error(t('organizationSettings.errors.logoUploadFailed'));
                return null;
            }
        } catch (error) {
            console.error(t('organizationSettings.errors.logoUploadFailed'), error);
            message.error(t('organizationSettings.errors.logoUploadFailed') + `: ${error.message}`);
            return null;
        }
    };

    const onCreateOrganization = async (values) => {
        setCreateLoading(true);
        const { name, description, theme_color, owner_id } = values;

        if (!owner_id && isSuperAdmin) {
            message.error(t('organizationSettings.errors.ownerRequired'));
            setCreateLoading(false);
            return;
        }

        if (newOrgLogoFileList.length === 0 || !newOrgLogoFileList[0]?.originFileObj) {
            message.error(t('organizationSettings.errors.logoRequiredForCreate'));
            setCreateLoading(false);
            return;
        }

        const createPayload = {
            name,
            description,
            theme_color,
            is_default: false,
            owner_user_id: owner_id,
        };

        try {
            const response = await organizationService.createOrganization(createPayload);
            const newOrgId = response?.data?.id || response?.id;

            if (!newOrgId) {
                message.error(t('organizationSettings.errors.orgCreateFailedNoId'));
                setCreateLoading(false);
                return;
            }

            const fileToUpload = newOrgLogoFileList[0].originFileObj;
            const uploadedImageUrl = await handleLogoUpload(fileToUpload, newOrgId);

            if (uploadedImageUrl) {
                await organizationService.updateOrganization(newOrgId, {
                    image_url: uploadedImageUrl,
                    status: 'active'
                });
                message.success(t('organizationSettings.messages.orgCreatedSuccessfully'));
                createForm.resetFields();
                setNewOrgLogoFileList([]);
                setSelectedOwnerId(null);
                if (fetchOrganizations) {
                    await fetchOrganizations();
                }
                // Navigate back to organization list
                navigate('/organization-settings');
            } else {
                message.error(t('organizationSettings.errors.logoUploadFailedOrgCreated', { orgName: name, defaultValue: `Organization '${name}' created, but logo upload failed. Please update the logo manually.` }));
                if (fetchOrganizations) {
                    await fetchOrganizations();
                }
                navigate('/organization-settings');
            }
        } catch (error) {
            console.error(t('organizationSettings.errors.orgCreateFailed'), error);
            message.error(t('organizationSettings.errors.orgCreateFailed') + `: ${error.message || ''}`);
        } finally {
            setCreateLoading(false);
        }
    };

    const showCreateAdminModal = () => setIsCreateAdminModalVisible(true);
    const handleCreateAdminModalCancel = () => setIsCreateAdminModalVisible(false);

    const handleCreateAdminSuccess = (successMessage) => {
        message.success(successMessage);
        setIsCreateAdminModalVisible(false);
        setCreateAdminModalLoading(false);
    };

    const handleCreateAdminError = (errorMessage) => {
        message.error(errorMessage);
        setCreateAdminModalLoading(false);
    };

    const refreshAdminUsers = async () => {
        try {
            // Using new API structure - backend now filters users with role 'admin'
            const response = await adminService.listUsers({
                page: 1,
                limit: 100
            });
            if (response && response.users) {
                // Filter admin users on frontend until backend supports role filtering
                const adminUsers = response.users.filter(user => user.role === 'admin');
                setAdminUsers(adminUsers || []);
            } else if (response && Array.isArray(response)) {
                const adminUsers = response.filter(user => user.role === 'admin');
                setAdminUsers(adminUsers);
            }
        } catch (error) {
            console.error(t('organizationSettings.errors.fetchAdminUsersFailed'), error);
        }
    };

    // 使用共用的文件上传配置
    const uploadProps = createOrgLogoUploadProps(newOrgLogoFileList, setNewOrgLogoFileList, t);

    if (isUserLoading) {
        return <Spin size="large" style={{ display: 'block', marginTop: '50px' }} />;
    }

    if (!currentUser || !isSuperAdmin) {
        return <Alert message={t('unauthorized.title')} description={t('unauthorized.defaultMessage')} type="error" showIcon />;
    }

    return (
        <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
            {/* Create Organization Title */}
            <Divider orientation="left" style={{ borderColor: '#bdbdbd', marginTop: '30px' }}>
                {t('organizationSettings.createOrg.title')}
            </Divider>

            <div style={{ padding: '0 16px', marginBottom: '40px' }}>
                <Form form={createForm} layout="vertical" onFinish={onCreateOrganization}>
                    <Row gutter={[24, 16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="name"
                                label={t('organizationSettings.common.name')}
                                rules={[{ required: true, message: t('organizationSettings.errors.nameRequired') }]}
                            >
                                <Input autoComplete="organization" />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="owner_id"
                                label={t('organizationSettings.createOrg.ownerLabel')}
                                rules={[{ required: true, message: t('organizationSettings.errors.ownerRequired') }]}
                            >
                                <Select
                                    placeholder={t('organizationSettings.createOrg.selectOwnerPlaceholder')}
                                    onChange={(value) => setSelectedOwnerId(value)}
                                    value={selectedOwnerId}
                                    showSearch
                                    optionFilterProp="children"
                                    filterOption={(input, option) =>
                                      (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                                    }
                                >
                                    {adminUsers.map(user => (
                                        <Option key={user.id} value={user.id}>
                                            {user.email} 
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                    
                    <Row gutter={[24, 16]}> 
                        <Col xs={24} sm={12}>
                            <Form.Item name="description" label={t('organizationSettings.common.description')}>
                                <Input.TextArea rows={3} autoComplete="off" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[24, 16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item name="logo" label={t('organizationSettings.common.logo')}>
                                <Upload {...uploadProps}>
                                    <Button icon={<UploadOutlined />}>{t('organizationSettings.common.selectLogo')}</Button>
                                </Upload>
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="theme_color"
                                label={t('organizationSettings.common.themeColor')}
                                initialValue={THEME_COLORS[1].name} 
                            >
                                <ColorPaletteSelector />
                            </Form.Item>
                        </Col>
                    </Row>
                    
                    <Divider />
                    <Row justify="space-between" align="middle" style={{ marginTop: '20px' }}>
                        <Col>
                            <Button 
                                size="large"
                                color="primary" 
                                variant="dashed"
                                icon={<UserAddOutlined />} 
                                onClick={showCreateAdminModal}
                            >
                                {t('organizationSettings.createAdmin.button')}
                            </Button>
                        </Col>
                        <Col>
                            <Space>
                                <Button size="large" onClick={() => navigate('/organization-settings')}>
                                    {t('common.cancel')}
                                </Button>
                                <Button
                                    size="large"
                                    type="primary"
                                    htmlType="submit"
                                    loading={createLoading}
                                    icon={<PlusOutlined />}
                                    style={{ minWidth: '120px'}}
                                >
                                    {t('organizationSettings.createOrg.submit')}
                                </Button>
                            </Space>
                        </Col>
                    </Row>
                </Form>
            </div>

            <CreateAdminModal
                visible={isCreateAdminModalVisible}
                loading={createAdminModalLoading}
                onCancel={handleCreateAdminModalCancel}
                onSuccess={handleCreateAdminSuccess}
                onError={handleCreateAdminError}
                onLoadingChange={setCreateAdminModalLoading}
                isSuperAdmin={isSuperAdmin}
                onRefreshAdminUsers={refreshAdminUsers}
            />
        </div>
    );
};

export default CreateOrganizationPage; 