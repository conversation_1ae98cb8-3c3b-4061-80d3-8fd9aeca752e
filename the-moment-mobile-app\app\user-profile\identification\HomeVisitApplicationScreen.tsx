import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Platform,
  StatusBar,
  TouchableOpacity
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { Stack, useRouter } from 'expo-router';
import { appStyleStore } from 'stores/app_style_store';
import { userProfileStore, userVerificationsStore } from 'stores/user_store';
import { useFetchUserProfile, useFetchUserVerifications, useSubmitVerification } from '@/api/user_services';
import { Button, Divider, Checkbox } from 'react-native-paper';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { VerificationTypeEnum } from 'types/enums';
import { createTheme } from 'theme/index';

const STATUSBAR_HEIGHT = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

export function HomeVisitApplicationScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const router = useRouter();
  
  const { data: userProfile } = useFetchUserProfile();
  const { refetch: refetchVerifications } = useFetchUserVerifications();
  const submitVerificationMutation = useSubmitVerification();

  const [agreed, setAgreed] = useState(false);
  const [showError, setShowError] = useState(false);
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!agreed) {
      setShowError(true);
      return;
    }

    setIsSubmitting(true);
    setShowError(false);
    setErrorMessage('');

    try {
      const formData = new FormData();
      formData.append('verification_type', VerificationTypeEnum.HomeVisit);

      await submitVerificationMutation.mutateAsync(formData);

      await refetchVerifications();
      setSuccessDialogVisible(true);

    } catch (error: any) {
      console.error('Home visit application submission error:', error);
      let specificMessage = t('programApplications.messages.submitError');
      if (error.response && error.response.data && error.response.data.error) {
        const errorCode = error.response.data.error;
        const translationKey = `identity.errors.api.${errorCode.toLowerCase()}`;
        specificMessage = t(translationKey, { defaultValue: specificMessage });
      }
      setErrorMessage(specificMessage);
      setErrorDialogVisible(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSuccessConfirm = () => {
    setSuccessDialogVisible(false);
    router.back();
  };

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('programApplications.homeVisit.title'),
      }} />
      <ScrollView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.system.text }]}>
            {t('programApplications.homeVisit.title')}
          </Text>
          <Text style={[styles.subtitle, { color: theme.system.secondaryText }]}>
            {t('programApplications.homeVisit.description')}
          </Text>
        </View>

        {/* User Information Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.system.secondaryText }]}>
            {t('programApplications.modal.userInformation')}
          </Text>

          <View style={styles.infoBox}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('identity.info.englishName')}</Text>
              <Text style={styles.infoValue}>{userProfile?.display_name || '-'}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('auth.phoneNumber')}</Text>
              <Text style={styles.infoValue}>{userProfile?.phone || '-'}</Text>
            </View>
          </View>
        </View>

        <Divider style={[styles.divider, { backgroundColor: theme.system.border }]} />

        {/* Terms and Conditions Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.system.secondaryText }]}>
            {t('programApplications.modal.termsAndConditions')}
          </Text>

          <View style={styles.termsBox}>
            <Text style={styles.termsSectionTitle}>{t('programApplications.modal.terms.eligibility')}</Text>
            <Text style={styles.termsText}>{t('programApplications.modal.terms.homeVisitEligibility')}</Text>

            <Text style={styles.termsSectionTitle}>{t('programApplications.modal.terms.services')}</Text>
            <Text style={styles.termsText}>{t('programApplications.modal.terms.homeVisitServices')}</Text>

            <Text style={styles.termsSectionTitle}>{t('programApplications.modal.terms.frequency')}</Text>
            <Text style={styles.termsText}>{t('programApplications.modal.terms.homeVisitFrequency')}</Text>

            <Text style={styles.termsSectionTitle}>{t('programApplications.modal.terms.policy')}</Text>
            <Text style={styles.termsText}>{t('programApplications.modal.terms.homeVisitPolicy')}</Text>
          </View>

          <View style={styles.checkboxContainer}>
            <Checkbox.Android
              status={agreed ? 'checked' : 'unchecked'}
              onPress={() => {
                setAgreed(!agreed);
                if (showError) setShowError(false);
              }}
              color={theme.colors.primary}
            />
            <Text
              style={[styles.checkboxLabel, { color: theme.system.text }]}
              onPress={() => {
                setAgreed(!agreed);
                if (showError) setShowError(false);
              }}
            >
              {t('programApplications.modal.agreeToTerms')}
            </Text>
          </View>
          {showError && (
            <Text style={styles.errorText}>
              {t('programApplications.modal.messages.agreementRequired')}
            </Text>
          )}
        </View>
        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleSubmit}
            style={styles.submitButton}
            contentStyle={styles.submitButtonContent}
            labelStyle={styles.submitButtonLabel}
            disabled={isSubmitting}
            loading={isSubmitting}
          >
            {t('programApplications.modal.submit')}
          </Button>
        </View>
      </ScrollView>

      <CustomDialog
        visible={successDialogVisible}
        title={t('common.success')}
        message={t('programApplications.messages.homeVisitSubmitSuccess')}
        confirmText={t('common.ok')}
        onConfirm={handleSuccessConfirm}
        type="success"
      />

      <CustomDialog
        visible={errorDialogVisible}
        title={t('common.error')}
        message={errorMessage}
        confirmText={t('common.ok')}
        onConfirm={() => setErrorDialogVisible(false)}
        type="error"
      />
    </>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    paddingTop: STATUSBAR_HEIGHT,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 24,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  section: {
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    letterSpacing: 0.3,
    lineHeight: 20,
  },
  infoBox: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  divider: {
    height: StyleSheet.hairlineWidth,
  },
  termsBox: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  termsSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginTop: 12,
    marginBottom: 4,
  },
  termsText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666666',
    marginBottom: 12,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkboxLabel: {
    fontSize: 14,
    marginLeft: 8,
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
    marginTop: 4,
    marginLeft: 8,
  },
  buttonContainer: {
    padding: 16,
  },
  submitButton: {
    borderRadius: 8,
  },
  submitButtonContent: {
    height: 48,
  },
  submitButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
});
export default HomeVisitApplicationScreen;
