import { useQuery } from '@tanstack/react-query';
import { volunteerService } from '../services/volunteerService';

// Hook for fetching QUALIFICATION volunteers (Admin view)
// These are users who applied to be general volunteers for an organization.
export const useQualificationVolunteers = (orgId, status = 'pending', page = 1, pageSize = 10) => {
  return useQuery({
    queryKey: ['qualificationVolunteers', orgId, status, page, pageSize],
    queryFn: async () => {
      const params = {
        status, // 'pending', 'approved', or 'rejected'
        limit: pageSize,
        offset: (page - 1) * pageSize,
      };
      // This service function fetches general volunteer qualification applications for an org
      const response = await volunteerService.listOrgVolunteerApplications(orgId, params);
      // Service now returns { applications, total }
      return { 
        data: response.applications || [], 
        total: response.total || 0 
      };
    },
    enabled: !!orgId, 
    staleTime: 5 * 60 * 1000, 
    keepPreviousData: true,
  });
};

// Hook for fetching EVENT-SPECIFIC volunteer applications (Admin view for an organization)
// These are users who applied to volunteer for specific events within an organization.
export const useEventRegistrationVolunteers = (orgId, status = 'pending', page = 1, pageSize = 10) => {
  return useQuery({
    queryKey: ['eventRegistrationVolunteers', orgId, status, page, pageSize],
    queryFn: async () => {
      const params = {
        status, // 'pending', 'approved', or 'rejected'
        limit: pageSize,
        offset: (page - 1) * pageSize,
      };
      // This service function fetches event-specific volunteer applications for an org
      const response = await volunteerService.listEventVolunteerApplicationsForOrg(orgId, params);
      // Service now returns { applications, total }
      return { 
        data: response.applications || [], 
        total: response.total || 0 
      };
    },
    enabled: !!orgId, 
    staleTime: 5 * 60 * 1000, 
    keepPreviousData: true,
  });
};
