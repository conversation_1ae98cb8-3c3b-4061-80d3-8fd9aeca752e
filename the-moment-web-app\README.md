# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Installation


Change directory to web-app to install dependencies and run the app:
`cd web-app`


Run the following command to install dependencies:

`npm install`

## Scripts
In the project directory, you can run:

`npm start`

Starts the app in development mode. Open http://localhost:3000 in your browser. The page will reload when you make changes.

`npm test`

Launches the test runner in interactive watch mode.

`npm run build`

Builds the app for production to the build folder. The build is optimized for best performance and minified with hashed filenames.

`npm run eject`

Note: This is a one-way operation! Ejecting gives you full control over the configuration files and dependencies, but you can't revert back.

You don’t need to eject unless you want to customize the build configuration.
