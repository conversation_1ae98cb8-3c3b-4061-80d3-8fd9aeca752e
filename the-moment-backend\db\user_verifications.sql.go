// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: user_verifications.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const adminGetFullVerificationRequestByID = `-- name: AdminGetFullVerificationRequestByID :one
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.email as reviewer_email,
    reviewer.display_name as reviewer_display_name
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
LEFT JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id
WHERE uvr.id = $1
`

type AdminGetFullVerificationRequestByIDRow struct {
	RequestID           uuid.UUID              `db:"request_id" json:"request_id"`
	UserID              uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType    VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status              VerificationStatusEnum `db:"status" json:"status"`
	DocumentID          *uuid.UUID             `db:"document_id" json:"document_id"`
	DocumentID2         *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
	SubmittedAt         time.Time              `db:"submitted_at" json:"submitted_at"`
	ReviewedAt          *time.Time             `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID    *uuid.UUID             `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes          *string                `db:"admin_notes" json:"admin_notes"`
	RequestCreatedAt    time.Time              `db:"request_created_at" json:"request_created_at"`
	RequestUpdatedAt    time.Time              `db:"request_updated_at" json:"request_updated_at"`
	FileName            *string                `db:"file_name" json:"file_name"`
	MimeType            *string                `db:"mime_type" json:"mime_type"`
	DocumentUploadedAt  *time.Time             `db:"document_uploaded_at" json:"document_uploaded_at"`
	UserEmail           *string                `db:"user_email" json:"user_email"`
	UserDisplayName     *string                `db:"user_display_name" json:"user_display_name"`
	ReviewerEmail       *string                `db:"reviewer_email" json:"reviewer_email"`
	ReviewerDisplayName *string                `db:"reviewer_display_name" json:"reviewer_display_name"`
}

func (q *Queries) AdminGetFullVerificationRequestByID(ctx context.Context, id uuid.UUID) (AdminGetFullVerificationRequestByIDRow, error) {
	row := q.db.QueryRow(ctx, adminGetFullVerificationRequestByID, id)
	var i AdminGetFullVerificationRequestByIDRow
	err := row.Scan(
		&i.RequestID,
		&i.UserID,
		&i.VerificationType,
		&i.Status,
		&i.DocumentID,
		&i.DocumentID2,
		&i.SubmittedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.AdminNotes,
		&i.RequestCreatedAt,
		&i.RequestUpdatedAt,
		&i.FileName,
		&i.MimeType,
		&i.DocumentUploadedAt,
		&i.UserEmail,
		&i.UserDisplayName,
		&i.ReviewerEmail,
		&i.ReviewerDisplayName,
	)
	return i, err
}

const adminListFullVerificationRequests = `-- name: AdminListFullVerificationRequests :many
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.display_name as reviewer_display_name -- Added reviewer display name
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id -- Join for reviewer info
WHERE (uvr.status = $1 OR $1 IS NULL)
  AND (
    $2::UUID IS NULL OR
    EXISTS (
        SELECT 1 FROM user_organization_memberships om
        WHERE om.user_id = u.id AND om.organization_id = $2::UUID
    )
  )
ORDER BY uvr.submitted_at ASC
`

type AdminListFullVerificationRequestsParams struct {
	FilterStatus NullVerificationStatusEnum `db:"filter_status" json:"filter_status"`
	FilterOrgID  *uuid.UUID                 `db:"filter_org_id" json:"filter_org_id"`
}

type AdminListFullVerificationRequestsRow struct {
	RequestID           uuid.UUID              `db:"request_id" json:"request_id"`
	UserID              uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType    VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status              VerificationStatusEnum `db:"status" json:"status"`
	DocumentID          *uuid.UUID             `db:"document_id" json:"document_id"`
	DocumentID2         *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
	SubmittedAt         time.Time              `db:"submitted_at" json:"submitted_at"`
	ReviewedAt          *time.Time             `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID    *uuid.UUID             `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes          *string                `db:"admin_notes" json:"admin_notes"`
	RequestCreatedAt    time.Time              `db:"request_created_at" json:"request_created_at"`
	RequestUpdatedAt    time.Time              `db:"request_updated_at" json:"request_updated_at"`
	FileName            *string                `db:"file_name" json:"file_name"`
	MimeType            *string                `db:"mime_type" json:"mime_type"`
	DocumentUploadedAt  *time.Time             `db:"document_uploaded_at" json:"document_uploaded_at"`
	UserEmail           *string                `db:"user_email" json:"user_email"`
	UserDisplayName     string                 `db:"user_display_name" json:"user_display_name"`
	ReviewerDisplayName *string                `db:"reviewer_display_name" json:"reviewer_display_name"`
}

func (q *Queries) AdminListFullVerificationRequests(ctx context.Context, arg AdminListFullVerificationRequestsParams) ([]AdminListFullVerificationRequestsRow, error) {
	rows, err := q.db.Query(ctx, adminListFullVerificationRequests, arg.FilterStatus, arg.FilterOrgID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []AdminListFullVerificationRequestsRow{}
	for rows.Next() {
		var i AdminListFullVerificationRequestsRow
		if err := rows.Scan(
			&i.RequestID,
			&i.UserID,
			&i.VerificationType,
			&i.Status,
			&i.DocumentID,
			&i.DocumentID2,
			&i.SubmittedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.AdminNotes,
			&i.RequestCreatedAt,
			&i.RequestUpdatedAt,
			&i.FileName,
			&i.MimeType,
			&i.DocumentUploadedAt,
			&i.UserEmail,
			&i.UserDisplayName,
			&i.ReviewerDisplayName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const adminListFullVerificationRequestsWithFilters = `-- name: AdminListFullVerificationRequestsWithFilters :many
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.display_name as reviewer_display_name
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id
LEFT JOIN event_registrations er ON uvr.user_id = er.user_id AND er.event_id = $1::UUID
WHERE
    (uvr.user_id = $2::UUID OR $2 IS NULL)
  AND (uvr.verification_type = $3 OR $3 IS NULL) -- Ensure filter_verification_type is compatible with the ENUM or cast appropriately
  AND (uvr.status = $4 OR $4 IS NULL) -- Ensure filter_status is compatible with the ENUM or cast appropriately
  AND ($5::UUID IS NULL OR EXISTS (
        SELECT 1 FROM user_organization_memberships om
        WHERE om.user_id = u.id AND om.organization_id = $5::UUID
      ))
  AND ($1::UUID IS NULL OR er.event_id IS NOT NULL) -- Ensures user is registered for the event if event_id is filtered
ORDER BY uvr.submitted_at DESC -- Or other preferred default order
LIMIT $7
OFFSET $6
`

type AdminListFullVerificationRequestsWithFiltersParams struct {
	FilterEventID          *uuid.UUID                 `db:"filter_event_id" json:"filter_event_id"`
	FilterUserID           *uuid.UUID                 `db:"filter_user_id" json:"filter_user_id"`
	FilterVerificationType NullVerificationTypeEnum   `db:"filter_verification_type" json:"filter_verification_type"`
	FilterStatus           NullVerificationStatusEnum `db:"filter_status" json:"filter_status"`
	FilterOrgID            *uuid.UUID                 `db:"filter_org_id" json:"filter_org_id"`
	Offs                   int32                      `db:"offs" json:"offs"`
	Lim                    int32                      `db:"lim" json:"lim"`
}

type AdminListFullVerificationRequestsWithFiltersRow struct {
	RequestID           uuid.UUID              `db:"request_id" json:"request_id"`
	UserID              uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType    VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status              VerificationStatusEnum `db:"status" json:"status"`
	DocumentID          *uuid.UUID             `db:"document_id" json:"document_id"`
	DocumentID2         *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
	SubmittedAt         time.Time              `db:"submitted_at" json:"submitted_at"`
	ReviewedAt          *time.Time             `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID    *uuid.UUID             `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes          *string                `db:"admin_notes" json:"admin_notes"`
	RequestCreatedAt    time.Time              `db:"request_created_at" json:"request_created_at"`
	RequestUpdatedAt    time.Time              `db:"request_updated_at" json:"request_updated_at"`
	FileName            *string                `db:"file_name" json:"file_name"`
	MimeType            *string                `db:"mime_type" json:"mime_type"`
	DocumentUploadedAt  *time.Time             `db:"document_uploaded_at" json:"document_uploaded_at"`
	UserEmail           *string                `db:"user_email" json:"user_email"`
	UserDisplayName     string                 `db:"user_display_name" json:"user_display_name"`
	ReviewerDisplayName *string                `db:"reviewer_display_name" json:"reviewer_display_name"`
}

// Optional: Join with event_registrations if filter_event_id is provided
func (q *Queries) AdminListFullVerificationRequestsWithFilters(ctx context.Context, arg AdminListFullVerificationRequestsWithFiltersParams) ([]AdminListFullVerificationRequestsWithFiltersRow, error) {
	rows, err := q.db.Query(ctx, adminListFullVerificationRequestsWithFilters,
		arg.FilterEventID,
		arg.FilterUserID,
		arg.FilterVerificationType,
		arg.FilterStatus,
		arg.FilterOrgID,
		arg.Offs,
		arg.Lim,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []AdminListFullVerificationRequestsWithFiltersRow{}
	for rows.Next() {
		var i AdminListFullVerificationRequestsWithFiltersRow
		if err := rows.Scan(
			&i.RequestID,
			&i.UserID,
			&i.VerificationType,
			&i.Status,
			&i.DocumentID,
			&i.DocumentID2,
			&i.SubmittedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.AdminNotes,
			&i.RequestCreatedAt,
			&i.RequestUpdatedAt,
			&i.FileName,
			&i.MimeType,
			&i.DocumentUploadedAt,
			&i.UserEmail,
			&i.UserDisplayName,
			&i.ReviewerDisplayName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const countAdminListFullVerificationRequestsWithFilters = `-- name: CountAdminListFullVerificationRequestsWithFilters :one
SELECT COUNT(*)
FROM user_verification_requests uvr
JOIN users u ON uvr.user_id = u.id
LEFT JOIN event_registrations er ON uvr.user_id = er.user_id AND er.event_id = $1::UUID
WHERE
    (uvr.user_id = $2::UUID OR $2 IS NULL)
  AND (uvr.verification_type = $3 OR $3 IS NULL)
  AND (uvr.status = $4 OR $4 IS NULL)
  AND ($5::UUID IS NULL OR EXISTS (
        SELECT 1 FROM user_organization_memberships om
        WHERE om.user_id = u.id AND om.organization_id = $5::UUID
      ))
  AND ($1::UUID IS NULL OR er.event_id IS NOT NULL)
`

type CountAdminListFullVerificationRequestsWithFiltersParams struct {
	FilterEventID          *uuid.UUID                 `db:"filter_event_id" json:"filter_event_id"`
	FilterUserID           *uuid.UUID                 `db:"filter_user_id" json:"filter_user_id"`
	FilterVerificationType NullVerificationTypeEnum   `db:"filter_verification_type" json:"filter_verification_type"`
	FilterStatus           NullVerificationStatusEnum `db:"filter_status" json:"filter_status"`
	FilterOrgID            *uuid.UUID                 `db:"filter_org_id" json:"filter_org_id"`
}

// Companion query for total count for pagination
// Optional: Join with event_registrations if filter_event_id is provided
func (q *Queries) CountAdminListFullVerificationRequestsWithFilters(ctx context.Context, arg CountAdminListFullVerificationRequestsWithFiltersParams) (int64, error) {
	row := q.db.QueryRow(ctx, countAdminListFullVerificationRequestsWithFilters,
		arg.FilterEventID,
		arg.FilterUserID,
		arg.FilterVerificationType,
		arg.FilterStatus,
		arg.FilterOrgID,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createAddressProofVerification = `-- name: CreateAddressProofVerification :one
INSERT INTO verification_address_proofs (
    verification_request_id,
    full_address
) VALUES (
    $1, $2
) RETURNING id, verification_request_id, full_address, created_at, updated_at
`

type CreateAddressProofVerificationParams struct {
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	FullAddress           *string   `db:"full_address" json:"full_address"`
}

// Address Proof
func (q *Queries) CreateAddressProofVerification(ctx context.Context, arg CreateAddressProofVerificationParams) (VerificationAddressProof, error) {
	row := q.db.QueryRow(ctx, createAddressProofVerification, arg.VerificationRequestID, arg.FullAddress)
	var i VerificationAddressProof
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.FullAddress,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createHKIDCardVerification = `-- name: CreateHKIDCardVerification :one

INSERT INTO verification_hk_id_cards (
    verification_request_id,
    chinese_name,
    chinese_commercial_code,
    english_name,
    sex,
    date_of_birth,
    hk_id_number,
    is_permanent_resident
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
) RETURNING id, verification_request_id, chinese_name, chinese_commercial_code, english_name, sex, date_of_birth, hk_id_number, is_permanent_resident, created_at, updated_at
`

type CreateHKIDCardVerificationParams struct {
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	ChineseName           *string   `db:"chinese_name" json:"chinese_name"`
	ChineseCommercialCode *string   `db:"chinese_commercial_code" json:"chinese_commercial_code"`
	EnglishName           *string   `db:"english_name" json:"english_name"`
	Sex                   *string   `db:"sex" json:"sex"`
	DateOfBirth           string    `db:"date_of_birth" json:"date_of_birth"`
	HkIDNumber            *string   `db:"hk_id_number" json:"hk_id_number"`
	IsPermanentResident   *bool     `db:"is_permanent_resident" json:"is_permanent_resident"`
}

// Specific Verification Type Queries --
// HK ID Card
func (q *Queries) CreateHKIDCardVerification(ctx context.Context, arg CreateHKIDCardVerificationParams) (VerificationHkIDCard, error) {
	row := q.db.QueryRow(ctx, createHKIDCardVerification,
		arg.VerificationRequestID,
		arg.ChineseName,
		arg.ChineseCommercialCode,
		arg.EnglishName,
		arg.Sex,
		arg.DateOfBirth,
		arg.HkIDNumber,
		arg.IsPermanentResident,
	)
	var i VerificationHkIDCard
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.ChineseName,
		&i.ChineseCommercialCode,
		&i.EnglishName,
		&i.Sex,
		&i.DateOfBirth,
		&i.HkIDNumber,
		&i.IsPermanentResident,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createHKYouthPlusVerification = `-- name: CreateHKYouthPlusVerification :one
INSERT INTO verification_hk_youth_plus (
    verification_request_id,
    member_number,
    document_id
) VALUES (
    $1, $2, $3
) RETURNING id, verification_request_id, member_number, created_at, updated_at, document_id
`

type CreateHKYouthPlusVerificationParams struct {
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	MemberNumber          *string   `db:"member_number" json:"member_number"`
	DocumentID            uuid.UUID `db:"document_id" json:"document_id"`
}

// HK Youth+
func (q *Queries) CreateHKYouthPlusVerification(ctx context.Context, arg CreateHKYouthPlusVerificationParams) (VerificationHkYouthPlu, error) {
	row := q.db.QueryRow(ctx, createHKYouthPlusVerification, arg.VerificationRequestID, arg.MemberNumber, arg.DocumentID)
	var i VerificationHkYouthPlu
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.MemberNumber,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DocumentID,
	)
	return i, err
}

const createHomeVisitVerification = `-- name: CreateHomeVisitVerification :one
INSERT INTO verification_home_visits (
    verification_request_id,
    notes
) VALUES (
    $1, $2
) RETURNING id, verification_request_id, notes, created_at, updated_at
`

type CreateHomeVisitVerificationParams struct {
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	Notes                 *string   `db:"notes" json:"notes"`
}

// Home Visit
func (q *Queries) CreateHomeVisitVerification(ctx context.Context, arg CreateHomeVisitVerificationParams) (VerificationHomeVisit, error) {
	row := q.db.QueryRow(ctx, createHomeVisitVerification, arg.VerificationRequestID, arg.Notes)
	var i VerificationHomeVisit
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createMainlandChinaIDCardVerification = `-- name: CreateMainlandChinaIDCardVerification :one
INSERT INTO verification_mainland_china_id_cards (
    verification_request_id,
    chinese_name,
    sex,
    date_of_birth,
    mainland_id_number,
    valid_until
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING id, verification_request_id, chinese_name, sex, date_of_birth, mainland_id_number, valid_until, created_at, updated_at
`

type CreateMainlandChinaIDCardVerificationParams struct {
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	ChineseName           *string   `db:"chinese_name" json:"chinese_name"`
	Sex                   *string   `db:"sex" json:"sex"`
	DateOfBirth           string    `db:"date_of_birth" json:"date_of_birth"`
	MainlandIDNumber      *string   `db:"mainland_id_number" json:"mainland_id_number"`
	ValidUntil            string    `db:"valid_until" json:"valid_until"`
}

// Mainland China ID Card
func (q *Queries) CreateMainlandChinaIDCardVerification(ctx context.Context, arg CreateMainlandChinaIDCardVerificationParams) (VerificationMainlandChinaIDCard, error) {
	row := q.db.QueryRow(ctx, createMainlandChinaIDCardVerification,
		arg.VerificationRequestID,
		arg.ChineseName,
		arg.Sex,
		arg.DateOfBirth,
		arg.MainlandIDNumber,
		arg.ValidUntil,
	)
	var i VerificationMainlandChinaIDCard
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.ChineseName,
		&i.Sex,
		&i.DateOfBirth,
		&i.MainlandIDNumber,
		&i.ValidUntil,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createMainlandTravelPermitVerification = `-- name: CreateMainlandTravelPermitVerification :one
INSERT INTO verification_mainland_travel_permits (
    verification_request_id,
    permit_number,
    issue_date,
    expiry_date
) VALUES (
    $1, $2, $3, $4
) RETURNING id, verification_request_id, permit_number, issue_date, expiry_date, created_at, updated_at
`

type CreateMainlandTravelPermitVerificationParams struct {
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	PermitNumber          *string   `db:"permit_number" json:"permit_number"`
	IssueDate             string    `db:"issue_date" json:"issue_date"`
	ExpiryDate            string    `db:"expiry_date" json:"expiry_date"`
}

// Home Return Permit -> Mainland Travel Permit
func (q *Queries) CreateMainlandTravelPermitVerification(ctx context.Context, arg CreateMainlandTravelPermitVerificationParams) (VerificationMainlandTravelPermit, error) {
	row := q.db.QueryRow(ctx, createMainlandTravelPermitVerification,
		arg.VerificationRequestID,
		arg.PermitNumber,
		arg.IssueDate,
		arg.ExpiryDate,
	)
	var i VerificationMainlandTravelPermit
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.PermitNumber,
		&i.IssueDate,
		&i.ExpiryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createPassportVerification = `-- name: CreatePassportVerification :one
INSERT INTO verification_passports (
    verification_request_id,
    passport_number,
    issuing_country,
    issue_date,
    expiry_date
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING id, verification_request_id, passport_number, issuing_country, issue_date, expiry_date, created_at, updated_at
`

type CreatePassportVerificationParams struct {
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	PassportNumber        *string   `db:"passport_number" json:"passport_number"`
	IssuingCountry        *string   `db:"issuing_country" json:"issuing_country"`
	IssueDate             string    `db:"issue_date" json:"issue_date"`
	ExpiryDate            string    `db:"expiry_date" json:"expiry_date"`
}

// Passport
func (q *Queries) CreatePassportVerification(ctx context.Context, arg CreatePassportVerificationParams) (VerificationPassport, error) {
	row := q.db.QueryRow(ctx, createPassportVerification,
		arg.VerificationRequestID,
		arg.PassportNumber,
		arg.IssuingCountry,
		arg.IssueDate,
		arg.ExpiryDate,
	)
	var i VerificationPassport
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.PassportNumber,
		&i.IssuingCountry,
		&i.IssueDate,
		&i.ExpiryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createStudentIDVerification = `-- name: CreateStudentIDVerification :one
INSERT INTO verification_student_ids (
    verification_request_id,
    school_name,
    grade,
    expiry_date
) VALUES (
    $1, $2, $3, $4
) RETURNING id, verification_request_id, school_name, grade, expiry_date, created_at, updated_at
`

type CreateStudentIDVerificationParams struct {
	VerificationRequestID uuid.UUID `db:"verification_request_id" json:"verification_request_id"`
	SchoolName            *string   `db:"school_name" json:"school_name"`
	Grade                 *string   `db:"grade" json:"grade"`
	ExpiryDate            string    `db:"expiry_date" json:"expiry_date"`
}

// Student ID
func (q *Queries) CreateStudentIDVerification(ctx context.Context, arg CreateStudentIDVerificationParams) (VerificationStudentID, error) {
	row := q.db.QueryRow(ctx, createStudentIDVerification,
		arg.VerificationRequestID,
		arg.SchoolName,
		arg.Grade,
		arg.ExpiryDate,
	)
	var i VerificationStudentID
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.SchoolName,
		&i.Grade,
		&i.ExpiryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createUserVerificationRequest = `-- name: CreateUserVerificationRequest :one
INSERT INTO user_verification_requests (
    user_id,
    verification_type,
    status,
    document_id,
    document_id_2
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING id, user_id, verification_type, status, document_id, submitted_at, reviewed_at, reviewed_by_user_id, admin_notes, created_at, updated_at, document_id_2
`

type CreateUserVerificationRequestParams struct {
	UserID           uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status           VerificationStatusEnum `db:"status" json:"status"`
	DocumentID       *uuid.UUID             `db:"document_id" json:"document_id"`
	DocumentID2      *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
}

func (q *Queries) CreateUserVerificationRequest(ctx context.Context, arg CreateUserVerificationRequestParams) (UserVerificationRequest, error) {
	row := q.db.QueryRow(ctx, createUserVerificationRequest,
		arg.UserID,
		arg.VerificationType,
		arg.Status,
		arg.DocumentID,
		arg.DocumentID2,
	)
	var i UserVerificationRequest
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VerificationType,
		&i.Status,
		&i.DocumentID,
		&i.SubmittedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DocumentID2,
	)
	return i, err
}

const createVerificationDocument = `-- name: CreateVerificationDocument :one
INSERT INTO verification_documents (
    user_id,
    file_name,
    mime_type,
    file_data
) VALUES (
    $1, $2, $3, $4
) RETURNING id, user_id, file_name, mime_type, file_data, uploaded_at, created_at, updated_at
`

type CreateVerificationDocumentParams struct {
	UserID   uuid.UUID `db:"user_id" json:"user_id"`
	FileName string    `db:"file_name" json:"file_name"`
	MimeType string    `db:"mime_type" json:"mime_type"`
	FileData []byte    `db:"file_data" json:"file_data"`
}

func (q *Queries) CreateVerificationDocument(ctx context.Context, arg CreateVerificationDocumentParams) (VerificationDocument, error) {
	row := q.db.QueryRow(ctx, createVerificationDocument,
		arg.UserID,
		arg.FileName,
		arg.MimeType,
		arg.FileData,
	)
	var i VerificationDocument
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.FileName,
		&i.MimeType,
		&i.FileData,
		&i.UploadedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteVerificationDocument = `-- name: DeleteVerificationDocument :exec
DELETE FROM verification_documents
WHERE id = $1 AND user_id = $2
`

type DeleteVerificationDocumentParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	UserID uuid.UUID `db:"user_id" json:"user_id"`
}

func (q *Queries) DeleteVerificationDocument(ctx context.Context, arg DeleteVerificationDocumentParams) error {
	_, err := q.db.Exec(ctx, deleteVerificationDocument, arg.ID, arg.UserID)
	return err
}

const getAddressProofVerificationByRequestID = `-- name: GetAddressProofVerificationByRequestID :one
SELECT id, verification_request_id, full_address, created_at, updated_at FROM verification_address_proofs
WHERE verification_request_id = $1
`

func (q *Queries) GetAddressProofVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationAddressProof, error) {
	row := q.db.QueryRow(ctx, getAddressProofVerificationByRequestID, verificationRequestID)
	var i VerificationAddressProof
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.FullAddress,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getFullVerificationDetails = `-- name: GetFullVerificationDetails :one
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    
    COALESCE(vd.file_name, '') as file_name,
    COALESCE(vd.mime_type, '') as mime_type,
    vd.uploaded_at as document_uploaded_at,
    
    COALESCE(vd2.file_name, '') AS file_name_2,
    COALESCE(vd2.mime_type, '') AS mime_type_2,
    
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.email as reviewer_email,
    reviewer.display_name as reviewer_display_name,
    hkid.id, hkid.verification_request_id, hkid.chinese_name, hkid.chinese_commercial_code, hkid.english_name, hkid.sex, hkid.date_of_birth, hkid.hk_id_number, hkid.is_permanent_resident, hkid.created_at, hkid.updated_at,
    mcid.id, mcid.verification_request_id, mcid.chinese_name, mcid.sex, mcid.date_of_birth, mcid.mainland_id_number, mcid.valid_until, mcid.created_at, mcid.updated_at,
    hrp.id, hrp.verification_request_id, hrp.permit_number, hrp.issue_date, hrp.expiry_date, hrp.created_at, hrp.updated_at,
    pass.id, pass.verification_request_id, pass.passport_number, pass.issuing_country, pass.issue_date, pass.expiry_date, pass.created_at, pass.updated_at,
    hkyp.id, hkyp.verification_request_id, hkyp.member_number, hkyp.created_at, hkyp.updated_at, hkyp.document_id,
    ap.id, ap.verification_request_id, ap.full_address, ap.created_at, ap.updated_at,
    sid.id, sid.verification_request_id, sid.school_name, sid.grade, sid.expiry_date, sid.created_at, sid.updated_at,
    hv.id, hv.verification_request_id, hv.notes, hv.created_at, hv.updated_at
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
LEFT JOIN verification_documents vd2 ON uvr.document_id_2 = vd2.id
LEFT JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id
LEFT JOIN verification_hk_id_cards hkid ON uvr.id = hkid.verification_request_id AND uvr.verification_type = 'hk_id_card'
LEFT JOIN verification_mainland_china_id_cards mcid ON uvr.id = mcid.verification_request_id AND uvr.verification_type = 'mainland_china_id_card'
LEFT JOIN verification_mainland_travel_permits hrp ON uvr.id = hrp.verification_request_id AND uvr.verification_type = 'mainland_travel_permit'
LEFT JOIN verification_passports pass ON uvr.id = pass.verification_request_id AND uvr.verification_type = 'passport'
LEFT JOIN verification_hk_youth_plus hkyp ON uvr.id = hkyp.verification_request_id AND uvr.verification_type = 'hk_youth_plus'
LEFT JOIN verification_address_proofs ap ON uvr.id = ap.verification_request_id AND uvr.verification_type = 'address_proof'
LEFT JOIN verification_student_ids sid ON uvr.id = sid.verification_request_id AND uvr.verification_type = 'student_id'
LEFT JOIN verification_home_visits hv ON uvr.id = hv.verification_request_id AND uvr.verification_type = 'home_visit'
WHERE uvr.id = $1
`

type GetFullVerificationDetailsRow struct {
	RequestID               uuid.UUID              `db:"request_id" json:"request_id"`
	UserID                  uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType        VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status                  VerificationStatusEnum `db:"status" json:"status"`
	DocumentID              *uuid.UUID             `db:"document_id" json:"document_id"`
	DocumentID2             *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
	SubmittedAt             time.Time              `db:"submitted_at" json:"submitted_at"`
	ReviewedAt              *time.Time             `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID        *uuid.UUID             `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes              *string                `db:"admin_notes" json:"admin_notes"`
	RequestCreatedAt        time.Time              `db:"request_created_at" json:"request_created_at"`
	RequestUpdatedAt        time.Time              `db:"request_updated_at" json:"request_updated_at"`
	FileName                string                 `db:"file_name" json:"file_name"`
	MimeType                string                 `db:"mime_type" json:"mime_type"`
	DocumentUploadedAt      *time.Time             `db:"document_uploaded_at" json:"document_uploaded_at"`
	FileName2               string                 `db:"file_name_2" json:"file_name_2"`
	MimeType2               string                 `db:"mime_type_2" json:"mime_type_2"`
	UserEmail               *string                `db:"user_email" json:"user_email"`
	UserDisplayName         *string                `db:"user_display_name" json:"user_display_name"`
	ReviewerEmail           *string                `db:"reviewer_email" json:"reviewer_email"`
	ReviewerDisplayName     *string                `db:"reviewer_display_name" json:"reviewer_display_name"`
	ID                      *uuid.UUID             `db:"id" json:"id"`
	VerificationRequestID   *uuid.UUID             `db:"verification_request_id" json:"verification_request_id"`
	ChineseName             *string                `db:"chinese_name" json:"chinese_name"`
	ChineseCommercialCode   *string                `db:"chinese_commercial_code" json:"chinese_commercial_code"`
	EnglishName             *string                `db:"english_name" json:"english_name"`
	Sex                     *string                `db:"sex" json:"sex"`
	DateOfBirth             *string                `db:"date_of_birth" json:"date_of_birth"`
	HkIDNumber              *string                `db:"hk_id_number" json:"hk_id_number"`
	IsPermanentResident     *bool                  `db:"is_permanent_resident" json:"is_permanent_resident"`
	CreatedAt               *time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt               *time.Time             `db:"updated_at" json:"updated_at"`
	ID_2                    *uuid.UUID             `db:"id_2" json:"id_2"`
	VerificationRequestID_2 *uuid.UUID             `db:"verification_request_id_2" json:"verification_request_id_2"`
	ChineseName_2           *string                `db:"chinese_name_2" json:"chinese_name_2"`
	Sex_2                   *string                `db:"sex_2" json:"sex_2"`
	DateOfBirth_2           *string                `db:"date_of_birth_2" json:"date_of_birth_2"`
	MainlandIDNumber        *string                `db:"mainland_id_number" json:"mainland_id_number"`
	ValidUntil              *string                `db:"valid_until" json:"valid_until"`
	CreatedAt_2             *time.Time             `db:"created_at_2" json:"created_at_2"`
	UpdatedAt_2             *time.Time             `db:"updated_at_2" json:"updated_at_2"`
	ID_3                    *uuid.UUID             `db:"id_3" json:"id_3"`
	VerificationRequestID_3 *uuid.UUID             `db:"verification_request_id_3" json:"verification_request_id_3"`
	PermitNumber            *string                `db:"permit_number" json:"permit_number"`
	IssueDate               *string                `db:"issue_date" json:"issue_date"`
	ExpiryDate              *string                `db:"expiry_date" json:"expiry_date"`
	CreatedAt_3             *time.Time             `db:"created_at_3" json:"created_at_3"`
	UpdatedAt_3             *time.Time             `db:"updated_at_3" json:"updated_at_3"`
	ID_4                    *uuid.UUID             `db:"id_4" json:"id_4"`
	VerificationRequestID_4 *uuid.UUID             `db:"verification_request_id_4" json:"verification_request_id_4"`
	PassportNumber          *string                `db:"passport_number" json:"passport_number"`
	IssuingCountry          *string                `db:"issuing_country" json:"issuing_country"`
	IssueDate_2             *string                `db:"issue_date_2" json:"issue_date_2"`
	ExpiryDate_2            *string                `db:"expiry_date_2" json:"expiry_date_2"`
	CreatedAt_4             *time.Time             `db:"created_at_4" json:"created_at_4"`
	UpdatedAt_4             *time.Time             `db:"updated_at_4" json:"updated_at_4"`
	ID_5                    *uuid.UUID             `db:"id_5" json:"id_5"`
	VerificationRequestID_5 *uuid.UUID             `db:"verification_request_id_5" json:"verification_request_id_5"`
	MemberNumber            *string                `db:"member_number" json:"member_number"`
	CreatedAt_5             *time.Time             `db:"created_at_5" json:"created_at_5"`
	UpdatedAt_5             *time.Time             `db:"updated_at_5" json:"updated_at_5"`
	DocumentID_2            *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
	ID_6                    *uuid.UUID             `db:"id_6" json:"id_6"`
	VerificationRequestID_6 *uuid.UUID             `db:"verification_request_id_6" json:"verification_request_id_6"`
	FullAddress             *string                `db:"full_address" json:"full_address"`
	CreatedAt_6             *time.Time             `db:"created_at_6" json:"created_at_6"`
	UpdatedAt_6             *time.Time             `db:"updated_at_6" json:"updated_at_6"`
	ID_7                    *uuid.UUID             `db:"id_7" json:"id_7"`
	VerificationRequestID_7 *uuid.UUID             `db:"verification_request_id_7" json:"verification_request_id_7"`
	SchoolName              *string                `db:"school_name" json:"school_name"`
	Grade                   *string                `db:"grade" json:"grade"`
	ExpiryDate_3            *string                `db:"expiry_date_3" json:"expiry_date_3"`
	CreatedAt_7             *time.Time             `db:"created_at_7" json:"created_at_7"`
	UpdatedAt_7             *time.Time             `db:"updated_at_7" json:"updated_at_7"`
	ID_8                    *uuid.UUID             `db:"id_8" json:"id_8"`
	VerificationRequestID_8 *uuid.UUID             `db:"verification_request_id_8" json:"verification_request_id_8"`
	Notes                   *string                `db:"notes" json:"notes"`
	CreatedAt_8             *time.Time             `db:"created_at_8" json:"created_at_8"`
	UpdatedAt_8             *time.Time             `db:"updated_at_8" json:"updated_at_8"`
}

func (q *Queries) GetFullVerificationDetails(ctx context.Context, id uuid.UUID) (GetFullVerificationDetailsRow, error) {
	row := q.db.QueryRow(ctx, getFullVerificationDetails, id)
	var i GetFullVerificationDetailsRow
	err := row.Scan(
		&i.RequestID,
		&i.UserID,
		&i.VerificationType,
		&i.Status,
		&i.DocumentID,
		&i.DocumentID2,
		&i.SubmittedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.AdminNotes,
		&i.RequestCreatedAt,
		&i.RequestUpdatedAt,
		&i.FileName,
		&i.MimeType,
		&i.DocumentUploadedAt,
		&i.FileName2,
		&i.MimeType2,
		&i.UserEmail,
		&i.UserDisplayName,
		&i.ReviewerEmail,
		&i.ReviewerDisplayName,
		&i.ID,
		&i.VerificationRequestID,
		&i.ChineseName,
		&i.ChineseCommercialCode,
		&i.EnglishName,
		&i.Sex,
		&i.DateOfBirth,
		&i.HkIDNumber,
		&i.IsPermanentResident,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ID_2,
		&i.VerificationRequestID_2,
		&i.ChineseName_2,
		&i.Sex_2,
		&i.DateOfBirth_2,
		&i.MainlandIDNumber,
		&i.ValidUntil,
		&i.CreatedAt_2,
		&i.UpdatedAt_2,
		&i.ID_3,
		&i.VerificationRequestID_3,
		&i.PermitNumber,
		&i.IssueDate,
		&i.ExpiryDate,
		&i.CreatedAt_3,
		&i.UpdatedAt_3,
		&i.ID_4,
		&i.VerificationRequestID_4,
		&i.PassportNumber,
		&i.IssuingCountry,
		&i.IssueDate_2,
		&i.ExpiryDate_2,
		&i.CreatedAt_4,
		&i.UpdatedAt_4,
		&i.ID_5,
		&i.VerificationRequestID_5,
		&i.MemberNumber,
		&i.CreatedAt_5,
		&i.UpdatedAt_5,
		&i.DocumentID_2,
		&i.ID_6,
		&i.VerificationRequestID_6,
		&i.FullAddress,
		&i.CreatedAt_6,
		&i.UpdatedAt_6,
		&i.ID_7,
		&i.VerificationRequestID_7,
		&i.SchoolName,
		&i.Grade,
		&i.ExpiryDate_3,
		&i.CreatedAt_7,
		&i.UpdatedAt_7,
		&i.ID_8,
		&i.VerificationRequestID_8,
		&i.Notes,
		&i.CreatedAt_8,
		&i.UpdatedAt_8,
	)
	return i, err
}

const getHKIDCardVerificationByRequestID = `-- name: GetHKIDCardVerificationByRequestID :one
SELECT id, verification_request_id, chinese_name, chinese_commercial_code, english_name, sex, date_of_birth, hk_id_number, is_permanent_resident, created_at, updated_at FROM verification_hk_id_cards
WHERE verification_request_id = $1
`

func (q *Queries) GetHKIDCardVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationHkIDCard, error) {
	row := q.db.QueryRow(ctx, getHKIDCardVerificationByRequestID, verificationRequestID)
	var i VerificationHkIDCard
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.ChineseName,
		&i.ChineseCommercialCode,
		&i.EnglishName,
		&i.Sex,
		&i.DateOfBirth,
		&i.HkIDNumber,
		&i.IsPermanentResident,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getHKYouthPlusVerificationByRequestID = `-- name: GetHKYouthPlusVerificationByRequestID :one
SELECT id, verification_request_id, member_number, created_at, updated_at, document_id FROM verification_hk_youth_plus
WHERE verification_request_id = $1
`

func (q *Queries) GetHKYouthPlusVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationHkYouthPlu, error) {
	row := q.db.QueryRow(ctx, getHKYouthPlusVerificationByRequestID, verificationRequestID)
	var i VerificationHkYouthPlu
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.MemberNumber,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DocumentID,
	)
	return i, err
}

const getHomeVisitVerificationByRequestID = `-- name: GetHomeVisitVerificationByRequestID :one
SELECT id, verification_request_id, notes, created_at, updated_at FROM verification_home_visits
WHERE verification_request_id = $1
`

func (q *Queries) GetHomeVisitVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationHomeVisit, error) {
	row := q.db.QueryRow(ctx, getHomeVisitVerificationByRequestID, verificationRequestID)
	var i VerificationHomeVisit
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getMainlandChinaIDCardVerificationByRequestID = `-- name: GetMainlandChinaIDCardVerificationByRequestID :one
SELECT id, verification_request_id, chinese_name, sex, date_of_birth, mainland_id_number, valid_until, created_at, updated_at FROM verification_mainland_china_id_cards
WHERE verification_request_id = $1
`

func (q *Queries) GetMainlandChinaIDCardVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationMainlandChinaIDCard, error) {
	row := q.db.QueryRow(ctx, getMainlandChinaIDCardVerificationByRequestID, verificationRequestID)
	var i VerificationMainlandChinaIDCard
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.ChineseName,
		&i.Sex,
		&i.DateOfBirth,
		&i.MainlandIDNumber,
		&i.ValidUntil,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getMainlandTravelPermitVerificationByRequestID = `-- name: GetMainlandTravelPermitVerificationByRequestID :one
SELECT id, verification_request_id, permit_number, issue_date, expiry_date, created_at, updated_at FROM verification_mainland_travel_permits
WHERE verification_request_id = $1
`

func (q *Queries) GetMainlandTravelPermitVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationMainlandTravelPermit, error) {
	row := q.db.QueryRow(ctx, getMainlandTravelPermitVerificationByRequestID, verificationRequestID)
	var i VerificationMainlandTravelPermit
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.PermitNumber,
		&i.IssueDate,
		&i.ExpiryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getPassportVerificationByRequestID = `-- name: GetPassportVerificationByRequestID :one
SELECT id, verification_request_id, passport_number, issuing_country, issue_date, expiry_date, created_at, updated_at FROM verification_passports
WHERE verification_request_id = $1
`

func (q *Queries) GetPassportVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationPassport, error) {
	row := q.db.QueryRow(ctx, getPassportVerificationByRequestID, verificationRequestID)
	var i VerificationPassport
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.PassportNumber,
		&i.IssuingCountry,
		&i.IssueDate,
		&i.ExpiryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentIDVerificationByRequestID = `-- name: GetStudentIDVerificationByRequestID :one
SELECT id, verification_request_id, school_name, grade, expiry_date, created_at, updated_at FROM verification_student_ids
WHERE verification_request_id = $1
`

func (q *Queries) GetStudentIDVerificationByRequestID(ctx context.Context, verificationRequestID uuid.UUID) (VerificationStudentID, error) {
	row := q.db.QueryRow(ctx, getStudentIDVerificationByRequestID, verificationRequestID)
	var i VerificationStudentID
	err := row.Scan(
		&i.ID,
		&i.VerificationRequestID,
		&i.SchoolName,
		&i.Grade,
		&i.ExpiryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserVerificationRequestByID = `-- name: GetUserVerificationRequestByID :one
SELECT
    uvr.id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.submitted_at,
    uvr.document_id,
    uvr.document_id_2,
    vd.id, vd.user_id, vd.file_name, vd.mime_type, vd.file_data, vd.uploaded_at, vd.created_at, vd.updated_at
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
WHERE uvr.id = $1
`

type GetUserVerificationRequestByIDRow struct {
	ID                   uuid.UUID              `db:"id" json:"id"`
	UserID               uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType     VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status               VerificationStatusEnum `db:"status" json:"status"`
	SubmittedAt          time.Time              `db:"submitted_at" json:"submitted_at"`
	DocumentID           *uuid.UUID             `db:"document_id" json:"document_id"`
	DocumentID2          *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
	VerificationDocument VerificationDocument   `db:"verification_document" json:"verification_document"`
}

func (q *Queries) GetUserVerificationRequestByID(ctx context.Context, id uuid.UUID) (GetUserVerificationRequestByIDRow, error) {
	row := q.db.QueryRow(ctx, getUserVerificationRequestByID, id)
	var i GetUserVerificationRequestByIDRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VerificationType,
		&i.Status,
		&i.SubmittedAt,
		&i.DocumentID,
		&i.DocumentID2,
		&i.VerificationDocument.ID,
		&i.VerificationDocument.UserID,
		&i.VerificationDocument.FileName,
		&i.VerificationDocument.MimeType,
		&i.VerificationDocument.FileData,
		&i.VerificationDocument.UploadedAt,
		&i.VerificationDocument.CreatedAt,
		&i.VerificationDocument.UpdatedAt,
	)
	return i, err
}

const getUserVerificationRequestsSimple = `-- name: GetUserVerificationRequestsSimple :many
SELECT id, user_id, verification_type, status, document_id, submitted_at, reviewed_at, reviewed_by_user_id, admin_notes, created_at, updated_at, document_id_2 FROM user_verification_requests WHERE user_id = $1 ORDER BY submitted_at DESC
`

func (q *Queries) GetUserVerificationRequestsSimple(ctx context.Context, userID uuid.UUID) ([]UserVerificationRequest, error) {
	rows, err := q.db.Query(ctx, getUserVerificationRequestsSimple, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []UserVerificationRequest{}
	for rows.Next() {
		var i UserVerificationRequest
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.VerificationType,
			&i.Status,
			&i.DocumentID,
			&i.SubmittedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.AdminNotes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DocumentID2,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserVerificationStatuses = `-- name: GetUserVerificationStatuses :one
SELECT
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'hk_id_card' AND uvr.status = 'approved') AS hk_id_card,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'mainland_china_id_card' AND uvr.status = 'approved') AS mainland_china_id_card,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'mainland_travel_permit' AND uvr.status = 'approved') AS mainland_travel_permit,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'passport' AND uvr.status = 'approved') AS passport,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'hk_youth_plus' AND uvr.status = 'approved') AS hk_youth_plus,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'address_proof' AND uvr.status = 'approved') AS address_proof,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'student_id' AND uvr.status = 'approved') AS student_id
`

type GetUserVerificationStatusesRow struct {
	HkIDCard             bool `db:"hk_id_card" json:"hk_id_card"`
	MainlandChinaIDCard  bool `db:"mainland_china_id_card" json:"mainland_china_id_card"`
	MainlandTravelPermit bool `db:"mainland_travel_permit" json:"mainland_travel_permit"`
	Passport             bool `db:"passport" json:"passport"`
	HkYouthPlus          bool `db:"hk_youth_plus" json:"hk_youth_plus"`
	AddressProof         bool `db:"address_proof" json:"address_proof"`
	StudentID            bool `db:"student_id" json:"student_id"`
}

func (q *Queries) GetUserVerificationStatuses(ctx context.Context, userID uuid.UUID) (GetUserVerificationStatusesRow, error) {
	row := q.db.QueryRow(ctx, getUserVerificationStatuses, userID)
	var i GetUserVerificationStatusesRow
	err := row.Scan(
		&i.HkIDCard,
		&i.MainlandChinaIDCard,
		&i.MainlandTravelPermit,
		&i.Passport,
		&i.HkYouthPlus,
		&i.AddressProof,
		&i.StudentID,
	)
	return i, err
}

const getVerificationDocumentByID = `-- name: GetVerificationDocumentByID :one
SELECT id, user_id, file_name, mime_type, file_data, uploaded_at, created_at, updated_at FROM verification_documents
WHERE id = $1 AND user_id = $2
`

type GetVerificationDocumentByIDParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	UserID uuid.UUID `db:"user_id" json:"user_id"`
}

func (q *Queries) GetVerificationDocumentByID(ctx context.Context, arg GetVerificationDocumentByIDParams) (VerificationDocument, error) {
	row := q.db.QueryRow(ctx, getVerificationDocumentByID, arg.ID, arg.UserID)
	var i VerificationDocument
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.FileName,
		&i.MimeType,
		&i.FileData,
		&i.UploadedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getVerificationDocumentDataByID = `-- name: GetVerificationDocumentDataByID :one
SELECT file_data FROM verification_documents
WHERE id = $1
`

func (q *Queries) GetVerificationDocumentDataByID(ctx context.Context, id uuid.UUID) ([]byte, error) {
	row := q.db.QueryRow(ctx, getVerificationDocumentDataByID, id)
	var file_data []byte
	err := row.Scan(&file_data)
	return file_data, err
}

const getVerificationDocumentMetaByID = `-- name: GetVerificationDocumentMetaByID :one
SELECT id, user_id, file_name, mime_type, uploaded_at, created_at, updated_at
FROM verification_documents
WHERE id = $1
`

type GetVerificationDocumentMetaByIDRow struct {
	ID         uuid.UUID `db:"id" json:"id"`
	UserID     uuid.UUID `db:"user_id" json:"user_id"`
	FileName   string    `db:"file_name" json:"file_name"`
	MimeType   string    `db:"mime_type" json:"mime_type"`
	UploadedAt time.Time `db:"uploaded_at" json:"uploaded_at"`
	CreatedAt  time.Time `db:"created_at" json:"created_at"`
	UpdatedAt  time.Time `db:"updated_at" json:"updated_at"`
}

func (q *Queries) GetVerificationDocumentMetaByID(ctx context.Context, id uuid.UUID) (GetVerificationDocumentMetaByIDRow, error) {
	row := q.db.QueryRow(ctx, getVerificationDocumentMetaByID, id)
	var i GetVerificationDocumentMetaByIDRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.FileName,
		&i.MimeType,
		&i.UploadedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const listFullPendingVerificationRequests = `-- name: ListFullPendingVerificationRequests :many
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    u.email as user_email,
    u.display_name as user_display_name
    -- Specific type details are not included here for brevity in a list view.
    -- Client can call GetFullVerificationDetails for a specific request if needed.
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
LEFT JOIN users u ON uvr.user_id = u.id
WHERE uvr.status = 'pending'
ORDER BY uvr.submitted_at ASC
`

type ListFullPendingVerificationRequestsRow struct {
	RequestID          uuid.UUID              `db:"request_id" json:"request_id"`
	UserID             uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType   VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status             VerificationStatusEnum `db:"status" json:"status"`
	DocumentID         *uuid.UUID             `db:"document_id" json:"document_id"`
	DocumentID2        *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
	SubmittedAt        time.Time              `db:"submitted_at" json:"submitted_at"`
	ReviewedAt         *time.Time             `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID   *uuid.UUID             `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes         *string                `db:"admin_notes" json:"admin_notes"`
	RequestCreatedAt   time.Time              `db:"request_created_at" json:"request_created_at"`
	RequestUpdatedAt   time.Time              `db:"request_updated_at" json:"request_updated_at"`
	FileName           *string                `db:"file_name" json:"file_name"`
	MimeType           *string                `db:"mime_type" json:"mime_type"`
	DocumentUploadedAt *time.Time             `db:"document_uploaded_at" json:"document_uploaded_at"`
	UserEmail          *string                `db:"user_email" json:"user_email"`
	UserDisplayName    *string                `db:"user_display_name" json:"user_display_name"`
}

func (q *Queries) ListFullPendingVerificationRequests(ctx context.Context) ([]ListFullPendingVerificationRequestsRow, error) {
	rows, err := q.db.Query(ctx, listFullPendingVerificationRequests)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListFullPendingVerificationRequestsRow{}
	for rows.Next() {
		var i ListFullPendingVerificationRequestsRow
		if err := rows.Scan(
			&i.RequestID,
			&i.UserID,
			&i.VerificationType,
			&i.Status,
			&i.DocumentID,
			&i.DocumentID2,
			&i.SubmittedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.AdminNotes,
			&i.RequestCreatedAt,
			&i.RequestUpdatedAt,
			&i.FileName,
			&i.MimeType,
			&i.DocumentUploadedAt,
			&i.UserEmail,
			&i.UserDisplayName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listFullUserVerificationRequestsByUserID = `-- name: ListFullUserVerificationRequestsByUserID :many
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    vd2.file_name as file_name_2,
    vd2.mime_type as mime_type_2,
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.email as reviewer_email,
    reviewer.display_name as reviewer_display_name
    -- Specific type details are not included here for brevity in a list view.
    -- Client can call GetFullVerificationDetails for a specific request if needed.
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
LEFT JOIN verification_documents vd2 ON uvr.document_id_2 = vd2.id
LEFT JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id
WHERE uvr.user_id = $1
ORDER BY uvr.submitted_at DESC
`

type ListFullUserVerificationRequestsByUserIDRow struct {
	RequestID           uuid.UUID              `db:"request_id" json:"request_id"`
	UserID              uuid.UUID              `db:"user_id" json:"user_id"`
	VerificationType    VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
	Status              VerificationStatusEnum `db:"status" json:"status"`
	DocumentID          *uuid.UUID             `db:"document_id" json:"document_id"`
	DocumentID2         *uuid.UUID             `db:"document_id_2" json:"document_id_2"`
	SubmittedAt         time.Time              `db:"submitted_at" json:"submitted_at"`
	ReviewedAt          *time.Time             `db:"reviewed_at" json:"reviewed_at"`
	ReviewedByUserID    *uuid.UUID             `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes          *string                `db:"admin_notes" json:"admin_notes"`
	RequestCreatedAt    time.Time              `db:"request_created_at" json:"request_created_at"`
	RequestUpdatedAt    time.Time              `db:"request_updated_at" json:"request_updated_at"`
	FileName            *string                `db:"file_name" json:"file_name"`
	MimeType            *string                `db:"mime_type" json:"mime_type"`
	DocumentUploadedAt  *time.Time             `db:"document_uploaded_at" json:"document_uploaded_at"`
	FileName2           *string                `db:"file_name_2" json:"file_name_2"`
	MimeType2           *string                `db:"mime_type_2" json:"mime_type_2"`
	UserEmail           *string                `db:"user_email" json:"user_email"`
	UserDisplayName     *string                `db:"user_display_name" json:"user_display_name"`
	ReviewerEmail       *string                `db:"reviewer_email" json:"reviewer_email"`
	ReviewerDisplayName *string                `db:"reviewer_display_name" json:"reviewer_display_name"`
}

func (q *Queries) ListFullUserVerificationRequestsByUserID(ctx context.Context, userID uuid.UUID) ([]ListFullUserVerificationRequestsByUserIDRow, error) {
	rows, err := q.db.Query(ctx, listFullUserVerificationRequestsByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListFullUserVerificationRequestsByUserIDRow{}
	for rows.Next() {
		var i ListFullUserVerificationRequestsByUserIDRow
		if err := rows.Scan(
			&i.RequestID,
			&i.UserID,
			&i.VerificationType,
			&i.Status,
			&i.DocumentID,
			&i.DocumentID2,
			&i.SubmittedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.AdminNotes,
			&i.RequestCreatedAt,
			&i.RequestUpdatedAt,
			&i.FileName,
			&i.MimeType,
			&i.DocumentUploadedAt,
			&i.FileName2,
			&i.MimeType2,
			&i.UserEmail,
			&i.UserDisplayName,
			&i.ReviewerEmail,
			&i.ReviewerDisplayName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPendingVerificationRequests = `-- name: ListPendingVerificationRequests :many
SELECT uvr.id, uvr.user_id, uvr.verification_type, uvr.status, uvr.document_id, uvr.submitted_at, uvr.reviewed_at, uvr.reviewed_by_user_id, uvr.admin_notes, uvr.created_at, uvr.updated_at, uvr.document_id_2, uvr.document_id_2, u.id, u.display_name, u.hashed_password, u.profile_picture_url, u.phone, u.phone_verified_at, u.email, u.email_verified_at, u.phone_otp_channel, u.interface_language, u.communication_language, u.enable_app_notifications, u.enable_whatsapp_notifications, u.enable_sms_notifications, u.enable_email_notifications, u.created_at, u.updated_at, u.role, vd.id, vd.user_id, vd.file_name, vd.mime_type, vd.file_data, vd.uploaded_at, vd.created_at, vd.updated_at
FROM user_verification_requests uvr
JOIN users u ON uvr.user_id = u.id
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
WHERE uvr.status = 'pending'
ORDER BY uvr.submitted_at ASC
`

type ListPendingVerificationRequestsRow struct {
	UserVerificationRequest UserVerificationRequest `db:"user_verification_request" json:"user_verification_request"`
	DocumentID2             *uuid.UUID              `db:"document_id_2" json:"document_id_2"`
	User                    User                    `db:"user" json:"user"`
	VerificationDocument    VerificationDocument    `db:"verification_document" json:"verification_document"`
}

func (q *Queries) ListPendingVerificationRequests(ctx context.Context) ([]ListPendingVerificationRequestsRow, error) {
	rows, err := q.db.Query(ctx, listPendingVerificationRequests)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPendingVerificationRequestsRow{}
	for rows.Next() {
		var i ListPendingVerificationRequestsRow
		if err := rows.Scan(
			&i.UserVerificationRequest.ID,
			&i.UserVerificationRequest.UserID,
			&i.UserVerificationRequest.VerificationType,
			&i.UserVerificationRequest.Status,
			&i.UserVerificationRequest.DocumentID,
			&i.UserVerificationRequest.SubmittedAt,
			&i.UserVerificationRequest.ReviewedAt,
			&i.UserVerificationRequest.ReviewedByUserID,
			&i.UserVerificationRequest.AdminNotes,
			&i.UserVerificationRequest.CreatedAt,
			&i.UserVerificationRequest.UpdatedAt,
			&i.UserVerificationRequest.DocumentID2,
			&i.DocumentID2,
			&i.User.ID,
			&i.User.DisplayName,
			&i.User.HashedPassword,
			&i.User.ProfilePictureUrl,
			&i.User.Phone,
			&i.User.PhoneVerifiedAt,
			&i.User.Email,
			&i.User.EmailVerifiedAt,
			&i.User.PhoneOtpChannel,
			&i.User.InterfaceLanguage,
			&i.User.CommunicationLanguage,
			&i.User.EnableAppNotifications,
			&i.User.EnableWhatsappNotifications,
			&i.User.EnableSmsNotifications,
			&i.User.EnableEmailNotifications,
			&i.User.CreatedAt,
			&i.User.UpdatedAt,
			&i.User.Role,
			&i.VerificationDocument.ID,
			&i.VerificationDocument.UserID,
			&i.VerificationDocument.FileName,
			&i.VerificationDocument.MimeType,
			&i.VerificationDocument.FileData,
			&i.VerificationDocument.UploadedAt,
			&i.VerificationDocument.CreatedAt,
			&i.VerificationDocument.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserVerificationRequestsByUserID = `-- name: ListUserVerificationRequestsByUserID :many
SELECT uvr.id, uvr.user_id, uvr.verification_type, uvr.status, uvr.document_id, uvr.submitted_at, uvr.reviewed_at, uvr.reviewed_by_user_id, uvr.admin_notes, uvr.created_at, uvr.updated_at, uvr.document_id_2, uvr.document_id_2, vd.id, vd.user_id, vd.file_name, vd.mime_type, vd.file_data, vd.uploaded_at, vd.created_at, vd.updated_at
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
WHERE uvr.user_id = $1
ORDER BY uvr.submitted_at DESC
`

type ListUserVerificationRequestsByUserIDRow struct {
	UserVerificationRequest UserVerificationRequest `db:"user_verification_request" json:"user_verification_request"`
	DocumentID2             *uuid.UUID              `db:"document_id_2" json:"document_id_2"`
	VerificationDocument    VerificationDocument    `db:"verification_document" json:"verification_document"`
}

func (q *Queries) ListUserVerificationRequestsByUserID(ctx context.Context, userID uuid.UUID) ([]ListUserVerificationRequestsByUserIDRow, error) {
	rows, err := q.db.Query(ctx, listUserVerificationRequestsByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListUserVerificationRequestsByUserIDRow{}
	for rows.Next() {
		var i ListUserVerificationRequestsByUserIDRow
		if err := rows.Scan(
			&i.UserVerificationRequest.ID,
			&i.UserVerificationRequest.UserID,
			&i.UserVerificationRequest.VerificationType,
			&i.UserVerificationRequest.Status,
			&i.UserVerificationRequest.DocumentID,
			&i.UserVerificationRequest.SubmittedAt,
			&i.UserVerificationRequest.ReviewedAt,
			&i.UserVerificationRequest.ReviewedByUserID,
			&i.UserVerificationRequest.AdminNotes,
			&i.UserVerificationRequest.CreatedAt,
			&i.UserVerificationRequest.UpdatedAt,
			&i.UserVerificationRequest.DocumentID2,
			&i.DocumentID2,
			&i.VerificationDocument.ID,
			&i.VerificationDocument.UserID,
			&i.VerificationDocument.FileName,
			&i.VerificationDocument.MimeType,
			&i.VerificationDocument.FileData,
			&i.VerificationDocument.UploadedAt,
			&i.VerificationDocument.CreatedAt,
			&i.VerificationDocument.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserVerificationsByStatus = `-- name: ListUserVerificationsByStatus :many
SELECT id, user_id, verification_type, status, document_id, submitted_at, reviewed_at, reviewed_by_user_id, admin_notes, created_at, updated_at, document_id_2 FROM user_verification_requests
WHERE user_id = $1 AND status = $2
ORDER BY submitted_at DESC
`

type ListUserVerificationsByStatusParams struct {
	UserID uuid.UUID              `db:"user_id" json:"user_id"`
	Status VerificationStatusEnum `db:"status" json:"status"`
}

func (q *Queries) ListUserVerificationsByStatus(ctx context.Context, arg ListUserVerificationsByStatusParams) ([]UserVerificationRequest, error) {
	rows, err := q.db.Query(ctx, listUserVerificationsByStatus, arg.UserID, arg.Status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []UserVerificationRequest{}
	for rows.Next() {
		var i UserVerificationRequest
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.VerificationType,
			&i.Status,
			&i.DocumentID,
			&i.SubmittedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.AdminNotes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DocumentID2,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserVerificationsByStatusAndType = `-- name: ListUserVerificationsByStatusAndType :many
SELECT id, user_id, verification_type, status, document_id, submitted_at, reviewed_at, reviewed_by_user_id, admin_notes, created_at, updated_at, document_id_2 FROM user_verification_requests
WHERE user_id = $1 AND status = $2 AND verification_type = $3
ORDER BY submitted_at DESC
`

type ListUserVerificationsByStatusAndTypeParams struct {
	UserID           uuid.UUID              `db:"user_id" json:"user_id"`
	Status           VerificationStatusEnum `db:"status" json:"status"`
	VerificationType VerificationTypeEnum   `db:"verification_type" json:"verification_type"`
}

func (q *Queries) ListUserVerificationsByStatusAndType(ctx context.Context, arg ListUserVerificationsByStatusAndTypeParams) ([]UserVerificationRequest, error) {
	rows, err := q.db.Query(ctx, listUserVerificationsByStatusAndType, arg.UserID, arg.Status, arg.VerificationType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []UserVerificationRequest{}
	for rows.Next() {
		var i UserVerificationRequest
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.VerificationType,
			&i.Status,
			&i.DocumentID,
			&i.SubmittedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.AdminNotes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DocumentID2,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserVerificationsByType = `-- name: ListUserVerificationsByType :many
SELECT id, user_id, verification_type, status, document_id, submitted_at, reviewed_at, reviewed_by_user_id, admin_notes, created_at, updated_at, document_id_2 FROM user_verification_requests
WHERE user_id = $1 AND verification_type = $2
ORDER BY submitted_at DESC
`

type ListUserVerificationsByTypeParams struct {
	UserID           uuid.UUID            `db:"user_id" json:"user_id"`
	VerificationType VerificationTypeEnum `db:"verification_type" json:"verification_type"`
}

func (q *Queries) ListUserVerificationsByType(ctx context.Context, arg ListUserVerificationsByTypeParams) ([]UserVerificationRequest, error) {
	rows, err := q.db.Query(ctx, listUserVerificationsByType, arg.UserID, arg.VerificationType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []UserVerificationRequest{}
	for rows.Next() {
		var i UserVerificationRequest
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.VerificationType,
			&i.Status,
			&i.DocumentID,
			&i.SubmittedAt,
			&i.ReviewedAt,
			&i.ReviewedByUserID,
			&i.AdminNotes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DocumentID2,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateUserVerificationRequestStatus = `-- name: UpdateUserVerificationRequestStatus :one
UPDATE user_verification_requests
SET
    status = $2,
    reviewed_at = now(),
    reviewed_by_user_id = $3,
    admin_notes = $4
WHERE id = $1
RETURNING id, user_id, verification_type, status, document_id, submitted_at, reviewed_at, reviewed_by_user_id, admin_notes, created_at, updated_at, document_id_2
`

type UpdateUserVerificationRequestStatusParams struct {
	ID               uuid.UUID              `db:"id" json:"id"`
	Status           VerificationStatusEnum `db:"status" json:"status"`
	ReviewedByUserID *uuid.UUID             `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
	AdminNotes       *string                `db:"admin_notes" json:"admin_notes"`
}

func (q *Queries) UpdateUserVerificationRequestStatus(ctx context.Context, arg UpdateUserVerificationRequestStatusParams) (UserVerificationRequest, error) {
	row := q.db.QueryRow(ctx, updateUserVerificationRequestStatus,
		arg.ID,
		arg.Status,
		arg.ReviewedByUserID,
		arg.AdminNotes,
	)
	var i UserVerificationRequest
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VerificationType,
		&i.Status,
		&i.DocumentID,
		&i.SubmittedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DocumentID2,
	)
	return i, err
}

const userDeleteVerificationData = `-- name: UserDeleteVerificationData :one
UPDATE user_verification_requests
SET
    status = 'data_deleted_by_user',
    document_id = NULL, -- Nullify the link to the document
    document_id_2 = NULL, -- Also nullify the link to the second document
    updated_at = now()
WHERE id = $1 AND user_id = $2
RETURNING id, user_id, verification_type, status, document_id, submitted_at, reviewed_at, reviewed_by_user_id, admin_notes, created_at, updated_at, document_id_2
`

type UserDeleteVerificationDataParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	UserID uuid.UUID `db:"user_id" json:"user_id"`
}

func (q *Queries) UserDeleteVerificationData(ctx context.Context, arg UserDeleteVerificationDataParams) (UserVerificationRequest, error) {
	row := q.db.QueryRow(ctx, userDeleteVerificationData, arg.ID, arg.UserID)
	var i UserVerificationRequest
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VerificationType,
		&i.Status,
		&i.DocumentID,
		&i.SubmittedAt,
		&i.ReviewedAt,
		&i.ReviewedByUserID,
		&i.AdminNotes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DocumentID2,
	)
	return i, err
}
