DO $$
DECLARE
    superadmin_user_id uuid;
    org_record record;
BEGIN
    -- Get the superadmin user ID
    SELECT id INTO superadmin_user_id FROM users WHERE email = '<EMAIL>';

    IF superadmin_user_id IS NULL THEN
        RAISE EXCEPTION 'Superadmin user (<EMAIL>) not found.';
    END IF;

    -- Loop through all organizations
    FOR org_record IN SELECT id FROM organizations
    LOOP
        -- Add superadmin as an admin member to the organization if not already a member
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (superadmin_user_id, org_record.id, 'admin', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END LOOP;
END $$; 