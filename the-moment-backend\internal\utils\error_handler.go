package utils

import (
	"errors"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// ErrorResponse defines the structure for a JSON error response.
// @Description Represents a standard error format returned by the API.
type ErrorResponse struct {
	Error   string `json:"error" example:"Not Found"`                      // A short, general error message or code (often corresponds to HTTP status text).
	Message string `json:"message,omitempty" example:"Resource not found"` // A more detailed, human-readable message about the error.
	Details string `json:"details,omitempty" example:"error details here"` // Optional technical details or underlying error string (usually omitted for 5xx errors).
}

// Predefined errors
var (
	ErrNotFound             = errors.New("resource not found")
	ErrUnauthorized         = errors.New("unauthorized")
	ErrForbidden            = errors.New("forbidden")
	ErrBadRequest           = errors.New("bad request")
	ErrConflict             = errors.New("resource conflict") // e.g., item already exists
	ErrInvalidRefreshToken  = errors.New("invalid refresh token")
	ErrInvalidPaymentStatus = errors.New("invalid payment status")
)

// HandleError logs the error and sends a JSON error response.
func HandleError(c echo.Context, statusCode int, publicMessage string, internalError error) error {
	response := ErrorResponse{
		Error: http.StatusText(statusCode),
	}

	if publicMessage != "" {
		response.Message = publicMessage
	}

	// Log the internal error with more details
	log.Ctx(c.Request().Context()).Error().
		Err(internalError).
		Int("status_code", statusCode).
		Str("public_message", publicMessage).
		Str("path", c.Request().URL.Path).
		Msg("API Error")

	// For client-facing errors (4xx), we might include more details if safe.
	// For server errors (5xx), we generally avoid sending internalError details to the client.
	if statusCode >= 400 && statusCode < 500 && internalError != nil {
		// Consider if internalError.Error() is safe to expose.
		// For validation errors, it often is, but be cautious with other errors.
		// response.Details = internalError.Error() // Potentially expose more detail for client errors
	}

	return c.JSON(statusCode, response)
}
