import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import eslint from 'vite-plugin-eslint';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
    plugins: [
        react(),
        tailwindcss(),
        eslint({ exclude: ['**/node_modules/**', '**/fonts/**'] })
    ],
    server: {
        proxy: {
            '/api': 'https://the-moment-api.haytech.io/',
            '/uploads': 'https://the-moment-api.haytech.io/'
        }
    }
});