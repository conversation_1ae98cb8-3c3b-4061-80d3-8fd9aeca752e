package locales

import (
	"bytes"
	"fmt"
	"os"
	"strings"
	"text/template"
)

const (
	LangEN   = "en"
	LangZHHK = "zh_HK"
	LangZHCN = "zh_CN"
)

// MessageKey defines a structure for accessing a localized message
type Message<PERSON>ey struct {
	Type string // e.g., NotificationTypeVerificationApproved
	Lang string // e.g., <PERSON><PERSON>
}

// messages will store the translations.
// The structure is map[MessageKey]map[string]string
// Outer map key: MessageKey{Type: "notification_type", Lang: "language_code"}
// Inner map key: "subject", "body_whatsapp", "body_sms", "body_websocket"
// Inner map value: "translated message template"
var messages = map[MessageKey]map[string]string{
	// Example for verification_approved (to be expanded)
	{Type: "verification_approved", Lang: LangEN}: {
		"subject":        "Your {{.VerificationType}} has been approved!",
		"body_whatsapp":  "Hi {{.UserName}}, your {{.VerificationType}} ({{.DocumentDetails}}) has been approved. Admin notes: {{.AdminNotes}}. View details: {{.DetailsURL}}",
		"body_sms":       "Your {{.VerificationType}} has been approved. Notes: {{.AdminNotes}}.",
		"body_websocket": "Your {{.VerificationType}} ({{.DocumentDetails}}) has been approved. Admin notes: {{.AdminNotes}}.",
	},
	{Type: "verification_approved", Lang: LangZHHK}: {
		"subject":        "您的 {{.VerificationType}} 已獲批准！",
		"body_whatsapp":  "你好 {{.UserName}}，你的 {{.VerificationType}} ({{.DocumentDetails}}) 已獲批准。管理員備註：{{.AdminNotes}}。查看詳情：{{.DetailsURL}}",
		"body_sms":       "您的 {{.VerificationType}} 已獲批准。備註：{{.AdminNotes}}。",
		"body_websocket": "您的 {{.VerificationType}} ({{.DocumentDetails}}) 已獲批准。管理員備註：{{.AdminNotes}}。",
	},
	{Type: "verification_approved", Lang: LangZHCN}: {
		"subject":        "您的 {{.VerificationType}} 已获批准！", // Simplified Chinese
		"body_whatsapp":  "你好 {{.UserName}}，你的 {{.VerificationType}} ({{.DocumentDetails}}) 已获批准。管理员备注：{{.AdminNotes}}。查看详情：{{.DetailsURL}}",
		"body_sms":       "您的 {{.VerificationType}} 已获批准。备注：{{.AdminNotes}}。",
		"body_websocket": "您的 {{.VerificationType}} ({{.DocumentDetails}}) 已获批准。管理员备注：{{.AdminNotes}}。",
	},

	// Volunteer Application Approved
	{Type: "volunteer_application_approved", Lang: LangEN}: {
		"subject":        "Your volunteer application for {{.EventName}} has been approved!",
		"body_whatsapp":  "Hi {{.UserName}}, your volunteer application for the event '{{.EventName}}' has been approved. Event details: {{.EventDetailsURL}}",
		"body_sms":       "Your volunteer application for {{.EventName}} approved. Details: {{.EventDetailsURL}}",
		"body_websocket": "Your volunteer application for '{{.EventName}}' has been approved. Event details: {{.EventDetailsURL}}",
	},
	{Type: "volunteer_application_approved", Lang: LangZHHK}: {
		"subject":        "您於 {{.EventName}} 的義工申請已獲批准！",
		"body_whatsapp":  "你好 {{.UserName}}，你於活動 '{{.EventName}}' 的義工申請已獲批准。活動詳情：{{.EventDetailsURL}}",
		"body_sms":       "您於 {{.EventName}} 的義工申請已獲批准。詳情：{{.EventDetailsURL}}",
		"body_websocket": "您於活動 '{{.EventName}}' 的義工申請已獲批准。活動詳情：{{.EventDetailsURL}}",
	},
	{Type: "volunteer_application_approved", Lang: LangZHCN}: {
		"subject":        "您于 {{.EventName}} 的义工申请已获批准！",
		"body_whatsapp":  "你好 {{.UserName}}，你于活动 '{{.EventName}}' 的义工申请已获批准。活动详情：{{.EventDetailsURL}}",
		"body_sms":       "您于 {{.EventName}} 的义工申请已获批准。详情：{{.EventDetailsURL}}",
		"body_websocket": "您于活动 '{{.EventName}}' 的义工申请已获批准。活动详情：{{.EventDetailsURL}}",
	},

	// Event Registration from Waitlist
	{Type: "event_registration_from_waitlist", Lang: LangEN}: {
		"subject":        "You are now registered for {{.EventName}}!",
		"body_whatsapp":  "Hi {{.UserName}}, good news! A spot opened up and you are now confirmed for the event '{{.EventName}}'. Event details: {{.EventDetailsURL}}",
		"body_sms":       "You are now registered for {{.EventName}} from the waitlist. Details: {{.EventDetailsURL}}",
		"body_websocket": "You have been moved from the waitlist and are now registered for '{{.EventName}}'. Event details: {{.EventDetailsURL}}",
	},
	{Type: "event_registration_from_waitlist", Lang: LangZHHK}: {
		"subject":        "您現在已成功報名 {{.EventName}}！",
		"body_whatsapp":  "你好 {{.UserName}}，好消息！活動 '{{.EventName}}' 現有空位，您已從等候名單中確認出席。活動詳情：{{.EventDetailsURL}}",
		"body_sms":       "您已從 {{.EventName}} 的等候名單中成功報名。詳情：{{.EventDetailsURL}}",
		"body_websocket": "您已從等候名單中移出，並成功報名活動 '{{.EventName}}'。活動詳情：{{.EventDetailsURL}}",
	},
	{Type: "event_registration_from_waitlist", Lang: LangZHCN}: {
		"subject":        "您现在已成功报名 {{.EventName}}！",
		"body_whatsapp":  "你好 {{.UserName}}，好消息！活动 '{{.EventName}}' 现有空位，您已从等候名单中确认出席。活动详情：{{.EventDetailsURL}}",
		"body_sms":       "您已从 {{.EventName}} 的等候名单中成功报名。详情：{{.EventDetailsURL}}",
		"body_websocket": "您已从等候名单中移出，并成功报名活动 '{{.EventName}}'。活动详情：{{.EventDetailsURL}}",
	},

	// Event Admin Registration Update
	{Type: "event_admin_registration_update", Lang: LangEN}: {
		"subject":        "Update on your registration for {{.EventName}}",
		"body_whatsapp":  "Hi {{.UserName}}, your registration status for '{{.EventName}}' has been updated to '{{.NewStatus}}' by an administrator. Admin notes: {{.AdminNotes}}. Event details: {{.EventDetailsURL}}",
		"body_sms":       "Reg for {{.EventName}} updated to {{.NewStatus}} by admin. Notes: {{.AdminNotes}}. Details: {{.EventDetailsURL}}",
		"body_websocket": "Your registration status for '{{.EventName}}' has been updated to '{{.NewStatus}}' by an administrator. Admin notes: {{.AdminNotes}}. Event details: {{.EventDetailsURL}}",
	},
	{Type: "event_admin_registration_update", Lang: LangZHHK}: {
		"subject":        "有關您於 {{.EventName}} 報名狀態的更新",
		"body_whatsapp":  "你好 {{.UserName}}，管理員已將您於 '{{.EventName}}' 的報名狀態更新為 '{{.NewStatus}}'。管理員備註：{{.AdminNotes}}。活動詳情：{{.EventDetailsURL}}",
		"body_sms":       "管理員已將您於 {{.EventName}} 的報名狀態更新為 {{.NewStatus}}。備註：{{.AdminNotes}}。詳情：{{.EventDetailsURL}}",
		"body_websocket": "管理員已將您於 '{{.EventName}}' 的報名狀態更新為 '{{.NewStatus}}'。管理員備註：{{.AdminNotes}}。活動詳情：{{.EventDetailsURL}}",
	},
	{Type: "event_admin_registration_update", Lang: LangZHCN}: {
		"subject":        "有关您于 {{.EventName}} 报名状态的更新",
		"body_whatsapp":  "你好 {{.UserName}}，管理员已将您于 '{{.EventName}}' 的报名状态更新为 '{{.NewStatus}}'。管理员备注：{{.AdminNotes}}。活动详情：{{.EventDetailsURL}}",
		"body_sms":       "管理员已将您于 {{.EventName}} 的报名状态更新为 {{.NewStatus}}。备注：{{.AdminNotes}}。详情：{{.EventDetailsURL}}",
		"body_websocket": "管理员已将您于 '{{.EventName}}' 的报名状态更新为 '{{.NewStatus}}'。管理员备注：{{.AdminNotes}}。活动详情：{{.EventDetailsURL}}",
	},

	// Event Reminder 1 Day
	{Type: "event_reminder_1_day", Lang: LangEN}: {
		"subject":        "Reminder: {{.EventName}} is tomorrow!",
		"body_whatsapp":  "Hi {{.UserName}}, just a reminder that '{{.EventName}}' is happening tomorrow, {{.EventDate}} at {{.EventTime}}. Location: {{.EventLocation}}. See you there! Details: {{.EventDetailsURL}}",
		"body_sms":       "Reminder: {{.EventName}} is tomorrow, {{.EventDate}} {{.EventTime}} at {{.EventLocation}}. Details: {{.EventDetailsURL}}",
		"body_websocket": "Reminder: '{{.EventName}}' is tomorrow, {{.EventDate}} at {{.EventTime}}. Location: {{.EventLocation}}. Event details: {{.EventDetailsURL}}",
	},
	{Type: "event_reminder_1_day", Lang: LangZHHK}: {
		"subject":        "溫馨提示：{{.EventName}} 將於明天舉行！",
		"body_whatsapp":  "你好 {{.UserName}}，溫馨提示，'{{.EventName}}' 將於明天 {{.EventDate}} {{.EventTime}} 於 {{.EventLocation}} 舉行。期待與你見面！詳情：{{.EventDetailsURL}}",
		"body_sms":       "溫馨提示：{{.EventName}} 將於明天 {{.EventDate}} {{.EventTime}} 於 {{.EventLocation}} 舉行。詳情：{{.EventDetailsURL}}",
		"body_websocket": "溫馨提示：'{{.EventName}}' 將於明天 {{.EventDate}} {{.EventTime}} 於 {{.EventLocation}} 舉行。活動詳情：{{.EventDetailsURL}}",
	},
	{Type: "event_reminder_1_day", Lang: LangZHCN}: {
		"subject":        "温馨提示：{{.EventName}} 将于明天举行！",
		"body_whatsapp":  "你好 {{.UserName}}，温馨提示，'{{.EventName}}' 将于明天 {{.EventDate}} {{.EventTime}} 于 {{.EventLocation}} 举行。期待与你见面！详情：{{.EventDetailsURL}}",
		"body_sms":       "温馨提示：{{.EventName}} 将于明天 {{.EventDate}} {{.EventTime}} 于 {{.EventLocation}} 举行。详情：{{.EventDetailsURL}}",
		"body_websocket": "温馨提示：'{{.EventName}}' 将于明天 {{.EventDate}} {{.EventTime}} 于 {{.EventLocation}} 举行。活动详情：{{.EventDetailsURL}}",
	},
	// Add other notification types and their translations here
}

// interpolate applies the data to the template string.
func interpolate(templateString string, data map[string]interface{}) (string, error) {
	tmpl, err := template.New("msg").Parse(templateString)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}
	return buf.String(), nil
}

// GetMessage retrieves a localized message part (subject, body_whatsapp, etc.) for a given notification type, language, and data.
// It handles fallback to English if the specified language or part is not found.
// It uses Go's text/template for placeholder substitution.
func GetMessage(messageType, lang, part string, data map[string]interface{}) (string, error) {
	key := MessageKey{Type: messageType, Lang: lang}
	if localizedMessages, ok := messages[key]; ok {
		if messageTemplate, ok := localizedMessages[part]; ok {
			return interpolate(messageTemplate, data)
		}
	}

	// Fallback to English if specific language or part not found
	key.Lang = LangEN
	if localizedMessages, ok := messages[key]; ok {
		if messageTemplate, ok := localizedMessages[part]; ok {
			// Log a warning that we are using fallback language?
			return interpolate(messageTemplate, data)
		}
	}

	return "", fmt.Errorf("message part '%s' for type '%s' not found in language '%s' or fallback '%s'", part, messageType, lang, LangEN)
}

// Add an init function or a public function to load more messages if they were in separate files.
// For now, all messages are hardcoded in the 'messages' map.

// It might be useful to also have a function to get all parts of a message for a given type and lang.
// type LocalizedMessageSet struct {
//    Subject       string
//    BodyWhatsapp  string
//    BodySMS       string
//    BodyWebsocket string
// }
// func GetMessageSet(messageType, lang string, data map[string]interface{}) (LocalizedMessageSet, error) { ... }

// GetTwilioTemplateSID retrieves the Twilio Template SID for a given notification type and language
// from environment variables.
// Environment variables should be named like: TWILIO_TEMPLATE_SID_MESSAGETYPE_LANG
// e.g., TWILIO_TEMPLATE_SID_VERIFICATION_APPROVED_EN
// It returns the SID and a boolean indicating if it was found (non-empty).
func GetTwilioTemplateSID(messageType, lang string) (string, bool) {
	envVarName := fmt.Sprintf("TWILIO_TEMPLATE_SID_%s_%s",
		strings.ToUpper(strings.ReplaceAll(messageType, "-", "_")),
		strings.ToUpper(strings.ReplaceAll(lang, "-", "_")),
	)
	sid := os.Getenv(envVarName)
	if sid == "" {
		// Log that the environment variable was not found or is empty
		// Consider using a structured logger if available throughout the project
		fmt.Printf("Warning: Twilio Template SID environment variable not found or empty: %s\n", envVarName)
		return "", false
	}
	return sid, true
}
