import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

// Helper to map JS-style camelCase params to API-style snake_case for query params
const mapToApiQueryParams = (params, mapping) => {
  const apiParams = {};
  if (params) {
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        const mappedKey = mapping[key] || key;
        apiParams[mappedKey] = params[key];
      }
    }
  }
  return apiParams;
};

// Normalize successful empty list responses
const normalizeEmptyListResponse = (response) => {
  // Priority 1: If response.data itself is an array from the API, wrap it.
  if (response && Array.isArray(response.data)) {
    return { ...response, data: { data: response.data, total: response.data.length } };
  }
  // Priority 2: Backend indicates success with no items by data: { data: null, total: 0 }
  // This case assumes the API might sometimes wrap an empty list in this structure.
  if (response && response.data && response.data.data === null && response.data.total === 0) {
    return { ...response, data: { ...response.data, data: [] } }; // Convert data: null to data: []
  }
  // Priority 3: Backend indicates success with no items by returning overall response.data as null (for a 200 OK)
  // This case also assumes the API might sometimes return null directly for an empty list.
  if (response && response.data === null) {
    // Preserve other response properties like status by spreading the original response
    return { ...response, data: { data: [], total: 0 } };
  }
  
  // If response.data is already in the expected format { data: [...], total: X }, 
  // or it's an error structure, or any other format not covered above, return as is.
  return response;
};

// No specific mappings defined yet for admin volunteer lists as api.md doesn't specify query params beyond PageRequest.
// If other specific params are added, mappings can be placed here.

export const volunteerService = {
  // User-facing volunteer application actions are primarily in other services.
  // This service focuses on admin-level volunteer management.

  // --- Admin-level Organization Volunteer Application Management ---

  getOrgVolunteerApplicationDetail: async (orgId, appId) => {
    // api.md GET /admin/organizations/:orgId/volunteer/applications/:appId
    try {
      const response = await api.get(API_ENDPOINTS.ADMIN.GET_ORG_VOLUNTEER_APPLICATION_DETAIL(orgId, appId));
      return response; // Expected: EnhancedVolunteerApplicationDetails
    } catch (error) {
      console.error(`Error fetching volunteer application detail ${appId} for org ${orgId}:`, error);
      throw error;
    }
  },

  listOrgVolunteerApplications: async (orgId, clientParams = {}) => {
    // API: GET /api/v1/admin/organizations/:orgId/volunteer/applications
    // This function lists general volunteer applications for an organization (e.g., for qualification review).
    // clientParams can include { status, limit, offset, etc. }
    const apiParams = mapToApiQueryParams(clientParams, {}); 
    try {
      // Constructing the path directly based on observed patterns for general org volunteer applications.
      const axiosResponse = await api.getWithFullResponse(API_ENDPOINTS.ADMIN.LIST_ORG_VOLUNTEER_APPLICATIONS(orgId), { params: apiParams });
      
      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;
      
      let applications = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.data)) {
        applications = responseData.data;
        total = parseInt(responseHeaders['x-total-count'], 10) || responseData.total || applications.length;
      } else if (Array.isArray(responseData)) {
        applications = responseData;
        total = parseInt(responseHeaders['x-total-count'], 10) || applications.length;
      } else if (responseData === null) {
        applications = [];
        total = 0;
      }
      
      return { applications, total };
    } catch (error) {
      console.error(`Error fetching organization volunteer applications for org ${orgId} with params ${JSON.stringify(apiParams)}:`, error);
      throw error;
    }
  },

  reviewOrgVolunteerApplication: async (orgId, appId, reviewData) => {
    // api.md PATCH /admin/organizations/:orgId/volunteer/applications/:appId/review
    // Payload: AdminReviewVolunteerApplicationRequest { Status `form:"status"`, AdminNotes `form:"admin_notes"` }
    // Sending as JSON, matching keys `status` and `admin_notes`.
    // reviewData should be { status: string, adminNotes: string }
    const payload = {
      status: reviewData.status, // e.g., 'approved', 'rejected'
      admin_notes: reviewData.adminNotes, // Ensure this key matches form tag if backend is strict
    };
    try {
      const response = await api.patch(API_ENDPOINTS.ADMIN.REVIEW_ORG_VOLUNTEER_APPLICATION(orgId, appId), payload);
      return response; // Expected: VolunteerApplicationResponse
    } catch (error) {
      console.error(`Error reviewing volunteer application ${appId} for org ${orgId}:`, error);
      throw error;
    }
  },
  
  // Note: Review of EVENT-SPECIFIC volunteer applications by an Org Manager (non-admin path)
  // is via API_ENDPOINTS.ORGANIZATIONS.REVIEW_EVENT_VOLUNTEER_APPLICATION(orgId, applicationId)
  // and would typically be in organizationService.js or eventService.js with payload ReviewVolunteerApplicationRequest.

  // --- Admin-level Organization QUALIFICATION Volunteer Application Management ---
  // The following functions were duplicates of the general ones above and used the same API endpoints.
  // They are removed to avoid duplication and ESLint errors.
  // listPendingQualificationVolunteerApplicationsForOrg: async (orgId, clientParams = {}) => { ... }
  // getOrgVolunteerApplicationDetail: async (orgId, appId) => { ... }
  // reviewOrgVolunteerApplication: async (orgId, appId, reviewData) => { ... }

  // --- EVENT-SPECIFIC Volunteer Application Management ---

  listEventVolunteerApplicationsForOrg: async (orgId, clientParams = {}) => {
    // API: GET /admin/organizations/{orgId}/event-volunteer-applications
    // clientParams should include { status: 'pending_review' || 'approved' || 'rejected', limit, offset }
    const apiParams = mapToApiQueryParams(clientParams, {}); 
    try {
      const responseData = await api.get(API_ENDPOINTS.ADMIN.LIST_EVENT_VOLUNTEER_APPLICATIONS(orgId), { params: apiParams });
      
      let applications = [];
      let total = 0;
      
      if (responseData && Array.isArray(responseData.data)) {
        applications = responseData.data;
        total = responseData.total || applications.length; 
      } else if (Array.isArray(responseData)) {
        applications = responseData;
        total = applications.length; 
      } else if (responseData === null) {
        applications = [];
        total = 0;
      }
      
      console.log("listEventVolunteerApplicationsForOrg response", { applications, total });
      return { applications, total };
    } catch (error) {
      console.error(`Error fetching EVENT volunteer applications for org ${orgId} with params ${JSON.stringify(apiParams)}:`, error);
      throw error;
    }
  },

  getEventVolunteerApplicationDetail: async (orgId, eventId, applicationId) => {
    // This function fetches details for a specific volunteer application to a specific event,
    // under an admin context for the organization.
    // console.warn(`getEventVolunteerApplicationDetail: Called for org ${orgId}, event ${eventId}, app ${applicationId}.`);
    try {
      // Using the newly defined specific endpoint for fetching event volunteer application details.
      const response = await api.get(API_ENDPOINTS.ADMIN.GET_ADMIN_EVENT_VOLUNTEER_APPLICATION_DETAIL(orgId, eventId, applicationId)); 
      return response;
    } catch (error) {
      console.error(`Error fetching EVENT volunteer application detail ${applicationId} for event ${eventId}, org ${orgId}:`, error);
      throw error;
    }
  },

  reviewEventVolunteerApplication: async (orgId, eventId, applicationId, reviewData) => {
    // API: PATCH /api/v1/organizations/:orgId/events/:eventId/volunteer-applications/:applicationId/review
    // Payload: ReviewVolunteerApplicationRequest { NewStatus string, AdminNotes *string }
    const payload = {
      new_status: reviewData.status, // "approved" or "rejected" 
      admin_notes: reviewData.adminNotes,
    };
    try {
      const response = await api.patch(API_ENDPOINTS.ORGANIZATIONS.REVIEW_EVENT_VOLUNTEER_APPLICATION(orgId, eventId, applicationId), payload);
      return response;
    } catch (error) {
      console.error(`Error reviewing EVENT volunteer application ${applicationId} for org ${orgId}, event ${eventId}:`, error);
      throw error;
    }
  },
  
  // Note: The functions listPendingEventVolunteerApplicationsSystemWide and listPendingEventVolunteerApplicationsForEvent were removed 
  // as the new LIST_EVENT_VOLUNTEER_APPLICATIONS under ADMIN scope for an orgId with status filter seems to cover the primary need for VolunteerApproval page.
  // If a true system-wide (no orgId) or specific event_id based list is needed elsewhere, they can be re-added with correct API mappings.

  // Original function from the file (seems to be a duplicate or an older version for qualifications?)
  // listOrgVolunteerApplications: async (orgId, clientParams = {}) => { ... }
  // This was present at the top of the provided file content. I'm keeping the ones under the specific headings.
  // If this top-level listOrgVolunteerApplications was intended for something different, it needs clarification.
};