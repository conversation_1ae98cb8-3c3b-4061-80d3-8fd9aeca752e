import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import { Button } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { whatsAppTheme, createTheme } from 'theme/index';
import {
    usePhoneCheck,
    useExistingPhoneOtpInitiate,
    useNewPhoneOtpInitiate,
} from '@/api/authentication_services';
import {
    PhoneCheckRequest,
    ExistingPhoneOtpInitiateRequest,
    NewPhoneOtpInitiateRequest,
} from '@/api/api_config';
import { generatePKCE, PKCECredentials } from '@/utils/pkce';

// Define a specific type for the custom theme properties expected
interface ScreenTheme {
  primary: string;
  primaryDark?: string; // Optional as not all themes might define this
  primaryDisabled: string;
  background: string;
  text: string;
  secondaryText: string;
  error: string; // Assuming error color is standard
}

interface OtpChannelVerificationScreenProps {
  channel: 'sms' | 'whatsapp';
  customScreenTheme?: ScreenTheme; // Keep this for backward compatibility but make it optional
  headerIconName: React.ComponentProps<typeof MaterialCommunityIcons>['name'];
  headerTitleKey: string;
  headerSubtitleKey: string;
  infoTextKey: string;
  buttonTextKey: string;
  screenTitleKey: string; // For Stack.Screen headerTitle
}

export default function OtpChannelVerificationScreen({
  channel,
  customScreenTheme,
  headerIconName,
  headerTitleKey,
  headerSubtitleKey,
  infoTextKey,
  buttonTextKey,
  screenTitleKey,
}: OtpChannelVerificationScreenProps) {
  const router = useRouter();
  const { t } = useTranslation();
  const storeTheme = appStyleStore(state => state.theme);

  // API Hooks mutations
  const { mutateAsync: checkPhoneMutation } = usePhoneCheck();
  const { mutateAsync: initiateOtpLoginMutation } = useExistingPhoneOtpInitiate();
  const { mutateAsync: initiateRegistrationMutation } = useNewPhoneOtpInitiate();

  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const phoneInputRef = useRef<TextInput>(null);

  // 根据渠道选择主题，优先级：
  // 1. 显式传入的customScreenTheme（向后兼容）
  // 2. 当channel为whatsapp时使用whatsAppTheme
  // 3. 否则使用全局主题
  let effectiveTheme: ScreenTheme;
  
  if (customScreenTheme) {
    // 使用传入的自定义主题（向后兼容）
    effectiveTheme = customScreenTheme;
  } else if (channel === 'whatsapp') {
    // 使用WhatsApp主题
    effectiveTheme = {
      primary: whatsAppTheme.colors.primary,
      primaryDark: whatsAppTheme.colors.secondary, // 使用secondary作为primaryDark
      primaryDisabled: whatsAppTheme.colors.primaryDisabled,
      background: whatsAppTheme.colors.background,
      text: whatsAppTheme.system.text,
      secondaryText: whatsAppTheme.system.secondaryText,
      error: whatsAppTheme.colors.error
    };
  } else {
    // 使用全局主题
    const currentGlobalTheme = storeTheme || createTheme('red'); // Fallback for store theme
    effectiveTheme = {
      primary: currentGlobalTheme.colors.primary,
      primaryDark: currentGlobalTheme.colors.primary, // Fallback for primaryDark
      primaryDisabled: currentGlobalTheme.colors.primaryDisabled,
      background: currentGlobalTheme.system.background,
      text: currentGlobalTheme.system.text,
      secondaryText: currentGlobalTheme.system.secondaryText,
      error: currentGlobalTheme.colors.error,
    };
  }

  const formatPhoneNumber = (input: string) => {
    const numbers = input.replace(/\D/g, '');
    return numbers.length <= 4 ? numbers : `${numbers.slice(0, 4)} ${numbers.slice(4, 8)}`;
  };

  const handlePhoneNumberChange = (text: string) => {
    setError(null);
    const formatted = formatPhoneNumber(text);
    if (formatted.length <= 9) { // Max 8 digits + 1 space
      setPhoneNumber(formatted);
    }
  };

  const getErrorKeyFromStatus = (status?: number, error?: string) => {
    // Handle specific server error messages first
    if (error) {
      // Phone check errors
      if (error.includes('Phone number is required')) return 'PHONE_REQUIRED';
      if (error.includes('Invalid request payload')) return 'INVALID_REQUEST';
      
      // OTP initiate errors
      if (error.includes('Client ID is required')) return 'INVALID_REQUEST';
      if (error.includes('Code challenge is required')) return 'INVALID_REQUEST';
      if (error.includes('State is required')) return 'INVALID_REQUEST';
      if (error.includes('Code challenge method must be S256')) return 'INVALID_REQUEST';
      if (error.includes('User with this phone number not found')) return 'USER_NOT_FOUND';
      if (error.includes('Failed to check phone number')) return 'PHONE_CHECK_FAILED';
      if (error.includes('Failed to initiate login flow')) return 'LOGIN_INITIATE_FAILED';
      if (error.includes('Failed to initiate registration flow')) return 'REGISTRATION_INITIATE_FAILED';
      if (error.includes('Failed to send OTP')) return 'OTP_SEND_FAILED';
      
      // Twilio specific errors
      if (error.includes('Twilio client not initialized')) return 'SERVICE_UNAVAILABLE';
      if (error.includes('Twilio Verify Service SID not configured')) return 'SERVICE_UNAVAILABLE';
      if (error.includes('unsupported OTP channel')) return 'INVALID_CHANNEL';
      if (error.includes('failed to send Twilio OTP')) return 'OTP_SEND_FAILED';
      if (error.includes('Twilio OTP send response did not include a SID')) return 'OTP_SEND_FAILED';
      
      // Generic fallback for unrecognized error messages
      return 'SYSTEM_ERROR';
    }
    
    // Fallback to status code mapping when no specific error message
    switch (status) {
      case 400: return 'INVALID_REQUEST';
      case 404: return 'USER_NOT_FOUND';
      case 500: return 'SYSTEM_ERROR';
      default: return 'SYSTEM_ERROR';
    }
  };

  const handleSendCode = async () => {
    const cleanedPhoneNumber = '+852' + phoneNumber.replace(/\s/g, '');
    // Use the same client_id logic as web app: 852 + local phone number
    const clientId = cleanedPhoneNumber.replace('+', '');
    setLoading(true);
    setError(null);

    try {
      const pkceCredentials = await generatePKCE();
      const phoneCheckPayload: PhoneCheckRequest = { phone: cleanedPhoneNumber };
      const checkResponse = await checkPhoneMutation(phoneCheckPayload);

      if (!checkResponse) {
        setError(t('error.sendVerificationCode.SYSTEM_ERROR'));
        setLoading(false);
        return;
      }

      let otpInitiationResponse;
      if (checkResponse.exists) {
        const loginPayload: ExistingPhoneOtpInitiateRequest = {
            phone: cleanedPhoneNumber,
            phone_otp_channel: channel as ('sms' | 'whatsapp'),
            client_id: clientId,
            code_challenge: pkceCredentials.codeChallenge,
            code_challenge_method: 'S256',
            state: pkceCredentials.state,
        };
        otpInitiationResponse = await initiateOtpLoginMutation(loginPayload);
      } else {
        const registerPayload: NewPhoneOtpInitiateRequest = {
            phone: cleanedPhoneNumber,
            phone_otp_channel: channel as 'whatsapp',
            client_id: clientId,
            code_challenge: pkceCredentials.codeChallenge,
            code_challenge_method: 'S256',
            state: pkceCredentials.state,
        };
        otpInitiationResponse = await initiateRegistrationMutation(registerPayload);
      }

      if (!otpInitiationResponse || !otpInitiationResponse.state) {
        setError(t('error.sendVerificationCode.SYSTEM_ERROR'));
        setLoading(false);
        return;
      }

      const navigationParams: any = {
        phoneNumber: cleanedPhoneNumber,
        isNewUser: checkResponse.exists ? '0' : '1',
        method: channel,
        flow_id: otpInitiationResponse.flow_id,
        state: otpInitiationResponse.state,
        codeVerifier: pkceCredentials.codeVerifier,
      };

      router.push({
        pathname: '/login/CodeVerificationScreen',
        params: navigationParams,
      });
    } catch (err: any) {
      const serverErrorCode = err.response?.data?.error;
      const statusCode = err.response?.status;
      const errorKey = getErrorKeyFromStatus(statusCode, serverErrorCode);
      const translatedError = t(`error.sendVerificationCode.${errorKey}`, {
        defaultValue: t('error.sendVerificationCode.SYSTEM_ERROR')
      });
      setError(translatedError);
    } finally {
      setLoading(false);
    }
  };

  const isPhoneNumberValid = phoneNumber.replace(/\s/g, '').length === 8;

  // Generate styles dynamically based on the effective theme
  const styles = getThemedStyles(effectiveTheme);

  return (
    <>
      <Stack.Screen options={{ headerTitle: t(screenTitleKey) }} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[styles.container, { backgroundColor: effectiveTheme.background }]}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <View style={styles.contentContainer}>
            <View style={styles.header}>
              <MaterialCommunityIcons name={headerIconName} size={48} color={effectiveTheme.primary} />
              <Text style={[styles.title, { color: effectiveTheme.primaryDark || effectiveTheme.primary }]}>
                {t(headerTitleKey)}
              </Text>
              <Text style={[styles.subtitle, { color: effectiveTheme.secondaryText }]}>
                {t(headerSubtitleKey)}
              </Text>
            </View>

            <View style={styles.inputContainer}>
              <View style={styles.infoContainer}>
                <MaterialCommunityIcons name="information-outline" size={16} color={effectiveTheme.secondaryText} />
                <Text style={[styles.infoText, { color: effectiveTheme.secondaryText }]}>{t(infoTextKey)}</Text>
              </View>

              <View style={[styles.phoneInputContainer, { borderColor: effectiveTheme.primary }]}>
                <Text style={[styles.prefix, { color: effectiveTheme.text }]}>{t('auth.phonePrefix')}</Text>
                <TextInput
                  ref={phoneInputRef}
                  style={[styles.phoneInput, { color: effectiveTheme.text }]}
                  value={phoneNumber}
                  onChangeText={handlePhoneNumberChange}
                  placeholder={t('auth.phonePlaceholder')}
                  keyboardType="number-pad"
                  placeholderTextColor={effectiveTheme.secondaryText}
                  maxLength={9} // "xxxx xxxx"
                />
              </View>

              {error && <Text style={[styles.errorText, { color: effectiveTheme.error }]}>{error}</Text>}

              {/* <TouchableOpacity
                onPress={() => router.push('/profile/AccountRecoveryScreen')}
                style={styles.recoveryLinkContainer}
              >
                <Text style={[styles.recoveryText, { color: effectiveTheme.secondaryText }]}>
                  {t('auth.forgotAccount')}
                </Text>
              </TouchableOpacity> */}

              <Button
                mode="contained"
                onPress={handleSendCode}
                disabled={!isPhoneNumberValid || loading}
                style={[
                  styles.button,
                  { backgroundColor: isPhoneNumberValid && !loading ? effectiveTheme.primary : effectiveTheme.primaryDisabled },
                ]}
                contentStyle={styles.buttonContent}
                labelStyle={[
                  styles.buttonLabel,
                  (!isPhoneNumberValid || loading) && { color: 'white' }
                ]}
                loading={loading}
              >
                {t(buttonTextKey)}
              </Button>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </>
  );
}

// Styles function to incorporate the theme
const getThemedStyles = (theme: ScreenTheme) => StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
  },
  contentContainer: {
    flex: 1,
    width: '100%',
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    // color: theme.secondaryText, // Applied inline
  },
  inputContainer: {
    alignItems: 'center',
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    height: 48,
    width: '100%',
    // borderColor: theme.primary, // Applied inline
  },
  prefix: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
    // color: theme.text, // Applied inline
  },
  phoneInput: {
    flex: 1,
    fontSize: 16,
    // color: theme.text, // Applied inline
  },
  errorText: {
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
    // color: theme.error, // Applied inline
  },
  button: {
    borderRadius: 12,
    width: '100%',
    height: 48,
  },
  buttonContent: {
    height: 48,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF', // Standard white label for buttons, adjust if needed per theme
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 4,
    marginBottom: 8,
    width: '100%',
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
    // color: theme.secondaryText, // Applied inline
  },
  recoveryLinkContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  recoveryText: {
    fontSize: 14,
    textDecorationLine: 'underline',
    // color: theme.secondaryText, // Applied inline
  },
}); 