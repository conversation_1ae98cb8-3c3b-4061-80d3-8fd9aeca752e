import i18n from 'i18next';

/**
 * Formats gender values using simple mapping
 * @param {string} gender - The gender value ('male', 'female', 'other')
 * @returns {string} - The localized gender text
 */
export const genderFormatter = (gender) => {
  if (!gender) return '-';
  
  // Get current language
  const currentLanguage = i18n.language;
  
  // Simple mapping for common gender values
  const genderMap = {
    'en': {
      male: 'Male',
      female: 'Female',
      other: 'Other'
    },
    'zh-HK': {
      male: '男士',
      female: '女士',
      other: '其他'
    }
  };
  
  // Use English as fallback if current language is not supported
  const languageMap = genderMap[currentLanguage] || genderMap.en;
  
  // Return the mapped value or capitalize the first letter as fallback
  return languageMap[gender.toLowerCase()] || 
         gender.charAt(0).toUpperCase() + gender.slice(1).toLowerCase();
};

const formatters = {
  genderFormatter
};

export default formatters;
