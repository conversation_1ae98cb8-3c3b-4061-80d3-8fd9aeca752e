// Helper function to get HTTP error messages
export const getHttpErrorMessage = (statusCode, t) => {
  switch (statusCode) {
    case 400:
      return t('httpErrors.400');
    case 401:
      return t('httpErrors.401');
    case 403:
      return t('httpErrors.403');
    case 404:
      return t('httpErrors.404');
    case 500:
      return t('httpErrors.500');
    default:
      // Check if it's a client-side error (4xx) or server-side (5xx)
      if (statusCode >= 400 && statusCode < 500) {
        return t('httpErrors.400'); // Generic client error
      }
      if (statusCode >= 500 && statusCode < 600) {
        return t('httpErrors.500'); // Generic server error
      }
      return t('httpErrors.unknown');
  }
}; 