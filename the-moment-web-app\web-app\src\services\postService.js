import api from './api';
import { API_ENDPOINTS } from './apiEndpoints';

// Helper to construct query parameters string from an object
const buildQueryParams = (params) => {
  return Object.entries(params)
    .filter(([_, value]) => value !== undefined && value !== null && value !== '')
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
};

const listPublicPostsParamsMapping = {
  tagIds: 'tag_ids',
  organizationId: 'organization_id',
  // limit, offset can be used directly if names match
};

const listOrgPostsParamsMapping = {
  tagIds: 'tag_ids',
  // limit, offset can be used directly if names match
};

export const postService = {
  /**
   * Lists all publicly published posts.
   * Corresponds to: GET /api/v1/posts
   */
  listPublicPosts: async (params = {}) => {
    try {
      const queryParams = buildQueryParams(params);
      const url = `${API_ENDPOINTS.POSTS.LIST_PUBLIC_POSTS}${queryParams ? `?${queryParams}` : ''}`;
      const axiosResponse = await api.getWithFullResponse(url);

      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;

      let posts = Array.isArray(responseData) ? responseData : [];
      let total = parseInt(responseHeaders['x-total-count'], 10) || posts.length;

      if (!Array.isArray(responseData)) {
        console.warn('listPublicPosts: API response data was not an array. Response data:', responseData);
      }

      return { posts, total };
    } catch (error) {
      console.error('Error fetching public posts:', error);
      return { posts: [], total: 0 };
    }
  },

  /**
   * Lists posts for a specific organization (Admin/Management View).
   * Corresponds to: GET /api/v1/organizations/{orgId}/posts
   */
  listOrganizationPosts: async (orgId, params = {}) => {
    if (!orgId) {
      console.error('listOrganizationPosts: Organization ID is required.');
      return { posts: [], total: 0 };
    }
    try {
      const queryParams = buildQueryParams(params);
      const url = `${API_ENDPOINTS.ORGANIZATIONS.LIST_ORG_POSTS(orgId)}${queryParams ? `?${queryParams}` : ''}`;
      const axiosResponse = await api.getWithFullResponse(url);

      const responseData = axiosResponse.data;
      const responseHeaders = axiosResponse.headers;

      let posts = Array.isArray(responseData) ? responseData : [];
      let total = parseInt(responseHeaders['x-total-count'], 10) || posts.length;

      if (!Array.isArray(responseData)) {
        console.warn(`listOrganizationPosts for org ${orgId}: API response data was not an array. Response data:`, responseData);
      }

      return { posts, total };
    } catch (error) {
      console.error(`Error fetching posts for organization ${orgId}:`, error);
      return { posts: [], total: 0 };
    }
  },

  // Get public post details by post ID or slug
  getPublicPostDetail: async (postIdOrSlug) => {
    try {
      const response = await api.get(API_ENDPOINTS.POSTS.GET_PUBLIC_POST_DETAIL(postIdOrSlug));
      return response;
    } catch (error) {
      console.error(`Error fetching public post details for ${postIdOrSlug}:`, error);
      throw error;
    }
  },

  /**
   * Download a media file attached to a post.
   * @param {string} orgId - Organization ID
   * @param {string} postIdOrSlug - Post ID or slug
   * @param {string} fileIdOrName - File ID or name
   * @returns {Promise<Blob>} - The file blob
   */
  downloadPostMedia: async (orgId, postIdOrSlug, fileIdOrName) => {
    try {
      // Using a similar path structure as resources but for posts
      const downloadUrl = `/posts/download/${orgId}/${postIdOrSlug}/${fileIdOrName}`;
      const response = await api.get(downloadUrl, {
        responseType: 'blob', // 重要：设置响应类型为blob用于文件下载
      });
      return response;
    } catch (error) {
      console.error(`Error downloading post media file: ${error.message || error}`);
      throw error;
    }
  },

  // List post tags - Updated to work with new API format
  listPostTags: async () => {
    try {
      const url = API_ENDPOINTS.POSTS.LIST_POST_TAGS_PAGINATED;
      const response = await api.get(url);
      
      // API now returns array of tags with format: { id, name_en, name_zh_hk, name_zh_cn }
      if (response && Array.isArray(response.data?.tags)) {
        return response.data.tags;
      } else if (response && Array.isArray(response.data?.items)) {
        return response.data.items;
      } else if (response && Array.isArray(response.data)) {
        return response.data;
      } else if (response && Array.isArray(response.tags)) {
        return response.tags;
      } else if (Array.isArray(response)) {
        return response;
      }
      
      console.warn('listPostTags: API response was not in the expected format. Response:', response);
      return [];
    } catch (error) {
      console.error('Error fetching post tags:', error);
      return [];
    }
  },

  // Create a new post tag - Updated to use new API format
  createPostTag: async (tagData) => {
    // tagData should be: { name_en: string, name_zh_hk: string, name_zh_cn: string }
    try {
      const response = await api.post(API_ENDPOINTS.POSTS.CREATE_POST_TAG, tagData);
      return response;
    } catch (error) {
      console.error('Error creating post tag:', error);
      throw error;
    }
  },

  // Get details for a specific post tag
  getPostTagDetail: async (tagId) => {
    try {
      const response = await api.get(API_ENDPOINTS.POSTS.GET_POST_TAG_DETAIL(tagId));
      return response;
    } catch (error) {
      console.error(`Error fetching post tag details for ${tagId}:`, error);
      throw error;
    }
  },

  updatePostTag: async (tagId, tagData) => {
    // tagData should be: { name_en?: string, name_zh?: string }
    // Caller must ensure this format.
    try {
      const response = await api.put(API_ENDPOINTS.POSTS.UPDATE_POST_TAG(tagId), tagData);
      return response;
    } catch (error) {
      console.error(`Error updating post tag ${tagId}:`, error);
      throw error;
    }
  },

  deletePostTag: async (tagId) => {
    try {
      await api.delete(API_ENDPOINTS.POSTS.DELETE_POST_TAG(tagId));
      // No response body typically expected
    } catch (error) {
      console.error(`Error deleting post tag ${tagId}:`, error);
      throw error;
    }
  },

  // TODO: Add other post-related service methods as needed (e.g., getPost, createPost, updatePost, deletePost)
  // based on the full API specification if available.

  /**
   * Retrieves a single post by its ID. (Example - not in the provided guide but typical)
   * Endpoint: GET /api/v1/posts/{postId}
   */
  // getPostById: async (postId) => {
  //   try {
  //     const response = await api.get(API_ENDPOINTS.POSTS.GET_POST_DETAIL(postId));
  //     return response.data; // Expecting a single post object
  //   } catch (error) {
  //     console.error(`Error fetching post ${postId}:`, error);
  //     throw error;
  //   }
  // },
}; 