openapi: 3.0.0
info:
  title: Membership API (2025-03-14)
  description: API for Membership Web-app.

    注意：

    1. 未登入用戶，及任意用戶（any user），也應顯示所有組織，我們將 organizations 的獲取從驗證（auth）部分替換到儀表板（dashboard/home）頁面。

    2. 是否應該為所有 events 指定 organizationId？我暫時未確定，可能應該附在params裡獲取數據。

    3. 錯誤代碼（error code）的標注不一定完全準確，請根據情境返回 status code。前端會根據 code 屬性顯示對應的錯誤頁面，可以正常顯示 403, 404, 500, 其他返回 default error頁面。

    4. All API formats are open to modifications and improvements as needed.

  version: 1.0.0

tags:
  - name: home
    description: Home endpoints
  - name: events
    description: Events endpoints
  - name: user events
    description: User Events endpoints
  - name: posts
    description: Posts endpoints
  - name: resources
    description: Resources endpoints
  - name: admin manager management
    description: Admin Manager Management endpoints
  - name: admin volunteer
    description: Admin Volunteer endpoints
  - name: admin user verification
    description: Admin User Verification endpoints
  - name: user
    description: User endpoints
  - name: admin events report
    description: Admin Events Report endpoints

paths:
  /organizations:
    get:
      tags:
        - home
      summary: Get all organizations
      description: |
        Retrieve a list of all organizations.

        註：由於Client說明未登入用戶，及任意用戶（any user），也應顯示所有組織，因此我們將organizations的獲取從auth部分替換到dashboard頁面。
      operationId: getAllOrganizations
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Organization"
              example:
                - id: "1"
                  name: "同行明天"
                  theme: "orange"
                  logo: "s3://membership-system-assets/organizations/project-tomorrow.png"
                - id: "2"
                  name: "沙田西關愛隊"
                  theme: "green"
                  logo: "s3://membership-system-assets/organizations/shatin-west.jpg"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /banner:
    get:
      tags:
        - home
      summary: Get banner posts
      description: |
        Get banner posts for home carousel.

        1. 此處增加了author的名字和avatarUrl
      operationId: getBannerPosts
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/BannerPost"
              example:
                - id: "1"
                  title: "簡單文字測試"
                  author:
                    name: "Jane Smith"
                    avatarUrl: "https://api.dicebear.com/7.x/miniavs/svg?seed=1"
                  coverImage: "s3://membership-system-assets/banners/banner1.jpg"
                - id: "2"
                  title: "所有文本樣式測試"
                  author:
                    name: "John Doe"
                    avatarUrl: "https://api.dicebear.com/7.x/miniavs/svg?seed=2"
                  coverImage: "s3://membership-system-assets/banners/banner2.jpg"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /upcoming-events:
    get:
      tags:
        - home
      summary: Get upcoming events
      description: |
        Get upcoming events for home page.

        改動為：
        1. 因應客戶需求，將顯示格式由單一日期改為時間範圍，故將「date」欄位更新為「startTime」，
           並配合後續 event detail 部分的 startTime 和 endTime 組合，取代原有的 date 和 duration 組合，
           以確保整體系統的consistency。後續所有date都改為startTime。
        2. 移除 description 欄位，改為提供 location 資訊。
           原因：description 資訊通常較長，且因應富文本編輯器的儲存結構變更，
           頻繁獲取 description 可能會增加系統負載。
      operationId: getUpcomingEvents
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/UpcomingEvent"
              example:
                - id: "1"
                  title: "AI in Business Seminar"
                  startTime: "2025-03-15T09:00:00+08:00"
                  coverImage: "s3://membership-system-assets/events/upcoming/image1.jpg"
                - id: "2"
                  title: "Digital Marketing Workshop"
                  startTime: "2025-03-20T14:00:00+08:00"
                  coverImage: "s3://membership-system-assets/events/upcoming/image2.jpg"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /popular-events:
    get:
      tags:
        - home
      summary: Get popular events
      description: |
        Get popular events for home page.

        改動為：
        1. 與 /upcoming-events 端點一致，將「date」欄位更新為「startTime」
        2. 移除 description 欄位，改為使用 location 欄位提供場地資訊
      operationId: getPopularEvents
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PopularEvent"
              example:
                - id: "1"
                  title: "領導力峰會"
                  startTime: "2025-03-05T09:00:00+08:00"
                  location: "市中心會議中心"
                  coverImage: "s3://membership-system-assets/events/popular/image1.jpg"
                - id: "2"
                  title: "創新工作坊"
                  startTime: "2025-03-15T09:00:00+08:00"
                  location: "科技中心"
                  coverImage: "s3://membership-system-assets/events/popular/image2.jpg"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /latest-posts:
    get:
      tags:
        - home
      summary: Get latest news
      description: |
        Retrieve the latest news items.

        1. 此處我們需要獲取一個author的name，為節省空間，我們不使用avatarUrl，但是保持相應結構
      operationId: getLatestPosts
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/NewsItem"
              example:
                - id: "1"
                  title: "簡單文字測試"
                  author:
                    name: "Jane Smith"
                  updatedAt: "2025-01-15T09:30:00Z"
                  coverImage: "s3://membership-system-assets/news/image1.jpg"
                - id: "2"
                  title: "所有文本樣式測試"
                  author:
                    name: "John Doe"
                  updatedAt: "2025-01-10T14:45:00Z"
                  coverImage: "s3://membership-system-assets/news/image2.jpg"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /events:
    get:
      tags:
        - events
      summary: Get events list
      description: >-
        Get paginated events with filters. 

        改動為：

        1. 新增篩選參數（filter parameters）功能。
           備註：行動版介面中使用了 priceFilter 參數，與 Thomas 討論後評估。

        2. 將「date」欄位更新為「startTime」，以配合整體系統的ISO時間表示方式。

        3. 應用戶要求，新增「maxParticipants」，欄位「活動人數上限」，
           用於前端顯示活動是否已滿員。

        4. 不再使用totalPages，改用total顯示總數在底部pagination中。

        5. 由於description我們使用較複雜的json存儲，可能導致服務器負載問題和行動端性能問題，後續可能會移除。

        6. 由於客戶要求，新增organizationId篩選參數，用於過濾當前組織的events。預設為0，代表所有組織。

        7. 新增isHidden參數，用於過濾隱藏的活動。預設為false，代表顯示活動。僅admin用戶可見此參數，可能需要token..?
      operationId: getEvents
      parameters:
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 12
        - name: searchText
          in: query
          description: Search by title/description
          required: false
          schema:
            type: string
          example: "conference"
        - name: dateRange
          in: query
          description: ISO 8601 date range
          required: false
          schema:
            type: array
            items:
              type: string
              format: date-time
          example: ["2024-08-01T00:00:00.000Z", "2024-08-31T23:59:59.999Z"]
        - name: eventTypes
          in: query
          description: Filter by event types
          required: false
          schema:
            type: array
            items:
              type: string
              enum:
                [
                  Webinar,
                  Networking,
                  Conference,
                  Workshop,
                  Expo,
                  Seminar,
                  Bootcamp,
                  Forum,
                  Hackathon,
                  Symposium,
                  Summit,
                ]
          example: ["Conference", "Workshop"]
        - name: showHotEvents
          in: query
          description: Show events with >100 participants
          required: false
          schema:
            type: boolean
            default: false
          example: true
        - name: organizationId
          in: query
          description: Filter events by organization ID
          required: false
          schema:
            type: integer
            default: 0
          example: 1
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EventsResponse"
              example:
                currentPage: 1
                total: 100
                events:
                  - id: "1"
                    title: "Business Summit 2024"
                    type: "Conference"
                    startTime: "2024-09-15T09:00:00+08:00"
                    location: "Convention Center"
                    participantsCount: 500
                    maxParticipants: 1000
                    price: 299.99
                    status: "Upcoming"
                    coverImage: "s3://membership-system-assets/events/event1/cover.jpg"
                    isHidden: false
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /events/{id}:
    get:
      tags:
        - events
      summary: Get event details
      description: >-
        Get detailed event info. 

        改動為：

        1. 新增「maxParticipants」欄位，提供活動人數上限資訊。

        2. 新增「startTime」和「endTime」欄位，取代原有的單一日期欄位，
           以提供更完整的活動時間資訊。
           
        3. 應用戶要求，新增「maxWaitingList」欄位，提供等候名單上限。

        4. 新增「video」欄位，提供活動相關視頻。

        5. 新增「organizer」欄位，提供活動主辦方信息。

        6. 新增「identityRequired」欄位，標識需要哪些身份證件證明。

        7. description 欄位改為使用 JSON 結構，與 Tiptap 編輯器兼容。
      operationId: getEventDetails
      parameters:
        - name: id
          in: path
          description: Event ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EventDetail"
              example:
                id: "1"
                title: "商業峰會 2025"
                type: "Conference"
                description:
                  {
                    "type": "doc",
                    "content":
                      [
                        {
                          "type": "heading",
                          "attrs": { "textAlign": "left", "level": 3 },
                          "content": [{ "type": "text", "text": "活動簡介" }],
                        },
                        {
                          "type": "paragraph",
                          "attrs": { "textAlign": "left" },
                          "content":
                            [
                              {
                                "type": "text",
                                "text": "年度商業峰會，邀請行業領袖參加。我們將探討最新的商業趨勢和創新策略。",
                              },
                            ],
                        },
                      ],
                  }
                location: "會議中心"
                participantsCount: 1100
                maxParticipants: 1000
                price: 299.99
                maxWaitingList: 200
                status: "Upcoming"
                startTime: "2025-03-15T09:00:00+08:00"
                endTime: "2025-03-15T17:00:00+08:00"
                coverImage: "s3://membership-system-assets/events/event1/cover.jpg"
                images:
                  [
                    "s3://membership-system-assets/events/event1/image1.jpg",
                    "s3://membership-system-assets/events/event1/image2.jpg",
                    "s3://membership-system-assets/events/event1/image3.jpg",
                  ]
                video: { url: "https://www.w3schools.com/html/mov_bbb.mp4" }
                organizer:
                  { name: "陳大文", organizationName: "商業活動有限公司" }
                identityRequired:
                  {
                    hkid: true,
                    passport: false,
                    mainlandTravelPermit: false,
                    hkyouth: false,
                    address: true,
                  }
                isHidden: false
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "Resource not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /user/events:
    get:
      tags:
        - user events
      summary: Get user events
      description: >-
        Get events that the current user is registered for. 

        改動為：

        1. 將「participants」欄位更正為「participantsCount」，修正原有的命名不一致問題。

        2. 新增「price」欄位，提供活動價格資訊。

        3. 新增「status」欄位，提供活動狀態資訊，可能的值包括 'upcoming'、'completed'、'absent' 和 'cancelled'。

        4. image更名為coverImage

      operationId: getUserEvents
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserEvents"
              example:
                events:
                  - id: "4"
                    title: "Web Development Bootcamp"
                    price: 999.99
                    startTime: "2025-02-20T09:00:00"
                    location: "Online"
                    role: "volunteer"
                    participantsCount: 30
                    coverImage: "s3://membership-system-assets/events/event4/cover.jpg"
                    status: "completed"
                  - id: "5"
                    title: "Networking Night"
                    price: 0
                    startTime: "2025-03-10T18:00:00"
                    location: "Downtown Café"
                    role: "volunteer"
                    participantsCount: 100
                    coverImage: "s3://membership-system-assets/events/event5/cover.jpg"
                    status: "absent"
                  - id: "6"
                    title: "Data Science Webinar"
                    price: 49.99
                    startTime: "2025-03-15T19:00:00"
                    location: "Virtual"
                    role: "participant"
                    participantsCount: 75
                    coverImage: "s3://membership-system-assets/events/event6/cover.jpg"
                    status: "completed"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /events/{id}/ticket:
    get:
      tags:
        - user events
      summary: Get event ticket
      description: >-
        Get user's ticket for a specific event

        改動為：

        1. 替換status為新增「participationStatus」欄位，提供活動狀態，區分是否已完成。"upcoming" 和 "completed" 和 "cancelled" 和 "absent"，具體怎麼設計待Winston自行評估。

        2. 此處設計還有待商榷，ticket QR code 應該使用的是固定user ID的hash。

      operationId: getEventTicket
      parameters:
        - name: id
          in: path
          description: Event ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EventTicket"
              example:
                event:
                  id: "4"
                  title: "Web Development Bootcamp"
                  startTime: "2025-02-20T09:00:00"
                  endTime: "2025-02-20T17:00:00"
                  location: "Online"
                  price: 999.99
                  coverImage: "s3://membership-system-assets/events/event4/cover.jpg"
                ticket:
                  userName: "Test Admin"
                  qrCodeData: "hashed-1-event4-data"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "404":
          description: Not found or ticket not available
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "Resource not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /posts:
    get:
      tags:
        - posts
      summary: Get posts list
      description: Get a list of all posts with pagination. 註：此處應bring admin token，才會返回isHidden。普通用戶不會看到isHidden的posts。
      operationId: getPosts
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number
        - name: pageSize
          in: query
          required: false
          schema:
            type: integer
            default: 12
          description: Number of items per page
        - name: searchText
          in: query
          required: false
          schema:
            type: string
          description: Text to search for in post titles
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  currentPage:
                    type: integer
                    description: Current page number
                  total:
                    type: integer
                    description: Total number of items
                  posts:
                    type: array
                    items:
                      $ref: "#/components/schemas/Post"
                required:
                  - currentPage
                  - total
                  - posts
              example:
                currentPage: 1
                total: 2
                posts:
                  - id: "1"
                    title: "簡單文字測試"
                    author:
                      name: "Jane Smith"
                    isHidden: false
                    updatedAt: "2025-01-15T09:30:00Z"
                    coverImage: "s3://membership-system-assets/posts/post1/cover.jpg"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /posts/{id}:
    get:
      tags:
        - posts
      summary: Get post details
      description: >-
        Get details of a specific post.


        請注意：我們的 content 欄位使用 JSON 結構而非純 HTML 字符串或者Markdown，這是因為我們使用 Tiptap 編輯器。
        Tiptap 基於 ProseMirror，內部使用 JSON 結構來存儲文檔內容。

        前端我們使用的是 Tiptap 的 generateHTML() 函數將 JSON 轉換為 HTML 進行顯示。我們可能需要討論是否需要對JSON進行compress。
      operationId: getPostDetails
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Post ID
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PostDetail"
              example:
                id: "1"
                title: "簡單文字測試"
                author:
                  name: "Jane Smith"
                  avatarUrl: "https://api.dicebear.com/7.x/miniavs/svg?seed=5"
                updatedAt: "2025-03-10T14:30:00Z"
                coverImage: "s3://membership-system-assets/posts/post1/cover.jpg"
                isHidden: false
                content:
                  type: "doc"
                  content:
                    [
                      {
                        "type": "heading",
                        "attrs": { "textAlign": "left", "level": 3 },
                        "content":
                          [
                            { "type": "text", "text": "1. " },
                            {
                              "type": "text",
                              "marks": [{ "type": "bold" }],
                              "text": "Markdown（純文本格式）",
                            },
                          ],
                      },
                    ]
                attachments:
                  - type: "video"
                    url: "s3://membership-system-assets/posts/post1/video.mp4"
                    name: "Introduction Video"
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "Resource not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /resources:
    get:
      tags:
        - resources
      summary: Get resources list
      description: >-
        Get a list of all resources with pagination

        請注意：

        1. 此處的default pageSize為24
        2. 我們的Resource總體簡介為可選的，並且每個file的description也為可選的，返回空字串。如要節省空間，可不返回此字段，我已做好處理。
      operationId: getResources
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number
        - name: pageSize
          in: query
          required: false
          schema:
            type: integer
            default: 24
          description: Number of items per page
        - name: searchText
          in: query
          required: false
          schema:
            type: string
          description: Text to search for in resource titles
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  currentPage:
                    type: integer
                    description: Current page number
                  total:
                    type: integer
                    description: Total number of items
                  resources:
                    type: array
                    items:
                      $ref: "#/components/schemas/Resource"
                required:
                  - currentPage
                  - total
                  - resources
              example:
                currentPage: 1
                total: 2
                resources:
                  - id: "1"
                    title: "多檔案測試 (18個檔案)"
                    description: "義工手冊材料的全面收集，包括文件、電子表格、演示文稿和多媒體資源。這些資源旨在幫助新義工快速適應工作環境，並為現有義工提供持續的支持和指導。"
                    updatedAt: "2025-01-15T10:30:00Z"
                    files:
                      - id: "1-1"
                        type: "pdf"
                        size: "1020 MB"
                        fileName: "volunteer_handbook_2024_final_version.pdf"
                        downloadUrl: "#"
                        description: "完整的義工手冊，包含所有政策和程序，以及組織的使命、願景和核心價值觀。此文件是所有義工必讀的基礎材料，涵蓋了從入職到進階的全部內容。"
                      - id: "1-2"
                        type: "docx"
                        size: "1.8 MB"
                        fileName: "volunteer_handbook_appendix.docx"
                        downloadUrl: "#"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /resources/{id}:
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
        description: Resource ID

    delete:
      tags:
        - resources
      summary: Delete resource
      description: Delete an existing resource
      operationId: deleteResource
      responses:
        "204":
          description: No content
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "404":
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "Resource not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/qualification-volunteers:
    get:
      tags:
        - admin volunteer
      summary: Get qualification volunteers list
      description: >-
        Get a list of qualification volunteers with pagination. These are users who have applied to become qualified volunteers.

        The response includes pagination information and volunteer data.
      operationId: getQualificationVolunteers
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - name: pageSize
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - name: status
          in: query
          description: Filter volunteers by status (pending, approved, rejected)
          schema:
            type: string
            enum: [pending, approved, rejected]
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  currentPage:
                    type: integer
                    description: Current page number
                  total:
                    type: integer
                    description: Total number of items
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        userId:
                          type: string
                          description: User ID
                        name:
                          type: string
                          description: User name
                        phoneNumber:
                          type: string
                          description: User phone number
                        applicationTime:
                          type: string
                          format: date-time
                          description: Application time
                        status:
                          type: string
                          description: Application status
              example:
                currentPage: 1
                total: 100
                data:
                  - userId: "1"
                    name: "John Doe"
                    phoneNumber: "********"
                    applicationTime: "2024-10-15T10:00:00Z"
                    status: "pending"
                  - userId: "2"
                    name: "Jane Smith"
                    phoneNumber: "62345678"
                    applicationTime: "2024-10-16T11:30:00Z"
                    status: "pending"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/event-volunteers:
    get:
      tags:
        - admin volunteer
      summary: Get event registration volunteers list
      description: >-
        Get a list of event registration volunteers with pagination. These are users who have applied to volunteer for specific events.

        The response includes pagination information and volunteer data.
      operationId: getEventRegistrationVolunteers
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number
        - name: pageSize
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - name: status
          in: query
          description: Filter volunteers by status (pending, approved, rejected)
          schema:
            type: string
            enum: [pending, approved, rejected]
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  currentPage:
                    type: integer
                    description: Current page number
                  total:
                    type: integer
                    description: Total number of items
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        userId:
                          type: string
                          description: User ID
                        name:
                          type: string
                          description: User name
                        phoneNumber:
                          type: string
                          description: User phone number
                        applicationTime:
                          type: string
                          format: date-time
                          description: Application time
                        status:
                          type: string
                          description: Application status
                        eventId:
                          type: string
                          description: Event ID
                        eventName:
                          type: string
                          description: Event name
              example:
                currentPage: 1
                total: 100
                data:
                  - userId: "1"
                    name: "John Doe"
                    phoneNumber: "********"
                    applicationTime: "2024-10-18T14:20:00Z"
                    status: "pending"
                    eventId: "101"
                    eventName: "Community Cleanup Day"
                  - userId: "4"
                    name: "Emily Chen"
                    phoneNumber: "********"
                    applicationTime: "2024-10-19T16:45:00Z"
                    status: "pending"
                    eventId: "102"
                    eventName: "Food Drive"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/qualification-volunteers/{id}:
    get:
      tags:
        - admin volunteer
      summary: Get qualification volunteer details
      description: >-
        Get qualification volunteer details.


        此 API 返回義工的基本資料，包括個人資料、參與統計及活動歷史。個人資料部分包含義工的基本資訊，如姓名、電話、性別等。
        請參考http://localhost:3000/volunteers-approval/qualification/details/1


        統計資料部分顯示義工的參與情況，包括：

        - 作為參與者參加的活動總數

        - 作為參與者參加的總時數

        - 缺席的活動數量

        活動歷史記錄顯示義工過去參與的活動，包括活動名稱、狀態（完成或缺席）及時間。
      operationId: getQualificationVolunteerDetails
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the volunteer
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Volunteer ID
                  status:
                    type: string
                    enum: [pending, approved, rejected]
                    description: Current status of the application
                  personalInfo:
                    type: object
                    properties:
                      name:
                        type: string
                        description: User name
                      phoneNumber:
                        type: string
                        description: User phone number
                      gender:
                        type: string
                        enum: [male, female, other]
                        description: User gender
                      dateOfBirth:
                        type: string
                        format: date
                        description: User birth date
                      avatarUrl:
                        type: string
                        description: URL to user's profile image
                  statistics:
                    type: object
                    properties:
                      totalParticipantEvents:
                        type: integer
                        description: Total number of events attended as participant
                      totalParticipantHours:
                        type: integer
                        description: Total hours spent as participant
                      absentParticipantEvents:
                        type: integer
                        description: Number of events marked as absent
                  activityHistory:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: Activity ID
                        eventName:
                          type: string
                          description: Name of the event
                        status:
                          type: string
                          enum: [completed, absent]
                          description: Status of participation
                        startTime:
                          type: string
                          format: date-time
                          description: Start time of the event
                        endTime:
                          type: string
                          format: date-time
                          description: End time of the event
                  accountHistory:
                    type: array
                    items:
                      type: object
                      properties:
                        timestamp:
                          type: string
                          format: date-time
                          description: Time of the action
                        action:
                          type: string
                          description: Description of the account action
                        status:
                          type: string
                          enum: [completed, approved, rejected]
                          description: Status of the action
                        reviewer:
                          type: string
                          description: Person or system who reviewed the action
                        comments:
                          type: string
                          description: Additional comments about the action
              example:
                id: "1"
                status: "pending"
                personalInfo:
                  name: "John Doe"
                  phoneNumber: "********"
                  gender: "male"
                  dateOfBirth: "1990-05-15"
                  avatarUrl: "https://example.com/profiles/johndoe.jpg"
                statistics:
                  totalParticipantEvents: 15
                  totalParticipantHours: 120
                  absentParticipantEvents: 2
                activityHistory:
                  - id: 1
                    eventName: "Community Clean-up Drive"
                    status: "completed"
                    startTime: "2024-02-15T09:00:00"
                    endTime: "2024-02-15T12:00:00"
                  - id: 2
                    eventName: "Senior Care Visit"
                    status: "completed"
                    startTime: "2024-02-01T14:00:00"
                    endTime: "2024-02-01T17:00:00"
                  - id: 3
                    eventName: "Food Bank Distribution"
                    status: "absent"
                    startTime: "2024-01-20T10:00:00"
                    endTime: "2024-01-20T13:00:00"
                accountHistory:
                  - timestamp: "2024-03-05T14:20:00"
                    action: "香港身份證認證拒絕"
                    status: "rejected"
                    reviewer: "Admin Team"
                    comments: "身分證件照片不清晰，請重新提交"
                  - timestamp: "2024-03-10T09:15:00"
                    action: "護照驗證通過"
                    status: "approved"
                    reviewer: "Verification Team"
                    comments: "身分證件已驗證通過"
                  - timestamp: "2024-03-15T11:45:00"
                    action: "提交義工認證請求"
                    status: "approved"
                    reviewer: "System"
                    comments: ""
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "Volunteer application not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/event-volunteers/{id}:
    get:
      tags:
        - admin volunteer
      summary: Get event volunteer details
      description: >-
        Get detailed information about a specific event volunteer application.  

        此頁面顯示全面的義工資料，包括個人資料、參與統計、活動歷史、帳戶歷史及相關活動詳情。請參考https://full-pages-development-mode.vercel.app/volunteers-approval/eventRegistration/details/4


        個人資料部分包含義工的基本資訊，如姓名、電話、性別等。


        統計資料部分顯示義工的參與情況，包括：

        - 作為參與者參加的活動總數及總時數

        - 作為義工參加的活動總數及總時數

        - 缺席的活動數量（包括一般參與和志願服務）


        使用bar charts展示活動歷史記錄的composition。

        帳戶歷史記錄顯示與義工帳戶相關的操作歷史，如帳戶創建、身份認證等，供管理員審核。

        活動詳情部分包含義工申請服務的特定活動資訊，如活動標題、地點、參與人數、最大參與人數、開始和結束時間，以及當前義工人數和預估需要人數（前端自動計算5%）。

      operationId: getEventVolunteerDetails
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the volunteer
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Volunteer application ID
                  status:
                    type: string
                    enum: [pending, approved, rejected]
                    description: Current status of the application
                  personalInfo:
                    type: object
                    properties:
                      id:
                        type: string
                        description: user id
                      name:
                        type: string
                        description: User's full name in English
                      chineseName:
                        type: string
                        description: User's Chinese name
                      phoneNumber:
                        type: string
                        description: User's phone number
                      gender:
                        type: string
                        description: User's gender
                      dateOfBirth:
                        type: string
                        format: date
                        description: User's date of birth
                      avatarUrl:
                        type: string
                        description: URL to user's avatar image
                  statistics:
                    type: object
                    properties:
                      totalParticipantEvents:
                        type: integer
                        description: Total number of events participated in as attendee
                      totalParticipantHours:
                        type: integer
                        description: Total hours spent as participant
                      absentParticipantEvents:
                        type: integer
                        description: Number of events marked as absent
                      totalVolunteerEvents:
                        type: integer
                        description: Total number of events volunteered for
                      totalVolunteerHours:
                        type: integer
                        description: Total hours spent volunteering
                      absentVolunteerEvents:
                        type: integer
                        description: Number of volunteer events marked as absent
                  activityHistory:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: Activity ID
                        eventName:
                          type: string
                          description: Name of the event
                        status:
                          type: string
                          enum: [completed, absent, upcoming]
                          description: Status of participation
                        startTime:
                          type: string
                          format: date-time
                          description: Start time of the event
                        endTime:
                          type: string
                          format: date-time
                          description: End time of the event
                  accountHistory:
                    type: array
                    items:
                      type: object
                      properties:
                        timestamp:
                          type: string
                          format: date-time
                          description: Time of the action
                        action:
                          type: string
                          description: Description of the account action
                        status:
                          type: string
                          enum: [completed, approved, rejected]
                          description: Status of the action
                        reviewer:
                          type: string
                          description: Person or system who reviewed the action
                        comments:
                          type: string
                          description: Additional comments about the action
                  eventDetails:
                    type: object
                    properties:
                      id:
                        type: string
                        description: Event ID
                      title:
                        type: string
                        description: Event title
                      location:
                        type: string
                        description: Event location
                      participantsCount:
                        type: integer
                        description: Current number of participants
                      maxParticipants:
                        type: integer
                        description: Maximum number of participants
                      startTime:
                        type: string
                        format: date-time
                        description: Start time of the event
                      endTime:
                        type: string
                        format: date-time
                        description: End time of the event
                      volunteerCount:
                        type: integer
                        description: Current number of volunteers
              example:
                id: "1"
                status: "approved"
                personalInfo:
                  id: "123456"
                  name: "John Anderson"
                  chineseName: "安德森"
                  phoneNumber: "********"
                  gender: "Male"
                  dateOfBirth: "1990-05-15"
                  avatarUrl: "https://api.dicebear.com/7.x/miniavs/svg?seed=1"
                statistics:
                  totalParticipantEvents: 15
                  totalParticipantHours: 120
                  absentParticipantEvents: 2
                  totalVolunteerEvents: 8
                  totalVolunteerHours: 64
                  absentVolunteerEvents: 1
                activityHistory:
                  - id: 1
                    eventName: "Community Clean-up Drive"
                    status: "completed"
                    startTime: "2024-02-15T09:00:00"
                    endTime: "2024-02-15T12:00:00"
                  - id: 2
                    eventName: "Senior Care Visit"
                    status: "completed"
                    startTime: "2024-02-01T14:00:00"
                    endTime: "2024-02-01T17:00:00"
                  - id: 3
                    eventName: "Food Bank Distribution"
                    status: "absent"
                    startTime: "2024-01-20T10:00:00"
                    endTime: "2024-01-20T13:00:00"
                accountHistory:
                  - timestamp: "2024-03-05T14:20:00"
                    action: "香港身份證認證拒絕"
                    status: "rejected"
                    reviewer: "Admin Team"
                    comments: "身分證件照片不清晰，請重新提交"
                  - timestamp: "2024-03-10T09:15:00"
                    action: "護照驗證通過"
                    status: "approved"
                    reviewer: "Verification Team"
                    comments: "身分證件已驗證通過"
                  - timestamp: "2024-03-15T11:45:00"
                    action: "提交義工認證請求"
                    status: "approved"
                    reviewer: "System"
                    comments: ""
                eventDetails:
                  id: "1"
                  title: "商業峰會 2025"
                  location: "會議中心"
                  participantsCount: 899
                  maxParticipants: 1000
                  startTime: "2025-03-15T09:00:00+08:00"
                  endTime: "2025-03-15T17:00:00+08:00"
                  volunteerCount: 25
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "Volunteer application not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/verifications:
    get:
      tags:
        - admin user verification
      summary: Get verifications list
      description: >-
        Get a list of user verifications with pagination. These are user identity and document verifications that need to be approved or rejected.

        The response includes pagination information and verification data.


        此處我設想的規則是：

        - Only when both are "approved", the status is considered "approved"

        - If any verification is "pending", the status is considered "pending"

        - Then if any verification is "rejected", the status is considered "rejected"


        驗證狀態判斷規則：

        - 當兩項驗證皆為「approved」，整體狀態才會被視為「approved」

        - 若任何驗證為「pending」，整體狀態會被視為「pending」

        - 若任何驗證為「rejected」，整體狀態會被視為「rejected」


        更新結構如下：

        1. 新增用戶資料欄位：
           - fullname：英文全名
           - chineseName：中文姓名 （可不提供，後續可移除也可保留）
           - phoneNumber：電話號碼

        2. 文件驗證資料整合至 documentVerification 物件： 
           - hkid：香港身份證驗證

           - passport：護照驗證

           - mainlandTravelPermit：内地旅行證件驗證, 包括回鄉證及港澳通行證。

           - hkyouth：香港青年證驗證

           - address：地址證明驗證

      operationId: getVerifications
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - name: pageSize
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - name: status
          in: query
          description: Filter verifications by status (pending, approved, rejected)
          schema:
            type: string
            enum: [pending, approved, rejected]
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Verifications"
              example:
                currentPage: 1
                total: 1
                verifications:
                  - userId: "2"
                    fullname: "Jane Smith"
                    chineseName: "史珍妮"
                    phoneNumber: "********"
                    documentVerification:
                      hkid:
                        status: "pending"
                        submissionTime: "2024-10-21T14:45:00Z"
                      passport:
                        status: "rejected"
                        submissionTime: "2024-10-21T14:50:00Z"
                      address:
                        status: "approved"
                        submissionTime: "2024-10-21T14:50:00Z"

        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/verifications/{userId}:
    get:
      tags:
        - admin user verification
      summary: Get verification details
      description: >-
        Get detailed information about a specific user verification.

        此處修改為列出所有證件類型對應的資料和document。

        1. documents.type的部分似乎看上去很奇怪，例如idFront和idBack，又有phoneBill等。這是因為我想在圖片容器的title上寫明這是什麼，如果你有更好的數據結構，請進行修改。

      operationId: getVerificationDetails
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
          description: User ID
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  userId:
                    type: string
                  username:
                    type: string
                  phoneNumber:
                    type: string
                  documentVerification:
                    type: object
                    properties:
                      passport:
                        type: object
                        properties:
                          status:
                            type: string
                            enum: [pending, approved, rejected]
                          submissionTime:
                            type: string
                            format: date-time
                          details:
                            type: object
                            properties:
                              fullname:
                                type: string
                              chineseName:
                                type: string
                              gender:
                                type: string
                                enum: [male, female, other]
                              idNumber:
                                type: string
                              dateOfBirth:
                                type: string
                                format: date
                          documents:
                            type: array
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                url:
                                  type: string
                      hkid:
                        type: object
                        properties:
                          status:
                            type: string
                            enum: [pending, approved, rejected]
                          submissionTime:
                            type: string
                            format: date-time
                          details:
                            type: object
                            properties:
                              fullname:
                                type: string
                              chineseName:
                                type: string
                              gender:
                                type: string
                                enum: [male, female, other]
                              idNumber:
                                type: string
                              dateOfBirth:
                                type: string
                                format: date
                          documents:
                            type: array
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                url:
                                  type: string
                      address:
                        type: object
                        properties:
                          status:
                            type: string
                            enum: [pending, approved, rejected]
                          submissionTime:
                            type: string
                            format: date-time
                          details:
                            type: object
                            properties:
                              flatNumber:
                                type: string
                              floor:
                                type: string
                              buildingName:
                                type: string
                              streetAddress:
                                type: string
                              district:
                                type: string
                              region:
                                type: string
                              docType:
                                type: string
                          documents:
                            type: array
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                url:
                                  type: string
                  accountHistory:
                    type: array
                    items:
                      type: object
                      properties:
                        timestamp:
                          type: string
                          format: date-time
                          description: Time of the action
                        action:
                          type: string
                          description: Description of the account action
                        status:
                          type: string
                          enum: [completed, approved, rejected]
                          description: Status of the action
                        reviewer:
                          type: string
                          description: Person or system who reviewed the action
                        comments:
                          type: string
                          description: Additional comments about the action
                required:
                  - userId
                  - username
                  - phoneNumber
                  - documentVerification
                  - accountHistory
              example:
                userId: "2"
                username: "janesmith"
                phoneNumber: "********"
                documentVerification:
                  passport:
                    status: "approved"
                    submissionTime: "2024-10-21T14:45:00Z"
                    details:
                      fullname: "Jane Smith"
                      chineseName: "史珍妮"
                      gender: "female"
                      idNumber: "P********1"
                      dateOfBirth: "1990-08-20"
                    documents:
                      - type: "idFront"
                        url: "https://example.com/documents/passport-proof.pdf"
                  hkid:
                    status: "pending"
                    submissionTime: "2024-10-22T10:30:00Z"
                    details:
                      fullname: "Jane Smith"
                      chineseName: "史珍妮"
                      gender: "female"
                      idNumber: "F2128671"
                      dateOfBirth: "1990-08-20"
                    documents:
                      - type: "idFront"
                        url: "https://example.com/documents/id-proof.png"
                      - type: "idBack"
                        url: "https://example.com/documents/id-proof-back.png"
                  address:
                    status: "approved"
                    submissionTime: "2024-10-21T15:30:00Z"
                    details:
                      flatNumber: "456"
                      floor: "10"
                      buildingName: "Ocean Towers"
                      streetAddress: "78 Harbour Road"
                      district: "Wan Chai"
                      region: "Hong Kong Island"
                      docType: "bankStatement"
                    documents:
                      - type: "bankStatement"
                        url: "https://example.com/documents/address-proof.pdf"
                accountHistory:
                  - timestamp: "2024-02-20T11:30:00"
                    action: "護照驗證通過"
                    status: "approved"
                    reviewer: "Verification Team"
                    comments: "護照已驗證通過"
                  - timestamp: "2024-02-25T14:15:00"
                    action: "地址驗證通過"
                    status: "approved"
                    reviewer: "Verification Team"
                    comments: "地址證明文件已驗證通過"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_REQUEST"
                  message: "Invalid request parameters"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "Verification not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/managers:
    get:
      tags:
        - admin manager management
      summary: Get managers list
      description: >-
        Get a list of all managers organized by organizations. This endpoint returns organizations 
        with their respective managers. Each organization includes its ID, name, 
        and a list of managers belonging to that organization.
      operationId: getManagers
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Managers"
              example:
                organizations:
                  - organizationId: "1"
                    name: "同行明天"
                    managers:
                      - managerId: "2"
                        name: "Jane Smith"
                      - managerId: "4"
                        name: "Sarah Johnson"
                  - organizationId: "2"
                    name: "沙田西關愛隊"
                    managers:
                      - managerId: "1"
                        name: "John Doe"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/managers/{managerId}/permissions:
    get:
      tags:
        - admin manager management
      summary: Get manager permissions
      description: Get the permissions for a specific manager
      operationId: getManagerPermissions
      parameters:
        - name: managerId
          in: path
          required: true
          schema:
            type: string
          description: Manager ID
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ManagerPermissions"
              example:
                managerId: "1"
                name: "John Doe"
                phoneNumber: "********"
                organization: "沙田西關愛隊"
                avatar: "https://api.dicebear.com/7.x/miniavs/svg?seed=john"
                permissions:
                  userManagement:
                    approve: false
                    edit: false
                    delete: false
                  volunteerManagement:
                    volunteerApprove: true
                    eventApprove: true
                    volunteerAttendance: true
                  eventManagement:
                    create: true
                    edit: true
                    delete: false
                    attendance: true
                  contentManagement:
                    create: true
                    edit: true
                    delete: false
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "Manager not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /user/{id}/profile:
    get:
      tags:
        - user
      summary: Get user profile information
      description: >-
        Get user profile information.

        用於在網頁右上角Profile的頁面，提供用戶的基本資訊以及使用line charts及bar charts展示活動參與歷史的詳細數據。

        - Line charts用於展示用戶近12個月的活動數據，移動端由於畫幅限制，只展示用戶近6個月的活動數據，後續通過討論進行統一標準。

        - Bar charts用於展示用戶的活動組成category的區別，例如用戶參與三次Business，兩次Technology的活動。


        注意：此處 events 是用於展示用戶每月的活動數據Line Charts，如果可以返回前端更高效的數組（按月區分的活動數量）更好，我會根據返回數據重新調整畫圖數據結構。
      operationId: getUserProfile
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: user id
      responses:
        "200":
          description: success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserProfile"
              example:
                personalInfo:
                  id: "1"
                  firstName: "Test"
                  lastName: "Admin"
                  phoneNumber: "12345678"
                  gender: "male"
                  dateOfBirth: "1990-01-01"
                  username: "Test Username"
                  chineseName: "測試管理員"
                activityHistory:
                  events:
                    - id: "1"
                      title: "React Summit 2024"
                      time: "2025-01-05T00:00:00Z"
                      type: "Conference"
                      role: "participant"
                    - id: "2"
                      title: "Web Development Workshop"
                      time: "2025-01-15T00:00:00Z"
                      type: "Workshop"
                      role: "volunteer"
                    - id: "3"
                      title: "Tech Meetup"
                      time: "2025-01-25T00:00:00Z"
                      type: "Meetup"
                      role: "participant"
                  statistics:
                    participantHours: 20
                    volunteerHours: 15
                    totalEvents: 3
        "403":
          description: forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "404":
          description: not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "User not found"
        "500":
          description: server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /user/{id}/settings:
    get:
      tags:
        - user
      summary: Get user settings
      description: >-
        Get user settings information.


        此處是用戶設定頁面的四個標籤頁（資料更改 Info Change、偏好設定 Preferences、身份驗證 Identity Verification、申請計劃 Program Applications）的整合 API。

        - 資料更改
          - 用戶名更改
          - 個人資料顯示 （禁止更改，僅展示，通過account recovery request修改）
          - 電話號碼顯示及更改
        - 偏好設定
          - 通知偏好設定
            - 應用內通知
            - whatsapp 通知
        - 身份驗證
          - 香港身份證狀態及申請按鈕
          - 護照狀態及申請按鈕
          - 內地出入境證件狀態及申請按鈕，此驗證類型包含回鄉證及內地居民往來港澳通行證。
          - HKYouth+ 驗證狀態及申請按鈕
        - 申請計劃
          - 家訪申請狀態及申請按鈕
          - 義工申請狀態及申請按鈕

        若後續認為分開獲取各標籤頁資料更為合適（如資料量過大或更新頻率控制），請告知 :]
      operationId: getUserSettings
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserSettings"
              example:
                id: "1"
                username: "testuser"
                title: "Mr"
                firstName: "Test"
                lastName: "User"
                phoneNumber: "12345678"
                gender: "male"
                dateOfBirth: "1990-01-01"
                chineseName: "測試用戶"
                role: "user"
                documentVerification:
                  hkid:
                    status: "approved"
                    comment: null
                  mainlandTravelPermit:
                    status: "pending"
                    comment: null
                  passport:
                    status: "rejected"
                    comment: "Document unclear"
                  hkyouth:
                    status: "pending"
                    comment: null
                  address:
                    status: "approved"
                    comment: null
                applications:
                  homeVisit:
                    status: "approved"
                    comment: null
                  volunteer:
                    status: "pending"
                    comment: null
                notificationSettings:
                  inApp: true
                  whatsapp: false
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "Authentication required"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "FORBIDDEN"
                  message: "You don't have permission to access this resource"
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "NOT_FOUND"
                  message: "User not found"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/reports/events:
    get:
      summary: Get events report data
      description: |
        返回參與者數量最多的熱門活動及對應type的統計數據。請參考 https://full-pages-development-mode.vercel.app/events-management 需要登錄admin賬號。

        數量限制可由你決定，前端做了可擴展性，可以容納很多數據，並帶有靜態搜索功能。
         
        此 API 提供兩個主要數據結構：
        1. `categoryStats`：各類型的熱門活動的數量統計，以數組形式返回，每個元素包含活動類型名稱和對應數量
        2. `topEvents`：按參與者數量排序的熱門活動列表，包含活動 ID、標題和參與者數量

        如果未提供日期範圍參數（`startDate` 和 `endDate` 均為 null），返回所有時間範圍內的數據。
        如果提供了日期範圍，則返回指定時間段內的活動統計數據。
      tags:
        - admin events report
      parameters:
        - in: query
          name: startDate
          schema:
            type: string
            format: date-time
          description: 報告期間的開始日期，使用 ISO 8601 格式（例如：2025-01-01T00:00:00Z）。若不提供，則使用所有可用數據的最早日期。
          required: false
        - in: query
          name: endDate
          schema:
            type: string
            format: date-time
          description: 報告期間的結束日期，使用 ISO 8601 格式（例如：2025-01-31T23:59:59Z）。若不提供，則使用所有可用數據的最晚日期。
          required: false
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  categoryStats:
                    type: array
                    description: 各類型活動的數量統計
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                          description: 活動類型名稱
                        value:
                          type: integer
                          description: 該類型活動的數量
                      required:
                        - name
                        - value
                  topEvents:
                    type: array
                    description: 按參與者數量排序的熱門活動列表
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          description: 活動 ID
                        title:
                          type: string
                          description: 活動標題
                        participants:
                          type: integer
                          description: 參與者數量
                      required:
                        - id
                        - title
                        - participants
                required:
                  - categoryStats
                  - topEvents
              example:
                categoryStats:
                  - name: "Workshop"
                    value: 15
                  - name: "Conference"
                    value: 8
                  - name: "Webinar"
                    value: 6
                  - name: "Networking"
                    value: 4
                  - name: "Training"
                    value: 3
                topEvents:
                  - id: "evt-12345"
                    title: "Annual Tech Conference 2025"
                    participants: 1250
                  - id: "evt-23456"
                    title: "Digital Marketing Workshop"
                    participants: 875
                  - id: "evt-34567"
                    title: "Data Science Bootcamp"
                    participants: 720
                  - id: "evt-45678"
                    title: "Leadership Summit"
                    participants: 680
                  - id: "evt-56789"
                    title: "Coding Hackathon"
                    participants: 550
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_DATE_RANGE"
                  message: "Invalid date range provided"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "User is not authorized to access this resource"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/reports/trends:
    get:
      summary: Get all events trends data
      description: |
        返回活動趨勢數據，以日期點數組形式呈現。請參考 https://full-pages-development-mode.vercel.app/events-management 需要登錄admin賬號。

        此 API 返回一個數組，每個元素包含特定日期的參與者和義工數量統計：
        - `date`：日期，使用 ISO 8601 格式（例如：2025-01-01T00:00:00Z）。
        - `Participants`：該日期的參與者數量
        - `Volunteers`：該日期的義工數量

        如果未提供日期範圍參數（`startDate` 和 `endDate` 均為 null 或未提供），API 將返回所有時間範圍內的趨勢數據。
        如果提供了日期範圍，則返回指定時間段內的每日趨勢數據。

        返回的數據可直接用於視覺化圖表顯示，無需額外處理。
      tags:
        - admin events report
      parameters:
        - in: query
          name: startDate
          schema:
            type: string
            format: date-time
          description: 趨勢數據的開始日期，使用 ISO 8601 格式（例如：2025-01-01T00:00:00Z）。若不提供，則使用所有可用數據的最早日期。
          required: false
        - in: query
          name: endDate
          schema:
            type: string
            format: date-time
          description: 趨勢數據的結束日期，使用 ISO 8601 格式（例如：2025-01-31T23:59:59Z）。若不提供，則使用所有可用數據的最晚日期。
          required: false
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/TrendDataPoint"
              example:
                - date: "2025-01-01T00:00:00Z"
                  Participants: 120
                  Volunteers: 15
                - date: "2025-01-02T00:00:00Z"
                  Participants: 145
                  Volunteers: 18
                - date: "2025-01-03T00:00:00Z"
                  Participants: 170
                  Volunteers: 22
                - date: "2025-01-04T00:00:00Z"
                  Participants: 210
                  Volunteers: 25
                - date: "2025-01-05T00:00:00Z"
                  Participants: 240
                  Volunteers: 28
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "INVALID_DATE_RANGE"
                  message: "Invalid date range provided"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "UNAUTHORIZED"
                  message: "User is not authorized to access this resource"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error:
                  code: "SERVER_ERROR"
                  message: "Internal server error"

  /admin/reports/event/statistics:
    get:
      tags:
        - admin events report
      summary: Get single event statistics
      description: |
        獲取單個活動的統計數據，包括參與者統計、義工統計、報名率以及人口統計數據。
        這些數據用於儀表板可視化，包括參與趨勢圖、年齡分佈和性別分佈的餅圖。

        请参考https://full-pages-development-mode.vercel.app/events/1/event-reports
      parameters:
        - name: eventId
          in: query
          required: true
          schema:
            type: string
          description: ID of the event to get statistics for
      responses:
        "200":
          description: 成功獲取活動統計數據
          content:
            application/json:
              schema:
                type: object
                properties:
                  statistics:
                    type: object
                    properties:
                      participants:
                        type: object
                        properties:
                          total:
                            type: integer
                            example: 1250
                            description: 參與者總數
                          change:
                            type: string
                            example: "125.5%"
                            description: 變化百分比（不包含+號，由前端添加）
                          changeType:
                            type: string
                            enum: [positive, negative]
                            example: "positive"
                            description: 變化類型（正面或負面）
                      volunteers:
                        type: object
                        properties:
                          total:
                            type: integer
                            example: 35
                            description: 義工總數
                          change:
                            type: string
                            example: "133.3%"
                            description: 變化百分比
                          changeType:
                            type: string
                            enum: [positive, negative]
                            example: "positive"
                            description: 變化類型
                      registration:
                        type: object
                        properties:
                          current:
                            type: integer
                            example: 1250
                            description: 當前註冊人數
                          maximum:
                            type: integer
                            example: 1500
                            description: 最大容量
                  demographics:
                    type: object
                    properties:
                      ageGroups:
                        type: array
                        description: 年齡組分佈
                        items:
                          type: object
                          properties:
                            name:
                              type: string
                              example: "under18"
                              description: 年齡組標識符（前端將應用翻譯）
                            amount:
                              type: integer
                              example: 200
                              description: 該年齡組的人數
                            share:
                              type: string
                              example: "16%"
                              description: 該年齡組佔總數的百分比
                      gender:
                        type: array
                        description: 性別分佈
                        items:
                          type: object
                          properties:
                            name:
                              type: string
                              example: "male"
                              description: 性別標識符（前端將應用翻譯）
                            amount:
                              type: integer
                              example: 750
                              description: 該性別的人數
                            share:
                              type: string
                              example: "60%"
                              description: 該性別佔總數的百分比
                  trendData:
                    type: array
                    description: 參與者和義工隨時間變化的趨勢數據
                    items:
                      type: object
                      properties:
                        date:
                          type: string
                          format: date
                          example: "2025-01-15"
                          description: 日期
                        Participants:
                          type: integer
                          example: 450
                          description: 當天的參與者數量
                        Volunteers:
                          type: integer
                          example: 15
                          description: 當天的義工數量
        "404":
          description: 找不到指定的活動
        "500":
          description: 服務器錯誤
    # Admin Reports Endpoints
  /admin/reports/event/attendance:
    get:
      tags:
        - admin events report
      summary: Get single event attendance records
      description: |
        獲取活動的出席記錄和參與者詳細資料，用於歷史數據標籤頁。
        支持按日期範圍和出席率進行過濾，支持虛擬滾動。


        请参考https://full-pages-development-mode.vercel.app/events/1/event-reports
      parameters:
        - name: eventId
          in: query
          required: true
          schema:
            type: string
          description: 要獲取出席記錄的活動 ID
        - name: dateRange
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
              format: date-time
            minItems: 2
            maxItems: 2
          description: 過濾的日期範圍，格式為 [startDate, endDate]
        - name: attendanceRate
          in: query
          required: false
          schema:
            type: string
            enum: [high, medium, low]
          description: 按出席率過濾 high >=80%, medium 50-79%, low <50% 
      responses:
        "200":
          description: 成功獲取活動出席記錄
          content:
            application/json:
              schema:
                type: object
                properties:
                  participants:
                    type: array
                    description: 參與者列表
                    items:
                      type: object
                      properties:
                        userId:
                          type: string
                          description: 用戶 ID
                        name:
                          type: string
                          description: 用戶姓名
                        chineseName:
                          type: string
                          description: 用戶中文姓名
                        gender:
                          type: string
                          enum: [Male, Female]
                          description: 性別
                        dateOfBirth:
                          type: string
                          format: date
                          description: 出生日期
                        phoneNumber:
                          type: string
                          description: 電話號碼
                        applicationDate:
                          type: string
                          format: date
                          description: 申請日期
                        attendanceRate:
                          type: number
                          minimum: 0
                          maximum: 100
                          description: 出席率百分比
                  total:
                    type: integer
                    description: 總記錄數
              example:
                participants:
                  - userId: "user-1"
                    name: "John Smith123"
                    chineseName: "李明"
                    gender: "Male"
                    dateOfBirth: "1985-06-15"
                    phoneNumber: "12345678"
                    applicationDate: "2025-01-15"
                    attendanceRate: 85
                  - userId: "user-2"
                    name: "Jane Wilson456"
                    chineseName: "王華"
                    gender: "Female"
                    dateOfBirth: "1990-03-22"
                    phoneNumber: "87654321"
                    applicationDate: "2025-01-16"
                    attendanceRate: 60
                total: 2
        "404":
          description: 找不到指定的活動
        "500":
          description: 服務器錯誤

  /admin/reports/event/attendance/export:
    post:
      tags:
        - admin events report
      summary: Export event attendance records
      description: |
        導出活動的出席記錄和參與者詳細資料，支持多種導出格式。
        
        可以選擇導出特定列，並根據指定的列順序進行導出。「columns」參數是一個有序數組，其中的元素順序決定了導出文件中列的排列順序。前端可以通過拖拽表格頭部來重新排序列，然後將當前顯示的列順序傳遞給後端進行導出。
        
        這種設計允許用戶通過以下步驟自定義導出：
        1. 使用列選擇器選擇需要顯示的列
        2. 通過拖放重新排列列的順序
        3. 選擇期望的導出格式（CSV、PDF 或 PNG）
        4. 觸發導出功能，系統將按照用戶定義的列順序生成導出文件
        
        後端應當嚴格遵循「columns」參數中定義的列順序，確保導出文件中的列與用戶在界面上看到的順序完全一致。
        

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - eventId
                - format
              properties:
                eventId:
                  type: string
                  description: 要導出出席記錄的活動 ID
                dateRange:
                  type: array
                  items:
                    type: string
                    format: date-time
                  minItems: 2
                  maxItems: 2
                  description: 過濾的日期範圍，格式為 [startDate, endDate]
                attendanceRate:
                  type: string
                  enum: [high, medium, low]
                  description: 按出席率過濾 high >=80%, medium 50-79%, low <50%
                columns:
                  type: array
                  items:
                    type: string
                    enum: [name, chineseName, gender, dateOfBirth, phoneNumber, attendanceRate, applicationDate]
                  description: 要導出的列，列在數組中的順序決定了導出文件中列的順序，確保與前端表格中的排列一致
                format:
                  type: string
                  enum: [csv, pdf, png]
                  description: 導出文件格式
              example:
                eventId: "event-123"
                dateRange: ["2025-01-01T00:00:00Z", "2025-03-31T23:59:59Z"]
                attendanceRate: "high"
                columns: ["gender", "name", "dateOfBirth", "attendanceRate"]
                format: "csv"
      responses:
        "200":
          description: 成功導出活動出席記錄
          content:
            application/csv:
              schema:
                type: string
                format: binary
            application/pdf:
              schema:
                type: string
                format: binary
            image/png:
              schema:
                type: string
                format: binary
        "404":
          description: 找不到指定的活動
        "500":
          description: 服務器錯誤

components:
  schemas:
    Organization:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        theme:
          type: string
          enum: [orange, green, blue, pink, all]
        logo:
          type: string
      required:
        - id
        - name
        - theme
        - logo

    BannerPost:
      type: object
      properties:
        id:
          type: string
          description: Banner ID
        title:
          type: string
          description: Banner title
        author:
          type: object
          properties:
            name:
              type: string
              description: Author name
            avatarUrl:
              type: string
              description: Author avatar URL
          required:
            - name
            - avatarUrl
        coverImage:
          type: string
          description: Banner cover image URL (Amazon S3 format)
      required:
        - id
        - title
        - author
        - coverImage

    UpcomingEvent:
      type: object
      properties:
        id:
          type: string
          description: Event ID
        title:
          type: string
          description: Event title
        startTime:
          type: string
          format: date-time
          description: Event start time
        coverImage:
          type: string
          description: Event cover image URL (Amazon S3 format)
      required:
        - id
        - title
        - startTime
        - coverImage

    PopularEvent:
      type: object
      properties:
        id:
          type: string
          description: Event ID
        title:
          type: string
          description: Event title
        startTime:
          type: string
          format: date-time
          description: Event start time
        location:
          type: string
          description: Event location
        coverImage:
          type: string
          description: Event cover image URL (Amazon S3 format)
      required:
        - id
        - title
        - startTime
        - location
        - coverImage

    EventsResponse:
      type: object
      properties:
        currentPage:
          type: integer
          description: Current page
        total:
          type: integer
          description: Total number of items
        events:
          type: array
          items:
            $ref: "#/components/schemas/Event"
          description: Events list
      required:
        - currentPage
        - total
        - events

    NewsItem:
      type: object
      properties:
        id:
          type: string
          description: News item ID
        title:
          type: string
          description: News title
        author:
          type: object
          properties:
            name:
              type: string
              description: Author name
          required:
            - name
        updatedAt:
          type: string
          format: date-time
          description: Last update time
        coverImage:
          type: string
          description: News cover image URL (Amazon S3 format)
      required:
        - id
        - title
        - author
        - updatedAt
        - coverImage

    Event:
      type: object
      properties:
        id:
          type: string
          description: Event ID
        title:
          type: string
          description: Event title
        type:
          type: string
          description: Event type
        description:
          type: string
          description: Event description
        startTime:
          type: string
          format: date-time
          description: Event start time
        location:
          type: string
          description: Event location
        participantsCount:
          type: integer
          description: Participant count
        maxParticipants:
          type: integer
          description: Maximum allowed participants
        price:
          type: number
          format: float
          description: Event price
        status:
          type: string
          enum: [Upcoming, Finished]
          description: Event status
        coverImage:
          type: string
          description: Event cover image URL (Amazon S3 format)
        isHidden:
          type: boolean
          description: Whether the event is hidden from public view
      required:
        - id
        - title
        - type
        - description
        - startTime
        - location
        - participantsCount
        - maxParticipants
        - price
        - status
        - coverImage
        - isHidden

    EventDetail:
      type: object
      allOf:
        - $ref: "#/components/schemas/Event"
        - type: object
          properties:
            endTime:
              type: string
              format: date-time
              description: Event end time
            maxWaitingList:
              type: integer
              description: Maximum allowed waiting list
            images:
              type: array
              items:
                type: string
              description: Additional event images
            video:
              type: object
              properties:
                url:
                  type: string
                  description: Video URL
              required:
                - url
            organizer:
              type: object
              properties:
                name:
                  type: string
                  description: Organizer name
                organizationName:
                  type: string
                  description: (Organizer company/ organization) name
              required:
                - name
                - organizationName
            identityRequired:
              type: object
              properties:
                hkid:
                  type: boolean
                  description: Whether HKID is required for participation
                passport:
                  type: boolean
                  description: Whether passport is required for participation
                mainlandTravelPermit:
                  type: boolean
                  description: Whether Mainland Travel Permit is required for participation
                hkyouth:
                  type: boolean
                  description: Whether HKYouth+ is required for participation

          required:
            - endTime
            - maxWaitingList
            - images
            - organizer
            - identityRequired

    UserEvent:
      type: object
      properties:
        id:
          type: string
          description: Event ID
        title:
          type: string
          description: Event title
        price:
          type: number
          format: float
          description: Event price
        startTime:
          type: string
          format: date-time
          description: Event start time
        location:
          type: string
          description: Event location
        participantsCount:
          type: integer
          description: Number of participants
        role:
          type: string
          enum: [participant, volunteer]
          description: User's role in the event
        coverImage:
          type: string
          description: Event cover image URL (Amazon S3 format)
        status:
          type: string
          enum: [upcoming, completed, absent, cancelled]
          description: Event participation status
      required:
        - id
        - title
        - price
        - startTime
        - location
        - participantsCount
        - role
        - coverImage
        - status

    UserEvents:
      type: object
      properties:
        events:
          type: array
          items:
            $ref: "#/components/schemas/UserEvent"
          description: List of events the user is registered for
      required:
        - events

    EventTicket:
      type: object
      properties:
        event:
          type: object
          properties:
            id:
              type: string
              description: Event ID
            title:
              type: string
              description: Event title
            startTime:
              type: string
              format: date-time
              description: Event start time
            endTime:
              type: string
              format: date-time
              description: Event end time
            location:
              type: string
              description: Event location
            price:
              type: number
              format: float
              description: Event price
            coverImage:
              type: string
              description: Event cover image URL (Amazon S3 format)
          required:
            - id
            - title
            - startTime
            - endTime
            - location
            - price
            - coverImage
        ticket:
          type: object
          properties:
            userName:
              type: string
              description: User name
            qrCodeData:
              type: string
              description: QR code data
            userId:
              type: string
              description: User ID
          required:
            - userName
            - qrCodeData
            - userId
      required:
        - event
        - ticket

    Author:
      type: object
      properties:
        name:
          type: string
        avatarUrl:
          type: string

    PostContent:
      type: object
      properties:
        type:
          type: string
          enum: [doc]
        content:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: [heading, paragraph, bulletList, listItem]
              attrs:
                type: object
                properties:
                  textAlign:
                    type: string
                    enum: [left, center, right]
                  level:
                    type: integer
                    minimum: 1
                    maximum: 6
              content:
                type: array
                items:
                  type: object
                  properties:
                    type:
                      type: string
                      enum: [text]
                    marks:
                      type: array
                      items:
                        type: object
                        properties:
                          type:
                            type: string
                            enum: [bold, italic]
                    text:
                      type: string

    Attachment:
      type: object
      properties:
        type:
          type: string
          enum: [video, pdf, doc, image]
        url:
          type: string
        name:
          type: string
      required:
        - type
        - url
        - name

    Post:
      type: object
      properties:
        id:
          type: string
          description: Post ID
        title:
          type: string
          description: Post title
        coverImage:
          type: string
          description: Optional URL to post cover image
        isHidden:
          type: boolean
          description: Whether the post is hidden from public view
          default: false
        author:
          type: object
          properties:
            name:
              type: string
              description: Author name
          required:
            - name
        updatedAt:
          type: string
          format: date-time
          description: Last update time
        content:
          type: object
          description: Post content in Prosemirror format
          properties:
            type:
              type: string
              enum: [doc]
            content:
              type: array
              items:
                type: object
                properties:
                  type:
                    type: string
                    enum: [heading, paragraph, bulletList, listItem]
                  attrs:
                    type: object
                    properties:
                      textAlign:
                        type: string
                        enum: [left, center, right]
                      level:
                        type: integer
                  content:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                          enum: [text]
                        marks:
                          type: array
                          items:
                            type: object
                            properties:
                              type:
                                type: string
                                enum: [bold, italic]
                        text:
                          type: string
        attachments:
          type: array
          description: List of post attachments
          items:
            type: object
            properties:
              type:
                type: string
                enum: [video, pdf, doc, image]
              url:
                type: string
              name:
                type: string
            required:
              - type
              - url
              - name
      required:
        - id
        - title
        - author
        - updatedAt

    PostDetail:
      type: object
      allOf:
        - $ref: "#/components/schemas/Post"
        - type: object
          properties:
            content:
              type: object
              description: Post content in rich text format
              properties:
                type:
                  type: string
                  description: Document type
                  example: "doc"
                content:
                  type: array
                  description: Array of content blocks
                  items:
                    type: object
            attachments:
              type: array
              description: Post attachments
              items:
                type: object
                properties:
                  type:
                    type: string
                    description: Attachment type (video, document, etc.)
                    example: "video"
                  url:
                    type: string
                    description: URL to the attachment
                  name:
                    type: string
                    description: Display name of the attachment
                required:
                  - type
                  - url
          required:
            - content
    Posts:
      type: object
      properties:
        posts:
          type: array
          items:
            $ref: "#/components/schemas/Post"
      required:
        - posts

    PostList:
      type: object
      properties:
        posts:
          type: array
          items:
            $ref: "#/components/schemas/Post"
        currentPage:
          type: integer
          minimum: 1
        total:
          type: integer
          minimum: 1
      required:
        - posts
        - currentPage
        - total

    Error:
      type: object
      properties:
        error:
          type: object
          properties:
            code:
              type: string
              description: Error code
            message:
              type: string
              description: Error message
          required:
            - code
            - message
      required:
        - error

    ResourceFile:
      type: object
      properties:
        id:
          type: string
          description: File ID
        type:
          type: string
          description: File type (e.g., pdf, docx, xlsx, txt, pptx, zip, jpg, mp4, csv)
        size:
          type: string
          description: File size with unit (e.g., "1.2 MB")
        fileName:
          type: string
          description: Name of the file
        downloadUrl:
          type: string
          description: URL to download the file
        description:
          type: string
          description: Optional description of the file
      required:
        - id
        - type
        - size
        - fileName
        - downloadUrl

    Resource:
      type: object
      properties:
        id:
          type: string
          description: Resource ID
        title:
          type: string
          description: Resource title
        description:
          type: string
          description: Resource description
        updatedAt:
          type: string
          format: date-time
          description: Last update time
        files:
          type: array
          items:
            $ref: "#/components/schemas/ResourceFile"
          description: List of files in this resource
      required:
        - id
        - title
        - updatedAt
        - files

    QualificationVolunteers:
      type: object
      properties:
        currentPage:
          type: integer
          description: Current page
        total:
          type: integer
          description: Total number of items
        data:
          type: array
          items:
            type: object
            properties:
              userId:
                type: string
                description: User ID
              name:
                type: string
                description: User name
              phoneNumber:
                type: string
                description: User phone number
              applicationTime:
                type: string
                format: date-time
                description: Application time
              status:
                type: string
                description: Application status
          required:
            - userId
            - name
            - phoneNumber
            - applicationTime
            - status
      required:
        - currentPage
        - total
        - data

    EventRegistrationVolunteers:
      type: object
      properties:
        currentPage:
          type: integer
          description: Current page
        total:
          type: integer
          description: Total number of items
        data:
          type: array
          items:
            type: object
            properties:
              userId:
                type: string
                description: User ID
              name:
                type: string
                description: User name
              phoneNumber:
                type: string
                description: User phone number
              applicationTime:
                type: string
                format: date-time
                description: Application time
              status:
                type: string
                description: Application status
              eventId:
                type: string
                description: Event ID
              eventName:
                type: string
                description: Event name
          required:
            - userId
            - name
            - phoneNumber
            - applicationTime
            - status
            - eventId
            - eventName
      required:
        - currentPage
        - total
        - data

    Verifications:
      type: object
      properties:
        currentPage:
          type: integer
          description: Current page
        total:
          type: integer
          description: Total number of items
        verifications:
          type: array
          items:
            type: object
            properties:
              userId:
                type: string
                description: User ID
              fullname:
                type: string
                description: User's full name
              chineseName:
                type: string
                description: User's Chinese name
              phoneNumber:
                type: string
                description: User phone number
              documentVerification:
                type: object
                properties:
                  hkid:
                    type: object
                    properties:
                      status:
                        type: string
                        enum: [pending, approved, rejected]
                      submissionTime:
                        type: string
                        format: date-time
                  address:
                    type: object
                    properties:
                      status:
                        type: string
                        enum: [pending, approved, rejected]
                      submissionTime:
                        type: string
                        format: date-time
              submissionTime:
                type: string
                format: date-time
                description: Time of submission
          required:
            - userId
            - fullname
            - chineseName
            - phoneNumber
            - documentVerification
            - submissionTime
      required:
        - currentPage
        - total
        - verifications

    VerificationDetail:
      type: object
      properties:
        userId:
          type: string
          description: User ID
        fullname:
          type: string
          description: User's full name
        chineseName:
          type: string
          description: User's Chinese name
        phoneNumber:
          type: string
          description: User phone number
        documentVerification:
          type: object
          properties:
            hkid:
              type: object
              properties:
                status:
                  type: string
                  enum: [pending, approved, rejected]
                submissionTime:
                  type: string
                  format: date-time
            address:
              type: object
              properties:
                status:
                  type: string
                  enum: [pending, approved, rejected]
                submissionTime:
                  type: string
                  format: date-time
        submissionTime:
          type: string
          format: date-time
          description: Time of submission
        verifications:
          type: array
          items:
            type: object
            properties:
              infoCategory:
                type: string
                description: Category of verification information
              submissionTime:
                type: string
                format: date-time
                description: Time of submission
              details:
                type: object
                description: Verification details specific to the category
              status:
                type: string
                description: Verification status
              documents:
                type: array
                items:
                  type: object
                  properties:
                    type:
                      type: string
                      description: Document type
                    url:
                      type: string
                      description: Document URL
        accountHistory:
          type: array
          items:
            type: object
            properties:
              timestamp:
                type: string
                format: date-time
                description: Time of action
              action:
                type: string
                description: Description of the account action
              status:
                type: string
                description: Status of the action
              reviewer:
                type: string
                description: Person or system who reviewed the action
              comments:
                type: string
                description: Additional comments about the action
      required:
        - userId
        - fullname
        - chineseName
        - phoneNumber
        - documentVerification
        - submissionTime
        - verifications
        - accountHistory

    Managers:
      type: object
      properties:
        organizations:
          type: array
          items:
            type: object
            properties:
              organizationId:
                type: string
                description: Organization ID
              name:
                type: string
                description: Organization name
              managers:
                type: array
                items:
                  type: object
                  properties:
                    managerId:
                      type: string
                      description: Manager ID
                    name:
                      type: string
                      description: Manager name
          required:
            - organizationId
            - name
            - managers
      required:
        - organizations

    ManagerPermissions:
      type: object
      properties:
        managerId:
          type: string
          description: Manager ID
        name:
          type: string
          description: Manager name
        phoneNumber:
          type: string
          description: Manager phone number
        organization:
          type: string
          description: Manager's organization name
        avatar:
          type: string
          description: Manager's avatar URL
        permissions:
          type: object
          properties:
            userManagement:
              type: object
              properties:
                approve:
                  type: boolean
                  description: Can approve users
                edit:
                  type: boolean
                  description: Can edit users
                delete:
                  type: boolean
                  description: Can delete users
              required:
                - approve
                - edit
                - delete
            volunteerManagement:
              type: object
              properties:
                volunteerApprove:
                  type: boolean
                  description: Can approve volunteers
                eventApprove:
                  type: boolean
                  description: Can approve volunteer events
                volunteerAttendance:
                  type: boolean
                  description: Can track volunteer attendance
              required:
                - volunteerApprove
                - eventApprove
                - volunteerAttendance
            eventManagement:
              type: object
              properties:
                create:
                  type: boolean
                  description: Can create events
                edit:
                  type: boolean
                  description: Can edit events
                delete:
                  type: boolean
                  description: Can delete events
                attendance:
                  type: boolean
                  description: Can view event attendance
              required:
                - create
                - edit
                - delete
                - attendance
            contentManagement:
              type: object
              properties:
                create:
                  type: boolean
                  description: Can create content
                edit:
                  type: boolean
                  description: Can edit content
                delete:
                  type: boolean
                  description: Can delete content
              required:
                - create
                - edit
                - delete
          required:
            - userManagement
            - volunteerManagement
            - eventManagement
            - contentManagement
      required:
        - managerId
        - name
        - permissions

    PermissionsObject:
      type: object
      properties:
        userManagement:
          type: object
          properties:
            approve:
              type: boolean
              description: Can approve users
            edit:
              type: boolean
              description: Can edit users
            delete:
              type: boolean
              description: Can delete users
          required:
            - approve
            - edit
            - delete
        volunteerManagement:
          type: object
          properties:
            volunteerApprove:
              type: boolean
              description: Can approve volunteers
            eventApprove:
              type: boolean
              description: Can approve volunteer events
            volunteerAttendance:
              type: boolean
              description: Can track volunteer attendance
          required:
            - volunteerApprove
            - eventApprove
            - volunteerAttendance
        eventManagement:
          type: object
          properties:
            create:
              type: boolean
              description: Can create events
            edit:
              type: boolean
              description: Can edit events
            delete:
              type: boolean
              description: Can delete events
            attendance:
              type: boolean
              description: Can view event attendance
          required:
            - create
            - edit
            - delete
            - attendance
        contentManagement:
          type: object
          properties:
            create:
              type: boolean
              description: Can create content
            edit:
              type: boolean
              description: Can edit content
            delete:
              type: boolean
              description: Can delete content
          required:
            - create
            - edit
            - delete
      required:
        - userManagement
        - volunteerManagement
        - eventManagement
        - contentManagement

    UserProfile:
      type: object
      properties:
        personalInfo:
          type: object
          properties:
            id:
              type: string
              description: 用戶 ID
            firstName:
              type: string
              description: 用戶名字
            lastName:
              type: string
              description: 用戶姓氏
            phoneNumber:
              type: string
              description: 用戶電話號碼
            gender:
              type: string
              enum: [male, female]
              description: 用戶性別
            dateOfBirth:
              type: string
              format: date
              description: 用戶出生日期
            username:
              type: string
              description: 用戶名稱
            chineseName:
              type: string
              description: 用戶中文名稱
          required:
            - id
            - firstName
            - lastName
            - phoneNumber
            - gender
            - dateOfBirth
            - username
        activityHistory:
          type: object
          properties:
            events:
              type: array
              description: 用戶參與的活動列表
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 活動 ID
                  title:
                    type: string
                    description: 活動標題
                  time:
                    type: string
                    format: date-time
                    description: 活動時間
                  type:
                    type: string
                    description: 活動類型
                  role:
                    type: string
                    enum: [participant, volunteer]
                    description: 用戶在活動中的角色
                required:
                  - id
                  - title
                  - time
                  - type
                  - role
            statistics:
              type: object
              description: 用戶活動參與統計數據
              properties:
                participantHours:
                  type: integer
                  description: 作為參與者的總小時數
                volunteerHours:
                  type: integer
                  description: 作為義工的總小時數
                totalEvents:
                  type: integer
                  description: 參與的總活動數量
              required:
                - participantHours
                - volunteerHours
                - totalEvents
          required:
            - events
            - statistics
      required:
        - personalInfo
        - activityHistory

    UserSettings:
      type: object
      properties:
        id:
          type: string
          description: User ID
        username:
          type: string
          description: Username
        title:
          type: string
          enum: [Mr, Mrs, Ms, Dr, Prof]
          description: User title
        firstName:
          type: string
          description: User first name
        lastName:
          type: string
          description: User last name
        phoneNumber:
          type: string
          description: User phone number
        gender:
          type: string
          enum: [male, female]
          description: User gender
        dateOfBirth:
          type: string
          format: date
          description: User date of birth
        chineseName:
          type: string
          description: User Chinese name
        role:
          type: string
          enum: [user, manager, admin]
          description: User role
        documentVerification:
          type: object
          properties:
            hkid:
              $ref: "#/components/schemas/DocumentVerificationStatus"
            mainlandTravelPermit:
              $ref: "#/components/schemas/DocumentVerificationStatus"
            passport:
              $ref: "#/components/schemas/DocumentVerificationStatus"
            hkyouth:
              $ref: "#/components/schemas/DocumentVerificationStatus"
            address:
              $ref: "#/components/schemas/DocumentVerificationStatus"
          required:
            - hkid
            - mainlandTravelPermit
            - passport
            - hkyouth
            - address
        applications:
          type: object
          properties:
            homeVisit:
              $ref: "#/components/schemas/ApplicationStatus"
            volunteer:
              $ref: "#/components/schemas/ApplicationStatus"
          required:
            - homeVisit
            - volunteer
        notificationSettings:
          type: object
          properties:
            inApp:
              type: boolean
              description: In-app notifications enabled
            whatsapp:
              type: boolean
              description: WhatsApp notifications enabled
          required:
            - inApp
            - whatsapp
      required:
        - id
        - username
        - firstName
        - lastName
        - phoneNumber
        - gender
        - dateOfBirth
        - role
        - documentVerification
        - applications
        - notificationSettings

    DocumentVerificationStatus:
      type: object
      properties:
        status:
          type: string
          enum: [unverifed, pending, approved, rejected]
          description: Document verification status
        comment:
          type: string
          nullable: true
          description: Reason for rejection if status is rejected
      required:
        - status

    ApplicationStatus:
      type: object
      properties:
        status:
          type: string
          enum: [unverified, pending, approved, rejected]
          description: Application status
        comment:
          type: string
          nullable: true
          description: Reason for rejection if status is rejected
      required:
        - status

    EventsReport:
      type: object
      properties:
        totalEvents:
          type: integer
          description: Total number of events
        totalParticipants:
          type: integer
          description: Total number of participants across all events
        averageParticipants:
          type: number
          format: float
          description: Average number of participants per event
        categoryStats:
          type: array
          description: Count of events by type
          items:
            type: object
            properties:
              name:
                type: string
                description: Event type name
              value:
                type: integer
                description: Count of events of this type
        topEvents:
          type: array
          description: Top events by number of participants
          items:
            type: object
            properties:
              id:
                type: string
                description: Event ID
              title:
                type: string
                description: Event title
              participants:
                type: integer
                description: Number of participants
      required:
        - categoryStats
        - topEvents

    TrendDataPoint:
      type: object
      properties:
        date:
          type: string
          format: date-time
          description: Date in ISO 8601 format (e.g. 2025-01-01T00:00:00Z)
        Participants:
          type: integer
          description: Number of participants on that date
        Volunteers:
          type: integer
          description: Number of volunteers on that date
      required:
        - date
        - Participants
        - Volunteers
