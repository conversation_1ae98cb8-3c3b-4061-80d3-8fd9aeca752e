/**
 * @typedef {Object} JsonContent
 * @property {Array<Object>} ops - Array of rich text operations (e.g., from a Quill editor).
 */

/**
 * @typedef {Object} Tag
 * @property {string} id
 * @property {string} name
 * @property {string} [description] - Optional description
 */

/**
 * @typedef {Object} MediaItem
 * @property {string} id
 * @property {string} event_id
 * @property {string} media_type // e.g., 'image', 'video_url'
 * @property {string} url
 * @property {string} [caption]
 * @property {string} created_at
 */

/**
 * @typedef {Object} VerificationType
 * @property {string} type_key // e.g., 'hkid', 'address_proof'
 * @property {string} name // Display name for the verification type
 * @property {string} [description]
 */

/**
 * @typedef {Object} Event
 * @property {string} id
 * @property {string} organization_id
 * @property {string} title
 * @property {string} slug
 * @property {JsonContent} JsonContent - Rich text content for the event description
 * @property {'physical' | 'online' | 'hybrid'} location_type
 * @property {string} [location_full_address]
 * @property {string} [location_online_url]
 * @property {string} start_time - ISO 8601 date-time string
 * @property {string} end_time - ISO 8601 date-time string
 * @property {string} timezone - e.g., "Asia/Hong_Kong"
 * @property {number} participant_limit
 * @property {number} [current_participants]
 * @property {number} waitlist_limit
 * @property {number} [current_waitlisted]
 * @property {boolean} requires_approval_for_registration
 * @property {Tag[]} [tags] - Array of tag objects
 * @property {string[]} [government_funding_type_ids] - Array of UUIDs
 * @property {'draft' | 'upcoming' | 'ongoing' | 'past' | 'cancelled' | 'hidden'} status
 * @property {boolean} [isHidden] // Derived or explicit, for admin view filtering
 * @property {MediaItem[]} [media_items]
 * @property {VerificationType[]} [required_verification_types]
 * @property {string} created_at - ISO 8601 date-time string
 * @property {string} last_updated_timestamp - ISO 8601 date-time string
 * @property {number} [registrations_count] // From admin list view
 * @property {number} [volunteers_count] // From admin list view
 */

/**
 * @typedef {Object} EventCreationPayload
 * @property {string} title
 * @property {JsonContent} JsonContent
 * @property {'physical' | 'online' | 'hybrid'} location_type
 * @property {string} [location_full_address]
 * @property {string} [location_online_url]
 * @property {string} start_time // ISO 8601 date-time string
 * @property {string} end_time // ISO 8601 date-time string
 * @property {string} timezone
 * @property {number} participant_limit
 * @property {number} waitlist_limit
 * @property {boolean} requires_approval_for_registration
 * @property {string[]} [tag_ids] - Array of tag UUIDs
 * @property {string[]} [government_funding_type_ids] - Array of UUIDs for funding types
 * @property {'draft' | 'upcoming'} [status] - Initial status, defaults to draft or upcoming
 */

/**
 * @typedef {Object} EventUpdatePayload
 * @property {string} [title]
 * @property {JsonContent} [JsonContent]
 * @property {'physical' | 'online' | 'hybrid'} [location_type]
 * @property {string} [location_full_address]
 * @property {string} [location_online_url]
 * @property {string} [start_time] // ISO 8601 date-time string
 * @property {string} [end_time] // ISO 8601 date-time string
 * @property {string} [timezone]
 * @property {number} [participant_limit]
 * @property {number} [waitlist_limit]
 * @property {boolean} [requires_approval_for_registration]
 * @property {string[]} [tag_ids]
 * @property {string[]} [government_funding_type_ids]
 * @property {'draft' | 'upcoming' | 'ongoing' | 'past' | 'cancelled' | 'hidden'} [status]
 */

/**
 * @typedef {Object} EventStatusUpdatePayload
 * @property {'draft' | 'upcoming' | 'ongoing' | 'past' | 'cancelled' | 'hidden'} new_status
 */

/**
 * @typedef {Object} Registration
 * @property {string} id
 * @property {string} event_id
 * @property {string} user_id
 * @property {string} user_display_name
 * @property {string} user_email
 * @property {'pending' | 'approved' | 'rejected' | 'cancelled_by_user' | 'waitlisted'} status
 * @property {string} registration_timestamp
 * @property {boolean} [attended]
 * @property {string} [attendance_timestamp]
 * @property {'pending' | 'paid' | 'failed' | 'refunded'} [payment_status]
 * @property {string} [payment_timestamp]
 * @property {Object} [answers_to_custom_questions] // if any custom questions were asked
 */

/**
 * @typedef {Object} VolunteerApplication
 * @property {string} id
 * @property {string} event_id
 * @property {string} user_id
 * @property {string} user_display_name
 * @property {string} user_email
 * @property {'pending' | 'approved' | 'rejected'} review_status
 * @property {string} [review_notes]
 * @property {string} application_timestamp
 * @property {string} [reviewed_at]
 * @property {string} [reviewed_by_staff_id]
 */

/**
 * @typedef {Object} TagResponse
 * @property {string} id - UUID of the tag.
 * @property {string} tag_name - Name of the tag.
 * @property {string} language_code - Language code (e.g., "en", "zh_HK").
 * @property {string} [description] - Optional description of the tag.
 * @property {boolean} is_globally_approved - Whether the tag is globally approved.
 * @property {string} created_at - ISO 8601 date-time string.
 * @property {string} updated_at - ISO 8601 date-time string.
 */

/**
 * @typedef {Object} CreateEventTagRequest
 * @property {string} tag_name - Name of the tag, required.
 * @property {string} language_code - Language code (e.g., "en", "zh_HK"), required.
 * @property {string} [description] - Optional description of the tag.
 */

/**
 * @typedef {Object} GovernmentFundingTypeResponse
 * @property {string} id - UUID of the funding type.
 * @property {string} name - Name of the funding type.
 * @property {string} description - Description of the funding type.
 */

/**
 * @typedef {Object} MediaItemResponse
 * @property {string} id - UUID of the media item.
 * @property {string} event_id - UUID of the event.
 * @property {string} file_name - Original file name.
 * @property {string} file_path - Server path to the file.
 * @property {string} file_type - MIME type of the file.
 * @property {number} file_size - Size of the file in bytes.
 * @property {string} uploaded_at - ISO 8601 date-time string.
 * @property {number} [display_order] - Optional display order for the media item.
 */

/**
 * @typedef {Object} CreateEventRequest
 * @property {string} title - Required.
 * @property {JsonContent} JsonContent - Rich text content for the event description, can be any valid JSON.
 * @property {'physical' | 'online' | 'hybrid'} location_type - Required.
 * @property {string} [location_full_address] - Required if location_type is physical or hybrid.
 * @property {string} [location_online_url] - Required if location_type is online or hybrid.
 * @property {string} start_time - ISO 8601 date-time string, required.
 * @property {string} end_time - ISO 8601 date-time string, required.
 * @property {string} timezone - e.g., "Asia/Hong_Kong", required.
 * @property {string} [price] - e.g., "10.99", "0.00" for free.
 * @property {string} [contact_email] - Contact email for the event.
 * @property {string} [contact_phone] - Contact phone for the event.
 * @property {number} participant_limit - Maximum number of participants, required.
 * @property {number} waitlist_limit - Maximum number of waitlisted, required.
 * @property {boolean} requires_approval_for_registration - Whether registration requires approval, required.
 * @property {string[]} [tag_ids] - Array of tag UUIDs.
 * @property {string[]} [government_funding_type_ids] - Array of funding type UUIDs.
 * @property {string[]} [required_verification_type_keys] - Array of verification type keys.
 */

/**
 * @typedef {Object} UpdateEventRequest
 * @property {string} [title]
 * @property {JsonContent} [JsonContent] - Rich text content for the event description.
 * @property {'physical' | 'online' | 'hybrid'} [location_type]
 * @property {string} [location_full_address]
 * @property {string} [location_online_url]
 * @property {string} [start_time] - ISO 8601 date-time string.
 * @property {string} [end_time] - ISO 8601 date-time string.
 * @property {string} [timezone] - e.g., "Asia/Hong_Kong".
 * @property {string} [price] - e.g., "10.99", "0.00" for free.
 * @property {string} [contact_email]
 * @property {string} [contact_phone]
 * @property {number} [participant_limit]
 * @property {number} [waitlist_limit]
 * @property {boolean} [requires_approval_for_registration]
 * @property {string[]} [tag_ids] - Array of tag UUIDs.
 * @property {string[]} [government_funding_type_ids] - Array of funding type UUIDs.
 * @property {string[]} [required_verification_type_keys] - Array of verification type keys.
 */

/**
 * @typedef {Object} EventResponse
 * @property {string} id - UUID of the event.
 * @property {string} organization_id - UUID of the organization.
 * @property {string} organization_name - Name of the organization.
 * @property {string} title - Title of the event.
 * @property {JsonContent} JsonContent - Rich text content for the event description.
 * @property {'physical' | 'online' | 'hybrid'} location_type
 * @property {string} [location_full_address]
 * @property {string} [location_online_url]
 * @property {string} start_time - ISO 8601 date-time string.
 * @property {string} end_time - ISO 8601 date-time string.
 * @property {string} timezone - e.g., "Asia/Hong_Kong".
 * @property {string} [price] - e.g., "10.99", "0.00" for free.
 * @property {string} [contact_email]
 * @property {string} [contact_phone]
 * @property {'draft' | 'upcoming' | 'ongoing' | 'past' | 'cancelled'} status
 * @property {number} participant_limit
 * @property {number} waitlist_limit
 * @property {boolean} requires_approval_for_registration
 * @property {string} created_by_user_id - UUID of the creator.
 * @property {string} [published_at] - ISO 8601 date-time string, if published.
 * @property {string} created_at - ISO 8601 date-time string.
 * @property {string} updated_at - ISO 8601 date-time string.
 * @property {TagResponse[]} tags - Array of tag objects.
 * @property {GovernmentFundingTypeResponse[]} government_funding_types - Array of funding type objects.
 * @property {string[]} required_verification_type_keys - Array of verification type keys.
 * @property {string[]} banner_image_urls - Array of banner image URLs.
 * @property {MediaItemResponse[]} media_items - Array of media item objects.
 * @property {number} [total_registrations] - Admin-only field.
 * @property {number} [total_waitlisted] - Admin-only field.
 * @property {number} [total_attended] - Admin-only field.
 * @property {string} [current_user_registration_status] - e.g., "registered", "waitlisted", "attended".
 * @property {string} [current_user_registration_id] - UUID of the user's registration, if any.
 */

/**
 * @typedef {Object} PublicEventResponse
 * @property {string} id - UUID of the event.
 * @property {string} organization_id - UUID of the organization.
 * @property {string} organization_name - Name of the organization.
 * @property {string} title - Title of the event.
 * @property {JsonContent} JsonContent - Rich text content for the event description.
 * @property {'physical' | 'online' | 'hybrid'} location_type
 * @property {string} [location_full_address]
 * @property {string} [location_online_url]
 * @property {string} start_time - ISO 8601 date-time string.
 * @property {string} end_time - ISO 8601 date-time string.
 * @property {string} timezone - e.g., "Asia/Hong_Kong".
 * @property {string} [price] - e.g., "10.99", "0.00" for free.
 * @property {'upcoming' | 'ongoing' | 'past' | 'cancelled'} status
 * @property {number} participant_limit
 * @property {string} [published_at] - ISO 8601 date-time string, if published.
 * @property {TagResponse[]} tags - Array of tag objects.
 * @property {GovernmentFundingTypeResponse[]} government_funding_types - Array of funding type objects.
 * @property {string[]} banner_image_urls - Array of banner image URLs.
 * @property {MediaItemResponse[]} media_items - Array of select media items.
 * @property {string} [current_user_registration_status] - e.g., "registered", "waitlisted", "attended".
 * @property {string} [current_user_registration_id] - UUID of the user's registration, if any.
 */

/**
 * @typedef {Object} EventStatusUpdateRequest
 * @property {'draft' | 'upcoming' | 'ongoing' | 'past' | 'cancelled'} new_status - Required.
 */

/**
 * @typedef {Object} EventRegistrationResponse
 * @property {string} id - UUID of the registration.
 * @property {string} event_id - UUID of the event.
 * @property {string} user_id - UUID of the user.
 * @property {'registered' | 'waitlisted' | 'approved' | 'rejected' | 'attended' | 'absent'} status
 * @property {'pending' | 'paid' | 'failed' | 'refunded'} payment_status
 * @property {'participant' | 'volunteer'} registration_role
 * @property {string} registered_at - ISO 8601 date-time string.
 * @property {string} [attended_at] - ISO 8601 date-time string, if attended.
 * @property {number} [waitlist_priority] - Priority in the waitlist.
 * @property {string} created_at - ISO 8601 date-time string.
 * @property {string} updated_at - ISO 8601 date-time string.
 * @property {string} [cancellation_reason_by_user] - Reason for cancellation, if any.
 * @property {string} [admin_notes_on_registration] - Admin notes, if any.
 * @property {string} event_title - Title of the event.
 * @property {string} event_start_time - ISO 8601 date-time string.
 * @property {string} user_display_name - Display name of the user.
 * @property {string} [user_email] - Email of the user.
 * @property {string} [user_phone] - Phone of the user.
 */

/**
 * @typedef {Object} UpdateEventRegistrationStatusRequest
 * @property {'approved' | 'rejected' | 'attended' | 'absent'} new_status - Required.
 * @property {string} [admin_notes] - Optional admin notes.
 */

/**
 * @typedef {Object} UpdateEventRegistrationPaymentRequest
 * @property {'pending' | 'paid' | 'failed' | 'refunded'} payment_status - Required.
 */

/**
 * @typedef {Object} MarkAttendanceResponse
 * @property {string} registration_id - UUID of the registration.
 * @property {string} event_id - UUID of the event.
 * @property {string} user_id - UUID of the user.
 * @property {'attended'} status
 * @property {string} attended_at - ISO 8601 date-time string.
 * @property {string} message - Success message.
 * @property {'pending' | 'paid' | 'failed' | 'refunded'} payment_status
 */

/**
 * @typedef {Object} EventVolunteerApplicationResponse
 * @property {string} id - UUID of the application.
 * @property {string} event_id - UUID of the event.
 * @property {string} user_id - UUID of the user.
 * @property {'pending' | 'approved' | 'rejected'} status
 * @property {string} [notes_by_applicant] - Notes from the applicant.
 * @property {string} [admin_review_notes] - Admin review notes.
 * @property {string} applied_at - ISO 8601 date-time string.
 * @property {string} [reviewed_at] - ISO 8601 date-time string, if reviewed.
 * @property {string} [reviewed_by_user_id] - UUID of the reviewer, if reviewed.
 * @property {string} created_at - ISO 8601 date-time string.
 * @property {string} updated_at - ISO 8601 date-time string.
 * @property {string} event_title - Title of the event.
 * @property {string} event_start_time - ISO 8601 date-time string.
 * @property {string} user_display_name - Display name of the user.
 * @property {string} [user_email] - Email of the user.
 * @property {string} [user_phone] - Phone of the user.
 */

/**
 * @typedef {Object} ReviewEventVolunteerApplicationRequest
 * @property {'approved' | 'rejected'} new_status - Required.
 * @property {string} [admin_notes] - Optional admin notes.
 */

/**
 * @typedef {Object} EventCheckInRequest
 * @property {string} user_id - UUID of the user to check in, required.
 */

/**
 * @typedef {Object} ListPublicEventsRequest
 * @property {string} [orgId] - UUID of the organization to filter by.
 * @property {string} [startDate] - ISO 8601 Date (YYYY-MM-DD) to filter start date.
 * @property {string} [endDate] - ISO 8601 Date (YYYY-MM-DD) to filter end date.
 * @property {string} [searchTerm] - Search term for title and location.
 * @property {string[]} [tagIds] - Array of tag UUIDs to filter by.
 * @property {'upcoming' | 'ongoing' | 'past'} [status] - Status to filter by.
 * @property {'popularity_desc' | 'start_time_asc' | 'start_time_desc'} [sortBy] - Sorting method.
 * @property {number} [limit] - Number of events to return per page.
 * @property {number} [offset] - Number of events to skip for pagination.
 */

/**
 * @typedef {Object} PageRequest
 * @property {number} [limit] - Number of items to return per page.
 * @property {number} [offset] - Number of items to skip for pagination.
 * @property {string} [sort] - Sorting method (e.g., 'start_time_asc', 'created_at_desc').
 * @property {string} [status] - Status to filter by.
 * @property {string} [searchTerm] - Search term.
 */

// For backward compatibility with existing code
export const eventTypes = {}; 