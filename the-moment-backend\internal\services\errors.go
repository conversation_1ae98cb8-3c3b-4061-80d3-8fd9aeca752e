package services

import "errors"

// General errors
var (
	ErrNotFound             = errors.New("requested resource not found")
	ErrUnauthorized         = errors.New("user is not authenticated")
	ErrForbidden            = errors.New("user is not authorized to perform this action")
	ErrBadRequest           = errors.New("bad request payload or parameters")
	ErrConflict             = errors.New("resource conflict")
	ErrInternalServer       = errors.New("internal server error")
	ErrValidation           = errors.New("validation failed")
	ErrPermissionDenied     = errors.New("permission denied")
	ErrOrganizationNotFound = errors.New("organization not found")
	ErrPostNotFound         = errors.New("post not found")
	ErrResourceNotFound     = errors.New("resource not found")
	ErrMediaItemNotFound    = errors.New("media item not found")
)

// Event specific errors
var (
	ErrEventRegistrationNotFound    = errors.New("event registration not found")
	ErrEventRegistrationNotOpen     = errors.New("event registration is not open")
	ErrEventRegistrationFull        = errors.New("event registration is full")
	ErrEventWaitlistFull            = errors.New("event waitlist is full")
	ErrUserAlreadyRegistered        = errors.New("user is already registered for this event")
	ErrUserAlreadyOnWaitlist        = errors.New("user is already on the waitlist for this event")
	ErrRegistrationCannotBeApproved = errors.New("registration cannot be approved at this time")
	ErrInvalidRegistrationStatus    = errors.New("invalid registration status transition")
	ErrEventNotPublic               = errors.New("event is not public")
)

// User specific errors
var (
	ErrDuplicateEmail       = errors.New("email address is already in use")
	ErrDuplicateUsername    = errors.New("username is already in use")
	ErrInvalidCredentials   = errors.New("invalid credentials")
	ErrEmailNotVerified     = errors.New("email not verified")
	ErrOTPExpired           = errors.New("OTP has expired")
	ErrInvalidOTP           = errors.New("invalid OTP")
	ErrVerificationNotFound = errors.New("user verification record not found")
	ErrDocumentNotFound     = errors.New("document not found")
)

// Volunteer specific errors
var (
	ErrNotQualifiedToVolunteer = errors.New("user is not a qualified volunteer for this organization")
)

// Organization specific errors
var (
	ErrOrgJoinRequestNotFound = errors.New("organization join request not found")
	ErrAlreadyMember          = errors.New("user is already a member of this organization")
	ErrJoinRequestExists      = errors.New("user already has a pending join request for this organization")
)

// Resource specific errors
var (
	ErrResourceFileNotFound = errors.New("resource file not found")
)
