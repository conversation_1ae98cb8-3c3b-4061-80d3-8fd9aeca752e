import React, { useState, useEffect, useCallback } from 'react';
import {
    Table,
    Button,
    Space,
    Typography,
    Alert,
    Spin,
    message,
    Popconfirm,
    Tag,
    Image
} from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { organizationService } from '../../services/organizationService';
import { formatDate } from '../../utils/dateFormatter';
import { THEME_COLORS } from '../../config/themeConfig';
import defaultLogo from '../../assets/logo/default-logo.png';
import '../../styles/TableBorder.css';

const { Title, Text } = Typography;

const OrganizationListPage = () => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const { currentUser, isLoading: isUserLoading } = useUser();
    const { fetchOrganizations } = useOrganization();

    const [loading, setLoading] = useState(false);
    const [organizations, setOrganizations] = useState([]);
    const [deleteLoading, setDeleteLoading] = useState(null);

    const isSuperAdmin = currentUser?.role === 'super_admin';
    const canManageOrganizations = currentUser?.role === 'super_admin' || currentUser?.role === 'admin';

    const fetchOrganizationsList = useCallback(async () => {
        if (!canManageOrganizations) return;
        
        setLoading(true);
        try {
            const response = await organizationService.listOrganizations();
            let orgList = response || [];
            
            // Filter out super org for normal admin users
            if (!isSuperAdmin) {
                orgList = orgList.filter(org => org.id !== ALL_ORGANIZATION_ID);
            }
            
            setOrganizations(orgList);
        } catch (error) {
            console.error('Error fetching organizations:', error);
            message.error(t('organizationList.errors.fetchFailed'));
        } finally {
            setLoading(false);
        }
    }, [canManageOrganizations, isSuperAdmin, t]);

    useEffect(() => {
        fetchOrganizationsList();
    }, [fetchOrganizationsList]);

    const handleDelete = async (orgId, orgName) => {
        setDeleteLoading(orgId);
        try {
            await organizationService.deleteOrganization(orgId);
            message.success(t('organizationList.messages.deleteSuccess', { orgName }));
            await fetchOrganizationsList(); // Refresh the list
            if (fetchOrganizations) {
                await fetchOrganizations(); // Update context
            }
        } catch (error) {
            console.error('Error deleting organization:', error);
            // More detailed error handling
            let errorMessage = t('organizationList.errors.deleteFailed', { orgName });
            if (error.response?.status === 403) {
                errorMessage = t('organizationList.errors.deleteUnauthorized', { orgName });
            } else if (error.response?.status === 404) {
                errorMessage = t('organizationList.errors.deleteNotFound', { orgName });
            } else if (error.response?.status === 409) {
                errorMessage = t('organizationList.errors.deleteConflict', { orgName });
            } else if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            }
            message.error(errorMessage);
        } finally {
            setDeleteLoading(null);
        }
    };

    const handleEdit = (orgId) => {
        navigate(`/organization-settings/${orgId}/edit`);
    };

    const handleRowClick = (record) => {
        return {
            onClick: () => handleEdit(record.id),
            style: { cursor: 'pointer' }
        };
    };

    // Helper function to get theme color display info
    const getThemeColorInfo = (colorName) => {
        const themeColor = THEME_COLORS.find(color => color.name === colorName);
        if (!themeColor) {
            return { color: '#F44336', name: t('organizationSettings.themeColors.red', 'Red') };
        }
        return {
            color: themeColor.value,
            name: t(`organizationSettings.themeColors.${themeColor.name}`, themeColor.name)
        };
    };

    // Helper function to check if organization can be deleted
    const canDeleteOrganization = (orgId) => {
        // Super org (ALL_ORGANIZATION_ID) cannot be deleted
        // Only superadmin can delete organizations
        return orgId !== ALL_ORGANIZATION_ID && isSuperAdmin;
    };

    const columns = [
        {
            title: t('organizationList.columns.logo'),
            dataIndex: 'image_url',
            key: 'logo',
            width: 80,
            render: (url, record) => (
                <Image
                    src={url || defaultLogo}
                    alt={record.name}
                    width={40}
                    height={40}
                    style={{ objectFit: 'cover', borderRadius: '4px' }}
                    fallback={defaultLogo}
                />
            ),
        },
        {
            title: t('organizationList.columns.name'),
            dataIndex: 'name',
            key: 'name',
            render: (text, record) => (
                <div>
                    <div style={{ fontWeight: 'bold', fontSize: '15px' }}>{text}</div>
                    {record.description && (
                        <div style={{ fontSize: '14px', color: '#666' }}>
                            {record.description}
                        </div>
                    )}
                </div>
            ),
        },
        {
            title: t('organizationList.columns.status'),
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: (status) => {
                const statusColors = {
                    active: 'green',
                    inactive: 'red',
                    pending_setup: 'orange',
                };
                return (
                    <Tag color={statusColors[status] || 'default'}>
                        {t(`organizationSettings.status.${status}`) || status}
                    </Tag>
                );
            },
        },
        {
            title: t('organizationList.columns.themeColor'),
            dataIndex: 'theme_color',
            key: 'themeColor',
            width: 120,
            render: (color) => {
                const themeInfo = getThemeColorInfo(color);
                return (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <div
                            style={{
                                width: '20px',
                                height: '20px',
                                borderRadius: '4px',
                                backgroundColor: themeInfo.color
                            }}
                        />
                        <span>{themeInfo.name}</span>
                    </div>
                );
            },
        },
        {
            title: t('organizationList.columns.createdAt'),
            dataIndex: 'created_at',
            key: 'createdAt',
            width: 180,
            render: (date) => formatDate(date, i18n.language),
        },
        // Only show actions column for super_admin and only for deletable orgs
        ...(isSuperAdmin ? [{
            title: t('organizationList.columns.actions'),
            key: 'actions',
            fixed: 'right',
            width: 80,
            render: (_, record) => {
                // Only show delete button if the org can be deleted
                if (!canDeleteOrganization(record.id)) {
                    return null;
                }
                
                return (
                    <div onClick={(e) => e.stopPropagation()}>
                        <Popconfirm
                            title={t('organizationList.deleteConfirm.title')}
                            description={t('organizationList.deleteConfirm.description', { orgName: record.name })}
                            onConfirm={() => handleDelete(record.id, record.name)}
                            okText={t('common.yes')}
                            cancelText={t('common.no')}
                        >
                            <Button
                                type="link"
                                danger
                                icon={<DeleteOutlined />}
                                loading={deleteLoading === record.id}
                                size="small"
                            >
                                {t('common.actions.delete')}
                            </Button>
                        </Popconfirm>
                    </div>
                );
            },
        }] : [])
    ];

    if (isUserLoading) {
        return <Spin size="large" style={{ display: 'block', marginTop: '50px' }} />;
    }

    if (!currentUser || !canManageOrganizations) {
        return <Alert message={t('unauthorized.title')} description={t('unauthorized.defaultMessage')} type="error" showIcon />;
    }

    return (
        <div className="py-6 px-4 md:px-6 bg-white">
            <div className="flex flex-col md:flex-row justify-between md:items-center items-start mb-6 gap-4">
                <div>
                    <Title level={2}>{t('organizationList.title')}</Title>
                    <Text type="secondary">{t('organizationList.description')}</Text>
                </div>
                {isSuperAdmin && (
                    <Button
                        type="dashed"
                        size="large"
                        onClick={() => navigate('/organization-settings/create')}
                    >
                        {t('organizationSettings.createOrg.title')}
                    </Button>
                )}
            </div>
            
            <div className="border rounded-lg mt-4 overflow-hidden">
                <Table
                    columns={columns}
                    dataSource={organizations.map(org => ({ ...org, key: org.id }))}
                    rowKey="id"
                    loading={loading}
                    pagination={false}
                    onRow={handleRowClick}
                    style={{ 
                        overflowX: 'auto',
                        borderRadius: 0 // Remove table's own border radius to prevent conflicts
                    }}
                    className="no-pagination-table"
                />
            </div>
        </div>
    );
};

export default OrganizationListPage; 