package payloads

import (
	"time"

	"github.com/google/uuid"
)

// RegisterForEventRequest defines the expected request body for event registration.
// @Description Request body for registering for an event.
type RegisterForEventRequest struct {
	// The UUID of the event to register for.
	EventID uuid.UUID `json:"event_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"`
}

// ListUserRegistrationsRequest defines parameters for listing a user's event registrations.
// It includes pagination and potential filters.
// @Description Query parameters for listing a user's event registrations.
type ListUserRegistrationsRequest struct {
	PageRequest // Embeds Limit and Offset
	// Filter by event start date (ISO 8601 format)
	StartDate *time.Time `query:"start_date" validate:"omitempty,ltefield=EndDate" example:"2023-01-01T00:00:00Z"` // Ensure StartDate is not after EndDate
	// Filter by event end date (ISO 8601 format)
	EndDate *time.Time `query:"end_date" validate:"omitempty,gtefield=StartDate" example:"2023-12-31T23:59:59Z"` // Ensure EndDate is not before StartDate
	// Filter by event status (e.g., 'published', 'archived', 'deleted', 'draft', 'hidden', 'cancelled')
	Status *string `query:"status" validate:"omitempty,oneof=published archived deleted draft hidden cancelled" example:"published"` // Filter by event status
	// Sort order (date_asc, date_desc)
	Sort *string `query:"sort" validate:"omitempty,oneof=date_asc date_desc" example:"date_asc"` // Sort by event date
	// Filter by user's role in event (participant, volunteer)
	Role *string `query:"role" validate:"omitempty,oneof=participant volunteer" example:"participant"` // Filter by user's role
	// Filter by organization ID (UUID)
	OrganizationID *uuid.UUID `query:"organization_id" validate:"omitempty,uuid" example:"123e4567-e89b-12d3-a456-************"` // Filter by organization ID
}

// ConflictingEventDetails holds information about an event that caused a time conflict.
// @Description Details about an event that caused a time conflict during registration.
type ConflictingEventDetails struct {
	// The UUID of the conflicting event.
	ConflictingEventID *uuid.UUID `json:"conflicting_event_id,omitempty" example:"123e4567-e89b-12d3-a456-************"`
	// The title of the conflicting event.
	ConflictingEventTitle *string `json:"conflicting_event_title,omitempty" example:"Another Event"`
	// The start time of the conflicting event.
	ConflictingEventStartTime *time.Time `json:"conflicting_event_start_time,omitempty" example:"2023-10-01T11:00:00Z"`
	// The end time of the conflicting event.
	ConflictingEventEndTime *time.Time `json:"conflicting_event_end_time,omitempty" example:"2023-10-01T13:00:00Z"`
}

// ListOrgEventRegistrationsRequest defines parameters for listing event registrations
// for an organization with pagination and filtering options.
// @Description Query parameters for listing event registrations for an organization (Admin view).
type ListOrgEventRegistrationsRequest struct {
	PageRequest // Embeds Limit and Offset
	// Optional filter by specific Event ID (UUID)
	EventID *uuid.UUID `query:"event_id" validate:"omitempty" example:"123e4567-e89b-12d3-a456-************"` // Optional filter by specific event
	// Filter events by start date range (ISO 8601)
	StartDate *time.Time `query:"start_date" validate:"omitempty,ltefield=EndDate" example:"2023-01-01T00:00:00Z"` // Filter events by start date
	// Filter events by end date range (ISO 8601)
	EndDate *time.Time `query:"end_date" validate:"omitempty,gtefield=StartDate" example:"2023-12-31T23:59:59Z"` // Filter events by end date
	// Filter by registration status (e.g., registered, waitlisted, cancelled_by_user, cancelled_by_admin, rejected, attended)
	Status *string `query:"status" validate:"omitempty,oneof=registered waitlisted cancelled_by_user cancelled_by_admin rejected attended" example:"registered"` // Filter by registration status
	// Filter registrations by specific User ID (UUID)
	UserID *uuid.UUID `query:"user_id" validate:"omitempty" example:"123e4567-e89b-12d3-a456-************"` // Filter registrations by specific user
	// Filter by user's role in event (participant, volunteer)
	Role *string `query:"role" validate:"omitempty,oneof=participant volunteer" example:"participant"` // Filter by user's role
	// Search by user name (partial match)
	SearchName *string `query:"search_name" validate:"omitempty" example:"John Doe"` // Search by user name (partial match)
	// Filter by payment status (e.g., paid, pending, not_required, failed, refunded)
	PaymentStatus *string `query:"payment_status" validate:"omitempty,oneof=paid pending not_required failed refunded" example:"paid"` // Filter by payment status
}

// UpdateEventRegistrationPaymentRequest defines the request body for updating registration payment status.
// @Description Request body for updating the payment status of an event registration (Public or Staff endpoint).
type UpdateEventRegistrationPaymentRequest struct {
	// The new payment status for the registration.
	NewPaymentStatus string `json:"new_payment_status" validate:"required,oneof=paid unpaid not_required refunded" example:"paid"`
	// The UUID of the staff member performing the update (optional, for logging).
	StaffID *uuid.UUID `json:"staff_id,omitempty" example:"123e4567-e89b-12d3-a456-************"`
}

// EventCheckInRequest defines the request body for checking into an event (Staff/Volunteer).
// @Description Request body for marking a user's attendance at an event.
type EventCheckInRequest struct {
	// The UUID of the user to check in.
	UserID string `json:"user_id" validate:"required,uuid" example:"123e4567-e89b-12d3-a456-************"`
}

// EmptyRequest is a placeholder for endpoints that require an empty JSON body.
// @Description Placeholder for endpoints requiring an empty request body.
type EmptyRequest struct{}

// TODO: Add other event registration payloads
