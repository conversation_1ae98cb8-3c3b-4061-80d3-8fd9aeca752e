/* src/styles/Home.css */

/* Common variables for reuse */
:root {
    --primary-color: #f14d34;
    --primary-color-rgb: 241, 77, 52;
    --border-radius-sm: 6px;
    --border-radius-md: 8px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --border-light: 1px solid rgba(0, 0, 0, 0.06);
}

/* Base layout styles */
.layout {
    min-height: 100vh;
    background-color: #ffffff;
}

.home-content {
    padding: 20px 30px;
}
@media (max-width: 576px) {
    .home-content {
        padding: 10px;
    }
}

/* Responsive grid layout */
.pr-md-4 { padding-right: 8px; }
.pl-md-4 { padding-left: 8px; }

.home-content .ant-row > .ant-col-md-16 {
    flex: 0 0 58%;
    max-width: 58%;
}

.home-content .ant-row > .ant-col-md-8 {
    flex: 0 0 42%;
    max-width: 42%;
}

/* Responsive breakpoints */
@media (max-width: 992px) {
    .home-content .ant-row > .ant-col-md-16,
    .home-content .ant-row > .ant-col-md-8 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .pr-md-4, .pl-md-4 {
        padding-right: 0;
        padding-left: 0;
    }
    
    .featured-article {
        margin-bottom: 16px;
    }
    
    .upcoming-events-container {
        margin-top: 20px;
    }
}

/* Optimized Banner Carousel Styles */
.banner-carousel, .carousel-slide {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    height: 100% !important;
    will-change: transform;
    contain: content;
    position: relative;
    display: block;
}

.carousel-slide {
    cursor: pointer;
    position: relative;
    height: 100%;
    display: flex !important;
    flex-direction: column;
    background-color: #fff;
}

.carousel-image {
    width: 100%;
    height: 350px !important;
    object-fit: cover;
    object-position: center;
    backface-visibility: hidden;
    transform: translateZ(0);
    display: block;
    flex-grow: 1;
}

.carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: transparent;
    z-index: 5;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Media query for responsive text */
@media (min-width: 768px) {
    .text-3xl { font-size: 1.875rem; }
}

/* Additional carousel styles */
.carousel-overlay {
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.carousel-slide:hover .carousel-overlay {
    opacity: 0.9;
}

.carousel-title {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Simplified dot navigation */
.banner-carousel .slick-dots {
    bottom: 20px;
    z-index: 10;
}

.banner-carousel .slick-dots li button {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    width: 8px;
    height: 8px;
}

.banner-carousel .slick-dots li.slick-active button {
    background: #f14d34;
    width: 8px;
    height: 8px;
}

/* Optimized Featured article styles */
.featured-article {
    margin-bottom: 10px;
    width: 100%;
    max-height: 500px !important;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: var(--border-light);
    contain: content;
}

.featured-article-container, .post-card, .event-card, .upcoming-events-container {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    contain: content;
}

.featured-article-container {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border: none;
    height: 100%;
}

.featured-article-title {
    font-size: 36px;
    font-weight: 700;
    line-height: 1.2;
    margin: 0 0 10px;
    color: #000;
    letter-spacing: -0.5px;
}

.featured-article-info {
    padding: 16px 16px 12px;
    flex: 0 0 auto;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

/* Optimized image containers */
.featured-article-image-container, .post-image-container, .news-item-image-container {
    overflow: hidden;
    contain: strict;
}

.featured-article-image-container {
    width: 100%;
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 300px;
    margin-bottom: 0;
    position: relative;
}

/* Optimized images with hardware acceleration */
.featured-article-image, .post-card-image, .news-item-image, .event-image, .carousel-image {
    transition: transform var(--transition-normal);
    backface-visibility: hidden;
    transform: translateZ(0);
}

.featured-article-image, .post-card-image, .carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-article-image {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
}

/* Banner empty styles */
.banner-empty {
    min-height: 500px;
    position: relative;
    width: 100%;
    border-radius: 12px;
    overflow: hidden;
}

.banner-empty .ant-skeleton {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
}

.banner-empty .ant-skeleton-image {
    width: 100% !important;
    height: 100% !important;
    min-height: 500px;
}

/* Update upcoming events container */
.upcoming-events-container {
    height: 500px;
    display: flex;
    flex-direction: column;
    padding: 15px 15px 0 15px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.upcoming-events-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    height: calc(100% - 60px);
}

/* News item styles */
.news-item {
    display: flex;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: background-color 0.15s ease;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    border-radius: var(--border-radius-sm);
    background-color: #ffffff;
}

.news-item:hover {
    background-color: #f9fafb;
}

.news-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.news-item-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.news-item-text {
    margin-left: 1rem;
    overflow: hidden;
    flex: 1;
}

.news-item-title {
    font-weight: 600;
    font-size: 1rem;
    color: #1f2937;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.news-item-meta {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
}

.news-item-category {
    font-size: 14px;
    color: #f14d34;
    margin-right: 16px;
    position: relative;
    font-weight: 500;
}

.news-item-category::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: #ddd;
}

.news-item-test {
    font-size: 14px;
    color: #777;
}

.news-item-image-container {
    width: 100px;
    height: 70px;
    flex-shrink: 0;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    overflow: hidden;
    position: relative;
}

.news-item-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.news-item-image-skeleton {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f4f6;
    position: absolute;
    top: 0;
    left: 0;
}

/* News Items Container */
.news-items-container {
    display: flex;
    flex-direction: column;
    height: calc(100% - 40px);
    padding: 0;
    overflow-y: auto;
    scrollbar-width: thin;
    margin-top: 4px;
    border-radius: 6px;
}

/* Custom scrollbar for webkit browsers */
.news-items-container::-webkit-scrollbar {
    width: 6px;
}

.news-items-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.news-items-container::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 10px;
}

.news-items-container::-webkit-scrollbar-thumb:hover {
    background: #ccc;
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Basic header styles */
.upcoming-header, .popular-header, .recent-posts-header {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
}

.upcoming-header .section-subtitle, 
.popular-header .section-subtitle,
.recent-posts-header .section-subtitle {
    margin-bottom: 0;
}

.upcoming-events-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
}

/* Basic button styles */
.view-all-btn {
    border-radius: 6px;
    border-color: transparent !important;
    background: transparent !important;
    font-size: 14px;
    padding: 4px 12px;
    height: 32px;
    display: flex;
    align-items: center;
}

.view-all-btn:hover {
    background: transparent !important;
    border-color: transparent !important;
}

/* Unified button styles */
.popular-header .ant-btn,
.upcoming-header .ant-btn,
.recent-posts-header .ant-btn {
    font-weight: 500;
}

/* Basic section styles */
.section-subtitle {
    font-size: 20px;
    font-weight: 700;
    color: #222;
    margin-bottom: 0;
    position: relative;
    letter-spacing: -0.3px;
}

.section-subtitle::after {
    content: '';
    display: block;
    width: 35px;
    height: 3px;
    background-color: #f14d34;
    margin-top: 4px;
}

/* Basic card styles */
.event-card,
.post-card,
.upcoming-events-card,
.featured-article-container {
    border-radius: 6px;
}

.post-card, .event-card {
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
    flex: 1;
    overflow: hidden;
    border: none;
    contain: content;
}

.event-card .ant-card-body {
    padding: 16px;
}

.event-card .ant-card-cover {
    overflow: hidden;
}

.post-card .ant-card-body {
    padding: 12px;
}

/* Basic empty state */
.section-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 180px;
    height: 100%;
    width: 100%;
    background-color: #ffffff;
    border-radius: 8px;
    padding: 16px;
    border: 1px dashed #eee;
}

.section-empty .ant-empty {
    margin: 20px 0;
}

.section-empty .ant-empty-description {
    color: #888;
}

.ant-empty-image {
    opacity: 0.7;
}

.ant-card-meta-title {
    font-weight: 600 !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
    margin-bottom: 8px !important;
}

/* Section spacing */
.popular-events-row,
.recent-posts-row {
    margin-top: 24px;
}

/* Section container styles */
.section-container {
    margin-bottom: 24px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 4px;
}

.section-header h4 {
    margin: 0;
}

/* Event related styles */
.event-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.event-item:last-child {
    border-bottom: none;
}

.event-icon {
    margin-right: 6px;
    font-size: 14px;
}

.event-thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
}

.article-meta {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    margin-top: 8px;
}

.article-category {
    font-size: 14px;
    color: #f14d34;
    margin-right: 16px;
    position: relative;
    font-weight: 500;
}

.article-category::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: #ddd;
}

.article-test {
    font-size: 14px;
    color: #777;
}

/* Event details styling */
.event-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 4px;
}

.event-date, .event-location {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 4px;
}

.event-date .anticon, .event-location .anticon, .event-participants .anticon {
  margin-right: 6px;
  font-size: 14px;
}

/* Post card related styles */
.post-image-container {
    height: 200px;
    overflow: hidden;
    contain: strict;
}

.post-card-image-container, .event-card-image-container {
    height: 160px;
    overflow: hidden;
}

.post-card-image, .event-card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-card-meta-info {
    display: flex;
    align-items: center;
    margin-top: 6px;
}

.post-card-category {
    font-size: 13px;
    color: #f14d34;
    margin-right: 16px;
    position: relative;
    font-weight: 500;
}

.post-card-category::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: #ddd;
}

.post-card-test {
    font-size: 13px;
    color: #777;
}

.post-card-meta, .event-card-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}

/* Carousel and Slider styles */
.posts-carousel-wrapper {
    position: relative;
    padding: 0 30px;
    margin-top: 8px;
    contain: layout;
}

.posts-carousel-container {
    overflow: hidden;
}

.posts-carousel-track {
    display: flex;
    will-change: transform;
}

.posts-group {
    display: flex;
    gap: 10px;
    flex: 0 0 100%;
    padding: 5px;
    contain: content;
}

.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    background: white;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.carousel-arrow.prev {
    left: 0;
}

.carousel-arrow.next {
    right: 0;
}

.carousel-arrow:hover {
    background: white;
    color: #f14d34;
}

/* Slider container styles */
.slider-container {
    position: relative;
    padding: 0 24px;
}

.slider-content {
    position: relative;
    z-index: 1;
}

.slider-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none !important;
}

.slider-button:hover {
    background-color: #fff !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.slider-button-prev {
    left: -8px;
}

.slider-button-next {
    right: -8px;
}

/* Slider controls */
.slider-control-btn {
    margin: 0 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.slider-control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.slider-control-btn:not(:disabled):hover {
    color: var(--primary-color);
    background-color: rgba(241, 77, 52, 0.05);
}

/* Recent Posts Slider */
.posts-slider-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-bottom: 20px;
    padding-bottom: 40px;
}

.slider-controls-container {
    position: absolute;
    bottom: 10px;
    right: 15px;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    padding: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.posts-slider {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
}

.post-slide {
    transition: transform var(--transition-normal);
}

/* Custom carousel navigation */
.custom-carousel-nav {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 8px;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    border-radius: 20px;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-carousel-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-carousel-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

.custom-carousel-dot .dot-content {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    opacity: 0;
}

.custom-carousel-dot.active .dot-content {
    opacity: 1;
}

/* Banner skeleton styles */
.banner-skeleton-header {
    display: flex;
    align-items: center;
}

.banner-skeleton-image {
    flex-grow: 1;
    width: 100% !important;
    height: 100% !important;
}

.banner-image-container {
    position: relative;
    height: 400px;
}

.banner-image-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.banner-skeleton-img {
    width: 100% !important;
    height: 100% !important;
}

.news-item-skeleton-img {
    width: 100% !important;
    height: 100% !important;
}

.skeleton-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Simple Glass Calendar */
.simple-glass-calendar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background-color: rgba(243, 244, 246, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.simple-glass-calendar.urgent {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.calendar-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.simple-glass-calendar.urgent .calendar-number {
  color: #ef4444;
}

.calendar-days-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.simple-glass-calendar.urgent .calendar-days-text {
  color: #ef4444;
}

/* Remove old calendar styles */
.event-countdown-calendar,
.calendar-month,
.calendar-day,
.calendar-countdown,
.simple-glass-calendar::after {
    display: none;
}

/* Media queries */
@media (max-width: 768px) {
    .banner-carousel .slick-dots {
        bottom: 8px;
    }
    
    .upcoming-events-container {
        height: 400px;
    }
    
    .custom-carousel-nav {
        bottom: 10px;
        right: 10px;
        padding: 6px 10px;
    }
    
    .custom-carousel-dot {
        width: 8px;
        height: 8px;
    }
    
    .custom-carousel-dot .dot-content {
        width: 4px;
        height: 4px;
    }
    
    .news-item-image-container {
        width: 70px;
        height: 50px;
    }
    
    .news-item {
        padding: 10px 8px;
    }
    
    .news-item-title {
        font-size: 14px;
    }
    
    .news-item-meta {
        font-size: 11px;
    }
    
    .simple-glass-calendar {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }
    
    .calendar-number {
        font-size: 18px;
    }
    
    .calendar-days-text {
        font-size: 9px;
    }
    
    .slider-container {
        padding: 0 16px;
    }
    
    .slider-button {
        width: 28px;
        height: 28px;
    }
    
    .post-card-image-container,
    .event-card-image-container {
        height: 120px;
    }
    
    .banner-skeleton-image {
        height: calc(100% - 100px) !important;
    }
}

@media (max-width: 576px) {
    .upcoming-events-container {
        height: 350px;
        padding: 12px 10px;
    }
    
    .news-item-image-container {
        width: 60px;
        height: 45px;
    }
    
    .upcoming-events-list {
        padding-right: 3px;
    }
}

/* Add styles for the upcoming events section based on restored_home.js */

/* Upcoming events section styles */
.upcoming-events-list {
  display: flex;
  flex-direction: column;
}

/* Larger news item styles */
.news-item-larger {
  padding: 0.5rem 1rem;
}

.news-item:hover {
  background-color: #f9fafb;
}

.news-item-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.news-item-text {
  margin-left: 1rem;
  overflow: hidden;
}

.news-item-title {
  font-weight: 600;
  font-size: 1rem;
  color: #1f2937;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-item-meta {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.news-item-image-container {
  width: 70px;
  height: 100px;
  margin-left: 1rem;
  flex-shrink: 0;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

/* Larger image container */
.news-item-image-container.larger {
  width: 120px;
  height: 90px;
}

.news-item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-item-image-skeleton {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  position: absolute;
  top: 0;
  left: 0;
}

.skeleton-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Glass calendar styling */
.simple-glass-calendar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background-color: rgba(243, 244, 246, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

/* Larger calendar */
.simple-glass-calendar.larger {
  width: 70px;
  height: 70px;
}

.simple-glass-calendar.urgent {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.calendar-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

/* Larger calendar number */
.simple-glass-calendar.larger .calendar-number {
  font-size: 1.5rem;
}

.simple-glass-calendar.urgent .calendar-number {
  color: #ef4444;
}

.calendar-days-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Larger calendar days text */
.simple-glass-calendar.larger .calendar-days-text {
  font-size: 0.875rem;
}

.simple-glass-calendar.urgent .calendar-days-text {
  color: #ef4444;
}
