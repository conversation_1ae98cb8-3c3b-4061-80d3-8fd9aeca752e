import React, { useState, useEffect } from 'react';
import { Typography, QRCode, Button, Row, Col, Card, Tag, Modal, Steps, Skeleton } from 'antd';
import { useTranslation } from 'react-i18next';
import {
    ClockCircleOutlined,
    EnvironmentOutlined,
    QrcodeOutlined,
    ExclamationCircleOutlined,
    ArrowRightOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { formatDate } from '../../utils/dateFormatter';
import { eventService } from '../../services/eventService';
import ErrorPage from '../ErrorPage';
import '../../styles/EventQRCode.css';

const { Title, Text } = Typography;
const { confirm } = Modal;

// Loading skeleton component
const QRCodeSkeleton = () => (
    <div className="qrcode-container">
        <Card className="qrcode-main-card">
            <Row gutter={[24, 24]}>
                <Col xs={24} md={14}>
                    <Skeleton active paragraph={{ rows: 4 }} />
                </Col>
                <Col xs={24} md={10}>
                    <Card className="qrcode-display-card">
                        <div style={{ display: 'flex', justifyContent: 'center', padding: '20px 0' }}>
                            <Skeleton.Avatar active size={200} shape="square" />
                        </div>
                    </Card>
                </Col>
            </Row>
        </Card>
    </div>
);

const EventQRCode = () => {
    const { t, i18n } = useTranslation();
    const { eventId } = useParams();
    const navigate = useNavigate();
    const { user } = useAuth();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [ticketData, setTicketData] = useState(null);

    useEffect(() => {
        const fetchTicketData = async () => {
            try {
                setLoading(true);
                const { data } = await eventService.getEventTicket(eventId);
                setTicketData(data);
                setError(null);
            } catch (error) {
                setError(error.response?.status === 404 ? '404' : 'general');
            } finally {
                setLoading(false);
            }
        };

        fetchTicketData();
    }, [eventId]);

    // Handle event cancellation
    const handleCancelRegistration = () => {
        confirm({
            title: t('eventQRCode.cancelModal.title'),
            icon: <ExclamationCircleOutlined />,
            content: t('eventQRCode.cancelModal.content'),
            okText: t('eventQRCode.cancelModal.confirm'),
            cancelText: t('eventQRCode.cancelModal.cancel'),
            onOk() {
                // Handle cancellation logic here
                console.log('Registration cancelled');
            },
        });
    };

    // If no user is logged in, redirect to events page
    if (!user) {
        navigate('/my-events');
        return null;
    }

    if (loading) {
        return <QRCodeSkeleton />;
    }

    if (error) {
        return <ErrorPage type={error} showBackHome={false} />;
    }

    const { event, ticket } = ticketData;

    return (
        <div className="qrcode-container">
            <Card className="qrcode-main-card">
                <Row gutter={[24, 24]}>
                    {/* Event Details Section */}
                    <Col xs={24} md={14}>
                        <div className="qrcode-info-section">
                            <Title level={2}>{event.title}</Title>

                            <div className="qrcode-status">
                                <Tag color={ticket?.paymentStatus === 'Paid' ? 'success' : 'orange'}>
                                    {t(`eventQRCode.userInfo.statuses.${ticket?.paymentStatus?.toLowerCase()}`)}
                                </Tag>
                                <Button
                                    type="link"
                                    className="qrcode-details-link"
                                    onClick={() => navigate(`/events/${event.id}`)}
                                >
                                    {t('eventQRCode.buttons.viewDetails')}
                                    <ArrowRightOutlined />
                                </Button>
                            </div>

                            <div className="qrcode-highlights">
                                <div className="qrcode-highlight-item">
                                    <ClockCircleOutlined />
                                    <Text strong>
                                        {formatDate(event.startTime, i18n.language)} - {formatDate(event.endTime, i18n.language, true)}
                                    </Text>
                                </div>
                                <div className="qrcode-highlight-item">
                                    <EnvironmentOutlined />
                                    <Text strong>{event.location}</Text>
                                </div>
                            </div>

                            <div className="qrcode-steps">
                                <Title level={3}>{t('eventQRCode.steps.title')}</Title>
                                <Steps
                                    direction="vertical"
                                    items={[
                                        {
                                            title: t('eventQRCode.steps.arrive.title'),
                                            description: t('eventQRCode.steps.arrive.description'),
                                        },
                                        {
                                            title: t('eventQRCode.steps.present.title'),
                                            description: t('eventQRCode.steps.present.description'),
                                        }
                                    ]}
                                />
                            </div>

                            <div className="qrcode-action-buttons">
                                <div className="qrcode-action-buttons-right">
                                    <Button danger onClick={handleCancelRegistration}>
                                        {t('eventQRCode.buttons.cancelRegistration')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </Col>

                    {/* QR Code Section */}
                    <Col xs={24} md={10}>
                        <div className="qrcode-right-section">
                            <Card className="qrcode-display-card">
                                <Title level={4}>{t('eventQRCode.qrcode.title')}</Title>
                                <div className="qrcode-display">
                                    <QRCode
                                        value={user?.id || 'invalid-user-id'}
                                        size={200}
                                        bordered={false}
                                    />
                                </div>
                                <div className="qrcode-user-info">
                                    <Text strong>{t('eventQRCode.userInfo.name')}: {ticket?.userName}</Text>
                                </div>
                                <Text type="secondary" className="qrcode-hint">
                                    {t('eventQRCode.qrcode.hint')}
                                </Text>
                            </Card>
                            <div className="qrcode-mobile-hint">
                                <QrcodeOutlined />
                                <Text type="secondary">{t('eventQRCode.mobileHint')}</Text>
                            </div>
                        </div>
                    </Col>
                </Row>
            </Card>
        </div>
    );
};

export default EventQRCode;
