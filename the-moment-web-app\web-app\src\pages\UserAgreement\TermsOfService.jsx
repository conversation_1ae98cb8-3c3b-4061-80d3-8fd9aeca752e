import React from 'react';
import { Typography, Layout, Space, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import '../../styles/UserAgreement.css';

const { Title, Text, Paragraph } = Typography;
const { Content } = Layout;

const TermsOfService = () => {
    const navigate = useNavigate();
    const { t } = useTranslation();

    const sections = [
        'serviceScope',
        'membershipEligibility',
        'identityVerification',
        'eventParticipation',
        'accountSecurity',
        'privacyProtection',
        'disclaimer'
    ];

    return (
        <Layout className="agreement-layout">
            <Content className="agreement-content">
                <div className="agreement-container">
                    <Title level={2}>{t('termsOfService.title')}</Title>
                    <Text type="secondary">{t('termsOfService.updatedAt')}</Text>
                    
                    <Paragraph className="introduction">
                        {t('termsOfService.introduction')}
                    </Paragraph>

                    <Space direction="vertical" size="large" className="sections">
                        {sections.map((section) => (
                            <div key={section} className="section">
                                <Title level={4}>
                                    {t(`termsOfService.sections.${section}.title`)}
                                </Title>
                                <Paragraph>
                                    {t(`termsOfService.sections.${section}.content`)}
                                </Paragraph>
                            </div>
                        ))}
                    </Space>

                    <Paragraph className="footer">
                        {t('termsOfService.footer')}
                    </Paragraph>
                </div>
            </Content>
        </Layout>
    );
};

export default TermsOfService; 