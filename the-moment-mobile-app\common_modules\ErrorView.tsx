import React from 'react';
import {
    View,
    Text,
    StyleSheet,
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Button } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';

export interface ErrorViewProps {
    onRetry?: () => void;
    onGoBack?: () => void;
}

export const ErrorView: React.FC<ErrorViewProps> = ({
    onRetry,
    onGoBack,
}) => {
    const { t } = useTranslation();
    const theme = appStyleStore(state => state.theme );

    const styles = StyleSheet.create({
        container: {
            flex: 1,
            alignItems: 'center',
            justifyContent: 'flex-start',
            paddingTop: '25%',
            padding: 20,
            backgroundColor: theme.colors.background,
        },
        title: {
            fontSize: 20,
            fontWeight: '600',
            color: theme.system.text,
            marginTop: 16,
            marginBottom: 8,
            textAlign: 'center',
        },
        message: {
            fontSize: 16,
            color: theme.system.secondaryText,
            textAlign: 'center',
            marginBottom: 24,
            lineHeight: 22,
        },
        buttonContainer: {
            gap: 12,
            width: '100%',
            maxWidth: 280,
            marginTop: 24,
        },
        button: {
            borderRadius: 12,
            minWidth: 120,
            backgroundColor: theme.colors.error,
        },
        buttonContent: {
            height: 44,
        },
        backButton: {
            // Add specific styles if needed, otherwise it inherits from button
            // Example: add margin if needed
            // marginTop: 10, 
        },
    });

    return (
        <View style={styles.container}>
            <MaterialCommunityIcons
                name="alert-circle-outline"
                size={64}
                color={theme.colors.error}
            />
            <Text style={styles.title}>{t('error.title')}</Text>
            <Text style={styles.message}>{t('error.message')}</Text>
            <View style={styles.buttonContainer}>
                {onRetry && (
                    <Button
                        mode="contained"
                        onPress={onRetry}
                        style={styles.button}
                        contentStyle={styles.buttonContent}
                    >
                        {t('error.retry')}
                    </Button>
                )}
                {onGoBack && (
                    <Button
                        mode="contained"
                        onPress={onGoBack}
                        style={[styles.button, styles.backButton]}
                        contentStyle={styles.buttonContent}
                    >
                        {t('common.goBack')}
                    </Button>
                )}
            </View>
        </View>
    );
}; 