.agreement-layout {
    min-height: 100vh;
    background: #f5f7ff;
}

.agreement-content {
    padding: 40px 20px;
}

.agreement-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.introduction {
    margin: 24px 0;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.65);
}

.sections {
    width: 100%;
}

.section {
    margin-bottom: 32px;
}

.section h4 {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
}

.section p {
    color: rgba(0, 0, 0, 0.65);
    line-height: 1.8;
}

.footer {
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    color: rgba(0, 0, 0, 0.45);
}

.back-button {
    margin-top: 32px;
    text-align: center;
}

@media (max-width: 768px) {
    .agreement-container {
        padding: 24px;
    }
} 