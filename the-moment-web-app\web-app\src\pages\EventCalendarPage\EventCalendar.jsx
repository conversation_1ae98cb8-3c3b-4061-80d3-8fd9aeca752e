import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Badge, Calendar, Segmented, List, Button, Divider, Spin, Tag, Empty, Tabs, Typography } from 'antd';
import { eventService } from '../../services/eventService';
import { useTranslation } from 'react-i18next';
import { CalendarOutlined, HistoryOutlined, EnvironmentOutlined, DollarOutlined, UserOutlined } from '@ant-design/icons';
import { Link, useLocation } from 'react-router-dom';
import { formatShortDatetime, formatToYYYYMMDD as formatToYYYYMMDDUtil } from '../../utils/dateFormatter';
import dayjs from 'dayjs';
import ErrorPage from '../ErrorPage';

const { Text: AntText } = Typography;

const EventCalendar = () => {
    const [mode, setMode] = useState('month');
    const [currentDate, setCurrentDate] = useState(dayjs());
    const [selectedDate, setSelectedDate] = useState(() => {
        return dayjs();
    });
    const [userEvents, setUserEvents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const location = useLocation();
    const [activeTab, setActiveTab] = useState(() => {
        const params = new URLSearchParams(location.search);
        return params.get('showCompleted') === 'true' ? 'past' : 'upcoming';
    });
    const { t, i18n } = useTranslation();

    const fetchUserEvents = useCallback(async () => {
        try {
            setLoading(true);
            // Define API parameters object similar to mobile implementation
            const apiParams = {};
            
            const response = await eventService.listMyRegistrations(apiParams);
            setUserEvents(response);
        } catch (error) {
            console.error('Error fetching user registered events:', error);
            setError(error || 'Failed to fetch events');
            setUserEvents([]);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchUserEvents();
    }, [fetchUserEvents]);

    // Add refresh capability if needed (similar to mobile implementation)
    const handleRefresh = () => {
        fetchUserEvents();
    };

    const getBadgeColor = (event) => {
        if (!event) return 'gray';
        
        if (event.registration_role === 'volunteer') {
            return 'pink';
        } else {
            const eventDate = event.event_start_time ? new Date(event.event_start_time) : new Date();
            const today = new Date();
            today.setHours(0,0,0,0);
            eventDate.setHours(0,0,0,0);
            return eventDate >= today ? 'blue' : 'gray';
        }
    };

    const getListData = (value) => {
        if (!value || !userEvents?.length) return [];
        
        if (mode === 'month') {
            const formattedDate = `${value.year()}-${String(value.month() + 1).padStart(2, '0')}-${String(value.date()).padStart(2, '0')}`;

            const eventsOnThisDay = userEvents.filter(event =>
                event?.event_start_time && formatToYYYYMMDDUtil(event.event_start_time) === formattedDate
            );

            return eventsOnThisDay.map((event) => ({
                type: getBadgeColor(event),
                content: event.event_title || '',
                event
            }));
        } else if (mode === 'year') {
            const year = value.year();
            const month = value.month();

            const eventsInThisMonth = userEvents.filter(event => {
                if (!event?.event_start_time) return false;
                const eventDate = new Date(event.event_start_time);
                return eventDate.getFullYear() === year && eventDate.getMonth() === month;
            });

            return eventsInThisMonth.map((event) => ({
                type: getBadgeColor(event),
                content: event.event_title || '',
                event
            }));
        }
        return [];
    };

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentDate(dayjs());
        }, 86400000);

        return () => clearInterval(timer);
    }, []);

    const calculateDaysRemaining = (eventDate) => {
        if (!eventDate) return 0;
        
        const today = new Date();
        const eventDay = new Date(eventDate);

        today.setHours(0, 0, 0, 0);
        eventDay.setHours(0, 0, 0, 0);

        const diffTime = eventDay - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays > 0 ? diffDays : 0;
    };

    const calculateDaysPassed = (eventDate) => {
        if (!eventDate) return 0;
        
        const today = new Date();
        const eventDay = new Date(eventDate);

        today.setHours(0, 0, 0, 0);
        eventDay.setHours(0, 0, 0, 0);

        const diffTime = today - eventDay;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays;
    };

    const sortedUpcomingEvents = useMemo(() => {
        if (!userEvents?.length) return [];

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const upcomingEvents = userEvents.filter(event => {
            if (!event?.event_start_time) return false;
            const eventDate = new Date(event.event_start_time);
            eventDate.setHours(0, 0, 0, 0);
            return eventDate >= today;
        });

        const eventsWithDaysRemaining = upcomingEvents.map(event => ({
            ...event,
            daysRemaining: calculateDaysRemaining(event.event_start_time)
        }));

        return eventsWithDaysRemaining.sort((a, b) => a.daysRemaining - b.daysRemaining);
    }, [userEvents]);

    const sortedPastEvents = useMemo(() => {
        if (!userEvents?.length) return [];

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const pastEvents = userEvents.filter(event => {
            if (!event?.event_start_time) return false;
            const eventDate = new Date(event.event_start_time);
            eventDate.setHours(0, 0, 0, 0);
            return eventDate < today;
        });

        const eventsWithDaysPassed = pastEvents.map(event => ({
            ...event,
            daysPassed: calculateDaysPassed(event.event_start_time)
        }));

        return eventsWithDaysPassed.sort((a, b) => a.daysPassed - b.daysPassed);
    }, [userEvents]);

    const getStatusTagColor = (status, role) => {
        if (role === 'volunteer') return 'magenta';

        switch (status) {
            case 'completed':
                return 'green';
            case 'absent':
                return 'red';
            default:
                return '';
        }
    };

    const getStatusText = (status, role) => {
        if (role === 'volunteer') return t('eventCalendar.status.volunteer');

        switch (status) {
            case 'completed':
                return t('eventCalendar.status.completed');
            case 'absent':
                return t('eventCalendar.status.absent');
            default:
                return '';
        }
    };

    const shouldDisplayTag = (status, role) => {
        return role === 'volunteer' || status === 'completed' || status === 'absent';
    };

    const monthCellRender = (value) => {
        if (!value) return null;
        const listData = getListData(value);

        if (listData.length === 0) return null;

        return (
            <ul className="events" style={{ padding: 0, margin: 0 }}>
                {listData.slice(0, 3).map((item) => (
                    <li key={item.event.id || Math.random()} className="mb-1 w-full overflow-hidden text-xs whitespace-nowrap text-ellipsis">
                        <Badge color={item.type} text={
                            <span className="text-gray-700">{item.content}</span>
                        } />
                    </li>
                ))}
                {listData.length > 3 && (
                    <li className="text-xs text-gray-500">+{listData.length - 3} more</li>
                )}
            </ul>
        );
    };

    const dateCellRender = (value) => {
        if (!value) return null;
        const listData = getListData(value);
        return (
            <ul className="events" style={{ padding: 0, margin: 0 }}>
                {listData.slice(0, 3).map((item) => (
                    <li key={item.event.id || Math.random()} className="mb-1 w-full overflow-hidden text-xs whitespace-nowrap text-ellipsis">
                        <Badge color={item.type} text={
                            <span className="text-gray-700">{item.content}</span>
                        } />
                    </li>
                ))}
                {listData.length > 3 && (
                    <li className="text-xs text-gray-500">+{listData.length - 3} more</li>
                )}
            </ul>
        );
    };

    const cellRender = (current, info) => {
        if (info.type === 'date') return dateCellRender(current);
        if (info.type === 'month') return monthCellRender(current);
        return info.originNode;
    };

    const headerRender = ({ value, type, onChange, onTypeChange }) => {
        const current = value?.toDate ? value.toDate() : new Date();

        const monthOptions = Array.from({ length: 12 }, (_, i) => new Date(0, i).toLocaleDateString(i18n.language, { month: 'short' }));

        const navigatePrevious = () => {
            let newValue;
            if (mode === 'month') {
                const newDate = new Date(current);
                newDate.setMonth(newDate.getMonth() - 1);
                newValue = dayjs(newDate);
            } else {
                const newDate = new Date(current);
                newDate.setFullYear(newDate.getFullYear() - 1);
                newValue = dayjs(newDate);
            }
            onChange(newValue);
            setSelectedDate(newValue);
        };

        const navigateNext = () => {
            let newValue;
            if (mode === 'month') {
                const newDate = new Date(current);
                newDate.setMonth(newDate.getMonth() + 1);
                newValue = dayjs(newDate);
            } else {
                const newDate = new Date(current);
                newDate.setFullYear(newDate.getFullYear() + 1);
                newValue = dayjs(newDate);
            }
            onChange(newValue);
            setSelectedDate(newValue);
        };

        return (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: 8 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    <Button type="text" onClick={navigatePrevious}>
                        &lt;
                    </Button>
                    <div style={{ fontSize: '16px', fontWeight: '500' }}>
                        {mode === 'month' ? `${monthOptions[current.getMonth()]} ${current.getFullYear()}` : current.getFullYear()}
                    </div>
                    <Button type="text" onClick={navigateNext}>
                        &gt;
                    </Button>
                </div>
                <Segmented
                    options={[
                        { label: t('eventCalendar.modes.month'), value: 'month' },
                        { label: t('eventCalendar.modes.year'), value: 'year' },
                    ]}
                    value={mode}
                    onChange={(value) => {
                        setMode(value);
                        onTypeChange(value);
                    }}
                    className="bg-gray-100 p-1 rounded-lg"
                />
            </div>
        );
    };

    if (loading && userEvents.length === 0) {
        return (
            <div className="events-loading-container">
                <Spin size="large" />
            </div>
        );
    }

    if (error) {
        return <ErrorPage type={error} showBackHome={false} className="mt-8" />;
    }

    const renderUpcomingEvents = () => {
        if (sortedUpcomingEvents.length === 0) {
            return <Empty description={t('eventCalendar.emptyStates.noUpcomingEvents')} />;
        }

        return sortedUpcomingEvents.map((event) => {
            const isUrgent = event.daysRemaining < 5;
            const isVolunteer = event.registration_role === 'volunteer';

            return (
                <Link to={`/my-events/${event.id}/qrcode`} key={event.id} className="news-item cursor-pointer mb-4 hover:bg-gray-50 rounded-lg transition-colors" style={{ padding: '12px', border: '1px solid #f0f0f0' }}>
                    <div className="news-item-content flex items-center">
                        {/* Calendar with Days Remaining */}
                        <div
                            className={`mr-4 flex flex-col items-center justify-center rounded-lg shadow-sm`}
                            style={{
                                width: '60px',
                                height: '60px',
                                backgroundColor: isUrgent ? '#FFF2F0' : '#F6F6F6',
                                border: `1px solid ${isUrgent ? '#FFCCC7' : '#E8E8E8'}`
                            }}
                        >
                            <div className="text-xl font-bold" style={{ color: isUrgent ? '#FF4D4F' : '#595959' }}>
                                {event.daysRemaining}
                            </div>
                            <div className="text-xs" style={{ color: isUrgent ? '#FF4D4F' : '#8C8C8C' }}>
                                {event.daysRemaining === 1 ? t('eventCalendar.timeUnits.day') : t('eventCalendar.timeUnits.days')}
                            </div>
                        </div>
                        
                        <div className="flex-1">
                            <div className="flex items-center justify-between mb-1">
                                <div className="text-base font-medium line-clamp-1 text-gray-800">{event.event_title}</div>
                                {shouldDisplayTag(event.status, event.registration_role) && (
                                    <Tag color={getStatusTagColor(event.status, event.registration_role)} className="ml-2">
                                        {getStatusText(event.status, event.registration_role)}
                                    </Tag>
                                )}
                            </div>
                            <div className="text-sm text-gray-500">
                                <CalendarOutlined className="mr-1" /> {dayjs(event.event_start_time).format('MMM D, YYYY h:mm A')}
                            </div>
                        </div>
                    </div>
                </Link>
            );
        });
    };

    const renderPastEvents = () => {
        if (sortedPastEvents.length === 0) {
            return <Empty description={t('eventCalendar.emptyStates.noPastEvents')} />;
        }

        return sortedPastEvents.map((event) => {
            const isVolunteer = event.registration_role === 'volunteer';
            return (
                <div key={event.id} className="news-item cursor-pointer mb-4 hover:bg-gray-50 rounded-lg transition-colors" style={{ padding: '12px', border: '1px solid #f0f0f0' }}>
                    <div className="news-item-content flex items-center">
                        {/* Calendar showing days passed */}
                        <div
                            className={`mr-4 flex flex-col items-center justify-center rounded-lg`}
                            style={{
                                width: '60px',
                                height: '60px',
                                backgroundColor: '#F5F5F5',
                                border: '1px solid #D9D9D9'
                            }}
                        >
                            <div className="text-xl font-bold text-gray-500">
                                {event.daysPassed}
                            </div>
                            <div className="text-xs text-gray-500">
                                {event.daysPassed === 1 ? t('eventCalendar.timeUnits.dayAgo') : t('eventCalendar.timeUnits.daysAgo')}
                            </div>
                        </div>

                        <div className="flex-1 overflow-hidden">
                            <div className="flex items-center justify-between mb-1">
                                <div className="text-base font-medium line-clamp-1 text-gray-800">{event.event_title}</div>
                                {shouldDisplayTag(event.status, event.registration_role) && (
                                    <Tag color={getStatusTagColor(event.status, event.registration_role)} className="ml-2">
                                        {getStatusText(event.status, event.registration_role)}
                                    </Tag>
                                )}
                            </div>
                            <div className="text-sm text-gray-500">
                                <CalendarOutlined className="mr-1" /> {dayjs(event.event_start_time).format('MMM D, YYYY h:mm A')}
                            </div>
                        </div>
                    </div>
                </div>
            );
        });
    };

    const tabItems = [
        {
            key: 'upcoming',
            icon: <CalendarOutlined />,
            label: t('eventCalendar.tabs.upcomingEvents'),
        },
        {
            key: 'past',
            icon: <HistoryOutlined />,
            label: t('eventCalendar.tabs.pastEvents'),
        }
    ];

    return (
        <div className="flex flex-col lg:flex-row gap-5 p-4 h-full">
            {/* Events Section with Tabs */}
            <div className="w-full lg:w-1/3 flex flex-col h-full">
                <Tabs
                    activeKey={activeTab}
                    onChange={setActiveTab}
                    items={tabItems}
                    className="mb-2"
                />
                <div className="overflow-y-auto flex-grow" style={{ height: 'calc(100vh - 210px)' }}>
                    {activeTab === 'upcoming' ? renderUpcomingEvents() : renderPastEvents()}
                </div>
            </div>

            <Divider type="vertical" className="h-auto hidden lg:block" />
            <Divider type="horizontal" className="w-full block lg:hidden" />

            {/* Calendar Section */}
            <div className="w-full lg:w-2/3 border-b">
                <Calendar
                    cellRender={cellRender}
                    mode={mode}
                    headerRender={headerRender}
                    value={selectedDate}
                    onPanelChange={(date, mode) => {
                        setMode(mode);
                        setSelectedDate(date);
                    }}
                    onChange={(date) => {
                        setSelectedDate(date);
                    }}
                />
            </div>
        </div>
    );
};

export default EventCalendar;