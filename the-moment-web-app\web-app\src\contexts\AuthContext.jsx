import React, { createContext, useState, useContext, useCallback, useEffect } from 'react';
import { authService } from '../services/authService';
import { profileService } from '../services/profileService';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
    const [authState, setAuthState] = useState(() => {
        const savedAuth = sessionStorage.getItem('auth');
        // authService.initializeAuth(); // Assuming this is local storage/token setup, not an API call.
        return savedAuth ? JSON.parse(savedAuth) : {
            isLoggedIn: false,
            user: null,
            userId: null, // Add userId to store the ID from login response
            loading: true, // To indicate initial auth check and profile fetch
            error: null, // Add error field for non-fatal errors
        };
    });

    // Logout handler
    const handleLogout = useCallback(async (callApi = true) => {
        if (callApi) {
            try {
                await authService.logout();
            } catch (error) {
                console.error('Logout API call failed:', error);
                // Proceed with local logout anyway
            }
        }
        authService.clearTokens(); // Clear tokens from localStorage
        sessionStorage.removeItem('auth'); // Clear auth state from session storage
        setAuthState({
            isLoggedIn: false,
            user: null,
            userId: null,
            loading: false,
            error: null, // Ensure error is cleared on logout
        });
    }, []);

    const fetchUserProfile = useCallback(async (currentUserId = null) => {
        // currentUserId might be passed if immediately available post-login,
        // otherwise, profileService.getUserProfile should rely on the authenticated session.
        try {
            const userProfile = await profileService.getUserProfile(); // Fetches for the current authenticated user
            setAuthState(prevState => {
                const newAuthState = {
                    ...prevState,
                    isLoggedIn: true, // Ensure isLoggedIn is true if profile fetch succeeds
                    user: userProfile, // Assuming userProfile is the data structure expected
                    userId: userProfile.id, // Store userId from profile
                    loading: false,
                    error: null, // Clear any previous error on success
                };
                sessionStorage.setItem('auth', JSON.stringify(newAuthState));
                return newAuthState;
            });
        } catch (error) {
            if (error.response && (error.response.status === 401 || error.response.status === 403)) {
                console.error('Failed to fetch user profile due to auth error (401/403):', error);
                await handleLogout(false); // Logout on 401/403. This will also set loading:false, error:null etc.
            } else {
                console.error('Failed to fetch user profile (non-auth error):', error);
                setAuthState(prevState => ({
                    ...prevState,
                    loading: false,
                    // user: prevState.user, // Keep existing user data, or clear it if preferred
                    error: error.message || 'Failed to load user profile. Please try again.',
                    // isLoggedIn should remain as it was, or be explicitly managed if this path is hit
                }));
            }
        }
    }, [handleLogout]);

    // Effect to check authentication status and fetch profile on mount or if tokens exist
    useEffect(() => {
        const checkAuthAndFetchProfile = async () => {
            const tokensExist = authService.getAccessToken(); // Check if tokens are in localStorage
            const savedAuth = sessionStorage.getItem('auth');
            let initialLoggedIn = false;
            let initialUserId = null;

            if (savedAuth) {
                try {
                    const parsedSavedAuth = JSON.parse(savedAuth);
                    initialLoggedIn = parsedSavedAuth.isLoggedIn;
                    initialUserId = parsedSavedAuth.userId;
                } catch (e) { /* ignore parsing error */ }
            }

            if (tokensExist && initialLoggedIn) {
                setAuthState(prevState => ({ ...prevState, loading: true, error: null })); // Clear previous error when attempting
                // If tokens exist and session indicates logged in, try to refresh and fetch profile
                try {
                    await authService.refreshToken(); // Refresh token first to ensure it's valid
                    await fetchUserProfile(initialUserId); // This will set error: null on its own success
                } catch (error) {
                    const isCriticalAuthMessage = error.message && (
                        error.message.includes('No refresh token available') ||
                        error.message.includes('Session expired or refresh failed. Please login again.') // Updated to the new generic message
                        // error.message.includes('Session expired. Please login again.') // This is now covered by the above
                    );
                    const isHttpAuthFailure = error.response && (error.response.status === 401 || error.response.status === 403);

                    if (isCriticalAuthMessage || isHttpAuthFailure) {
                        console.error('Critical auth error or HTTP auth failure during initial load, logging out:', error);
                        await handleLogout(false); // This will update authState, including setting error to null.
                    } else {
                        // This block now handles non-401/403 HTTP errors from refreshToken (e.g., 500)
                        // or other non-critical errors from fetchUserProfile if refreshToken succeeded.
                        console.error('Non-critical error during initial load sequence (e.g., server error on refresh/profile, network issue):', error);
                        const errorMessage = error.response?.data?.message || // For Axios HTTP errors
                                         error.response?.statusText ||    // Fallback for HTTP errors
                                         error.message ||                 // For other JS errors
                                         'An unexpected error occurred during application initialization.';
                        setAuthState(prevState => ({
                            ...prevState,
                            loading: false,
                            isLoggedIn: !!authService.getAccessToken(), // Re-assess if still logged in based on token existence
                            error: errorMessage,
                        }));
                    }
                }
            } else {
                // No tokens or session doesn't indicate logged in state that requires refresh attempt.
                if (tokensExist) { // If tokens exist but session says logged out, clear inconsistent tokens.
                  authService.clearTokens();
                }
                sessionStorage.removeItem('auth'); // Clear any stale session state.
                setAuthState({ // Reset to a fully logged-out and clean state.
                    isLoggedIn: false,
                    user: null,
                    userId: null,
                    loading: false,
                    error: null, // Ensure error is cleared here too.
                });
            }
        };

        if (authState.loading) { // Only run if in initial loading state
            checkAuthAndFetchProfile();
        }
    }, [fetchUserProfile, authState.loading, handleLogout]);

    // Login handler - called after successful authService.verify... which returns { userId, accessToken, refreshToken ... }
    // Tokens are handled by authService directly.
    const login = useCallback(async (userIdFromAuth) => {
        setAuthState(prevState => ({
            ...prevState,
            isLoggedIn: true,
            userId: userIdFromAuth, // Store the userId returned by auth service
            loading: true, // Indicate that we are now fetching the profile
            error: null, // Clear any pre-existing error state upon new login attempt
        }));
        // Persist minimal auth state immediately (isLoggedIn and userId)
        // Error state is primarily transient UI feedback in AuthContext, not persisted to session storage here.
        sessionStorage.setItem('auth', JSON.stringify({ isLoggedIn: true, userId: userIdFromAuth, user: null, loading: true }));
        await fetchUserProfile(userIdFromAuth); // This will also ensure error: null on success
    }, [fetchUserProfile]);

    // Update user info in the context and persist to session storage
    const updateAuthContextUser = useCallback((updatedUserPartialData) => {
        setAuthState(prevState => {
            if (!prevState.user) return prevState; // Should not happen if logged in
            const newUserData = { ...prevState.user, ...updatedUserPartialData };
            const newAuthState = {
                ...prevState,
                user: newUserData,
            };
            sessionStorage.setItem('auth', JSON.stringify(newAuthState));
            return newAuthState;
        });
    }, []);

    // Function to call profileService.updateUserProfile and then update context
    const updateUserProfile = useCallback(async (profileUpdateData) => {
        if (!authState.isLoggedIn) {
            throw new Error("User is not logged in.");
        }
        try {
            const updatedProfile = await profileService.updateUserProfile(profileUpdateData);
            updateAuthContextUser(updatedProfile); // Update context with the response from service
            return updatedProfile;
        } catch (error) {
            console.error('Error updating user profile in AuthContext:', error);
            if (error.message && error.message.includes('Session expired')) { // Or check for specific error codes
                await handleLogout(false);
            }
            throw error;
        }
    }, [authState.isLoggedIn, updateAuthContextUser, handleLogout]);

    const isAuthenticated = useCallback(() => {
        return authState.isLoggedIn && !!authState.user && !authState.loading;
    }, [authState.isLoggedIn, authState.user, authState.loading]);

    return (
        <AuthContext.Provider value={{
            ...authState,
            login,
            logout: handleLogout,
            updateUserProfile, // Expose the new updateUserProfile
            fetchUserProfile, // Might be useful to expose for manual refresh
            isAuthenticated, // More robust isAuthenticated
        }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}; 