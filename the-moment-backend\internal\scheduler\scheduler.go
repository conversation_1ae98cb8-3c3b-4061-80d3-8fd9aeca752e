package scheduler

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/services"
	"context"
	"fmt"
	"time" // For time calculations

	"github.com/robfig/cron/v3"
	"github.com/rs/zerolog/log"
)

// SchedulerService handles scheduled tasks.
type SchedulerService struct {
	cronScheduler   *cron.Cron
	notificationSvc services.NotificationService
	dbQueries       *db.Queries // Added db.Queries
}

// NewSchedulerService creates a new SchedulerService.
func NewSchedulerService(notificationSvc services.NotificationService, queries *db.Queries) *SchedulerService {
	return &SchedulerService{
		cronScheduler:   cron.New(cron.WithSeconds()),
		notificationSvc: notificationSvc,
		dbQueries:       queries, // Initialize db.Queries
	}
}

// Start begins the cron scheduler.
func (s *SchedulerService) Start() {
	log.Info().Msg("Starting scheduler service...")
	// Add jobs here
	s.addEventReminderJob()

	s.cronScheduler.Start()
	log.Info().Msg("Scheduler service started.")
}

// Stop stops the cron scheduler.
func (s *SchedulerService) Stop() {
	log.Info().Msg("Stopping scheduler service...")
	s.cronScheduler.Stop()
	log.Info().Msg("Scheduler service stopped.")
}

// addEventReminderJob schedules the daily event reminder task.
func (s *SchedulerService) addEventReminderJob() {
	// Schedule to run daily, e.g., at 1 AM server time.
	// Adjust cron expression as needed. "0 1 * * *" means at 01:00 every day.
	// For testing, you might use a more frequent schedule like "@every 1m"
	spec := "0 0 1 * * *" // Daily at 1:00:00 AM
	jobID, err := s.cronScheduler.AddFunc(spec, func() {
		log.Info().Msg("Running daily event reminder job...")
		ctx := context.Background() // Create a new background context for the job
		s.sendEventReminders(ctx)
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to add daily event reminder job")
		return
	}
	log.Info().Str("cron_spec", spec).Int("job_id", int(jobID)).Msg("Successfully scheduled daily event reminder job.")
}

// sendEventReminders is the actual task executed by the cron job.
func (s *SchedulerService) sendEventReminders(ctx context.Context) {
	logger := log.Ctx(ctx).With().Str("task", "sendEventReminders").Logger()
	logger.Info().Msg("Executing sendEventReminders task.")

	// 1. Query the database for events starting "tomorrow".
	// Calculate tomorrow's date range
	now := time.Now()
	tomorrowStart := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
	tomorrowEnd := time.Date(now.Year(), now.Month(), now.Day()+2, 0, 0, 0, -1, now.Location()) // Up to the last nanosecond before the day after tomorrow

	logger.Info().Time("tomorrow_start", tomorrowStart).Time("tomorrow_end", tomorrowEnd).Msg("Querying events for tomorrow.")

	tomorrowEvents, err := s.dbQueries.GetEventsByStartTimeRange(ctx, db.GetEventsByStartTimeRangeParams{
		StartTime:   tomorrowStart,
		StartTime_2: tomorrowEnd, // Corrected parameter name
	})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to query events starting tomorrow for reminders")
		return
	}

	if len(tomorrowEvents) == 0 {
		logger.Info().Msg("No events starting tomorrow found. No reminders to send.")
		return
	}
	logger.Info().Int("event_count", len(tomorrowEvents)).Msg("Found events for tomorrow to send reminders for.")

	for _, event := range tomorrowEvents {
		logger := logger.With().Str("eventID", event.ID.String()).Str("eventName", event.Title).Logger() // Use event.Title

		// 2. For each upcoming event, retrieve all registered users (status 'registered').
		attendees, err := s.dbQueries.GetEventRegistrationsByEventIDAndStatus(ctx, db.GetEventRegistrationsByEventIDAndStatusParams{
			EventID: event.ID,
			Status:  db.EventRegistrationStatusTypeRegistered, // Use enum for status
		})
		if err != nil {
			logger.Error().Err(err).Msg("Failed to get registered users for event reminder")
			continue // Skip to next event
		}

		if len(attendees) == 0 {
			logger.Info().Msg("No confirmed attendees for this event. Skipping reminders.")
			continue
		}

		logger.Info().Int("user_count", len(attendees)).Msg("Sending reminders for event.")

		for _, attendeeReg := range attendees {
			// UserDisplayName is already available in attendeeReg from the SQLC query join
			var eventLocation string
			if event.LocationFullAddress != nil && *event.LocationFullAddress != "" {
				eventLocation = *event.LocationFullAddress
			} else if event.LocationOnlineUrl != nil && *event.LocationOnlineUrl != "" {
				eventLocation = *event.LocationOnlineUrl
			} else {
				eventLocation = "Details in event description"
			}

			// 3. For each registered user, call notificationService.SendToUser
			payload := map[string]interface{}{
				"EventName":       event.Title, // Use event.Title
				"EventDate":       event.StartTime.Format("January 2, 2006"),
				"EventTime":       event.StartTime.Format("3:04 PM MST"),
				"EventLocation":   eventLocation, // Adjusted event location
				"EventDetailsURL": fmt.Sprintf("/events/%s", event.ID.String()),
				"UserName":        attendeeReg.UserDisplayName, // Use UserDisplayName from attendeeReg
			}

			err := s.notificationSvc.SendToUser(ctx, attendeeReg.UserID, services.NotificationTypeEventReminder1Day, payload)
			if err != nil {
				logger.Error().Err(err).Str("userID", attendeeReg.UserID.String()).Msg("Failed to send event reminder notification")
			} else {
				logger.Info().Str("userID", attendeeReg.UserID.String()).Msg("Event reminder notification sent successfully.")
			}
		}
	}
	logger.Info().Msg("Finished sendEventReminders task.")
}
