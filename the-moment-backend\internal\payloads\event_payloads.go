package payloads

import (
	"Membership-SAAS-System-Backend/db"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// PageRequest is a generic struct for pagination parameters.
type PageRequest struct {
	Limit  int `query:"limit" validate:"omitempty,min=1,max=100"`
	Offset int `query:"offset" validate:"omitempty,min=0"`
}

// --- Event Request Payloads ---

// CreateEventRequest defines the payload for creating a new event.
type CreateEventRequest struct {
	// Title of the event
	Title string `json:"title" validate:"required,min=3,max=255" example:"Annual Charity Gala"`
	// JSON content for the event description (e.g., EditorJS format)
	JsonContent json.RawMessage `json:"jsonContent" validate:"required" swagger:"type:object" example:"{\"blocks\":[{\"type\":\"paragraph\",\"data\":{\"text\":\"Join us for a night of fun!\"}}]}"`
	// Type of event location (online, physical, hybrid)
	LocationType string `json:"location_type" validate:"required,oneof=online physical hybrid" example:"physical"`
	// Full address for physical or hybrid events
	LocationFullAddress *string `json:"location_full_address,omitempty" validate:"required_if=LocationType physical LocationType hybrid,omitempty,max=1000" example:"123 Event Hall, City"` // Consolidated address
	// Online URL for online or hybrid events
	LocationOnlineURL *string `json:"location_online_url,omitempty" validate:"required_if=LocationType online LocationType hybrid,omitempty,url,max=500" example:"http://zoom.us/meeting/abc-123"`
	// Start time of the event (RFC3339 format)
	StartTime time.Time `json:"start_time" validate:"required" example:"2025-12-01T18:00:00Z"`
	// End time of the event (RFC3339 format)
	EndTime time.Time `json:"end_time" validate:"required,gtfield=StartTime" example:"2025-12-01T22:00:00Z"`
	// Price of the event (text, e.g., "Free", "$10", "Contact for price")
	Price *string `json:"price,omitempty" validate:"omitempty,max=50" example:"$50"`
	// List of Government Funding Keys associated with the event
	GovernmentFundingKeys []string `json:"government_funding_keys,omitempty" validate:"omitempty,max=50" example:"[\"NSF_GRANT_123\", \"CITY_ARTS_FUND\"]"`
	// Maximum number of participants allowed
	ParticipantLimit *int32 `json:"participant_limit,omitempty" validate:"omitempty,min=0" example:"200"`
	// Maximum number of people allowed on the waitlist
	WaitlistLimit *int32 `json:"waitlist_limit,omitempty" validate:"omitempty,min=0" example:"50"`
	// Indicates if registration requires manual approval
	RequiresApprovalForRegistration bool `json:"requires_approval_for_registration" validate:"boolean" example:"false"`
	// List of Tag IDs associated with the event
	TagIDs []uuid.UUID `json:"tag_ids,omitempty" validate:"dive,uuid" example:"[\"123e4567-e89b-12d3-a456-************\"]"`
	// List of required verification type keys for registration
	RequiredVerificationTypeKeys []string `json:"verification_type_keys,omitempty" validate:"dive" example:"[\"hk_id_card\"]"`
	// Contact email for the event
	ContactEmail *string `json:"contact_email,omitempty" validate:"omitempty,email,max=255" example:"<EMAIL>"`
	// Contact phone number for the event (E.164 format)
	ContactPhone *string `json:"contact_phone" validate:"omitempty,e164"`
	// List of URLs for banner images
	MediaEventItem []string `json:"mediaeventitem,omitempty" validate:"dive,url,max=500" example:"[\"http://example.com/banner1.jpg\"]"`
	// Optional status for the event (defaults to 'draft' if not provided)
	Status *string `json:"status,omitempty" validate:"omitempty,oneof=published archived deleted draft hidden cancelled" example:"draft"`
	// Time for scheduled publication (RFC3339 format)
	PublishedAt *time.Time `json:"published_at,omitempty" form:"published_at,omitempty" example:"2025-12-31T10:00:00Z"`
}

// UpdateEventRequest defines the payload for updating an existing event.
type UpdateEventRequest struct {
	// Optional new title for the event
	Title *string `json:"title,omitempty" validate:"omitempty,min=3,max=255" example:"Annual Community Festival"`
	// Optional new JSON content for the event description
	JsonContent json.RawMessage `json:"jsonContent,omitempty" validate:"omitempty" swagger:"type:object" example:"{\"blocks\":[{\"type\":\"paragraph\",\"data\":{\"text\":\"Updated description.\"}}]}"`
	// Optional new location type
	LocationType *string `json:"location_type,omitempty" validate:"omitempty,oneof=online physical hybrid" example:"hybrid"`
	// Optional new full address for physical/hybrid events
	LocationFullAddress *string `json:"location_full_address,omitempty" validate:"omitempty,max=1000" example:"456 Community Center, Town"` // Consolidated address
	// Optional new online URL for online/hybrid events
	LocationOnlineURL *string `json:"location_online_url,omitempty" validate:"omitempty,url,max=500" example:"http://meet.google.com/xyz-789"`
	// Optional new start time
	StartTime *time.Time `json:"start_time,omitempty" example:"2025-12-10T09:00:00Z"`
	// Optional new end time
	EndTime *time.Time `json:"end_time,omitempty" validate:"omitempty,gtfield=StartTime" example:"2025-12-10T17:00:00Z"`
	// Optional new price
	Price *string `json:"price,omitempty" validate:"omitempty,max=50" example:"Free"`
	// Optional new list of Government Funding Keys (replaces existing list)
	GovernmentFundingKeys *[]string `json:"government_funding_keys,omitempty" validate:"omitempty,max=50" example:"[\"NEW_FED_GRANT_XYZ\"]"`
	// Optional new participant limit
	ParticipantLimit *int32 `json:"participant_limit,omitempty" validate:"omitempty,min=0" example:"250"`
	// Optional new waitlist limit
	WaitlistLimit *int32 `json:"waitlist_limit,omitempty" validate:"omitempty,min=0" example:"75"`
	// Optional new value for requiring registration approval
	RequiresApprovalForRegistration *bool `json:"requires_approval_for_registration,omitempty" validate:"omitempty,boolean" example:"true"`
	// Optional new list of Tag IDs (replaces existing list)
	TagIDs *[]uuid.UUID `json:"tag_ids,omitempty" validate:"omitempty,dive,uuid" example:"[\"123e4567-e89b-12d3-a456-************\"]"`
	// Optional new list of required verification type keys (replaces existing list)
	RequiredVerificationTypeKeys *[]string `json:"verification_type_keys,omitempty" validate:"omitempty,dive" example:"[\"student_id\"]"`
	// Optional new contact email
	ContactEmail *string `json:"contact_email,omitempty" validate:"omitempty,email,max=255" example:"<EMAIL>"`
	// Optional new contact phone number
	ContactPhone *string `json:"contact_phone,omitempty" validate:"omitempty,e164,max=20" example:"+15559876543"`
	// Optional new list of banner image URLs (replaces existing list)
	MediaEventItem *[]string `json:"mediaeventitem,omitempty" validate:"omitempty,dive,max=500" example:"[\"http://example.com/banner2.png\"]"`
	// Optional new status for the event
	Status *string `json:"status,omitempty" validate:"omitempty,oneof=published archived deleted draft hidden cancelled" example:"published"`
	// Optional new time for scheduled publication (RFC3339 format)
	PublishedAt *time.Time `json:"published_at,omitempty" form:"published_at,omitempty" example:"2026-01-15T10:00:00Z"`
}

/*
// UpdateEventStatusRequest defines the payload for updating an event's status.
type UpdateEventStatusRequest struct {
	// The new status for the event (e.g., 'published', 'archived', 'deleted', 'draft', 'hidden', 'cancelled')
	NewStatus string `json:"new_status" validate:"required,oneof=published archived deleted draft hidden cancelled" example:"published"`
}
*/

// UpdateRegistrationStatusRequest defines the payload for updating a registration status by an admin.
type UpdateRegistrationStatusRequest struct {
	// The new status for the registration (e.g., 'registered', 'pending_approval', 'cancelled_by_system', 'absent')
	NewStatus string `json:"new_status" validate:"required,oneof=registered pending_approval cancelled_by_system absent" example:"registered"` // Add other valid admin-settable statuses
	// Optional notes from the admin regarding the status change
	AdminNotes *string `json:"admin_notes,omitempty" example:"Approved after verification check."`
}

// UpdateOrgEventRegistrationStatusRequest defines the payload for updating a registration status by an organization admin.
type UpdateOrgEventRegistrationStatusRequest struct {
	// The new status for the registration (e.g., 'registered', 'cancelled_by_admin', 'rejected', 'waitlisted', 'attended')
	NewStatus string `json:"new_status" validate:"required,oneof=registered cancelled_by_admin rejected waitlisted attended" example:"approved"`
	// Optional notes from the admin regarding the status change
	AdminNotes *string `json:"admin_notes,omitempty" example:"Approved after reviewing application."`
}

// UpdatePaymentStatusRequest defines the payload for updating a registration's payment status.
type UpdatePaymentStatusRequest struct {
	// The new payment status (e.g., 'paid', 'pending', 'not_required', 'failed', 'refunded')
	NewPaymentStatus string `json:"new_payment_status" validate:"required,oneof=paid pending not_required failed refunded" example:"paid"`
	// The ID of the staff member making the change (required for auditing)
	StaffID uuid.UUID `json:"staff_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"` // Require ID of who made the change, even if not for auth
}

// ReviewVolunteerApplicationRequest defines the payload for reviewing a volunteer application by an admin.
type ReviewVolunteerApplicationRequest struct {
	// The new status for the application (pending, approved, rejected, or withdrawn)
	NewStatus string `json:"new_status" validate:"required,oneof=pending approved rejected withdrawn" example:"approved"`
	// Optional notes from the admin regarding the review
	AdminNotes *string `json:"admin_notes,omitempty" example:"Applicant has relevant skills."`
}

type ListPublicEventsRequest struct {
	PageRequest
	OrganizationID           *uuid.UUID  `query:"org_id" validate:"omitempty,uuid"`
	OrganizationID2          *uuid.UUID  `query:"org_id2" validate:"omitempty,uuid"`
	SearchTerm               *string     `query:"search_term"`
	StartDate                *time.Time  `query:"start_date"`
	EndDate                  *time.Time  `query:"end_date"`
	TagIDs                   []uuid.UUID `query:"tag_ids"`
	Status                   *string     `query:"status" validate:"omitempty,oneof=published archived deleted draft hidden cancelled"`
	GovernmentFundingKeys    []string    `query:"government_funding_keys" validate:"omitempty,dive,omitempty"`
	EventVerificationTypeKey *string     `query:"event_verification_type_key" validate:"omitempty"`
	// Location filters removed, search term can match address
}

// ListOrganizationEventsRequest defines the payload for listing events for a specific organization with filters.
type ListOrganizationEventsRequest struct {
	PageRequest
	SearchTerm               *string     `query:"search_term"`
	StartDate                *time.Time  `query:"start_date"`
	EndDate                  *time.Time  `query:"end_date"`
	TagIDs                   []uuid.UUID `query:"tag_ids"`
	Status                   *string     `query:"status" validate:"omitempty,oneof=published archived deleted draft hidden cancelled"`
	GovernmentFundingKeys    []string    `query:"government_funding_keys" validate:"omitempty,dive,omitempty"`
	EventVerificationTypeKey *string     `query:"event_verification_type_key" validate:"omitempty"`
	IsAdminView              bool        `query:"is_admin_view"` // To determine if admin-only fields should be returned
}

// --- Event Response Payloads ---
type EventResponse struct {
	ID                                uuid.UUID           `json:"id"`
	OrganizationID                    uuid.UUID           `json:"organization_id"`
	OrganizationName                  string              `json:"organization_name"`
	Title                             string              `json:"title"`
	JsonContent                       json.RawMessage     `json:"jsonContent" swagger:"type:object"`
	LocationType                      string              `json:"location_type"`
	LocationFullAddress               *string             `json:"location_full_address,omitempty"` // Consolidated address
	LocationOnlineURL                 *string             `json:"location_online_url,omitempty"`
	StartTime                         time.Time           `json:"start_time"`
	EndTime                           time.Time           `json:"end_time"`
	Price                             *string             `json:"price,omitempty"`
	GovernmentFundingKeys             []string            `json:"government_funding_keys,omitempty"`
	Status                            string              `json:"status"`
	ParticipantLimit                  *int32              `json:"participant_limit,omitempty"`
	WaitlistLimit                     *int32              `json:"waitlist_limit,omitempty"`
	RequiresApprovalForRegistration   bool                `json:"requires_approval_for_registration"`
	CreatedByUserID                   uuid.UUID           `json:"created_by_user_id"`
	CreatedAt                         time.Time           `json:"created_at"`
	UpdatedAt                         time.Time           `json:"updated_at"`
	PublishedAt                       *time.Time          `json:"published_at,omitempty"`
	ContactEmail                      *string             `json:"contact_email,omitempty"`
	ContactPhone                      *string             `json:"contact_phone,omitempty"`
	MediaEventItem                    []string            `json:"mediaeventitem,omitempty"`
	Tags                              []TagResponse       `json:"tags,omitempty"`
	RequiredVerificationTypeKeys      []string            `json:"verification_type_keys,omitempty"`
	RegisteredCount                   int64               `json:"registered_count"`
	WaitlistedCount                   int64               `json:"waitlisted_count"`
	AttendedCount                     int64               `json:"attended_count"`
	CurrentUserRegistrationStatus     *string             `json:"current_user_registration_status,omitempty"`
	CurrentUserRegistrationID         *uuid.UUID          `json:"current_user_registration_id,omitempty"`
	CurrentUserVolunteerStatus        *string             `json:"current_user_volunteer_status,omitempty"`
	CurrentUserVolunteerApplicationID *uuid.UUID          `json:"current_user_volunteer_application_id,omitempty"`
	MediaItems                        []MediaItemResponse `json:"media_items,omitempty"` // Added for generic media
}

type PublicEventResponse struct {
	ID                                uuid.UUID           `json:"id"`
	OrganizationID                    uuid.UUID           `json:"organization_id"`
	OrganizationName                  string              `json:"organization_name"`
	Title                             string              `json:"title"`
	JsonContent                       json.RawMessage     `json:"jsonContent" swagger:"type:object"`
	LocationType                      string              `json:"location_type"`
	LocationFullAddress               *string             `json:"location_full_address,omitempty"` // Consolidated address
	LocationOnlineURL                 *string             `json:"location_online_url,omitempty"`
	StartTime                         time.Time           `json:"start_time"`
	EndTime                           time.Time           `json:"end_time"`
	Price                             *string             `json:"price,omitempty"`
	GovernmentFundingKeys             []string            `json:"government_funding_keys,omitempty"`
	Status                            string              `json:"status"`
	ParticipantLimit                  *int32              `json:"participant_limit,omitempty"`
	MediaEventItem                    []string            `json:"mediaeventitem,omitempty"`
	MediaItems                        []MediaItemResponse `json:"media_items,omitempty"` // Added for generic media
	Tags                              []TagResponse       `json:"tags,omitempty"`
	RequiredVerificationTypeKeys      []string            `json:"verification_type_keys,omitempty"`
	ContactEmail                      *string             `json:"contact_email,omitempty"`
	ContactPhone                      *string             `json:"contact_phone,omitempty"`
	CurrentUserRegistrationStatus     *string             `json:"current_user_registration_status,omitempty"`
	CurrentUserRegistrationID         *uuid.UUID          `json:"current_user_registration_id,omitempty"`
	CurrentUserVolunteerStatus        *string             `json:"current_user_volunteer_status,omitempty"`
	CurrentUserVolunteerApplicationID *uuid.UUID          `json:"current_user_volunteer_application_id,omitempty"`
	PublishedAt                       *time.Time          `json:"published_at,omitempty"`

	// New Fields
	RegisteredCount int64  `json:"registered_count"`         // Number of users currently registered for the event.
	WaitlistedCount int64  `json:"waitlisted_count"`         // Number of users currently on the waitlist for the event.
	AttendedCount   int64  `json:"attended_count"`           // Number of users who attended the event.
	WaitlistLimit   *int32 `json:"waitlist_limit,omitempty"` // The maximum number of users allowed on the waitlist.
}

// --- Event Media Payloads ---

// AddEventMediaRequest is used by the handler to pass data to the service after processing a file upload.
// Fields like FilePath are determined server-side during the save operation.
// The client typically sends a multipart/form-data request with a 'file' part.
// Other optional metadata (like a client-suggested FileName or a Description if we add it) can be sent as other form parts.
type AddEventMediaRequest struct {
	FileName string // Derived from uploaded file by handler
	FileType string // Derived from uploaded file by handler (MIME type)
	FileSize int64  // Derived from uploaded file by handler
	// EventID, OrgID, UserID are usually passed as separate params to the service method
}

// MediaItemResponse defines the structure for a media item associated with an event or post.
type MediaItemResponse struct {
	ID         uuid.UUID `json:"id"`
	FileName   string    `json:"file_name"`
	FilePath   string    `json:"file_path"` // This will be the public URL
	FileType   string    `json:"file_type"`
	FileSize   int64     `json:"file_size"`
	UploadedAt time.Time `json:"uploaded_at"`
	IsBanner   bool      `json:"is_banner"` // @Description "Indicates if this media item is the designated banner for the event."
}

// --- Event Tag Payloads ---

type CreateEventTagRequest struct {
	NameEn             string  `json:"name_en" validate:"required,min=1,max=100"`
	NameZhHk           string  `json:"name_zh_hk" validate:"required,min=1,max=100"`
	NameZhCn           string  `json:"name_zh_cn" validate:"required,min=1,max=100"`
	DescriptionEn      *string `json:"description_en"`
	DescriptionZhHk    *string `json:"description_zh_hk"`
	DescriptionZhCn    *string `json:"description_zh_cn"`
	IsGloballyApproved bool    `json:"is_globally_approved"`
}

type UpdateEventTagRequest struct {
	NameEn             *string `json:"name_en,omitempty" validate:"omitempty,min=1,max=100"`
	NameZhHk           *string `json:"name_zh_hk,omitempty" validate:"omitempty,min=1,max=100"`
	NameZhCn           *string `json:"name_zh_cn,omitempty" validate:"omitempty,min=1,max=100"`
	DescriptionEn      *string `json:"description_en,omitempty"`
	DescriptionZhHk    *string `json:"description_zh_hk,omitempty"`
	DescriptionZhCn    *string `json:"description_zh_cn,omitempty"`
	IsGloballyApproved *bool   `json:"is_globally_approved,omitempty"`
}

// --- Event Registration Payloads ---

type EventRegistrationResponse struct {
	db.EventRegistration
	EventTitle               string              `json:"event_title"`
	EventStartTime           time.Time           `json:"event_start_time"`
	EventEndTime             time.Time           `json:"event_end_time,omitempty"`
	EventDescription         *string             `json:"event_description,omitempty"`
	EventLocationType        string              `json:"event_location_type,omitempty"`
	EventLocationFullAddress *string             `json:"event_location_full_address,omitempty"`
	EventLocationOnlineURL   *string             `json:"event_location_online_url,omitempty"`
	EventStatus              string              `json:"event_status,omitempty"`
	EventOrganizationID      *uuid.UUID          `json:"event_organization_id,omitempty"`
	EventOrganizationName    *string             `json:"event_organization_name,omitempty"`
	EventPrice               *string             `json:"event_price,omitempty"`
	EventContactEmail        *string             `json:"event_contact_email,omitempty"`
	EventContactPhone        *string             `json:"event_contact_phone,omitempty"`
	RegisteredCount          int                 `json:"registered_count,omitempty"`
	WaitlistedCount          int                 `json:"waitlisted_count,omitempty"`
	MediaItems               []MediaItemResponse `json:"media_items,omitempty"` // Added MediaItems

	UserDisplayName string  `json:"user_display_name"`
	UserEmail       *string `json:"user_email,omitempty"`
	UserPhone       *string `json:"user_phone,omitempty"`
}

type MarkAttendanceResponse struct {
	RegistrationID uuid.UUID `json:"registration_id"`
	EventID        uuid.UUID `json:"event_id"`
	UserID         uuid.UUID `json:"user_id"`
	Status         string    `json:"status"` // Should be 'attended'
	AttendedAt     time.Time `json:"attended_at"`
	Message        string    `json:"message"`
	PaymentStatus  string    `json:"payment_status"`
	// Potentially include other unpaid org events info here
}

// --- Event Volunteering Payloads ---

type EventVolunteerApplicationResponse struct {
	db.EventVolunteerApplication
	EventTitle               string              `json:"event_title"`
	EventStartTime           time.Time           `json:"event_start_time"`
	EventEndTime             time.Time           `json:"event_end_time,omitempty"`
	EventDescription         *string             `json:"event_description,omitempty"`
	EventLocationType        string              `json:"event_location_type,omitempty"`
	EventLocationFullAddress *string             `json:"event_location_full_address,omitempty"`
	EventLocationOnlineURL   *string             `json:"event_location_online_url,omitempty"`
	EventStatus              string              `json:"event_status,omitempty"`
	EventOrganizationID      *uuid.UUID          `json:"event_organization_id,omitempty"`
	OrganizationName         string              `json:"organization_name,omitempty"`
	EventPrice               *string             `json:"event_price,omitempty"`
	EventContactEmail        *string             `json:"event_contact_email,omitempty"`
	EventContactPhone        *string             `json:"event_contact_phone,omitempty"`
	MediaItems               []MediaItemResponse `json:"media_items,omitempty"`
	UserDisplayName          string              `json:"user_display_name"`
	UserEmail                *string             `json:"user_email,omitempty"`
	UserPhone                *string             `json:"user_phone,omitempty"`
	ReviewerDisplayName      *string             `json:"reviewer_display_name,omitempty"`
}

// --- Event Statistics Payloads ---

type UserEventStatisticItem struct {
	EventID                   uuid.UUID `json:"event_id"`
	EventTitle                string    `json:"event_title"`
	EventStartTime            time.Time `json:"event_start_time"`
	EventStatus               string    `json:"event_status"`        // Status of the event itself (e.g., 'past')
	RegistrationStatus        string    `json:"registration_status"` // User's registration status for this event
	RegistrationPaymentStatus string    `json:"registration_payment_status"`
	Attended                  bool      `json:"attended"`
}

type OrgEventStatisticItem struct {
	EventID                    uuid.UUID `json:"event_id"`
	EventTitle                 string    `json:"event_title"`
	EventStartTime             time.Time `json:"event_start_time"`
	EventStatus                string    `json:"event_event_status"` // Status of the event itself
	TotalRegisteredOrFinalized int64     `json:"total_registered_or_finalized"`
	TotalAttended              int64     `json:"total_attended"`
	TotalPaidRegistrations     int64     `json:"total_paid_registrations"`
	// Potentially add attendance rate: TotalAttended / TotalRegisteredOrFinalized (if finalized means registered, attended, or absent)
}

// TagResponse defines the response for a single tag.
type TagResponse struct {
	ID                 uuid.UUID `json:"id"`
	NameEn             string    `json:"name_en"`
	NameZhHk           string    `json:"name_zh_hk"`
	NameZhCn           string    `json:"name_zh_cn"`
	DescriptionEn      *string   `json:"description_en,omitempty"`
	DescriptionZhHk    *string   `json:"description_zh_hk,omitempty"`
	DescriptionZhCn    *string   `json:"description_zh_cn,omitempty"`
	IsGloballyApproved bool      `json:"is_globally_approved"`
}

// ToPublicEventListResponseFromOrgRows converts []db.ListPublicEventsByOrganizationRow to []PublicEventResponse
func ToPublicEventListResponseFromOrgRows(rows []db.ListPublicEventsByOrganizationRow) []PublicEventResponse {
	responses := make([]PublicEventResponse, 0, len(rows))
	for _, row := range rows {
		dbEvent := row.Event // Access the embedded event data

		// Basic conversion (adapt fields as needed for PublicEventResponse)
		resp := PublicEventResponse{
			ID:                  dbEvent.ID,
			OrganizationID:      dbEvent.OrganizationID,
			Title:               dbEvent.Title,
			JsonContent:         dbEvent.DescriptionContent, // Map from DB column
			LocationType:        string(dbEvent.LocationType),
			LocationFullAddress: dbEvent.LocationFullAddress,
			LocationOnlineURL:   dbEvent.LocationOnlineUrl,
			StartTime:           dbEvent.StartTime,
			EndTime:             dbEvent.EndTime,
			Status:              string(dbEvent.Status),
			ParticipantLimit:    dbEvent.ParticipantLimit,
		}
		responses = append(responses, resp)
	}
	return responses
}

// PopularEvent defines the structure for items in the /popular-events list.
type PopularEvent struct {
	ID        string    `json:"id"` // UUID as string
	Title     string    `json:"title"`
	StartTime time.Time `json:"startTime"` // Use time.Time
	Location  *string   `json:"location"`  // Nullable
}

// EventVolunteerCountResponse defines the response for event volunteer count
type EventVolunteerCountResponse struct {
	EventID                   uuid.UUID `json:"event_id"`
	VolunteerApplicationCount int64     `json:"volunteer_application_count"`
}

// WithdrawEventVolunteerApplicationRequest is the payload for a user to withdraw their event volunteer application.
type WithdrawEventVolunteerApplicationRequest struct {
	// Notes string `json:"notes,omitempty"`
}

// UserEventStatusInfo holds registration status for a user concerning a specific event.
type UserEventStatusInfo struct {
	RegistrationStatus     *string    `json:"registration_status,omitempty"`
	RegistrationID         *uuid.UUID `json:"registration_id,omitempty"`
	VolunteerApplicationID *uuid.UUID `json:"volunteer_application_id,omitempty"` // Added for completeness
	VolunteerStatus        *string    `json:"volunteer_status,omitempty"`         // Added for completeness
}
