import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Platform,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Image,
} from 'react-native';
import { Searchbar, Chip, Card, Portal, Modal, Button, RadioButton } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { format, parseISO, startOfDay as dfnsStartOfDay, addDays as dfnsAddDays, endOfDay as dfnsEndOfDay, Locale } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { fromZonedTime, toZonedTime } from 'date-fns-tz';
import { appStyleStore } from 'stores/app_style_store';
import { organizationStore } from 'stores/organization_store';
import {
  PublishedEventListPullRequest,
  EventListPayload,
  EventTagPayload,
} from '@/api/api_config';
import { createTheme } from 'theme/index';
import { useFetchExploreEvents, useFetchEventTags } from '@/api/public_events_services';
import { eventTagsStore } from 'stores/public_events_store';
import { processEventImageUrls } from '@/utils/imageUtils';
import { ErrorView } from '@/common_modules/ErrorView';

// Define ALL_ORGANIZATIONS_ID constant to match restored version
const ALL_ORGANIZATIONS_ID = '00000000-0000-0000-0000-000000000002';

interface FilterState {
  searchText: string;
  eventTypes: string[];
  dateRange: string[];
  orgId?: string;
  sortBy?: string;
  governmentFundingKeys?: string[];
  eventVerificationTypeKey?: string;
}

// Helper function to get tag name based on current language
const getTagName = (tag: EventTagPayload, language: string): string => {
  if (!tag) return '';
  if (language.startsWith('zh')) {
    if (language.includes('HK')) {
      return tag.name_zh_hk || tag.name_en || '';
    } else {
      return tag.name_zh_cn || tag.name_en || '';
    }
  } else {
    return tag.name_en || '';
  }
};

export default function EventsListScreen() {
  const { t, i18n } = useTranslation();
  const storeTheme = appStyleStore(state => state.theme);
  const activeTheme = storeTheme || createTheme('red');
  const contextSelectedOrgId = organizationStore(state => state.selectedOrganization?.id);
  const router = useRouter();
  const params = useLocalSearchParams<{ focusSearch?: string; filters?: string; organizationId?: string }>();
  const searchBarRef = useRef<any>(null);
  const [filterVisible, setFilterVisible] = useState(false);
  
  // UI state (immediate updates)
  const [displaySearchText, setDisplaySearchText] = useState('');
  const [displayFilters, setDisplayFilters] = useState<FilterState>({
    searchText: '',
    eventTypes: [],
    dateRange: ['next30Days'],
    orgId: undefined,
    sortBy: undefined,
    governmentFundingKeys: undefined,
    eventVerificationTypeKey: undefined,
  });
  
  // API state (debounced updates)
  const [apiFilters, setApiFilters] = useState<FilterState>({
    searchText: '',
    eventTypes: [],
    dateRange: ['next30Days'],
    orgId: undefined,
    sortBy: undefined,
    governmentFundingKeys: undefined,
    eventVerificationTypeKey: undefined,
  });
  
  const [tempFilters, setTempFilters] = useState<FilterState>(displayFilters);
  const [offset, setOffset] = useState(0);
  const [allEvents, setAllEvents] = useState<EventListPayload[]>([]);
  const [hasMoreEvents, setHasMoreEvents] = useState(true);
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});
  
  // Simplified loading states
  const [isMainLoading, setIsMainLoading] = useState(true); // For new data (initial, search, filter, refresh)
  const [isLoadingMore, setIsLoadingMore] = useState(false); // For pagination
  
  // RefreshControl state (separate from main loading)
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const PAGE_SIZE = 20;
  const LOAD_MORE_THRESHOLD = 15;

  // Debounce refs for different operations
  const filterDebounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  const availableEventTags = eventTagsStore(state => state.eventTags);
  const isLoadingTags = eventTagsStore(state => state.isFetching);
  const tagsError = eventTagsStore(state => state.error);

  // Fetch event tags
  const eventTagsQuery = useFetchEventTags();

  const getLocale = (language: string): Locale => {
    switch (language.toLowerCase().split('-')[0]) {
      case 'zh':
        return zhCN;
      default:
        return enUS;
    }
  };

  const getDateRangeFromPreset = useCallback((preset: string): { start_date?: string; end_date?: string } => {
    if (preset === 'all' || !preset) return {};
    const now = new Date();
    let startDate: Date;
    let endDate: Date;
    switch (preset) {
      case 'today': 
        startDate = dfnsStartOfDay(now);
        endDate = dfnsEndOfDay(now);
        break;
      case 'tomorrow': 
        startDate = dfnsStartOfDay(dfnsAddDays(now, 1));
        endDate = dfnsEndOfDay(dfnsAddDays(now, 1));
        break;
      case 'next7Days': 
        startDate = dfnsStartOfDay(now);
        endDate = dfnsEndOfDay(dfnsAddDays(now, 6));
        break;
      case 'next30Days': 
        startDate = dfnsStartOfDay(now);
        endDate = dfnsEndOfDay(dfnsAddDays(now, 29));
        break;
      default: return {};
    }
    return {
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
    };
  }, []);

  const currentQueryParams = React.useMemo<PublishedEventListPullRequest>(() => {
    const { start_date, end_date } = getDateRangeFromPreset(apiFilters.dateRange[0]);
    
    return {
      limit: PAGE_SIZE,
      offset,
      status: 'published',
      search_term: apiFilters.searchText,
      tagIds: apiFilters.eventTypes.length > 0 ? apiFilters.eventTypes : undefined,
      org_id: apiFilters.orgId,
      org_id2: ALL_ORGANIZATIONS_ID,
      government_funding_keys: apiFilters.governmentFundingKeys?.length ? apiFilters.governmentFundingKeys : undefined,
      event_verification_type_key: apiFilters.eventVerificationTypeKey,
      start_date,
      end_date,
    };
  }, [apiFilters, offset, getDateRangeFromPreset, PAGE_SIZE]);

  const { 
    data: eventsData,
    isLoading: isLoadingEvents,
    isFetching: isFetchingEvents,
    error: eventsError,
    refetch: refetchEvents,
  } = useFetchExploreEvents(currentQueryParams);

  useEffect(() => {
    if (eventsData) {
      const newEvents = eventsData as EventListPayload[];
      if (offset === 0) { 
        // Always replace data when offset is 0 (fresh fetch or refresh)
        setAllEvents(newEvents);
        // Only set main loading to false if we're not refreshing
        if (!isRefreshing) {
          setIsMainLoading(false);
        }
      } else { 
        // Append data for pagination
        setAllEvents(prevEvents => [...prevEvents, ...newEvents]);
        setIsLoadingMore(false);
      }
      setHasMoreEvents(newEvents.length === PAGE_SIZE);
    }
  }, [eventsData, offset, isRefreshing]);

  // Update loading states based on React Query states
  useEffect(() => {
    // Don't show main loading when refreshing - only use native refresh indicator
    if (isRefreshing) {
      return;
    }
    
    if (offset === 0) {
      // For new data requests (offset 0), use main loading
      setIsMainLoading(isLoadingEvents || isFetchingEvents);
    } else {
      // For pagination requests (offset > 0), use load more loading
      setIsLoadingMore(isFetchingEvents);
    }
  }, [isLoadingEvents, isFetchingEvents, offset, isRefreshing]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    // Reset offset for refresh, but keep existing data visible during refresh
    setOffset(0);
    // Don't clear allEvents immediately to prevent blank screen flash
    try {
      await refetchEvents();
    } finally {
      setIsRefreshing(false);
    }
  }, [refetchEvents]);

  // Debounced API update for filters
  const updateApiFiltersWithFilterDebounce = useCallback((newFilters: FilterState) => {
    // Show loading state and clear data for filter changes
    setIsMainLoading(true);
    setOffset(0); 
    setAllEvents([]);
    
    // Debounce only the API call
    if (filterDebounceTimerRef.current) clearTimeout(filterDebounceTimerRef.current);
    filterDebounceTimerRef.current = setTimeout(() => {
      setApiFilters(newFilters);
    }, 1000);
  }, []);

  // Immediate API update (no debounce)
  const updateApiFiltersImmediately = useCallback((newFilters: FilterState) => {
    // Check if filters actually changed to avoid unnecessary loading state
    const filtersChanged = JSON.stringify(newFilters) !== JSON.stringify(apiFilters);
    if (!filtersChanged) {
      return; // No change, no need to update
    }
    
    // Show loading state and clear data for filter/search changes
    setIsMainLoading(true);
    setOffset(0); 
    setAllEvents([]);
    setApiFilters(newFilters);
  }, [apiFilters]);

  // Handle focus search using useFocusEffect
  useFocusEffect(
    useCallback(() => {
      if (params?.focusSearch === 'true' && searchBarRef.current) {
        const timer = setTimeout(() => { 
          searchBarRef.current?.focus();
          // Clear the focus parameter after focusing to prevent re-triggering on tab switches
          router.setParams({ focusSearch: undefined });
        }, 300);
        return () => clearTimeout(timer);
      }
    }, [params?.focusSearch])
  );

  useEffect(() => {
    if (params?.filters) {
      try {
        const parsedFilters = JSON.parse(params.filters as string);
        const newFiltersFromParams: Partial<FilterState> = {
          dateRange: parsedFilters.dateRange || displayFilters.dateRange,
          eventTypes: Array.isArray(parsedFilters.eventTypes) ? parsedFilters.eventTypes : displayFilters.eventTypes,
          searchText: parsedFilters.searchText !== undefined ? parsedFilters.searchText : displayFilters.searchText,
          sortBy: parsedFilters.sortBy !== undefined ? parsedFilters.sortBy : displayFilters.sortBy,
          governmentFundingKeys: parsedFilters.governmentFundingKeys !== undefined ? parsedFilters.governmentFundingKeys : displayFilters.governmentFundingKeys,
          eventVerificationTypeKey: parsedFilters.eventVerificationTypeKey !== undefined ? parsedFilters.eventVerificationTypeKey : displayFilters.eventVerificationTypeKey,
        };
        const updatedFilters = { ...displayFilters, ...newFiltersFromParams };
        setDisplayFilters(updatedFilters);
        setTempFilters(updatedFilters);
        setDisplaySearchText(updatedFilters.searchText);
        updateApiFiltersImmediately(updatedFilters);
      } catch (e) {
        console.error("Failed to parse filters from params", e);
      }
    }
  }, [params?.filters, updateApiFiltersImmediately]);

  useEffect(() => {
    let effectiveOrgId: string | undefined = undefined;
    if (params?.organizationId) {
      effectiveOrgId = params.organizationId;
    } else if (contextSelectedOrgId) {
      effectiveOrgId = contextSelectedOrgId;
    }    
    if (displayFilters.orgId !== effectiveOrgId) {
        const updatedFilters = { ...displayFilters, orgId: effectiveOrgId };
        setDisplayFilters(updatedFilters);
        setTempFilters(updatedFilters);
        updateApiFiltersImmediately(updatedFilters);
    }
  }, [params?.organizationId, contextSelectedOrgId, displayFilters, updateApiFiltersImmediately]);

  const dateRangeOptions = [
    { id: 'all', label: t('explore.filters.date.all') },
    { id: 'today', label: t('explore.filters.date.today') },
    { id: 'tomorrow', label: t('explore.filters.date.tomorrow') },
    { id: 'next7Days', label: t('explore.filters.date.next7Days') },
    { id: 'next30Days', label: t('explore.filters.date.next30Days') },
  ];

  const handleLoadMore = useCallback(() => {
    // Only trigger load more if we have events, no errors, not loading, and still have more data
    if (!isLoadingMore && hasMoreEvents && !isMainLoading && allEvents.length > 0 && !eventsError) {
      setIsLoadingMore(true);
      setOffset(prev => prev + PAGE_SIZE);
    }
  }, [isLoadingMore, hasMoreEvents, isMainLoading, allEvents.length, eventsError]);

  const onScroll = useCallback(({ nativeEvent }: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    if (isCloseToBottom) {
      handleLoadMore();
    }
  }, [handleLoadMore]);

  // Search handling: immediate UI update + API call only on submit
  const handleSearchChange = (text: string) => {
    // Immediate UI update only
    setDisplaySearchText(text);
    setDisplayFilters(prev => ({ ...prev, searchText: text }));
  };
  
  const handleSearchSubmit = () => {
    // Apply current search text immediately
    updateApiFiltersImmediately({ ...displayFilters, searchText: displaySearchText });
  };

  const handleSearchClear = () => {
    // Immediate UI update and API call
    setDisplaySearchText('');
    const updatedFilters = { ...displayFilters, searchText: '' };
    setDisplayFilters(updatedFilters);
    updateApiFiltersImmediately(updatedFilters);
  };

  const showFilterModal = () => { setTempFilters(displayFilters); setFilterVisible(true); };
  const hideFilterModal = () => setFilterVisible(false);

  // Apply filters immediately (no debounce for better UX)
  const applyFilters = () => {
    setDisplayFilters(tempFilters);
    setDisplaySearchText(tempFilters.searchText);
    updateApiFiltersImmediately(tempFilters);
    hideFilterModal();
  };

  const resetFilters = () => {
    const defaultFilters: FilterState = {
      searchText: '',
      eventTypes: [],
      dateRange: ['next30Days'],
      orgId: contextSelectedOrgId ? contextSelectedOrgId : undefined,
      sortBy: undefined,
      governmentFundingKeys: undefined,
      eventVerificationTypeKey: undefined,
    };
    setTempFilters(defaultFilters);
  };

  // Filter chip removal: immediate UI update + debounced API call
  const handleRemoveFilter = (filterType: keyof FilterState, value?: string) => {
    let updatedFilters = { ...displayFilters };
    
    if (filterType === 'eventTypes' && value) {
      updatedFilters.eventTypes = updatedFilters.eventTypes.filter(id => id !== value);
    } else if (filterType === 'dateRange') {
      updatedFilters.dateRange = ['all'];
    }
    
    // Immediate UI update
    setDisplayFilters(updatedFilters);
    
    // Debounced API call
    updateApiFiltersWithFilterDebounce(updatedFilters);
  };

  const handleDateRangeSelect = (optionId: string) => {
    setTempFilters(prev => ({ ...prev, dateRange: [optionId] }));
  };

  const handleImageError = (eventId: string) => {
    setImageErrors(prev => ({ ...prev, [eventId]: true }));
  };

  const renderEventCard = (event: EventListPayload) => {
    const participantLimit = event.participant_limit ?? 0;
    const isFree = event.price === "0" || !event.price;

    // Process event images using utility function
    const processedEvent = processEventImageUrls(event);
    const imageUrl = processedEvent.processed_image_url;

    return (
      <TouchableOpacity
        key={event.id}
        onPress={() => router.push(`/explore/events/EventDetailsScreen?eventId=${event.id}&orgId=${event.organization_id}`)}
        style={styles.cardWrapper}
      >
        <Card style={styles.card}>
          <View style={styles.cardImageContainer}>
            {imageUrl && !imageErrors[event.id] ? (
              <Image
                source={{ uri: imageUrl }}
                style={styles.cardImage}
                onError={() => handleImageError(event.id)}
              />
            ) : (
              <View style={[styles.cardImage, { backgroundColor: activeTheme.colors.primaryContainer, alignItems: 'center', justifyContent: 'center' }]}>
                <MaterialCommunityIcons name="image-off" size={48} color="#666666" />
              </View>
            )}
          </View>
          <Card.Content style={styles.cardContent}>
            <View style={styles.dateLocationContainer}>
              <View style={styles.iconTextContainer}>
                <MaterialCommunityIcons name="calendar-outline" size={16} color="#666666" />
                <Text style={styles.dateText}>
                  {format(parseISO(event.start_time), 'yyyy-MM-dd HH:mm', { locale: getLocale(i18n.language) })}
                </Text>
              </View>
              {(event.location_full_address || event.location_online_url) && (
                <View style={styles.locationContainer}>
                  <Text style={styles.locationText} numberOfLines={1} ellipsizeMode="tail">
                    {event.location_full_address || event.location_online_url}
                  </Text>
                  <MaterialCommunityIcons name="map-marker-outline" size={16} color="#666666" />
                </View>
              )}
            </View>
            <Text style={styles.cardTitle} numberOfLines={2}>{event.title}</Text>

            <View style={styles.cardFooter}>
              {isFree ? (
                <View style={styles.freeContainer}>
                  <Text style={styles.freeText}>{t('explore.free')}</Text>
                </View>
              ) : (
                <View style={styles.paidContainer}>
                  <Text style={styles.paidText}>{`HK$${event.price}`}</Text>
                </View>
              )}
            </View>
          </Card.Content>
        </Card>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Searchbar
            ref={searchBarRef}
            placeholder={t('explore.searchPlaceholder.events')}
            onChangeText={handleSearchChange}
            value={displaySearchText}
            style={styles.searchBar}
            returnKeyType="search"
            onSubmitEditing={handleSearchSubmit}
            onClearIconPress={handleSearchClear}
            inputStyle={styles.searchInput}
            iconColor="#666666"
            placeholderTextColor="#666666"
          />
          <TouchableOpacity onPress={showFilterModal} style={styles.filterButton}>
            <MaterialCommunityIcons name="filter-variant" size={24} color={activeTheme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {(displayFilters.eventTypes.length > 0 || (displayFilters.dateRange.length > 0 && displayFilters.dateRange[0] !== 'all')) && (
        <View style={styles.filterTags}>
          {displayFilters.eventTypes.map(typeId => {
            const tag = availableEventTags.find((t: EventTagPayload) => t.id === typeId);
            return (
              <Chip
                key={typeId}
                mode="outlined"
                onClose={() => handleRemoveFilter('eventTypes', typeId)}
                style={styles.filterChip}
              >
                {tag ? getTagName(tag, i18n.language) : t('common.unknown')} 
              </Chip>
            );
          })}
          {displayFilters.dateRange[0] !== 'all' && (
            <Chip
              key={displayFilters.dateRange[0]}
              mode="outlined"
              onClose={() => handleRemoveFilter('dateRange')}
              style={styles.filterChip}
            >
              {dateRangeOptions.find(d => d.id === displayFilters.dateRange[0])?.label || displayFilters.dateRange[0]}
            </Chip>
          )}
        </View>
      )}

      {/* Loading state - outside ScrollView for better performance */}
      {isMainLoading && allEvents.length === 0 && !isRefreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={activeTheme.colors.primary} />
          <Text style={styles.loadingText}>{t('common.loading')}</Text>
        </View>
      ) : !isMainLoading && eventsError && !isRefreshing ? (
        /* Error state - outside ScrollView, no scrolling needed */
        <ErrorView 
          onRetry={() => {
            setIsMainLoading(true);
            setOffset(0);
            setAllEvents([]);
          }}
        />
      ) : (
        /* Content state - inside ScrollView for normal scrolling */
        <ScrollView
          style={styles.eventsList}
          contentContainerStyle={styles.eventsListContent}
          showsVerticalScrollIndicator={false}
          onScroll={onScroll}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor={activeTheme.colors.primary}
              colors={[activeTheme.colors.primary]}
            />
          }
        >
          {!isMainLoading && allEvents.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <MaterialCommunityIcons name="calendar-remove-outline" size={64} color="#666666" />
              <Text style={styles.noEventsTitle}>{t('explore.noEventsTitle')}</Text>
              <Text style={styles.noEventsText}>{t('explore.noEventsText')}</Text>
            </View>
          ) : (
            allEvents.map(renderEventCard)
          )}
          {isLoadingMore && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={activeTheme.colors.primary} />
              <Text style={styles.loadingText}>{t('explore.loadingMore')}</Text>
            </View>
          )}
        </ScrollView>
      )}

      <Portal>
        <Modal
          visible={filterVisible}
          onDismiss={hideFilterModal}
          contentContainerStyle={styles.modalContainer}
        >
          <View style={styles.modalContent}> 
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('explore.filters.title')}</Text>
              <TouchableOpacity onPress={resetFilters} style={styles.resetButton}>
                <Text style={[styles.resetButtonText, { color: activeTheme.colors.primary }]}>{t('explore.filters.reset')}</Text>
              </TouchableOpacity>
            </View>

            <ScrollView 
                style={styles.modalScrollContent}
                contentContainerStyle={styles.modalScrollContentContainer}
            >
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>{t('explore.filters.eventTypes')}</Text>
                <View style={styles.chipContainer}>
                  <Chip
                    key="all-types-chip"
                    mode="flat"
                    selected={tempFilters.eventTypes.length === 0}
                    onPress={() => setTempFilters(prev => ({ ...prev, eventTypes: [] }))}
                    style={[
                      styles.filterChip,
                      tempFilters.eventTypes.length === 0 && { backgroundColor: activeTheme.colors.primary }
                    ]}
                    textStyle={[
                      styles.filterChipText,
                      tempFilters.eventTypes.length === 0 && styles.selectedFilterChipText
                    ]}
                    showSelectedCheck={false}
                  >
                    {t('explore.filters.allEventTypes')} 
                  </Chip>
                  {availableEventTags.map((tag: EventTagPayload) => (
                    <Chip
                      key={tag.id}
                      mode="flat"
                      selected={tempFilters.eventTypes.includes(tag.id)}
                      onPress={() => {
                        setTempFilters(prev => {
                          const newEventTypes = prev.eventTypes.includes(tag.id)
                            ? prev.eventTypes.filter(id => id !== tag.id)
                            : [...prev.eventTypes, tag.id];
                          return { ...prev, eventTypes: newEventTypes };
                        });
                      }}
                      style={[
                        styles.filterChip,
                        tempFilters.eventTypes.includes(tag.id) && { backgroundColor: activeTheme.colors.primary }
                      ]}
                      textStyle={[
                        styles.filterChipText,
                        tempFilters.eventTypes.includes(tag.id) && styles.selectedFilterChipText
                      ]}
                      showSelectedCheck={false}
                    >
                      {getTagName(tag, i18n.language)} 
                    </Chip>
                  ))}
                </View>
              </View>

              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>{t('explore.filters.date.title')}</Text>
                <RadioButton.Group
                  onValueChange={handleDateRangeSelect}
                  value={tempFilters.dateRange[0]}
                >
                  {dateRangeOptions.map(option => (
                    <TouchableOpacity
                      key={option.id}
                      style={styles.radioItem}
                      onPress={() => handleDateRangeSelect(option.id)}
                    >
                      <RadioButton.Android value={option.id} color={activeTheme.colors.primary} />
                      <Text style={styles.radioLabel}>{option.label}</Text>
                    </TouchableOpacity>
                  ))}
                </RadioButton.Group>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <Button
                mode="outlined"
                onPress={hideFilterModal}
                style={[styles.modalButton, {borderColor: activeTheme.colors.primary}] }
                labelStyle={[styles.modalButtonLabel, {color: activeTheme.colors.primary}]}
              >
                {t('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={applyFilters}
                style={[styles.modalButton, { backgroundColor: activeTheme.colors.primary, borderRadius: 12 }]}
                labelStyle={[styles.applyButtonLabel, { color: activeTheme.colors.onPrimary }]}
              >
                {t('common.apply')}
              </Button>
            </View>
          </View>
        </Modal>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchBar: {
    flex: 1,
    elevation: 0,
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    height: 42,
  },
  searchInput: { 
    fontSize: 15, 
    minHeight: 0, 
    lineHeight: Platform.OS === 'ios' ? undefined: 18,
  },
  filterButton: {
    width: 48,
    height: 42,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    margin: 20,
    backgroundColor: 'transparent',
    height: '80%',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    height: '100%',
  },
  modalScrollContent: {
    flex: 1,
  },
  modalScrollContentContainer: {
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  resetButton: {
    padding: 8,
  },
  resetButtonText: {
    fontWeight: '500',
  },
  filterSection: {
    marginBottom: 16,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterChip: {
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    height: 32,
  },
  filterChipText: {
    color: '#666666',
    fontSize: 15,
  },
  selectedFilterChipText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  radioLabel: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    borderRadius: 12,
  },
  modalButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  applyButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  eventsList: {
    flex: 1,
  },
  eventsListContent: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
    gap: 16,
  },
  card: {
    borderRadius: 12,
    marginBottom: 4,
    marginHorizontal: 4,
    backgroundColor: '#FFFFFF',
  },
  cardWrapper: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  cardImageContainer: {
    overflow: 'hidden',
  },
  cardImage: {
    backgroundColor: '#FFFFFF',
    height: 160,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  cardContent: {
    padding: 16,
  },
  dateLocationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  iconTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    maxWidth: '45%',
    marginLeft: 4,
  },
  locationText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'right',
    marginRight: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    lineHeight: 24,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 4,
    lineHeight: 20,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  freeContainer: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: '#5DB075',
    minWidth: 60,
    alignItems: 'center',
  },
  freeText: {
    fontWeight: '600',
    fontSize: 14,
    color: '#FFFFFF',
  },
  paidContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e95612',
    backgroundColor: 'transparent',
    minWidth: 60,
    alignItems: 'center',
  },
  paidText: {
    fontWeight: '600',
    fontSize: 14,
    color: '#e95612',
  },
  attendeesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  attendeesText: {
    color: '#666666',
    fontSize: 15,
  },
  fullAttendees: {
    color: '#FF3B30',
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 24,
  },
  loadingText: {
    fontSize: 15,
    color: '#666666',
    marginTop: 16,
  },
  filterTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
    paddingHorizontal: 24,
  },
  noEventsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  noEventsText: {
    fontSize: 15,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
  eventTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    lineHeight: 24,
  },
});