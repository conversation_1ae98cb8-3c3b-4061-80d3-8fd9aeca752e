import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, SafeAreaView, Image, StatusBar } from 'react-native';
import { Button, List, Avatar } from 'react-native-paper';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useRouter, Stack, useFocusEffect } from 'expo-router';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { TextDialog } from '@/common_modules/TextDialog';
import { TextInputDialog } from '@/common_modules/TextInputDialog';
import { LanguageSwitch } from '@/common_modules/LanguageSwitch';
import { AuthModal } from '@/common_modules/AuthModal';
import OrganizationManagement from '@/user-profile/organization_management/organization_management';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { VerificationTypeEnum, VerificationStatusEnum } from 'types/enums';
import { format, parseISO } from 'date-fns';
import { useLogout } from '@/api/authentication_services';
import { authenticationStore } from 'stores/authentication_store';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import type { MD3Theme as CustomTheme } from 'react-native-paper';
import {
  userProfileStore,
  userOrganizationsStore,
  userVerificationsStore,
} from 'stores/user_store';
import { userEventVolunteerApplicationsListStore } from 'stores/volunteer_store';
import {
  useFetchUserProfile,
  useUpdateUserProfile,
  useUploadProfileIcon,
  useFetchUserOrganizations,
  useFetchUserVerifications
} from '@/api/user_services';
import { useFetchUserEventVolunteerApplicationsList } from '@/api/volunteer_services';
import {
  useFetchOrganizationList,
  useJoinOrganization,
  useLeaveOrganization
} from '@/api/organization_services';
import {
  ProfileResponse as BaseUserSettings,
  OrganizationListPayload,
  VerificationPayload,
  EventVolunteerApplicationPayload,
} from '@/api/api_config';
import type { Organization } from 'types/organization';

type IconName = keyof typeof Ionicons.glyphMap;
type MaterialIconName = keyof typeof MaterialCommunityIcons.glyphMap;

// Define types locally if not available globally or causing issues
// Assuming Organization is part of OrganizationListPayload
type VerificationListItem = VerificationPayload;

const ListItem = ({
  icon,
  materialIcon,
  title,
  description,
  onPress,
  disabled = false,
  theme
}: {
  icon?: IconName;
  materialIcon?: MaterialIconName;
  title: string;
  description?: string | React.ReactElement;
  onPress?: () => void;
  disabled?: boolean;
  theme: CustomTheme;
}) => {
  return (
    <TouchableOpacity
      onPress={disabled ? undefined : onPress}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
        backgroundColor: theme.colors.background,
        minHeight: 64,
      }}
      activeOpacity={disabled ? 1 : 0.7}
    >
      <View style={{
        width: 36,
        height: 36,
        borderRadius: 10,
        backgroundColor: theme.colors.primaryContainer,
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        {materialIcon ? (
          <MaterialCommunityIcons
            name={materialIcon}
            size={22}
            color={theme.colors.primary}
          />
        ) : (
          <Ionicons
            name={icon as IconName}
            size={22}
            color={theme.colors.primary}
          />
        )}
      </View>
      <View style={{
        flex: 1,
        marginLeft: 12,
        justifyContent: 'center',
      }}>
        <View style={{
          gap: 2,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '500',
            color: theme.system.text,
            lineHeight: 20,
          }}>{title}</Text>
          {description && (
             typeof description === 'string' ? (
              <Text style={{
                fontSize: 14,
                color: theme.system.secondaryText,
                lineHeight: 18,
              }}>{description}</Text>
             ) : ( description )
          )}
        </View>
      </View>
      {!disabled && (
        <Ionicons
          name="chevron-forward"
          size={20}
          color="#CCC"
        />
      )}
    </TouchableOpacity>
  );
};

const getStatusInfo = (status: VerificationStatusEnum | 'unverified' | 'verified') => {
  switch (status) {
    case 'unverified':
      return {
        icon: 'alert-circle-outline' as const,
        color: '#FF9800',
        textKey: 'unverified'
      };
    case VerificationStatusEnum.Pending:
      return {
        icon: 'time-outline' as const,
        color: '#757575',
        textKey: 'pending'
      };
    case VerificationStatusEnum.Approved:
      return {
        icon: 'checkmark-circle-outline' as const,
        color: '#4CAF50',
        textKey: 'verified'
      };
    case VerificationStatusEnum.Rejected:
      return {
        icon: 'close-circle-outline' as const,
        color: '#FF3B30',
        textKey: 'rejected'
      };
    default:
      return {
        icon: 'help-circle-outline' as const, 
        color: '#757575',
        textKey: 'unknown'
      };
  }
};

const ALL_ORGANIZATIONS_ID = '00000000-0000-0000-0000-000000000002';

const RELEVANT_APPLICATION_TYPES = [
  VerificationTypeEnum.HomeVisit,
  VerificationTypeEnum.HkIDCard,
  VerificationTypeEnum.MainlandChinaIDCard,
  VerificationTypeEnum.MainlandTravelPermit,
  VerificationTypeEnum.Passport,
  VerificationTypeEnum.HkYouthPlus,
  VerificationTypeEnum.AddressProof,
  VerificationTypeEnum.StudentID,
];

const DOCUMENT_VERIFICATION_TYPES = [
  VerificationTypeEnum.HkIDCard,
  VerificationTypeEnum.MainlandChinaIDCard,
  VerificationTypeEnum.MainlandTravelPermit,
  VerificationTypeEnum.Passport,
  VerificationTypeEnum.HkYouthPlus,
  VerificationTypeEnum.AddressProof,
  VerificationTypeEnum.StudentID,
];

export function ProfileScreen() {
  const { t, i18n } = useTranslation();
  const router = useRouter();

  const storedTheme = appStyleStore(state => state.theme);
  const theme = storedTheme || createTheme('red');

  const logoutMutation = useLogout();
  const { data: userProfile, isLoading: profileLoading, isError: profileError, refetch: refetchUserProfile } = useFetchUserProfile();
  const { mutateAsync: updateUserProfile, isPending: isUpdatingProfile } = useUpdateUserProfile();
  const { mutateAsync: uploadProfileIcon, isPending: isUploadingIcon } = useUploadProfileIcon();
  const { data: userOrganizationsData, isLoading: userOrgsLoading, refetch: refetchUserOrganizations } = useFetchUserOrganizations() as { data?: OrganizationListPayload[], isLoading: boolean, refetch: () => void };
  const { data: verificationsData, isLoading: verificationsLoading, refetch: refetchVerifications } = useFetchUserVerifications();
  const { data: volunteerAppsData, isLoading: volunteerAppsLoading, refetch: refetchVolunteerApps } = useFetchUserEventVolunteerApplicationsList({ limit: 100, offset: 0 }) as { data: EventVolunteerApplicationPayload[] | undefined, isLoading: boolean, refetch: () => void };

  const { data: allOrganizationsData, isLoading: allOrgsLoading, refetch: refetchAllOrganizations } = useFetchOrganizationList() as { data?: OrganizationListPayload[], isLoading: boolean, refetch: () => void };
  const joinOrganizationMutation = useJoinOrganization();
  const leaveOrganizationMutation = useLeaveOrganization();

  const isAuthenticated = authenticationStore(state => state.isAuthenticated);
  const currentRefreshToken = authenticationStore(state => state.refreshToken);

  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const [avatarUri, setAvatarUri] = useState<string | null>(null);
  const [usernameDialogVisible, setUsernameDialogVisible] = useState(false);
  const [newUsername, setNewUsername] = useState('');
  const [usernameError, setUsernameError] = useState<string | null>(null);
  const [isLanguageModalVisible, setIsLanguageModalVisible] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);
  const [showVerificationDialog, setShowVerificationDialog] = useState(false);
  const [confirmDialogProps, setConfirmDialogProps] = useState<React.ComponentProps<typeof CustomDialog>>({
    title: '',
    message: '',
    confirmText: t('common.confirm'),
    cancelText: t('common.cancel'),
    visible: false,
    onConfirm: () => {},
    onCancel: () => setConfirmDialogProps((prev: React.ComponentProps<typeof CustomDialog>) => ({ ...prev, visible: false })),
  });
  const [showHkidVerificationDialog, setShowHkidVerificationDialog] = useState(false);
  const [showOrgJoinModal, setShowOrgJoinModal] = useState(false);

  const [textDialogProps, setTextDialogProps] = useState<React.ComponentProps<typeof TextDialog>>({
    visible: false,
    title: '',
    message: '',
    confirmText: t('common.ok'),
    onConfirm: () => setTextDialogProps(prev => ({ ...prev, visible: false })),
  });
  
  useEffect(() => {
    if (userProfile?.display_name && !usernameDialogVisible) {
      setNewUsername(userProfile.display_name);
    }
  }, [userProfile?.display_name, usernameDialogVisible]);

  const latestApplicationDate = React.useMemo(() => {
    const currentVerifications = verificationsData || [];
    if (currentVerifications.length === 0) {
      return null;
    }
    const relevantVerifications = currentVerifications.filter((v: VerificationListItem) =>
      RELEVANT_APPLICATION_TYPES.includes(v.verification_type as VerificationTypeEnum) && v.updated_at
    );

    if (relevantVerifications.length === 0) {
      return null;
    }

    relevantVerifications.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    return relevantVerifications[0].updated_at;
  }, [verificationsData]);

  const handleJoinOrganizations = async (selectedOrgIdsFromModal?: string[]) => {
    if (!selectedOrgIdsFromModal) {
      setShowOrgJoinModal(false);
      return;
    }
    try {
      const currentOrgArray: OrganizationListPayload[] = userOrganizationsData || [];
      const currentOrgIds = currentOrgArray.map((org) => org.id).filter((id: string | null): id is string => id !== null && id !== ALL_ORGANIZATIONS_ID);
      const orgsToJoin = selectedOrgIdsFromModal.filter((id: string) => !currentOrgIds.includes(id));
      const orgsToLeave = currentOrgIds.filter((id: string) => !selectedOrgIdsFromModal.includes(id));

      for (const orgId of orgsToJoin) {
        await joinOrganizationMutation.mutateAsync({ orgId });
      }
      for (const orgId of orgsToLeave) {
        await leaveOrganizationMutation.mutateAsync({ orgId });
      }
      
      await refetchUserOrganizations();
      setTextDialogProps({
        visible: true,
        title: t('profile.organization.updateSuccess.title'),
        message: t('profile.organization.updateSuccess.message'),
        confirmText: t('common.ok'),
        type: 'success',
        onConfirm: () => setTextDialogProps(prev => ({ ...prev, visible: false })),
      });
    } catch (error) {
      console.error('[ProfileScreen] Error updating organization memberships:', error);
      setTextDialogProps({
        visible: true,
        title: t('profile.organization.updateError.title'),
        message: t('profile.organization.updateError.message'),
        confirmText: t('common.ok'),
        type: 'warning',
        onConfirm: () => setTextDialogProps(prev => ({ ...prev, visible: false })),
      });
    } finally {
      setShowOrgJoinModal(false);
    }
  };

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    contentContainer: {
      paddingBottom: 16,
    },
    profileSection: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: theme.system.border,
      backgroundColor: theme.colors.background,
    },
    avatarContainer: {
      marginRight: 20,
      position: 'relative',
      borderRadius: 40,
      borderWidth: 2,
      borderColor: theme.system.secondaryText,
      borderStyle: 'dashed',
      padding: 0.8,
    },
    uploadingOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(255, 255, 255, 0.7)',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 40,
    },
    userInfoContainer: {
      flex: 1,
      justifyContent: 'center',
      gap: 4,
    },
    usernameContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 2,
    },
    displayName: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.system.text,
      marginRight: 8,
    },
    phoneNumber: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.system.secondaryText,
      marginBottom: 2,
    },
    emailText: {
      fontSize: 14,
      color: theme.system.secondaryText,
      marginBottom: 8,
    },
    verificationBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 4,
      alignSelf: 'flex-start',
      marginTop: 4,
      maxWidth: '100%',
    },
    verificationText: {
      fontSize: 12,
      fontWeight: '600',
    },
    editButton: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      borderRadius: 12,
      padding: 4,
      borderWidth: 1,
      borderColor: theme.colors.background,
    },
    verifiedBadge: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      backgroundColor: theme.colors.primaryContainer,
      marginTop: 8,
    },
    userStatus: {
      fontSize: 16,
      fontWeight: '500',
    },
    section: {
      paddingTop: 12,
      backgroundColor: theme.colors.background,
      borderBottomWidth: 1,
      borderBottomColor: theme.system.border,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.system.secondaryText,
      marginLeft: 16,
      marginBottom: 4,
      lineHeight: 20,
    },
    logoutSection: {
      marginTop: 16,
      paddingHorizontal: 24,
      paddingVertical: 16,
      backgroundColor: theme.colors.background,
    },
    logoutButton: {
      borderColor: theme.colors.error,
      borderRadius: 12,
      borderWidth: 2,
      marginBottom: 8,
    },
    logoutButtonContent: {
      height: 44,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
    },
    logoutButtonLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.error,
    },
    deleteAccountContainer: {
      alignItems: 'center',
      paddingVertical: 12,
    },
    deleteAccountText: {
      color: theme.colors.error,
      textDecorationLine: 'underline',
      fontWeight: '500',
    },
    notLoggedInContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: 20,
      backgroundColor: theme.colors.background,
    },
    notLoggedInTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.system.text,
      marginTop: 16,
      marginBottom: 8,
      textAlign: 'center',
    },
    notLoggedInMessage: {
      fontSize: 16,
      color: theme.system.secondaryText,
      textAlign: 'center',
      marginBottom: 24,
      lineHeight: 22,
    },
    notLoggedInButtonContainer: {
      width: '100%',
      maxWidth: 280,
      marginTop: 24,
    },
    loginButton: {
      borderRadius: 12,
      width: '100%',
    },
    loginButtonContent: {
      height: 44,
    },
    orgSwitcherContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.background,
    },
  });

  useFocusEffect(
    React.useCallback(() => {
      console.log('[ProfileScreen] Focus effect: Fetching data');
      if (isAuthenticated) {
        refetchUserProfile();
        refetchVerifications();
        refetchVolunteerApps();
        refetchUserOrganizations();
      }
      refetchAllOrganizations();
    }, [isAuthenticated, refetchUserProfile, refetchVerifications, refetchVolunteerApps, refetchUserOrganizations, refetchAllOrganizations])
  );

  const handleLogout = async () => {
    setShowLogoutDialog(true);
  };

  const handleConfirmLogout = async () => {
    if (!currentRefreshToken) {
      console.error('Logout error: Refresh token is missing.');
      setTextDialogProps({
        visible: true,
        title: t('profile.logoutError'),
        message: t('profile.logoutErrorDesc'),
        confirmText: t('common.ok'),
        type: 'warning',
        onConfirm: () => setTextDialogProps(prev => ({ ...prev, visible: false })),
      });
      setShowLogoutDialog(false);
      return;
    }
    try {
      await logoutMutation.mutateAsync({ refresh_token: currentRefreshToken });
      // Navigate to dashboard/home page after successful logout
      router.replace('/tabs');
    } catch (error: any) {
      console.error('Logout failed in component:', error);
      setTextDialogProps({
        visible: true,
        title: t('profile.logoutError'),
        message: t('profile.logoutErrorDesc'),
        confirmText: t('common.ok'),
        type: 'warning',
        onConfirm: () => setTextDialogProps(prev => ({ ...prev, visible: false })),
      });
    } finally {
      setShowLogoutDialog(false);
    }
  };

  const handleNavigateToHelpCenter = () => router.push('/documents/HelpCenterScreen');
  const handleNavigateToPrivacyPolicy = () => router.push('/documents/PrivacyPolicyScreen');
  const handleNavigateToTermsOfService = () => router.push('/documents/TermsOfServiceScreen');
  const handleNavigateToApplicationStatus = () => router.push('/user-profile/identification/IdentifyStatusScreen');
  const handleNavigateToNotificationSettings = () => {
    if (userProfile) {
      router.push({
        pathname: '/user-profile/NotificationSettingsScreen',
        params: { profile: JSON.stringify(userProfile) }
      });
    }
  };
  const handleNavigateToChangePhone = () => router.push('/user-profile/changePhone/ChangePhoneGuideScreen');
  const handleNavigateToStats = () => router.push('/user-profile/StatsScreen');
  const handleNavigateToVolunteerApplication = () => {
    if (getVerificationStatus(VerificationTypeEnum.HkIDCard) !== VerificationStatusEnum.Approved) {
      setShowHkidVerificationDialog(true);
      return;
    }
    router.push('/user-profile/volunteer-application/VolunteerStatusScreen');
  };

  const getVerificationStatus = (type: VerificationTypeEnum) => {
    const currentVerifications = verificationsData || [];
    const verification = currentVerifications.find(v => v.verification_type === type.toString());
    return verification?.status as VerificationStatusEnum || VerificationStatusEnum.Unverified;
  };

  const handlePickAvatar = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      setTextDialogProps({
        visible: true,
        title: t('common.dialog.warning'),
        message: t('identity.permissions.gallery'),
        confirmText: t('common.ok'),
        type: 'warning',
        onConfirm: () => setTextDialogProps(prev => ({ ...prev, visible: false })),
      });
      return;
    }
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5,
    });
    if (!result.canceled && result.assets && result.assets[0].uri) {
      const assetUri = result.assets[0].uri;
      try {
        const formData = new FormData();
        const filename = assetUri.split('/').pop() || 'avatar.jpg';
        const match = /\.(\w+)$/.exec(filename);
        const type = match ? `image/${match[1]}` : `image`;
        formData.append('file', { uri: assetUri, name: filename, type } as any);
        await uploadProfileIcon(formData);
        setAvatarUri(assetUri);
      } catch (error: any) {
        console.error("Avatar processing error in component:", error);
        setTextDialogProps({
          visible: true,
          title: t('common.dialog.error'),
          message: t('profile.avatar.error'),
          confirmText: t('common.ok'),
          type: 'warning',
          onConfirm: () => setTextDialogProps(prev => ({ ...prev, visible: false })),
        });
      }
    }
  };

  const handleEditUsername = () => {
    setNewUsername(userProfile?.display_name || '');
    setUsernameDialogVisible(true);
    setUsernameError(null);
  };

  const handleSaveUsername = async () => {
    if (!newUsername.trim()) {
      setUsernameError(t('profile.usernameRequired'));
      return;
    }
    try {
      await updateUserProfile({ display_name: newUsername.trim() });
      setUsernameDialogVisible(false);
    } catch (error: any) {
      console.error("Username update error:", error);
      setUsernameError(t('profile.usernameUpdateError'));
    }
  };

  const getUserVerificationStatus = () => {
    // Show loading state if verifications are still loading
    if (verificationsLoading) {
      return { 
        text: t('common.loading'), 
        color: '#757575', 
        borderColor: '#757575', 
        icon: 'time' as IconName 
      };
    }
    
    const idTypes = [VerificationTypeEnum.HkIDCard, VerificationTypeEnum.MainlandChinaIDCard];
    const idStatuses = idTypes.map(type => getVerificationStatus(type));
    if (idStatuses.some(s => s === VerificationStatusEnum.Approved)) return { text: t('auth.verifiedAccount'), color: '#4CAF50', borderColor: '#4CAF50', icon: 'checkmark-circle' as IconName };
    if (idStatuses.some(s => s === VerificationStatusEnum.Pending)) return { text: t('auth.underReviewAccount'), color: '#757575', borderColor: '#757575', icon: 'time' as IconName };
    return { text: t('auth.unverifiedAccount'), color: '#FF9800', borderColor: '#FF9800', icon: 'alert-circle' as IconName };
  };

  const getUnifiedStatusSummary = (statuses: VerificationStatusEnum[], noApplicationsText: string) => {
    const hasRejected = statuses.some(s => s === VerificationStatusEnum.Rejected);
    const hasApproved = statuses.some(s => s === VerificationStatusEnum.Approved);
    const hasPending = statuses.some(s => s === VerificationStatusEnum.Pending);
    if (hasRejected) return { text: t('profile.appStatus.summary.someRejected'), color: theme.colors.error, icon: 'close-circle-outline' as IconName };
    if (hasApproved) return { text: t('profile.appStatus.summary.someApproved'), color: theme.colors.primary, icon: 'checkmark-circle-outline' as IconName };
    if (hasPending) return { text: t('profile.appStatus.summary.somePending'), color: theme.system.secondaryText, icon: 'time-outline' as IconName };
    return { text: noApplicationsText, color: theme.system.secondaryText, icon: null };
  };

  const getApplicationStatusSummaryText = (): string => {
    const itemStatuses = RELEVANT_APPLICATION_TYPES.map(type => getVerificationStatus(type));
    return getUnifiedStatusSummary(itemStatuses, t('profile.appStatus.summary.allUnverifiedOrNotSubmitted')).text;
  };

  const getApplicationSummaryIconAndColor = () => {
    const itemStatuses = RELEVANT_APPLICATION_TYPES.map(type => getVerificationStatus(type));
    return { color: getUnifiedStatusSummary(itemStatuses, t('profile.appStatus.summary.allUnverifiedOrNotSubmitted')).color };
  };

  const handleFeaturePress = (action: () => void, requiresAuth: boolean = false) => {
    if (requiresAuth && !isAuthenticated) {
      setPendingAction(() => action);
      setShowAuthModal(true);
    } else {
      action();
    }
  };

  const handleLogin = () => {
    setShowAuthModal(false);
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
  };
  
  if (profileLoading && !userProfile) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center'}]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (profileError && !userProfile) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center', padding: 20}]}>
        <Text style={{fontSize: 16, color: theme.system.text, textAlign: 'center', marginBottom: 12}}>{t('error.message')}</Text>
        <Button onPress={() => { refetchUserProfile(); refetchUserOrganizations();}} mode="outlined">{t('error.retry')}</Button>
      </View>
    );
  }

  return (
    <View style={styles.safeArea}>
      {isAuthenticated ? (
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
          <View style={styles.profileSection}>
            <TouchableOpacity 
              onPress={handlePickAvatar} 
              style={styles.avatarContainer} 
              disabled={isUploadingIcon}
              activeOpacity={0.7}
            >
              <Avatar.Image 
                size={80} 
                source={avatarUri ? { uri: avatarUri } : (userProfile?.profile_picture_url ? { uri: userProfile.profile_picture_url } : require('@/assets/default-images/default-image.jpg'))}
                style={{ backgroundColor: '#FFFFFF' }}
              />
              {isUploadingIcon && 
                <View style={styles.uploadingOverlay}>
                  <ActivityIndicator color={theme.colors.primary} />
                </View>
              }
            </TouchableOpacity>
            
            <View style={styles.userInfoContainer}>
              <View style={styles.usernameContainer}>
                <Text style={styles.displayName} numberOfLines={1}>
                  {userProfile?.display_name || t('profile.guest')}
                </Text>
                <TouchableOpacity onPress={handleEditUsername} hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
                  <MaterialCommunityIcons name="pencil" size={16} color={theme.colors.primary} />
                </TouchableOpacity>
              </View>
              
              {userProfile?.phone && (
                <Text style={styles.phoneNumber}>{userProfile.phone}</Text>
              )}
              
              {userProfile?.email && (
                <Text style={styles.emailText}>{userProfile.email}</Text>
              )}
              
              {isAuthenticated && (
                <View 
                  style={[
                    styles.verificationBadge, 
                    { 
                      borderColor: getUserVerificationStatus().borderColor,
                      borderWidth: 1
                    }
                  ]}
                >
                  <Ionicons name={getUserVerificationStatus().icon} size={14} color={getUserVerificationStatus().color} />
                  <Text style={[styles.verificationText, { color: getUserVerificationStatus().color }]}>
                    {getUserVerificationStatus().text}
                  </Text>
                </View>
              )}
            </View>
          </View>

          <TouchableOpacity 
            onPress={() => setShowOrgJoinModal(true)} 
            activeOpacity={0.7}
            style={styles.orgSwitcherContainer}
          >
            <Image
              source={require('@/assets/default-images/default-logo.png')}
              style={{ width: 42, height: 42, borderRadius: 21 }}
              resizeMode="contain"
            />
            <View style={{flex: 1, marginLeft: 12}}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: theme.system.text, marginBottom: 4 }}>
                {t('profile.organization.manageOrganizations')}
              </Text>
              <Text style={{ fontSize: 14, color: theme.system.secondaryText }} numberOfLines={1}>
                {t('profile.organization.tapToManage')}
              </Text>
            </View>
            <MaterialCommunityIcons name="chevron-right" size={24} color="#CCC" />
          </TouchableOpacity>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('profile.sections.verification')}</Text>
            <ListItem
              theme={theme}
              icon="file-tray-full"
              title={t('profile.applicationStatus')}
              description={
                verificationsLoading
                  ? <Text style={{ fontSize: 14, color: theme.system.secondaryText, lineHeight: 18 }}>{t('common.loading')}</Text>
                  : (
                    <Text style={{ color: theme.system.secondaryText, fontSize: 14, lineHeight: 18 }}>
                      {getApplicationStatusSummaryText()}{
                        latestApplicationDate && ` (${t('profile.latestApplication')}: ${format(parseISO(latestApplicationDate), 'MMM d')})`
                      }
                    </Text>
                  )
              }
              onPress={() => handleFeaturePress(handleNavigateToApplicationStatus, true)}
              disabled={verificationsLoading}
            />
            <ListItem
              theme={theme}
              materialIcon="account-heart"
              title={t('applications.types.volunteer')}
              description={(() => {
                if (volunteerAppsLoading && (!volunteerAppsData || volunteerAppsData.length === 0)) {
                  return <Text style={{ fontSize: 14, color: theme.system.secondaryText, lineHeight: 18 }}>{t('common.loading')}</Text>;
                }
                const currentVolunteerApplications = (volunteerAppsData as any)?.items || volunteerAppsData || [];
                const statuses = currentVolunteerApplications.map((app: EventVolunteerApplicationPayload) => {
                  if (app.status === 'approved') return VerificationStatusEnum.Approved;
                  if (app.status === 'pending') return VerificationStatusEnum.Pending;
                  if (app.status === 'rejected') return VerificationStatusEnum.Rejected;
                  return VerificationStatusEnum.Unverified;
                });
                const statusSummary = getUnifiedStatusSummary(statuses, t('profile.appStatus.noApplications'));
                return (
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>

                    {/* {statusSummary.icon && <Ionicons name={statusSummary.icon} size={16} color={statusSummary.color} style={{ marginRight: 6 }} />} */}
                    <Text style={{ fontSize: 14, color: theme.system.secondaryText, lineHeight: 18 }}>{statusSummary.text}</Text>
                  </View>
                );
              })()}
              onPress={() => handleFeaturePress(handleNavigateToVolunteerApplication, true)}
              disabled={volunteerAppsLoading && (!volunteerAppsData || volunteerAppsData.length === 0)}
            />
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('profile.sections.accountSettings')}</Text>
            <ListItem theme={theme} icon="call" title={t('profile.items.phoneNumber.title')} description={t('profile.items.phoneNumber.description')} onPress={() => handleFeaturePress(handleNavigateToChangePhone, true)} />
            <ListItem theme={theme} icon="notifications" title={t('profile.items.notifications.title')} description={t('profile.items.notifications.description')} onPress={() => handleFeaturePress(handleNavigateToNotificationSettings, true)} />
            <ListItem theme={theme} icon="language" title={t('profile.items.language.title')} description={t('profile.items.language.description')} onPress={() => setIsLanguageModalVisible(true)} />
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('profile.sections.supportAndAbout')}</Text>
            <ListItem theme={theme} icon="stats-chart" title={t('profile.items.myStats.title')} description={t('profile.items.myStats.description')} onPress={() => handleFeaturePress(handleNavigateToStats, true)} />
            <ListItem theme={theme} icon="help-circle" title={t('profile.items.helpCenter.title')} description={t('profile.items.helpCenter.description')} onPress={() => handleFeaturePress(handleNavigateToHelpCenter, true)} />
            <ListItem theme={theme} icon="document-text" title={t('profile.items.termsOfService.title')} onPress={() => handleFeaturePress(handleNavigateToTermsOfService, true)} />
            <ListItem theme={theme} icon="shield-checkmark" title={t('profile.items.privacyPolicy.title')} onPress={() => handleFeaturePress(handleNavigateToPrivacyPolicy, true)} />
          </View>

          <View style={styles.logoutSection}>
            <Button
              mode="outlined"
              onPress={handleLogout}
              icon={({size, color}) => <MaterialCommunityIcons name="logout" size={24} color={color} />}
              style={styles.logoutButton}
              contentStyle={styles.logoutButtonContent}
              labelStyle={styles.logoutButtonLabel}
              textColor={theme.colors.error}
              loading={logoutMutation.isPending}
              disabled={logoutMutation.isPending}
            >
              {t('profile.logout')}
            </Button>
            
            <TouchableOpacity 
              onPress={() => router.push('/user-profile/deleteAccount/DeleteAccountGuideScreen')}
              style={styles.deleteAccountContainer}
              activeOpacity={0.7}
            >
              <Text style={styles.deleteAccountText}>
                {t('profile.deleteAccount.title')}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      ) : (
        <View style={styles.notLoggedInContainer}>
          <MaterialCommunityIcons name="account-lock" size={64} color={theme.colors.primary} />
          <Text style={styles.notLoggedInTitle}>{t('auth.loginRequired')}</Text>
          <Text style={styles.notLoggedInMessage}>{t('auth.loginRequiredMessage')}</Text>
          <View style={styles.notLoggedInButtonContainer}>
            <Button mode="contained" onPress={() => router.push('/tabs/login')} style={styles.loginButton} contentStyle={styles.loginButtonContent} buttonColor={theme.colors.primary}>
              {t('auth.loginButton')}
            </Button>
          </View>
        </View>
      )}

      <TextDialog {...textDialogProps} />
      <CustomDialog
        visible={showLogoutDialog}
        title={t('profile.logoutConfirmTitle')}
        message={t('profile.logoutConfirmMessage')}
        confirmText={t('profile.logout')}
        cancelText={t('common.cancel')}
        onConfirm={handleConfirmLogout}
        onCancel={() => setShowLogoutDialog(false)}
        confirmLoading={logoutMutation.isPending}
      />
      <LanguageSwitch visible={isLanguageModalVisible} onClose={() => setIsLanguageModalVisible(false)} />
      <TextInputDialog
        visible={usernameDialogVisible}
        title={t('profile.editUsernameTitle')}
        placeholder={t('profile.usernameLabel')}
        value={newUsername}
        onChangeText={setNewUsername}
        confirmText={t('common.save')}
        cancelText={t('common.cancel')}
        onConfirm={handleSaveUsername}
        onCancel={() => setUsernameDialogVisible(false)}
        error={usernameError ?? undefined}
        maxLength={20}
      />
      <AuthModal visible={showAuthModal} onClose={() => setShowAuthModal(false)} onLogin={handleLogin} />
      <CustomDialog {...confirmDialogProps} />
      <TextDialog
        visible={showVerificationDialog}
        title={t('verification.pending.title')}
        message={t('verification.pending.message')}
        confirmText={t('common.ok')}
        onConfirm={() => setShowVerificationDialog(false)}
      />
      <OrganizationManagement
        visible={showOrgJoinModal}
        onClose={handleJoinOrganizations}
        organizations={(allOrganizationsData || [])
          .filter(org => org.id !== null && org.id !== ALL_ORGANIZATIONS_ID) as OrganizationListPayload[]}
        userOrganizationIds={(userOrganizationsData || []).map(org => org.id).filter((id: string | null): id is string => id !== null && id !== ALL_ORGANIZATIONS_ID)}
      />
      <CustomDialog
        visible={showHkidVerificationDialog}
        title={t('applications.prerequisites.hkidRequired.title')}
        message={t('applications.prerequisites.hkidRequired.message')}
        confirmText={t('applications.prerequisites.hkidRequired.verifyNow')}
        cancelText={t('common.cancel')}
        onConfirm={() => {
          setShowHkidVerificationDialog(false);
          router.push({ pathname: '/user-profile/identification/IdentityGuideScreen', params: { documentType: VerificationTypeEnum.HkIDCard } });
        }}
        onCancel={() => setShowHkidVerificationDialog(false)}
        type="warning"
      />
    </View>
  );
}
export default ProfileScreen;
