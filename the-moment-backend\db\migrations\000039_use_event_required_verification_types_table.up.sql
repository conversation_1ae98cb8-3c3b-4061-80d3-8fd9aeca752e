-- Migrate existing single verification keys to the event_required_verification_types table
INSERT INTO event_required_verification_types (event_id, verification_type_key)
SELECT id, event_verification_type_key
FROM events
WHERE event_verification_type_key IS NOT NULL AND event_verification_type_key <> ''
ON CONFLICT (event_id, verification_type_key) DO NOTHING;

-- Remove the old event_verification_type_key column from the events table
ALTER TABLE events
DROP COLUMN IF EXISTS event_verification_type_key; 