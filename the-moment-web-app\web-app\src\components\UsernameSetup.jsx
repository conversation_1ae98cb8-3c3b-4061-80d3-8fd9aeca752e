import React from 'react';
import { Form, Input, Button, Typography, Space } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

const UsernameSetup = ({ onSubmit, isLoading, onSkip }) => {
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const handleSubmit = async (values) => {
    await onSubmit(values);
  };

  return (
    <div className="username-setup-content">
      <div className="text-center mb-8">
        <Title level={2}>
          {t('username.title')}
        </Title>
        <Text className="username-description">
          {t('username.description')}
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="username-form"
      >
        <Form.Item
          name="username"
          rules={[
            { required: true, message: t('username.validation.required') },
            { min: 2, max: 20, message: t('username.validation.length') }
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            size="large"
            placeholder={t('username.placeholder')}
            autoFocus
          />
        </Form.Item>


        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'center', gap: '12px' }}>
            <Button
              size="large"
              onClick={onSkip}
            >
              {t('username.button.skip')}
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              loading={isLoading}
            >
              {t('username.button.continue')}
            </Button>
          </Space>
        </Form.Item>
        <Text type="secondary" className="text-center block">
          {t('username.skipHint')}
        </Text>

      </Form>
    </div>
  );
};

export default UsernameSetup;
