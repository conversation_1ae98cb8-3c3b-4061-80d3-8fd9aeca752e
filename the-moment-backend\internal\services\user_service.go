package services

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/authn"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/twilio_service"
	"Membership-SAAS-System-Backend/internal/utils"

	// For sql.NullString
	// "Membership-SAAS-System-Backend/internal/payloads" // Already imported below if needed specifically for IsActorOwnerOfOrg

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/rs/zerolog/log"
)

// UserService handles business logic related to user profiles.
type UserService struct {
	Queries                 db.Querier // Changed from *db.Queries to db.Querier interface
	DB                      db.DBTX
	TwilioVerify            *twilio_service.TwilioService
	Authn                   *authn.AuthnService
	Notify                  NotificationService
	UserVerificationService *UserVerificationService     // Added
	OrganizationService     OrganizationServiceInterface // Changed from *OrganizationService to OrganizationServiceInterface
}

// NewUserService creates a new UserService.
// func NewUserService(q *db.Queries, dbtx db.DBTX, twilioOTP *twilio_service.TwilioService, authnService *authn.AuthnService, notifyService NotificationService, userVerificationService *UserVerificationService, orgService *OrganizationService) *UserService { // Added userVerificationService and orgService
// The NewUserService signature was already updated in a previous step.
// The existing signature is:
func NewUserService(q db.Querier, dbtx db.DBTX, twilioOTP *twilio_service.TwilioService, authnService *authn.AuthnService, notifyService NotificationService, userVerificationService *UserVerificationService, orgService OrganizationServiceInterface) *UserService { // Changed orgService from *OrganizationService to OrganizationServiceInterface
	return &UserService{
		Queries:                 q,
		DB:                      dbtx,
		TwilioVerify:            twilioOTP,
		Authn:                   authnService,
		Notify:                  notifyService,
		UserVerificationService: userVerificationService,
		OrganizationService:     orgService,
	}
}

// IsActorOwnerOfAnyOrg checks if a given actorUserID is an 'owner' in any organization
// by calling the OrganizationService.
func (s *UserService) IsActorOwnerOfAnyOrg(ctx context.Context, actorUserID uuid.UUID) (bool, error) {
	if s.OrganizationService == nil {
		log.Ctx(ctx).Error().Msg("OrganizationService not available in UserService for IsActorOwnerOfAnyOrg check")
		return false, errors.New("internal server error: organization service not configured for ownership check")
	}

	ownsAny, err := s.OrganizationService.CheckIfUserOwnsAnyOrganization(ctx, actorUserID)
	if err != nil {
		// The error from CheckIfUserOwnsAnyOrganization is already logged,
		// so we can return it directly or wrap it if more context is needed here.
		// For now, returning it directly is fine as it's specific enough.
		return false, err
	}
	return ownsAny, nil
}

// IsActorOwnerOfOrg checks if the actorUserID is an owner of the organizationID.
func (s *UserService) IsActorOwnerOfOrg(ctx context.Context, actorUserID uuid.UUID, organizationID uuid.UUID) (bool, error) {
	if s.OrganizationService == nil {
		log.Ctx(ctx).Error().Msg("OrganizationService not available in UserService for ownership check")
		return false, errors.New("internal server error: organization service not configured")
	}
	// Assumes OrganizationService has CheckUserOrganizationRole method
	// and payloads.CheckUserOrganizationRoleOptions is defined.
	return s.OrganizationService.CheckUserOrganizationRole(ctx, payloads.CheckUserOrganizationRoleOptions{
		UserID:         actorUserID,
		OrganizationID: organizationID,
		RequiredRoles:  []string{"owner"},
	})
}

// GetUserProfile retrieves the full profile of a user.
func (s *UserService) GetUserProfile(ctx context.Context, userID uuid.UUID) (*payloads.UserProfileResponse, error) {
	user, err := s.Queries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, &payloads.NotFoundError{Message: "User not found."}
		}
		log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("Failed to get user by ID")
		return nil, err
	}

	profileResponse := s.mapUserToProfileResponse(&user)

	// Fetch verification statuses
	if s.UserVerificationService != nil {
		verificationStatuses, vsErr := s.UserVerificationService.GetUserVerificationStatuses(ctx, userID)
		if vsErr != nil {
			// Log the error but don't fail the entire profile retrieval
			// The profile can still be useful without verification status in case of an issue here
			log.Ctx(ctx).Error().Err(vsErr).Str("userID", userID.String()).Msg("Failed to get user verification statuses")
		} else {
			profileResponse.VerificationStatus = verificationStatuses
		}
	} else {
		log.Ctx(ctx).Warn().Str("userID", userID.String()).Msg("UserVerificationService is not initialized in UserService. Verification statuses will not be fetched.")
	}

	return profileResponse, nil
}

// UpdateUserProfile updates parts of a user's profile.
func (s *UserService) UpdateUserProfile(ctx context.Context, userID uuid.UUID, req *payloads.UpdateUserProfileRequest) (*payloads.UserProfileResponse, error) {
	currentUser, err := s.Queries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, &payloads.NotFoundError{Message: "User not found to update."}
		}
		return nil, err
	}

	updated := false

	if req.DisplayName != nil && *req.DisplayName != currentUser.DisplayName {
		_, err := s.Queries.UpdateUserDisplayName(ctx, db.UpdateUserDisplayNameParams{
			ID:          userID,
			DisplayName: *req.DisplayName,
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("Failed to update display name")
			return nil, err
		}
		updated = true
	}

	if req.NotificationSettings != nil {
		nsParams := db.UpdateUserNotificationSettingsParams{
			ID:                          userID,
			EnableAppNotifications:      currentUser.EnableAppNotifications,
			EnableWhatsappNotifications: currentUser.EnableWhatsappNotifications,
			EnableSmsNotifications:      currentUser.EnableSmsNotifications,
			EnableEmailNotifications:    currentUser.EnableEmailNotifications,
		}
		nsUpdated := false
		if req.NotificationSettings.EnableAppNotifications != nil && *req.NotificationSettings.EnableAppNotifications != currentUser.EnableAppNotifications {
			nsParams.EnableAppNotifications = *req.NotificationSettings.EnableAppNotifications
			nsUpdated = true
		}
		if req.NotificationSettings.EnableWhatsappNotifications != nil && *req.NotificationSettings.EnableWhatsappNotifications != currentUser.EnableWhatsappNotifications {
			nsParams.EnableWhatsappNotifications = *req.NotificationSettings.EnableWhatsappNotifications
			nsUpdated = true
		}
		if req.NotificationSettings.EnableSmsNotifications != nil && *req.NotificationSettings.EnableSmsNotifications != currentUser.EnableSmsNotifications {
			nsParams.EnableSmsNotifications = *req.NotificationSettings.EnableSmsNotifications
			nsUpdated = true
		}
		if req.NotificationSettings.EnableEmailNotifications != nil && *req.NotificationSettings.EnableEmailNotifications != currentUser.EnableEmailNotifications {
			nsParams.EnableEmailNotifications = *req.NotificationSettings.EnableEmailNotifications
			nsUpdated = true
		}

		if req.NotificationSettings.PhoneOtpChannel != nil && *req.NotificationSettings.PhoneOtpChannel != currentUser.PhoneOtpChannel {
			log.Ctx(ctx).Warn().Str("userID", userID.String()).Msg("PhoneOtpChannel update requested but not handled by current UpdateUserNotificationSettings query. Needs separate SQLC.")
		}

		if nsUpdated {
			_, err := s.Queries.UpdateUserNotificationSettings(ctx, nsParams)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("Failed to update notification settings")
				return nil, err
			}
			updated = true
		}
	}

	if req.LanguagePreferences != nil {
		lpParams := db.UpdateUserLanguagePreferencesParams{
			ID:                    userID,
			InterfaceLanguage:     currentUser.InterfaceLanguage,
			CommunicationLanguage: currentUser.CommunicationLanguage,
		}
		lpUpdated := false
		if req.LanguagePreferences.InterfaceLanguage != nil && *req.LanguagePreferences.InterfaceLanguage != currentUser.InterfaceLanguage {
			lpParams.InterfaceLanguage = *req.LanguagePreferences.InterfaceLanguage
			lpUpdated = true
		}
		if req.LanguagePreferences.CommunicationLanguage != nil && *req.LanguagePreferences.CommunicationLanguage != currentUser.CommunicationLanguage {
			lpParams.CommunicationLanguage = *req.LanguagePreferences.CommunicationLanguage
			lpUpdated = true
		}
		if lpUpdated {
			_, err := s.Queries.UpdateUserLanguagePreferences(ctx, lpParams)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("Failed to update language preferences")
				return nil, err
			}
			updated = true
		}
	}

	if req.ProfilePictureURL != nil {
		if currentUser.ProfilePictureUrl == nil || (req.ProfilePictureURL != nil && *req.ProfilePictureURL != *currentUser.ProfilePictureUrl) {
			_, err := s.Queries.UpdateUserProfilePictureURL(ctx, db.UpdateUserProfilePictureURLParams{
				ID:                userID,
				ProfilePictureUrl: req.ProfilePictureURL,
			})
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("Failed to update profile picture URL")
				return nil, err
			}
			updated = true
			log.Ctx(ctx).Info().Str("userID", userID.String()).Msg("ProfilePictureURL updated successfully.")
		} else if req.ProfilePictureURL != nil && currentUser.ProfilePictureUrl != nil && *req.ProfilePictureURL == *currentUser.ProfilePictureUrl {
			log.Ctx(ctx).Debug().Str("userID", userID.String()).Msg("ProfilePictureURL provided is the same as current; no update performed.")
		}
	} else if req.ProfilePictureURL == nil && currentUser.ProfilePictureUrl != nil {
		_, err := s.Queries.UpdateUserProfilePictureURL(ctx, db.UpdateUserProfilePictureURLParams{
			ID:                userID,
			ProfilePictureUrl: nil,
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("Failed to clear profile picture URL")
			return nil, err
		}
		updated = true
		log.Ctx(ctx).Info().Str("userID", userID.String()).Msg("ProfilePictureURL cleared successfully.")
	}

	if updated {
		return s.GetUserProfile(ctx, userID)
	}

	return s.mapUserToProfileResponse(&currentUser), nil
}

// InitiatePhoneNumberChange starts the process for a user to change their phone number.
func (s *UserService) InitiatePhoneNumberChange(ctx context.Context, userID uuid.UUID, req *payloads.InitiatePhoneChangeRequest) (*payloads.InitiatePhoneChangeResponse, error) {
	logger := log.Ctx(ctx)

	existingUser, err := s.Queries.GetUserByPhone(ctx, &req.NewPhoneNumber)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		logger.Error().Err(err).Str("new_phone", req.NewPhoneNumber).Msg("Error checking if new phone number exists")
		return nil, errors.New("failed to verify new phone number availability")
	}
	if existingUser.ID != uuid.Nil && existingUser.ID != userID {
		logger.Warn().Str("new_phone", req.NewPhoneNumber).Str("existing_user_id", existingUser.ID.String()).Msg("New phone number is already in use by another user")
		return nil, &payloads.ConflictError{Message: "This phone number is already registered with another account."}
	}

	otpChannel := "sms" // Default OTP channel
	if req.PhoneOTPChannel != nil && *req.PhoneOTPChannel != "" {
		otpChannel = *req.PhoneOTPChannel
	} else {
		currentUser, userErr := s.Queries.GetUserByID(ctx, userID)
		if userErr == nil && currentUser.PhoneOtpChannel != "" {
			otpChannel = currentUser.PhoneOtpChannel
		} else if userErr != nil {
			logger.Error().Err(userErr).Str("userID", userID.String()).Msg("Failed to get current user to determine OTP channel preference, using default.")
		}
	}

	state, flowID, err := s.Authn.InitiatePhoneChangeOTP(ctx, userID, req.NewPhoneNumber, otpChannel, req.ClientID, req.State)
	if err != nil {
		return nil, fmt.Errorf("failed to initiate phone change OTP: %w", err)
	}

	logger.Info().Str("userID", userID.String()).Str("new_phone", req.NewPhoneNumber).Str("flowID", flowID.String()).Msg("Phone number change OTP initiated successfully")

	return &payloads.InitiatePhoneChangeResponse{
		State:        state,
		FlowID:       flowID.String(),
		Message:      fmt.Sprintf("OTP sent to %s via %s to verify phone number change.", req.NewPhoneNumber, otpChannel),
		OtpChannel:   otpChannel,
		ExpiresInSec: int((10 * time.Minute).Seconds()), // This should ideally come from authn service config
	}, nil
}

// VerifyPhoneNumberChange completes the phone number change process after OTP verification.
func (s *UserService) VerifyPhoneNumberChange(ctx context.Context, userID uuid.UUID, req *payloads.VerifyPhoneChangeRequest) (*payloads.VerifyPhoneChangeResponse, error) {
	logger := log.Ctx(ctx)

	verified, flowID, err := s.Authn.VerifyPhoneChangeOTP(ctx, req.State, req.Otp, req.NewPhoneNumber)
	if err != nil {
		return nil, fmt.Errorf("OTP verification for phone change failed: %w", err)
	}

	if !verified {
		logger.Warn().Str("userID", userID.String()).Str("state", req.State).Str("flowID", flowID.String()).Msg("OTP verification returned false for phone change")
		return nil, errors.New("OTP verification failed. Please try again")
	}

	logger.Info().Str("userID", userID.String()).Str("state", req.State).Str("flowID", flowID.String()).Msg("OTP verification successful for phone change")

	updateParams := db.UpdateUserPhoneAndMarkVerifiedParams{
		ID:    userID,
		Phone: &req.NewPhoneNumber,
	}

	updatedUser, err := s.Queries.UpdateUserPhoneAndMarkVerified(ctx, updateParams)
	if err != nil {
		logger.Error().Err(err).Str("userID", userID.String()).Str("new_phone", req.NewPhoneNumber).Msg("Failed to update user phone number after verification")
		return nil, errors.New("failed to finalize phone number change after verification")
	}

	logger.Info().Str("userID", userID.String()).Str("new_phone", *updatedUser.Phone).Msg("User phone number successfully changed and verified")

	// Send notification
	if s.Notify != nil {
		messageType := "phone_change_success"
		actualPayload := map[string]interface{}{
			"message":   "Your phone number was successfully updated.",
			"new_phone": *updatedUser.Phone,
		}
		errNotify := s.Notify.SendToUser(ctx, updatedUser.ID, messageType, actualPayload)
		if errNotify != nil {
			logger.Error().Err(errNotify).Str("userID", updatedUser.ID.String()).Msg("Failed to send phone change success notification")
			// Log and continue, as the core operation was successful.
		}
	}

	return &payloads.VerifyPhoneChangeResponse{
		Message: "Phone number successfully changed.",
	}, nil
}

// InitiateReAuthOTP handles the business logic for starting a re-authentication flow.
func (s *UserService) InitiateReAuthOTP(ctx context.Context, userID uuid.UUID, req *payloads.InitiateReAuthOTPRequest) (*payloads.InitiateReAuthOTPResponse, error) {
	logger := log.Ctx(ctx)

	flowID, channel, err := s.Authn.InitiateReAuthOTP(ctx, userID, req.Purpose, req.Channel)
	if err != nil {
		// The error from AuthnService is already logged and specific.
		return nil, err
	}

	logger.Info().Str("userID", userID.String()).Str("flowID", flowID.String()).Msg("Re-authentication OTP initiated successfully via service.")

	return &payloads.InitiateReAuthOTPResponse{
		FlowID:  flowID,
		Channel: channel,
		Message: fmt.Sprintf("A one-time password has been sent to your registered number via %s.", channel),
	}, nil
}

// VerifyReAuthOTP handles the business logic for verifying a re-authentication OTP.
func (s *UserService) VerifyReAuthOTP(ctx context.Context, userID uuid.UUID, req *payloads.VerifyReAuthOTPRequest) (*payloads.VerifyReAuthOTPResponse, error) {
	logger := log.Ctx(ctx)

	flowID, err := uuid.Parse(req.FlowID)
	if err != nil {
		logger.Error().Err(err).Str("flow_id_str", req.FlowID).Msg("Failed to parse FlowID string into UUID.")
		return nil, fmt.Errorf("invalid flow_id format: %w", err)
	}

	reauthToken, err := s.Authn.VerifyReAuthOTP(ctx, userID, flowID, req.OTP)
	if err != nil {
		// Error is already specific, e.g., "invalid OTP"
		return nil, err
	}

	logger.Info().Str("userID", userID.String()).Str("flowID", req.FlowID).Msg("Re-authentication OTP verified successfully via service.")

	return &payloads.VerifyReAuthOTPResponse{
		Message:     "Verification successful. You can now proceed with the action.",
		ReAuthToken: reauthToken,
	}, nil
}

// UploadProfilePicture handles the logic for uploading a user profile picture.
// It saves the file and updates the user's profile_picture_url.
func (s *UserService) UploadProfilePicture(ctx context.Context, userID uuid.UUID, fileHeader *multipart.FileHeader) (*payloads.UserProfileResponse, error) {
	logger := log.Ctx(ctx).With().Str("userID", userID.String()).Logger()

	// 1. Validate File (Basic validation for now, enhance as needed)
	if fileHeader == nil {
		return nil, &payloads.IllegalArgumentError{Message: "File header is missing."}
	}

	// Max file size (e.g., 5MB) - consider making this configurable
	const maxFileSize = 5 * 1024 * 1024
	if fileHeader.Size > maxFileSize {
		return nil, &payloads.FileProcessingError{Message: fmt.Sprintf("File size exceeds maximum allowed limit of %dMB", maxFileSize/1024/1024)}
	}

	// Allowed MIME types - consider making this configurable
	allowedMimeTypes := map[string]bool{
		"image/jpeg": true,
		"image/png":  true,
		"image/gif":  true,
		"image/webp": true,
	}
	mimeType := fileHeader.Header.Get("Content-Type")
	if !allowedMimeTypes[mimeType] {
		return nil, &payloads.FileProcessingError{Message: fmt.Sprintf("File type '%s' not allowed. Supported types: JPEG, PNG, GIF, WebP.", mimeType)}
	}

	// 2. Prepare file path and name
	// No need to manually check for APP_BASE_URL here anymore, the util function will handle it.
	newFileName := uuid.New().String() + filepath.Ext(fileHeader.Filename)
	// Relative path for storage and for constructing the URL part
	// Example: uploads/profile-pictures/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx/yyyyyyyy-yyyy-yyyy-yyyy-yyyyyyyyyyyy.png
	relativePathDir := filepath.Join("profile-pictures", userID.String())
	fullRelativePath := filepath.Join(relativePathDir, newFileName) // Path relative to the 'uploads' base

	// Physical storage path
	// Assuming 'uploads' directory is at the root of the application or a configured static assets path
	storageDir := filepath.Join(".", "uploads", relativePathDir) // Prepend "./uploads/" to relativePathDir
	storagePath := filepath.Join(storageDir, newFileName)        // Full physical path to save the file

	// Ensure directory exists
	if err := os.MkdirAll(storageDir, os.ModePerm); err != nil {
		logger.Error().Err(err).Str("path", storageDir).Msg("Failed to create directory for profile picture")
		return nil, fmt.Errorf("failed to create storage directory: %w", err)
	}

	// 3. Save the file
	src, err := fileHeader.Open()
	if err != nil {
		logger.Error().Err(err).Msg("Failed to open uploaded file")
		return nil, &payloads.FileProcessingError{Message: "Failed to open uploaded file."}
	}
	defer src.Close()

	dst, err := os.Create(storagePath)
	if err != nil {
		logger.Error().Err(err).Str("path", storagePath).Msg("Failed to create destination file for profile picture")
		return nil, &payloads.FileProcessingError{Message: "Failed to create destination file on server."}
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		logger.Error().Err(err).Str("path", storagePath).Msg("Failed to copy uploaded file to destination")
		// Attempt to clean up partially written file
		_ = os.Remove(storagePath)
		return nil, &payloads.FileProcessingError{Message: "Failed to save uploaded file to server."}
	}

	logger.Info().Str("path", storagePath).Msg("Profile picture saved successfully to disk.")

	// 4. Update database with the new URL
	// Construct the web-accessible URL using the new utility function.
	webPath := utils.ConstructURL("uploads", fullRelativePath)

	updatedUser, err := s.Queries.UpdateUserProfilePictureURL(ctx, db.UpdateUserProfilePictureURLParams{
		ID:                userID,
		ProfilePictureUrl: &webPath,
	})
	if err != nil {
		logger.Error().Err(err).Str("webPath", webPath).Msg("Failed to update profile picture URL in database")
		// Attempt to clean up the saved file if DB update fails to prevent orphaned files
		_ = os.Remove(storagePath)
		return nil, err // Return the DB error
	}

	logger.Info().Str("new_url", webPath).Msg("User profile picture URL updated in database.")

	// 5. Return updated user profile
	return s.mapUserToProfileResponse(&updatedUser), nil
}

// mapUserToProfileResponse is a helper to convert db.User to payloads.UserProfileResponse
func (s *UserService) mapUserToProfileResponse(user *db.User) *payloads.UserProfileResponse {
	var phoneOtpChannelPtr *string
	if user.PhoneOtpChannel != "" {
		phoneOtpChannelPtr = &user.PhoneOtpChannel
	}

	return &payloads.UserProfileResponse{
		ID:                          user.ID,
		DisplayName:                 user.DisplayName,
		Phone:                       user.Phone,
		PhoneVerifiedAt:             user.PhoneVerifiedAt,
		Email:                       user.Email,
		EmailVerifiedAt:             user.EmailVerifiedAt,
		ProfilePictureURL:           user.ProfilePictureUrl,
		PhoneOtpChannel:             phoneOtpChannelPtr,
		InterfaceLanguage:           user.InterfaceLanguage,
		CommunicationLanguage:       user.CommunicationLanguage,
		EnableAppNotifications:      user.EnableAppNotifications,
		EnableWhatsappNotifications: user.EnableWhatsappNotifications,
		EnableSmsNotifications:      user.EnableSmsNotifications,
		EnableEmailNotifications:    user.EnableEmailNotifications,

		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}
}

// GetUserStats retrieves and computes statistics for a given user.
func (s *UserService) GetUserStats(ctx context.Context, userID uuid.UUID) (*payloads.APIUserStats, error) {
	logger := log.Ctx(ctx)
	stats := &payloads.APIUserStats{
		MonthlyAttendedEvents: []payloads.MonthlyAttendedEvent{},
		TopAttendedEventTags:  []payloads.TopAttendedEventTag{},
	}

	// Get user registration date for UserJoinedAt
	regDate, err := s.Queries.GetUserRegistrationDate(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, &payloads.NotFoundError{Message: "User not found."}
		}
		logger.Error().Err(err).Str("userID", userID.String()).Msg("Error getting user registration date")
		return nil, err
	}
	stats.UserJoinedAt = regDate // Assign directly

	// Get total attended events
	attendedEventsCount, err := s.Queries.GetUserTotalAttendedEvents(ctx, userID) // Use new query name
	if err != nil {
		if !errors.Is(err, pgx.ErrNoRows) { // ErrNoRows means 0 events, which is fine for a COUNT query
			logger.Error().Err(err).Str("userID", userID.String()).Msg("Error getting user total attended events")
			return nil, err
		}
		stats.TotalEvents = 0
	} else {
		stats.TotalEvents = int(attendedEventsCount)
	}

	// Get total volunteer events
	volunteerEventCount, err := s.Queries.GetUserTotalVolunteerEvents(ctx, userID)
	if err != nil {
		if !errors.Is(err, pgx.ErrNoRows) { // ErrNoRows means 0 events
			logger.Error().Err(err).Str("userID", userID.String()).Msg("Error getting user total volunteer events")
			return nil, err
		}
		stats.VolunteerEvents = 0
	} else {
		stats.VolunteerEvents = int(volunteerEventCount)
	}

	// Get monthly attended events (last 6 months)
	monthlyEvents, err := s.Queries.GetMonthlyAttendedEvents(ctx, userID)
	if err != nil {
		logger.Error().Err(err).Str("userID", userID.String()).Msg("Error getting monthly attended events")
	}
	for _, me := range monthlyEvents {
		stats.MonthlyAttendedEvents = append(stats.MonthlyAttendedEvents, payloads.MonthlyAttendedEvent{
			Month: me.Month,
			Count: int(me.Count),
		})
	}

	// Get top attended event tags (last 6 months)
	topTags, err := s.Queries.GetTopAttendedEventTags(ctx, userID)
	if err != nil {
		logger.Error().Err(err).Str("userID", userID.String()).Msg("Error getting top attended event tags")
	}
	for _, tt := range topTags {
		stats.TopAttendedEventTags = append(stats.TopAttendedEventTags, payloads.TopAttendedEventTag{
			NameEn:   tt.NameEn,
			NameZhHk: tt.NameZhHk,
			NameZhCn: tt.NameZhCn,
			Count:    int(tt.Count),
		})
	}

	return stats, nil
}

// ListUserOrganizations retrieves all organizations a user is an active member of.
func (s *UserService) ListUserOrganizations(ctx context.Context, userID uuid.UUID) ([]payloads.OrganizationResponse, error) {
	logger := log.Ctx(ctx)
	dbOrgs, err := s.Queries.ListUserOrganizations(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// Not an error, just means the user isn't a member of any active organizations. Return empty list.
			return []payloads.OrganizationResponse{}, nil
		}
		logger.Error().Err(err).Str("userID", userID.String()).Msg("Failed to list active organizations for user")
		return nil, err
	}

	return toUserOrganizationListResponses(dbOrgs), nil
}

// toUserOrganizationListResponses converts a slice of db.Organization to a slice of payloads.OrganizationResponse.
func toUserOrganizationListResponses(dbOrgs []db.Organization) []payloads.OrganizationResponse {
	responses := make([]payloads.OrganizationResponse, len(dbOrgs))
	for i, org := range dbOrgs {
		responses[i] = payloads.OrganizationResponse{
			ID:           org.ID,
			Name:         org.Name,
			Description:  org.Description,
			IsDefaultOrg: org.IsDefaultOrg,
			ImageURL:     org.ImageUrl,
			ThemeColor:   org.ThemeColor,
			Status:       org.Status,
			CreatedAt:    org.CreatedAt,
			UpdatedAt:    org.UpdatedAt,
		}
	}
	return responses
}

// mapUserToAdminUserResponse maps a db.User to a payloads.AdminUserResponse
func (s *UserService) mapUserToAdminUserResponse(user *db.User) *payloads.AdminUserResponse {
	if user == nil {
		return nil
	}
	return &payloads.AdminUserResponse{
		ID:                user.ID,
		DisplayName:       user.DisplayName,
		Email:             user.Email,
		EmailVerifiedAt:   user.EmailVerifiedAt,
		Phone:             user.Phone,
		PhoneVerifiedAt:   user.PhoneVerifiedAt,
		ProfilePictureURL: user.ProfilePictureUrl,

		Role:                        string(user.Role),
		CreatedAt:                   user.CreatedAt,
		UpdatedAt:                   user.UpdatedAt,
		InterfaceLanguage:           user.InterfaceLanguage,
		CommunicationLanguage:       user.CommunicationLanguage,
		EnableAppNotifications:      user.EnableAppNotifications,
		EnableWhatsappNotifications: user.EnableWhatsappNotifications,
		EnableSmsNotifications:      user.EnableSmsNotifications,
		EnableEmailNotifications:    user.EnableEmailNotifications,
		PhoneOtpChannel:             user.PhoneOtpChannel,
	}
}

// ListUsersParams holds parameters for ListUsers service method.
type ListUsersParams struct {
	Page            int
	PageSize        int            // Renamed from Limit
	Search          string         // Added
	Email           string         // Added
	RoleFilter      sql.NullString // Type changed, ensure this is present for role filtering
	ActorOrgOwnerID uuid.NullUUID  // Add this field
	SortBy          string         // Added
	SortOrder       string         // Added
}

// ListUsers retrieves a paginated list of users with optional filters.
func (s *UserService) ListUsers(ctx context.Context, params ListUsersParams) (*payloads.PaginatedUsersResponse, error) {
	if params.PageSize <= 0 {
		params.PageSize = 10 // Default limit
	}
	if params.Page < 1 {
		params.Page = 1
	}
	offset := (params.Page - 1) * params.PageSize

	// Validate RoleFilter if it's provided
	if params.RoleFilter.Valid {
		validRole := false
		for _, r := range db.AllUserRoleValues() {
			if string(r) == params.RoleFilter.String {
				validRole = true
				break
			}
		}
		if !validRole {
			return nil, &payloads.IllegalArgumentError{Message: fmt.Sprintf("Invalid role filter value: %s. Allowed values are: %v", params.RoleFilter.String, db.AllUserRoleValues())}
		}
	}

	// Default sort order if not specified
	sortBy := params.SortBy
	sortOrder := params.SortOrder
	if sortBy == "" {
		sortBy = "created_at" // Default sort by
	}
	if sortOrder == "" {
		sortOrder = "desc" // Default sort order
	}

	dbParams := db.ListUsersFilteredParams{
		PageSize:   int32(params.PageSize),
		PageOffset: int32(offset),
	}

	if params.Search != "" {
		dbParams.SearchQuery = &params.Search
	}
	if params.Email != "" {
		dbParams.Email = &params.Email
	}
	if params.RoleFilter.Valid {
		dbParams.RoleFilter = &params.RoleFilter.String
	}
	if params.ActorOrgOwnerID.Valid {
		dbParams.ActorOrgOwnerID = &params.ActorOrgOwnerID.UUID
	}
	if sortBy != "" { // sortBy is already defaulted if params.SortBy was empty
		dbParams.SortBy = &sortBy
	}
	if sortOrder != "" { // sortOrder is already defaulted if params.SortOrder was empty
		dbParams.SortOrder = &sortOrder
	}

	usersData, err := s.Queries.ListUsersFiltered(ctx, dbParams)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// If no rows are found, it's not an error in the sense of a failed query,
			// but rather an empty result set. Return an empty list and total count of 0.
			return &payloads.PaginatedUsersResponse{
				Users:      []payloads.AdminUserResponse{},
				TotalCount: 0,
				Page:       params.Page,
				Limit:      params.PageSize, // Use PageSize here
			}, nil
		}
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list users filtered")
		return nil, errors.New("failed to retrieve users")
	}

	userResponses := make([]payloads.AdminUserResponse, 0, len(usersData))
	var totalUsers int64 = 0

	if len(usersData) > 0 {
		// Assuming TotalCount is a field in each row of usersData, as per `COUNT(*) OVER()`.
		// We take it from the first row.
		totalUsers = usersData[0].TotalCount
	}

	for _, userData := range usersData {
		// Need to map db.ListUsersFilteredRow to db.User for mapUserToAdminUserResponse
		// This requires ListUsersFilteredRow to have all fields of User.
		// Or, mapUserToAdminUserResponse needs to be adapted for ListUsersFilteredRow.
		// For now, let's assume ListUsersFilteredRow has the necessary fields.
		// If sqlc generates User and TotalCount separately, this will need adjustment.
		// The provided SQL selects u.id, u.display_name, etc. which are fields of the users table.
		// So, each row in usersData should effectively be a "user" plus TotalCount.

		user := db.User{
			ID:                userData.ID,
			DisplayName:       userData.DisplayName,
			Email:             userData.Email,
			EmailVerifiedAt:   userData.EmailVerifiedAt,
			Phone:             userData.Phone,           // Corrected: use userData.Phone
			PhoneVerifiedAt:   userData.PhoneVerifiedAt, // Corrected: use userData.PhoneVerifiedAt
			ProfilePictureUrl: userData.AvatarUrl,       // Corrected: use ProfilePictureUrl (camelCase 'Url')
			Role:              userData.Role,
			CreatedAt:         userData.CreatedAt,
			UpdatedAt:         userData.UpdatedAt,
			// Other fields of db.User (like HashedPassword, InterfaceLanguage, etc.)
			// are not selected by ListUsersFiltered and will remain as their zero values.
			// This is acceptable if mapUserToAdminUserResponse handles potentially missing fields
			// or if those fields are not essential for the AdminUserResponse.
		}
		userResponses = append(userResponses, *s.mapUserToAdminUserResponse(&user))
	}

	return &payloads.PaginatedUsersResponse{
		Users:      userResponses,
		TotalCount: totalUsers,
		Page:       params.Page,
		Limit:      params.PageSize, // Use PageSize here
	}, nil
}

// FindUserByEmail retrieves a user by their email address.
func (s *UserService) FindUserByEmail(ctx context.Context, email string) (*payloads.AdminUserResponse, error) {
	user, err := s.Queries.GetUserByEmail(ctx, &email) // GetUserByEmail expects *string
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, &payloads.NotFoundError{Message: fmt.Sprintf("User with email %s not found", email)}
		}
		log.Ctx(ctx).Error().Err(err).Str("email", email).Msg("Failed to get user by email")
		return nil, fmt.Errorf("failed to find user by email: %w", err)
	}
	return s.mapUserToAdminUserResponse(&user), nil
}

// FindUserByPhone retrieves a user by their phone number.
func (s *UserService) FindUserByPhone(ctx context.Context, phone string) (*payloads.AdminUserResponse, error) {
	user, err := s.Queries.GetUserByPhone(ctx, &phone)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, &payloads.NotFoundError{Message: fmt.Sprintf("User with phone %s not found.", phone)}
		}
		log.Ctx(ctx).Error().Err(err).Str("phone", phone).Msg("FindUserByPhone: Error getting user by phone")
		return nil, fmt.Errorf("error getting user by phone %s: %w", phone, err)
	}
	return s.mapUserToAdminUserResponse(&user), nil
}

// FindUserByID retrieves a user by their ID and returns it in AdminUserResponse format.
func (s *UserService) FindUserByID(ctx context.Context, userID uuid.UUID) (*payloads.AdminUserResponse, error) {
	user, err := s.Queries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, &payloads.NotFoundError{Message: fmt.Sprintf("User with ID %s not found.", userID.String())}
		}
		log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("FindUserByID: Error getting user by ID")
		return nil, fmt.Errorf("error getting user by ID %s: %w", userID.String(), err)
	}
	return s.mapUserToAdminUserResponse(&user), nil
}

// CreateStaffUser creates a new user with is_staff set to true.
func (s *UserService) CreateStaffUser(ctx context.Context, req *payloads.CreateStaffUserRequest) (*payloads.AdminUserResponse, error) {
	// 1. Check if email already exists
	existingUser, err := s.Queries.GetUserByEmail(ctx, &req.Email)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		log.Ctx(ctx).Error().Err(err).Str("email", req.Email).Msg("Failed to check for existing user by email")
		return nil, fmt.Errorf("failed to check for existing email: %w", err)
	}
	if existingUser.ID != uuid.Nil { // Check if a user was actually found (ID is not zero UUID)
		return nil, &payloads.ConflictError{Message: fmt.Sprintf("User with email %s already exists", req.Email)}
	}

	// 2. Hash the password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to hash password for staff user")
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 3. Prepare parameters for SQLC query
	now := time.Now()
	staffUserParams := db.CreateStaffUserWithEmailPasswordParams{
		DisplayName:     req.DisplayName,
		Email:           &req.Email,
		EmailVerifiedAt: &now, // Set email as verified for admin-created staff
		HashedPassword:  &hashedPassword,
		// Default values for optional fields if not provided in request
		InterfaceLanguage:           "en",  // Default
		CommunicationLanguage:       "en",  // Default
		EnableAppNotifications:      true,  // Default
		EnableWhatsappNotifications: true,  // Default
		EnableSmsNotifications:      true,  // Default
		EnableEmailNotifications:    true,  // Default
		PhoneOtpChannel:             "sms", // Default, can be updated later by user
	}

	if req.InterfaceLanguage != nil {
		staffUserParams.InterfaceLanguage = *req.InterfaceLanguage
	}
	if req.CommunicationLanguage != nil {
		staffUserParams.CommunicationLanguage = *req.CommunicationLanguage
	}

	// 4. Call CreateStaffUserWithEmailPassword SQLC query
	createdUser, err := s.Queries.CreateStaffUserWithEmailPassword(ctx, staffUserParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create staff user in DB")
		return nil, fmt.Errorf("failed to create staff user: %w", err)
	}

	// 5. Map the created db.User to payloads.AdminUserResponse
	return s.mapUserToAdminUserResponse(&createdUser), nil
}

// ListAdminOwnedOrganizations retrieves all organizations an admin user owns.
func (s *UserService) ListAdminOwnedOrganizations(ctx context.Context, adminUserID uuid.UUID) ([]*payloads.OrganizationResponse, error) {
	// TODO: Ensure 'sqlc generate' has been run in the 'db/' directory for ListOrganizationsWhereUserIsOwner to be available.
	dbOrgs, err := s.Queries.ListOrganizationsWhereUserIsOwner(ctx, adminUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return []*payloads.OrganizationResponse{}, nil // No orgs owned, return empty list
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to list organizations where user %s is owner", adminUserID)
		return nil, errors.New("failed to retrieve owned organizations")
	}

	orgResponses := make([]*payloads.OrganizationResponse, len(dbOrgs))
	for i, org := range dbOrgs {
		orgResponses[i] = &payloads.OrganizationResponse{
			ID:           org.ID,
			Name:         org.Name,
			Description:  org.Description,
			IsDefaultOrg: org.IsDefaultOrg,
			ImageURL:     org.ImageUrl,   // Corrected field name from db.Organization
			ThemeColor:   org.ThemeColor, // Corrected field name from db.Organization
			Status:       org.Status,
			CreatedAt:    org.CreatedAt,
			UpdatedAt:    org.UpdatedAt,
		}
	}
	return orgResponses, nil
}

func (s *UserService) AssignOrganizationManager(ctx context.Context, organizationID uuid.UUID, targetUserID uuid.UUID, actorUserID uuid.UUID) error {
	log.Ctx(ctx).Info().Msgf("UserService.AssignOrganizationManager called for org %s, user %s, by actor %s", organizationID, targetUserID, actorUserID)

	actor, err := s.Queries.GetUserByID(ctx, actorUserID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Error fetching actor user %s for manager assignment", actorUserID)
		return errors.New("error verifying actor user")
	}

	// 0. Verify actor has permission (is owner of the organization or a superadmin)
	// TODO: Confirm the exact name for the superadmin role. Using "superadmin".
	if string(actor.Role) != "superadmin" {
		isOwner, err := s.OrganizationService.CheckUserOrganizationRole(ctx, payloads.CheckUserOrganizationRoleOptions{
			UserID:         actorUserID,
			OrganizationID: organizationID,
			RequiredRoles:  []string{"owner"},
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Error checking if actor %s is owner of org %s", actorUserID, organizationID)
			return errors.New("org service error") // Match test expectation
		}
		if !isOwner {
			log.Ctx(ctx).Warn().Msgf("Actor %s is not an owner of org %s, cannot assign manager", actorUserID, organizationID)
			return &payloads.ForbiddenError{Message: "actor is not authorized to assign managers for this organization"}
		}
	}

	// 1. Verify targetUserID exists
	_, err = s.Queries.GetUserByID(ctx, targetUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Msgf("Target user %s not found", targetUserID)
			return &payloads.NotFoundError{Message: "target user not found"} // Match test expectation
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Error fetching target user %s", targetUserID)
		return errors.New("error verifying target user") // Keep generic for other DB errors
	}

	// 2. Verify organizationID exists
	_, err = s.Queries.GetOrganizationByID(ctx, organizationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Msgf("Organization %s not found", organizationID)
			return &payloads.NotFoundError{Message: "organization not found"} // Match test expectation
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Error fetching organization %s", organizationID)
		return errors.New("error verifying organization") // Keep generic for other DB errors
	}

	// 3. Check if targetUserID is already a member of organizationID
	membership, err := s.Queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
		UserID:         targetUserID,
		OrganizationID: organizationID,
	})

	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		log.Ctx(ctx).Error().Err(err).Msgf("Error checking user %s membership in org %s", targetUserID, organizationID)
		return errors.New("db error get membership") // Match test expectation
	}

	if errors.Is(err, pgx.ErrNoRows) { // Not a member
		// 5. If not a member: Add them with role "manager"
		log.Ctx(ctx).Info().Msgf("User %s is not a member of org %s. Adding as manager.", targetUserID, organizationID)
		_, err = s.Queries.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
			UserID:               targetUserID,
			OrganizationID:       organizationID,
			Role:                 "manager",
			IsActive:             true, // Default to active
			NotificationsEnabled: true, // Default to enabled
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Error adding user %s to org %s as manager", targetUserID, organizationID)
			return errors.New("db error add user") // Match test expectation
		}
		log.Ctx(ctx).Info().Msgf("Successfully added user %s to org %s as manager", targetUserID, organizationID)
	} else { // Already a member
		if membership.Role == "manager" {
			log.Ctx(ctx).Info().Msgf("User %s is already a manager in org %s. No action needed.", targetUserID, organizationID)
			return nil // Idempotent: already a manager
		}
		// 4. If member (but not manager): Update their role to "manager"
		log.Ctx(ctx).Info().Msgf("User %s is already a member of org %s (role: %s). Updating role to manager.", targetUserID, organizationID, membership.Role)
		// Ensure all fields for UpdateUserOrganizationMembershipDetailsParams are provided.
		// The existing membership object can provide current values for IsActive and NotificationsEnabled.
		_, err = s.Queries.UpdateUserOrganizationMembershipDetails(ctx, db.UpdateUserOrganizationMembershipDetailsParams{
			UserID:               targetUserID,
			OrganizationID:       organizationID,
			Role:                 "manager",
			IsActive:             membership.IsActive,             // Preserve current active status
			NotificationsEnabled: membership.NotificationsEnabled, // Preserve current notification status
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Error updating user %s role to manager in org %s", targetUserID, organizationID)
			return errors.New("db error update membership") // Match test expectation
		}
		log.Ctx(ctx).Info().Msgf("Successfully updated user %s role to manager in org %s", targetUserID, organizationID)
	}
	return nil
}

func (s *UserService) RemoveOrganizationManager(ctx context.Context, organizationID uuid.UUID, targetUserID uuid.UUID, actorUserID uuid.UUID) error {
	log.Ctx(ctx).Info().Msgf("UserService.RemoveOrganizationManager called for org %s, user %s, by actor %s", organizationID, targetUserID, actorUserID)

	actor, err := s.Queries.GetUserByID(ctx, actorUserID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Error fetching actor user %s for manager removal", actorUserID)
		return errors.New("error verifying actor user")
	}

	// 0. Verify actor has permission (is owner of the organization or a superadmin)
	// TODO: Confirm the exact name for the superadmin role. Using "superadmin".
	if string(actor.Role) != "superadmin" {
		isOwner, err := s.OrganizationService.CheckUserOrganizationRole(ctx, payloads.CheckUserOrganizationRoleOptions{
			UserID:         actorUserID,
			OrganizationID: organizationID,
			RequiredRoles:  []string{"owner"},
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Error checking if actor %s is owner of org %s for manager removal", actorUserID, organizationID)
			return errors.New("org service error") // Match test expectation (assuming same as assign)
		}
		if !isOwner {
			log.Ctx(ctx).Warn().Msgf("Actor %s is not an owner of org %s, cannot remove manager", actorUserID, organizationID)
			return &payloads.ForbiddenError{Message: "actor is not authorized to remove managers for this organization"}
		}
	}

	// 1. Verify targetUserID exists
	_, err = s.Queries.GetUserByID(ctx, targetUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Msgf("Target user %s not found for manager removal", targetUserID)
			return &payloads.NotFoundError{Message: "target user not found"} // Match test expectation
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Error fetching target user %s for manager removal", targetUserID)
		return errors.New("error verifying target user") // Keep generic for other DB errors
	}

	// 2. Verify organizationID exists
	_, err = s.Queries.GetOrganizationByID(ctx, organizationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Msgf("Organization %s not found for manager removal", organizationID)
			return &payloads.NotFoundError{Message: "organization not found"} // Match test expectation
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Error fetching organization %s for manager removal", organizationID)
		return errors.New("error verifying organization") // Keep generic for other DB errors
	}

	// 3. Check if targetUserID is currently a "manager" for organizationID
	membership, err := s.Queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
		UserID:         targetUserID,
		OrganizationID: organizationID,
	})

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Msgf("User %s is not a member of org %s. Cannot remove as manager.", targetUserID, organizationID)
			return &payloads.NotFoundError{Message: "target user is not a member of this organization"} // Match test expectation
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Error checking user %s membership in org %s for manager removal", targetUserID, organizationID)
		return errors.New("db error get membership") // Match test expectation
	}

	if membership.Role != "manager" {
		log.Ctx(ctx).Warn().Msgf("User %s is a member of org %s but not a manager (role: %s). Cannot remove as manager.", targetUserID, organizationID, membership.Role)
		return &payloads.ConflictError{Message: "target user is not a manager of this organization"} // Match test expectation
	}

	// At this point, membership.Role is confirmed to be "manager"
	// If, for some reason, the logic were to allow non-managers here and we wanted to make it idempotent for "member":
	// if membership.Role == "member" {
	//  log.Ctx(ctx).Info().Msgf("User %s is already a member in org %s (not a manager). No demotion needed.", targetUserID, organizationID)
	// 	return nil // Idempotent: already a member, nothing to demote from manager status
	// }

	// 4. If role is "manager": Update their role to "member" (demotion)
	log.Ctx(ctx).Info().Msgf("User %s is a manager of org %s. Demoting to member.", targetUserID, organizationID)
	_, err = s.Queries.UpdateUserOrganizationMembershipDetails(ctx, db.UpdateUserOrganizationMembershipDetailsParams{
		UserID:               targetUserID,
		OrganizationID:       organizationID,
		Role:                 "member",
		IsActive:             membership.IsActive,             // Preserve current active status
		NotificationsEnabled: membership.NotificationsEnabled, // Preserve current notification status
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Error demoting user %s from manager to member in org %s", targetUserID, organizationID)
		return errors.New("db error update membership") // Match test expectation
	}
	log.Ctx(ctx).Info().Msgf("Successfully demoted user %s from manager to member in org %s", targetUserID, organizationID)
	return nil
}

// DeleteUser deletes a user from the system.
func (s *UserService) DeleteUser(ctx context.Context, userID uuid.UUID) error {
	err := s.Queries.DeleteUser(ctx, userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return &payloads.NotFoundError{Message: "User not found."}
		}
		log.Ctx(ctx).Error().Err(err).Str("userID", userID.String()).Msg("Failed to delete user")
		return err
	}
	return nil
}
