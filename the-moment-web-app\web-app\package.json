{"scripts": {"start": "vite", "build": "vite build"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/plots": "^2.3.2", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@headlessui/react": "^2.2.0", "@headlessui/tailwindcss": "^0.2.2", "@iconify/react": "^5.2.0", "@remixicon/react": "^4.6.0", "@tanstack/react-query": "^5.68.0", "@tiptap/extension-list-item": "^2.9.1", "@tiptap/extension-text-align": "^2.9.1", "@tiptap/extension-text-style": "^2.9.1", "@tiptap/extension-underline": "^2.9.1", "@tiptap/html": "^2.11.5", "@tiptap/pm": "^2.9.1", "@tiptap/react": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@tremor/react": "^3.18.7", "antd": "^5.24.3", "axios": "^1.7.9", "country-list-with-dial-code-and-flag": "^5.1.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dompurify": "^3.2.4", "html2canvas": "^1.4.1", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "moment": "^2.30.1", "react": "^18.3.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-i18next": "^13.5.0", "react-responsive": "^10.0.0", "react-router-dom": "^6.26.2", "recharts": "^2.15.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-react": "^4.6.0", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "vite": "^7.0.0", "vite-plugin-eslint": "^1.8.1"}, "eslintConfig": {"extends": ["react-app"], "rules": {"no-unused-vars": "off", "exhaustive-deps": "off"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}