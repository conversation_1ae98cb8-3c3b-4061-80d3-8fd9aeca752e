-- Add the old individual location fields back
ALTER TABLE events
ADD COLUMN location_address_line1 TEXT NULL,
ADD COLUMN location_address_line2 TEXT NULL,
ADD COLUMN location_city TEXT NULL,
ADD COLUMN location_state_province_region TEXT NULL,
ADD COLUMN location_postal_code TEXT NULL,
ADD COLUMN location_country TEXT NULL;

-- Attempt to split the consolidated address back (highly unreliable, data loss likely)
-- It's generally better to restore from backup if rolling back this kind of change.
-- UPDATE events
-- SET 
--   location_city = split_part(location_full_address, ', ', -3), -- Example, depends heavily on format used
--   location_country = split_part(location_full_address, ', ', -1) -- Example
-- WHERE location_full_address IS NOT NULL;

-- Remove the consolidated field
ALTER TABLE events DROP COLUMN IF EXISTS location_full_address; 