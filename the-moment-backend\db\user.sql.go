// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: user.sql

package db

import (
	"context"

	"github.com/google/uuid"
)

const updateUserProfilePictureURL = `-- name: UpdateUserProfilePictureURL :one
UPDATE users
SET
    profile_picture_url = $2,
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, role
`

type UpdateUserProfilePictureURLParams struct {
	ID                uuid.UUID `db:"id" json:"id"`
	ProfilePictureUrl *string   `db:"profile_picture_url" json:"profile_picture_url"`
}

func (q *Queries) UpdateUserProfilePictureURL(ctx context.Context, arg UpdateUserProfilePictureURLParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserProfilePictureURL, arg.ID, arg.ProfilePictureUrl)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Role,
	)
	return i, err
}
