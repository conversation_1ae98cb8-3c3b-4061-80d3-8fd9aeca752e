import React from 'react';
import { Pagination as AntPagination } from 'antd';
import { useTranslation } from 'react-i18next';

const Pagination = ({ currentPage, totalItems, itemsPerPage, onPageChange }) => {
  const { t } = useTranslation();

  if (totalItems <= itemsPerPage) {
    return null;
  }

  return (
    <div style={{ textAlign: 'center', marginTop: '20px' }}>
      <AntPagination
        current={currentPage}
        total={totalItems}
        pageSize={itemsPerPage}
        onChange={onPageChange}
        showSizeChanger={false} // Or make configurable
        showTotal={(total, range) => 
          t('common.pagination.totalItems', '{range0}-{range1} of {total} items', { 
            range0: range[0], 
            range1: range[1], 
            total 
          })
        }
      />
    </div>
  );
};

export default Pagination; 