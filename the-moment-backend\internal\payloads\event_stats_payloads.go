package payloads

import (
	"time"

	"github.com/google/uuid"
)

// EventStatisticsRequest defines the request payload for event statistics
// Currently uses query parameters, but defined for consistency and future body params.
type EventStatisticsRequest struct {
	StartDate *time.Time `query:"startDate"`
	EndDate   *time.Time `query:"endDate"`
	Limit     *int       `query:"limit"` // For Top N events
}

// EventStatisticsResponse defines the response payload for event statistics
type EventStatisticsResponse struct {
	CategoryStats []EventCategoryStat `json:"categoryStats"`
	TopEvents     []TopEventItem      `json:"topEvents"`
}

// EventCategoryStat defines a single item for category statistics
type EventCategoryStat struct {
	CategoryNameEn     string `json:"name_en"`
	CategoryNameZhHk   string `json:"name_zh_hk"`
	CategoryNameZhCn   string `json:"name_zh_cn"`
	TotalEvents        int64  `json:"total_events"`
	TotalRegistrations int64  `json:"total_registrations"`
	TotalAttendees     int64  `json:"total_attendees"`
}

// TopEventItem defines a single item for top events
type TopEventItem struct {
	ID           uuid.UUID `json:"id"`
	Title        string    `json:"title"`
	Participants int64     `json:"participants"` // Changed from int32 to int64 to match generated SQLC type
}

type SpecificEventStatisticsResponse struct {
	ParticipantsCount    int           `json:"participants_count"`
	VolunteersCount      int           `json:"volunteers_count"`
	MaxParticipants      int           `json:"max_participants"`
	ParticipantsByAge    []AgeGroup    `json:"participants_by_age"`
	VolunteersByAge      []AgeGroup    `json:"volunteers_by_age"`
	ParticipantsByGender []GenderGroup `json:"participants_by_gender"`
	VolunteersByGender   []GenderGroup `json:"volunteers_by_gender"`
	ParticipantsByDate   []DateGroup   `json:"participants_by_date"`
	VolunteersByDate     []DateGroup   `json:"volunteers_by_date"`
}

type AgeGroup struct {
	AgeRange string `json:"age_range"`
	Count    int    `json:"count"`
}

type GenderGroup struct {
	Gender string `json:"gender"`
	Count  int    `json:"count"`
}

type DateGroup struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}
