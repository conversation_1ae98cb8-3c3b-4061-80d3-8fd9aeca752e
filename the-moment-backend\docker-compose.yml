version: '3.8'

services:
  postgres:
    image: postgres:17-alpine
    container_name: membership_postgres
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: membership_db
    # ports:
    #   - "5432:5432"
    network_mode: host
    volumes:
      - postgres_membership_db_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_membership_db_data:
    driver: local 