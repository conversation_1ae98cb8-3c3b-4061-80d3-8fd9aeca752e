package middleware

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/authn"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services"
	"Membership-SAAS-System-Backend/internal/utils"
	"net/http"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// AdminRequired is an Echo middleware that checks if the user has admin privileges (IsStaff).
// It assumes JWTAuth middleware has already run and populated claims.
func AdminRequired(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		claims, err := authn.GetValidatedClaims(c)
		if err != nil {
			// This might happen if JWTAuth middleware is optional and token is not present
			return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Admin access required, token missing or invalid.", err)
		}

		if claims.Role != string(db.UserRoleAdmin) && claims.Role != string(db.UserRoleSuperadmin) {
			return utils.HandleError(c, http.StatusForbidden, "Forbidden: Admin privileges required.", nil)
		}
		return next(c)
	}
}

// OrgAdminOrSuperAdminRequired creates a middleware that checks if a user is a superadmin
// or an admin of the specific organization being accessed.
func OrgAdminOrSuperAdminRequired(orgService *services.OrganizationService) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			claims, err := authn.GetValidatedClaims(c)
			if err != nil {
				return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
			}

			// Superadmin always has access.
			if claims.Role == string(db.UserRoleSuperadmin) {
				return next(c)
			}

			// For other roles, they must be an admin of the specific organization.
			orgIdStr := c.Param("orgId")
			if orgIdStr == "" {
				// This case should ideally not be reached if the route is /organizations/:orgId/...
				return utils.HandleError(c, http.StatusBadRequest, "Organization ID is required in the URL path.", nil)
			}
			orgId, err := uuid.Parse(orgIdStr)
			if err != nil {
				return utils.HandleError(c, http.StatusBadRequest, "Invalid Organization ID format.", err)
			}

			isOrgAdmin, err := orgService.CheckUserOrganizationRole(c.Request().Context(), payloads.CheckUserOrganizationRoleOptions{
				UserID:         claims.UserID,
				OrganizationID: orgId,
				RequiredRoles:  []string{string(db.UserRoleAdmin)},
			})

			if err != nil {
				log.Ctx(c.Request().Context()).Error().Err(err).Msg("Error checking organization admin status")
				return utils.HandleError(c, http.StatusInternalServerError, "Failed to verify user permissions.", err)
			}

			if !isOrgAdmin {
				return utils.HandleError(c, http.StatusForbidden, "Forbidden: User is not an admin of this organization.", nil)
			}

			return next(c)
		}
	}
}
