import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { TipT<PERSON><PERSON><PERSON><PERSON>, defaultHand<PERSON>, isTipTapJSON, TipTapNode } from './TipTapRenderer';

interface RichTextDescriptionProps {
  description: any;
  style?: any;
}

export const RichTextDescription: React.FC<RichTextDescriptionProps> = ({ description, style }) => {
  // Handle empty or null description
  if (!description) {
    return null;
  }
  
  // If description is a string, render it directly (only if not empty)
  if (typeof description === 'string') {
    if (description.trim() === '') {
      return null;
    }
    
    return (
      <Text style={[styles.plainText, style]}>
        {description}
      </Text>
    );
  }
  
  // If description is TipTap JSON, render it with TipTapRenderer
  if (isTipTapJSON(description)) {
    // Check if TipTap content is empty
    if (!description.content || description.content.length === 0) {
      return null;
    }
    
    return (
      <View style={[styles.container, style]}>
        <TipTapRenderer node={description as TipTapNode} handlers={defaultHandlers} />
      </View>
    );
  }
  
  // Fallback for other object types
  try {
    const stringified = JSON.stringify(description, null, 2);
    if (stringified === '{}' || stringified === '[]') {
      return null;
    }
    
    return (
      <Text style={[styles.plainText, style]}>
        {stringified}
      </Text>
    );
  } catch (error) {
    return <Text style={styles.errorText}>Unable to display content</Text>;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    paddingHorizontal: 4,
  },
  plainText: {
    fontSize: 15,
    lineHeight: 24,
    color: '#333333',
    paddingLeft: 4,
  },
  errorText: {
    fontSize: 15,
    color: '#FF0000',
    fontStyle: 'italic',
    paddingLeft: 4,
  }
}); 