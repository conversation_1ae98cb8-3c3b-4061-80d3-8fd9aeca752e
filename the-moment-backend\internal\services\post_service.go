package services

import (
	"Membership-SAAS-System-Backend/db"
	// "Membership-SAAS-System-Backend/internal/authn" // No longer needed here directly
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/utils"
	"context"
	"database/sql"
	"encoding/json" // Ensure json is imported
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gosimple/slug"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
)

const (
	DefaultPostStatus      = "draft"
	UploadDirPosts         = "uploads/post-media"
	MaxUploadSizePostMedia = 10 << 20 // 10 MB
)

type PostService struct {
	Queries        *db.Queries
	PostTagService *PostTagService
	Pool           *pgxpool.Pool
	// Config can be added here if needed for things like base URL for file paths
}

func NewPostService(queries *db.Queries, postTagService *PostTagService, pool *pgxpool.Pool) *PostService {
	return &PostService{
		Queries:        queries,
		PostTagService: postTagService,
		Pool:           pool,
	}
}

// CreatePost creates a new post for an organization.
// userID is the ID of the authenticated user creating the post.
func (s *PostService) CreatePost(ctx context.Context, userID uuid.UUID, req payloads.CreatePostRequest) (db.Post, error) {
	slugStr := req.Slug
	if slugStr == "" {
		slugStr = slug.Make(req.Title)
	} else {
		slugStr = slug.Make(req.Slug) // Ensure user-provided slug is clean
	}

	// Check if slug is unique for this organization (or globally depending on requirements)
	// For now, assuming slug must be globally unique as per DB schema.
	_, err := s.Queries.GetPostBySlug(ctx, slugStr)
	if err == nil {
		// Slug exists, generate a unique one
		slugStr = fmt.Sprintf("%s-%s", slugStr, uuid.New().String()[:8])
	} else if !errors.Is(err, sql.ErrNoRows) {
		return db.Post{}, fmt.Errorf("failed to check slug uniqueness: %w", err)
	}

	// Determine initial status and publishedAt
	status := req.Status
	if status == "" {
		status = DefaultPostStatus // Default to 'draft'
	}

	var finalPublishedAt *time.Time // This will be passed to the DB

	if req.PublishedAt != nil { // User provided a PublishedAt
		if req.PublishedAt.After(time.Now()) { // Future date
			if status == "published" { // If user explicitly wants to publish but sets a future date
				// This case could be an error, or we honor PublishedAt and force draft.
				// Plan for resources: "If req.PublishedAt is provided and is in the future, and status is draft, the resource is scheduled."
				// Let's assume if PublishedAt is future, it implies 'draft' until that time, unless status is explicitly 'published' (then it's an immediate publish with that future date as published_at)
				// For now, let's align with: if PublishedAt is future, status becomes 'draft' to schedule it.
				// If they also send "status: published", the scheduler will pick it up.
				// If they send "status: draft" (or nothing, defaulting to draft), it's a clear schedule.
				status = "draft" // Ensure it's a draft if published_at is in the future
			}
			finalPublishedAt = req.PublishedAt
		} else { // Past or current date
			status = "published" // Publish immediately
			finalPublishedAt = req.PublishedAt
		}
	} else { // No PublishedAt provided by user
		if status == "published" { // If status is 'published' but no PublishedAt, publish now
			now := time.Now()
			finalPublishedAt = &now
		}
		// If status is 'draft' and no PublishedAt, finalPublishedAt remains nil (no schedule)
	}

	// If status ended up being 'published' but finalPublishedAt is still nil (e.g. user sent status: "published" but no published_at)
	// ensure published_at is set to now.
	if status == "published" && finalPublishedAt == nil {
		now := time.Now()
		finalPublishedAt = &now
	}

	var contentParam []byte
	if len(req.Content) > 0 { // Assuming req.Content is json.RawMessage or []byte
		contentParam = req.Content
	}

	if userID == uuid.Nil {
		return db.Post{}, errors.New("author ID (userID) cannot be nil when creating a post")
	}

	params := db.CreatePostParams{
		OrganizationID: req.OrganizationID,
		AuthorID:       userID,
		Title:          req.Title,
		Slug:           slugStr,
		Content:        contentParam,
		Status:         status,
		PublishedAt:    req.PublishedAt,
	}

	post, err := s.Queries.CreatePost(ctx, params)
	if err != nil {
		return db.Post{}, fmt.Errorf("failed to create post: %w", err)
	}

	// Handle TagIDs if provided in the request
	if req.TagIDs != nil && len(*req.TagIDs) > 0 {
		for _, tagID := range *req.TagIDs {
			err := s.PostTagService.AddTagToPost(ctx, post.ID, tagID)
			if err != nil {
				// Log error and continue. If one add fails, still try others.
				// The AddTagToPost query has ON CONFLICT DO NOTHING.
				log.Ctx(ctx).Error().Err(err).Str("post_id", post.ID.String()).Str("tag_id", tagID.String()).Msg("CreatePost: Failed to add tag to newly created post")
			}
		}
	}

	return post, nil
}

// GetPost retrieves a single post by its ID, including its media items and tags.
func (s *PostService) GetPostByID(ctx context.Context, postID uuid.UUID) (*payloads.PostResponse, error) {
	// Use GetPostDetailsWithTags which includes author display name and tags JSON
	postDetails, err := s.Queries.GetPostDetailsWithTags(ctx, postID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, utils.ErrNotFound
		}
		return nil, fmt.Errorf("failed to get post details with tags: %w", err)
	}

	// Media items need to be fetched separately as GetPostDetailsWithTags doesn't include them directly in a join.
	mediaItemsDB, err := s.Queries.ListMediaItemsByPost(ctx, postID)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		// Log error but proceed, media items are optional for the main post data
		log.Ctx(ctx).Error().Err(err).Str("post_id", postID.String()).Msg("Failed to list media items for GetPostByID")
	}
	mediaItemsPayload := payloads.ToPostMediaItemListResponse(mediaItemsDB)

	// Convert to response DTO.
	// The postDetails (db.GetPostDetailsWithTagsRow) contains the Tags as interface{}.
	// We need to assert it to json.RawMessage before passing to ToPostResponse.
	var tagsPayload json.RawMessage
	if postDetails.Tags != nil {
		if tagsRM, ok := postDetails.Tags.(json.RawMessage); ok {
			tagsPayload = tagsRM
		} else if tagsBytes, ok := postDetails.Tags.([]byte); ok {
			// If it's []byte, convert to json.RawMessage
			if json.Valid(tagsBytes) {
				tagsPayload = json.RawMessage(tagsBytes)
			}
		}
	}
	response := payloads.ToPostResponse(postDetails, mediaItemsPayload, tagsPayload)
	return &response, nil
}

// GetPostBySlug retrieves a single published post by its slug, including its media items and tags.
func (s *PostService) GetPostBySlug(ctx context.Context, slugStr string) (*payloads.PostResponse, error) {
	// Use GetPostDetailsWithTagsBySlug which includes author display name and tags JSON
	// Assuming a GetPostDetailsWithTagsBySlug query exists or can be added.
	// If not, we'd first GetPostBySlug, then GetPostDetailsWithTags by ID.
	// For this example, let's assume GetPostDetailsWithTagsBySlug is available.
	// If GetPostDetailsWithTagsBySlug is not available, we'd do:
	// postCore, err := s.Queries.GetPostBySlug(ctx, slugStr)
	// if err != nil { ... }
	// if postCore.Status != "published" { ... }
	// postDetails, err := s.Queries.GetPostDetailsWithTags(ctx, postCore.ID)
	// For now, let's assume a direct query for simplicity or that GetPostDetailsWithTags can take a slug.
	// The provided queries list GetPostDetailsWithTags (by ID) and ListPostsByOrganizationWithTags.
	// So, we must first fetch the post by slug, then its details with tags.

	postCore, err := s.Queries.GetPostBySlug(ctx, slugStr)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, utils.ErrNotFound
		}
		return nil, fmt.Errorf("failed to get post by slug: %w", err)
	}

	if postCore.Status != "published" {
		return nil, utils.ErrNotFound // Only published posts by slug
	}

	postDetails, err := s.Queries.GetPostDetailsWithTags(ctx, postCore.ID)
	if err != nil {
		// This could happen if the post was deleted between calls, though unlikely.
		return nil, fmt.Errorf("failed to get post details with tags after finding by slug: %w", err)
	}

	mediaItemsDB, err := s.Queries.ListMediaItemsByPost(ctx, postDetails.ID)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		log.Ctx(ctx).Error().Err(err).Str("post_id", postDetails.ID.String()).Msg("Failed to list media items for GetPostBySlug")
	}
	mediaItemsPayload := payloads.ToPostMediaItemListResponse(mediaItemsDB)

	var tagsPayload json.RawMessage
	if postDetails.Tags != nil {
		if tagsRM, ok := postDetails.Tags.(json.RawMessage); ok {
			tagsPayload = tagsRM
		} else if tagsBytes, ok := postDetails.Tags.([]byte); ok {
			if json.Valid(tagsBytes) {
				tagsPayload = json.RawMessage(tagsBytes)
			}
		}
	}
	response := payloads.ToPostResponse(postDetails, mediaItemsPayload, tagsPayload)
	return &response, nil
}

// ListPostsByOrganization retrieves posts for a specific organization with pagination.
// This is typically for an admin/management view, so it can include drafts.
func (s *PostService) ListPostsByOrganization(ctx context.Context, orgID uuid.UUID, params payloads.ListPostsParams) ([]payloads.PostResponse, int64, error) {
	log.Ctx(ctx).Debug().Str("org_id", orgID.String()).Interface("params", params).Msg("ListPostsByOrganization: Service called")

	var tagIDsForDB []uuid.UUID // Ensure type is []uuid.UUID
	if params.TagIDs != nil && len(params.TagIDs) > 0 {
		tagIDsForDB = params.TagIDs
	} else {
		// Always pass a non-nil (but possibly empty) slice for tag_ids
		tagIDsForDB = []uuid.UUID{}
	}

	// Prepare parameters for counting posts
	countParams := db.CountPostsByOrganizationWithTagsParams{
		OrganizationID: orgID,
		Status:         params.Status,
		TagIds:         tagIDsForDB, // This will be []uuid.UUID
		StartDate:      params.StartDate,
		EndDate:        params.EndDate,
		SearchTerm:     params.SearchTerm,
	}

	totalCount, err := s.Queries.CountPostsByOrganizationWithTags(ctx, countParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("org_id", orgID.String()).Interface("count_params", countParams).Msg("Failed to count posts by organization with tags and status")
		return nil, 0, fmt.Errorf("failed to count posts by organization: %w", err)
	}

	if totalCount == 0 {
		log.Ctx(ctx).Debug().Str("org_id", orgID.String()).Interface("params", params).Msg("ListPostsByOrganization: Total count is 0, returning empty list.")
		return []payloads.PostResponse{}, 0, nil
	}

	// Prepare parameters for listing posts
	listDbParams := db.ListPostsByOrganizationWithTagsParams{
		OrganizationID: orgID,
		Limit:          int32(params.Limit),
		Offset:         int32(params.Offset),
		Status:         params.Status,
		TagIds:         tagIDsForDB, // This will be []uuid.UUID
		StartDate:      params.StartDate,
		EndDate:        params.EndDate,
		SearchTerm:     params.SearchTerm,
	}

	log.Ctx(ctx).Debug().Str("org_id", orgID.String()).Interface("list_db_params", listDbParams).Msg("ListPostsByOrganization: Calling Queries.ListPostsByOrganizationWithTags")
	dbPostsWithTags, err := s.Queries.ListPostsByOrganizationWithTags(ctx, listDbParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("org_id", orgID.String()).Interface("list_db_params", listDbParams).Msg("Failed to list posts by organization with tags and status")
		return nil, 0, fmt.Errorf("failed to list posts by organization with tags: %w", err)
	}

	postResponses := payloads.ToPostListResponseFromListPostsByOrganizationWithTagsRows(dbPostsWithTags)
	postIDs := make([]uuid.UUID, 0, len(postResponses))
	for _, pr := range postResponses {
		postIDs = append(postIDs, pr.ID)
	}

	// Batch fetch media items for all posts in the current list
	if len(postIDs) > 0 {
		allMediaItemsDB, mediaErr := s.Queries.ListMediaItemsByPostIDs(ctx, postIDs) // Use the new batch query
		if mediaErr != nil && !errors.Is(mediaErr, sql.ErrNoRows) {
			log.Ctx(ctx).Error().Err(mediaErr).Interface("post_ids", postIDs).Msg("Failed to batch list media items for ListPostsByOrganization")
			// Proceed without media items if this fails, or handle error more strictly
		} else if len(allMediaItemsDB) > 0 {
			mediaByPostID := make(map[uuid.UUID][]payloads.PostMediaItemResponse)
			for _, mediaDB := range allMediaItemsDB {
				mediaByPostID[mediaDB.PostID] = append(mediaByPostID[mediaDB.PostID], payloads.ToPostMediaItemResponse(mediaDB))
			}

			for i := range postResponses {
				if media, ok := mediaByPostID[postResponses[i].ID]; ok {
					postResponses[i].MediaItems = media
				}
			}
		}
	}

	log.Ctx(ctx).Debug().Str("org_id", orgID.String()).Int("count_returned", len(postResponses)).Int64("total_count", totalCount).Msg("ListPostsByOrganization: Posts fetched successfully")
	return postResponses, totalCount, nil
}

// ListPublishedPosts retrieves all published posts with pagination.
// This is for public consumption.
func (s *PostService) ListPublishedPosts(ctx context.Context, params payloads.ListPostsParams) ([]payloads.PostResponse, int64, error) {
	// log.Ctx(ctx).Debug().Interface("params", params).Msg("ListPublishedPosts: Service called")

	// Ensure TagIDs is an empty slice if nil, for sqlc compatibility with `cardinality` check
	var tagIDsForDB []uuid.UUID
	if params.TagIDs != nil && len(params.TagIDs) > 0 {
		tagIDsForDB = params.TagIDs
	} else {
		tagIDsForDB = []uuid.UUID{}
	}

	// Convert *string to sql.NullString for SearchTerm, and *uuid.UUID to uuid.NullUUID for OrganizationID
	// sqlc typically generates *string for nullable text, and *uuid.UUID for nullable UUIDs when emit_pointers_for_null_types is true.
	// If using sql.NullString or pgtype.Text, conversion would be needed here.
	// Assuming direct use of *string and *uuid.UUID as per plan if sqlc configuration supports it for narg.
	// The SQL uses sqlc.narg which works well with pointers (*time.Time, *string, *uuid.UUID).

	countQueryDBParams := db.CountPublishedPostsParams{
		OrganizationID:  params.OrganizationID,
		OrganizationId2: params.OrganizationID2, // Corrected to OrganizationId2
		TagIds:          tagIDsForDB,
		StartDate:       params.StartDate,  // Pass *time.Time directly
		EndDate:         params.EndDate,    // Pass *time.Time directly
		SearchTerm:      params.SearchTerm, // Pass *string directly
	}

	totalCount, err := s.Queries.CountPublishedPosts(ctx, countQueryDBParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Interface("query_params", countQueryDBParams).Msg("Failed to count published posts")
		return nil, 0, fmt.Errorf("failed to count published posts: %w", err)
	}

	if totalCount == 0 {
		log.Ctx(ctx).Debug().Interface("params", params).Msg("ListPublishedPosts: Total count is 0, returning empty list.")
		return []payloads.PostResponse{}, 0, nil
	}

	listQueryDBParams := db.ListPublishedPostsWithAuthorParams{
		LimitVal:        int32(params.Limit),
		OffsetVal:       int32(params.Offset),
		OrganizationID:  params.OrganizationID,
		OrganizationId2: params.OrganizationID2, // Corrected to OrganizationId2
		TagIds:          tagIDsForDB,
		StartDate:       params.StartDate,  // Pass *time.Time directly
		EndDate:         params.EndDate,    // Pass *time.Time directly
		SearchTerm:      params.SearchTerm, // Pass *string directly
	}

	dbPostsWithAuthor, err := s.Queries.ListPublishedPostsWithAuthor(ctx, listQueryDBParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Interface("query_params", listQueryDBParams).Msg("Failed to list published posts with author")
		return nil, 0, fmt.Errorf("failed to list published posts: %w", err)
	}

	postResponses := make([]payloads.PostResponse, 0, len(dbPostsWithAuthor))
	postIDs := make([]uuid.UUID, 0, len(dbPostsWithAuthor))
	for _, dbPost := range dbPostsWithAuthor {
		// The Tags are not directly part of ListPublishedPostsWithAuthorRow in the current SQL structure.
		// Pass nil for mediaItems for now, will be populated later. Pass nil for tagsData.
		postResp := payloads.ToPostResponse(dbPost, nil, nil)
		postResponses = append(postResponses, postResp)
		postIDs = append(postIDs, dbPost.ID)
	}

	// Batch fetch media items for all posts in the current list
	if len(postIDs) > 0 {
		allMediaItemsDB, err := s.Queries.ListMediaItemsByPostIDs(ctx, postIDs) // Assuming this query exists or will be added
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			log.Ctx(ctx).Error().Err(err).Interface("post_ids", postIDs).Msg("Failed to batch list media items for ListPublishedPosts")
			// Proceed without media items if this fails, or handle error more strictly
		} else if len(allMediaItemsDB) > 0 {
			mediaByPostID := make(map[uuid.UUID][]payloads.PostMediaItemResponse)
			for _, mediaDB := range allMediaItemsDB {
				mediaByPostID[mediaDB.PostID] = append(mediaByPostID[mediaDB.PostID], payloads.ToPostMediaItemResponse(mediaDB))
			}

			for i := range postResponses {
				if media, ok := mediaByPostID[postResponses[i].ID]; ok {
					postResponses[i].MediaItems = media
				}
			}
		}
	}

	return postResponses, totalCount, nil
}

// ListPublishedPostsByOrganization retrieves published posts for a specific organization with pagination.
func (s *PostService) ListPublishedPostsByOrganization(ctx context.Context, orgID uuid.UUID, params payloads.ListPostsParams) ([]payloads.PostResponse, int64, error) {
	// Get total count first
	totalCount, err := s.Queries.CountPublishedPostsByOrganization(ctx, orgID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count published posts by organization: %w", err)
	}

	if totalCount == 0 {
		return []payloads.PostResponse{}, 0, nil
	}

	dbParams := db.ListPublishedPostsByOrganizationWithMediaParams{
		OrganizationID: orgID,
		Limit:          int32(params.Limit),
		Offset:         int32(params.Offset),
	}

	dbPostRows, err := s.Queries.ListPublishedPostsByOrganizationWithMedia(ctx, dbParams)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list published posts by organization with media: %w", err)
	}

	// Process rows into PostResponse with grouped media items
	// Re-use the existing helper function as the row structure is compatible
	postResponses := payloads.ToPostListResponseFromListPublishedPostsWithMediaRows(dbPostRows)

	return postResponses, totalCount, nil
}

// UpdatePost updates an existing post.
// userID is the ID of the authenticated user updating the post.
func (s *PostService) UpdatePost(ctx context.Context, postID uuid.UUID, userID uuid.UUID, req payloads.UpdatePostRequest) (db.Post, error) {
	originalPost, err := s.Queries.GetPostByID(ctx, postID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return db.Post{}, utils.ErrNotFound
		}
		return db.Post{}, fmt.Errorf("failed to get post for update: %w", err)
	}

	if userID == uuid.Nil {
		return db.Post{}, errors.New("updater ID (userID) cannot be nil when updating a post")
	}

	params := db.UpdatePostParams{
		ID:        postID,
		UpdatedBy: &userID, // Set the UpdatedBy field
		// Title, Slug, Content are COALESCE(sqlc.narg(), current), so pass pointer from req or nil
		Title: req.Title,
		Slug:  req.Slug,
	}
	// Only set content if it's explicitly provided in the request
	if len(req.Content) > 0 {
		params.Content = req.Content // req.Content is json.RawMessage which is []byte, sqlc.narg will handle if it's nil/empty
	}

	// --- Status and PublishedAt Logic (Revised as per plan4.md for Posts) ---
	// Initialize params fields that will be passed to DB update.
	// These are pointers, so nil means "do not update" due to COALESCE(sqlc.narg(), ...)
	var updateStatus *string
	var updatePublishedAt *time.Time

	// Determine the target status and published_at based on request and original post.
	// Start with original values. These are the values we are *aiming* for.
	targetStatus := originalPost.Status
	targetPublishedAt := originalPost.PublishedAt // This is *time.Time from db.Post

	// 1. Process req.Status first, as it can influence PublishedAt logic.
	// If req.Status is provided, it becomes the primary driver for targetStatus.
	if req.Status != nil {
		targetStatus = *req.Status
	}

	// 2. Process req.PublishedAt.
	if req.PublishedAt != nil { // User explicitly sent a published_at value.
		targetPublishedAt = req.PublishedAt    // Tentatively update to the new time.
		if req.PublishedAt.After(time.Now()) { // Future date.
			// Scheduling/Rescheduling: if user sends future PublishedAt, status must be draft.
			targetStatus = "draft"
		} else { // Past or current date.
			// Publishing immediately or updating a past publish: status must be published.
			targetStatus = "published"
		}
	} else { // req.PublishedAt is nil (not provided in request, meaning "omitempty" or user sent null to clear it).
		// If targetStatus (which could be original or from req.Status) is 'draft',
		// and req.PublishedAt is nil, this means "unschedule" or "keep as non-scheduled draft".
		if targetStatus == "draft" {
			targetPublishedAt = nil // Unschedule: set to NULL.
		}
		// If targetStatus is 'published' and req.PublishedAt is nil, targetPublishedAt remains originalPost.PublishedAt.
		// We don't clear published_at just because it wasn't sent in an update for an already published post,
		// unless status is also changing to draft (handled above or by req.Status).
	}

	// 3. Apply explicit status changes from req.Status and ensure consistency.
	// This step re-evaluates targetStatus and targetPublishedAt if req.Status was provided.
	if req.Status != nil {
		if *req.Status == "published" {
			targetStatus = "published" // Confirm target status.
			// If publishing, and targetPublishedAt is nil or future, set to now.
			if targetPublishedAt == nil || targetPublishedAt.After(time.Now()) {
				now := time.Now()
				targetPublishedAt = &now
			}
		} else if *req.Status == "draft" {
			targetStatus = "draft" // Confirm target status.
			// If changing to draft, and targetPublishedAt is currently set to past/current, clear it (unschedule).
			if targetPublishedAt != nil && !targetPublishedAt.After(time.Now()) {
				targetPublishedAt = nil
			}
		}
		// If req.Status is 'hidden' or another status, PublishedAt logic is mostly driven by previous steps.
	}

	// 4. Final consistency checks.
	// If final targetStatus is 'published', targetPublishedAt MUST be set. If nil, set to now.
	if targetStatus == "published" && targetPublishedAt == nil {
		now := time.Now()
		targetPublishedAt = &now
	}
	// If final targetStatus is 'draft', and targetPublishedAt is set to a past/current time,
	// it's an invalid schedule, so clear targetPublishedAt.
	if targetStatus == "draft" && targetPublishedAt != nil && !targetPublishedAt.After(time.Now()) {
		targetPublishedAt = nil
	}

	// 5. Set params for DB update only if final target values have changed from original.
	if targetStatus != originalPost.Status {
		updateStatus = &targetStatus
	}

	// Compare targetPublishedAt with originalPost.PublishedAt to see if an update is needed.
	originalPATisNull := originalPost.PublishedAt == nil
	targetPATisNull := targetPublishedAt == nil

	if originalPATisNull != targetPATisNull { // One is nil, the other is not (e.g., scheduling or unscheduling).
		updatePublishedAt = targetPublishedAt
	} else if !originalPATisNull && !targetPATisNull && !originalPost.PublishedAt.Equal(*targetPublishedAt) { // Both non-nil and different times.
		updatePublishedAt = targetPublishedAt
	}
	// If both are nil, or both non-nil and equal, updatePublishedAt remains nil (meaning no change to this field via params.PublishedAt).

	params.Status = updateStatus
	params.PublishedAt = updatePublishedAt
	// --- End of Status and PublishedAt Logic ---

	updatedPost, err := s.Queries.UpdatePost(ctx, params)
	if err != nil {
		return db.Post{}, fmt.Errorf("failed to update post: %w", err)
	}

	// Handle TagIDs if provided in the request
	if req.TagIDs != nil {
		// Get current tags for the post
		currentTags, err := s.PostTagService.GetTagsForPost(ctx, postID)
		if err != nil {
			// If fetching current tags fails, we cannot reliably update them.
			// Return an error instead of proceeding with a potentially incorrect state.
			log.Ctx(ctx).Error().Err(err).Str("post_id", postID.String()).Msg("UpdatePost: Failed to get current tags for post, cannot update tags")
			return db.Post{}, fmt.Errorf("failed to get current tags for post %s: %w", postID.String(), err)
		} else {
			// Remove all current tags ONLY if GetTagsForPost succeeded
			for _, tag := range currentTags {
				err := s.PostTagService.RemoveTagFromPost(ctx, postID, tag.ID)
				if err != nil {
					// Log error and continue. If one removal fails, still try others and adding new ones.
					log.Ctx(ctx).Error().Err(err).Str("post_id", postID.String()).Str("tag_id", tag.ID.String()).Msg("UpdatePost: Failed to remove current tag from post")
				}
			}

			// Add new tags
			// req.TagIDs is *[]uuid.UUID. Dereference it.
			if req.TagIDs != nil { // Double check, though outer if should cover
				for _, tagID := range *req.TagIDs {
					err := s.PostTagService.AddTagToPost(ctx, postID, tagID)
					if err != nil {
						// Log error and continue. If one add fails, still try others.
						// The AddTagToPost query has ON CONFLICT DO NOTHING, so some errors might be benign (e.g., tag doesn't exist, or already linked).
						// However, a real DB error should be logged.
						log.Ctx(ctx).Error().Err(err).Str("post_id", postID.String()).Str("tag_id", tagID.String()).Msg("UpdatePost: Failed to add new tag to post")
					}
				}
			}
		}
	}

	return updatedPost, nil
}

// DeletePost deletes a post and its associated media files from storage.
func (s *PostService) DeletePost(ctx context.Context, postID uuid.UUID) error {
	// First, get all media items for the post to delete files from storage
	mediaItems, err := s.Queries.ListMediaItemsByPost(ctx, postID)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return fmt.Errorf("failed to list media items for deletion: %w", err)
	}

	for _, item := range mediaItems {
		if err := os.Remove(item.FilePath); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("file_path", item.FilePath).Msg("Failed to delete post media file from storage")
			// Decide if we should continue or return an error. For now, log and continue.
		}
		if err := s.Queries.DeletePostMediaItem(ctx, item.ID); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("media_item_id", item.ID.String()).Msg("Failed to delete post media item from DB")
			// Decide if we should continue or return an error. For now, log and continue.
		}
	}

	err = s.Queries.DeletePost(ctx, postID)
	if err != nil {
		return fmt.Errorf("failed to delete post from DB: %w", err)
	}

	// Delete the post's media directory
	//uploads/post-media/{org_id_of_post}/{post_id}/
	// Need organization ID to construct this path. Fetch the post again (or ensure it's passed).
	// For now, skipping directory deletion as orgID isn't readily available here post-deletion.
	// This could be improved by fetching post details before deletion or changing how paths are stored/
	// managed if directory cleanup is critical.

	return nil
}

// UploadPostMedia handles uploading a media file for a specific post.
// func (s *PostService) UploadPostMedia(ctx context.Context, postID uuid.UUID, fileHeader *multipart.FileHeader) (db.PostMediaItem, error) {
// AddPostMediaItem handles uploading a media file for a specific post and optionally marking it as banner.
func (s *PostService) AddPostMediaItem(ctx context.Context, postID uuid.UUID, fileHeader *multipart.FileHeader, isBanner bool) (*payloads.PostMediaItemResponse, error) {
	post, err := s.Queries.GetPostByID(ctx, postID) // Initial check outside transaction for early exit
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, utils.ErrNotFound
		}
		return nil, fmt.Errorf("post not found: %w", err)
	}

	if fileHeader.Size > MaxUploadSizePostMedia {
		return nil, fmt.Errorf("file size %d exceeds maximum allowed %d bytes", fileHeader.Size, MaxUploadSizePostMedia)
	}

	orgPostMediaDir := filepath.Join(UploadDirPosts, post.OrganizationID.String(), postID.String())
	if err := os.MkdirAll(orgPostMediaDir, os.ModePerm); err != nil {
		return nil, fmt.Errorf("failed to create upload directory: %w", err)
	}

	safeFileName := slug.Make(strings.TrimSuffix(fileHeader.Filename, filepath.Ext(fileHeader.Filename))) + filepath.Ext(fileHeader.Filename)
	destPath := filepath.Join(orgPostMediaDir, safeFileName)

	counter := 1
	for {
		if _, errStat := os.Stat(destPath); os.IsNotExist(errStat) {
			break
		}
		fileExt := filepath.Ext(safeFileName)
		fileNameOnly := strings.TrimSuffix(safeFileName, fileExt)
		baseNameOnly := fileNameOnly
		if counter > 1 {
			baseNameOnly = strings.TrimSuffix(fileNameOnly, fmt.Sprintf("(%d)", counter-1))
		}
		destPath = filepath.Join(orgPostMediaDir, fmt.Sprintf("%s(%d)%s", baseNameOnly, counter, fileExt))
		counter++
	}

	src, err := fileHeader.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer src.Close()

	dstFile, err := os.Create(destPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dstFile.Close()

	if _, err := io.Copy(dstFile, src); err != nil {
		// If copy fails, attempt to remove the partially created file
		_ = os.Remove(destPath)
		return nil, fmt.Errorf("failed to copy uploaded file to destination: %w", err)
	}

	var mediaItem db.PostMediaItem
	tx, err := s.Pool.Begin(ctx)
	if err != nil {
		_ = os.Remove(destPath) // Clean up file if transaction cannot start
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx) // Rollback unless committed

	qtx := s.Queries.WithTx(tx)

	if isBanner {
		if err := qtx.UnsetBannerForPostMediaItems(ctx, postID); err != nil {
			_ = os.Remove(destPath) // Clean up file
			return nil, fmt.Errorf("failed to unset existing banner: %w", err)
		}
	}

	mediaParams := db.CreatePostMediaItemParams{
		PostID:   postID,
		FileName: safeFileName,
		FilePath: destPath, // Store the actual saved path
		FileType: fileHeader.Header.Get("Content-Type"),
		FileSize: fileHeader.Size,
		IsBanner: isBanner,
	}

	createdMediaItem, err := qtx.CreatePostMediaItem(ctx, mediaParams)
	if err != nil {
		_ = os.Remove(destPath) // Clean up file
		return nil, fmt.Errorf("failed to create post media item in DB: %w", err)
	}
	mediaItem = createdMediaItem

	if err := tx.Commit(ctx); err != nil {
		_ = os.Remove(destPath) // Clean up file
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	payloadResponse := payloads.ToPostMediaItemResponse(mediaItem)
	return &payloadResponse, nil
}

// DeletePostMedia deletes a media item associated with a post.
// ... existing code ...
// SetBannerForPostMediaItem designates an existing media item as the banner for a post.
func (s *PostService) SetBannerForPostMediaItem(ctx context.Context, postID, mediaItemID uuid.UUID) (*payloads.PostMediaItemResponse, error) {
	// Check if media item belongs to the post (outside transaction for early exit)
	mediaItemToCheck, err := s.Queries.GetPostMediaItemByID(ctx, mediaItemID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, utils.ErrNotFound
		}
		return nil, fmt.Errorf("failed to get media item %s: %w", mediaItemID, err)
	}
	if mediaItemToCheck.PostID != postID {
		return nil, utils.ErrForbidden // Or a more specific error like ErrMediaItemNotForPost
	}

	var updatedMediaItem db.PostMediaItem
	tx, err := s.Pool.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx) // Rollback unless committed

	qtx := s.Queries.WithTx(tx)

	if err := qtx.UnsetBannerForPostMediaItems(ctx, postID); err != nil {
		return nil, fmt.Errorf("failed to unset existing banner for post %s: %w", postID, err)
	}

	setParams := db.SetBannerForPostMediaItemParams{
		ID:     mediaItemID,
		PostID: postID,
	}
	item, err := qtx.SetBannerForPostMediaItem(ctx, setParams)
	if err != nil {
		return nil, fmt.Errorf("failed to set banner for media item %s: %w", mediaItemID, err)
	}
	updatedMediaItem = item

	if err := tx.Commit(ctx); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	payloadResponse := payloads.ToPostMediaItemResponse(updatedMediaItem)
	return &payloadResponse, nil
}

// DeletePostMediaItem deletes a specific media item associated with a post and its file from storage.
func (s *PostService) DeletePostMediaItem(ctx context.Context, mediaItemID uuid.UUID) error {
	// Get media item details to find the file path for deletion
	mediaItem, err := s.Queries.GetPostMediaItemByID(ctx, mediaItemID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return utils.ErrNotFound // Media item not found
		}
		return fmt.Errorf("failed to get post media item %s for deletion: %w", mediaItemID, err)
	}

	// Delete the physical file from storage
	if mediaItem.FilePath != "" { // Check if FilePath is not empty
		if err := os.Remove(mediaItem.FilePath); err != nil {
			// Log the error but attempt to delete from DB anyway
			log.Ctx(ctx).Error().Err(err).Str("file_path", mediaItem.FilePath).Msg("Failed to delete post media file from storage")
			// Depending on requirements, you might want to return an error here and not proceed.
			// For now, we log and continue to attempt DB deletion.
		}
	}

	// Delete the media item record from the database
	err = s.Queries.DeletePostMediaItem(ctx, mediaItemID)
	if err != nil {
		// If the item was not found for DB deletion, it might have already been deleted
		// or the file deletion part failed but we still want to report DB error if it occurs.
		if errors.Is(err, sql.ErrNoRows) {
			return utils.ErrNotFound // Or perhaps it's not an error if we expect it might be gone
		}
		return fmt.Errorf("failed to delete post media item %s from DB: %w", mediaItemID, err)
	}

	return nil
}

// SchedulePublishedPosts checks for scheduled posts and publishes them.
// ... existing code ...
