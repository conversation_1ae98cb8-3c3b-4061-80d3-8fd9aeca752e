import React, { useState, useEffect, useLayoutEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    Image,
    Dimensions,
    TouchableOpacity,
    Platform,
    Linking,
    Alert,
    Share,
    ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, useLocalSearchParams, Stack, useNavigation } from 'expo-router';
import { Button } from 'react-native-paper';
import { parseISO, addHours, isBefore, format } from 'date-fns';
import * as Calendar from 'expo-calendar';
import { appStyleStore } from 'stores/app_style_store';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { generateShareContent } from 'utils/shareUtils';
import { type EventRegistrationPayload, type EventListPayloadDetails, type MediaItemPayload, MEDIA_BASE_URL } from '@/api/api_config';
import { useFetchRegisteredEventDetails, useCancelEventRegistration } from '@/api/user_events_services';
import { useFetchEventDetails } from '@/api/public_events_services';
import { ErrorView } from '@/common_modules/ErrorView';
import { TextInputDialog } from '@/common_modules/TextInputDialog';
import * as ics from 'ics';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

const getValidImageUrl = (filePath: string | undefined): string | undefined => {
  if (!filePath) return undefined;
  try {
    const url = new URL(filePath, MEDIA_BASE_URL);
    return new URL(url.pathname, MEDIA_BASE_URL).toString();
  } catch (error) {
    return undefined;
  }
};

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export const EventManagementScreen = () => {
    const { t, i18n } = useTranslation();
    const activeTheme = appStyleStore(state => state.theme);

    const router = useRouter();
    const navigation = useNavigation();
    const params = useLocalSearchParams<{ registrationId?: string; eventId?: string; eventTitle?: string; role?: 'participant' | 'volunteer' }>();
    
    const registrationId = params.registrationId;
    const role = params.role;

    const { 
        data: registrationData, 
        isLoading: isLoadingRegistration, 
        error: registrationError,
        refetch: refetchRegistrationDetails,
    } = useFetchRegisteredEventDetails({ registrationId: registrationId || '' });
    
    const registrationDetails: EventRegistrationPayload | null = registrationData || null;
    const eventIdForDetailsHook = registrationDetails?.event_id;

    const { 
        data: eventDetailsData, 
        isLoading: isLoadingEventDetails, 
        error: eventDetailsError,
        refetch: refetchEventDetails,
    } = useFetchEventDetails({ eventId: eventIdForDetailsHook || '' });

    const detailedEvent: EventListPayloadDetails | null = eventDetailsData || null;

    const { mutateAsync: cancelRegistrationMutation, isPending: isCancelling } = useCancelEventRegistration();

    const [isPastEvent, setIsPastEvent] = useState(false);
    const [cancellationReason, setCancellationReason] = useState('');
    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [showWithdrawDialog, setShowWithdrawDialog] = useState(false);
    const [showResultDialog, setShowResultDialog] = useState(false);
    const [resultDialogProps, setResultDialogProps] = useState<{
        type: 'success' | 'error';
        title: string;
        message: string;
    }>({ type: 'success', title: '', message: '' });
    const [showReasonInputDialog, setShowReasonInputDialog] = useState(false);

    const overallIsLoading = isLoadingRegistration || (!!registrationDetails && !detailedEvent && isLoadingEventDetails);
    const overallError = registrationError || eventDetailsError;

    useLayoutEffect(() => {
        if (registrationDetails) {
            navigation.setOptions({
                title: t('eventManagement.title'),
            });
        }
    }, [navigation, registrationDetails, t, activeTheme]);

    useLayoutEffect(() => {
        navigation.setOptions({
            title: t('eventManagement.title'),
            headerTitleAlign: 'center',
            headerShadowVisible: false,
            headerStyle: { backgroundColor: activeTheme.colors.background },
            headerTitleStyle: { color: activeTheme.system.text },
            headerTintColor: activeTheme.colors.primary,
        });
    }, [navigation, t, i18n.language, activeTheme]);

    useEffect(() => {
        if (registrationDetails?.event_start_time) {
            try {
                const eventStartDate = parseISO(registrationDetails.event_start_time);
                setIsPastEvent(isBefore(eventStartDate, new Date()));
            } catch (dateError) {
                setIsPastEvent(false);
            }
        }
    }, [registrationDetails?.event_start_time]);
    
    useEffect(() => {
        if (params.registrationId && !registrationDetails && !isLoadingRegistration) {
            refetchRegistrationDetails();
        }
        if (eventIdForDetailsHook && !detailedEvent && !isLoadingEventDetails) {
            refetchEventDetails();
        }
    }, [params.registrationId, registrationDetails, isLoadingRegistration, refetchRegistrationDetails, eventIdForDetailsHook, detailedEvent, isLoadingEventDetails, refetchEventDetails]);

    const getRoleColor = (roleParam: 'participant' | 'volunteer' | string | undefined) => {
        return roleParam === 'volunteer' ? activeTheme.system.volunteer : activeTheme.system.participant;
    };

    const handleShare = async () => {
        if (!registrationDetails || isPastEvent) return;
        try {
            const shareEventData = {
                id: registrationDetails.event_id,
                title: registrationDetails.event_title,
                date: registrationDetails.event_start_time,
                location: registrationDetails.event_location_full_address || registrationDetails.event_location_online_url || '',
                type: registrationDetails.registration_role as 'participant' | 'volunteer',
                participantsCount: registrationDetails.registered_count || 0,
            };
            const shareContent = generateShareContent({
                event: shareEventData,
                t,
                language: i18n.language
            });
            await Share.share(shareContent);
        } catch (shareError) {
        }
    };

    if (overallIsLoading && !registrationDetails) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: activeTheme.system.background }}>
                <ActivityIndicator size="large" color={activeTheme.colors.primary} />
            </View>
        );
    }

    if (overallError || (!isLoadingRegistration && !registrationDetails)) {
        return (
             <>
                <Stack.Screen options={{ title: t('eventManagement.title')}} />
                <ErrorView
                    onGoBack={() => router.back()}
                />
            </>
        );
    }
    
    if (!registrationDetails) {
        return <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><Text>Error: Registration details not found.</Text></View>;
    }

    const handleShowQRCode = () => {
        router.push('/tabs/qrcode');
    };

    const handleViewEventDetail = () => {
        router.push({ pathname: '/explore/cardDetails/EventDetailsScreen', params: { eventId: registrationDetails.event_id } });
    };

    const handleAddToCalendar = async () => {
        if (isPastEvent) return;
        try {
            if (Platform.OS === 'android' || Platform.OS === 'ios') {
                const { status } = await Calendar.requestCalendarPermissionsAsync();
                if (status !== 'granted') {
                    Alert.alert(
                        t(Platform.OS === 'ios' ? 'events.detail.permissionRequired' : 'events.detail.calendarPermissionDenied'),
                        t(Platform.OS === 'ios' ? 'events.detail.permissionDeniedIOS' : 'events.detail.calendarPermissionDeniedMessage'),
                        [
                            { text: t('common.cancel'), style: 'cancel' },
                            { text: t('events.detail.openSettings'), onPress: () => Platform.OS === 'ios' ? Linking.openURL('app-settings:') : Linking.openSettings() }
                        ]
                    );
                    return;
                }
            }

            const startDate = parseISO(registrationDetails.event_start_time);
            const endDate = registrationDetails.event_end_time ? parseISO(registrationDetails.event_end_time) : addHours(startDate, 2);

            if (Platform.OS === 'web') {
                const eventDetailsForIcs: ics.EventAttributes = {
                    title: registrationDetails.event_title,
                    description: registrationDetails.event_description || registrationDetails.event_title,
                    location: registrationDetails.event_location_full_address || registrationDetails.event_location_online_url || '',
                    start: [startDate.getFullYear(), startDate.getMonth() + 1, startDate.getDate(), startDate.getHours(), startDate.getMinutes()],
                    end: [endDate.getFullYear(), endDate.getMonth() + 1, endDate.getDate(), endDate.getHours(), endDate.getMinutes()],
                    status: 'CONFIRMED',
                };

                const { error: icsError, value } = ics.createEvent(eventDetailsForIcs);
                if (icsError || !value) {
                    Alert.alert(t('events.detail.addToCalendarError'), t('events.detail.addToCalendarErrorDesc'));
                    return;
                }

                const filename = `${registrationDetails.event_title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.ics`;
                const uri = FileSystem.cacheDirectory + filename;
                await FileSystem.writeAsStringAsync(uri, value, { encoding: FileSystem.EncodingType.UTF8 });

                if (!(await Sharing.isAvailableAsync())) {
                    const link = document.createElement('a');
                    link.href = `data:text/calendar;charset=utf-8,${encodeURIComponent(value)}`;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    Alert.alert(t('events.detail.addToCalendarSuccess'), t('events.detail.icsDownloaded'));
                } else {
                    await Sharing.shareAsync(uri, { mimeType: 'text/calendar', dialogTitle: t('events.detail.shareIcsDialogTitle') });
                    Alert.alert(t('events.detail.shareActionTitle'), t('events.detail.shareActionMessage'));
                }
            } else {
                const calendars = await Calendar.getCalendarsAsync(Calendar.EntityTypes.EVENT);
                const defaultCalendar = calendars.find((cal: Calendar.Calendar) => 
                    cal.allowsModifications && 
                    (cal.source.name === 'iCloud' || cal.source.name === 'Default' || 
                     cal.isPrimary || (cal.source && cal.source.type === 'com.google'))
                );

                if (!defaultCalendar) {
                    Alert.alert(t('events.detail.addToCalendarError'), t('events.detail.calendarReadOnly'));
                    return;
                }
                
                const existingEvents = await Calendar.getEventsAsync([defaultCalendar.id], startDate, endDate);
                const duplicateEvent = existingEvents.find((event: Calendar.Event) => event.title === registrationDetails.event_title);
                
                if (duplicateEvent) {
                    Alert.alert(t('events.detail.eventAlreadyAdded'), t('events.detail.eventAlreadyAddedDesc'));
                    return;
                }

                await Calendar.createEventAsync(defaultCalendar.id, {
                    title: registrationDetails.event_title,
                    location: registrationDetails.event_location_full_address || registrationDetails.event_location_online_url || '',
                    startDate,
                    endDate,
                    notes: registrationDetails.event_description || registrationDetails.event_title,
                    alarms: [{ relativeOffset: -60 }],
                    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    availability: Calendar.Availability.BUSY
                });
                Alert.alert(t('events.detail.addToCalendarSuccess'), t('events.detail.addToCalendarSuccessDesc'));
            }
        } catch (calError) {
            Alert.alert(t('events.detail.addToCalendarError'), t('events.detail.addToCalendarErrorDesc'));
        }
    };

    const handleGetDirections = () => {
        if (!detailedEvent?.location_full_address || isPastEvent) return;
        const address = detailedEvent.location_full_address;
        const iosUrl = `maps:0,0?q=${encodeURIComponent(address)}`;
        const androidUrl = `geo:0,0?q=${encodeURIComponent(address)}`;
        const webUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;

        const openMap = (url: string) => Linking.canOpenURL(url).then(supported => {
            if (supported) Linking.openURL(url);
            else Alert.alert(t('common.error'), t('events.detail.mapAppError'));
        });

        Alert.alert(
            t('events.detail.openMap'),
            t('events.detail.chooseMapApp'),
            Platform.select({
                ios: [
                    { text: 'Apple Maps', onPress: () => openMap(iosUrl) },
                    { text: 'Google Maps', onPress: () => openMap(`comgooglemaps://?q=${encodeURIComponent(address)}`) },
                    { text: t('common.cancel'), style: 'cancel' },
                ],
                android: [
                    { text: 'Google Maps', onPress: () => openMap(androidUrl) },
                    { text: t('common.cancel'), style: 'cancel' },
                ],
                default: [
                    { text: 'Google Maps', onPress: () => openMap(webUrl) },
                    { text: t('common.cancel'), style: 'cancel' },
                ]
            })
        );
    };

    const handleCancelParticipation = () => {
        if (registrationDetails.registration_role !== 'participant') return;
        setCancellationReason('');
        setShowReasonInputDialog(true);
    };

    const handleSubmitReasonAndProceedToConfirm = () => {
        setShowReasonInputDialog(false);
        setShowCancelDialog(true);
    };

    const handleConfirmCancel = async () => {
        setShowCancelDialog(false); 
        try {
            await cancelRegistrationMutation({ 
                registrationId: registrationDetails.id, 
                eventId: registrationDetails.event_id,
            });
            setResultDialogProps({
                type: 'success',
                title: t('eventManagement.cancelSuccess.title'),
                message: t('eventManagement.cancelSuccess.message'),
            });
        } catch (e: any) {
            setResultDialogProps({
                type: 'error',
                title: t('eventManagement.cancelError.title'),
                message: e.message || t('eventManagement.cancelError.message'),
            });
        }
        setShowResultDialog(true);
        setCancellationReason(''); 
    };

    const handleWithdrawVolunteerApplication = () => {
        if (registrationDetails.registration_role !== 'volunteer') return;
        setShowWithdrawDialog(true);
    };

    const handleConfirmWithdraw = async () => {
        setShowWithdrawDialog(false);
        try {
            await cancelRegistrationMutation({ 
                registrationId: registrationDetails.id, 
                eventId: registrationDetails.event_id,
            });
            setResultDialogProps({
                type: 'success',
                title: t('eventManagement.withdrawSuccess.title'),
                message: t('eventManagement.withdrawSuccess.message'),
            });
        } catch (e: any) {
            setResultDialogProps({
                type: 'error',
                title: t('eventManagement.withdrawError.title'),
                message: e.message || t('eventManagement.withdrawError.message'),
            });
        }
        setShowResultDialog(true);
        setCancellationReason('');
    };

    const handleResultDialogConfirm = () => {
        setShowResultDialog(false);
        if (resultDialogProps.type === 'success') {
            router.back();
        }
    };

    const DetailSection = ({ title, content }: { title: string; content: string | React.ReactNode }) => (
        <View>
            <Text style={styles.sectionTitle}>{title}</Text>
            {typeof content === 'string' ? (
                <Text style={styles.detailContent}>{content}</Text>
            ) : (
                content
            )}
        </View>
    );

    const styles = getStyles(activeTheme);

    let mainEventImage = detailedEvent?.media_items?.find((item: MediaItemPayload) => item.is_banner);
    if (!mainEventImage && detailedEvent?.media_items && detailedEvent.media_items.length > 0) {
        mainEventImage = detailedEvent.media_items[0];
    }
    const imageUri = mainEventImage ? getValidImageUrl(mainEventImage.file_path) : undefined;

    return (
        <>
            <Stack.Screen />
            <View style={styles.container}>
                <ScrollView bounces={false} showsVerticalScrollIndicator={false}>
                    <View style={styles.imageContainer}>
                        {imageUri ? (
                            <Image source={{ uri: imageUri }} style={styles.coverImage} />
                        ) : (
                            <View style={styles.noImagePlaceholder}>
                                <MaterialCommunityIcons name="image-off" size={48} color={activeTheme.colors.secondary}/>
                            </View>
                        )}
                    </View>

                    <View style={styles.content}>
                        <View style={styles.headerSection}>
                            <View style={styles.tagContainer}>
                                <View style={styles.roleTag}>
                                    <MaterialCommunityIcons
                                        name="account-group"
                                        size={16}
                                        color={getRoleColor(registrationDetails.registration_role)}
                                    />
                                    <Text style={[styles.roleText, { color: getRoleColor(registrationDetails.registration_role) }]}>
                                        {t(registrationDetails.registration_role === 'volunteer' ? 'my_events.status.volunteer' : 'my_events.status.participant')}
                                    </Text>
                                </View>
                                <View style={[
                                    styles.statusTag,
                                    {
                                        backgroundColor: isPastEvent ? 
                                            (registrationDetails.status === 'absent' ? '#FFF1F0' : '#F6FFED') :
                                            'transparent'
                                    }
                                ]}>
                                    {isPastEvent && (
                                        <>
                                            <MaterialCommunityIcons
                                                name={registrationDetails.status === 'absent' ? 'close-circle' : 'check-circle'}
                                                size={16}
                                                color={registrationDetails.status === 'absent' ? activeTheme.system.error : activeTheme.system.success}
                                            />
                                            <Text style={[
                                                styles.statusText,
                                                {
                                                    color: registrationDetails.status === 'absent' ? activeTheme.system.error : activeTheme.system.success
                                                }
                                            ]}>
                                                {t(registrationDetails.status === 'absent' ? 'my_events.status.notAttended' : 'my_events.status.completed')}
                                            </Text>
                                        </>
                                    )}
                                </View>
                                <TouchableOpacity style={styles.viewDetailButton} onPress={handleViewEventDetail}>
                                    <Text style={styles.viewDetailText}>{t('events.detail.viewDetails')}</Text>
                                    <MaterialCommunityIcons name="chevron-right" size={20} color="#666666" />
                                </TouchableOpacity>
                            </View>
                            <Text style={styles.title}>{registrationDetails.event_title}</Text>
                        </View>

                        <View style={styles.infoSection}>
                            <View style={styles.sectionHeader}>
                                <Text style={styles.sectionTitle}>{t('events.detail.dateAndTime')}</Text>
                                {!isPastEvent && (<Text style={styles.sectionHint}>{t('eventManagement.clickToAddCalendar')}</Text>)}
                            </View>
                            {isPastEvent ? (
                                <View style={styles.infoCard}>
                                    <View style={styles.infoContent}>
                                        <MaterialCommunityIcons name="calendar-clock" size={24} color={activeTheme.colors.primary} />
                                        <View style={styles.infoTextContainer}>
                                            <Text style={[styles.infoText, { color: activeTheme.system.secondaryText }]}>
                                                {registrationDetails.event_start_time ? format(parseISO(registrationDetails.event_start_time), 'p') : ''}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            ) : (
                                <TouchableOpacity style={styles.infoCard} onPress={handleAddToCalendar}>
                                    <View style={styles.infoContent}>
                                        <MaterialCommunityIcons name="calendar-clock" size={24} color={activeTheme.colors.primary} />
                                        <View style={styles.infoTextContainer}>
                                            <Text style={[styles.infoText, { color: activeTheme.system.secondaryText }]}>
                                                {registrationDetails.event_start_time ? format(parseISO(registrationDetails.event_start_time), 'p') : ''}
                                            </Text>
                                            <Text style={styles.ctaText}>{t('events.detail.addToCalendar')}</Text>
                                        </View>
                                        <MaterialCommunityIcons name="chevron-right" size={24} color="#666666" />
                                    </View>
                                </TouchableOpacity>
                            )}

                            <View style={styles.sectionHeader}>
                                <Text style={styles.sectionTitle}>{t('events.detail.location')}</Text>
                            </View>
                            <TouchableOpacity style={styles.infoCard} onPress={handleGetDirections} disabled={isPastEvent}>
                                <View style={styles.infoContent}>
                                    <MaterialCommunityIcons name="map-marker" size={24} color={activeTheme.colors.primary} />
                                    <View style={styles.infoTextContainer}>
                                        <Text style={[styles.infoText, { color: activeTheme.system.secondaryText }]}>
                                            {registrationDetails.event_location_full_address || 
                                             registrationDetails.event_location_online_url || 
                                             t('eventManagement.locationNotAvailable')}
                                        </Text>
                                        {!isPastEvent && (<Text style={styles.ctaText}>{t('events.detail.openToMapApp')}</Text>)}
                                    </View>
                                    {!isPastEvent && (<MaterialCommunityIcons name="chevron-right" size={24} color="#666666" />)}
                                </View>
                            </TouchableOpacity>
                        </View>

                        <DetailSection
                            title={t('eventManagement.howToJoin')}
                            content={
                                <View style={styles.stepsContainer}>
                                    <View style={styles.stepItem}>
                                        <View style={[styles.stepIconContainer, { backgroundColor: activeTheme.colors.primaryContainer }]}>
                                            <MaterialCommunityIcons name="map-marker-radius" size={24} color={activeTheme.colors.primary} />
                                        </View>
                                        <View style={styles.stepContent}>
                                            <Text style={styles.stepNumber}>{t('common.step')} 1</Text>
                                            <Text style={styles.stepText}>{t('eventManagement.steps.arrival')}</Text>
                                        </View>
                                    </View>
                                    <View style={styles.stepItem}>
                                        <View style={[styles.stepIconContainer, { backgroundColor: activeTheme.colors.primaryContainer }]}>
                                            <MaterialCommunityIcons name="qrcode-scan" size={24} color={activeTheme.colors.primary} />
                                        </View>
                                        <View style={styles.stepContent}>
                                            <Text style={styles.stepNumber}>{t('common.step')} 2</Text>
                                            <Text style={styles.stepText}>{t('eventManagement.steps.showQRCode')}</Text>
                                            <TouchableOpacity onPress={handleShowQRCode} style={styles.qrCodeLink}>
                                                <Text style={[styles.qrCodeLinkText, { color: activeTheme.colors.primary }]}>
                                                    {t('eventManagement.openQRCode')}
                                                </Text>
                                                <MaterialCommunityIcons name="chevron-right" size={16} color={activeTheme.colors.primary} />
                                            </TouchableOpacity>
                                            <Text style={styles.qrCodeHint}>{t('eventManagement.qrCodeHint')}</Text>
                                        </View>
                                    </View>
                                </View>
                            }
                        />
                        
                        <View style={styles.actionButtonContainer}>
                            {registrationDetails.registration_role === 'participant' && 
                                !isPastEvent && 
                                ['registered', 'waitlisted', 'pending_approval'].includes(registrationDetails.status) && (
                                <Button
                                    mode="outlined"
                                    onPress={handleCancelParticipation}
                                    style={styles.actionButton}
                                    labelStyle={styles.actionButtonLabel}
                                    textColor={activeTheme.colors.error}
                                    loading={isCancelling && registrationDetails.status !== 'cancelled_by_user'}
                                    disabled={isCancelling}
                                >
                                    {t('eventManagement.cancelButton')}
                                </Button>
                            )}
                            {registrationDetails.registration_role === 'volunteer' && 
                                !isPastEvent && 
                                ['pending', 'approved'].includes(registrationDetails.status) &&
                                <Button
                                    mode="outlined"
                                    onPress={handleWithdrawVolunteerApplication}
                                    style={styles.actionButton}
                                    labelStyle={styles.actionButtonLabel}
                                    textColor={activeTheme.colors.error}
                                    loading={isCancelling}
                                    disabled={isCancelling}
                                >
                                    {t('eventManagement.withdrawButton')} 
                                </Button>
                            }
                        </View>
                    </View>
                </ScrollView>

                <TextInputDialog
                    visible={showReasonInputDialog}
                    title={t('eventManagement.cancelReason.title')}
                    placeholder={t('eventManagement.cancelReason.placeholder')}
                    value={cancellationReason}
                    onChangeText={setCancellationReason}
                    confirmText={t('common.next')}
                    cancelText={t('common.skipAndContinue')}
                    onCancel={() => {
                        setShowReasonInputDialog(false);
                        setCancellationReason('');
                        setShowCancelDialog(true);
                    }}
                    onConfirm={handleSubmitReasonAndProceedToConfirm}
                    maxLength={200}
                />

                <CustomDialog
                    visible={showCancelDialog}
                    title={t('eventManagement.cancelConfirm.title')}
                    message={t('eventManagement.cancelConfirm.message')}
                    confirmText={t('common.confirm')}
                    cancelText={t('common.cancel')}
                    onConfirm={handleConfirmCancel}
                    onCancel={() => setShowCancelDialog(false)}
                    type="warning"
                    confirmLoading={isCancelling}
                />

                <CustomDialog
                    visible={showWithdrawDialog}
                    title={t('eventManagement.withdrawConfirm.title')}
                    message={t('eventManagement.withdrawConfirm.message')}
                    confirmText={t('common.confirm')}
                    cancelText={t('common.cancel')}
                    onConfirm={handleConfirmWithdraw}
                    onCancel={() => setShowWithdrawDialog(false)}
                    type="warning"
                    confirmLoading={isCancelling}
                />

                <CustomDialog
                    visible={showResultDialog}
                    title={resultDialogProps.title}
                    message={resultDialogProps.message}
                    confirmText={t('common.ok')}
                    onConfirm={handleResultDialogConfirm}
                    type={resultDialogProps.type}
                />
            </View>
        </>
    );
};

const getStyles = (theme: any) => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.system.background,
    },
    imageContainer: {
        height: SCREEN_HEIGHT * 0.35,
        width: SCREEN_WIDTH,
    },
    coverImage: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
    },
    content: {
        flex: 1,
        backgroundColor: theme.system.background,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        marginTop: -24,
        paddingTop: 24,
        paddingHorizontal: 20,
        paddingBottom: 32,
    },
    headerSection: {
        marginBottom: 24,
    },
    tagContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
        flexWrap: 'wrap',
    },
    roleTag: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        backgroundColor: theme.colors.primaryContainer,
        paddingHorizontal: 12,
        paddingVertical: 4,
        borderRadius: 16,
        gap: 4,
    },
    roleText: {
        fontSize: 14,
        fontWeight: '500',
    },
    statusTag: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 4,
        borderRadius: 16,
        gap: 4,
        marginLeft: 8,
    },
    statusText: {
        fontSize: 14,
        fontWeight: '500',
    },
    title: {
        fontSize: 24,
        fontWeight: '700',
        color: theme.system.text,
        marginBottom: 12,
    },
    viewDetailButton: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 'auto',
    },
    viewDetailText: {
        fontSize: 14,
        fontWeight: '500',
        color: theme.system.secondaryText,
    },
    infoSection: {
        marginBottom: 24,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 12,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: theme.system.text,
    },
    sectionHint: {
        fontSize: 12,
        color: theme.system.secondaryText,
    },
    infoCard: {
        backgroundColor: theme.colors.primaryContainer,
        borderRadius: 12,
        padding: 16,
        ...Platform.select({
            android: {
                elevation: 1,
            },
            ios: {
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.1,
                shadowRadius: 1,
            },
        }),
        marginBottom: 16,
    },
    infoContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    infoTextContainer: {
        flex: 1,
        marginLeft: 12,
        marginRight: 8,
    },
    infoText: {
        fontSize: 15,
        color: theme.system.text,
        fontWeight: '500',
    },
    ctaText: {
        fontSize: 14,
        color: theme.colors.primary,
        marginTop: 4,
    },
    detailContent: {
        fontSize: 15,
        lineHeight: 24,
        color: theme.system.text,
    },
    stepsContainer: {
        marginTop: 16,
        gap: 20,
    },
    stepItem: {
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    stepIconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
        backgroundColor: theme.colors.primaryContainer,
    },
    stepContent: {
        flex: 1,
    },
    stepNumber: {
        fontSize: 14,
        fontWeight: '600',
        color: theme.system.text,
        marginBottom: 4,
    },
    stepText: {
        fontSize: 15,
        color: theme.system.text,
        lineHeight: 22,
    },
    qrCodeLink: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
    },
    qrCodeLinkText: {
        fontSize: 14,
        fontWeight: '500',
    },
    qrCodeHint: {
        fontSize: 13,
        color: theme.system.secondaryText,
        marginTop: 4,
    },
    actionButtonContainer: {
        marginTop: 24,
        flexDirection: 'row',
        justifyContent: 'flex-end',
    },
    actionButton: {
        borderRadius: 12,
        borderWidth: 1.5,
        borderColor: theme.colors.error,
    },
    actionButtonLabel: {
        fontSize: 14,
        fontWeight: '600',
    },
    noImagePlaceholder: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.primaryContainer,
    },
});
export default EventManagementScreen;

