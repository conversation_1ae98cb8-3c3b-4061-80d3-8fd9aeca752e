/**
 * Common form validation rules and helpers for use with Ant Design Form
 */
import { t } from 'i18next';
import { validateHKID, formatHKID, removeHKIDFormat } from './idValidationUtils';

/**
 * Returns validation rules for required fields
 * @param {string} fieldName - The name of the field for error message
 * @returns {Array} - Array of validation rules
 */
export const getRequiredRule = (fieldName) => [
  { required: true, message: t('common.validation.required', { field: fieldName }) }
];

/**
 * Returns validation rules for email fields
 * @returns {Array} - Array of validation rules
 */
export const getEmailRules = () => [
  { required: true, message: t('common.validation.emailRequired') },
  { type: 'email', message: t('common.validation.emailFormat') }
];

/**
 * Returns validation rules for phone number fields
 * @returns {Array} - Array of validation rules
 */
export const getPhoneRules = () => [
  { required: true, message: t('common.validation.phoneRequired') },
  { pattern: /^[0-9]{8}$/, message: t('common.validation.phoneFormat') }
];

/**
 * Returns validation rules for username fields
 * @returns {Array} - Array of validation rules
 */
export const getUsernameRules = () => [
  { required: true, message: t('common.validation.usernameRequired') },
  { min: 4, message: t('common.validation.usernameMinLength') },
  { max: 12, message: t('common.validation.usernameMaxLength') },
  { pattern: /^[a-zA-Z0-9_-]+$/, message: t('common.validation.usernameFormat') }
];

/**
 * Returns validation rules for password fields
 * @returns {Array} - Array of validation rules
 */
export const getPasswordRules = () => [
  { required: true, message: t('common.validation.passwordRequired') },
  { min: 8, message: t('common.validation.passwordMinLength') },
  {
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    message: t('common.validation.passwordRequirements')
  }
];

/**
 * Returns validation rules for ID document fields based on type
 * @param {string} docType - The type of ID document (e.g., 'hkid', 'passport')
 * @returns {Array} - Array of validation rules
 */
export const getIdValidationRules = (docType) => {
  switch (docType) {
    case 'hkid':
      return [
        { required: true, message: t('common.validation.hkid') },
        {
          validator: async (_, value) => {
            if (!value) return Promise.resolve();
            const result = validateHKID(value);
            if (!result.valid) {
              return Promise.reject(new Error(result.message));
            }
            return Promise.resolve();
          }
        }
      ];
    case 'passport':
      return [
        { required: true, message: t('common.validation.passport') },
        {
          validator: async (_, value) => {
            if (!value || /^[A-Z0-9]{6,12}$/i.test(value)) {
              return Promise.resolve();
            }
            return Promise.reject(new Error(t('common.validation.passport')));
          }
        }
      ];
    case 'mainlandTravelPermit':
      return [
        { required: true, message: t('common.validation.mainlandTravelPermit') },
        {
          validator: async (_, value) => {
            if (!value) return Promise.resolve();
            
            // Check format: H/M followed by 8 digits for permanent number and 2 digits for reissue count
            const formatRegex = /^[HM]\d{8}\d{2}$/;
            if (!formatRegex.test(value)) {
              return Promise.reject(new Error(t('common.validation.mainlandTravelPermit')));
            }
            return Promise.resolve();
          }
        }
      ];
    default:
      return [{ required: true, message: t('common.validation.idNumber') }];
  }
};

/**
 * Handle ID document input change (specifically for HKID formatting)
 * @param {Event} e - The change event
 * @param {Object} form - The form instance
 * @param {string} selectedDocType - The selected document type
 */
export const handleIdDocChange = (e, form, selectedDocType) => {
  if (selectedDocType !== 'hkid') return;
  
  const currentValue = e.target.value;
  const nativeEvent = e.nativeEvent;
  
  if (nativeEvent.inputType === 'deleteContentBackward') {
    const unformatted = removeHKIDFormat(currentValue);
    form.setFieldsValue({ idNumber: unformatted });
    return;
  }

  const result = formatHKID(currentValue);
  if (result.success) {
    form.setFieldsValue({ idNumber: result.formattedID });
  }
}; 