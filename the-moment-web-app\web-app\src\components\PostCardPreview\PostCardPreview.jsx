import React, { useState } from 'react';
import { Card, Typography, Space, Tag } from 'antd';
import { EyeInvisibleOutlined, CalendarOutlined, UserOutlined, EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import fallbackImage from '../../assets/images/picture-loading-failed.svg';
import { formatSimpleDateTime } from '../../utils/dateFormatter'; // Import date formatter
import './PostCardPreview.css';

const { Title, Text } = Typography;

const PostCardPreview = ({ title, imageUrl, status, authorName, postDate }) => {
    const { t } = useTranslation();
    const [currentImageUrl, setCurrentImageUrl] = useState(imageUrl || fallbackImage);
    const [hasError, setHasError] = useState(false);

    React.useEffect(() => {
        setCurrentImageUrl(imageUrl || fallbackImage);
        setHasError(false); // Reset error state when imageUrl changes
    }, [imageUrl]);

    const onImageError = () => {
        if (!hasError) { // Prevent infinite loop if fallback also fails, though unlikely with local svg
            setCurrentImageUrl(fallbackImage);
            setHasError(true);
        }
    };

    const displayDate = postDate ? formatSimpleDateTime(postDate) : '';
    const isHidden = status === 'hidden';
    const isDraft = status === 'draft';

    return (
        <Card
            className={`post-card-preview ${isDraft ? 'draft-preview' : ''} ${isHidden ? 'hidden-preview' : ''}`}
            cover={
                <div className={`preview-image-container ${isHidden ? 'grayscale' : ''}`}>
                    <img
                        alt={title || t('createPost.preview.altTitle', 'Post Preview Cover')}
                        src={currentImageUrl}
                        className="preview-actual-img" // New class for direct styling
                        onError={onImageError}
                    />
                    {isHidden && (
                        <div className="preview-hidden-overlay">
                            <EyeInvisibleOutlined style={{ fontSize: '24px', color: 'rgba(0, 0, 0, 0.65)' }} />
                            <Text strong className="preview-hidden-text">{t('createPost.status.hidden', 'Hidden')}</Text>
                        </div>
                    )}
                    {isDraft && !isHidden && ( // Show draft indicator only if not also hidden
                        <div className="preview-draft-overlay">
                            <EditOutlined style={{ fontSize: '24px', color: 'rgba(0, 0, 0, 0.65)' }} />
                            <Text strong className="preview-draft-text">{t('createPost.status.draft', 'Draft')}</Text>
                        </div>
                    )}
                </div>
            }
        >
            <Card.Meta
                title={
                    <Title level={5} className="preview-title" ellipsis>
                        {title || t('createPost.preview.untitled', 'Untitled Post')}
                    </Title>
                }
                description={
                    <div className="preview-meta">
                        <Space direction="vertical" style={{ width: '100%' }}>
                            <div className="preview-meta-item">
                                <UserOutlined />
                                <Text className="truncate">{authorName || t('common.unknown')}</Text>
                            </div>
                            {displayDate && (
                                <div className="preview-meta-item">
                                    <CalendarOutlined />
                                    <Text className="truncate">{displayDate}</Text>
                                </div>
                            )}
                        </Space>
                    </div>
                }
            />
            {isDraft && (
                 <Tag color="blue" className="preview-status-tag draft-tag">
                    {t('createPost.status.draft', 'Draft')}
                </Tag>
            )}
            {isHidden && (
                <Tag color="gold" className="preview-status-tag hidden-tag">
                    {t('createPost.status.hidden', 'Hidden')}
                </Tag>
            )}
        </Card>
    );
};

export default PostCardPreview; 