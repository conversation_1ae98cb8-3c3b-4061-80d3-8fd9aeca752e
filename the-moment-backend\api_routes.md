# API Routes Documentation

This document outlines the available API routes, their purpose, request/response formats, and authentication requirements.

## Base URL

All API routes are prefixed with `/api/v1`.

## Authentication

Most routes require authentication via a JWT Bearer token in the `Authorization` header. Specific authentication methods (e.g., staff login, phone registration) are detailed below.

---

## 1. Authentication & Authorization

### 1.1. Phone Registration & Login

*   **POST** `/api/v1/authn/phone/check`
    *   Description: Checks if a given phone number is already registered in the system.
    *   Request Payload:
        *   `phone` (string, required): The user's phone number in E.164 format (e.g., "+85212345678").
    *   Response Payload:
        *   `exists` (boolean): `true` if the phone number is registered, `false` otherwise.
        *   `user` (object, optional): If the user exists, a subset of the user's data is returned.
            *   `id` (UUID): User's unique identifier.
            *   `display_name` (string): User's public display name.
            *   `profile_picture_url` (string, nullable): URL to the user's profile picture.
    *   Authentication: None

*   **POST** `/api/v1/authn/register/phone/initiate`
    *   Description: Initiates the phone number registration process for a new user by sending an OTP. This is the first step in a PKCE-secured flow.
    *   Request Payload:
        *   `phone` (string, required): The user's phone number in E.164 format.
        *   `client_id` (string, required): The identifier for the client application.
        *   `code_challenge` (string, required): The code challenge for PKCE flow, derived from the `code_verifier`.
        *   `code_challenge_method` (string, required): The hashing method used for the code challenge. Must be "S256".
        *   `state` (string, required): An opaque value used to maintain state between the request and callback, preventing CSRF attacks.
        *   `phone_otp_channel` (string, optional): Preferred channel for OTP delivery. Can be "whatsapp" or "sms". Defaults to "whatsapp".
    *   Response Payload:
        *   `state` (string): The state value, echoed back for client verification.
        *   `message` (string): A confirmation message (e.g., "Registration OTP initiated successfully").
        *   `flow_id` (string): The unique ID for the authentication flow.
    *   Authentication: None

*   **POST** `/api/v1/authn/register/phone/verify`
    *   Description: Verifies the OTP, completes the phone number registration, creates the user account, and issues authentication tokens.
    *   Request Payload:
        *   `state` (string, required): The state value from the `/initiate` request.
        *   `otp` (string, required): The One-Time Password received by the user.
        *   `code_verifier` (string, required): The original secret used to generate the `code_challenge` in the `/initiate` call.
        *   `display_name` (string, required): The user's desired display name.
        *   `interface_language` (string, optional): Preferred interface language (e.g., "en", "zh_HK").
        *   `communication_language` (string, optional): Preferred communication language (e.g., "en", "zh_HK").
        *   `phone_otp_channel` (string, optional): Preferred channel for future OTPs ("whatsapp" or "sms").
    *   Response Payload:
        *   `message` (string): A success message.
        *   `user_id` (UUID): The newly created user's unique ID.
        *   `access_token` (string): A short-lived JWT for authenticating subsequent API requests.
        *   `refresh_token` (string): A long-lived token used to obtain a new access token.
    *   Authentication: None

*   **POST** `/api/v1/authn/phone/otp/initiate`
    *   Description: Initiates a phone OTP login flow for an *existing* user.
    *   Request Payload: Same as `/register/phone/initiate`.
    *   Response Payload: Same as `/register/phone/initiate`.
    *   Authentication: None

*   **POST** `/api/v1/authn/phone/otp/verify`
    *   Description: Verifies a phone OTP for an existing user to log them in and get tokens.
    *   Request Payload:
        *   `state` (string, required): The state value from the `/initiate` request.
        *   `otp` (string, required): The One-Time Password.
        *   `code_verifier` (string, required): The PKCE code verifier.
    *   Response Payload:
        *   `message` (string): A success message.
        *   `user_id` (UUID): The logged-in user's unique ID.
        *   `access_token` (string): A short-lived JWT.
        *   `refresh_token` (string): A long-lived token.
    *   Authentication: None

### 1.2. Staff Login

*   **POST** `/api/v1/authn/staff/email/check`
    *   Description: Checks if a staff email exists in the system.
    *   Request Payload:
        *   `email` (string, required): The staff member's email address.
    *   Response Payload:
        *   `exists` (boolean): `true` if the email is registered to a staff member.
    *   Authentication: None

*   **POST** `/api/v1/authn/staff/login/initiate`
    *   Description: Initiates the login process for a staff member. This creates the auth flow state.
    *   Request Payload:
        *   `email` (string, required): The staff member's email address.
        *   `redirect_url` (string, optional): URL to redirect to after login (for future use).
        *   `client_id` (string, required): Identifier for the client application.
        *   `code_challenge` (string, required): The PKCE code challenge.
        *   `code_challenge_method` (string, required): Must be "S256".
        *   `state` (string, required): Opaque value to maintain state.
    *   Response Payload:
        *   `state` (string): The state value, echoed back.
        *   `flow_id` (string): The unique ID for the authentication flow.
    *   Authentication: None

*   **POST** `/api/v1/authn/staff/login/verify`
    *   Description: Verifies staff credentials (email and password) and issues authentication tokens.
    *   Request Payload:
        *   `state` (string, required): State value from the staff login initiation flow.
        *   `email` (string, required): Staff member's email address.
        *   `password` (string, required): Staff member's password.
        *   `code_verifier` (string, required): The PKCE code verifier.
    *   Response Payload:
        *   `access_token` (string): A short-lived JWT.
        *   `refresh_token` (string): A long-lived token.
        *   `access_token_expires_at` (timestamp): Expiration time of the access token.
        *   `refresh_token_expires_at` (timestamp): Expiration time of the refresh token.
        *   `user_id` (UUID): The staff member's user ID.
        *   `role` (string): The user's role (e.g., "admin", "superadmin").
    *   Authentication: None

### 1.3. Token Management

*   **POST** `/api/v1/authn/token/refresh`
    *   Description: Refreshes an expired access token using a valid refresh token.
    *   Request Payload:
        *   `refresh_token` (string, required): The refresh token issued during login.
    *   Response Payload: Same as staff login verify response.
    *   Authentication: None

*   **POST** `/api/v1/authn/logout`
    *   Description: Invalidates the provided refresh token, effectively logging the user out of that session.
    *   Request Payload:
        *   `refresh_token` (string, required): The refresh token to be invalidated.
    *   Response: `204 No Content` on success.
    *   Authentication: Bearer Token

---

## 2. User Profile Management

*   **GET** `/api/v1/users/me`
    *   Description: Retrieves the full profile of the currently authenticated user.
    *   Response Payload:
        *   `id` (UUID): User's unique identifier.
        *   `display_name` (string): User's public display name.
        *   `phone` (string, nullable): User's phone number.
        *   `email` (string, nullable): User's email address.
        *   `profile_picture_url` (string, nullable): URL to the user's profile picture.
        *   `role` (string): The user's role (e.g., "user", "admin").
        *   `notification_settings` (object):
            *   `enable_app_notifications` (boolean)
            *   `enable_whatsapp_notifications` (boolean)
            *   `enable_sms_notifications` (boolean)
            *   `enable_email_notifications` (boolean)
            *   `phone_otp_channel` (string)
        *   `language_preferences` (object):
            *   `interface_language` (string)
            *   `communication_language` (string)
    *   Authentication: Bearer Token

*   **PATCH** `/api/v1/users/me`
    *   Description: Updates parts of the currently authenticated user's profile.
    *   Request Payload:
        *   `display_name` (string, optional): The user's new display name.
        *   `profile_picture_url` (string, optional): URL for the user's new profile picture.
        *   `notification_settings` (object, optional):
            *   `enable_app_notifications` (boolean, optional)
            *   `enable_whatsapp_notifications` (boolean, optional)
            *   `enable_sms_notifications` (boolean, optional)
            *   `enable_email_notifications` (boolean, optional)
            *   `phone_otp_channel` (string, optional): "sms" or "whatsapp".
        *   `language_preferences` (object, optional):
            *   `interface_language` (string, optional): "en", "zh_HK", or "zh_CN".
            *   `communication_language` (string, optional): "en", "zh_HK", or "zh_CN".
    *   Response Payload: The updated User Profile object (same as `GET /api/v1/users/me`).
    *   Authentication: Bearer Token

*   **POST** `/api/v1/users/me/profile-picture`
    *   Description: Uploads a new profile picture for the user. This is a `multipart/form-data` request.
    *   Request Payload:
        *   `file` (file, required): The image file to upload.
    *   Response Payload:
        *   `url` (string): The publicly accessible URL of the uploaded profile picture.
    *   Authentication: Bearer Token

*   **POST** `/api/v1/users/me/phone/initiate-change`
    *   Description: Initiates a secure flow to change the user's phone number by sending an OTP to the new number.
    *   Request Payload:
        *   `new_phone_number` (string, required): The desired new phone number in E.164 format.
        *   `phone_otp_channel` (string, optional): Preferred OTP channel ("sms" or "whatsapp").
        *   `client_id` (string, required): Client identifier for the application.
        *   `state` (string, required): Client-generated state value for correlating requests.
    *   Response Payload:
        *   `state` (string): The state value, echoed back.
        *   `flow_id` (string): The unique ID for the phone change flow.
        *   `message` (string): Confirmation message.
        *   `otp_channel` (string): The channel the OTP was sent to.
        *   `expires_in_sec` (integer): The number of seconds the flow is valid for.
    *   Authentication: Bearer Token

*   **POST** `/api/v1/users/me/phone/verify-change`
    *   Description: Verifies the OTP to complete the phone number change process.
    *   Request Payload:
        *   `state` (string, required): The state value from the initiation request.
        *   `otp` (string, required): The 6-digit numeric One-Time Password.
        *   `new_phone_number` (string, required): The new phone number (must match the one from initiation).
    *   Response Payload:
        *   `message` (string): A success message.
        *   `user` (object): The updated User Profile object.
    *   Authentication: Bearer Token

*   **POST** `/api/v1/users/me/reauth/otp/initiate`
    *   Description: Initiates a re-authentication OTP flow required for sensitive actions. Sends an OTP to the user's registered OTP channel.
    *   Request Payload:
        *   `purpose` (string, required): The reason for the re-authentication request (e.g., "delete_account").
    *   Response Payload:
        *   `message` (string): A confirmation message.
    *   Authentication: Bearer Token

*   **POST** `/api/v1/users/me/reauth/otp/verify`
    *   Description: Verifies the re-authentication OTP to authorize a sensitive action.
    *   Request Payload:
        *   `purpose` (string, required): The purpose must match the one from the initiation.
        *   `otp` (string, required): The One-Time Password.
    *   Response Payload:
        *   `message` (string): A success message.
        *   `reauth_token` (string): A short-lived token that can be used once to perform the sensitive action.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/uuid`
    *   Description: Retrieves the UUID of the currently authenticated user.
    *   Response Payload: `{"uuid": "user-uuid-string"}`
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/stats`
    *   Description: Retrieves activity and engagement statistics for the currently authenticated user.
    *   Response Payload:
        *   `total_events_registered` (integer): Total number of events the user has registered for.
        *   `total_events_attended` (integer): Total number of events the user has attended.
        *   `total_volunteer_hours` (float): Total number of hours the user has contributed as a volunteer.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/organizations`
    *   Description: Lists all organizations the current user is a member of.
    *   Response Payload: An array of organization objects.
        *   `organization_id` (UUID): The organization's unique ID.
        *   `name` (string): The organization's name.
        *   `image_url` (string, nullable): URL of the organization's logo.
        *   `role` (string): The user's role within that organization.
    *   Authentication: Bearer Token

---

## 3. User Verification

*   **GET** `/api/v1/verification-types`
    *   Description: Retrieves a list of all available verification types that users can apply for.
    *   Response Payload: An array of verification type objects.
        *   `key` (string): The unique identifier for the verification type (e.g., "hk_id_card").
        *   `name` (string): The display name of the verification type (e.g., "Hong Kong ID Card").
        *   `description` (string): A brief description of the verification type.
    *   Authentication: None

*   **POST** `/api/v1/users/me/verifications`
    *   Description: Submits a verification request for the currently authenticated user. This is a `multipart/form-data` request.
    *   Request Payload:
        *   `data` (json string, required): A JSON object containing `verification_type`.
            *   Example: `{"verification_type": "id_card"}`
        *   `file` (file, required): The document or image for verification.
    *   Response Payload: The created User Verification Request object.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/verifications`
    *   Description: Retrieves a list of all verification requests submitted by the current user.
    *   Response Payload: An array of verification request objects.
        *   `id` (UUID): The unique ID of the verification request.
        *   `verification_type` (string): The type of the verification.
        *   `status` (string): The current status (e.g., "pending", "approved", "rejected").
        *   `submitted_at` (timestamp): When the request was submitted.
        *   `admin_notes` (string, nullable): Notes from the reviewing admin.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/verifications/:reqID`
    *   Description: Retrieves the full details for a specific verification request, including all submitted data.
    *   Response Payload: A detailed verification request object, including specific fields for the verification type.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/verifications/documents/:docID`
    *   Description: Downloads a document file associated with one of the user's verification requests.
    *   Response: The raw file content.
    *   Authentication: Bearer Token

*   **DELETE** `/api/v1/users/me/verifications/:reqID`
    *   Description: Allows a user to delete their own verification data. This is a soft delete.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token

---

## 4. Organization Management

### 4.1. Public & General Routes

*   **GET** `/api/v1/organizations`
    *   Description: Lists all public organizations. Supports pagination (`?page=1&limit=20`).
    *   Query Parameters: `limit`, `offset`.
    *   Response Payload: An array of organization objects.
        *   `id` (UUID): The organization's unique ID.
        *   `name` (string): The organization's name.
        *   `description` (string, nullable): The organization's description.
        *   `image_url` (string, nullable): URL of the organization's logo.
        *   `theme_color` (string, nullable): The organization's theme color.
        *   `is_default_org` (boolean): Whether this is the default organization.
    *   Authentication: None

*   **GET** `/api/v1/organizations/:orgId`
    *   Description: Retrieves public details for a specific organization.
    *   Response Payload: A single organization object (same structure as the list response).
    *   Authentication: Bearer Token (required to view details)

### 4.2. Authenticated User Routes

*   **POST** `/api/v1/organizations`
    *   Description: Allows an authenticated user to create a new organization. The user who creates it becomes the owner.
    *   Request Payload:
        *   `name` (string, required): The name of the organization.
        *   `description` (string, optional): A description for the organization.
    *   Response Payload: The newly created organization object.
    *   Authentication: Bearer Token

*   **POST** `/api/v1/organizations/:orgId/join`
    *   Description: Allows a user to request to join an organization.
    *   Response: `200 OK` with a success message.
    *   Authentication: Bearer Token

*   **DELETE** `/api/v1/organizations/:orgId/leave`
    *   Description: Allows a user to leave an organization they are a member of.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token

### 4.3. Organization Admin Routes

*   **PUT** `/api/v1/organizations/:orgId`
    *   Description: Updates an organization's details. Requires the user to be an admin of the organization.
    *   Request Payload:
        *   `name` (string, optional): The new name for the organization.
        *   `description` (string, optional): The new description.
        *   `image_url` (string, optional): New URL for the organization's logo.
        *   `theme_color` (string, optional): New theme color (e.g., hex code).
    *   Response Payload: The updated organization object.
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId`
    *   Description: Deletes an organization. Requires the user to be an admin of the organization.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token (Admin Role Required)

*   **POST** `/api/v1/organizations/:orgId/logo`
    *   Description: Uploads a logo for the organization. This is a `multipart/form-data` request. Requires admin role.
    *   Request Payload:
        *   `file` (file, required): The image file for the logo.
    *   Response Payload:
        *   `url` (string): The publicly accessible URL of the uploaded logo.
    *   Authentication: Bearer Token (Admin Role Required)

---

## 5. Event Management

### 5.1. Public Event Routes

*   **GET** `/api/v1/events`
    *   Description: Lists all publicly visible events across all organizations. Supports filtering and pagination.
    *   Query Parameters: `limit`, `offset`, `org_id`, `search_term`, `start_date`, `end_date`, `tag_ids`, `status`, etc.
    *   Response Payload: An array of public event objects.
        *   Each object contains key details like `id`, `title`, `organization_name`, `start_time`, `end_time`, `location`, `banner_url`, etc.
    *   Authentication: Optional (provides a customized response if authenticated)

*   **GET** `/api/v1/events/:eventId`
    *   Description: Retrieves the full public details for a specific event.
    *   Response Payload: A single detailed public event object.
    *   Authentication: Optional

*   **GET** `/api/v1/events/:eventId/volunteer-count`
    *   Description: Gets the number of approved volunteer applications for a specific event.
    *   Response Payload: `{"count": 25}`
    *   Authentication: None

*   **GET** `/api/v1/event-tags`
    *   Description: Lists all unique, globally approved event tags that can be used for filtering.
    *   Response Payload: An array of tag objects `{"id": "uuid", "name": "Tag Name"}`.
    *   Authentication: None

*   **PATCH** `/api/v1/event-tags/:tagId`
    *   Description: (Admin) Updates an existing event tag.
    *   Request Payload:
        *   `name_en` (string, optional)
        *   `name_zh_hk` (string, optional)
        *   `name_zh_cn` (string, optional)
        *   `is_globally_approved` (boolean, optional)
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/event-statistics`
    *   Description: Retrieves aggregated, public statistics for all events on the platform.
    *   Response Payload: `{"total_events": 100, "total_participants": 5000}`
    *   Authentication: None

### 5.2. Organization Event Routes (Org Admin)

*   **GET** `/api/v1/organizations/:orgId/events`
    *   Description: Lists all events for a specific organization, including drafts and hidden events for admins.
    *   Authentication: Optional JWT (provides admin view with more details)

*   **POST** `/api/v1/organizations/:orgId/events`
    *   Description: Creates a new event for an organization.
    *   Request Payload: A comprehensive JSON object including `title`, `description_content`, `location_type`, `start_time`, `end_time`, `participant_limit`, `status`, etc.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/organizations/:orgId/events/:eventId`
    *   Description: Retrieves full details for a specific organization event, including non-public information.
    *   Authentication: Optional JWT

*   **PUT** `/api/v1/organizations/:orgId/events/:eventId`
    *   Description: Performs a full update of an event's details. All fields must be provided.
    *   Authentication: Bearer Token (Admin Role Required)

*   **PATCH** `/api/v1/organizations/:orgId/events/:eventId`
    *   Description: Partially updates an event's details. Only include the fields to be changed.
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/events/:eventId`
    *   Description: Deletes an event.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/organizations/:orgId/events/:eventId/statistics`
    *   **Description**: Retrieves detailed statistics for a specific event within an organization. This endpoint provides a comprehensive overview of participant and volunteer demographics and engagement over time.
    *   **URL Parameters**:
        *   `orgId` (UUID, required): The unique identifier of the organization.
        *   `eventId` (UUID, required): The unique identifier of the event.
    *   **Authentication**:
        *   Requires a valid Bearer Token.
        *   The authenticated user must be a member of the organization specified by `orgId`.
    *   **Success Response (200 OK)**:
        *   **Content**: `SpecificEventStatisticsResponse` object.
        *   **Payload Breakdown**:
            *   `participants_count` (integer): Total number of users registered for the event.
            *   `volunteers_count` (integer): Total number of users approved as volunteers for the event.
            *   `max_participants` (integer): The maximum number of participants allowed for the event.
            *   `participants_by_age` (array of objects): A list of age brackets and the count of participants in each.
                *   `age_range` (string): The age group label (e.g., "0-17", "18-24", "25-34").
                *   `count` (integer): The number of participants within that age range.
            *   `volunteers_by_age` (array of objects): A list of age brackets and the count of volunteers in each.
                *   `age_range` (string): The age group label.
                *   `count` (integer): The number of volunteers within that age range.
            *   `participants_by_gender` (array of objects): A breakdown of participants by gender.
                *   `gender` (string): The gender ("Male", "Female", "Other", "Prefer not to say").
                *   `count` (integer): The number of participants of that gender.
            *   `volunteers_by_gender` (array of objects): A breakdown of volunteers by gender.
                *   `gender` (string): The gender.
                *   `count` (integer): The number of volunteers of that gender.
            *   `participants_by_date` (array of objects): A daily count of participant registrations leading up to the event.
                *   `date` (string, `YYYY-MM-DD`): The specific date of registration.
                *   `count` (integer): The number of users who registered on that date.
            *   `volunteers_by_date` (array of objects): A daily count of volunteer applications.
                *   `date` (string, `YYYY-MM-DD`): The date of application.
                *   `count` (integer): The number of users who applied to be a volunteer on that date.
    *   **Error Responses**:
        *   `401 Unauthorized`: If the request lacks a valid authentication token.
        *   `403 Forbidden`: If the authenticated user is not a member of the organization.
        *   `404 Not Found`: If the specified `orgId` or `eventId` does not exist.
        *   `500 Internal Server Error`: If there is a server-side error while fetching the statistics.

### 5.3. Event Media (Org Admin)

*   **GET** `/api/v1/organizations/:orgId/events/:eventId/media`
    *   Description: Lists all media items (images, videos) associated with an event.
    *   Authentication: Bearer Token

*   **POST** `/api/v1/organizations/:orgId/events/:eventId/media`
    *   Description: Adds a new media item to an event. `multipart/form-data` request.
    *   Request Payload: `file` (the media file).
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/events/:eventId/media/:itemId`
    *   Description: Deletes a specific media item from an event.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token (Admin Role Required)

*   **PATCH** `/api/v1/organizations/:orgId/events/:eventId/media/:mediaItemId/set-banner`
    *   Description: Designates a specific media item as the event's main banner image.
    *   Response: `200 OK`
    *   Authentication: Bearer Token (Admin Role Required)

### 5.4. Event Tags (Org Admin)

*   **GET** `/api/v1/organizations/:orgId/events/:eventId/tags`
    *   Description: Lists all tags associated with a specific event.
    *   Authentication: None

*   **POST** `/api/v1/organizations/:orgId/events/:eventId/tags/:tagId`
    *   Description: Associates an existing tag with an event.
    *   Response: `200 OK`
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/events/:eventId/tags/:tagId`
    *   Description: Removes a tag from an event.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token (Admin Role Required)

### 5.5. Event Verification Types (Org Admin)

*   **GET** `/api/v1/organizations/:orgId/events/:eventId/verification-types`
    *   Description: Lists the verification types that are required for participants of a specific event.
    *   Authentication: None

*   **POST** `/api/v1/organizations/:orgId/events/:eventId/verification-types/:typeKey`
    *   Description: Adds a required verification type to an event.
    *   Response: `200 OK`
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/events/:eventId/verification-types/:typeKey`
    *   Description: Removes a required verification type from an event.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token (Admin Role Required)

### 5.6. Event Registration & Volunteering

*   **GET** `/api/v1/events/:eventId/registrations`
    *   Description: Lists registrations for an event. The level of detail depends on the user's role (user sees their own, admin sees all).
    *   Authentication: Bearer Token

*   **POST** `/api/v1/events/:eventId/volunteer-applications`
    *   Description: Allows an authenticated user to apply to volunteer for a specific event.
    *   Request Payload:
        *   `application_notes_by_user` (string, optional): Notes or motivation from the user.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/organizations/:orgId/events/:eventId/registrations`
    *   Description: Provides an admin view of all event registrations for a specific event.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/organizations/:orgId/events/statistics`
    *   Description: Retrieves detailed statistics for all events within an organization.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/users/me/event-statistics`
    *   Description: Retrieves the current user's personal event-related statistics.
    *   Authentication: Bearer Token

---

## 6. Volunteer Management

### 6.1. User Routes

*   **GET** `/api/v1/users/me/volunteer/applications`
    *   Description: Lists all of the current user's general volunteer qualification applications submitted to various organizations.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/volunteer/applications/:appId`
    *   Description: Gets the details of a specific general volunteer application submitted by the user.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/volunteer/qualifications`
    *   Description: Lists all of the user's approved volunteer qualifications across all organizations.
    *   Authentication: Bearer Token

*   **POST** `/api/v1/organizations/:orgId/volunteer/apply`
    *   Description: User applies to become a qualified volunteer for a specific organization. This is for a general qualification, not for a single event.
    *   Request Payload:
        *   `motivation` (string, optional): A message explaining the user's motivation to volunteer.
    *   Response Payload: The newly created application object.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/organizations/:orgId/volunteer/status`
    *   Description: Gets the user's current volunteer qualification status for a specific organization.
    *   Response Payload:
        *   `status` (string): e.g., "not_applied", "pending", "approved", "rejected".
    *   Authentication: Bearer Token

*   **POST** `/api/v1/volunteer/applications/:appId/withdraw`
    *   Description: Allows a user to withdraw their general volunteer qualification application from an organization.
    *   Response: `200 OK`
    *   Authentication: Bearer Token

*   **PATCH** `/api/v1/event/applications/:appId/withdraw`
    *   Description: Allows a user to withdraw their volunteer application from a *specific event*.
    *   Response: `200 OK`
    *   Authentication: Bearer Token

### 6.2. Event-Specific Volunteer Routes

*   **GET** `/api/v1/users/me/event-volunteer-applications`
    *   Description: Lists all of the user's applications to volunteer for specific events.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/event-volunteer-applications/:appId`
    *   Description: Gets the details of a user's specific application to volunteer for an event.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/organizations/:orgId/events/:eventId/volunteer-applications`
    *   Description: (Admin) Lists all volunteer applications for a specific event.
    *   Authentication: Bearer Token (Admin Role Required)

*   **PATCH** `/api/v1/organizations/:orgId/events/:eventId/volunteer-applications/:applicationId/review`
    *   Description: (Admin) Reviews a specific event volunteer application (approve/reject).
    *   Request Payload:
        *   `status` (string, required): The new status, either "approved" or "rejected".
        *   `admin_review_notes` (string, optional): Notes from the admin regarding the decision.
    *   Response: `200 OK`
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/organizations/:orgId/events/:eventId/volunteer-applications/:appId`
    *   Description: Get details for a specific volunteer application for an event. Can be accessed by the applicant or an org admin.
    *   Authentication: Bearer Token

---

## 7. Event Registration Management

### 7.1. User Routes

*   **GET** `/api/v1/users/me/event-registrations`
    *   Description: Lists all of the current user's event registrations.
    *   Response Payload: An array of registration objects, each including event details and the user's registration status.
    *   Authentication: Bearer Token

*   **POST** `/api/v1/users/me/event-registrations/events/:eventId/register`
    *   Description: Registers the currently authenticated user for a specific event. The outcome depends on event capacity and settings (e.g., direct registration, waitlisting).
    *   Response Payload:
        *   `status` (string): The resulting status of the registration (e.g., "registered", "waitlisted").
        *   `message` (string): A confirmation message.
    *   Authentication: Bearer Token

*   **GET** `/api/v1/users/me/event-registrations/:registrationId`
    *   Description: Get the details of a specific event registration for the current user.
    *   Response Payload: A detailed registration object.
    *   Authentication: Bearer Token

*   **PATCH** `/api/v1/users/me/event-registrations/:registrationId/cancel`
    *   Description: Cancels the user's own registration for an event.
    *   Response Payload:
        *   `status` (string): The new status ("cancelled_by_user").
        *   `message` (string): A confirmation message.
    *   Authentication: Bearer Token

### 7.2. Organization Admin Routes

*   **GET** `/api/v1/organizations/:orgId/event-registrations`
    *   Description: (Admin) Lists all event registrations for all events within an organization.
    *   Authentication: Bearer Token (Admin Role Required)

*   **PATCH** `/api/v1/organizations/:orgId/event-registrations/:registrationId/status`
    *   Description: (Admin) Updates the status of a specific event registration (e.g., approve a pending registration, mark as attended).
    *   Request Payload:
        *   `status` (string, required): The new status for the registration (e.g., "registered", "attended", "absent").
    *   Response Payload: The updated registration object.
    *   Authentication: Bearer Token (Admin Role Required)

---

## 8. Content Management (Posts)

### 8.1. Public Post Routes

*   **GET** `/api/v1/posts`
    *   Description: Lists all publicly published posts.
    *   Authentication: None

*   **GET** `/api/v1/posts/:postIdOrSlug`
    *   Description: Retrieves a single public post by its UUID or URL-friendly slug.
    *   Authentication: None

### 8.2. Organization Post Routes (Org Admin)

*   **GET** `/api/v1/organizations/:orgId/posts`
    *   Description: Lists all posts for an organization. For admins, this includes drafts and scheduled posts.
    *   Authentication: Optional JWT

*   **POST** `/api/v1/organizations/:orgId/posts`
    *   Description: Creates a new post for an organization.
    *   Request Payload:
        *   `title` (string, required): The title of the post.
        *   `content` (json, required): The body of the post, often in a rich text format like TipTap JSON.
        *   `status` (string, required): "draft" or "published".
        *   `slug` (string, optional): A URL-friendly slug. If omitted, one is generated from the title.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/organizations/:orgId/posts/:postId`
    *   Description: Retrieves a specific post from an organization, including non-public data for admins.
    *   Authentication: Optional JWT

*   **PUT** `/api/v1/organizations/:orgId/posts/:postId`
    *   Description: Updates a post's content and properties.
    *   Request Payload: Same as POST request.
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/posts/:postId`
    *   Description: Deletes a post.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token (Admin Role Required)

### 8.3. Post Media and Tags (Org Admin)

*   **POST** `/api/v1/organizations/:orgId/posts/:postId/media`
    *   Description: Adds a media item (e.g., image) to a post. `multipart/form-data` request.
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/posts/:postId/media/:mediaItemId`
    *   Description: Deletes a media item from a post.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token (Admin Role Required)

*   **PUT** `/api/v1/organizations/:orgId/posts/:postId/media/:mediaItemId/banner`
    *   Description: Sets a specific media item as the post's banner image.
    *   Response: `200 OK`
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/organizations/:orgId/posts/:postId/tags`
    *   Description: Lists all tags associated with a specific post.
    *   Authentication: None

*   **POST** `/api/v1/organizations/:orgId/posts/:postId/tags/:tagId`
    *   Description: Adds an existing tag to a post.
    *   Response: `200 OK`
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/posts/:postId/tags/:tagId`
    *   Description: Removes a tag from a post.
    *   Response: `204 No Content`
    *   Authentication: Bearer Token (Admin Role Required)

### 8.4. Post Tag Management (Superadmin)

*   **GET** `/api/v1/post-tags`
    *   Description: Lists all available post tags in the system.
    *   Authentication: None

*   **POST** `/api/v1/post-tags`
    *   Description: (Superadmin) Creates a new globally available post tag.
    *   Request Payload:
        *   `tag_name` (string, required): The name of the tag.
        *   `language_code` (string, required): e.g., "en", "zh_HK".
        *   `description` (string, optional): Description of the tag.
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **GET** `/api/v1/post-tags/:tagId`
    *   Description: Retrieves a specific post tag by its ID.
    *   Authentication: None

*   **PUT** `/api/v1/post-tags/:tagId`
    *   Description: (Superadmin) Updates an existing post tag.
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **DELETE** `/api/v1/post-tags/:tagId`
    *   Description: (Superadmin) Deletes a post tag from the system.
    *   Authentication: Bearer Token (Superadmin Role Required)

---

## 9. Content Management (Resources & Files)

### 9.1. Public Resource Routes

*   **GET** `/api/v1/resources`
    *   Description: Lists all public resources.
    *   Authentication: None

*   **GET** `/api/v1/organizations/:orgId/resources/:resourceIdOrSlug/public`
    *   Description: Retrieves a single public resource by its slug or ID for a specific organization.
    *   Authentication: None

*   **GET** `/api/v1/resources/download/:orgId/:resourceIdOrSlug/:fileIdOrName`
    *   Description: Downloads a specific file that is part of a public resource.
    *   Authentication: None

### 9.2. Organization Resource Routes (Org Admin)

*   **GET** `/api/v1/organizations/:orgId/resources`
    *   Description: Lists all resources for an organization, including drafts.
    *   Authentication: Bearer Token (Admin Role Required)

*   **POST** `/api/v1/organizations/:orgId/resources`
    *   Description: Creates a new resource.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/organizations/:orgId/resources/:resourceId`
    *   Description: Retrieves a specific resource, with full details for admins.
    *   Authentication: Optional JWT

*   **PUT** `/api/v1/organizations/:orgId/resources/:resourceId`
    *   Description: Updates a resource's properties.
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/resources/:resourceId`
    *   Description: Deletes a resource.
    *   Authentication: Bearer Token (Admin Role Required)

### 9.3. Resource File Management (Org Admin)

*   **POST** `/api/v1/organizations/:orgId/resources/:resourceId/files`
    *   Description: Uploads a file and attaches it to a specific resource. `multipart/form-data` request.
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/resources/:resourceId/files/:fileId`
    *   Description: Deletes a file from a resource.
    *   Authentication: Bearer Token (Admin Role Required)

### 9.4. Organization File Management (Org Admin)

*   **GET** `/api/v1/organizations/:orgId/files`
    *   Description: Lists all files and folders for an organization's internal file manager.
    *   Authentication: Bearer Token (Admin Role Required)

*   **POST** `/api/v1/organizations/:orgId/files/folder`
    *   Description: Creates a new folder in the file manager.
    *   Authentication: Bearer Token (Admin Role Required)

*   **POST** `/api/v1/organizations/:orgId/files/upload`
    *   Description: Uploads a file to the organization's general file storage. `multipart/form-data` request.
    *   Authentication: Bearer Token (Admin Role Required)

*   **PUT** `/api/v1/organizations/:orgId/files/:fileOrFolderId`
    *   Description: Updates (renames or moves) a file or folder.
    *   Authentication: Bearer Token (Admin Role Required)

*   **DELETE** `/api/v1/organizations/:orgId/files/:fileOrFolderId`
    *   Description: Deletes a file or an empty folder.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/organizations/:orgId/files/:fileId/download`
    *   Description: Downloads a file directly from the organization's file storage.
    *   Authentication: Bearer Token (Admin Role Required)

---

## 10. Admin Routes

### 10.1. User Management (Superadmin)

*   **GET** `/api/v1/admin/users`
    *   Description: (Superadmin) Lists all users in the system, with pagination.
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **POST** `/api/v1/admin/users`
    *   Description: (Superadmin) Creates a new user with a specific role (e.g., creating an admin account).
    *   Request Payload:
        *   `email` (string, required): User's email.
        *   `password` (string, required): User's password.
        *   `display_name` (string, required): User's display name.
        *   `role` (string, required): "admin" or "superadmin".
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **GET** `/api/v1/admin/users/:userId`
    *   Description: (Superadmin) Retrieves full details for a specific user.
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **PUT** `/api/v1/admin/users/:userId`
    *   Description: (Superadmin) Updates a user's details, including their role.
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **DELETE** `/api/v1/admin/users/:userId`
    *   Description: (Superadmin) Deletes a user from the system.
    *   Authentication: Bearer Token (Superadmin Role Required)

### 10.2. Organization Manager (Superadmin)

*   **POST** `/api/v1/admin/users/organizations/:organizationId/manager`
    *   Description: (Superadmin) Assigns an existing user as a manager (admin) for an organization.
    *   Request Payload: `{"user_id": "uuid-of-user-to-promote"}`
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **DELETE** `/api/v1/admin/users/organizations/:organizationId/manager`
    *   Description: (Superadmin) Removes a manager from an organization.
    *   Request Payload: `{"user_id": "uuid-of-user-to-demote"}`
    *   Authentication: Bearer Token (Superadmin Role Required)

### 10.3. Verification Management (Superadmin)

*   **GET** `/api/v1/admin/verifications`
    *   Description: (Superadmin) Lists all user verification requests across the system.
    *   Request Payload: `{"user_id": "uuid-of-user"}`
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **GET** `/api/v1/admin/verifications/:reqID`
    *   Description: (Superadmin) Retrieves the full details of a specific verification request for review.
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **POST** `/api/v1/admin/verifications/:reqID/review`
    *   Description: (Superadmin) Reviews a verification request (approve/reject). Also supports `PATCH`.
    *   Request Payload:
        *   `status` (string, required): "approved" or "rejected".
        *   `admin_notes` (string, optional): Notes for the user about the decision.
    *   Authentication: Bearer Token (Superadmin Role Required)

*   **GET** `/api/v1/admin/verifications/documents/:docID`
    *   Description: (Superadmin) Securely downloads a document associated with a verification request for review.
    *   Authentication: Bearer Token (Superadmin Role Required)

### 10.4. Volunteer Application Management (Org Admin)

*   **GET** `/api/v1/admin/organizations/:orgId/volunteer/applications`
    *   Description: (Admin) Lists all general volunteer qualification applications for a specific organization.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/admin/organizations/:orgId/volunteer/applications/pending`
    *   Description: (Admin) Lists only the pending volunteer applications for an organization.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/admin/organizations/:orgId/volunteer/applications/:appId`
    *   Description: (Admin) Retrieves details of a specific general volunteer application for review.
    *   Authentication: Bearer Token (Admin Role Required)

*   **PATCH** `/api/v1/admin/organizations/:orgId/volunteer/applications/:appId/review`
    *   Description: (Admin) Reviews a general volunteer qualification application (approve/reject).
    *   Request Payload:
        *   `status` (string, required): "approved" or "rejected".
        *   `admin_notes` (string, optional): Notes regarding the decision.
    *   Authentication: Bearer Token (Admin Role Required)

*   **GET** `/api/v1/admin/organizations/:orgId/event-volunteer-applications`
    *   Description: (Admin) Lists all volunteer applications for all events within a specific organization.
    *   Authentication: Bearer Token (Admin Role Required)

### 10.5. Admin Profile

*   **GET** `/api/v1/admin/me/organizations`
    *   Description: Retrieves the organizations managed by the currently authenticated admin.
    *   Authentication: Bearer Token (Admin Role Required)

---

## 11. Miscellaneous

*   **GET** `/health`
    *   Description: Performs a health check of the API server and its database connection.
    *   Response Payload:
        *   `status` (string): "ok" or "error".
        *   `database` (string): "connected" or an error message.
    *   Authentication: None

*   **GET** `/api/v1/swagger.yaml`
    *   Description: Serves the OpenAPI specification file in YAML format. This file is auto-generated and provides a machine-readable definition of the API.
    *   Authentication: None

*   **GET** `/api/v1/docs/*`
    *   Description: Serves the interactive Swagger UI for API documentation and testing.
    *   Authentication: None

*   **GET** `/api/v1` (WebSocket)
    *   Description: Establishes a WebSocket connection for real-time notifications. The connection must be authenticated by passing the Bearer token as a query parameter (`?token=...`).
    *   Authentication: Bearer Token (via query parameter)

*   **POST** `/api/v1/volunteers/users/verifications`
    *   Description: Submits a verification request on behalf of a user. This is a `multipart/form-data` request.
    *   Request Payload:
        *   `data` (json string, required): A JSON object containing `user_id` and `verification_type`.
            *   Example: `{"user_id": "user-uuid-string", "verification_type": "id_card"}`
        *   `file`