

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    display_name varchar(255) NOT NULL,
    hashed_password varchar(255) DEFAULT NULL,
    profile_picture_url text DEFAULT NULL,
    phone varchar(50) UNIQUE DEFAULT NULL,
    phone_verified_at timestamptz DEFAULT NULL,
    email varchar(255) UNIQUE DEFAULT NULL,
    email_verified_at timestamptz DEFAULT NULL,
    phone_otp_channel varchar(20) NOT NULL DEFAULT 'whatsapp',
    interface_language varchar(10) NOT NULL DEFAULT 'en',
    communication_language varchar(10) NOT NULL DEFAULT 'en',
    enable_app_notifications BOOLEAN NOT NULL DEFAULT TRUE,
    enable_whatsapp_notifications BOOLEAN NOT NULL DEFAULT TRUE,
    enable_sms_notifications BOOLEAN NOT NULL DEFAULT FALSE,
    enable_email_notifications BOOLEAN NOT NULL DEFAULT FALSE,
    is_staff BOOLEAN NOT NULL DEFAULT FALSE,
    created_at timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create auth_flows table for PKCE-based authentication
CREATE TABLE IF NOT EXISTS auth_flows (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    flow_type varchar(20) NOT NULL, -- 'phone_otp' or 'email_password'
    code_verifier varchar(128),
    code_challenge varchar(128) NOT NULL,
    code_challenge_method varchar(20) NOT NULL DEFAULT 'S256',
    state varchar(64) NOT NULL UNIQUE,
    client_id varchar(64) NOT NULL,
    redirect_uri text NOT NULL,
    phone varchar(50), -- For phone OTP flow
    email varchar(255), -- For email/password flow
    otp_sid varchar(255), -- Twilio Verify service SID for OTP verification
    expires_at timestamptz NOT NULL,
    updated_at timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_flow_type CHECK (flow_type IN ('phone_otp', 'email_password', 'phone_registration')),
    CONSTRAINT chk_flow_data CHECK (
        ((flow_type = 'phone_otp' OR flow_type = 'phone_registration') AND phone IS NOT NULL AND email IS NULL) OR
        (flow_type = 'email_password' AND email IS NOT NULL AND phone IS NULL)
    )
);

-- Create otp_attempts table for rate limiting
CREATE TABLE IF NOT EXISTS otp_attempts (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    phone varchar(50) NOT NULL,
    attempt_count int NOT NULL DEFAULT 1,
    last_attempt_at timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    locked_until timestamptz,
    created_at timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_phone FOREIGN KEY (phone) REFERENCES users(phone) ON DELETE CASCADE
);

-- Create refresh_tokens table
CREATE TABLE IF NOT EXISTS refresh_tokens (
  id UUID PRIMARY KEY DEFAULT (gen_random_uuid()),
  user_id UUID NOT NULL,
  token_hash TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT (now()),
  CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Trigger function to update updated_at columns automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply the trigger to all tables with updated_at
DO $$
DECLARE
    t_name TEXT;
BEGIN
    FOR t_name IN
        SELECT table_name
        FROM information_schema.columns
        WHERE column_name = 'updated_at' AND table_schema = 'public'
        AND table_name IN (
            'users', 'auth_flows'
        )
    LOOP
        EXECUTE format('CREATE OR REPLACE TRIGGER update_%I_updated_at
                     BEFORE UPDATE ON %I
                     FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();', t_name, t_name);
    END LOOP;
END;
$$;
