package tagutils

import (
	"Membership-SAAS-System-Backend/db" // For db.EventTag type
	"github.com/google/uuid"
	"strings" // Or other UUID manipulation if needed
)

// Predefined prefixes or parts of UUIDs that identify language variants for a concept.
// Example: "e1e1e1e1-" is common, then "0001" for concept 1, "1001" for concept 1 in lang 1, etc.
// This needs to precisely match the observed pattern from seed data:
// EN: e1e1e1e1-0xxx-...
// ZH_HK: e1e1e1e1-1xxx-...
// ZH_CN: e1e1e1e1-2xxx-...
// The 'xxx' part represents the core concept ID.

const (
	uuidConceptPrefix = "e1e1e1e1-" // Common prefix for these deterministic UUIDs
	// Assuming the 5th segment (index 4 after splitting by '-') determines lang/concept
	// And the first char of that segment: '0' for EN, '1' for ZH_HK, '2' for ZH_CN
)

// GetEquivalentTagIDsByPattern identifies other tag UUIDs that belong to the same translation concept
// based on a predefined UUID pattern.
// allSystemTags: A pre-loaded slice of all event tags in the system.
func GetEquivalentTagIDsByPattern(sourceTagID uuid.UUID, allSystemTags []db.EventTag) []uuid.UUID {
	equivalents := make(map[uuid.UUID]struct{})
	sourceTagIDStr := sourceTagID.String()

	// Extract the 'concept identifier' part from the sourceTagID.
	// Example: from "e1e1e1e1-0001-4000-8000-000000000001", we want to isolate "001"
	// or ensure the "e1e1e1e1-" and "-4000-8000-000000000001" parts are constant for a concept group.
	parts := strings.Split(sourceTagIDStr, "-")
	if len(parts) != 5 || !strings.HasPrefix(sourceTagIDStr, uuidConceptPrefix) {
		// Not a tag following the expected pattern, return only itself initially
		equivalents[sourceTagID] = struct{}{}
		return mapToSlice(equivalents)
	}

	// The key part seems to be parts[1], e.g., "0001", "1001", "2001"
	// The actual concept is identified by the digits *after* the language prefix.
	// e.g., "0001" -> lang_prefix '0', concept_digits "001"
	//       "1001" -> lang_prefix '1', concept_digits "001"
	sourceConceptKey := parts[1]
	if len(sourceConceptKey) < 1 { // Should be like "0001"
		equivalents[sourceTagID] = struct{}{}
		return mapToSlice(equivalents)
	}
	actualConceptDigits := sourceConceptKey[1:] // e.g., "001" from "0001" or "1001"

	for _, systemTag := range allSystemTags {
		systemTagIDStr := systemTag.ID.String()
		systemParts := strings.Split(systemTagIDStr, "-")

		if len(systemParts) == 5 && strings.HasPrefix(systemTagIDStr, uuidConceptPrefix) {
			potentialConceptKey := systemParts[1]
			if len(potentialConceptKey) > 0 && potentialConceptKey[1:] == actualConceptDigits {
				equivalents[systemTag.ID] = struct{}{}
			}
		}
	}

	// Ensure the original tag is included if it somehow wasn't matched by its own pattern logic
	equivalents[sourceTagID] = struct{}{}

	return mapToSlice(equivalents)
}

func mapToSlice(m map[uuid.UUID]struct{}) []uuid.UUID {
	s := make([]uuid.UUID, 0, len(m))
	for k := range m {
		s = append(s, k)
	}
	return s
}
