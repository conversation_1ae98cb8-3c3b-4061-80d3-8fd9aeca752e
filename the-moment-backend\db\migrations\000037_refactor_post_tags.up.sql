-- Drop existing tables related to post tags
DROP TABLE IF EXISTS "post_post_tags";
DROP TABLE IF EXISTS "post_tags";

-- Create the new "post_tags" table
CREATE TABLE "post_tags" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- Using gen_random_uuid() as it was used in 000026
  "tag_name" VARCHAR(255) NOT NULL,
  "language_code" VARCHAR(10) NOT NULL,
  "description" TEXT,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  UNIQUE ("tag_name", "language_code")
);

-- Recreate the "post_post_tags" table
CREATE TABLE "post_post_tags" (
  "post_id" uuid NOT NULL,
  "tag_id" uuid NOT NULL,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  PRIMARY KEY ("post_id", "tag_id")
);

ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("post_id") REFERENCES "posts" ("id") ON DELETE CASCADE;
ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("tag_id") REFERENCES "post_tags" ("id") ON DELETE CASCADE;
