import React, { useState, useEffect, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, FlatList, TextInput, SafeAreaView } from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';

import countryData from 'country-list-with-dial-code-and-flag';
import { useTheme } from '@react-navigation/native';

// Type definition for country object
export interface Country {
  name: string;
  code: string;
  dialCode: string;
  flag: string;
  localName?: string;
}

interface CountryPickerProps {
  selectedCountry: Country | null;
  onSelectCountry: (country: Country) => void;
  error?: string;
  touched?: boolean;
  countries?: Country[]; // Make countries optional, we'll process them internally if not provided
  shouldProcessCountryData?: boolean; // New prop to determine if we should process country data internally
  countryCode?: string; // Add countryCode prop to find country by code
}

export const CountryPicker: React.FC<CountryPickerProps> = ({
  selectedCountry,
  onSelectCountry,
  error,
  touched,
  countries: propCountries,
  shouldProcessCountryData = true, // Default to true for backward compatibility
  countryCode
}) => {
  const { t } = useTranslation();
  const theme = appStyleStore((state) => state.theme);
  const [countryModalVisible, setCountryModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [internalSelectedCountry, setInternalSelectedCountry] = useState<Country | null>(selectedCountry);

  // Process countries internally if not provided or if shouldProcessCountryData is true
  const countries = propCountries || (shouldProcessCountryData ? processCountryData(countryData.getAll()) : []);

  // Find country by code if countryCode prop is provided
  useEffect(() => {
    if (countryCode && !internalSelectedCountry) {
      const foundCountry = countries.find(c => c.code === countryCode);
      if (foundCountry) {
        setInternalSelectedCountry(foundCountry);
        onSelectCountry(foundCountry);
      }
    }
  }, [countryCode, countries, internalSelectedCountry, onSelectCountry]);

  // Update internal selected country when prop changes
  useEffect(() => {
    if (selectedCountry !== internalSelectedCountry) {
      setInternalSelectedCountry(selectedCountry);
    }
  }, [selectedCountry]);

  // Update the filter to also search by localName
  const filteredCountries = searchQuery 
    ? countries.filter(country => 
        country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        country.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (country.localName && country.localName.toLowerCase().includes(searchQuery.toLowerCase()))
      ) 
    : countries;

  const openCountryModal = () => {
    setCountryModalVisible(true);
  };

  const closeCountryModal = () => {
    setCountryModalVisible(false);
    setSearchQuery('');
  };

  const handleCountrySelect = (country: Country) => {
    setInternalSelectedCountry(country);
    onSelectCountry(country);
    closeCountryModal();
  };

  const styles = StyleSheet.create({
    countrySelector: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: 48,
      borderWidth: 1,
      borderColor: theme.system.border,
      borderRadius: 8,
      paddingHorizontal: 16,
    },
    countrySelectorError: {
      borderColor: theme.colors.error,
    },
    selectedCountryText: {
      fontSize: 16,
      color: theme.system.text,
      flex: 1,
    },
    countryPlaceholder: {
      fontSize: 16,
      color: theme.system.secondaryText,
    },
    countryFlag: {
      marginRight: 10,
      fontSize: 20,
    },
    selectedCountryRow: {
      flexDirection: 'row', 
      alignItems: 'center',
      flex: 1,
    },
    // Country modal styles
    modalContainer: {
      flex: 1,
      backgroundColor: theme.system.background,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.system.border,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.system.text,
      flex: 1,
      textAlign: 'center',
    },
    closeButton: {
      padding: 8,
    },
    searchBar: {
      borderWidth: 1,
      borderColor: theme.system.border,
      borderRadius: 8,
      padding: 12,
      margin: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    searchInput: {
      flex: 1,
      marginLeft: 8,
      fontSize: 16,
      color: theme.system.text,
    },
    countryItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.system.border,
    },
    countryItemContent: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      overflow: 'hidden',
    },
    countryNameContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    countryName: {
      fontSize: 16,
      color: theme.system.text,
    },
    countryCode: {
      fontSize: 14,
      color: theme.system.secondaryText,
      marginLeft: 2,
    },
    noResults: {
      padding: 16,
      alignItems: 'center',
    },
    noResultsText: {
      fontSize: 16,
      color: theme.system.secondaryText,
    },
  });

  return (
    <>
      <TouchableOpacity 
        style={[styles.countrySelector, error && touched && styles.countrySelectorError]}
        onPress={openCountryModal}
      >
        {internalSelectedCountry ? (
          <View style={styles.selectedCountryRow}>
            <Text style={styles.countryFlag}>{internalSelectedCountry.flag}</Text>
            <Text style={styles.selectedCountryText} numberOfLines={1} ellipsizeMode="tail">
              <Text>{internalSelectedCountry.localName || internalSelectedCountry.name}</Text>
              <Text style={styles.countryCode}> ({internalSelectedCountry.name})</Text>
            </Text>
          </View>
        ) : (
          <Text style={styles.countryPlaceholder}>{t('identity.info.countryPlaceholder')}</Text>
        )}
        <MaterialCommunityIcons 
          name="chevron-down" 
          size={20} 
          color={theme.system.secondaryText} 
        />
      </TouchableOpacity>

      {/* Custom Country Picker Modal */}
      <Modal
        visible={countryModalVisible}
        animationType="slide"
        onRequestClose={closeCountryModal}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity style={styles.closeButton} onPress={closeCountryModal}>
              <MaterialCommunityIcons name="close" size={24} color={theme.system.text} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>{t('identity.info.selectCountry')}</Text>
            <View style={{ width: 40 }} />
          </View>
          
          <View style={styles.searchBar}>
            <MaterialCommunityIcons name="magnify" size={20} color={theme.system.secondaryText} />
            <TextInput
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder={t('identity.info.searchCountry')}
              placeholderTextColor={theme.system.secondaryText}
              autoCapitalize="none"
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <MaterialCommunityIcons name="close-circle" size={20} color={theme.system.secondaryText} />
              </TouchableOpacity>
            ) : null}
          </View>
          
          {filteredCountries.length > 0 ? (
            <FlatList
              data={filteredCountries}
              keyExtractor={(item, index) => `${item.code}-${index}`}
              renderItem={({ item }) => (
                <TouchableOpacity 
                  style={styles.countryItem} 
                  onPress={() => handleCountrySelect(item)}
                >
                  <Text style={styles.countryFlag}>{item.flag}</Text>
                  <View style={styles.countryItemContent}>
                    <View style={styles.countryNameContainer}>
                      <Text numberOfLines={1} ellipsizeMode="tail">
                        <Text style={styles.countryName}>
                          {item.localName || item.name}
                        </Text>
                        <Text style={styles.countryCode}> ({item.name})</Text>
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              )}
            />
          ) : (
            <View style={styles.noResults}>
              <Text style={styles.noResultsText}>{t('identity.info.noCountryResults')}</Text>
            </View>
          )}
        </SafeAreaView>
      </Modal>
    </>
  );
};

// Helper function to process country data
export const processCountryData = (countryList: any[]): Country[] => {
  // Parse countries from the library using the correct property names
  let countries: Country[] = countryList.map((country: any) => {
    // 特殊處理台灣、香港和澳門的本地化名稱和英文名稱
    let localName = country.localName;
    let name = country.name;
    
    // 特殊情況處理
    if (country.code === 'TW') {
      localName = '中國台灣';
      name = 'Taiwan, China';
    } else if (country.code === 'HK') {
      localName = '中國香港';
      name = 'Hong Kong, China';
    } else if (country.code === 'MO') {
      localName = '中國澳門';
      name = 'Macau, China';
    }
    
    return {
      name: name,
      code: country.code,
      dialCode: country.dialCode,
      flag: country.flag,
      localName: localName
    };
  });
  
  // 去除重複的國家（基於國家代碼）
  const uniqueCountryCodes = new Set<string>();
  countries = countries.filter(country => {
    if (uniqueCountryCodes.has(country.code)) {
      return false;
    }
    uniqueCountryCodes.add(country.code);
    return true;
  });
  
  // 將中國、台灣、香港、澳門放在列表最前方
  const priorityCountries = countries.filter(country => 
    ['CN', 'TW', 'HK', 'MO'].includes(country.code)
  );
  const otherCountries = countries.filter(country => 
    !['CN', 'TW', 'HK', 'MO'].includes(country.code)
  );
  
  return [...priorityCountries, ...otherCountries];
}; 