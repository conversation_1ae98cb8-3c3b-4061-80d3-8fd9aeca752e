/* PostListPage.css */

.list-container {
    max-width: 800px;
    margin: 10px auto;
    background-color: #ffffff;
}

.search-create-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.list-item {
    padding: 8px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.list-item:last-child {
    border-bottom: none;
}

.list-item .content {
    color: rgba(0, 0, 0, 0.65);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
}

.list-item.clickable {
    cursor: pointer;
    transition: background-color 0.3s;
}

.list-item.clickable:hover {
    background-color: #f5f5f5;
}

/* Action button styles */
.list-item .ant-list-item-action {
    margin-top: 8px;
}

/* Image container styles */
.list-item .ant-list-item-extra {
    margin-left: 16px;
}

/* Title styles */
.list-item .ant-list-item-meta {
    margin-bottom: 4px;
}

.list-item .ant-list-item-meta-title {
    font-size: 16px;
    margin-bottom: 0;
}

.delete-action {
    color: #ff4d4f;
    cursor: pointer;
    transition: color 0.3s;
}

.delete-action:hover {
    color: #ff7875;
}

.list-item .ant-list-item-action > li {
    padding: 0 8px;
}

/* Post List Page */

.post-image-container {
    position: relative;
    width: 100%;
    height: 200px;
    background-color: #f5f5f5;
    overflow: hidden;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.post-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-image-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
}

.post-skeleton {
    width: 100% !important;
    height: 100% !important;
}

/* Override Ant Design skeleton image styles */
.post-image-container .ant-skeleton-image {
    width: 100% !important;
    height: 200px !important;
    background-color: #f5f5f5;
}

/* Post card styles to match EventList */
.post-list-card {
    width: 100%;
    border-radius: 10px;
    position: relative;
    transition: transform 0.3s;
    background-color: white;
    margin: 10px 0;
    border: 1px solid #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.post-list-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.post-image-container.grayscale img.post-image {
    filter: grayscale(100%);
}

.post-list-item.post-hidden /* You can add further specific styling to the entire card item if it's hidden */ {
    /* Example: border: 1px solid #ccc; */
}

.post-list-item.post-draft /* You can add further specific styling to the entire card item if it's a draft */ {
    /* Example: border-left: 3px solid blue; */
}

/* Styles for the overlay text on the image */
.status-overlay-text {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 3px 7px;
    border-radius: 10px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 10; /* Ensure it's above the image */
}

.draft-overlay-text {
    background-color: rgba(24, 144, 255, 0.7); /* Ant Design blue with some transparency */
}

/* Ensure Card.Meta and its contents are styled well, adjust if tags cause layout issues */
.post-list-card .ant-card-meta-description .ant-tag {
    margin-top: 4px; /* Add some space if tags are below other description items */
}

.post-image-skeleton {
    width: 100%;
    height: 200px; /* Ensure this matches your image container or image height */
    display: flex;
    align-items: center;
    justify-content: center;
}

.post-image-skeleton .ant-skeleton-image {
    width: 100% !important;
    height: 100% !important;
}

.post-image-skeleton .ant-skeleton-image svg {
    width: 100%;
    height: 100%;
}
