// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: posts.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const countPostsByOrganization = `-- name: CountPostsByOrganization :one
SELECT COUNT(*) FROM posts
WHERE organization_id = $1
`

func (q *Queries) CountPostsByOrganization(ctx context.Context, organizationID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, countPostsByOrganization, organizationID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPostsByOrganizationWithTags = `-- name: CountPostsByOrganizationWithTags :one
SELECT COUNT(DISTINCT p.id)
FROM posts p
LEFT JOIN post_post_tags ppt ON p.id = ppt.post_id -- Join for tag filtering
LEFT JOIN post_tags pt ON ppt.tag_id = pt.id     -- Join for tag filtering
WHERE p.organization_id = $1
  AND ($2::TEXT IS NULL OR p.status = $2::TEXT) -- Status can still be narg
  AND ($3::TIMESTAMPTZ IS NULL OR p.created_at >= $3::TIMESTAMPTZ)
  AND ($4::TIMESTAMPTZ IS NULL OR p.created_at <= $4::TIMESTAMPTZ)
  AND ($5::TEXT IS NULL OR (p.title ILIKE '%' || $5::TEXT || '%' OR p.content::TEXT ILIKE '%' || $5::TEXT || '%'))
  AND (
    cardinality($6::UUID[]) = 0 OR -- Use sqlc.arg, rely on cardinality for empty check
    EXISTS (
      SELECT 1
      FROM post_post_tags e_ppt
      JOIN post_tags e_pt ON e_ppt.tag_id = e_pt.id
      WHERE e_ppt.post_id = p.id AND e_pt.id = ANY($6::UUID[]) -- Use sqlc.arg
    )
  )
`

type CountPostsByOrganizationWithTagsParams struct {
	OrganizationID uuid.UUID   `db:"organization_id" json:"organization_id"`
	Status         *string     `db:"status" json:"status"`
	StartDate      *time.Time  `db:"start_date" json:"start_date"`
	EndDate        *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm     *string     `db:"search_term" json:"search_term"`
	TagIds         []uuid.UUID `db:"tag_ids" json:"tag_ids"`
}

func (q *Queries) CountPostsByOrganizationWithTags(ctx context.Context, arg CountPostsByOrganizationWithTagsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countPostsByOrganizationWithTags,
		arg.OrganizationID,
		arg.Status,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.TagIds,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPublishedPosts = `-- name: CountPublishedPosts :one
SELECT COUNT(p.id)
FROM posts p
WHERE p.status = 'published'
  AND (
    ($1::UUID IS NULL AND $2::UUID IS NULL) OR
    ($1::UUID IS NOT NULL AND p.organization_id = $1::UUID) OR
    ($2::UUID IS NOT NULL AND p.organization_id = $2::UUID)
  )
  AND ($3::TIMESTAMPTZ IS NULL OR p.published_at >= $3::TIMESTAMPTZ)
  AND ($4::TIMESTAMPTZ IS NULL OR p.published_at <= $4::TIMESTAMPTZ)
  AND ($5::TEXT IS NULL OR p.title ILIKE '%' || $5::TEXT || '%')
  AND (cardinality($6::UUID[]) = 0 OR
       EXISTS (SELECT 1 FROM post_post_tags e_ppt WHERE e_ppt.post_id = p.id AND e_ppt.tag_id = ANY($6::UUID[]))
      )
`

type CountPublishedPostsParams struct {
	OrganizationID  *uuid.UUID  `db:"organization_id" json:"organization_id"`
	OrganizationId2 *uuid.UUID  `db:"organization_id2" json:"organization_id2"`
	StartDate       *time.Time  `db:"start_date" json:"start_date"`
	EndDate         *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm      *string     `db:"search_term" json:"search_term"`
	TagIds          []uuid.UUID `db:"tag_ids" json:"tag_ids"`
}

func (q *Queries) CountPublishedPosts(ctx context.Context, arg CountPublishedPostsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countPublishedPosts,
		arg.OrganizationID,
		arg.OrganizationId2,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.TagIds,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countPublishedPostsByOrganization = `-- name: CountPublishedPostsByOrganization :one
SELECT COUNT(*) FROM posts
WHERE organization_id = $1 AND status = 'published'
`

func (q *Queries) CountPublishedPostsByOrganization(ctx context.Context, organizationID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, countPublishedPostsByOrganization, organizationID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createPost = `-- name: CreatePost :one
INSERT INTO posts (
    organization_id,
    author_id,
    title,
    slug,
    content,
    status,
    published_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) RETURNING id, organization_id, title, slug, status, published_at, created_at, updated_at, content, author_id, updated_by
`

type CreatePostParams struct {
	OrganizationID uuid.UUID  `db:"organization_id" json:"organization_id"`
	AuthorID       uuid.UUID  `db:"author_id" json:"author_id"`
	Title          string     `db:"title" json:"title"`
	Slug           string     `db:"slug" json:"slug"`
	Content        []byte     `db:"content" json:"content"`
	Status         string     `db:"status" json:"status"`
	PublishedAt    *time.Time `db:"published_at" json:"published_at"`
}

func (q *Queries) CreatePost(ctx context.Context, arg CreatePostParams) (Post, error) {
	row := q.db.QueryRow(ctx, createPost,
		arg.OrganizationID,
		arg.AuthorID,
		arg.Title,
		arg.Slug,
		arg.Content,
		arg.Status,
		arg.PublishedAt,
	)
	var i Post
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Content,
		&i.AuthorID,
		&i.UpdatedBy,
	)
	return i, err
}

const createPostMediaItem = `-- name: CreatePostMediaItem :one
INSERT INTO post_media_items (
    post_id,
    file_name,
    file_path,
    file_type,
    file_size,
    is_banner
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING id, post_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner
`

type CreatePostMediaItemParams struct {
	PostID   uuid.UUID `db:"post_id" json:"post_id"`
	FileName string    `db:"file_name" json:"file_name"`
	FilePath string    `db:"file_path" json:"file_path"`
	FileType string    `db:"file_type" json:"file_type"`
	FileSize int64     `db:"file_size" json:"file_size"`
	IsBanner bool      `db:"is_banner" json:"is_banner"`
}

func (q *Queries) CreatePostMediaItem(ctx context.Context, arg CreatePostMediaItemParams) (PostMediaItem, error) {
	row := q.db.QueryRow(ctx, createPostMediaItem,
		arg.PostID,
		arg.FileName,
		arg.FilePath,
		arg.FileType,
		arg.FileSize,
		arg.IsBanner,
	)
	var i PostMediaItem
	err := row.Scan(
		&i.ID,
		&i.PostID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.UploadedAt,
		&i.IsBanner,
	)
	return i, err
}

const deletePost = `-- name: DeletePost :exec
DELETE FROM posts
WHERE id = $1
`

func (q *Queries) DeletePost(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deletePost, id)
	return err
}

const deletePostMediaByPostID = `-- name: DeletePostMediaByPostID :exec
DELETE FROM post_media_items WHERE post_id = $1
`

func (q *Queries) DeletePostMediaByPostID(ctx context.Context, postID uuid.UUID) error {
	_, err := q.db.Exec(ctx, deletePostMediaByPostID, postID)
	return err
}

const deletePostMediaItem = `-- name: DeletePostMediaItem :exec
DELETE FROM post_media_items
WHERE id = $1
`

func (q *Queries) DeletePostMediaItem(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deletePostMediaItem, id)
	return err
}

const getPostByID = `-- name: GetPostByID :one
SELECT id, organization_id, title, slug, status, published_at, created_at, updated_at, content, author_id, updated_by FROM posts
WHERE id = $1 LIMIT 1
`

func (q *Queries) GetPostByID(ctx context.Context, id uuid.UUID) (Post, error) {
	row := q.db.QueryRow(ctx, getPostByID, id)
	var i Post
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Content,
		&i.AuthorID,
		&i.UpdatedBy,
	)
	return i, err
}

const getPostBySlug = `-- name: GetPostBySlug :one
SELECT id, organization_id, title, slug, status, published_at, created_at, updated_at, content, author_id, updated_by FROM posts
WHERE slug = $1 LIMIT 1
`

func (q *Queries) GetPostBySlug(ctx context.Context, slug string) (Post, error) {
	row := q.db.QueryRow(ctx, getPostBySlug, slug)
	var i Post
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Content,
		&i.AuthorID,
		&i.UpdatedBy,
	)
	return i, err
}

const getPostDetailsWithTags = `-- name: GetPostDetailsWithTags :one
SELECT
    p.id, p.organization_id, p.title, p.slug, p.status, p.published_at, p.created_at, p.updated_at, p.content, p.author_id, p.updated_by,
    u.display_name as author_display_name,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', pt.id, 'name_en', pt.name_en, 'name_zh_hk', pt.name_zh_hk, 'name_zh_cn', pt.name_zh_cn, 'description_en', pt.description_en, 'description_zh_hk', pt.description_zh_hk, 'description_zh_cn', pt.description_zh_cn)) FILTER (WHERE pt.id IS NOT NULL), '[]') AS tags
FROM posts p
JOIN users u ON p.author_id = u.id
LEFT JOIN post_post_tags ppt ON p.id = ppt.post_id
LEFT JOIN post_tags pt ON ppt.tag_id = pt.id
WHERE p.id = $1
GROUP BY p.id, u.display_name
`

type GetPostDetailsWithTagsRow struct {
	ID                uuid.UUID   `db:"id" json:"id"`
	OrganizationID    uuid.UUID   `db:"organization_id" json:"organization_id"`
	Title             string      `db:"title" json:"title"`
	Slug              string      `db:"slug" json:"slug"`
	Status            string      `db:"status" json:"status"`
	PublishedAt       *time.Time  `db:"published_at" json:"published_at"`
	CreatedAt         time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time   `db:"updated_at" json:"updated_at"`
	Content           []byte      `db:"content" json:"content"`
	AuthorID          uuid.UUID   `db:"author_id" json:"author_id"`
	UpdatedBy         *uuid.UUID  `db:"updated_by" json:"updated_by"`
	AuthorDisplayName string      `db:"author_display_name" json:"author_display_name"`
	Tags              interface{} `db:"tags" json:"tags"`
}

func (q *Queries) GetPostDetailsWithTags(ctx context.Context, id uuid.UUID) (GetPostDetailsWithTagsRow, error) {
	row := q.db.QueryRow(ctx, getPostDetailsWithTags, id)
	var i GetPostDetailsWithTagsRow
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Content,
		&i.AuthorID,
		&i.UpdatedBy,
		&i.AuthorDisplayName,
		&i.Tags,
	)
	return i, err
}

const getPostMediaItemByID = `-- name: GetPostMediaItemByID :one
SELECT id, post_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner FROM post_media_items
WHERE id = $1 LIMIT 1
`

func (q *Queries) GetPostMediaItemByID(ctx context.Context, id uuid.UUID) (PostMediaItem, error) {
	row := q.db.QueryRow(ctx, getPostMediaItemByID, id)
	var i PostMediaItem
	err := row.Scan(
		&i.ID,
		&i.PostID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.UploadedAt,
		&i.IsBanner,
	)
	return i, err
}

const getPostWithMedia = `-- name: GetPostWithMedia :many
SELECT
    p.id, p.organization_id, p.title, p.slug, p.status, p.published_at, p.created_at, p.updated_at, p.content, p.author_id, p.updated_by,
    pm.id AS post_media_item_id,
    pm.post_id AS post_media_item_post_id,
    pm.file_name AS post_media_item_file_name,
    pm.file_path AS post_media_item_file_path,
    pm.file_type AS post_media_item_file_type,
    pm.file_size AS post_media_item_file_size,
    pm.uploaded_at AS post_media_item_uploaded_at
FROM
    posts p
LEFT JOIN
    post_media_items pm ON p.id = pm.post_id
WHERE
    p.id = $1
`

type GetPostWithMediaRow struct {
	Post                    Post       `db:"post" json:"post"`
	PostMediaItemID         *uuid.UUID `db:"post_media_item_id" json:"post_media_item_id"`
	PostMediaItemPostID     *uuid.UUID `db:"post_media_item_post_id" json:"post_media_item_post_id"`
	PostMediaItemFileName   *string    `db:"post_media_item_file_name" json:"post_media_item_file_name"`
	PostMediaItemFilePath   *string    `db:"post_media_item_file_path" json:"post_media_item_file_path"`
	PostMediaItemFileType   *string    `db:"post_media_item_file_type" json:"post_media_item_file_type"`
	PostMediaItemFileSize   *int64     `db:"post_media_item_file_size" json:"post_media_item_file_size"`
	PostMediaItemUploadedAt *time.Time `db:"post_media_item_uploaded_at" json:"post_media_item_uploaded_at"`
}

func (q *Queries) GetPostWithMedia(ctx context.Context, id uuid.UUID) ([]GetPostWithMediaRow, error) {
	rows, err := q.db.Query(ctx, getPostWithMedia, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetPostWithMediaRow{}
	for rows.Next() {
		var i GetPostWithMediaRow
		if err := rows.Scan(
			&i.Post.ID,
			&i.Post.OrganizationID,
			&i.Post.Title,
			&i.Post.Slug,
			&i.Post.Status,
			&i.Post.PublishedAt,
			&i.Post.CreatedAt,
			&i.Post.UpdatedAt,
			&i.Post.Content,
			&i.Post.AuthorID,
			&i.Post.UpdatedBy,
			&i.PostMediaItemID,
			&i.PostMediaItemPostID,
			&i.PostMediaItemFileName,
			&i.PostMediaItemFilePath,
			&i.PostMediaItemFileType,
			&i.PostMediaItemFileSize,
			&i.PostMediaItemUploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getScheduledPostsToPublish = `-- name: GetScheduledPostsToPublish :many
SELECT id FROM posts WHERE status = 'draft' AND published_at IS NOT NULL AND published_at <= NOW()
`

func (q *Queries) GetScheduledPostsToPublish(ctx context.Context) ([]uuid.UUID, error) {
	rows, err := q.db.Query(ctx, getScheduledPostsToPublish)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []uuid.UUID{}
	for rows.Next() {
		var id uuid.UUID
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		items = append(items, id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listMediaItemsByPost = `-- name: ListMediaItemsByPost :many
SELECT id, post_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner FROM post_media_items
WHERE post_id = $1
ORDER BY is_banner DESC, uploaded_at ASC
`

func (q *Queries) ListMediaItemsByPost(ctx context.Context, postID uuid.UUID) ([]PostMediaItem, error) {
	rows, err := q.db.Query(ctx, listMediaItemsByPost, postID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []PostMediaItem{}
	for rows.Next() {
		var i PostMediaItem
		if err := rows.Scan(
			&i.ID,
			&i.PostID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.UploadedAt,
			&i.IsBanner,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listMediaItemsByPostIDs = `-- name: ListMediaItemsByPostIDs :many
SELECT id, post_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner FROM post_media_items
WHERE post_id = ANY($1::UUID[])
ORDER BY post_id, is_banner DESC, uploaded_at ASC
`

func (q *Queries) ListMediaItemsByPostIDs(ctx context.Context, postIds []uuid.UUID) ([]PostMediaItem, error) {
	rows, err := q.db.Query(ctx, listMediaItemsByPostIDs, postIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []PostMediaItem{}
	for rows.Next() {
		var i PostMediaItem
		if err := rows.Scan(
			&i.ID,
			&i.PostID,
			&i.FileName,
			&i.FilePath,
			&i.FileType,
			&i.FileSize,
			&i.UploadedAt,
			&i.IsBanner,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOrgPostsWithMedia = `-- name: ListOrgPostsWithMedia :many
SELECT
    p.id,
    p.organization_id,
    p.author_id,
    p.title,
    p.slug,
    p.content,
    p.status,
    p.published_at,
    p.created_at,
    p.updated_at,
    u.display_name AS author_display_name,
    pm.id AS post_media_item_id,
    pm.post_id AS post_media_item_post_id,
    pm.file_name AS post_media_item_file_name,
    pm.file_path AS post_media_item_file_path,
    pm.file_type AS post_media_item_file_type,
    pm.file_size AS post_media_item_file_size,
    pm.uploaded_at AS post_media_item_uploaded_at
FROM
    posts p
JOIN
    users u ON p.author_id = u.id
LEFT JOIN
    post_media_items pm ON p.id = pm.post_id
WHERE
    p.organization_id = $1
ORDER BY
    p.created_at DESC, p.id ASC, pm.uploaded_at ASC
LIMIT $2 OFFSET $3
`

type ListOrgPostsWithMediaParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Limit          int32     `db:"limit" json:"limit"`
	Offset         int32     `db:"offset" json:"offset"`
}

type ListOrgPostsWithMediaRow struct {
	ID                      uuid.UUID  `db:"id" json:"id"`
	OrganizationID          uuid.UUID  `db:"organization_id" json:"organization_id"`
	AuthorID                uuid.UUID  `db:"author_id" json:"author_id"`
	Title                   string     `db:"title" json:"title"`
	Slug                    string     `db:"slug" json:"slug"`
	Content                 []byte     `db:"content" json:"content"`
	Status                  string     `db:"status" json:"status"`
	PublishedAt             *time.Time `db:"published_at" json:"published_at"`
	CreatedAt               time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt               time.Time  `db:"updated_at" json:"updated_at"`
	AuthorDisplayName       string     `db:"author_display_name" json:"author_display_name"`
	PostMediaItemID         *uuid.UUID `db:"post_media_item_id" json:"post_media_item_id"`
	PostMediaItemPostID     *uuid.UUID `db:"post_media_item_post_id" json:"post_media_item_post_id"`
	PostMediaItemFileName   *string    `db:"post_media_item_file_name" json:"post_media_item_file_name"`
	PostMediaItemFilePath   *string    `db:"post_media_item_file_path" json:"post_media_item_file_path"`
	PostMediaItemFileType   *string    `db:"post_media_item_file_type" json:"post_media_item_file_type"`
	PostMediaItemFileSize   *int64     `db:"post_media_item_file_size" json:"post_media_item_file_size"`
	PostMediaItemUploadedAt *time.Time `db:"post_media_item_uploaded_at" json:"post_media_item_uploaded_at"`
}

func (q *Queries) ListOrgPostsWithMedia(ctx context.Context, arg ListOrgPostsWithMediaParams) ([]ListOrgPostsWithMediaRow, error) {
	rows, err := q.db.Query(ctx, listOrgPostsWithMedia, arg.OrganizationID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListOrgPostsWithMediaRow{}
	for rows.Next() {
		var i ListOrgPostsWithMediaRow
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.AuthorID,
			&i.Title,
			&i.Slug,
			&i.Content,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AuthorDisplayName,
			&i.PostMediaItemID,
			&i.PostMediaItemPostID,
			&i.PostMediaItemFileName,
			&i.PostMediaItemFilePath,
			&i.PostMediaItemFileType,
			&i.PostMediaItemFileSize,
			&i.PostMediaItemUploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPostsByOrganization = `-- name: ListPostsByOrganization :many
SELECT id, organization_id, title, slug, status, published_at, created_at, updated_at, content, author_id, updated_by FROM posts
WHERE organization_id = $1
ORDER BY created_at DESC
LIMIT $2 OFFSET $3
`

type ListPostsByOrganizationParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Limit          int32     `db:"limit" json:"limit"`
	Offset         int32     `db:"offset" json:"offset"`
}

func (q *Queries) ListPostsByOrganization(ctx context.Context, arg ListPostsByOrganizationParams) ([]Post, error) {
	rows, err := q.db.Query(ctx, listPostsByOrganization, arg.OrganizationID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Post{}
	for rows.Next() {
		var i Post
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.Slug,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Content,
			&i.AuthorID,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPostsByOrganizationWithTags = `-- name: ListPostsByOrganizationWithTags :many
SELECT
    p.id, p.organization_id, p.title, p.slug, p.status, p.published_at, p.created_at, p.updated_at, p.content, p.author_id, p.updated_by,
    u.display_name as author_display_name,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', pt.id, 'name_en', pt.name_en, 'name_zh_hk', pt.name_zh_hk, 'name_zh_cn', pt.name_zh_cn, 'description_en', pt.description_en, 'description_zh_hk', pt.description_zh_hk, 'description_zh_cn', pt.description_zh_cn)) FILTER (WHERE pt.id IS NOT NULL), '[]') AS tags
FROM posts p
JOIN users u ON p.author_id = u.id
LEFT JOIN post_post_tags ppt ON p.id = ppt.post_id -- For collecting tags for json_agg
LEFT JOIN post_tags pt ON ppt.tag_id = pt.id     -- For collecting tags for json_agg
WHERE p.organization_id = $1
  AND ($4::TEXT IS NULL OR p.status = $4::TEXT) -- Status can still be narg
  AND ($5::TIMESTAMPTZ IS NULL OR p.created_at >= $5::TIMESTAMPTZ)
  AND ($6::TIMESTAMPTZ IS NULL OR p.created_at <= $6::TIMESTAMPTZ)
  AND ($7::TEXT IS NULL OR (p.title ILIKE '%' || $7::TEXT || '%' OR p.content::TEXT ILIKE '%' || $7::TEXT || '%'))
  AND (
    cardinality($8::UUID[]) = 0 OR -- Use sqlc.arg, rely on cardinality for empty check
    EXISTS (
      SELECT 1
      FROM post_post_tags e_ppt
      JOIN post_tags e_pt ON e_ppt.tag_id = e_pt.id
      WHERE e_ppt.post_id = p.id AND e_pt.id = ANY($8::UUID[]) -- Use sqlc.arg
    )
  )
GROUP BY p.id, u.display_name
ORDER BY p.created_at DESC
LIMIT $2 OFFSET $3
`

type ListPostsByOrganizationWithTagsParams struct {
	OrganizationID uuid.UUID   `db:"organization_id" json:"organization_id"`
	Limit          int32       `db:"limit" json:"limit"`
	Offset         int32       `db:"offset" json:"offset"`
	Status         *string     `db:"status" json:"status"`
	StartDate      *time.Time  `db:"start_date" json:"start_date"`
	EndDate        *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm     *string     `db:"search_term" json:"search_term"`
	TagIds         []uuid.UUID `db:"tag_ids" json:"tag_ids"`
}

type ListPostsByOrganizationWithTagsRow struct {
	ID                uuid.UUID   `db:"id" json:"id"`
	OrganizationID    uuid.UUID   `db:"organization_id" json:"organization_id"`
	Title             string      `db:"title" json:"title"`
	Slug              string      `db:"slug" json:"slug"`
	Status            string      `db:"status" json:"status"`
	PublishedAt       *time.Time  `db:"published_at" json:"published_at"`
	CreatedAt         time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time   `db:"updated_at" json:"updated_at"`
	Content           []byte      `db:"content" json:"content"`
	AuthorID          uuid.UUID   `db:"author_id" json:"author_id"`
	UpdatedBy         *uuid.UUID  `db:"updated_by" json:"updated_by"`
	AuthorDisplayName string      `db:"author_display_name" json:"author_display_name"`
	Tags              interface{} `db:"tags" json:"tags"`
}

func (q *Queries) ListPostsByOrganizationWithTags(ctx context.Context, arg ListPostsByOrganizationWithTagsParams) ([]ListPostsByOrganizationWithTagsRow, error) {
	rows, err := q.db.Query(ctx, listPostsByOrganizationWithTags,
		arg.OrganizationID,
		arg.Limit,
		arg.Offset,
		arg.Status,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.TagIds,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPostsByOrganizationWithTagsRow{}
	for rows.Next() {
		var i ListPostsByOrganizationWithTagsRow
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.Slug,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Content,
			&i.AuthorID,
			&i.UpdatedBy,
			&i.AuthorDisplayName,
			&i.Tags,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublishedPosts = `-- name: ListPublishedPosts :many
SELECT id, organization_id, title, slug, status, published_at, created_at, updated_at, content, author_id, updated_by FROM posts
WHERE status = 'published'
ORDER BY published_at DESC
LIMIT $1 OFFSET $2
`

type ListPublishedPostsParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

func (q *Queries) ListPublishedPosts(ctx context.Context, arg ListPublishedPostsParams) ([]Post, error) {
	rows, err := q.db.Query(ctx, listPublishedPosts, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Post{}
	for rows.Next() {
		var i Post
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.Slug,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Content,
			&i.AuthorID,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublishedPostsByOrganizationWithMedia = `-- name: ListPublishedPostsByOrganizationWithMedia :many
SELECT
    p.id, p.organization_id, p.title, p.slug, p.status, p.published_at, p.created_at, p.updated_at, p.content, p.author_id, p.updated_by,
    pm.id AS post_media_item_id,
    pm.post_id AS post_media_item_post_id,
    pm.file_name AS post_media_item_file_name,
    pm.file_path AS post_media_item_file_path,
    pm.file_type AS post_media_item_file_type,
    pm.file_size AS post_media_item_file_size,
    pm.uploaded_at AS post_media_item_uploaded_at
FROM
    posts p
LEFT JOIN
    post_media_items pm ON p.id = pm.post_id
WHERE
    p.organization_id = $1 AND p.status = 'published'
ORDER BY
    p.published_at DESC, p.id ASC, pm.uploaded_at ASC -- Ensuring stable sort for posts and their media
LIMIT $2 OFFSET $3
`

type ListPublishedPostsByOrganizationWithMediaParams struct {
	OrganizationID uuid.UUID `db:"organization_id" json:"organization_id"`
	Limit          int32     `db:"limit" json:"limit"`
	Offset         int32     `db:"offset" json:"offset"`
}

type ListPublishedPostsByOrganizationWithMediaRow struct {
	Post                    Post       `db:"post" json:"post"`
	PostMediaItemID         *uuid.UUID `db:"post_media_item_id" json:"post_media_item_id"`
	PostMediaItemPostID     *uuid.UUID `db:"post_media_item_post_id" json:"post_media_item_post_id"`
	PostMediaItemFileName   *string    `db:"post_media_item_file_name" json:"post_media_item_file_name"`
	PostMediaItemFilePath   *string    `db:"post_media_item_file_path" json:"post_media_item_file_path"`
	PostMediaItemFileType   *string    `db:"post_media_item_file_type" json:"post_media_item_file_type"`
	PostMediaItemFileSize   *int64     `db:"post_media_item_file_size" json:"post_media_item_file_size"`
	PostMediaItemUploadedAt *time.Time `db:"post_media_item_uploaded_at" json:"post_media_item_uploaded_at"`
}

func (q *Queries) ListPublishedPostsByOrganizationWithMedia(ctx context.Context, arg ListPublishedPostsByOrganizationWithMediaParams) ([]ListPublishedPostsByOrganizationWithMediaRow, error) {
	rows, err := q.db.Query(ctx, listPublishedPostsByOrganizationWithMedia, arg.OrganizationID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPublishedPostsByOrganizationWithMediaRow{}
	for rows.Next() {
		var i ListPublishedPostsByOrganizationWithMediaRow
		if err := rows.Scan(
			&i.Post.ID,
			&i.Post.OrganizationID,
			&i.Post.Title,
			&i.Post.Slug,
			&i.Post.Status,
			&i.Post.PublishedAt,
			&i.Post.CreatedAt,
			&i.Post.UpdatedAt,
			&i.Post.Content,
			&i.Post.AuthorID,
			&i.Post.UpdatedBy,
			&i.PostMediaItemID,
			&i.PostMediaItemPostID,
			&i.PostMediaItemFileName,
			&i.PostMediaItemFilePath,
			&i.PostMediaItemFileType,
			&i.PostMediaItemFileSize,
			&i.PostMediaItemUploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublishedPostsWithAuthor = `-- name: ListPublishedPostsWithAuthor :many
SELECT
    p.id,
    p.organization_id,
    p.title,
    p.slug,
    p.status,
    p.published_at,
    p.created_at,
    p.updated_at,
    p.content,
    p.author_id,
    u.display_name AS author_display_name,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', pt.id, 'name_en', pt.name_en, 'name_zh_hk', pt.name_zh_hk, 'name_zh_cn', pt.name_zh_cn, 'description_en', pt.description_en, 'description_zh_hk', pt.description_zh_hk, 'description_zh_cn', pt.description_zh_cn)) FILTER (WHERE pt.id IS NOT NULL), '[]') AS tags
FROM posts p
JOIN users u ON p.author_id = u.id
LEFT JOIN post_post_tags ppt ON p.id = ppt.post_id
LEFT JOIN post_tags pt ON ppt.tag_id = pt.id
WHERE p.status = 'published'
  AND (
    ($1::UUID IS NULL AND $2::UUID IS NULL) OR
    ($1::UUID IS NOT NULL AND p.organization_id = $1::UUID) OR
    ($2::UUID IS NOT NULL AND p.organization_id = $2::UUID)
  )
  AND ($3::TIMESTAMPTZ IS NULL OR p.published_at >= $3::TIMESTAMPTZ)
  AND ($4::TIMESTAMPTZ IS NULL OR p.published_at <= $4::TIMESTAMPTZ)
  AND ($5::TEXT IS NULL OR p.title ILIKE '%' || $5::TEXT || '%')
  AND (cardinality($6::UUID[]) = 0 OR
       EXISTS (SELECT 1 FROM post_post_tags e_ppt_filter WHERE e_ppt_filter.post_id = p.id AND e_ppt_filter.tag_id = ANY($6::UUID[]))
      )
GROUP BY p.id, u.display_name
ORDER BY p.published_at DESC
LIMIT $8 OFFSET $7
`

type ListPublishedPostsWithAuthorParams struct {
	OrganizationID  *uuid.UUID  `db:"organization_id" json:"organization_id"`
	OrganizationId2 *uuid.UUID  `db:"organization_id2" json:"organization_id2"`
	StartDate       *time.Time  `db:"start_date" json:"start_date"`
	EndDate         *time.Time  `db:"end_date" json:"end_date"`
	SearchTerm      *string     `db:"search_term" json:"search_term"`
	TagIds          []uuid.UUID `db:"tag_ids" json:"tag_ids"`
	OffsetVal       int32       `db:"offset_val" json:"offset_val"`
	LimitVal        int32       `db:"limit_val" json:"limit_val"`
}

type ListPublishedPostsWithAuthorRow struct {
	ID                uuid.UUID   `db:"id" json:"id"`
	OrganizationID    uuid.UUID   `db:"organization_id" json:"organization_id"`
	Title             string      `db:"title" json:"title"`
	Slug              string      `db:"slug" json:"slug"`
	Status            string      `db:"status" json:"status"`
	PublishedAt       *time.Time  `db:"published_at" json:"published_at"`
	CreatedAt         time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time   `db:"updated_at" json:"updated_at"`
	Content           []byte      `db:"content" json:"content"`
	AuthorID          uuid.UUID   `db:"author_id" json:"author_id"`
	AuthorDisplayName string      `db:"author_display_name" json:"author_display_name"`
	Tags              interface{} `db:"tags" json:"tags"`
}

func (q *Queries) ListPublishedPostsWithAuthor(ctx context.Context, arg ListPublishedPostsWithAuthorParams) ([]ListPublishedPostsWithAuthorRow, error) {
	rows, err := q.db.Query(ctx, listPublishedPostsWithAuthor,
		arg.OrganizationID,
		arg.OrganizationId2,
		arg.StartDate,
		arg.EndDate,
		arg.SearchTerm,
		arg.TagIds,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPublishedPostsWithAuthorRow{}
	for rows.Next() {
		var i ListPublishedPostsWithAuthorRow
		if err := rows.Scan(
			&i.ID,
			&i.OrganizationID,
			&i.Title,
			&i.Slug,
			&i.Status,
			&i.PublishedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Content,
			&i.AuthorID,
			&i.AuthorDisplayName,
			&i.Tags,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPublishedPostsWithMedia = `-- name: ListPublishedPostsWithMedia :many
SELECT
    p.id, p.organization_id, p.title, p.slug, p.status, p.published_at, p.created_at, p.updated_at, p.content, p.author_id, p.updated_by,
    pm.id AS post_media_item_id,
    pm.post_id AS post_media_item_post_id,
    pm.file_name AS post_media_item_file_name,
    pm.file_path AS post_media_item_file_path,
    pm.file_type AS post_media_item_file_type,
    pm.file_size AS post_media_item_file_size,
    pm.uploaded_at AS post_media_item_uploaded_at
FROM
    posts p
LEFT JOIN
    post_media_items pm ON p.id = pm.post_id
WHERE
    p.status = 'published'
ORDER BY
    p.published_at DESC
LIMIT $1 OFFSET $2
`

type ListPublishedPostsWithMediaParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

type ListPublishedPostsWithMediaRow struct {
	Post                    Post       `db:"post" json:"post"`
	PostMediaItemID         *uuid.UUID `db:"post_media_item_id" json:"post_media_item_id"`
	PostMediaItemPostID     *uuid.UUID `db:"post_media_item_post_id" json:"post_media_item_post_id"`
	PostMediaItemFileName   *string    `db:"post_media_item_file_name" json:"post_media_item_file_name"`
	PostMediaItemFilePath   *string    `db:"post_media_item_file_path" json:"post_media_item_file_path"`
	PostMediaItemFileType   *string    `db:"post_media_item_file_type" json:"post_media_item_file_type"`
	PostMediaItemFileSize   *int64     `db:"post_media_item_file_size" json:"post_media_item_file_size"`
	PostMediaItemUploadedAt *time.Time `db:"post_media_item_uploaded_at" json:"post_media_item_uploaded_at"`
}

func (q *Queries) ListPublishedPostsWithMedia(ctx context.Context, arg ListPublishedPostsWithMediaParams) ([]ListPublishedPostsWithMediaRow, error) {
	rows, err := q.db.Query(ctx, listPublishedPostsWithMedia, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPublishedPostsWithMediaRow{}
	for rows.Next() {
		var i ListPublishedPostsWithMediaRow
		if err := rows.Scan(
			&i.Post.ID,
			&i.Post.OrganizationID,
			&i.Post.Title,
			&i.Post.Slug,
			&i.Post.Status,
			&i.Post.PublishedAt,
			&i.Post.CreatedAt,
			&i.Post.UpdatedAt,
			&i.Post.Content,
			&i.Post.AuthorID,
			&i.Post.UpdatedBy,
			&i.PostMediaItemID,
			&i.PostMediaItemPostID,
			&i.PostMediaItemFileName,
			&i.PostMediaItemFilePath,
			&i.PostMediaItemFileType,
			&i.PostMediaItemFileSize,
			&i.PostMediaItemUploadedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const setBannerForPostMediaItem = `-- name: SetBannerForPostMediaItem :one
UPDATE post_media_items
SET is_banner = TRUE
WHERE id = $1 AND post_id = $2
RETURNING id, post_id, file_name, file_path, file_type, file_size, uploaded_at, is_banner
`

type SetBannerForPostMediaItemParams struct {
	ID     uuid.UUID `db:"id" json:"id"`
	PostID uuid.UUID `db:"post_id" json:"post_id"`
}

func (q *Queries) SetBannerForPostMediaItem(ctx context.Context, arg SetBannerForPostMediaItemParams) (PostMediaItem, error) {
	row := q.db.QueryRow(ctx, setBannerForPostMediaItem, arg.ID, arg.PostID)
	var i PostMediaItem
	err := row.Scan(
		&i.ID,
		&i.PostID,
		&i.FileName,
		&i.FilePath,
		&i.FileType,
		&i.FileSize,
		&i.UploadedAt,
		&i.IsBanner,
	)
	return i, err
}

const setPostStatusToPublished = `-- name: SetPostStatusToPublished :exec
UPDATE posts SET status = 'published', updated_at = NOW() WHERE id = $1
`

func (q *Queries) SetPostStatusToPublished(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, setPostStatusToPublished, id)
	return err
}

const unsetBannerForPostMediaItems = `-- name: UnsetBannerForPostMediaItems :exec

UPDATE post_media_items
SET is_banner = FALSE
WHERE post_id = $1 AND is_banner = TRUE
`

// Order by post_id for easier grouping, then by banner status and uploaded_at
func (q *Queries) UnsetBannerForPostMediaItems(ctx context.Context, postID uuid.UUID) error {
	_, err := q.db.Exec(ctx, unsetBannerForPostMediaItems, postID)
	return err
}

const updatePost = `-- name: UpdatePost :one
UPDATE posts
SET
    title = COALESCE($1, title),
    slug = COALESCE($2, slug),
    content = COALESCE($3, content),
    status = COALESCE($4, status),
    published_at = COALESCE($5, published_at),
    updated_by = $6,
    updated_at = now()
WHERE id = $7
RETURNING id, organization_id, title, slug, status, published_at, created_at, updated_at, content, author_id, updated_by
`

type UpdatePostParams struct {
	Title       *string    `db:"title" json:"title"`
	Slug        *string    `db:"slug" json:"slug"`
	Content     []byte     `db:"content" json:"content"`
	Status      *string    `db:"status" json:"status"`
	PublishedAt *time.Time `db:"published_at" json:"published_at"`
	UpdatedBy   *uuid.UUID `db:"updated_by" json:"updated_by"`
	ID          uuid.UUID  `db:"id" json:"id"`
}

func (q *Queries) UpdatePost(ctx context.Context, arg UpdatePostParams) (Post, error) {
	row := q.db.QueryRow(ctx, updatePost,
		arg.Title,
		arg.Slug,
		arg.Content,
		arg.Status,
		arg.PublishedAt,
		arg.UpdatedBy,
		arg.ID,
	)
	var i Post
	err := row.Scan(
		&i.ID,
		&i.OrganizationID,
		&i.Title,
		&i.Slug,
		&i.Status,
		&i.PublishedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Content,
		&i.AuthorID,
		&i.UpdatedBy,
	)
	return i, err
}
