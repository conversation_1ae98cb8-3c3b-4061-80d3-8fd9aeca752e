-- Drop triggers first if they were specific to these tables and not general
DO $$
DECLARE
    t_name TEXT;
BEGIN
    FOR t_name IN
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = current_schema()
          AND (table_name LIKE 'verification_%' OR table_name = 'user_verification_requests')
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS set_timestamp ON %I;', t_name);
    END LOOP;
END;
$$;

-- Drop the trigger function if it's no longer needed by other tables
-- For safety, check if it exists and is only used by these tables before dropping.
-- For simplicity here, we'll drop it. If it's a shared function, this might need adjustment.
DROP FUNCTION IF EXISTS trigger_set_timestamp();

-- Drop specific verification type tables
DROP TABLE IF EXISTS verification_student_ids;
DROP TABLE IF EXISTS verification_address_proofs;
DROP TABLE IF EXISTS verification_hk_youth_plus;
DROP TABLE IF EXISTS verification_passports;
DROP TABLE IF EXISTS verification_home_return_permits;
DROP TABLE IF EXISTS verification_mainland_china_id_cards;
DROP TABLE IF EXISTS verification_hk_id_cards;

-- Drop the central user_verification_requests table
DROP TABLE IF EXISTS user_verification_requests;

-- Drop the verification_documents table
DROP TABLE IF EXISTS verification_documents;

-- Drop ENUM types
DROP TYPE IF EXISTS verification_status_enum;
DROP TYPE IF EXISTS verification_type_enum; 