package payloads

import "errors"

// Errors related to specific business logic, suitable for errors.Is()
var (
	ErrOrgNotFound            = errors.New("organization not found")
	ErrNotMember              = errors.New("user is not a member of this organization")
	ErrCannotDeleteDefaultOrg = errors.New("cannot delete the default organization")
	ErrAlreadyMember          = errors.New("user is already a member of this organization")
	ErrCannotRemoveOwner      = errors.New("organization owner cannot be removed. Transfer ownership first")

	// General purpose errors that can be used by services and handlers
	// These are intended for use with errors.Is() or direct comparison if simple.
	ErrUnauthorizedError = errors.New("user is not authorized to perform this action")
	ErrValidationError   = errors.New("input validation failed")
)

// General purpose error structs that can be instantiated with custom messages.
// These are for errors where you want to return a struct satisfying the error interface.

// NotFoundError is used when a resource is not found.
// To be used as: &payloads.NotFoundError{Message: "..."}
type NotFoundError struct {
	Message string
}

func (e *NotFoundError) Error() string {
	return e.Message
}

// ConflictError is used when an action cannot be performed due to a conflict with the current state of the resource.
// To be used as: &payloads.ConflictError{Message: "..."}
type ConflictError struct {
	Message string
}

func (e *ConflictError) Error() string {
	return e.Message
}

// IllegalArgumentError is used for invalid input parameters.
// To be used as: &payloads.IllegalArgumentError{Message: "..."}
type IllegalArgumentError struct {
	Message string
}

func (e *IllegalArgumentError) Error() string {
	return e.Message
}

// ForbiddenError is used when a user is authenticated but not allowed to access a resource.
// To be used as: &payloads.ForbiddenError{Message: "..."}
type ForbiddenError struct {
	Message string
}

func (e *ForbiddenError) Error() string {
	return e.Message
}

// FileProcessingError indicates an issue with file upload/processing.
// To be used as: &payloads.FileProcessingError{Message: "..."}
type FileProcessingError struct {
	Message string
}

func (e *FileProcessingError) Error() string {
	return e.Message
}
