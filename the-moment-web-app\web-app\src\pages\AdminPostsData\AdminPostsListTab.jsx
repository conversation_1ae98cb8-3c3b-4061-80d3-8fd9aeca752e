import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Table, Button, Space, Badge, App, Tag, Input, Select, DatePicker } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { formatDate } from '../../utils/dateFormatter';
import { postService } from '../../services/postService'; // To be created
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { useAuth } from '../../contexts/AuthContext';
import { useUser } from '../../contexts/UserContext';
import ErrorPage from '../ErrorPage';
import { SearchOutlined, UserOutlined, TagOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import '../../styles/TableBorder.css';

dayjs.extend(utc);
dayjs.extend(timezone);

const DEFAULT_TIMEZONE = 'Asia/Hong_Kong';

const { RangePicker } = DatePicker;
const { Option } = Select;

// Debounce helper function
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    // Set debouncedValue to value after the specified delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Cancel the timeout if value changes or unmounts
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

const AdminPostsListTab = ({ isActive = true }) => {
  const { message } = App.useApp();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({});
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { currentOrganization } = useOrganization();
  const { user: authUser } = useAuth();
  const { currentUser } = useUser();
  const isSuperAdmin = currentUser?.role === 'super_admin';
  const [showErrorPage, setShowErrorPage] = useState(false);
  const pageSize = 100; // Default page size for posts
  const isFirstRender = useRef(true);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const [dateRange, setDateRange] = useState(null);
  const [availableTags, setAvailableTags] = useState([]);
  const [loadingTags, setLoadingTags] = useState(false);
  const [selectedTag, setSelectedTag] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState(null);

  // Check for access control when organization changes
  useEffect(() => {
    if (currentOrganization?.id === ALL_ORGANIZATION_ID && !isSuperAdmin) {
      message.info(t('messages.notAvailableForAllOrgs'));
      setShowErrorPage(true);
    } else {
      setShowErrorPage(false);
    }
  }, [currentOrganization, isSuperAdmin, message, t]);

  // Generate available tags for the filter dropdown from API
  const generateAvailableFilterTags = useCallback(async () => {
    setLoadingTags(true);
    try {
      const tagsResponse = await postService.listPostTags();
      const tagsArray = Array.isArray(tagsResponse) ? tagsResponse : [];
      
      const newTags = tagsArray
        .filter(tag => tag.id && (tag.name_en || tag.name_zh_hk || tag.name_zh_cn))
        .map(tag => ({
          key: tag.id,
          value: tag.id,
          label: getTagName(tag, i18n.language),
        }))
        .filter((tag, index, self) => // Deduplicate by label
          index === self.findIndex((t) => t.label === tag.label)
        )
        .sort((a, b) => a.label.localeCompare(b.label)); // Sort alphabetically

      setAvailableTags(newTags);
    } catch (error) {
      console.error("Failed to fetch post tags for filter dropdown:", error);
      message.error(t('adminPosts.messages.fetchTagsError', "Failed to load tags for filter."));
      setAvailableTags([]);
    } finally {
      setLoadingTags(false);
    }
  }, [i18n.language, t, message]);

  // Generate tags on component mount or when language changes
  useEffect(() => {
    generateAvailableFilterTags();
  }, [generateAvailableFilterTags]);

  // Update filters when individual filter values change
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    const newFilters = {};
    if (debouncedSearchTerm) newFilters.search_term = debouncedSearchTerm;
    if (dateRange && dateRange[0]) {
        newFilters.start_date = dayjs(dateRange[0]).tz(DEFAULT_TIMEZONE, true).startOf('day').utc().format();
    }
    if (dateRange && dateRange[1]) {
        newFilters.end_date = dayjs(dateRange[1]).tz(DEFAULT_TIMEZONE, true).endOf('day').utc().format();
    }
    if (selectedTag) newFilters.tag_ids = selectedTag;
    if (selectedStatus) newFilters.status = selectedStatus;

    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  }, [debouncedSearchTerm, dateRange, selectedTag, selectedStatus]);

  const fetchPosts = useCallback(async (page, currentFilters) => {
    if (!isActive) return;
    setLoading(true);
    try {
      // If not super admin and trying to access all organizations, don't fetch data
      if (currentOrganization?.id === ALL_ORGANIZATION_ID && !isSuperAdmin) {
        setPosts([]);
        setTotal(0);
        setLoading(false);
        return;
      }

      const params = {
        limit: pageSize,
        offset: (page - 1) * pageSize,
        ...currentFilters,
      };

      // Determine which API endpoint to call based on organization context
      let responseData;
      if (currentOrganization && currentOrganization.id !== ALL_ORGANIZATION_ID) {
        // Fetching posts for a specific organization (admin/management view)
        if (currentFilters.status) {
            params.status = currentFilters.status;
        }
        // Ensure tag_ids is correctly formatted if present
        if (currentFilters.tag_ids && !Array.isArray(currentFilters.tag_ids)) {
            params.tag_ids = [currentFilters.tag_ids];
        } else if (currentFilters.tag_ids) {
            params.tag_ids = currentFilters.tag_ids;
        }
        responseData = await postService.listOrganizationPosts(currentOrganization.id, params);

      } else {
        // For super_admin viewing "All Organizations", use listOrganizationPosts with ALL_ORGANIZATION_ID.
        if (currentFilters.status) {
            params.status = currentFilters.status;
        }
        // Ensure tag_ids is correctly formatted if present
        if (currentFilters.tag_ids && !Array.isArray(currentFilters.tag_ids)) {
            params.tag_ids = [currentFilters.tag_ids];
        } else if (currentFilters.tag_ids) {
            params.tag_ids = currentFilters.tag_ids;
        }
        responseData = await postService.listOrganizationPosts(ALL_ORGANIZATION_ID, params);
      }

      if (responseData && responseData.posts) {
        setPosts(responseData.posts);
        setTotal(responseData.total || 0);
      } else {
        setPosts([]);
        setTotal(0);
        message.error(t('adminPosts.messages.fetchError'));
      }
    } catch (error) {
      console.error('Error fetching posts:', error);
      message.error(t('adminPosts.messages.fetchError') + (error.message ? `: ${error.message}` : ''));
      setPosts([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  }, [isActive, pageSize, currentOrganization, message, t, isSuperAdmin]);

  useEffect(() => {
    // Initial fetch or when filters/page/organization change
    fetchPosts(currentPage, filters);
  }, [currentPage, filters, fetchPosts, currentOrganization]); // Added currentOrganization

  const handleDateRangeChange = useCallback((dates) => {
    setDateRange(dates);
  }, []);

  const handleTagChange = useCallback((value) => {
    setSelectedTag(value);
  }, []);

  const handleStatusChange = useCallback((value) => {
    setSelectedStatus(value);
  }, []);

  // Define date presets
  const datePresets = [
    { label: t('postList.dateFilter.allDates'), value: null },
    { label: t('postList.dateFilter.last7Days'), value: [dayjs().subtract(7, 'day'), dayjs()] },
    { label: t('postList.dateFilter.last30Days'), value: [dayjs().subtract(30, 'day'), dayjs()] },
    { label: t('postList.dateFilter.thisMonth'), value: [dayjs().startOf('month'), dayjs().endOf('month')] },
    { label: t('postList.dateFilter.lastMonth'), value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] },
  ];

  // Handle row click to navigate to post details
  const handleRowClick = (record) => {
    return {
      onClick: () => {
        navigate(`/posts-management/${record.id}`);
      },
      style: { cursor: 'pointer' }
    };
  };

  // Helper function to get tag name based on current language
  const getTagName = (tag, language) => {
    if (!tag) return '';
    if (language.startsWith('zh')) {
      if (language.includes('HK')) {
        return tag.name_zh_hk || tag.name_en || '';
      } else {
        return tag.name_zh_cn || tag.name_en || '';
      }
    } else {
      return tag.name_en || '';
    }
  };

  // Columns for the posts table
  const columns = [
    {
      title: t('adminPosts.list.columns.title'),
      dataIndex: 'title',
      key: 'title',
      sorter: (a, b) => {
        const titleA = (a.title || '').toLowerCase();
        const titleB = (b.title || '').toLowerCase();
        return titleA.localeCompare(titleB);
      },
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={t('adminPosts.list.filters.searchTitle')}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              size="small"
              style={{ width: 90 }}
            >
              {t('common.search')}
            </Button>
            <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
              {t('common.reset')}
            </Button>
          </Space>
        </div>
      ),
      onFilter: (value, record) => {
        const title = record.title || '';
        return title.toLowerCase().includes(value.toLowerCase());
      },
    },
    // {
    //   title: t('adminPosts.list.columns.organization'),
    //   dataIndex: 'organization_id',
    //   key: 'organization_id',
    //   width: '15%',
    //   render: (_, record) => record?.name || record.organization_id || t('common.unknown')
    // },
    {
      title: (
        <>
          <UserOutlined /> {t('adminPosts.list.columns.author')}
        </>
      ),
      dataIndex: 'author_display_name',
      key: 'author_display_name',
      width: 150,
      sorter: (a, b) => {
        const authorA = (a.author_display_name || '').toLowerCase();
        const authorB = (b.author_display_name || '').toLowerCase();
        return authorA.localeCompare(authorB);
      },
    },
    {
      title: t('adminPosts.list.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      filters: [
        { text: t('adminPosts.status.published'), value: 'published' },
        { text: t('adminPosts.status.draft'), value: 'draft' },
        { text: t('adminPosts.status.hidden'), value: 'hidden' },
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => (
        <Badge
          status={status === 'published' ? 'success' : (status === 'draft' ? 'processing' : (status === 'hidden' ? 'warning' : 'default'))}
          text={t(`adminPosts.status.${status}`, status)}
        />
      ),
    },
    {
      title: t('adminPosts.list.columns.publishedAt'),
      dataIndex: 'published_at',
      key: 'published_at',
      width: 180,
      sorter: (a, b) => {
        const dateA = new Date(a.published_at || 0);
        const dateB = new Date(b.published_at || 0);
        return dateA.getTime() - dateB.getTime();
      },
      render: (date) => (date ? formatDate(date, i18n.language) : '-'),
    },
    {
      title: t('adminPosts.list.columns.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      sorter: (a, b) => {
        const dateA = new Date(a.created_at || 0);
        const dateB = new Date(b.created_at || 0);
        return dateA.getTime() - dateB.getTime();
      },
      render: (date) => formatDate(date, i18n.language),
    },
    {
      title: (
        <>
          <TagOutlined /> {t('adminPosts.list.columns.tags')}
        </>
      ),
      dataIndex: 'tags',
      key: 'tags',
      width: 'auto',
      sorter: (a, b) => {
        const getTagsText = (tags) => {
          if (!tags || tags.length === 0) return '';
          return tags.map(tag => getTagName(tag, i18n.language)).join(', ').toLowerCase();
        };
        const tagsA = getTagsText(a.tags);
        const tagsB = getTagsText(b.tags);
        return tagsA.localeCompare(tagsB);
      },
      render: (tags) => {
        if (!tags || tags.length === 0) return null;
        return (
          <>
            {tags.map(tag => (
                getTagName(tag, i18n.language)
            ))}
          </>
        );
      },
    },
    {
      title: t('adminPosts.list.columns.actions'),
      key: 'actions',
      fixed: 'right',
      width: 100,
      render: (_, record) => (
        <Space size="middle">
          <Button onClick={(e) => { e.stopPropagation(); navigate(`/posts-management/${record.id}/edit?returnTo=management`); }}>{t('common.actions.edit')}</Button>
        </Space>
      ),
    },
  ];

  if (showErrorPage) {
    return <ErrorPage type="403" />;
  }

  return (
    <div style={{ padding: '10px 24px 42px' }}>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="w-full md:w-auto flex-grow">
          <Space wrap size={8} className="w-full">
            <Input
              placeholder={t('postList.search.placeholder')}
              prefix={<SearchOutlined />}
              onChange={e => setSearchTerm(e.target.value)}
              size='large'
              allowClear
              style={{ width: '100%', maxWidth: 300 }}
            />
            <RangePicker
              presets={datePresets}
              value={dateRange}
              onChange={handleDateRangeChange}
              allowClear
              style={{ width: 280 }}
              size='large'
              format="YYYY-MM-DD"
            />
            <Select
              allowClear
              showSearch={false}
              style={{ width: 240 }}
              placeholder={t('postList.tags.placeholder')}
              onChange={handleTagChange}
              value={selectedTag}
              loading={loadingTags}
              size='large'
              optionLabelProp="label"
            >
              {availableTags.map(option => (
                <Option key={option.key} value={option.value} label={option.label}>
                  {option.label}
                </Option>
              ))}
            </Select>
            <Select
              allowClear
              style={{ width: 200 }}
              placeholder={t('adminPosts.filter.statusPlaceholder')}
              onChange={handleStatusChange}
              value={selectedStatus}
              size='large'
            >
              <Option value="published">{t('adminPosts.status.published')}</Option>
              <Option value="draft">{t('adminPosts.status.draft')}</Option>
              <Option value="hidden">{t('adminPosts.status.hidden')}</Option>
            </Select>
          </Space>
        </div>
        <div className="w-full md:w-auto">
          <Button
            type="dashed"
            size="large"
            onClick={() => navigate('/posts/create?returnTo=management')}
            style={{ width: '100%', minWidth: '140px' }}
          >
            {t('adminPosts.buttons.create', 'Create Post')}
          </Button>
        </div>
      </div>
      <div className="border rounded-lg overflow-hidden">
        <Table
          loading={loading}
          columns={columns.map(col => ({ ...col, title: col.key === 'organization_id' && currentOrganization?.id !== ALL_ORGANIZATION_ID ? t('adminPosts.list.columns.organization') : col.title }))}
          dataSource={posts.map(post => ({ ...post, key: post.id }))}
          rowKey="id"
          pagination={{
            pageSize: pageSize,
            current: currentPage,
            total: total,
            onChange: (page) => setCurrentPage(page),
            position: ['bottomCenter'],
            showTotal: (total) => t('common.totalItems', { total: total }),
          }}
          onRow={handleRowClick}
          scroll={{ x: 'max-content' }}
          style={{ overflowX: 'auto' }}
          className="no-pagination-table"
        />
      </div>
    </div>
  );
};

export default AdminPostsListTab; 