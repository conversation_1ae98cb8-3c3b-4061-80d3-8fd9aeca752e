-- name: GetOTPAttempt :one
SELECT * 
FROM otp_attempts
WHERE phone = $1;

-- name: UpsertOTPAttempt :one
INSERT INTO otp_attempts (phone, attempt_count, last_attempt_at, locked_until)
VALUES ($1, 1, NOW(), NULL)
ON CONFLICT (phone) 
DO UPDATE SET 
    attempt_count = otp_attempts.attempt_count + 1,
    last_attempt_at = NOW(),
    -- Only update locked_until if explicitly set by a lock operation, otherwise keep existing or NULL
    locked_until = CASE WHEN otp_attempts.locked_until > NOW() THEN otp_attempts.locked_until ELSE NULL END
WHERE otp_attempts.phone = $1
RETURNING *;

-- name: LockOTPAttempts :one
UPDATE otp_attempts
SET locked_until = $2, last_attempt_at = NOW()
WHERE phone = $1
RETURNING *;

-- name: ResetOTPAttempts :exec
DELETE FROM otp_attempts
WHERE phone = $1; 