/**
 * ///updatedAt:1548939200|Powered by Simple Clock. Check it out on https://github.com/knugi0123/SimpleClock///
 * ///Hong Kong ID Card Verification and Formatting///
 */

import i18next from 'i18next';

/**
 * Validates a Hong Kong Identity Card (HKID) number.
 * @param {string} id - The HKID number input by the user.
 * @returns {Object} - An object containing the validation result and an appropriate message.
 */
function validateHKID(id) {
  const t = i18next.t;
  
  // Remove all non-letter and non-digit characters and convert to uppercase
  const cleanedID = id.toUpperCase().replace(/[^A-Z0-9]/g, '');

  // Check if the length is either 8 or 9 characters
  if (cleanedID.length !== 8 && cleanedID.length !== 9) {
    return {
      valid: false,
      message: t('tools.hkidValidator.messages.lengthError')
    };
  }

  let letters = '';
  let numbers = '';
  let checkDigit = '';

  if (cleanedID.length === 8) {
    letters = cleanedID.charAt(0);
    numbers = cleanedID.substring(1, 7);
    checkDigit = cleanedID.charAt(7);
  } else if (cleanedID.length === 9) {
    letters = cleanedID.substring(0, 2);
    numbers = cleanedID.substring(2, 8);
    checkDigit = cleanedID.charAt(8);
  }

  // Validate the letters part using regex (only 1 or 2 uppercase letters)
  const letterRegex = /^[A-Z]{1,2}$/;
  if (!letterRegex.test(letters)) {
    return {
      valid: false,
      message: t('tools.hkidValidator.messages.invalidLetters')
    };
  }

  // Validate the numbers part using regex (exactly 6 digits)
  const numberRegex = /^\d{6}$/;
  if (!numberRegex.test(numbers)) {
    return {
      valid: false,
      message: t('tools.hkidValidator.messages.invalidNumbers')
    };
  }

  // Convert letters to numerical values (A=1, B=2, ..., Z=26)
  let convertedLetters = 0;
  for (let i = 0; i < letters.length; i++) {
    const ascii = letters.charCodeAt(i);
    if (ascii < 65 || ascii > 90) {
      return {
        valid: false,
        message: t('tools.hkidValidator.messages.invalidCharacters')
      };
    }
    convertedLetters += (ascii - 64) * (cleanedID.length === 9 ? (9 - i) : (8 - i));
  }

  // Calculate the weighted sum of the numbers part
  let productNsum = 0;
  for (let i = 0; i < numbers.length; i++) {
    const digit = parseInt(numbers.charAt(i), 10);
    if (isNaN(digit)) {
      return {
        valid: false,
        message: t('tools.hkidValidator.messages.invalidNumberCharacters')
      };
    }
    productNsum += digit * (7 - i);
  }

  productNsum += convertedLetters;

  let remainder = cleanedID.length === 9 ? (productNsum % 10) : (productNsum % 11);
  let expectedCheck;

  if (remainder !== 0) {
    expectedCheck = 11 - remainder;
    if (expectedCheck === 10) {
      expectedCheck = 'A';
    }
  } else {
    expectedCheck = '0';
  }

  expectedCheck = expectedCheck.toString();

  if (expectedCheck === checkDigit) {
    return {
      valid: true,
      message: t('tools.hkidValidator.messages.valid')
    };
  } else {
    return {
      valid: false,
      message: t('tools.hkidValidator.messages.invalid')
    };
  }
}

/**
* Formats a valid Hong Kong Identity Card (HKID) number by adding parentheses around the check digit.
* @param {string} id - The HKID number input by the user.
* @returns {Object} - An object containing the formatted HKID or an error message.
*/
function formatHKID(id) {
  const t = i18next.t;
  const validation = validateHKID(id);

  if (!validation.valid) {
    return {
      success: false,
      message: validation.message,
      formattedID: null
    };
  }

  const cleanedID = id.toUpperCase().replace(/[^A-Z0-9]/g, '');

  let letters = '';
  let numbers = '';
  let checkDigit = '';

  if (cleanedID.length === 8) {
    letters = cleanedID.charAt(0);
    numbers = cleanedID.substring(1, 7);
    checkDigit = cleanedID.charAt(7);
    return {
      success: true,
      message: t('tools.hkidValidator.messages.valid'),
      formattedID: `${letters}${numbers}(${checkDigit})`
    };
  } else if (cleanedID.length === 9) {
    letters = cleanedID.substring(0, 2);
    numbers = cleanedID.substring(2, 8);
    checkDigit = cleanedID.charAt(8);
    return {
      success: true,
      message: t('tools.hkidValidator.messages.valid'),
      formattedID: `${letters}${numbers}(${checkDigit})`
    };
  } else {
    return {
      success: false,
      message: t('tools.hkidValidator.messages.formatError'),
      formattedID: null
    };
  }
}

/**
 * Removes formatting and check digit from a formatted HKID number
 * @param {string} id - The formatted HKID number
 * @returns {string} - The HKID number without check digit and formatting
 */
function removeHKIDFormat(id) {
  if (id.match(/^[A-Z]{1,2}\d{6}\(\w\)$/)) {
    return id;
  }
  
  if (id.includes('(')) {
    const baseID = id.split('(')[0];
    return baseID;
  }
  
  return id;
}

// Export the functions
export { validateHKID, formatHKID, removeHKIDFormat };
