/**
 * @typedef {object} FileDetails
 * @property {string} id - File ID (UUID)
 * @property {string} resource_id - UUID of the resource this file belongs to
 * @property {string} file_name - Original name of the file
 * @property {string} file_type - MIME type of the file
 * @property {number} file_size - Size of the file in bytes
 * @property {string} [url] - Optional URL to access/download the file if available
 * @property {string} [storage_path] - Internal storage path (usually not for frontend)
 * @property {string} uploaded_at - Timestamp of when the file was uploaded
 * @property {string} [description] - Optional description for the file
 */

/**
 * @typedef {object} Resource
 * @property {string} id - Resource ID (UUID)
 * @property {string} organization_id - UUID of the organization this resource belongs to
 * @property {string} title - Title of the resource
 * @property {string} slug - Auto-generated URL-friendly slug
 * @property {string} [description] - Optional description for the resource
 * @property {string} [category] - Optional category for the resource
 * @property {'public' | 'private' | 'members_only'} visibility - Visibility status of the resource
 * @property {'draft' | 'published' | 'archived'} status - Publication status of the resource
 * @property {string} created_at - Timestamp of when the resource was created
 * @property {string} updated_at - Timestamp of when the resource was updated
 * @property {FileDetails[]} [files] - Array of associated files (present in detail view)
 * @property {number} [files_count] - Count of associated files (present in list view)
 */

/**
 * @typedef {object} CreateResourcePayload
 * @property {string} title
 * @property {string} [description]
 * @property {string} [category]
 * @property {'public' | 'private' | 'members_only'} [visibility]
 * @property {'draft' | 'published' | 'archived'} [status]
 */

/**
 * @typedef {object} UpdateResourcePayload
 * @property {string} [title]
 * @property {string} [description]
 * @property {string} [category]
 * @property {'public' | 'private' | 'members_only'} [visibility]
 * @property {'draft' | 'published' | 'archived'} [status]
 */

// It's good practice to export something, even if it's just an empty object or a comment,
// to ensure the file is treated as a module.
// In this case, we don't need to export the JSDoc types themselves as they are globally available
// to the JSDoc parser once referenced.
export {}; 