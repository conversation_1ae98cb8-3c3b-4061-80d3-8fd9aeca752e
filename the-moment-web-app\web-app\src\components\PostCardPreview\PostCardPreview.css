.post-card-preview {
    width: 100%;
    max-width: 320px; /* Adjust as needed */
    border: 1px solid #f0f0f0;
    border-radius: 8px; /* This is for the overall card */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    overflow: hidden;
}

.post-card-preview .ant-card-cover {
    border-bottom: 1px solid #f0f0f0;
}

.preview-image-container {
    position: relative;
    width: 100%;
    height: 200px; /* Matched: Changed from 180px to 200px */
    background-color: #f5f5f5; /* Matched: Changed from #fafafa */
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* Added, good practice */
    /* Note: border-top-left-radius and border-top-right-radius for the image container itself 
       might conflict with the Card's cover styling. 
       The Card component usually handles the rounding of its cover. 
       If the image isn't rounding correctly, we might need to adjust how the AntD Card `cover` prop is styled or ensure the image itself rounds if it's not fitting the Card's rounding.
       For now, let's rely on the parent Card's border-radius for the corners. 
       The actual .post-image-container in PostListPage.css applies this to a simple div, 
       not an AntD Card cover slot, so behavior might differ slightly. */
}

.preview-image-container.grayscale .preview-actual-img { /* Adjusted for grayscale */
    filter: grayscale(100%);
}

.preview-actual-img { /* Styles for the new plain <img> tag */
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.preview-hidden-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5); /* Lighten the image slightly */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 8px;
    box-sizing: border-box;
}

.preview-hidden-text {
    margin-top: 4px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
}

/* Overlay for Draft Status */
.preview-draft-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* background-color: rgba(240, 242, 245, 0.3); Slightly different overlay if needed */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 8px;
    box-sizing: border-box;
}

.preview-draft-text {
    margin-top: 4px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
}

.post-card-preview .ant-card-body {
    padding: 12px 16px; /* Adjust padding */
}

.preview-title {
    margin-bottom: 0 !important; /* Override Ant Design's default margin */
    font-size: 1rem !important; /* Consistent title size */
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.preview-meta {
    margin-top: 8px;
    width: 100%; /* Ensure Space component takes full width if needed */
}

.preview-meta-item {
    display: flex;
    align-items: center;
    font-size: 0.8rem; /* Slightly smaller text for meta */
    color: #888;
}

.preview-meta-item .anticon {
    margin-right: 6px;
}

/* Status Tags */
.preview-status-tag {
    position: absolute;
    bottom: 12px; /* Adjust as needed */
    right: 12px;  /* Adjust as needed */
    font-weight: bold;
}

.draft-tag {
    /* Custom styles for draft tag if needed, e.g., specific background or border */
}

.hidden-tag {
    /* Custom styles for hidden tag if needed */
}

/* Styling for overall card when draft or hidden */
.post-card-preview.draft-preview {
    /* border-left: 4px solid #1890ff; /* Example: Blue border for drafts */
}

.post-card-preview.hidden-preview .ant-card-body {
    /* background-color: #fafafa; /* Slightly different body background for hidden posts */
} 