import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Table, Button, Space, Badge, App, Tag, Input, Select, DatePicker } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { formatDate } from '../../utils/dateFormatter';
import { resourceService } from '../../services/resourceService';
import { organizationService } from '../../services/organizationService';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { useAuth } from '../../contexts/AuthContext';
import { useUser } from '../../contexts/UserContext';
import ErrorPage from '../ErrorPage';
import { SearchOutlined, EyeOutlined, FileOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { formatFileSize } from '../../utils/fileUtils';
import UploadResourceModal from '../ResourcePage/UploadResourceModal';
import '../../styles/TableBorder.css';

const { RangePicker } = DatePicker;
const { Option } = Select;

dayjs.extend(utc);
dayjs.extend(timezone);

const DEFAULT_TIMEZONE = 'Asia/Hong_Kong';

// Debounce helper function
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    // Set debouncedValue to value after the specified delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Cancel the timeout if value changes or unmounts
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Helper function to trigger file download from a blob
const triggerBlobDownload = (blobData, fileName, messageApi) => {
  try {
    const url = window.URL.createObjectURL(blobData);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    link.parentNode.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error in triggerBlobDownload:", error);
    if (messageApi) {
      messageApi.error('Failed to initiate download process.');
    }
  }
};

const AdminResourcesListTab = ({ isActive = true }) => {
  const { message, modal } = App.useApp();
  const [resources, setResources] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({});
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { currentOrganization } = useOrganization();
  const { user: authUser } = useAuth();
  const { currentUser } = useUser();
  const isSuperAdmin = currentUser?.role === 'super_admin';
  const [showErrorPage, setShowErrorPage] = useState(false);
  const isAdmin = authUser?.role === 'admin' || authUser?.role === 'super_admin';
  const pageSize = 100; // Default page size for resources
  const isFirstRender = useRef(true);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]); // Add state for tracking expanded rows

  // Check for access control when organization changes
  useEffect(() => {
    if (currentOrganization?.id === ALL_ORGANIZATION_ID && !isSuperAdmin) {
      message.info(t('messages.notAvailableForAllOrgs'));
      setShowErrorPage(true);
    } else {
      setShowErrorPage(false);
    }
  }, [currentOrganization, isSuperAdmin, message, t]);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const [dateRange, setDateRange] = useState(null);
  const [selectedVisibility, setSelectedVisibility] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState(null);

  // Modal states
  const [modalData, setModalData] = useState({
    visible: false,
    mode: 'create',
    initialData: null,
    orgId: null
  });
  const [modalLoading, setModalLoading] = useState(false);

  // Update filters when individual filter values change
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    const newFilters = {};
    if (debouncedSearchTerm) newFilters.search_term = debouncedSearchTerm;
    if (dateRange && dateRange[0]) {
        newFilters.start_date = dayjs(dateRange[0]).tz(DEFAULT_TIMEZONE, true).startOf('day').utc().format();
    }
    if (dateRange && dateRange[1]) {
        newFilters.end_date = dayjs(dateRange[1]).tz(DEFAULT_TIMEZONE, true).endOf('day').utc().format();
    }
    if (selectedVisibility) newFilters.visibility = selectedVisibility;
    if (selectedStatus) newFilters.status = selectedStatus;

    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  }, [debouncedSearchTerm, dateRange, selectedVisibility, selectedStatus]);

  const fetchResources = useCallback(async (page, currentFilters) => {
    if (!isActive) return;
    setLoading(true);
    try {
      // If not super admin and trying to access all organizations, don't fetch data
      if (currentOrganization?.id === ALL_ORGANIZATION_ID && !isSuperAdmin) {
        setResources([]);
        setTotal(0);
        setLoading(false);
        return;
      }

      const params = {
        limit: pageSize,
        offset: (page - 1) * pageSize,
        ...currentFilters,
      };

      // Determine which API endpoint to call based on organization context
      let responseData;
      if (currentOrganization && currentOrganization.id !== ALL_ORGANIZATION_ID) {
        responseData = await organizationService.listOrgResources(currentOrganization.id, params);
      } else {
        responseData = await organizationService.listOrgResources(ALL_ORGANIZATION_ID, params);
      }

      let resourcesList = [];
      if (responseData && responseData.resources) {
        resourcesList = responseData.resources;
        setTotal(responseData.total || 0);
      } else if (Array.isArray(responseData)) {
        resourcesList = responseData;
        setTotal(responseData.length);
      } else {
        setResources([]);
        setTotal(0);
        message.error(t('messages.fetchError'));
        return;
      }

      // Fetch detailed information for each resource
      if (resourcesList.length > 0) {
        const detailPromises = resourcesList.map(resource =>
          organizationService.getOrgResourceDetail(
            resource.organization_id || currentOrganization?.id || ALL_ORGANIZATION_ID,
            resource.id
          )
            .then(detailResponse => detailResponse.data || detailResponse)
            .catch(err => {
              console.error(`Failed to fetch details for resource ${resource.id}:`, err);
              return { ...resource, files: [], errorFetchingDetails: true };
            })
        );

        const detailedResources = await Promise.all(detailPromises);
        console.log('detailedResources', detailedResources);
        setResources(detailedResources.filter(r => r !== null));
      } else {
        setResources([]);
      }
    } catch (error) {
      console.error('Error fetching resources:', error);
      message.error(t('messages.fetchError') + (error.message ? `: ${error.message}` : ''));
      setResources([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  }, [isActive, pageSize, currentOrganization, message, t, isSuperAdmin]);

  useEffect(() => {
    // Initial fetch or when filters/page/organization change
    fetchResources(currentPage, filters);
  }, [currentPage, filters, fetchResources, currentOrganization]);

  const handleDateRangeChange = useCallback((dates) => {
    setDateRange(dates);
  }, []);

  const handleVisibilityChange = useCallback((value) => {
    setSelectedVisibility(value);
  }, []);

  const handleStatusChange = useCallback((value) => {
    setSelectedStatus(value);
  }, []);

  // Define date presets
  const datePresets = [
    { label: t('resourceList.dateFilter.allDates'), value: null },
    { label: t('resourceList.dateFilter.last7Days'), value: [dayjs().subtract(7, 'day'), dayjs()] },
    { label: t('resourceList.dateFilter.last30Days'), value: [dayjs().subtract(30, 'day'), dayjs()] },
    { label: t('resourceList.dateFilter.thisMonth'), value: [dayjs().startOf('month'), dayjs().endOf('month')] },
    { label: t('resourceList.dateFilter.lastMonth'), value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] },
  ];

  const handleDelete = async (resourceItem) => {
    const resourceId = resourceItem.id;
    const resourceOrgId = resourceItem.organization_id;

    modal.confirm({
      title: t('resourceList.messages.deleteResourceConfirmTitle', { resourceName: resourceItem.title }),
      content: t('resourceList.messages.deleteResourceConfirmContent'),
      okText: t('common.actions.delete'),
      okType: 'danger',
      cancelText: t('common.actions.cancel'),
      onOk: async () => {
        try {
          await organizationService.deleteResource(resourceOrgId, resourceId);
          message.success(t('resourceList.messages.deleteSuccess'));

          // After successful deletion, check if we need to adjust the page
          if (resources.length === 1 && currentPage > 1) {
            setCurrentPage(prevPage => prevPage - 1);
          } else {
            fetchResources(currentPage, filters);
          }
        } catch (error) {
          console.error('Error deleting resource:', error);
          message.error(t('resourceList.messages.deleteFailed'));
        }
      },
    });
  };

  const handleDownload = async (resourceItem) => {
    if (!resourceItem || !resourceItem.files || resourceItem.files.length === 0) {
      message.info(t('resourceList.noFilesToDownload'));
      return;
    }

    message.info(t('resourceList.messages.downloadAllFilesStarting', { count: resourceItem.files.length }));

    for (const file of resourceItem.files) {
      // Adding a small delay between downloads to be browser-friendly
      await new Promise(resolve => setTimeout(resolve, 300));
      try {
        message.loading({
          content: t('resourceList.messages.downloadStartingFor', { fileName: file.file_name }),
          key: `download-${file.id}`,
          duration: 0
        });

        console.log(`Downloading file: orgId=${resourceItem.organization_id}, resourceId=${resourceItem.id}, fileId=${file.id}`);
        const response = await resourceService.downloadResourceFile(
          resourceItem.organization_id,
          resourceItem.id,
          file.id
        );

        if (response && response.data) {
          triggerBlobDownload(response.data, file.file_name, message);
          message.success({
            content: t('resourceList.messages.downloadSuccessFor', { fileName: file.file_name }),
            key: `download-${file.id}`,
            duration: 3
          });
        } else if (response instanceof Blob) {
          triggerBlobDownload(response, file.file_name, message);
          message.success({
            content: t('resourceList.messages.downloadSuccessFor', { fileName: file.file_name }),
            key: `download-${file.id}`,
            duration: 3
          });
        } else {
          throw new Error('Invalid response data for file download.');
        }
      } catch (error) {
        console.error(`Error downloading file ${file.file_name}:`, error);
        message.error({
          content: t('resourceList.messages.downloadFailedFor', { fileName: file.file_name }),
          key: `download-${file.id}`,
          duration: 3
        });
      }
    }
  };

  const handleDownloadFile = async (e, record, file) => {
    e.stopPropagation(); // Prevent row expansion

    // Guard clause for missing data
    if (!file || !record) {
      message.error(t('resourceList.messages.downloadErrorMissingInfo'));
      return;
    }

    const fileName = file.file_name || 'unknownFile';

    try {
      message.loading({
        content: t('resourceList.messages.downloadStartingFor', { fileName: fileName }),
        key: `download-${file.id}`,
        duration: 0
      });

      console.log(`Downloading file: orgId=${record.organization_id}, resourceId=${record.id}, fileId=${file.id}`);
      const response = await resourceService.downloadResourceFile(
        record.organization_id,
        record.id,
        file.id
      );

      if (response && response.data) {
        triggerBlobDownload(response.data, fileName, message);
        message.success({
          content: t('resourceList.messages.downloadSuccessFor', { fileName: fileName }),
          key: `download-${file.id}`,
          duration: 3
        });
      } else if (response instanceof Blob) {
        triggerBlobDownload(response, fileName, message);
        message.success({
          content: t('resourceList.messages.downloadSuccessFor', { fileName: fileName }),
          key: `download-${file.id}`,
          duration: 3
        });
      } else {
        throw new Error('Invalid response data for file download.');
      }
    } catch (error) {
      console.error(`Error downloading file ${fileName}:`, error);
      message.error({
        content: t('resourceList.messages.downloadFailedFor', { fileName: fileName }),
        key: `download-${file.id}`,
        duration: 3
      });
    }
  };

  const handleDeleteFile = async (e, record, file) => {
    e.stopPropagation(); // Prevent row expansion

    // Guard clause for missing data
    if (!file || !record) {
      message.error(t('resourceList.messages.downloadErrorMissingInfo'));
      return;
    }

    if (!isAdmin) {
      message.error(t('resourceList.messages.unauthorized'));
      return;
    }

    modal.confirm({
      title: t('resourceList.messages.deleteFileConfirmTitle'),
      content: t('resourceList.messages.deleteFileConfirmContent', { fileName: file.file_name || 'unknownFile' }),
      okText: t('common.actions.delete'),
      okType: 'danger',
      cancelText: t('common.actions.cancel'),
      onOk: async () => {
        try {
          await organizationService.deleteResourceFile(record.organization_id, record.id, file.id);
          message.success(t('resourceList.messages.deleteFileSuccess', { fileName: file.file_name || 'unknownFile' }));
          fetchResources(currentPage, filters); // Refresh the data
        } catch (error) {
          console.error(`Error deleting file ${file.id}:`, error);
          message.error(t('resourceList.messages.deleteFileFailed', { fileName: file.file_name || 'unknownFile' }));
        }
      },
    });
  };

  const handleUpload = () => {
    if (!currentOrganization) {
      message.warning(t('resourceList.messages.selectOrgForUpload'));
      return;
    }
    setModalData({
      visible: true,
      mode: 'create',
      initialData: null,
      orgId: currentOrganization.id
    });
  };

  const handleEdit = (item) => {
    if (!currentOrganization) {
      message.warning(t('resourceList.messages.selectOrgForEdit'));
      return;
    }
    // Ensure editing happens in the context of the resource's own org or current if it matches
    const orgContextId = item.organization_id === currentOrganization.id ? currentOrganization.id : null;
    if (!orgContextId) {
      message.info(t('resourceList.messages.editContextWarning'));
    }

    setModalData({
      visible: true,
      mode: 'edit',
      initialData: item,
      orgId: item.organization_id || currentOrganization.id
    });
  };

  // File columns for the nested table
  const fileColumns = [
    {
      title: t('resourceList.fileColumns.fileName'),
      dataIndex: 'file_name',
      key: 'file_name',
      width: 250,
      render: (fileName) => fileName || '-',
    },
    {
      title: t('resourceList.fileColumns.fileType'),
      dataIndex: 'file_type',
      key: 'file_type',
      width: 120,
      render: (fileType) => fileType || '-',
    },
    {
      title: t('resourceList.fileColumns.fileSize'),
      dataIndex: 'file_size',
      key: 'file_size',
      width: 120,
      render: (size) => formatFileSize(size || 0),
    },
    {
      title: t('resourceList.fileColumns.uploadedAt'),
      dataIndex: 'uploaded_at',
      key: 'uploaded_at',
      width: 180,
      render: (date) => date ? formatDate(date, i18n.language) : '-',
    },
    {
      title: t('adminResources.list.columns.actions'),
      key: 'actions',
      width: 200,
      render: (_, file) => (
        <Space size="large">
          <a
            href="#"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (file && file.resource) {
                handleDownloadFile(e, file.resource, file);
              }
            }}
          >
            {t('common.actions.download')}
          </a>
          <button
            type="button"
            style={{ 
              color: '#ff4d4f', 
              background: 'none',
              border: 'none',
              padding: 0,
              font: 'inherit',
              cursor: 'pointer',
              textDecoration: 'underline'
            }}
            onClick={(e) => {
              e.stopPropagation();
              if (file && file.resource) {
                handleDeleteFile(e, file.resource, file);
              }
            }}
          >
            {t('common.actions.delete')}
          </button>
        </Space>
      ),
    },
  ];

  // Expanded row render function to show files for a resource
  const expandedRowRender = (record) => {
    if (!record.files || !Array.isArray(record.files) || record.files.length === 0) {
      return <p>{t('resourceList.noFilesAttached')}</p>;
    }

    // Add record to each file object for reference in action handlers
    const filesWithResource = record.files.map(file => ({
      ...file,
      resource: record, // Store parent record directly in each file object
      key: file.id || `file-${Math.random()}`,
    }));

    return (
      <Table
        columns={fileColumns}
        dataSource={filesWithResource}
        pagination={false}
        size="small"
        rowKey="key"
      />
    );
  };

  const handleModalComplete = async (values, intendedStatus) => {
    const isEdit = modalData.mode === 'edit';
    const orgId = modalData.orgId;
    let resourceId = isEdit ? modalData.initialData.id : null;

    if (!orgId) {
      message.error(t('resourceList.messages.invalidOrgContext'));
      setModalLoading(false);
      return;
    }

    try {
      setModalLoading(true);
      const minLoadingTime = new Promise(resolve => setTimeout(resolve, 500));

      let resourceResponseData;

      // Step 1: Create or Update Resource Metadata
      console.log('[ModalComplete] Step 1: Creating/Updating Resource Metadata', { isEdit, orgId, resourceId, valuesPassed: values, intendedStatus });
      if (isEdit) {
        const updatePayload = {
          title: values.title,
          description: values.description,
          visibility: values.visibility,
          status: intendedStatus,
        };
        if (intendedStatus === 'published') {
          if (values.published_at) {
            updatePayload.published_at = dayjs(values.published_at).tz(DEFAULT_TIMEZONE, true).utc().format();
          } else if (modalData.initialData?.status !== 'published') {
            updatePayload.published_at = dayjs().utc().format();
          }
        } else {
          updatePayload.published_at = null;
        }

        const response = await organizationService.updateResource(orgId, resourceId, updatePayload);
        resourceResponseData = response;
      } else {
        const createPayload = {
          title: values.title,
          description: values.description,
          visibility: values.visibility,
          status: intendedStatus,
          published_at: intendedStatus === 'published'
            ? (values.published_at 
                ? dayjs(values.published_at).tz(DEFAULT_TIMEZONE, true).utc().format() 
                : dayjs().utc().format())
            : null,
        };
        const response = await organizationService.createResource(orgId, createPayload);
        resourceResponseData = response;
        if (resourceResponseData && resourceResponseData.id) {
          resourceId = resourceResponseData.id;
        } else {
          console.error('[ModalComplete] Create resource response did not include an ID or was not structured as expected. Response was:', resourceResponseData);
          message.error(t('resourceList.messages.createFailedNoId'));
          setModalLoading(false);
          return;
        }
      }

      if (resourceId) {
        const uploadedFilesInfo = [];
        if (isEdit && modalData.initialData && modalData.initialData.files) {
          const initialFileIds = modalData.initialData.files.map(f => f.id.toString());
          const currentFileUids = values.files.map(f => f.uid ? f.uid.toString() : null).filter(Boolean);

          for (const initialFile of modalData.initialData.files) {
            if (!currentFileUids.includes(initialFile.id.toString())) {
              try {
                await organizationService.deleteResourceFile(orgId, resourceId, initialFile.id);
                message.info(t('resourceList.messages.fileRemoved', { fileName: initialFile.file_name || initialFile.fileName }));
              } catch (deleteError) {
                console.error(`Failed to delete file ${initialFile.id}:`, deleteError);
                message.error(t('resourceList.messages.deleteFileFailed', { fileName: initialFile.file_name || initialFile.fileName }));
              }
            }
          }
        }

        for (const file of values.files) {
          if (file.originFileObj instanceof File) {
            try {
              const uploadResponse = await organizationService.uploadResourceFile(orgId, resourceId, file.originFileObj);
              uploadedFilesInfo.push({ ...uploadResponse.data, localName: file.name });
              message.success(t('resourceList.messages.fileUploadSuccess', { fileName: file.name }));
            } catch (uploadError) {
              console.error(`[ModalComplete] FAILED to upload file ${file.name}:`, uploadError.response?.data || uploadError.message || uploadError);
              message.error(t('resourceList.messages.fileUploadFailed', { fileName: file.name }));
            }
          } else {
            console.log(`[ModalComplete] Skipping file (not a new upload or not a File instance): ${file.name}`);
          }
        }
      }

      await minLoadingTime;

      message.success(t(`resourceList.messages.${isEdit ? 'updateSuccess' : 'uploadSuccess'}` + (intendedStatus === 'draft' ? 'Draft' : '')));
      setModalData({ visible: false, mode: 'create', initialData: null, orgId: null });
      fetchResources(currentPage, filters);
    } catch (error) {
      let mainMessage = t(`resourceList.messages.${isEdit ? 'updateFailed' : 'uploadFailed'}`);
      if (error.response && error.response.data && error.response.data.message) {
        mainMessage += `: ${error.response.data.message}`;
      } else if (error.message) {
        mainMessage += `: ${error.message}`;
      } else if (typeof error === 'string') {
        mainMessage += `: ${error}`;
      }
      message.error(mainMessage);
      console.error('[ModalComplete] Error in modal complete (final catch):', error, error?.response?.data);
    } finally {
      setModalLoading(false);
    }
  };

  // Handle row click for expansion
  const handleRowClick = (record) => {
    if (!record.files || record.files.length === 0) return;
    
    const isExpanded = expandedRowKeys.includes(record.id);
    setExpandedRowKeys(isExpanded ? [] : [record.id]);
  };

  // Columns for the resources table
  const columns = [
    {
      title: t('adminResources.list.columns.title'),
      dataIndex: 'title',
      key: 'title',
      width: 200,
      sorter: (a, b) => {
        const titleA = (a.title || '').toLowerCase();
        const titleB = (b.title || '').toLowerCase();
        return titleA.localeCompare(titleB);
      },
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={t('adminResources.list.filters.searchTitle')}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              size="small"
              style={{ width: 90 }}
            >
              {t('common.search')}
            </Button>
            <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
              {t('common.reset')}
            </Button>
          </Space>
        </div>
      ),
      onFilter: (value, record) => {
        const title = record.title || '';
        return title.toLowerCase().includes(value.toLowerCase());
      },
    },
    // {
    //   title: t('adminResources.list.columns.organization'),
    //   dataIndex: 'organization_id',
    //   key: 'organization_id',
    //   width: '15%',
    //   render: (_, record) => record?.organization_name || record.organization_id || t('common.unknown')
    // },
    {
      title: (
        <>
          <EyeOutlined /> {t('adminResources.list.columns.visibility')}
        </>
      ),
      dataIndex: 'visibility',
      key: 'visibility',
      width: 120,
      filters: [
        { text: t('adminResources.visibility.public'), value: 'public' },
        { text: t('adminResources.visibility.org_only'), value: 'org_only' },
        { text: t('adminResources.visibility.private'), value: 'private' },
      ],
      onFilter: (value, record) => record.visibility === value,
      render: (visibility) => {
        let color = 'default';
        switch (visibility) {
          case 'public': color = 'green'; break;
          case 'org_only': color = 'blue'; break;
          case 'private': color = 'red'; break;
          default: color = 'default';
        }
        return t(`adminResources.visibility.${visibility}`);
      },
    },
    {
      title: t('adminResources.list.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      filters: [
        { text: t('adminResources.status.published'), value: 'published' },
        { text: t('adminResources.status.draft'), value: 'draft' },
        { text: t('adminResources.status.hidden'), value: 'hidden' },
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => (
        <Badge
          status={status === 'published' ? 'success' : (status === 'draft' ? 'processing' : 'default')}
          text={t(`adminResources.status.${status}`)}
        />
      ),
    },
    {
      title: (
        <>
          <FileOutlined /> {t('adminResources.list.columns.fileCount')}
        </>
      ),
      dataIndex: 'files',
      key: 'files',
      width: 120,
      sorter: (a, b) => {
        const filesA = (a.files || []).length;
        const filesB = (b.files || []).length;
        return filesA - filesB;
      },
      render: (files) => {
        if (!files || !files.length) return <span>0</span>;

        const totalSize = files.reduce((sum, file) => sum + file.file_size, 0);
        return (
          <span>
            {files.length} ({formatFileSize(totalSize)})
          </span>
        );
      },
    },
    {
      title: t('adminResources.list.columns.publishedAt'),
      dataIndex: 'published_at',
      key: 'published_at',
      width: '15%',
      sorter: (a, b) => {
        const dateA = new Date(a.published_at || 0);
        const dateB = new Date(b.published_at || 0);
        return dateA.getTime() - dateB.getTime();
      },
      render: (date) => (date ? formatDate(date, i18n.language) : t('common.unknown')),
    },
    {
      title: t('adminResources.list.columns.updatedAt'),
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: '15%',
      sorter: (a, b) => {
        const dateA = new Date(a.updated_at || 0);
        const dateB = new Date(b.updated_at || 0);
        return dateA.getTime() - dateB.getTime();
      },
      render: (date) => formatDate(date, i18n.language),
    },
    {
      title: t('adminResources.list.columns.actions'),
      key: 'actions',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          {record.files && record.files.length > 0 ? (
            <button
              type="button"
              style={{ 
                color: 'rgba(0, 0, 0, 0.88)',
                background: 'none',
                border: 'none',
                padding: 0,
                font: 'inherit',
                cursor: 'pointer',
                textDecoration: 'underline'
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleDownload(record);
              }}
            >
              {t('common.actions.download')}
            </button>
          ) : (
            <button
              type="button"
              disabled
              style={{ 
                color: '#d9d9d9',
                background: 'none',
                border: 'none',
                padding: 0,
                font: 'inherit',
                cursor: 'not-allowed'
              }}
            >
              {t('common.actions.download')}
            </button>
          )}
          <button
            type="button"
            style={{ 
              color: 'rgba(0, 0, 0, 0.88)',
              background: 'none',
              border: 'none',
              padding: 0,
              font: 'inherit',
              cursor: 'pointer',
              textDecoration: 'underline'
            }}
            onClick={() => handleEdit(record)}
          >
            {t('common.actions.edit')}
          </button>
          <button 
            type="button"
            style={{ 
              color: '#ff4d4f',
              background: 'none',
              border: 'none',
              padding: 0,
              font: 'inherit',
              cursor: 'pointer',
              textDecoration: 'underline'
            }}
            onClick={() => handleDelete(record)}
          >
            {t('common.actions.delete')}
          </button>
        </Space>
      ),
    },
  ];

  if (showErrorPage) {
    return <ErrorPage type="403" />;
  }

  return (
    <div style={{ padding: '10px 24px 42px' }}>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="w-full md:w-auto flex-grow">
          <Space wrap size={8} className="w-full">
            <Input
              placeholder={t('resourceList.search.placeholder')}
              prefix={<SearchOutlined />}
              onChange={e => setSearchTerm(e.target.value)}
              size='large'
              allowClear
              style={{ width: '100%', maxWidth: 300 }}
            />
            <RangePicker
              presets={datePresets}
              value={dateRange}
              onChange={handleDateRangeChange}
              allowClear
              style={{ width: 280 }}
              size='large'
              format="YYYY-MM-DD"
            />
            <Select
              allowClear
              style={{ width: 200 }}
              placeholder={t('adminResources.filter.visibilityPlaceholder')}
              onChange={handleVisibilityChange}
              value={selectedVisibility}
              size='large'
            >
              <Option value="public">{t('adminResources.visibility.public')}</Option>
              <Option value="org_only">{t('adminResources.visibility.org_only')}</Option>
              <Option value="private">{t('adminResources.visibility.private')}</Option>
            </Select>
            <Select
              allowClear
              style={{ width: 200 }}
              placeholder={t('adminResources.filter.statusPlaceholder')}
              onChange={handleStatusChange}
              value={selectedStatus}
              size='large'
            >
              <Option value="published">{t('adminResources.status.published')}</Option>
              <Option value="draft">{t('adminResources.status.draft')}</Option>
            </Select>
          </Space>
        </div>
        <div className="w-full md:w-auto">
          <Button
            type="dashed"
            size="large"
            onClick={handleUpload}
            style={{ width: '100%', minWidth: '140px' }}
          >
            {t('adminResources.buttons.create')}
          </Button>
        </div>
      </div>
      <div className="border rounded-lg overflow-hidden">
        <Table
          loading={loading}
          columns={columns}
          dataSource={resources}
          rowKey="id"
          pagination={{
            pageSize: pageSize,
            current: currentPage,
            total: total,
            onChange: (page) => setCurrentPage(page),
            position: ['bottomCenter'],
            showTotal: (total) => t('common.totalItems', { total: total }),
          }}
          expandable={{
            expandedRowRender,
            rowExpandable: record => record.files?.length > 0,
            expandedRowKeys: expandedRowKeys,
            onExpand: (expanded, record) => {
              setExpandedRowKeys(expanded ? [record.id] : []);
            }
          }}
          onRow={(record) => ({
            onClick: () => handleRowClick(record),
            style: { cursor: record.files?.length > 0 ? 'pointer' : 'default' }
          })}
          scroll={{ x: 'max-content' }}
          style={{ overflowX: 'auto' }}
        />
      </div>

      <UploadResourceModal
        visible={modalData.visible}
        onCancel={() => {
          setModalData({ visible: false, mode: 'create', initialData: null, orgId: null });
          setModalLoading(false);
        }}
        onOk={handleModalComplete}
        mode={modalData.mode}
        initialData={modalData.initialData}
        loading={modalLoading}
      />
    </div>
  );
};

export default AdminResourcesListTab; 