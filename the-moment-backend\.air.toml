# .air.toml - Air configuration file for live reloading Go applications.
# For more information, see: https://github.com/air-verse/air

# The root directory of your project. All paths in this file are relative to 'root'.
root = "."

# Directory where 'air' will place temporary files, including the built binary.
# This directory should be added to your .gitignore.
tmp_dir = "tmp"

[build]
  # The command to build your Go application.
  # The output binary should be placed in the 'tmp_dir'.
  # Based on your README, the main package is in 'cmd/api/main.go'.
  cmd = "go build -o ./tmp/app ./cmd/api/main.go"

  # The name of the binary file to run after a successful build.
  # This path is relative to 'tmp_dir'.
  bin = "app"
  full_bin = "APP_BASE_URL='http://localhost:8080' ./tmp/app"

  # Watch these file extensions for changes.
  # When a file with one of these extensions changes, 'air' will trigger a rebuild.
  include_ext = ["go", "tpl", "tmpl", "html", "env"]

  # Directories to exclude from watching.
  # 'air' will ignore changes in these directories.
  exclude_dir = [
    "tmp",        # Air's own temporary directory
    "vendor",     # Go vendor directory
    ".git",       # Git metadata
    ".vscode",    # VSCode specific files
    ".idea",      # IntelliJ specific files
    "docs",       # Documentation files
    "assets",     # Static assets (if any, that don't require rebuild)
    ".aider.tags.cache.v4", # Aider cache
    ".cursor",    # Cursor specific files
    ".github",    # GitHub specific files (workflows, etc.)
    ".ignore",    # Ignore directory (from project structure)
    ".roo"        # Roo directory (from project structure)
  ]

  # Exclude files matching these regular expressions.
  # Useful for excluding test files from triggering a rebuild of the main app.
  exclude_regex = ["_test.go"]

  # Delay in milliseconds after a file change before 'air' triggers a rebuild.
  # Helps to batch multiple quick saves.
  delay = 1000 # 1 second

  # If true, 'air' will stop the currently running binary if the build fails.
  stop_on_error = true

  # Log file for build output.
  # Default is air_build.log in the current working directory. This file should also be gitignored.
  log = "air_build.log"

[log]
  # If true, prefix log messages with the current time.
  time = true

[misc]
  # If true, 'air' will delete the 'tmp_dir' when it exits.
  clean_on_exit = true

  # If true, 'air' will attempt to load environment variables from a .env file
  # in the 'root' directory at startup. Your README mentions a .env file, so this is enabled.
  load_env = true

[screen]
  # If true, clear the screen before each rebuild.
  clear_on_rebuild = true 