-- Revert date/timestamp columns from TEXT back to TIMESTAMPTZ

-- For verification_hk_id_cards
ALTER TABLE verification_hk_id_cards
ALTER COLUMN date_of_birth TYPE TIMESTAMPTZ USING date_of_birth::TIMESTAMPTZ;

-- For verification_mainland_china_id_cards
ALTER TABLE verification_mainland_china_id_cards
ALTER COLUMN date_of_birth TYPE TIMESTAMPTZ USING date_of_birth::TIMESTAMPTZ,
ALTER COLUMN valid_until TYPE TIMESTAMPTZ USING valid_until::TIMESTAMPTZ;

-- For verification_mainland_travel_permits (formerly verification_home_return_permits)
ALTER TABLE verification_mainland_travel_permits
ALTER COLUMN issue_date TYPE TIMESTAMPTZ USING issue_date::TIMESTAMPTZ,
ALTER COLUMN expiry_date TYPE TIMESTAMPTZ USING expiry_date::TIMESTAMPTZ;

-- For verification_passports
ALTER TABLE verification_passports
ALTER COLUMN issue_date TYPE TIMESTAMPTZ USING issue_date::TIMESTAMPTZ,
ALTER COLUMN expiry_date TYPE TIMESTAMPTZ USING expiry_date::TIMESTAMPTZ;

-- For verification_student_ids
ALTER TABLE verification_student_ids
ALTER COLUMN expiry_date TYPE TIMESTAMPTZ USING expiry_date::TIMESTAMPTZ; 