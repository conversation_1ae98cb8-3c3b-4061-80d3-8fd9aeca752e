import React, { useState, useEffect, useCallback } from 'react';
import { Typography, Form, Divider, message, Card, Space, Tag, Button, Alert, Spin, Empty } from 'antd';
import { CheckCircleFilled, ClockCircleFilled, CloseCircleFilled, ExclamationCircleFilled, RightOutlined } from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import ProgramApplicationModal from '../../components/ProgramApplicationModal';
import { profileService } from '../../services/profileService';

const { Title, Text } = Typography;

const STATUS_CONFIG = {
    approved: {
        icon: <CheckCircleFilled />,
        color: 'success',
        messageKey: 'approved'
    },
    pending: {
        icon: <ClockCircleFilled />,
        color: 'processing',
        messageKey: 'pending'
    },
    rejected: {
        icon: <CloseCircleFilled />,
        color: 'error',
        messageKey: 'rejected'
    },
    unverified: {
        icon: <ExclamationCircleFilled />,
        color: 'warning',
        messageKey: 'unverified'
    }
};

// Helper function to normalize status values
const normalizeStatus = (status) => {
    return status === null || status === undefined ? 'unverified' : status;
};

const ProgramApplicationsTab = () => {
    const [form] = Form.useForm();
    const { user } = useAuth();
    const { t } = useTranslation();
    const [modalVisible, setModalVisible] = useState(false);
    const [currentProgram, setCurrentProgram] = useState(null);
    const [volunteerApplications, setVolunteerApplications] = useState([]);
    const [userOrganizations, setUserOrganizations] = useState([]);
    const [loading, setLoading] = useState(false);
    const [selectedOrganizationId, setSelectedOrganizationId] = useState(null);
    
    // Temporary flag to hide verification functionality
    const hideVerifyButton = true;

    const verificationStatus = {
        hasVerifiedDocument: Object.values(user?.documentVerification || {}).some(
            doc => doc.status === 'approved' && doc.type !== 'address'
        ),
        isAddressVerified: user?.documentVerification?.address?.status === 'approved'
    };

    // Define fetchVolunteerApplications first as fetchUserData depends on it
    const fetchVolunteerApplications = useCallback(async (organizations) => {
        try {
            const response = await profileService.listMyVolunteerApplications();
            if (response) {
                // Filter applications to only include those that match user's organizations
                const orgIds = organizations.map(org => org.id);
                const filteredApplications = response.filter(app => 
                    orgIds.includes(app.organization_id)
                );
                setVolunteerApplications(filteredApplications || []);
            }
        } catch (error) {
            console.error('Failed to fetch volunteer applications:', error);
            message.error(t('messages.fetchError'));
        }
    }, [t, setVolunteerApplications]); // Added profileService and message if they were not stable, but they usually are. Assuming t and setVolunteerApplications are the main reactive dependencies.

    const fetchUserData = useCallback(async () => {
        try {
            setLoading(true);
            // First fetch user organizations
            const orgResponse = await profileService.getUserOrganizations();
            if (orgResponse) {
                setUserOrganizations(orgResponse || []);
            }
            // Then fetch volunteer applications
            await fetchVolunteerApplications(orgResponse || []);
        } catch (error) {
            console.error('Failed to fetch user data:', error);
            message.error(t('messages.fetchError'));
        } finally {
            setLoading(false);
        }
    }, [t, setLoading, setUserOrganizations, fetchVolunteerApplications]); // Added profileService and message if they were not stable.

    useEffect(() => {
        fetchUserData();
    }, [fetchUserData]);

    const handleApplicationSubmit = (programType, organizationId = null) => {
        if (!verificationStatus.hasVerifiedDocument || !verificationStatus.isAddressVerified) {
            message.info(t('userSettings.programApplications.messages.verificationRequired'));
            return;
        }
        setCurrentProgram(programType);
        setSelectedOrganizationId(organizationId);
        setModalVisible(true);
    };

    const handleModalSubmit = () => {
        message.success(t('userSettings.programApplications.messages.volunteerSubmitSuccess'));
        // Refresh applications after submission
        fetchUserData();
    };

    const getStatusTag = (status) => {
        const normalizedStatus = normalizeStatus(status);
        const config = STATUS_CONFIG[normalizedStatus] || STATUS_CONFIG.unverified;
        return (
            <Tag icon={config.icon} color={config.color}>
                {t(`userSettings.programApplications.status.${config.messageKey}`)}
            </Tag>
        );
    };

    const getStatusMessage = (status) => {
        const normalizedStatus = normalizeStatus(status);
        const config = STATUS_CONFIG[normalizedStatus] || STATUS_CONFIG.unverified;
        return (
            <Text type="secondary">
                {t(`userSettings.programApplications.messages.${config.messageKey}`)}
            </Text>
        );
    };

    const getAlertType = (status) => {
        const normalizedStatus = normalizeStatus(status);
        switch (normalizedStatus) {
            case 'approved':
                return 'success';
            case 'pending':
                return 'info';
            case 'rejected':
                return 'error';
            default:
                return 'warning';
        }
    };

    const ApplicationCard = ({ application, onApply }) => {
        const { status = 'unverified', admin_notes, organization_name } = application || {};
        const normalizedStatus = normalizeStatus(status);
        
        return (
            <Card className="rounded-lg border border-gray-200 mb-4">
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    <div>
                        <Title level={5}>{organization_name}</Title>
                    </div>
                    <div className="flex items-center justify-between">
                        <Space size={8} align="center">
                            <Text strong style={{ fontSize: '16px', color: '#1f1f1f' }}>
                                {t('userSettings.programApplications.labels.status')}：
                            </Text>
                            {getStatusTag(status)}
                        </Space>
                        
                        {!hideVerifyButton && (normalizedStatus === 'rejected' || normalizedStatus === 'unverified') && (
                            <Button
                                type="text"
                                onClick={onApply}
                                className="flex items-center text-primary hover:text-primary-dark"
                                style={{ padding: '4px 0' }}
                            >
                                {t('userSettings.programApplications.buttons.applyNow')} <RightOutlined className="ml-1" />
                            </Button>
                        )}
                    </div>

                    <div>{getStatusMessage(status)}</div>

                    {/* Display admin notes for any status if they exist */}
                    {admin_notes && (
                        <Alert
                            type={getAlertType(status)}
                            message={t('identityVerificationCard.messages.comment')}
                            description={admin_notes}
                            className="mt-3"
                            showIcon
                        />
                    )}
                </Space>
            </Card>
        );
    };

    const renderApplicationCards = () => {
        if (volunteerApplications.length === 0) {
            return (
                <Empty />
            );
        }

        return volunteerApplications.map((application, index) => (
            <ApplicationCard
                key={application.id || index}
                application={application}
                onApply={() => handleApplicationSubmit('volunteer', application.organization_id)}
            />
        ));
    };

    return (
        <div>
            <ProgramApplicationModal 
                visible={modalVisible}
                onClose={() => setModalVisible(false)}
                programType={currentProgram}
                onSubmit={handleModalSubmit}
                organizationId={selectedOrganizationId}
            />

            <Title level={3}>{t('userSettings.programApplications.title')}</Title>
            <Text type="secondary">{t('userSettings.programApplications.subtitle')}</Text>

            <Divider />

            {/* Volunteer Application Row */}
            <div className="settings-tab-content">
                <div className="settings-tab-sidebar">
                    <div className="settings-section-header">
                        <span className="settings-section-title">{t('userSettings.programApplications.volunteer.title')}</span>
                        <span className="settings-section-subtitle">{t('userSettings.programApplications.volunteer.description')}</span>
                    </div>
                </div>
                <div className="settings-tab-main">
                    {loading ? (
                        <div className="text-center p-4">
                            <Spin />
                        </div>
                    ) : (
                        renderApplicationCards()
                    )}
                </div>
            </div>
        </div>
    );
};

export default ProgramApplicationsTab;
