/* UserInfo.css */

/* Activity card styles */
.activity-card {
    max-width: 1300px;
    margin: 0 auto;
    padding: 0 24px;
    box-sizing: border-box;
}

/* Tab Content Layout */
.settings-tab-content {
    display: flex;
    gap: 48px;
    max-width: 100%;
}

.settings-tab-sidebar {
    flex: 0 0 30%;
    max-width: 30%;
}

.settings-tab-main {
    flex: 0 0 calc(70% - 48px);
    max-width: calc(70% - 48px);
    width: 100%;
}

/* Section Headers */
.settings-section-header {
    margin-bottom: 24px;
}

.settings-section-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #1f1f1f;
    margin-bottom: 4px;
}

.settings-section-subtitle {
    display: block;
    font-size: 14px;
    color: #666;
}

/* Subsection Styles */
.settings-subsection {
    margin-bottom: 32px;
}

.settings-subsection-header {
    margin-bottom: 16px;
}

.settings-subsection-title {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #1f1f1f;
    margin-bottom: 4px;
}

/* Notification subsection title with larger font size */
.notification-subsection-title {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: #1f1f1f;
    margin-bottom: 6px;
}

.settings-subsection-subtitle {
    display: block;
    font-size: 14px;
    color: #666;
}

/* Settings Card */
.settings-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 24px;
    background-color: #ffffff;
    width: 100%;
    box-sizing: border-box;
}

/* Settings Group */
.settings-group {
    padding-bottom: 24px;
    margin-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;
    width: 100%;
}

.settings-group:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.settings-group-header {
    margin-bottom: 16px;
}

.settings-group-title {
    font-size: 14px;
    font-weight: 500;
    color: #1f1f1f;
}

.settings-group-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Vertical Checkbox Styles */
.vertical-checkbox-group {
    display: flex;
    flex-direction: column;
}

.vertical-checkbox-group.with-dividers {
    padding: 8px 0;
}

.vertical-checkbox-group.with-dividers .ant-checkbox-wrapper {
    padding: 12px 8px;
    margin: 0 !important;
}

.notification-option-item {
    margin-bottom: 0 !important;
}

.notification-option-item .ant-checkbox-wrapper {
    margin-left: 0;
    font-size: 14px;
}

/* Responsive Layout */
@media screen and (max-width: 992px) {
    .settings-tab-content {
        flex-direction: column;
        gap: 24px;
    }

    .settings-tab-sidebar,
    .settings-tab-main {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .activity-card {
        padding: 0 16px;
        margin: 0;
    }
}