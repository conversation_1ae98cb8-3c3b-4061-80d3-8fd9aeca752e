-- Recreate Table for user organization volunteer qualifications
CREATE TABLE user_organization_volunteer_qualifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    application_id UUID NOT NULL REFERENCES user_volunteer_applications(id) ON DELETE CASCADE, -- Link to the approved application
    qualification_date TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- When they were approved
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT uq_user_org_qualification UNIQUE (user_id, organization_id) -- A user is either qualified or not for an org
);

-- Recreate Trigger
CREATE TRIGGER set_timestamp_user_organization_volunteer_qualifications
BEFORE UPDATE ON user_organization_volunteer_qualifications
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamp();

-- Recreate Indexes
CREATE INDEX idx_user_organization_volunteer_qualifications_user_id ON user_organization_volunteer_qualifications(user_id);
CREATE INDEX idx_user_organization_volunteer_qualifications_organization_id ON user_organization_volunteer_qualifications(organization_id); 