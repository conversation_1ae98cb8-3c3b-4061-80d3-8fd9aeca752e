import React, { useState, useCallback, useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { isPast, parseISO } from 'date-fns';

// Import all the dialog components
import { TextDialog } from '@/common_modules/TextDialog';
import { AuthModal } from '@/common_modules/AuthModal';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { TextInputDialog } from '@/common_modules/TextInputDialog';

// Import types
import { EventListPayloadDetails, ProfileResponse } from '@/api/api_config';
import { VerificationStatusEnum, VerificationTypeEnum } from 'types/enums';

// Types for button states
interface ButtonState {
  text: string;
  mode: 'contained' | 'outlined';
  action: () => void | Promise<void>;
  loading?: boolean;
  disabled?: boolean;
  visible: boolean;
}

interface EventActionButtonsProps {
  // Event data
  event: EventListPayloadDetails;
  
  // User authentication and profile
  isAuthenticated: boolean;
  currentUser?: ProfileResponse;
  
  // User event data
  userEventRegistration?: any;
  eventVolunteerApplication?: any;
  
  // Organization volunteer status
  canVolunteerForEventOrg: boolean;
  isLoadingOrgVolunteerStatus: boolean;
  
  // API mutation functions
  registerAsParticipant: (data: { event_id: string }) => Promise<any>;
  applyAsVolunteer: (data: { eventId: string; motivation: string }) => Promise<any>;
  cancelEventRegistration: (data: { registrationId: string }) => Promise<any>;
  withdrawVolunteerApplication: (data: { appId: string; eventId?: string }) => Promise<any>;
  
  // Loading states
  isRegisteringAsParticipant: boolean;
  isApplyingAsVolunteer: boolean;
  isCancellingRegistration: boolean;
  isWithdrawingVolunteerApp: boolean;
  
  // Refresh functions
  refetchEventDetails: () => void;
  refetchUserEventRegistrations: () => void;
  refetchUserEventVolunteerApps: () => void;
  
  // Theme
  theme: any;
}

// Button type enum for better type safety
enum ButtonType {
  PARTICIPANT = 'participant',
  VOLUNTEER = 'volunteer'
}

// Registration status type
type RegistrationStatus = 
  | 'registered' 
  | 'waitlisted' 
  | 'pending_approval' 
  | 'attended' 
  | 'cancelled_by_user' 
  | 'cancelled_by_admin' 
  | 'rejected_approval' 
  | 'absent'
  | null;

// Volunteer application status type  
type VolunteerStatus = 
  | 'approved'
  | 'pending' 
  | 'pending_approval'
  | 'waitlisted'
  | 'confirmed'
  | 'attended'
  | 'cancelled_by_user'
  | 'rejected_by_host'
  | 'withdrawn_by_user'
  | null;

export const EventActionButtons: React.FC<EventActionButtonsProps> = ({
  event,
  isAuthenticated,
  currentUser,
  userEventRegistration,
  eventVolunteerApplication,
  canVolunteerForEventOrg,
  isLoadingOrgVolunteerStatus,
  registerAsParticipant,
  applyAsVolunteer,
  cancelEventRegistration,
  withdrawVolunteerApplication,
  isRegisteringAsParticipant,
  isApplyingAsVolunteer,
  isCancellingRegistration,
  isWithdrawingVolunteerApp,
  refetchEventDetails,
  refetchUserEventRegistrations,
  refetchUserEventVolunteerApps,
  theme,
}) => {
  const { t } = useTranslation();
  const router = useRouter();

  // === State Management ===
  // Authentication modal
  const [showAuthModal, setShowAuthModal] = useState(false);
  
  // Registration/Application confirmation flow
  const [registrationRole, setRegistrationRole] = useState<'participant' | 'volunteer' | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showRegistrationSuccessDialog, setShowRegistrationSuccessDialog] = useState(false);
  const [showRegistrationFailureDialog, setShowRegistrationFailureDialog] = useState(false);
  const [registrationErrorMessage, setRegistrationErrorMessage] = useState('');
  
  // Verification related dialogs
  const [showVerificationAlertDialog, setShowVerificationAlertDialog] = useState(false);
  const [verificationAlertMessage, setVerificationAlertMessage] = useState('');
  const [showVolunteerJoinOrgDialog, setShowVolunteerJoinOrgDialog] = useState(false);
  const [volunteerJoinOrgMessage, setVolunteerJoinOrgMessage] = useState('');
  
  // Cancellation/Withdrawal dialogs
  const [showParticipantCancelDialog, setShowParticipantCancelDialog] = useState(false);
  const [showVolunteerWithdrawDialog, setShowVolunteerWithdrawDialog] = useState(false);
  const [showParticipantReasonDialog, setShowParticipantReasonDialog] = useState(false);
  const [participantCancellationReason, setParticipantCancellationReason] = useState('');
  const [participantRegistrationIdToCancel, setParticipantRegistrationIdToCancel] = useState<string | null>(null);
  const [volunteerApplicationIdToWithdraw, setVolunteerApplicationIdToWithdraw] = useState<string | null>(null);
  
  // Success/notification dialogs
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [notificationDialogVisible, setNotificationDialogVisible] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');

  // === Helper Functions ===
  
  // Check if event has ended
  const isEventEnded = useMemo(() => {
    return isPast(parseISO(event.end_time || event.start_time));
  }, [event.end_time, event.start_time]);

  // Check if event is open for registration
  const isEventOpen = useMemo(() => {
    return event.status === 'published' && !isEventEnded;
  }, [event.status, isEventEnded]);

  // Get current user registration status
  const currentRegistrationStatus: RegistrationStatus = useMemo(() => {
    return userEventRegistration?.status || event.current_user_registration_status || null;
  }, [userEventRegistration?.status, event.current_user_registration_status]);

  // Get current user volunteer application status
  const currentVolunteerStatus: VolunteerStatus = useMemo(() => {
    return eventVolunteerApplication?.status || null;
  }, [eventVolunteerApplication?.status]);

  // Check if user is effectively a participant (has active registration)
  const isEffectivelyParticipant = useMemo(() => {
    const activeParticipantStatuses = ['registered', 'waitlisted', 'pending_approval', 'pending_payment', 'confirmed'];
    return currentRegistrationStatus && activeParticipantStatuses.includes(currentRegistrationStatus);
  }, [currentRegistrationStatus]);

  // Check if user is effectively a volunteer (has an approved/confirmed application)
  const isEffectivelyVolunteer = useMemo(() => {
    const activeVolunteerStatuses = ['approved', 'confirmed', 'attended'];
    return currentVolunteerStatus && activeVolunteerStatuses.includes(currentVolunteerStatus);
  }, [currentVolunteerStatus]);

  // Event capacity calculations
  const eventCapacity = useMemo(() => {
    const registeredCount = event.registered_count || 0;
    const participantLimit = event.participant_limit ?? Infinity;
    const waitlistedCount = event.waitlisted_count || 0;
    const waitlistLimit = event.waitlist_limit ?? 0;
    
    return {
      registeredCount,
      participantLimit,
      waitlistedCount,
      waitlistLimit,
      hasAvailableSpots: registeredCount < participantLimit,
      hasAvailableWaitlistSpots: waitlistedCount < waitlistLimit,
      isFull: registeredCount >= participantLimit && waitlistedCount >= waitlistLimit
    };
  }, [event.registered_count, event.participant_limit, event.waitlisted_count, event.waitlist_limit]);

  // === Verification Logic ===
  
  // Helper function to map event verification key to enum
  const mapEventVerificationKeyToEnum = (key: string): VerificationTypeEnum | null => {
    const keyMap: { [key: string]: VerificationTypeEnum } = {
      'hk_id_card': VerificationTypeEnum.HkIDCard,
      'mainland_china_id_card': VerificationTypeEnum.MainlandChinaIDCard,
      'mainland_travel_permit': VerificationTypeEnum.MainlandTravelPermit,
      'passport': VerificationTypeEnum.Passport,
      'hk_youth_plus': VerificationTypeEnum.HkYouthPlus,
      'address_proof': VerificationTypeEnum.AddressProof,
      'student_id': VerificationTypeEnum.StudentID,
      'home_visit': VerificationTypeEnum.HomeVisit,
    };
    return keyMap[key.toLowerCase()] || null;
  };

  // Get verification status by type
  const getVerificationByType = useCallback((verificationTypeKey: string): { status: VerificationStatusEnum } => {
    if (!currentUser?.verification_status) return { status: VerificationStatusEnum.Unverified };
    
    const isVerified = (currentUser.verification_status as any)[verificationTypeKey];
    if (typeof isVerified === 'boolean' && isVerified) {
      return { status: VerificationStatusEnum.Approved };
    }
    return { status: VerificationStatusEnum.Unverified };
  }, [currentUser?.verification_status]);

  // Perform comprehensive verification check
  const performVerificationCheck = useCallback((requiredKeys: string[]): { 
    isValid: boolean; 
    missingVerifications: string[]; 
    pendingVerifications: string[] 
  } => {
    if (requiredKeys.length === 0) {
      return { isValid: true, missingVerifications: [], pendingVerifications: [] };
    }

    const missingVerifications: string[] = [];
    const pendingVerifications: string[] = [];

    for (const key of requiredKeys) {
      const enumType = mapEventVerificationKeyToEnum(key);
      
      if (enumType) {
        const userVerification = getVerificationByType(key);
        
        if (userVerification.status !== VerificationStatusEnum.Approved) {
          missingVerifications.push(t(`verificationTypes.${key}`, key));
        }
      } else {
        console.warn(`[EventActionButtons] Unknown required verification key: ${key}`);
        missingVerifications.push(key);
      }
    }

    return {
      isValid: missingVerifications.length === 0 && pendingVerifications.length === 0,
      missingVerifications,
      pendingVerifications
    };
  }, [getVerificationByType, t]);

  // Show verification error
  const showVerificationError = useCallback((missingVerifications: string[], pendingVerifications: string[]) => {
    let alertMessage = "";
    if (missingVerifications.length > 0) {
      alertMessage += t('events.detail.missingVerificationsError', {
        verifications: missingVerifications.join(', '),
      }) + "\n";
    }
    if (pendingVerifications.length > 0) {
      alertMessage += t('events.detail.pendingVerificationsWarning', {
        verifications: pendingVerifications.join(', '),
      });
    }

    setVerificationAlertMessage(alertMessage.trim());
    setShowVerificationAlertDialog(true);
  }, [t]);

  // Handle API verification error
  const handleApiVerificationError = useCallback((errorMessage: string): boolean => {
    const match = errorMessage.match(/missing \[(.+)\]/);
    if (match && match[1]) {
      const missingKeysFromApi = match[1].split(',').map((k: string) => k.trim()).filter((k: string) => k);
      if (missingKeysFromApi.length > 0) {
        const displayableMissingVerifications = missingKeysFromApi.map((key: string) => 
          t(`verificationTypes.${key}`, key)
        );

        const alertMessage = t('events.detail.missingVerificationsError', {
          verifications: displayableMissingVerifications.join(', '),
        });

        setVerificationAlertMessage(alertMessage.trim());
        setShowVerificationAlertDialog(true);
        return true;
      }
    }
    return false;
  }, [t]);

  // === Success/Notification Helpers ===
  
  const showSuccessDialog = useCallback((message: string) => {
    setSuccessMessage(message);
    setSuccessDialogVisible(true);
  }, []);
  
  const showNotificationDialog = useCallback((message: string) => {
    setNotificationMessage(message);
    setNotificationDialogVisible(true);
  }, []);

  // === Button Action Handlers ===
  
  // Handle participant registration
  const handleParticipantRegister = useCallback(async () => {
    if (!event || !isAuthenticated) {
      setShowAuthModal(true);
      return;
    }

    // Pre-verification check
    const requiredKeys = event.verification_type_keys || [];
    if (requiredKeys.length > 0) {
      const verificationResult = performVerificationCheck(requiredKeys);
      if (!verificationResult.isValid) {
        showVerificationError(verificationResult.missingVerifications, verificationResult.pendingVerifications);
        return;
      }
    }

    // Show confirmation dialog
    setRegistrationRole('participant');
    setShowConfirmDialog(true);
  }, [event, isAuthenticated, performVerificationCheck, showVerificationError]);

  // Handle volunteer application
  const handleVolunteerApply = useCallback(async () => {
    if (!event || !isAuthenticated) {
      setShowAuthModal(true);
      return;
    }

    if (isLoadingOrgVolunteerStatus) {
      showNotificationDialog(t('events.detail.loadingVolunteerStatus'));
      return;
    }

    // Check organization volunteer qualification
    if (!canVolunteerForEventOrg) {
      setVolunteerJoinOrgMessage(t('events.detail.volunteerJoinOrgMessage', {
        organizationName: event.organization_name || t('common.thisOrganization')
      }));
      setShowVolunteerJoinOrgDialog(true);
      return;
    }

    // Pre-verification check
    const requiredKeys = event.verification_type_keys || [];
    if (requiredKeys.length > 0) {
      const verificationResult = performVerificationCheck(requiredKeys);
      if (!verificationResult.isValid) {
        showVerificationError(verificationResult.missingVerifications, verificationResult.pendingVerifications);
        return;
      }
    }

    // Show confirmation dialog
    setRegistrationRole('volunteer');
    setShowConfirmDialog(true);
  }, [event, isAuthenticated, isLoadingOrgVolunteerStatus, canVolunteerForEventOrg, performVerificationCheck, showVerificationError, showNotificationDialog, t]);

  // Handle registration submission
  const handleRegistrationSubmit = useCallback(async () => {
    setShowConfirmDialog(false);
    if (!event?.id) {
      console.error("[handleRegistrationSubmit] Event ID missing");
      setRegistrationErrorMessage(t('events.detail.eventNotLoaded'));
      setShowRegistrationFailureDialog(true);
      return;
    }
    
    try {
      let response;
      if (registrationRole === 'volunteer') {
        response = await applyAsVolunteer({ eventId: event.id, motivation: '' });
        showSuccessDialog(t('events.detail.volunteerApplicationSuccess'));
        refetchEventDetails();
        refetchUserEventVolunteerApps();
      } else {
        response = await registerAsParticipant({ event_id: event.id });
        showSuccessDialog(
          (response as any).status === 'waitlisted' ? t('events.detail.waitlistedSuccess') : t('events.detail.registrationSuccess'),
        );
        refetchEventDetails();
        refetchUserEventRegistrations();
      }
    } catch (error: any) {
      console.error('[handleRegistrationSubmit] Registration API error Raw:', JSON.stringify(error));
      let errorMessage = t('events.detail.registrationFailedUnknown');
      let showGenericFailureDialog = true;

      const apiStatus = error.status;
      const apiDetailedMessage = typeof error.message === 'string' ? error.message : null;

      if (apiStatus && apiDetailedMessage) {
        console.error(`[handleRegistrationSubmit] Registration API error. Status: ${apiStatus}, Message: ${apiDetailedMessage}`);
        
        if (apiStatus === 409) {
          errorMessage = registrationRole === 'volunteer' 
            ? t('events.detail.duplicateVolunteerApplication', 'You have already applied as a volunteer for this event.')
            : t('events.detail.duplicateRegistration', 'You have already registered for this event.');
          
          refetchEventDetails();
          if (registrationRole === 'volunteer') {
            refetchUserEventVolunteerApps();
          }
        } else if (apiStatus === 403 && handleApiVerificationError(apiDetailedMessage)) {
          showGenericFailureDialog = false;
        } else {
          errorMessage = apiDetailedMessage;
        }
      } else if (error.message) {
        console.error('[handleRegistrationSubmit] Registration API error.message (fallback):', error.message);
        errorMessage = error.message;
      }

      if (showGenericFailureDialog) {
        setRegistrationErrorMessage(errorMessage);
        setShowRegistrationFailureDialog(true);
      }
    }
  }, [event?.id, registrationRole, applyAsVolunteer, registerAsParticipant, showSuccessDialog, refetchEventDetails, refetchUserEventVolunteerApps, refetchUserEventRegistrations, t, handleApiVerificationError]);

  // Handle participant cancellation
  const handleParticipantCancelAction = useCallback(() => {
    const registrationIdToUse = userEventRegistration?.id || event?.current_user_registration_id;
    
    if (!registrationIdToUse) {
      console.warn("Current user registration ID not found for cancellation.");
      return;
    }
    
    setParticipantRegistrationIdToCancel(registrationIdToUse); 
    setParticipantCancellationReason('');
    setShowParticipantReasonDialog(true);
  }, [userEventRegistration?.id, event?.current_user_registration_id]);

  // Handle participant reason submission
  const handleSubmitParticipantReasonAndProceedToConfirm = useCallback(() => {
    setShowParticipantReasonDialog(false);
    setShowParticipantCancelDialog(true);
  }, []);

  // Handle participant cancellation confirmation
  const handleConfirmParticipantCancel = useCallback(async () => {
    if (!participantRegistrationIdToCancel) return;
    setShowParticipantCancelDialog(false);

    try {
      await cancelEventRegistration({ 
        registrationId: participantRegistrationIdToCancel,
      });
      showSuccessDialog(t('events.detail.cancelSuccess'));
      refetchEventDetails();
      refetchUserEventRegistrations();
    } catch (err: any) {
      const errorMessage = err.message || (err.code ? t(`apiErrors.${err.code}`, t('events.detail.cancelError')) : t('events.detail.cancelError'));
      setRegistrationErrorMessage(errorMessage);
      setShowRegistrationFailureDialog(true);
    }
    
    setParticipantRegistrationIdToCancel(null);
    setParticipantCancellationReason('');
  }, [participantRegistrationIdToCancel, cancelEventRegistration, showSuccessDialog, refetchEventDetails, refetchUserEventRegistrations, t]);

  // Handle volunteer withdrawal
  const handleOpenVolunteerWithdrawDialog = useCallback((id: string) => {
    setVolunteerApplicationIdToWithdraw(id);
    setShowVolunteerWithdrawDialog(true);
  }, []);

  // Handle volunteer withdrawal confirmation
  const handleConfirmVolunteerWithdraw = useCallback(async () => {
    if (!volunteerApplicationIdToWithdraw) return;
    setShowVolunteerWithdrawDialog(false);

    try {
      await withdrawVolunteerApplication({ 
        appId: volunteerApplicationIdToWithdraw, 
        eventId: event?.id 
      });
      showSuccessDialog(t('events.detail.withdrawVolunteerSuccess'));
      refetchEventDetails();
      refetchUserEventVolunteerApps();
    } catch (err: any) {
      const errorMessage = err.message || (err.code ? t(`apiErrors.${err.code}`, t('events.detail.withdrawVolunteerError')) : t('events.detail.withdrawVolunteerError'));
      setRegistrationErrorMessage(errorMessage);
      setShowRegistrationFailureDialog(true);
    }
    
    setVolunteerApplicationIdToWithdraw(null);
  }, [volunteerApplicationIdToWithdraw, withdrawVolunteerApplication, event?.id, showSuccessDialog, refetchEventDetails, refetchUserEventVolunteerApps, t]);

  // === Button State Calculators ===
  
  // Calculate participant button state
  const participantButtonState: ButtonState = useMemo(() => {
    if (!isAuthenticated) {
      return {
        text: t('auth.loginRequired'),
        mode: 'contained',
        action: () => setShowAuthModal(true),
        visible: true
      };
    }

    // Handle existing registration status
    if (currentRegistrationStatus) {
      switch (currentRegistrationStatus) {
        case 'registered':
          return {
            text: t('events.detail.cancelParticipation'),
            mode: 'outlined',
            action: handleParticipantCancelAction,
            loading: isCancellingRegistration,
            disabled: isApplyingAsVolunteer || isWithdrawingVolunteerApp,
            visible: true
          };
        case 'waitlisted':
          return {
            text: t('events.detail.cancelWaitlistSpot'),
            mode: 'outlined',
            action: handleParticipantCancelAction,
            loading: isCancellingRegistration,
            disabled: isApplyingAsVolunteer || isWithdrawingVolunteerApp,
            visible: true
          };
        case 'pending_approval':
          return {
            text: t('events.detail.statusPendingApproval'),
            mode: 'contained',
            action: () => {},
            disabled: true,
            visible: true
          };
        case 'attended':
          return {
            text: t('events.detail.statusAttended'),
            mode: 'contained',
            action: () => {},
            disabled: true,
            visible: true
          };
        case 'cancelled_by_user':
        case 'cancelled_by_admin':
        case 'rejected_approval':
        case 'absent':
          if (!isEventOpen) {
            return {
              text: t('events.detail.registrationClosed'),
              mode: 'contained',
              action: () => {},
              disabled: true,
              visible: true
            };
          }
          // Fall through to default registration logic
          break;
      }
    }

    // Handle new registration (no status or terminal status allowing re-join)
    if (!currentRegistrationStatus || ['cancelled_by_user', 'cancelled_by_admin', 'rejected_approval', 'absent'].includes(currentRegistrationStatus)) {
      if (!isEventOpen) {
        return {
          text: t('events.detail.registrationClosed'),
          mode: 'contained',
          action: () => {},
          disabled: true,
          visible: true
        };
      }

      // Check event capacity
      if (eventCapacity.hasAvailableSpots) {
        return {
          text: t('events.detail.participate'),
          mode: 'contained',
          action: handleParticipantRegister,
          loading: isRegisteringAsParticipant,
          disabled: isApplyingAsVolunteer || isWithdrawingVolunteerApp,
          visible: true
        };
      } else if (eventCapacity.hasAvailableWaitlistSpots) {
        return {
          text: t('events.detail.joinWaitingList'),
          mode: 'contained',
          action: handleParticipantRegister,
          loading: isRegisteringAsParticipant,
          disabled: isApplyingAsVolunteer || isWithdrawingVolunteerApp,
          visible: true
        };
      } else {
        return {
          text: t('events.detail.eventFull'),
          mode: 'contained',
          action: () => {},
          disabled: true,
          visible: true
        };
      }
    }

    // Default fallback
    return {
      text: '',
      mode: 'contained',
      action: () => {},
      visible: false
    };
  }, [
    isAuthenticated, currentRegistrationStatus,
    isEventOpen, eventCapacity, handleParticipantCancelAction,
    handleParticipantRegister, isCancellingRegistration, isRegisteringAsParticipant,
    isApplyingAsVolunteer, isWithdrawingVolunteerApp, t
  ]);

  // Calculate volunteer button state
  const volunteerButtonState: ButtonState = useMemo(() => {
    if (!isAuthenticated) {
      return {
        text: '',
        mode: 'outlined',
        action: () => {},
        visible: false
      };
    }

    const activeApplicationStatuses = ['approved', 'pending', 'pending_approval', 'waitlisted', 'confirmed', 'attended'];

    // Handle existing volunteer application
    if (currentVolunteerStatus && activeApplicationStatuses.includes(currentVolunteerStatus)) {
      switch (currentVolunteerStatus) {
        case 'pending':
        case 'pending_approval':
          return {
            text: t('events.detail.volunteerApplicationPending'),
            mode: 'outlined',
            action: () => {},
            disabled: true,
            visible: true
          };
        case 'approved':
        case 'waitlisted':
          return {
            text: t('events.detail.cancelVolunteerRole'),
            mode: 'outlined',
            action: () => handleOpenVolunteerWithdrawDialog(eventVolunteerApplication.id),
            loading: isWithdrawingVolunteerApp,
            disabled: isRegisteringAsParticipant || isCancellingRegistration,
            visible: true
          };
        case 'confirmed':
          return {
            text: t('events.detail.volunteerConfirmed'),
            mode: 'outlined',
            action: () => {},
            disabled: true,
            visible: true
          };
        case 'attended':
          return {
            text: t('events.detail.statusAttended'),
            mode: 'outlined',
            action: () => {},
            disabled: true,
            visible: true
          };
      }
    }

    // Hide volunteer button if user is not qualified and has no application
    if (!canVolunteerForEventOrg) {
        return {
            text: '',
            mode: 'outlined',
            action: () => {},
            visible: false
        };
    }

    // Handle new volunteer application
    if (!isEventOpen) {
      return {
        text: t('events.detail.applicationsClosed'),
        mode: 'outlined',
        action: () => {},
        disabled: true,
        visible: true
      };
    }

    if (isLoadingOrgVolunteerStatus) {
      return {
        text: t('events.detail.loadingStatus'),
        mode: 'outlined',
        action: () => {},
        loading: true,
        disabled: true,
        visible: true
      };
    }

    return {
      text: t('events.detail.volunteer'),
      mode: 'outlined',
      action: handleVolunteerApply,
      loading: isApplyingAsVolunteer || isLoadingOrgVolunteerStatus,
      disabled: isRegisteringAsParticipant || isCancellingRegistration,
      visible: true
    };
  }, [
    isAuthenticated, currentVolunteerStatus, isEventOpen,
    isLoadingOrgVolunteerStatus, handleOpenVolunteerWithdrawDialog,
    handleVolunteerApply, eventVolunteerApplication, isWithdrawingVolunteerApp,
    isApplyingAsVolunteer, isRegisteringAsParticipant, isCancellingRegistration, t,
    canVolunteerForEventOrg
  ]);

  // === Styles ===
  const styles = StyleSheet.create({
    footerContent: {
      flexDirection: 'row',
      gap: 12,
      paddingHorizontal: 16,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: '#F0F0F0',
      paddingBottom: 36,
      backgroundColor: '#FFFFFF',
    },
    registerButton: {
      flex: 1,
      borderRadius: 8,
      marginHorizontal: 4,
    },
    volunteerButton: {
      borderColor: '#FF1493',
      backgroundColor: 'transparent',
    },
    registerButtonContent: {
      height: 50,
    },
    registerButtonLabel: {
      fontSize: 16,
      fontWeight: '600',
    },
    volunteerButtonLabel: {
      color: '#FF1493',
    },
  });

  // === Render ===
  
  // If event has ended, don't show any buttons
  if (isEventEnded) {
    return null;
  }
  
  return (
    <>
      {/* Action Buttons */}
      <View style={styles.footerContent}>
        {participantButtonState.visible && (
          <Button
            mode={participantButtonState.mode}
            onPress={participantButtonState.action}
            style={styles.registerButton}
            contentStyle={styles.registerButtonContent}
            labelStyle={styles.registerButtonLabel}
            loading={participantButtonState.loading}
            disabled={participantButtonState.disabled}
          >
            {participantButtonState.text}
          </Button>
        )}
        {volunteerButtonState.visible && (
          <Button
            mode={volunteerButtonState.mode}
            onPress={volunteerButtonState.action}
            style={[styles.registerButton, styles.volunteerButton]}
            contentStyle={styles.registerButtonContent}
            labelStyle={[styles.registerButtonLabel, styles.volunteerButtonLabel]}
            buttonColor="transparent"
            textColor="#FF1493"
            loading={volunteerButtonState.loading}
            disabled={volunteerButtonState.disabled}
          >
            {volunteerButtonState.text}
          </Button>
        )}
      </View>

      {/* Authentication Modal */}
      <AuthModal
        visible={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onLogin={() => {
          setShowAuthModal(false);
          router.push('/tabs/login');
        }}
      />

      {/* Registration/Application Confirmation Dialog */}
      <TextDialog
        visible={showConfirmDialog}
        title={t('events.detail.confirmRegistration')}
        message={registrationRole === 'volunteer'
          ? t('events.detail.volunteerConfirmGuide')
          : t('events.detail.participantConfirmGuide')}
        confirmText={t('common.confirm')}
        cancelText={t('common.cancel')}
        onConfirm={handleRegistrationSubmit}
        onCancel={() => setShowConfirmDialog(false)}
        type="info"
      />

      {/* Registration Success Dialog */}
      <TextDialog
        visible={showRegistrationSuccessDialog}
        title={t('events.detail.registrationSuccess')}
        message={registrationRole === 'volunteer'
          ? t('events.detail.volunteerSuccessGuide')
          : t('events.detail.participantSuccessGuide')}
        confirmText={t('common.ok')}
        onConfirm={() => setShowRegistrationSuccessDialog(false)}
        type="success"
      />

      {/* Registration Failure Dialog */}
      <TextDialog
        visible={showRegistrationFailureDialog}
        title={t('events.detail.registrationFailed')}
        message={registrationErrorMessage}
        confirmText={t('common.ok')}
        onConfirm={() => setShowRegistrationFailureDialog(false)}
        type="warning"
      />

      {/* Verification Alert Dialog */}
      <CustomDialog
        visible={showVerificationAlertDialog}
        title={t('events.detail.verificationRequiredTitle')}
        message={verificationAlertMessage}
        confirmText={t('events.detail.goToVerification')}
        cancelText={t('common.ok')}
        onConfirm={() => {
          setShowVerificationAlertDialog(false);
          router.push('/user-profile/identification/IdentifyStatusScreen');
        }}
        onCancel={() => setShowVerificationAlertDialog(false)}
        type="warning"
      />

      {/* Volunteer Join Organization Dialog */}
      <TextDialog
        visible={showVolunteerJoinOrgDialog}
        title={t('events.detail.volunteerJoinOrgTitle')}
        message={volunteerJoinOrgMessage}
        confirmText={t('common.ok')}
        onConfirm={() => setShowVolunteerJoinOrgDialog(false)}
        type="info"
      />

      {/* Participant Cancellation Reason Input Dialog */}
      <TextInputDialog
        visible={showParticipantReasonDialog}
        title={t('events.detail.cancelReasonTitle')}
        placeholder={t('events.detail.cancelReasonPlaceholder')}
        value={participantCancellationReason}
        onChangeText={setParticipantCancellationReason}
        confirmText={t('common.continue')}
        cancelText={t('common.cancel')}
        onCancel={() => {
          setShowParticipantReasonDialog(false);
          setParticipantCancellationReason('');
        }}
        onConfirm={handleSubmitParticipantReasonAndProceedToConfirm}
        maxLength={200}
      />

      {/* Participant Cancellation Confirmation Dialog */}
      <CustomDialog
        visible={showParticipantCancelDialog}
        title={t('events.detail.cancelRegistrationTitle')}
        message={t('events.detail.cancelRegistrationMessage')}
        confirmText={t('common.confirm')}
        cancelText={t('common.cancel')}
        onConfirm={handleConfirmParticipantCancel}
        onCancel={() => setShowParticipantCancelDialog(false)}
        type="warning"
        confirmLoading={isCancellingRegistration}
      />

      {/* Volunteer Withdrawal Dialog */}
      <CustomDialog
        visible={showVolunteerWithdrawDialog}
        title={t('events.detail.withdrawVolunteerTitle')}
        message={t('events.detail.withdrawVolunteerMessage')}
        confirmText={t('common.confirm')}
        cancelText={t('common.cancel')}
        onConfirm={handleConfirmVolunteerWithdraw}
        onCancel={() => setShowVolunteerWithdrawDialog(false)}
        type="warning"
        confirmLoading={isWithdrawingVolunteerApp}
      />

      {/* Success Dialog */}
      <CustomDialog
        visible={successDialogVisible}
        title={t('common.success')}
        message={successMessage}
        confirmText={t('common.ok')}
        onConfirm={() => setSuccessDialogVisible(false)}
        type="success"
      />

      {/* Notification Dialog */}
      <CustomDialog
        visible={notificationDialogVisible}
        title={t('common.notification')}
        message={notificationMessage}
        confirmText={t('common.ok')}
        onConfirm={() => setNotificationDialogVisible(false)}
        type="info"
      />
    </>
  );
};

export default EventActionButtons; 