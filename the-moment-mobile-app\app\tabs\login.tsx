import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, useWindowDimensions, ScrollView, SafeAreaView } from 'react-native';
import { Image } from 'expo-image';
import { Button } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { LanguageSwitch } from '@/common_modules/LanguageSwitch';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';

export default function LoginTabScreen() {
    const router = useRouter();
    const { t, i18n } = useTranslation();
    const storeTheme = appStyleStore(state => state.theme);
    const { width, height } = useWindowDimensions();
    const [languageModalVisible, setLanguageModalVisible] = useState(false);

    // Use store theme or default to 'red' without causing re-renders
    const activeTheme: ReturnType<typeof createTheme> = storeTheme || createTheme('red');
    const styles = getStyles(activeTheme, width, height);

    const getLogoSource = () => {
        return require('@/assets/default-images/default-logo.png');
    };
    
    const handleWhatsAppLogin = () => {
        router.push({
            pathname: '/login/WhatsAppVerificationScreen',
        });
    };

    const handleTermsPress = () => {
        router.push('/documents/TermsOfServiceScreen');
    };

    const handlePrivacyPress = () => {
        router.push('/documents/PrivacyPolicyScreen');
    };

    return (
        <SafeAreaView style={[styles.safeArea, { backgroundColor: activeTheme.system.background }]}>
            <View style={[styles.container, { backgroundColor: activeTheme.system.background }]}>
                <TouchableOpacity
                    style={styles.langButtonTop}
                    onPress={() => setLanguageModalVisible(true)}
                >
                    <Ionicons name="language" size={20} color={activeTheme.system.secondaryText} />
                    <Text style={[styles.langButtonText, { color: activeTheme.system.text }]}>
                        {i18n.language === 'en' ? 'English' : '繁體中文'}
                    </Text>
                </TouchableOpacity>

                <View style={styles.centerWrapper}>
                    <View style={styles.topContent}>
                        <View style={styles.logoContainer}>
                            <Image
                                source={getLogoSource()}
                                style={styles.logo}
                                contentFit="contain"
                            />
                            <View style={styles.titleContainer}>
                                <Text style={[styles.title, { color: activeTheme.colors.primary }]}>
                                    {t('auth.appName')}
                                </Text>
                                <Text style={[styles.subtitle, { color: activeTheme.system.secondaryText }]}>
                                    {t('auth.slogan')}
                                </Text>
                            </View>
                        </View>
                    </View>

                    <View style={styles.bottomContent}>
                        <View style={styles.buttonContainer}>
                            <Button
                                onPress={handleWhatsAppLogin}
                                style={styles.whatsappButton}
                                contentStyle={styles.buttonContent}
                                labelStyle={styles.whatsappLabel}
                                buttonColor="transparent"
                                textColor="#25D366"
                                rippleColor="rgba(37, 211, 102, 0.1)"
                                icon={({ size }) => (
                                    <MaterialCommunityIcons name="whatsapp" size={24} color="#25D366" />
                                )}
                            >
                                {t('auth.continueWithWhatsApp')}
                            </Button>
                        </View>
                        <Text style={[styles.terms, { color: activeTheme.system.secondaryText }]}>
                            {t('auth.termsAgreement.prefix')}{' '}
                            <Text style={[styles.linkText, { color: activeTheme.colors.primary }]} onPress={handleTermsPress}>
                                {t('auth.termsAgreement.terms')}
                            </Text>
                            {' '}{t('auth.termsAgreement.and')}{' '}
                            <Text style={[styles.linkText, { color: activeTheme.colors.primary }]} onPress={handlePrivacyPress}>
                                {t('auth.termsAgreement.privacy')}
                            </Text>
                        </Text>
                    </View>
                </View>

                <LanguageSwitch
                    visible={languageModalVisible}
                    onClose={() => setLanguageModalVisible(false)}
                />
            </View>
        </SafeAreaView>
    );
}

const getStyles = (theme: ReturnType<typeof createTheme>, width: number, height: number) => StyleSheet.create({
    safeArea: {
        flex: 1,
    },
    container: {
        flex: 1,
        justifyContent: 'flex-start',
    },
    langButtonTop: {
        position: 'absolute',
        top: 16,
        right: 16,
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderRadius: 16,
        borderWidth: 1,
        borderColor: theme.system.border,
        backgroundColor: `${theme.system.background}CC`,
        zIndex: 1000,
        elevation: 5,
    },
    centerWrapper: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 24,
        paddingTop: Platform.OS === 'ios' ? 80 : 60,
        paddingBottom: 40,
    },
    topContent: {
        alignItems: 'center',
        width: '100%',
        flex: 1,
        justifyContent: 'center',
        minHeight: height * 0.4,
    },
    bottomContent: {
        width: '100%',
        alignItems: 'center',
        paddingBottom: 20,
    },
    closeButton: {
        marginLeft: 10,
        padding: 5,
    },
    langButtonText: {
        fontSize: 14,
        fontWeight: '500',
        marginHorizontal: 6,
    },
    logoContainer: {
        alignItems: 'center',
        flexDirection: 'column',
        paddingHorizontal: 20,
        width: '100%',
    },
    logo: {
        height: Math.min(height * 0.25, width * 0.5, 200),
        aspectRatio: 1,
        width: undefined,
        maxWidth: Math.min(width * 0.6, 200),
        marginBottom: height > 700 ? 30 : 20,
    },
    logoChevron: {
        marginTop: 10,
    },
    titleContainer: {
        minHeight: height > 700 ? 100 : 80,
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        paddingHorizontal: 20,
    },
    title: {
        fontSize: Math.min(width * 0.06, 28),
        fontWeight: '600',
        marginBottom: height > 700 ? 12 : 8,
        lineHeight: Math.min(width * 0.08, 36),
        textAlign: 'center',
    },
    subtitle: {
        fontSize: Math.min(width * 0.04, 18),
        marginBottom: height > 700 ? 30 : 20,
        lineHeight: Math.min(width * 0.06, 24),
        textAlign: 'center',
        paddingHorizontal: 10,
    },
    buttonContainer: {
        width: '100%',
        marginBottom: 30,
    },
    whatsappButton: {
        borderRadius: 12,
        height: 52,
        borderColor: '#25D366',
        borderWidth: 1.5,
        width: '100%',
    },
    buttonContent: {
        height: 52,
    },
    whatsappLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#25D366',
    },
    terms: {
        textAlign: 'center',
        fontSize: 14,
        lineHeight: 20,
        fontWeight: '500',
        paddingHorizontal: 20,
        maxWidth: width * 0.9,
    },
    linkText: {
        textDecorationLine: 'underline',
        fontSize: 14,
    },
}); 