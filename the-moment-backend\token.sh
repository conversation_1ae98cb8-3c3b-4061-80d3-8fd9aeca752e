#!/bin/bash

# Load environment variables from .env file
if [ -f .env ]; then
    export $(cat .env | sed 's/#.*//g' | xargs)
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "Error: DATABASE_URL is not set. Please create a .env file from .env.example."
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null
then
    echo "jq could not be found. Please install it to run this script."
    echo "On Debian/Ubuntu: sudo apt-get install jq"
    echo "On macOS: brew install jq"
    exit
fi

# Check if openssl is installed
if ! command -v openssl &> /dev/null
then
    echo "openssl could not be found. Please install it to run this script."
    echo "On Debian/Ubuntu: sudo apt-get install openssl"
    echo "On macOS: brew install openssl"
    exit
fi

echo "Fetching superadmin password from the database..."

# Get the password hash for the superadmin user directly from the database
# Note: This is a simplified approach for a local development script.
# In a real-world scenario, you wouldn't typically do this.
PASSWORD_HASH=$(psql $DATABASE_URL -t -c "SELECT hashed_password FROM users WHERE email = '<EMAIL>';")

# Trim whitespace
PASSWORD_HASH=$(echo $PASSWORD_HASH | xargs)

if [ -z "$PASSWORD_HASH" ] || [ "$PASSWORD_HASH" == "NULL" ]; then
    echo "Error: Could not retrieve password <NAME_EMAIL>."
    echo "Please ensure the database is running and has been seeded correctly (check migrations)."
    exit 1
fi

echo "Superadmin password hash retrieved. Using default password '123456' for login."

# The default password used during seeding is '123456'.
# We will use this to log in.

# Generate PKCE parameters
# Generate a URL-safe code verifier (43-128 characters)
CODE_VERIFIER=$(openssl rand -base64 43 | tr -d '+/=' | head -c 43)

# Generate code challenge (BASE64URL-ENCODE(SHA256(ASCII(code_verifier))))
CODE_CHALLENGE=$(echo -n "$CODE_VERIFIER" | openssl dgst -sha256 -binary | openssl base64 -e -A | sed 's/+/-/g' | sed 's/\//_/g' | sed 's/=//g')

# Generate a random state for the client
CLIENT_STATE=$(openssl rand -hex 16)

# Initiate login to get flow_id and state
echo "Initiating staff login..."
LOGIN_INIT_RESPONSE=$(curl -s --location 'http://localhost:8080/api/v1/authn/staff/login/initiate' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email": "<EMAIL>",
    "client_id": "default_client",
    "code_challenge": "'"$CODE_CHALLENGE"'",
    "code_challenge_method": "S256",
    "state": "'"$CLIENT_STATE"'"
}')

FLOW_ID=$(echo $LOGIN_INIT_RESPONSE | jq -r '.flow_id')
STATE=$(echo $LOGIN_INIT_RESPONSE | jq -r '.state')

if [ -z "$FLOW_ID" ] || [ "$FLOW_ID" == "null" ]; then
    echo "Error: Failed to initiate staff login."
    echo "Response: $LOGIN_INIT_RESPONSE"
    exit 1
fi

echo "Login initiated. Flow ID: $FLOW_ID"

# Verify login to get tokens
echo "Verifying staff login to get token..."
LOGIN_VERIFY_RESPONSE=$(curl -s --location 'http://localhost:8080/api/v1/authn/staff/login/verify' \
--header 'Content-Type: application/json' \
--data-raw '{
    "state": "'"$STATE"'",
    "email": "<EMAIL>",
    "password": "123456",
    "code_verifier": "'"$CODE_VERIFIER"'"
}')

ACCESS_TOKEN=$(echo $LOGIN_VERIFY_RESPONSE | jq -r '.access_token')

if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" == "null" ]; then
    echo "Error: Failed to get access token."
    echo "Response: $LOGIN_VERIFY_RESPONSE"
    exit 1
fi

# Save the token to a file
echo $ACCESS_TOKEN > token.txt

echo "Admin access token retrieved successfully and saved to token.txt."
echo "You can now use this token to authenticate with the API." 