-- Drop Triggers first (if they reference the tables or functions)
DROP TRIGGER IF EXISTS set_timestamp_event_volunteer_applications ON "event_volunteer_applications";
DROP TRIGGER IF EXISTS set_timestamp_event_registrations ON "event_registrations";
DROP TRIGGER IF EXISTS set_timestamp_event_tags ON "event_tags";
DROP TRIGGER IF EXISTS set_timestamp_events ON "events";

-- Drop the function if it's no longer needed by other tables
-- (Assuming it's specific to these tables for now)
DROP FUNCTION IF EXISTS trigger_set_timestamp();

-- Drop Tables in reverse order of creation or by dependency
DROP TABLE IF EXISTS "event_volunteer_applications";
DROP TABLE IF EXISTS "event_registrations";
DROP TABLE IF EXISTS "event_required_verification_types";
DROP TABLE IF EXISTS "event_event_tags";
DROP TABLE IF EXISTS "event_tags";
DROP TABLE IF EXISTS "event_media_items";
DROP TABLE IF EXISTS "events";

-- Drop Enum Types in reverse order of creation
DROP TYPE IF EXISTS event_volunteer_application_status_type;
DROP TYPE IF EXISTS check_in_method_type;
DROP TYPE IF EXISTS event_registration_role_type;
DROP TYPE IF EXISTS payment_status_type;
DROP TYPE IF EXISTS event_registration_status_type;
DROP TYPE IF EXISTS event_media_type;
DROP TYPE IF EXISTS event_location_type;
DROP TYPE IF EXISTS event_status_type;

-- Note: The pgcrypto extension is usually not dropped in a down migration
-- as it might be used by other parts of the database.
-- If you are sure it's not needed, you can add: DROP EXTENSION IF EXISTS "pgcrypto"; 