import React, { useEffect, useState } from 'react';
import { Stack } from 'expo-router';
import { Provider as PaperProvider } from 'react-native-paper';
import { I18nextProvider, useTranslation } from 'react-i18next';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { initializeI18n } from '@/i18n/index';
import { type i18n as I18n } from 'i18next';
import * as SplashScreen from 'expo-splash-screen';
import { appStyleStore } from '@/stores/app_style_store';
import { organizationStore } from '@/stores/organization_store';

// Helper component to correctly apply PaperProvider theme
const PaperThemeProvider = ({ children }: { children: React.ReactNode }) => {
  // Use the hook inside the component to respond to theme changes
  const theme = appStyleStore(state => state.theme);
  return <PaperProvider theme={theme}>{children}</PaperProvider>;
};

// Prevent the splash screen from auto-hiding before app setup is complete
SplashScreen.preventAutoHideAsync();

// Create a client
const queryClient = new QueryClient();

export default function RootLayout() {
  const [i18nInstance, setI18nInstance] = useState<I18n | null>(null);
  const [isAppReady, setIsAppReady] = useState(false);

  useEffect(() => {
    async function prepareApp() {
      try {
        const i18n = await initializeI18n();
        setI18nInstance(i18n);
        
        // Initialize theme based on selected organization
        const selectedOrganization = organizationStore.getState().selectedOrganization;
        if (selectedOrganization?.theme_color) {
          appStyleStore.getState().setTheme(selectedOrganization.theme_color);
        }
      } catch (error) {
        console.error("Failed to initialize the app:", error);
      } finally {
        setIsAppReady(true);
        SplashScreen.hideAsync();
      }
    }

    prepareApp();
  }, []);

  if (!isAppReady || !i18nInstance) {
    return null;
  }

  // New component for the main stack that uses translation and dynamic theme
  const TranslatedAppStack = () => {
    const { t } = useTranslation();
    // Use the hook inside the component to respond to theme changes
    const theme = appStyleStore(state => state.theme);

    return (
      <Stack
        screenOptions={{
          headerShown: true,
          contentStyle: {
            backgroundColor: theme.colors.background
          },
          headerTitleAlign: 'center',
          headerTitleStyle: {
            color: theme.system.text,
            fontSize: 17,
            fontWeight: '700',
          },
          headerShadowVisible: false,
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerTintColor: theme.colors.primary,
          headerBackTitle: t('common.back'),
        }}
      >
        <Stack.Screen name="tabs" options={{ headerShown: false }} />
      </Stack>
    );
  };

  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18nInstance}>
        <PaperThemeProvider>
          <TranslatedAppStack />
        </PaperThemeProvider>
      </I18nextProvider>
    </QueryClientProvider>
  );
}
