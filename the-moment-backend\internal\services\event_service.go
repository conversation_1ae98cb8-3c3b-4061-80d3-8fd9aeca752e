package services

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	_ "image/gif"  // for decoding GIF
	_ "image/jpeg" // for decoding JPEG
	_ "image/png"  // for decoding PNG
	"io"
	"mime/multipart"
	"net/url" // Added for parsing URL in MediaEventItem
	"os"
	"path/filepath" // Added for extracting filename in MediaEventItem
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
	// _ "golang.org/x/image/webp" // for decoding WebP - Removed due to linter error
)

const (
	DefaultPageSize                  = 20
	MaxPageSize                      = 100
	DefaultWaitlistPromotionJobDelay = 10 * time.Second
	DefaultNotificationJobDelay      = 5 * time.Second
	ReminderBefore24Hours            = 24 * time.Hour
	ReminderBefore1Hour              = 1 * time.Hour
	DefaultPopularEventsLimit        = 5
)

var (
	ErrUserNotMemberOfOrg              = errors.New("user is not a member of the organization")
	ErrEventNotFound                   = errors.New("event not found")
	ErrRegistrationNotFound            = errors.New("registration not found")
	ErrRegistrationClosed              = errors.New("event registration is closed")
	ErrEventCapacityReached            = errors.New("event capacity reached, cannot register more attendees")
	ErrWaitlistFull                    = errors.New("event waitlist is full")
	ErrAlreadyRegistered               = errors.New("already registered for this event")
	ErrAlreadyWaitlisted               = errors.New("already on waitlist for this event")
	ErrCannotCancel                    = errors.New("cannot cancel registration at this time or status")
	ErrRequiresApproval                = errors.New("registration requires approval")
	ErrNotOnWaitlist                   = errors.New("user is not on the waitlist")
	ErrVolunteerApplicationNotFound    = errors.New("volunteer application not found")
	ErrAlreadyAppliedToVolunteer       = errors.New("user has already applied to volunteer for this event")
	ErrEventTagNotFound                = errors.New("event tag not found")
	ErrEventMediaItemNotFound          = errors.New("event media item not found")
	ErrUserNotVerifiedForRequiredTypes = errors.New("user lacks required verifications for this event")
	ErrTimeConflict                    = errors.New("event time conflict")
	ErrInvalidPaymentStatus            = errors.New("invalid payment status")
	ErrOrgQualificationRequired        = errors.New("user must be a qualified volunteer for the organization to apply for its events")
)

// EventTimeConflictError is a custom error type for event time conflicts.
// It includes details of the conflicting event.
// It includes details of the conflicting event.
type EventTimeConflictError struct {
	Details *payloads.ConflictingEventDetails
	message string
}

// NewEventTimeConflictError creates a new EventTimeConflictError.
func NewEventTimeConflictError(details *payloads.ConflictingEventDetails) *EventTimeConflictError {
	return &EventTimeConflictError{
		Details: details,
		message: "User registration blocked due to time conflict with another registered event.",
	}
}

// Error implements the error interface for EventTimeConflictError.
func (e *EventTimeConflictError) Error() string {
	return e.message
}

// Unwrap provides compatibility with errors.Is and errors.As.
func (e *EventTimeConflictError) Unwrap() error {
	return ErrTimeConflict // Returns the generic ErrTimeConflict for errors.Is checks
}

// EventService defines the interface for event management operations.
// It outlines methods for creating, retrieving, updating, and deleting events,
// managing event registrations, tags, media, volunteer applications, and statistics.
// Implementations of this interface will handle the business logic and database interactions
// related to events.
type EventService interface {
	// --- Event Management ---
	CreateEvent(ctx context.Context, orgID uuid.UUID, userID uuid.UUID, params payloads.CreateEventRequest) (payloads.EventResponse, error)
	GetEventByID(ctx context.Context, eventID uuid.UUID, currentUserID uuid.UUID, isAdminView bool) (payloads.EventResponse, error)
	GetPublicEventByID(ctx context.Context, eventID uuid.UUID, currentUserID uuid.UUID) (payloads.PublicEventResponse, error)                                                      // Slimmer version for public view
	ListEventsByOrganization(ctx context.Context, orgID uuid.UUID, userID uuid.UUID, filterParams payloads.ListOrganizationEventsRequest) ([]payloads.EventResponse, int64, error) // Updated to use filterParams
	ListPublicEvents(ctx context.Context, userID uuid.UUID, filterParams payloads.ListPublicEventsRequest) ([]payloads.PublicEventResponse, int64, error)
	UpdateEventDetails(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID, req payloads.UpdateEventRequest) (payloads.EventResponse, error) // Corrected return type
	DeleteEvent(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error

	// --- Home Page Event Lists ---
	ListPopularEvents(ctx context.Context, limit int) ([]payloads.PopularEvent, error)                                // New
	ListPopularEventsByOrganization(ctx context.Context, orgID uuid.UUID, limit int) ([]payloads.PopularEvent, error) // New

	// --- Event Media ---
	AddEventMediaItem(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID, fileHeader *multipart.FileHeader, isBanner bool) (payloads.MediaItemResponse, error)
	ListEventMediaItems(ctx context.Context, eventID uuid.UUID) ([]db.EventMediaItem, error)                                // Renamed, updated return type
	DeleteEventMediaItem(ctx context.Context, itemID uuid.UUID, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error // Renamed
	SetBannerForEventMediaItem(ctx context.Context, eventID uuid.UUID, mediaItemID uuid.UUID, orgID uuid.UUID, actingUserID uuid.UUID) (payloads.MediaItemResponse, error)

	// --- Event Tags ---
	CreateEventTag(ctx context.Context, userID uuid.UUID, params payloads.CreateEventTagRequest) (payloads.TagResponse, error)
	UpdateEventTag(ctx context.Context, tagID uuid.UUID, params payloads.UpdateEventTagRequest) (payloads.TagResponse, error)
	ListEventTags(ctx context.Context, approved *bool, pageParams payloads.PageRequest) ([]payloads.TagResponse, error)
	AddTagToEvent(ctx context.Context, eventID uuid.UUID, tagID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error
	RemoveTagFromEvent(ctx context.Context, eventID uuid.UUID, tagID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error
	ListTagsForEvent(ctx context.Context, eventID uuid.UUID) ([]payloads.TagResponse, error)
	GetEventTag(ctx context.Context, tagID uuid.UUID) (payloads.TagResponse, error)
	DeleteEventTag(ctx context.Context, tagID uuid.UUID) error

	// --- Event Required Verification Types ---
	AddEventRequiredVerificationType(ctx context.Context, eventID uuid.UUID, verificationTypeKey string, orgID uuid.UUID, userID uuid.UUID) error
	RemoveEventRequiredVerificationType(ctx context.Context, eventID uuid.UUID, verificationTypeKey string, orgID uuid.UUID, userID uuid.UUID) error
	ListRequiredVerificationTypesForEvent(ctx context.Context, eventID uuid.UUID) ([]string, error)

	// --- Event Registration ---
	GetUserEventRegistration(ctx context.Context, userID uuid.UUID, eventID uuid.UUID) (payloads.EventRegistrationResponse, error)
	ListUserRegistrations(ctx context.Context, userID uuid.UUID, params payloads.ListUserRegistrationsRequest) ([]payloads.EventRegistrationResponse, int64, error)                                          // User's "My Events"
	ListEventRegistrations(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID, pageParams payloads.PageRequest) ([]payloads.EventRegistrationResponse, int64, error)                  // Admin view, added totalCount
	UpdateRegistrationStatusByAdmin(ctx context.Context, registrationID uuid.UUID, orgID uuid.UUID, adminUserID uuid.UUID, newStatus string, adminNotes *string) (payloads.EventRegistrationResponse, error) // Approval/Rejection
	CancelEventRegistration(ctx context.Context, userID uuid.UUID, registrationID uuid.UUID) (payloads.EventRegistrationResponse, error)
	UpdatePaymentStatus(ctx context.Context, registrationID uuid.UUID, newPaymentStatus string, staffOrVolunteerID uuid.UUID) (payloads.EventRegistrationResponse, error) // No auth for this API endpoint as per requirement
	PromoteFromWaitlist(ctx context.Context, eventID uuid.UUID, slotsToFill int) ([]payloads.EventRegistrationResponse, error)                                            // Returns slice of promoted registrations

	// --- Event Volunteering ---
	ApplyForEventVolunteering(ctx context.Context, userID uuid.UUID, eventID uuid.UUID, notes *string) (db.EventVolunteerApplication, error)
	ListUserEventVolunteerApplications(ctx context.Context, userID uuid.UUID, pageParams payloads.PageRequest) ([]payloads.EventVolunteerApplicationResponse, error)
	ListEventVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, adminUserID uuid.UUID, pageParams payloads.PageRequest) ([]payloads.EventVolunteerApplicationResponse, error) // Admin view
	ReviewEventVolunteerApplication(ctx context.Context, applicationID uuid.UUID, orgID uuid.UUID, eventID uuid.UUID, adminUserID uuid.UUID, newStatus string, adminNotes *string) (payloads.EventVolunteerApplicationResponse, error)
	WithdrawEventApplication(ctx context.Context, userID uuid.UUID, appID uuid.UUID) (payloads.EventVolunteerApplicationResponse, error) // New method for withdrawal
	GetEventVolunteerApplicationDetails(ctx context.Context, requestingUserID uuid.UUID, orgID uuid.UUID, eventID uuid.UUID, appID uuid.UUID) (payloads.EventVolunteerApplicationResponse, error)

	// --- Event Statistics ---
	GetUserEventStatistics(ctx context.Context, userID uuid.UUID, startDate, endDate *time.Time, pageParams payloads.PageRequest) ([]payloads.UserEventStatisticItem, error)
	GetOrganizationEventStatistics(ctx context.Context, orgID uuid.UUID, adminUserID uuid.UUID, startDate, endDate *time.Time, tagIDs []uuid.UUID, pageParams payloads.PageRequest) ([]payloads.OrgEventStatisticItem, error)
	GetEventStatistics(ctx context.Context, params payloads.EventStatisticsRequest) (*payloads.EventStatisticsResponse, error)                                 // New method for general event statistics
	GetEventStatisticsForOrganization(ctx context.Context, orgID uuid.UUID, params payloads.EventStatisticsRequest) (*payloads.EventStatisticsResponse, error) // New method for org-specific event statistics
	GetSpecificEventStatistics(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) (*payloads.SpecificEventStatisticsResponse, error)

	// --- Pending Review Volunteer Applications ---
	ListPendingReviewVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID, pageID int32, pageSize int32) ([]db.ListPendingReviewEventVolunteerApplicationsForEventRow, int64, error)
	ListPendingReviewVolunteerApplicationsForOrganization(ctx context.Context, orgID uuid.UUID, pageID int32, pageSize int32) ([]db.ListPendingReviewEventVolunteerApplicationsForOrganizationRow, int64, error)
	ListAllPendingReviewVolunteerApplications(ctx context.Context, pageID int32, pageSize int32) ([]db.ListAllPendingReviewEventVolunteerApplicationsRow, int64, error)

	// New method for listing event volunteer applications for an organization with status filter
	ListOrgEventVolunteerApplications(ctx context.Context, orgID uuid.UUID, status *string, pageParams payloads.PageRequest) ([]payloads.EventVolunteerApplicationResponse, int64, error)

	CountVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID) (int64, error)                                                           // New method
	GetUserEventVolunteerApplicationDetails(ctx context.Context, userID uuid.UUID, appID uuid.UUID) (payloads.EventVolunteerApplicationResponse, error) // New method for specific app by user
}

// eventService implements the EventService interface.
type eventService struct {
	queries             *db.Queries
	pool                *pgxpool.Pool
	notificationService NotificationService
	jobService          JobService // Added JobService
}

// NewEventService creates a new instance of EventService.
func NewEventService(q *db.Queries, pool *pgxpool.Pool, notifySvc NotificationService, jobSvc JobService) EventService {
	return &eventService{
		queries:             q,
		pool:                pool,
		notificationService: notifySvc,
		jobService:          jobSvc, // Assign injected JobService
	}
}

// getEventResponseFromDBEvent is a helper to construct the full EventResponse.
func (s *eventService) getEventResponseFromDBEvent(ctx context.Context, qtx *db.Queries, dbEvent db.Event, currentUserID uuid.UUID, isAdminView bool) (payloads.EventResponse, error) {
	resp := payloads.EventResponse{
		ID:                              dbEvent.ID,
		OrganizationID:                  dbEvent.OrganizationID,
		Title:                           dbEvent.Title,
		JsonContent:                     dbEvent.DescriptionContent,
		LocationType:                    string(dbEvent.LocationType),
		LocationFullAddress:             dbEvent.LocationFullAddress,
		LocationOnlineURL:               dbEvent.LocationOnlineUrl,
		StartTime:                       dbEvent.StartTime,
		EndTime:                         dbEvent.EndTime,
		Status:                          string(dbEvent.Status),
		ParticipantLimit:                dbEvent.ParticipantLimit,
		WaitlistLimit:                   dbEvent.WaitlistLimit,
		RequiresApprovalForRegistration: dbEvent.RequiresApprovalForRegistration,
		CreatedByUserID:                 dbEvent.CreatedByUserID,
		CreatedAt:                       dbEvent.CreatedAt,
		UpdatedAt:                       dbEvent.UpdatedAt,
		PublishedAt:                     dbEvent.PublishedAt,
		GovernmentFundingKeys:           dbEvent.GovernmentFundingKeys, // Added this line
	}

	// Price, ContactEmail, ContactPhone are *string in db.Event
	resp.Price = dbEvent.Price
	resp.ContactEmail = dbEvent.ContactEmail
	resp.ContactPhone = dbEvent.ContactPhone

	// Attempt to get Organization Name
	if dbEventWithOrg, ok := interface{}(dbEvent).(interface{ GetOrganizationName() string }); ok {
		resp.OrganizationName = dbEventWithOrg.GetOrganizationName()
	} else {
		org, err := qtx.GetOrganizationByID(ctx, dbEvent.OrganizationID) // Changed from GetOrganizationMinByID
		if err == nil {
			resp.OrganizationName = org.Name
		} else if !errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Error().Err(err).Str("orgID", dbEvent.OrganizationID.String()).Msg("Failed to fetch organization name for event response")
		}
	}

	// Fetch and map Tags
	dbTags, err := qtx.ListTagsForEvent(ctx, dbEvent.ID)
	if err == nil {
		resp.Tags = make([]payloads.TagResponse, len(dbTags))
		for i, t := range dbTags {
			resp.Tags[i] = toEventTagResponse(t)
		}
	} else {
		log.Ctx(ctx).Error().Err(err).Str("eventID", dbEvent.ID.String()).Msg("Failed to list tags for event")
	}

	// Fetch and map Event Media Items (formerly Banner Image URLs)
	dbMediaItems, err := qtx.ListEventMediaItems(ctx, dbEvent.ID) // Updated to ListEventMediaItems
	if err == nil {
		resp.MediaItems = make([]payloads.MediaItemResponse, len(dbMediaItems)) // Assuming a new MediaItemResponse in payloads
		for i, m := range dbMediaItems {
			resp.MediaItems[i] = payloads.MediaItemResponse{
				ID:         m.ID,
				FileName:   m.FileName,
				FilePath:   m.FilePath, // This should be the web-accessible path
				FileType:   m.FileType,
				FileSize:   m.FileSize,
				UploadedAt: m.UploadedAt,
				IsBanner:   m.IsBanner, // Populate IsBanner
			}
		}
		// For backward compatibility or simple banner display, we can still populate BannerImageUrls
		// This part might be removed if BannerImageUrls is fully deprecated from EventResponse
		resp.MediaEventItem = make([]string, 0, len(dbMediaItems))
		for _, m := range dbMediaItems {
			// Heuristic: if it's an image, add to banner URLs. This logic might need refinement.
			if strings.HasPrefix(m.FileType, "image/") {
				resp.MediaEventItem = append(resp.MediaEventItem, m.FilePath) // Ensure FilePath is web-accessible URL
			}
		}
	} else {
		log.Ctx(ctx).Error().Err(err).Str("eventID", dbEvent.ID.String()).Msg("Failed to list media items for event")
	}

	// Fetch Required Verification Keys
	dbVerifications, err := qtx.ListRequiredVerificationTypesForEvent(ctx, dbEvent.ID)
	if err == nil {
		resp.RequiredVerificationTypeKeys = dbVerifications
	} else {
		log.Ctx(ctx).Error().Err(err).Str("eventID", dbEvent.ID.String()).Msg("Failed to list required verifications for event")
	}

	// Populate counts
	dbEventWithCounts, err := qtx.GetEventDetailsWithCounts(ctx, dbEvent.ID)
	if err == nil {
		resp.RegisteredCount = dbEventWithCounts.RegisteredCount
		resp.WaitlistedCount = dbEventWithCounts.WaitlistedCount
		resp.AttendedCount = dbEventWithCounts.AttendedCount
		if dbEventWithCounts.OrganizationName != "" { // Still useful to prefer this if available
			resp.OrganizationName = dbEventWithCounts.OrganizationName
		}
	} else {
		log.Ctx(ctx).Error().Err(err).Str("eventID", dbEvent.ID.String()).Msg("Failed to get event counts")
		// Optionally, set counts to 0 or a specific error indicator if preferred when query fails
		// For now, they will remain as their zero values (0 for int64) if the query fails
	}

	// Populate current user's status
	if currentUserID != uuid.Nil {
		reg, err := qtx.GetEventRegistrationByUserAndEvent(ctx, db.GetEventRegistrationByUserAndEventParams{
			UserID:  currentUserID,
			EventID: dbEvent.ID,
		})
		if err == nil {
			statusStr := string(reg.Status)
			resp.CurrentUserRegistrationStatus = &statusStr
			resp.CurrentUserRegistrationID = &reg.ID
		} else if !errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Error().Err(err).Str("eventID", dbEvent.ID.String()).Str("userID", currentUserID.String()).Msg("Failed to get user registration status for event")
		}
	}

	return resp, nil
}

// CreateEvent implements EventService.
func (s *eventService) CreateEvent(ctx context.Context, orgID uuid.UUID, userID uuid.UUID, req payloads.CreateEventRequest) (payloads.EventResponse, error) {
	// Get user role to check for superadmin
	user, err := s.queries.GetUserByID(ctx, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting user by ID")
		// Decide if this should be a hard error. If user must exist, then it should be.
		return payloads.EventResponse{}, fmt.Errorf("failed to verify user: %w", err)
	}

	// Authorization: Superadmins can create events anywhere. Other users must be members.
	if user.Role != "superadmin" {
		isMember, err := s.queries.IsUserMemberOfOrganization(ctx, db.IsUserMemberOfOrganizationParams{
			UserID:         userID,
			OrganizationID: orgID,
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Error checking organization membership")
			return payloads.EventResponse{}, fmt.Errorf("failed to check organization membership: %w", err)
		}
		if !isMember {
			return payloads.EventResponse{}, ErrUserNotMemberOfOrg // Or a more specific permission error
		}
	}

	// Basic time validation (StartTime before EndTime is handled by payload validation)
	if req.StartTime.Before(time.Now()) && (req.Status == nil || *req.Status == string(db.EventStatusTypePublished)) {
		// Allow creating past events only if they are drafts or explicitly set to a past status
		// For published, start time must be in future.
		// return payloads.EventResponse{}, errors.New("event start time must be in the future for published events")
	}

	var publishedAtTime *time.Time
	if req.PublishedAt != nil {
		publishedAtTime = req.PublishedAt
	}

	eventStatus := db.EventStatusTypeDraft
	if req.Status != nil {
		// Validate and set status
		switch *req.Status {
		case string(db.EventStatusTypePublished):
			eventStatus = db.EventStatusTypePublished
			if publishedAtTime == nil { // If status is published but no publish time, set to now
				now := time.Now()
				publishedAtTime = &now
			}
		case string(db.EventStatusTypeArchived):
			eventStatus = db.EventStatusTypeArchived
		case string(db.EventStatusTypeDeleted):
			eventStatus = db.EventStatusTypeDeleted
		case string(db.EventStatusTypeHidden):
			eventStatus = db.EventStatusTypeHidden
		case string(db.EventStatusTypeCancelled):
			eventStatus = db.EventStatusTypeCancelled
		default:
			eventStatus = db.EventStatusTypeDraft // Default to draft if invalid status provided
		}
	}

	// Start a database transaction
	tx, err := s.pool.Begin(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to begin database transaction")
		return payloads.EventResponse{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx) // Rollback if anything goes wrong

	qtx := s.queries.WithTx(tx)

	dbParams := db.CreateEventParams{
		OrganizationID:                  orgID,
		Title:                           req.Title,
		DescriptionContent:              req.JsonContent,
		LocationType:                    db.EventLocationType(req.LocationType),
		LocationFullAddress:             req.LocationFullAddress,
		LocationOnlineUrl:               req.LocationOnlineURL,
		StartTime:                       req.StartTime,
		EndTime:                         req.EndTime,
		Status:                          eventStatus,
		ParticipantLimit:                req.ParticipantLimit,
		WaitlistLimit:                   req.WaitlistLimit,
		RequiresApprovalForRegistration: req.RequiresApprovalForRegistration,
		CreatedByUserID:                 userID,
		PublishedAt:                     publishedAtTime,
		Price:                           req.Price,
		ContactEmail:                    req.ContactEmail,
		ContactPhone:                    req.ContactPhone,
		GovernmentFundingKeys:           req.GovernmentFundingKeys,
	}

	createdEvent, err := qtx.CreateEvent(ctx, dbParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create event in database")
		return payloads.EventResponse{}, fmt.Errorf("failed to create event: %w", err)
	}

	// Handle Tags
	if len(req.TagIDs) > 0 {
		for _, tagID := range req.TagIDs {
			err = qtx.AddTagToEvent(ctx, db.AddTagToEventParams{
				EventID:    createdEvent.ID,
				EventTagID: tagID,
			})
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("eventID", createdEvent.ID.String()).Str("tagID", tagID.String()).Msg("Failed to add tag to event")
				// Decide if this is a critical error that should cause rollback
			}
		}
	}

	// Handle RequiredVerificationTypeKeys
	if len(req.RequiredVerificationTypeKeys) > 0 {
		for _, key := range req.RequiredVerificationTypeKeys {
			err = qtx.AddEventRequiredVerificationType(ctx, db.AddEventRequiredVerificationTypeParams{
				EventID:             createdEvent.ID,
				VerificationTypeKey: key,
			})
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("eventID", createdEvent.ID.String()).Str("verificationKey", key).Msg("Failed to add required verification type to event")
				// Potentially return error or log and continue
			}
		}
	}

	// Handle MediaEventItem (Banner Images) - Assuming these are URLs for now
	// This part needs to be adapted if direct file uploads are part of CreateEventRequest
	// For now, it's implied they are handled by a separate media upload endpoint
	// and these are just URLs to existing media or external media.
	// If MediaEventItem contains URLs to be saved as event_media_items:
	for i, mediaURL := range req.MediaEventItem { // Assuming MediaEventItem is []string of URLs
		// This is a placeholder. In a real scenario, you'd likely:
		// 1. Download the image if it's external and not yet in your system.
		// 2. Save it to your storage (e.g., S3, local disk).
		// 3. Create an EventMediaItem record with the file path and other metadata.
		// For this example, we'll assume it's just a file name or path.
		_, err = qtx.CreateEventMediaItem(ctx, db.CreateEventMediaItemParams{
			EventID:  createdEvent.ID,
			FileName: fmt.Sprintf("banner_%d.jpg", i+1), // Placeholder
			FilePath: mediaURL,                          // Assuming URL is the path for now
			FileType: "image/jpeg",                      // Placeholder, determine from URL or content
			FileSize: 0,                                 // Placeholder, determine from downloaded file
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("eventID", createdEvent.ID.String()).Str("mediaURL", mediaURL).Msg("Failed to create event media item for banner")
			// Consider error handling strategy
		}
	}

	// Commit the transaction
	if err := tx.Commit(ctx); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to commit transaction for creating event")
		return payloads.EventResponse{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Schedule reminders if the event is published and start time is in the future
	if createdEvent.Status == db.EventStatusTypePublished && createdEvent.StartTime.After(time.Now()) {
		s.scheduleEventReminders(ctx, createdEvent.ID, createdEvent.StartTime)
	}

	// Return the full response, potentially fetching associated data like org name, tags again
	// For simplicity, we use a helper that might re-fetch or use available data.
	// The `createdEvent` itself might not have all fields for EventResponse (like OrganizationName).
	return s.getEventResponseFromDBEvent(ctx, s.queries, createdEvent, userID, true) // isAdminView true as creator is admin
}

// scheduleEventReminders enqueues jobs to send reminders for an event.
func (s *eventService) scheduleEventReminders(ctx context.Context, eventID uuid.UUID, eventStartTime time.Time) {
	reminderTimes := []time.Duration{24 * time.Hour, 1 * time.Hour} // Example: 24h and 1h before

	for _, rt := range reminderTimes {
		runAt := eventStartTime.Add(-rt)
		if runAt.After(time.Now()) {
			delay := time.Until(runAt) // Corrected: Use time.Until
			// delay := runAt.Sub(time.Now()) // Original line with S1024 warning
			jobPayload := map[string]interface{}{
				"event_id": eventID,
			}
			_, err := s.jobService.EnqueueJob(ctx, JobTypeEventReminder, jobPayload, delay, nil)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Dur("reminderLeadTime", rt).Msg("Failed to enqueue event reminder job")
			} else {
				log.Ctx(ctx).Info().Dur("reminderLeadTime", rt).Time("reminderTime", runAt).Msg("Successfully enqueued event reminder job")
			}
		} else {
			log.Ctx(ctx).Info().Dur("reminderLeadTime", rt).Time("eventStartTime", eventStartTime).Msg("Reminder time is in the past, not scheduling job.")
		}
	}
}

// GetEventByID implements EventService - updated signature
func (s *eventService) GetEventByID(ctx context.Context, eventID uuid.UUID, currentUserID uuid.UUID, isAdminView bool) (payloads.EventResponse, error) {
	// Use GetEventWithOrganizationByID if OrganizationName is needed and not joining separately
	dbEvent, err := s.queries.GetEventWithOrganizationByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.EventResponse{}, ErrEventNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to get event by ID")
		return payloads.EventResponse{}, fmt.Errorf("failed to retrieve event: %w", err)
	}

	// Manually construct the db.Event part from the GetEventWithOrganizationByIDRow if needed for getEventResponseFromDBEvent
	// Or modify getEventResponseFromDBEvent to accept GetEventWithOrganizationByIDRow directly (requires interface or careful type handling)
	// Simplified approach: Assume dbEvent can be constructed or adapted.
	// Here we assume GetEventWithOrganizationByIDRow has all fields of Event plus OrganizationName
	eventData := db.Event{ // Reconstruct db.Event from the joined row
		ID:                              dbEvent.ID,
		OrganizationID:                  dbEvent.OrganizationID,
		Title:                           dbEvent.Title,
		DescriptionContent:              dbEvent.DescriptionContent, // Corrected field
		LocationType:                    dbEvent.LocationType,
		LocationFullAddress:             dbEvent.LocationFullAddress,
		LocationOnlineUrl:               dbEvent.LocationOnlineUrl,
		StartTime:                       dbEvent.StartTime,
		EndTime:                         dbEvent.EndTime,
		Status:                          dbEvent.Status,
		ParticipantLimit:                dbEvent.ParticipantLimit,
		WaitlistLimit:                   dbEvent.WaitlistLimit,
		RequiresApprovalForRegistration: dbEvent.RequiresApprovalForRegistration,
		CreatedByUserID:                 dbEvent.CreatedByUserID,
		PublishedAt:                     dbEvent.PublishedAt,
		CreatedAt:                       dbEvent.CreatedAt,
		UpdatedAt:                       dbEvent.UpdatedAt,
	}

	// Call the helper using the reconstructed db.Event
	response, err := s.getEventResponseFromDBEvent(ctx, s.queries, eventData, currentUserID, isAdminView)
	if err != nil {
		return payloads.EventResponse{}, err // Error already logged in helper
	}
	// Ensure OrganizationName from the joined query is used
	response.OrganizationName = dbEvent.OrganizationName

	return response, nil
}

// UpdateEventDetails implements EventService.
func (s *eventService) UpdateEventDetails(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID, params payloads.UpdateEventRequest) (resp payloads.EventResponse, err error) {
	var updatedEventDB db.Event
	var slotsToPromote int = 0

	// Wrap the core logic in a transaction
	tx, err := s.pool.Begin(ctx)
	if err != nil {
		return payloads.EventResponse{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	// Use defer with explicit error check for rollback
	defer func() {
		if err != nil {
			// Attempt rollback, log error if rollback fails
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				log.Ctx(ctx).Error().Err(rbErr).Msg("Failed to rollback transaction after error in UpdateEventDetails")
			}
		}
	}()

	qtx := s.queries.WithTx(tx)

	// Fetch the existing event to compare and selectively update
	existingEvent, err := qtx.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.EventResponse{}, ErrEventNotFound
		}
		err = fmt.Errorf("failed to fetch event for update: %w", err) // Assign error for defer
		return payloads.EventResponse{}, err
	}

	// Store the old participant limit for later comparison
	oldParticipantLimit := existingEvent.ParticipantLimit

	// TODO: Add authorization check: ensure userID is allowed to update this event in this orgID

	updateParams := db.UpdateEventDetailsParams{
		ID: eventID,
		// Title, DescriptionContent, etc. will be set using COALESCE(sqlc.narg(field), existing_field)
		// So, only pass pointers to fields that are actually being updated from `params`.
	}

	// Apply updates from the request payload (params)
	if params.Title != nil {
		updateParams.Title = params.Title
	}
	if len(params.JsonContent) > 0 && string(params.JsonContent) != "null" {
		updateParams.DescriptionContent = params.JsonContent
	}

	if params.LocationType != nil {
		updateParams.LocationType = db.NullEventLocationType{EventLocationType: db.EventLocationType(*params.LocationType), Valid: true}
	}
	if params.LocationFullAddress != nil {
		updateParams.LocationFullAddress = params.LocationFullAddress
	}
	if params.LocationOnlineURL != nil {
		updateParams.LocationOnlineUrl = params.LocationOnlineURL
	}
	if params.StartTime != nil {
		updateParams.StartTime = params.StartTime
	}
	if params.EndTime != nil {
		updateParams.EndTime = params.EndTime
	}
	if params.Price != nil {
		updateParams.Price = params.Price
	}
	if params.ContactEmail != nil {
		updateParams.ContactEmail = params.ContactEmail
	}
	if params.ContactPhone != nil {
		updateParams.ContactPhone = params.ContactPhone
	}
	if params.RequiresApprovalForRegistration != nil {
		updateParams.RequiresApprovalForRegistration = params.RequiresApprovalForRegistration
	}
	if params.ParticipantLimit != nil {
		updateParams.ParticipantLimit = params.ParticipantLimit
	}
	if params.WaitlistLimit != nil {
		updateParams.WaitlistLimit = params.WaitlistLimit
	}

	// Add GovernmentFundingKeys if provided in the request
	if params.GovernmentFundingKeys != nil {
		updateParams.GovernmentFundingKeys = *params.GovernmentFundingKeys // Dereference pointer
	}

	// Logic for Status and PublishedAt based on scheduling requirements
	finalStatus := existingEvent.Status           // db.EventStatusType
	finalPublishedAt := existingEvent.PublishedAt // *time.Time (pointer)
	now := time.Now()

	userInteractedWithPublishedAt := false

	if params.PublishedAt != nil {
		userInteractedWithPublishedAt = true
		finalPublishedAt = params.PublishedAt
		if params.PublishedAt.After(now) {
			if finalStatus != db.EventStatusTypeDraft {
				finalStatus = db.EventStatusTypeDraft
			}
		} else {
			if finalStatus != db.EventStatusTypePublished {
				finalStatus = db.EventStatusTypePublished
			}
		}
	}

	if params.Status != nil {
		requestedDBStatus := db.EventStatusType(*params.Status)
		finalStatus = requestedDBStatus

		if requestedDBStatus == db.EventStatusTypePublished {
			if finalPublishedAt == nil || (finalPublishedAt != nil && finalPublishedAt.After(now)) {
				finalPublishedAt = &now
				userInteractedWithPublishedAt = true
			}
		} else if requestedDBStatus == db.EventStatusTypeDraft {
			if finalPublishedAt != nil && !finalPublishedAt.After(now) {
				finalPublishedAt = nil
				userInteractedWithPublishedAt = true
			}
		} else {
			if finalPublishedAt != nil {
				finalPublishedAt = nil
				userInteractedWithPublishedAt = true
			}
		}
	}

	if params.PublishedAt == nil && params.Status == nil && existingEvent.Status == db.EventStatusTypeDraft && existingEvent.PublishedAt != nil {
		finalPublishedAt = nil
		userInteractedWithPublishedAt = true
	}

	if finalStatus != existingEvent.Status {
		updateParams.Status = db.NullEventStatusType{EventStatusType: finalStatus, Valid: true}
		log.Ctx(ctx).Info().Str("event_id", eventID.String()).Str("old_status", string(existingEvent.Status)).Str("new_status", string(finalStatus)).Msg("Event status changed.")
	} else if params.Status != nil {
		updateParams.Status = db.NullEventStatusType{EventStatusType: db.EventStatusType(*params.Status), Valid: true}
	}

	publishedAtActuallyChanged := (finalPublishedAt == nil && existingEvent.PublishedAt != nil) ||
		(finalPublishedAt != nil && existingEvent.PublishedAt == nil) ||
		(finalPublishedAt != nil && existingEvent.PublishedAt != nil && !finalPublishedAt.Equal(*existingEvent.PublishedAt))

	if publishedAtActuallyChanged {
		updateParams.PublishedAt = finalPublishedAt
		if finalPublishedAt != nil {
			log.Ctx(ctx).Info().Str("event_id", eventID.String()).Time("new_published_at", *finalPublishedAt).Msg("Event published_at changed.")
		} else {
			log.Ctx(ctx).Info().Str("event_id", eventID.String()).Msg("Event published_at cleared (set to NULL via change).")
		}
	} else if userInteractedWithPublishedAt {
		updateParams.PublishedAt = finalPublishedAt
		if finalPublishedAt == nil {
			log.Ctx(ctx).Info().Str("event_id", eventID.String()).Msg("Event published_at explicitly set to NULL (was already NULL or no change).")
		}
	}

	// Perform the main event details update
	updatedEventDB, err = qtx.UpdateEventDetails(ctx, updateParams)
	if err != nil {
		err = fmt.Errorf("failed to update event details in DB: %w", err)
		return payloads.EventResponse{}, err
	}

	// --- Media Items Handling (after main event update, before commit) ---
	if params.MediaEventItem != nil { // Check if the client sent this field (it's a *[]string)
		log.Ctx(ctx).Info().Str("event_id", eventID.String()).Msg("Processing MediaEventItem update.")

		// 1. Delete all existing media items for this event (within the transaction)
		if delErr := qtx.DeleteEventMediaItemsByEventID(ctx, eventID); delErr != nil {
			err = fmt.Errorf("failed to delete existing event media items: %w", delErr)
			log.Ctx(ctx).Error().Err(err).Str("event_id", eventID.String()).Msg("Rollback will be triggered.")
			return payloads.EventResponse{}, err
		}
		log.Ctx(ctx).Debug().Str("event_id", eventID.String()).Msg("Successfully deleted existing media items.")

		// 2. Add new media items from the payload (within the transaction)
		if len(*params.MediaEventItem) > 0 {
			for _, itemURL := range *params.MediaEventItem {
				if itemURL == "" {
					log.Ctx(ctx).Warn().Str("event_id", eventID.String()).Msg("Empty URL found in MediaEventItem, skipping.")
					continue
				}

				// Derive FileName from URL
				parsedURL, parseErr := url.Parse(itemURL)
				var fileName string
				if parseErr == nil {
					fileName = filepath.Base(parsedURL.Path)
					if fileName == "." || fileName == "/" { // Sanitize if base is just . or /
						fileName = "untitled_media_from_path" // Or derive from host + path more robustly
					}
				} else {
					fileName = "untitled_media" // Fallback if URL parsing fails
					log.Ctx(ctx).Warn().Err(parseErr).Str("url", itemURL).Msg("Could not parse URL to extract filename, using default.")
				}

				mediaParams := db.CreateEventMediaItemParams{
					EventID:  eventID,
					FilePath: itemURL,
					FileName: fileName,
					FileType: "image/link", // Placeholder/Default as per plan
					FileSize: 0,            // Placeholder/Default as per plan
				}
				if _, mediaErr := qtx.CreateEventMediaItem(ctx, mediaParams); mediaErr != nil {
					err = fmt.Errorf("failed to create event media item for URL %s: %w", itemURL, mediaErr)
					log.Ctx(ctx).Error().Err(err).Str("event_id", eventID.String()).Msg("Rollback will be triggered.")
					return payloads.EventResponse{}, err
				}
			}
			log.Ctx(ctx).Debug().Str("event_id", eventID.String()).Int("count", len(*params.MediaEventItem)).Msg("Successfully added new media items.")
		} else {
			log.Ctx(ctx).Debug().Str("event_id", eventID.String()).Msg("MediaEventItem was an empty list, all existing items deleted and no new items added.")
		}
	}
	// --- End Media Items Handling ---

	// --- Waitlist Promotion Logic Calculation (still within the transaction scope) ---
	newParticipantLimit := updatedEventDB.ParticipantLimit

	// Check if participant limit was increased
	if newParticipantLimit != nil { // Only proceed if there IS a new limit
		oldLimitValue := int32(0) // Assume 0 if there was no old limit
		if oldParticipantLimit != nil {
			oldLimitValue = *oldParticipantLimit
		}

		if *newParticipantLimit > oldLimitValue {
			slotsOpened := *newParticipantLimit - oldLimitValue

			// Get current registered count (using the transaction)
			currentRegisteredCount, countErr := qtx.GetRegisteredCountForEvent(ctx, eventID)
			if countErr != nil {
				// Don't fail the transaction, but log that promotion check couldn't happen accurately
				// log.Error().Err(countErr).Msgf("Failed to get registered count for event %s, skipping waitlist promotion check within transaction", eventID)
			} else {
				// Calculate available slots considering the *new* limit
				availableSlotsToFill := *newParticipantLimit - int32(currentRegisteredCount)
				if availableSlotsToFill > 0 {
					// Determine slots to promote
					if slotsOpened < availableSlotsToFill {
						slotsToPromote = int(slotsOpened)
					} else {
						slotsToPromote = int(availableSlotsToFill)
					}
				}
			}
		}
	}

	// Commit the main event update transaction *before* launching the background promotion task
	if err = tx.Commit(ctx); err != nil {
		// If commit fails, assign error. The defer func will handle rollback.
		err = fmt.Errorf("failed to commit event update transaction: %w", err)
		return payloads.EventResponse{}, err
	}

	// Trigger promotion asynchronously if needed, *after* the main transaction is committed
	if slotsToPromote > 0 {
		go func() {
			// TODO: CRITICAL - Replace with a robust background job system (e.g., Asynq, Machinery).
			// Current goroutine approach lacks retries, durability, and observability.
			// A job queue would allow for:
			//   - Persistent tasks that survive server restarts.
			//   - Automatic retries with backoff for transient errors.
			//   - Better monitoring and error tracking.
			//   - Controlled concurrency for tasks like this.
			// JOB PAYLOAD: { event_id: eventID, slots_to_fill: slotsToPromote }
			fmt.Printf("INFO: Event %s participant limit increased, attempting to promote %d from waitlist\n", eventID.String(), slotsToPromote)
			promoted, promoErr := s.PromoteFromWaitlist(context.Background(), eventID, slotsToPromote)
			if promoErr != nil {
				fmt.Printf("ERROR: Background waitlist promotion failed after limit increase for event %s: %v\n", eventID.String(), promoErr)
			} else if len(promoted) > 0 {
				fmt.Printf("INFO: Background waitlist promotion successful after limit increase for event %s, promoted: %d\n", eventID.String(), len(promoted))
			}
		}()
	}

	// Return the successfully updated event (commit was successful)
	// Fetch the full response details
	response, err := s.getEventResponseFromDBEvent(ctx, s.queries, updatedEventDB, userID, true) // isAdminView = true as this is an update op
	if err != nil {
		return payloads.EventResponse{}, err // Error already logged in helper
	}

	// If StartTime was changed, schedule new reminders
	// (TODO: In a future iteration, cancel old reminders for the old StartTime)
	if params.StartTime != nil && !params.StartTime.Equal(existingEvent.StartTime) {
		log.Ctx(ctx).Info().Str("eventID", eventID.String()).Time("newStartTime", *params.StartTime).Msg("Event start time changed, scheduling new reminders.")
		s.scheduleEventReminders(ctx, updatedEventDB.ID, *params.StartTime)
	}

	return response, nil
}

// DeleteEvent implements EventService.
func (s *eventService) DeleteEvent(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error {
	// First, get the existing event to verify ownership and existence
	existingEvent, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("event not found") // Or return nil if idempotent deletion is desired and event not found is acceptable
		}
		return fmt.Errorf("failed to retrieve event for deletion: %w", err)
	}

	// Basic permission check: ensure the event belongs to the organization
	if existingEvent.OrganizationID != orgID {
		return errors.New("event does not belong to the specified organization or permission denied")
	}

	// TODO: Add more granular permission check based on userID's role within the orgID
	// (e.g., only org admin/event creator can delete)

	// Note: ON DELETE CASCADE for related tables (event_banner_gallery_items, event_event_tags, etc.)
	// should handle deletion of associated data in the database.
	err = s.queries.DeleteEvent(ctx, eventID)
	if err != nil {
		// s.logger.Error("failed to delete event", "error", err, "eventID", eventID)
		return fmt.Errorf("failed to delete event from DB: %w", err)
	}

	return nil
}

// AddEventMediaItem implements EventService.
// It now takes a multipart.FileHeader to handle the file upload directly.
func (s *eventService) AddEventMediaItem(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID, fileHeader *multipart.FileHeader, isBanner bool) (payloads.MediaItemResponse, error) {
	// Authorization: Check if user has permission to add media to this event (e.g., org admin or event creator)
	// This typically involves checking user's role in the organization.
	// For simplicity, assuming a helper function like s.queries.IsUserAdminInOrg(ctx, userID, orgID) exists or similar logic.
	// If not authorized, return an error (e.g., ErrForbidden)

	// Validate event existence
	event, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.MediaItemResponse{}, ErrEventNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to get event for media upload")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to get event: %w", err)
	}
	if event.OrganizationID != orgID {
		// Event does not belong to the organization specified in the path
		return payloads.MediaItemResponse{}, ErrEventNotFound // Or a more specific error like ErrMismatchOrgEvent
	}

	// TODO: Add more robust permission check (e.g., based on user roles within the organization)
	// For example, check if userID is an admin of orgID or creator of eventID.
	// if event.CreatedByUserID != userID { // This is a simplistic check, admins should also be able to.
	// 	isOrgAdmin, _ := s.queries.IsUserAdminInOrg(ctx, db.IsUserAdminInOrgParams{UserID: userID, OrganizationID: orgID})
	// 	if !isOrgAdmin {
	// 		return payloads.MediaItemResponse{}, fmt.Errorf("user does not have permission to add media to this event")
	// 	}
	// }

	src, err := fileHeader.Open()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to open uploaded file")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to open file: %w", err)
	}
	defer src.Close()

	// Create a unique filename to prevent collisions
	uniqueFileName := fmt.Sprintf("%s-%s", uuid.New().String(), filepath.Clean(fileHeader.Filename))
	// Define the storage path. Ensure this directory exists and is writable.
	// Example: "uploads/event-media/{orgID}/{eventID}/{uniqueFileName}"
	uploadDir := filepath.Join("uploads", "event-media", orgID.String(), eventID.String())
	if err := os.MkdirAll(uploadDir, os.ModePerm); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("uploadDir", uploadDir).Msg("Failed to create upload directory")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to create upload directory: %w", err)
	}
	filePath := filepath.Join(uploadDir, uniqueFileName)

	dst, err := os.Create(filePath)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("filePath", filePath).Msg("Failed to create file on server")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to create file: %w", err)
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to copy uploaded file to destination")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to save file: %w", err)
	}

	// Store file metadata in the database
	// The FilePath stored should be the web-accessible path, not the local filesystem path.
	// This might involve constructing a URL based on your server's configuration.
	// For example: "/static/uploads/event-media/{orgID}/{eventID}/{uniqueFileName}"
	// or "https://your-cdn.com/uploads/event-media/{orgID}/{eventID}/{uniqueFileName}"
	webAccessibleFilePath := "/" + strings.ReplaceAll(filePath, "\\", "/") // Basic example, adjust for your serving setup

	tx, err := s.pool.Begin(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to begin transaction for adding event media")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx) // Rollback if not committed

	qtx := s.queries.WithTx(tx)

	if isBanner {
		// Corrected: UnsetBannerForEventMediaItems returns only error
		if err := qtx.UnsetBannerForEventMediaItems(ctx, eventID); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to unset existing banners")
			return payloads.MediaItemResponse{}, fmt.Errorf("failed to unset existing banners: %w", err)
		}
	}

	dbMediaItem, err := qtx.CreateEventMediaItem(ctx, db.CreateEventMediaItemParams{
		EventID:  eventID,
		FileName: fileHeader.Filename,
		FilePath: webAccessibleFilePath, // Store the web-accessible path
		FileType: fileHeader.Header.Get("Content-Type"),
		FileSize: fileHeader.Size,
		IsBanner: isBanner, // Pass the isBanner flag
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create event media item in DB")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to save media item metadata: %w", err)
	}

	if err := tx.Commit(ctx); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to commit transaction for adding event media")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return payloads.MediaItemResponse{
		ID:         dbMediaItem.ID,
		FileName:   dbMediaItem.FileName,
		FilePath:   dbMediaItem.FilePath,
		FileType:   dbMediaItem.FileType,
		FileSize:   dbMediaItem.FileSize,
		UploadedAt: dbMediaItem.UploadedAt,
		IsBanner:   dbMediaItem.IsBanner,
	}, nil
}

// ListEventMediaItems implements EventService.
func (s *eventService) ListEventMediaItems(ctx context.Context, eventID uuid.UUID) ([]db.EventMediaItem, error) {
	items, err := s.queries.ListEventMediaItems(ctx, eventID)
	if err != nil {
		// s.logger.Error("failed to list event media items", "error", err, "eventID", eventID)
		// Unlike Get, a List operation returning no rows is often not an error but an empty list.
		// pgx.ErrNoRows check might not be needed here unless the query itself would error for no parent event.
		// Assuming the query handles non-existent eventID gracefully (e.g. returns empty slice without error) or GetEventByID was called before.
		return nil, fmt.Errorf("failed to list event media items from DB: %w", err)
	}
	// If no items are found, sqlc typically returns an empty slice and nil error, which is desired.
	return items, nil
}

// DeleteEventMediaItem implements EventService.
func (s *eventService) DeleteEventMediaItem(ctx context.Context, itemID uuid.UUID, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error {
	// 1. Get the media item to verify its existence and that it belongs to the specified eventID
	item, err := s.queries.GetEventMediaItemByID(ctx, itemID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("media item not found")
		}
		return fmt.Errorf("failed to retrieve media item: %w", err)
	}

	if item.EventID != eventID {
		return errors.New("media item does not belong to the specified event")
	}

	// 2. Get the event to verify ownership by orgID
	existingEvent, err := s.queries.GetEventByID(ctx, eventID) // eventID is from item.EventID, confirmed to match param
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// This should ideally not happen if the item existed and had a valid event_id
			return errors.New("event not found for the media item")
		}
		return fmt.Errorf("failed to retrieve event for media item deletion: %w", err)
	}

	if existingEvent.OrganizationID != orgID {
		return errors.New("event does not belong to the specified organization or permission denied")
	}

	// TODO: Add more granular permission check based on userID's role within the orgID
	// (e.g., only org admin/event creator can delete media items)

	// 3. Delete the media item
	err = s.queries.DeleteEventMediaItem(ctx, itemID)
	if err != nil {
		// s.logger.Error("failed to delete event media item", "error", err, "itemID", itemID)
		return fmt.Errorf("failed to delete event media item from DB: %w", err)
	}

	return nil
}

// CreateEventTag creates a new event tag.
func (s *eventService) CreateEventTag(ctx context.Context, userID uuid.UUID, params payloads.CreateEventTagRequest) (payloads.TagResponse, error) {
	// TODO: Add authorization logic here if needed (e.g., only admins can create global tags)

	dbParams := db.CreateEventTagParams{
		NameEn:             params.NameEn,
		NameZhHk:           params.NameZhHk,
		NameZhCn:           params.NameZhCn,
		IsGloballyApproved: params.IsGloballyApproved,
		CreatedByUserID:    &userID,
	}
	tag, err := s.queries.CreateEventTag(ctx, dbParams)
	if err != nil {
		return payloads.TagResponse{}, err
	}
	return toEventTagResponse(tag), nil
}

// UpdateEventTag updates an existing event tag.
func (s *eventService) UpdateEventTag(ctx context.Context, tagID uuid.UUID, params payloads.UpdateEventTagRequest) (payloads.TagResponse, error) {
	// For now, only superadmin can update.
	// A more complex logic could check if user is admin of the org that created the tag.
	// This requires getting the user's roles/permissions first.

	// 1. Get existing tag to ensure it exists before updating.
	_, err := s.queries.GetEventTag(ctx, tagID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.TagResponse{}, ErrEventTagNotFound
		}
		return payloads.TagResponse{}, err
	}

	// 2. Prepare update parameters. The sqlc.narg parameters are pointers.
	// We pass the pointers from the payload directly. If a field is nil,
	// COALESCE in the SQL query will keep the existing value.
	updateParams := db.UpdateEventTagParams{
		ID:                 tagID,
		NameEn:             params.NameEn,
		NameZhHk:           params.NameZhHk,
		NameZhCn:           params.NameZhCn,
		IsGloballyApproved: params.IsGloballyApproved,
	}

	// 3. Execute update
	updatedTag, err := s.queries.UpdateEventTag(ctx, updateParams)
	if err != nil {
		return payloads.TagResponse{}, err
	}

	return toEventTagResponse(updatedTag), nil
}

// ListEventTags lists all event tags, optionally filtering by approval status.
func (s *eventService) ListEventTags(ctx context.Context, approved *bool, pageParams payloads.PageRequest) ([]payloads.TagResponse, error) {
	// TODO: Implement pagination and filtering based on 'approved' status in the DB query
	dbTags, err := s.queries.ListEventTags(ctx)
	if err != nil {
		return nil, err
	}

	tags := make([]payloads.TagResponse, len(dbTags))
	for i, t := range dbTags {
		tags[i] = toEventTagResponse(t)
	}
	return tags, nil
}

// AddTagToEvent adds a tag to an event.
func (s *eventService) AddTagToEvent(ctx context.Context, eventID uuid.UUID, tagID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error {
	// 1. Authorize: check if user has rights to edit the event in the org
	existingEvent, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("event not found")
		}
		return fmt.Errorf("failed to retrieve event: %w", err)
	}

	if existingEvent.OrganizationID != orgID {
		return errors.New("event does not belong to the specified organization or permission denied")
	}

	// TODO: Add more granular permission check based on userID's role within the orgID

	// 2. Get the tag to verify it exists (and potentially check if approved)
	_, err = s.queries.GetEventTag(ctx, tagID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("event tag not found")
		}
		return fmt.Errorf("failed to retrieve event tag: %w", err)
	}
	// We might want to check tag.IsGloballyApproved here in the future

	// 3. Add the tag to the event
	dbParams := db.AddTagToEventParams{
		EventID:    eventID,
		EventTagID: tagID,
	}
	err = s.queries.AddTagToEvent(ctx, dbParams)
	if err != nil {
		// Since the query uses ON CONFLICT DO NOTHING, an error here is likely
		// a DB connection issue or a more fundamental problem, not just a duplicate.
		// s.logger.Error("failed to add tag to event", "error", err, "params", dbParams)
		return fmt.Errorf("failed to add tag to event in DB: %w", err)
	}

	return nil
}

// RemoveTagFromEvent removes a tag from an event.
func (s *eventService) RemoveTagFromEvent(ctx context.Context, eventID uuid.UUID, tagID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error {
	// 1. Get the event to verify ownership by orgID
	existingEvent, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("event not found")
		}
		return fmt.Errorf("failed to retrieve event: %w", err)
	}

	if existingEvent.OrganizationID != orgID {
		return errors.New("event does not belong to the specified organization or permission denied")
	}

	// TODO: Add more granular permission check based on userID's role within the orgID

	// 2. (Optional) Verify the tag exists - ensures tagID is valid
	_, err = s.queries.GetEventTag(ctx, tagID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// If the tag doesn't exist, we can arguably still proceed as the link won't exist either.
			// For robustness, we could return an error.
			return errors.New("event tag not found")
		}
		return fmt.Errorf("failed to retrieve event tag: %w", err)
	}

	// 3. Remove the tag from the event
	dbParams := db.RemoveTagFromEventParams{
		EventID:    eventID,
		EventTagID: tagID,
	}
	err = s.queries.RemoveTagFromEvent(ctx, dbParams)
	if err != nil {
		// An error here likely indicates a DB connection issue.
		// The query itself might not return an error if the specific link didn't exist.
		// s.logger.Error("failed to remove tag from event", "error", err, "params", dbParams)
		return fmt.Errorf("failed to remove tag from event in DB: %w", err)
	}

	return nil
}

// ListTagsForEvent lists all tags associated with a specific event.
func (s *eventService) ListTagsForEvent(ctx context.Context, eventID uuid.UUID) ([]payloads.TagResponse, error) {
	dbTags, err := s.queries.ListTagsForEvent(ctx, eventID)
	if err != nil {
		return nil, fmt.Errorf("failed to list tags for event: %w", err)
	}
	tags := make([]payloads.TagResponse, len(dbTags))
	for i, t := range dbTags {
		tags[i] = toEventTagResponse(t)
	}
	return tags, nil
}

// toEventTagResponse is a helper function to convert a db.EventTag to a payloads.TagResponse.
func toEventTagResponse(t db.EventTag) payloads.TagResponse {
	return payloads.TagResponse{
		ID:                 t.ID,
		NameEn:             t.NameEn,
		NameZhHk:           t.NameZhHk,
		NameZhCn:           t.NameZhCn,
		DescriptionEn:      t.DescriptionEn,
		DescriptionZhHk:    t.DescriptionZhHk,
		DescriptionZhCn:    t.DescriptionZhCn,
		IsGloballyApproved: t.IsGloballyApproved,
	}
}

// AddEventRequiredVerificationType implements EventService.
func (s *eventService) AddEventRequiredVerificationType(ctx context.Context, eventID uuid.UUID, verificationTypeKey string, orgID uuid.UUID, userID uuid.UUID) error {
	// 1. Get the event to verify ownership by orgID
	existingEvent, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("event not found")
		}
		return fmt.Errorf("failed to retrieve event: %w", err)
	}

	if existingEvent.OrganizationID != orgID {
		return errors.New("event does not belong to the specified organization or permission denied")
	}

	// TODO: Add more granular permission check based on userID's role within the orgID

	// TODO: Add validation for verificationTypeKey - check against allowed/known keys?
	if verificationTypeKey == "" {
		return errors.New("verification type key cannot be empty")
	}

	// 3. Add the verification type to the event
	dbParams := db.AddEventRequiredVerificationTypeParams{
		EventID:             eventID,
		VerificationTypeKey: verificationTypeKey,
	}
	err = s.queries.AddEventRequiredVerificationType(ctx, dbParams)
	if err != nil {
		// Since the query uses ON CONFLICT DO NOTHING, an error here is likely
		// a DB connection issue or a more fundamental problem, not just a duplicate.
		// s.logger.Error("failed to add required verification type to event", "error", err, "params", dbParams)
		return fmt.Errorf("failed to add required verification type to event in DB: %w", err)
	}

	return nil
}

// RemoveEventRequiredVerificationType implements EventService.
func (s *eventService) RemoveEventRequiredVerificationType(ctx context.Context, eventID uuid.UUID, verificationTypeKey string, orgID uuid.UUID, userID uuid.UUID) error {
	// 1. Get the event to verify ownership by orgID
	existingEvent, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("event not found")
		}
		return fmt.Errorf("failed to retrieve event: %w", err)
	}

	if existingEvent.OrganizationID != orgID {
		return errors.New("event does not belong to the specified organization or permission denied")
	}

	// TODO: Add more granular permission check based on userID's role within the orgID

	// 2. Remove the verification type from the event
	dbParams := db.RemoveEventRequiredVerificationTypeParams{
		EventID:             eventID,
		VerificationTypeKey: verificationTypeKey,
	}
	err = s.queries.RemoveEventRequiredVerificationType(ctx, dbParams)
	if err != nil {
		// An error here likely indicates a DB connection issue.
		// s.logger.Error("failed to remove required verification type from event", "error", err, "params", dbParams)
		return fmt.Errorf("failed to remove required verification type from event in DB: %w", err)
	}

	return nil
}

// ListRequiredVerificationTypesForEvent implements EventService.
func (s *eventService) ListRequiredVerificationTypesForEvent(ctx context.Context, eventID uuid.UUID) ([]string, error) {
	keys, err := s.queries.ListRequiredVerificationTypesForEvent(ctx, eventID)
	if err != nil {
		// s.logger.Error("failed to list required verification types for event", "error", err, "eventID", eventID)
		// Assuming query handles non-existent eventID gracefully (returns empty slice).
		return nil, fmt.Errorf("failed to list required verification types for event from DB: %w", err)
	}
	// Return empty slice if none are found
	return keys, nil
}

// GetUserEventRegistration implements EventService.
func (s *eventService) GetUserEventRegistration(ctx context.Context, userID uuid.UUID, eventID uuid.UUID) (payloads.EventRegistrationResponse, error) {
	registrationRow, err := s.queries.GetEventRegistrationByUserAndEvent(ctx, db.GetEventRegistrationByUserAndEventParams{
		UserID:  userID,
		EventID: eventID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.EventRegistrationResponse{}, errors.New("event registration not found for this user and event")
		}
		// s.logger.Error("failed to get event registration by user and event", "error", err, "userID", userID, "eventID", eventID)
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to retrieve event registration: %w", err)
	}

	user, userErr := s.queries.GetUserByID(ctx, userID)     // userID is registrationRow.UserID
	event, eventErr := s.queries.GetEventByID(ctx, eventID) // eventID is registrationRow.EventID

	dbReg := db.EventRegistration{
		ID:                       registrationRow.ID,
		EventID:                  registrationRow.EventID,
		UserID:                   registrationRow.UserID,
		Status:                   registrationRow.Status,
		PaymentStatus:            registrationRow.PaymentStatus,
		RegistrationRole:         registrationRow.RegistrationRole,
		RegisteredAt:             registrationRow.RegisteredAt,
		AttendedAt:               registrationRow.AttendedAt,
		CancellationReasonByUser: registrationRow.CancellationReasonByUser,
		AdminNotesOnRegistration: registrationRow.AdminNotesOnRegistration,
		WaitlistPriority:         registrationRow.WaitlistPriority,
		CreatedAt:                registrationRow.CreatedAt,
		UpdatedAt:                registrationRow.UpdatedAt,
	}

	response := payloads.EventRegistrationResponse{
		EventRegistration: dbReg,
	}

	if userErr == nil {
		response.UserDisplayName = user.DisplayName
		response.UserEmail = user.Email
		response.UserPhone = user.Phone
	} else {
		// s.logger.Warn("failed to fetch user details for GetUserEventRegistration response", "error", userErr, "userID", userID)
		response.UserDisplayName = "Error fetching name"
	}

	if eventErr == nil {
		response.EventTitle = event.Title
		response.EventStartTime = event.StartTime
	} else {
		// s.logger.Warn("failed to fetch event details for GetUserEventRegistration response", "error", eventErr, "eventID", eventID)
		response.EventTitle = "Error fetching event title"
	}

	return response, nil
}

// ListUserRegistrations implements EventService.
func (s *eventService) ListUserRegistrations(ctx context.Context, userID uuid.UUID, params payloads.ListUserRegistrationsRequest) ([]payloads.EventRegistrationResponse, int64, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Logger()

	limit := int32(DefaultPageSize)
	if params.Limit > 0 {
		limit = int32(params.Limit)
	}
	offset := int32(params.Offset)

	// Prepare params for count query
	countDbParams := db.CountUserRegistrationsWithFiltersParams{
		UserID:            userID,
		StartDate:         params.StartDate,
		EndDate:           params.EndDate,
		EventFilterStatus: db.NullEventStatusType{Valid: false},           // Initialize as NULL
		Role:              db.NullEventRegistrationRoleType{Valid: false}, // Initialize as NULL
		OrganizationID:    params.OrganizationID,
	}
	if params.Status != nil {
		statusEnum := db.EventStatusType(*params.Status)
		if statusEnum.Valid() {
			countDbParams.EventFilterStatus = db.NullEventStatusType{EventStatusType: statusEnum, Valid: true}
		} else {
			logger.Warn().Str("status_filter", *params.Status).Msg("Invalid event status filter value provided")
		}
	}
	if params.Role != nil {
		roleEnum := db.EventRegistrationRoleType(*params.Role)
		if roleEnum.Valid() {
			countDbParams.Role = db.NullEventRegistrationRoleType{EventRegistrationRoleType: roleEnum, Valid: true}
		} else {
			logger.Warn().Str("role_filter", *params.Role).Msg("Invalid role filter value provided")
		}
	}

	totalCount, err := s.queries.CountUserRegistrationsWithFilters(ctx, countDbParams)
	if err != nil {
		logger.Error().Err(err).Msg("failed to count user registrations with filters")
		return nil, 0, fmt.Errorf("failed to count user registrations: %w", err)
	}

	if totalCount == 0 {
		return []payloads.EventRegistrationResponse{}, 0, nil
	}

	// Prepare params for list query
	listDbParams := db.ListUserRegistrationsWithFiltersParams{
		UserID:            userID,
		StartDate:         params.StartDate,
		EndDate:           params.EndDate,
		EventFilterStatus: countDbParams.EventFilterStatus, // Reuse from count params
		Role:              countDbParams.Role,              // Reuse from count params
		OrganizationID:    params.OrganizationID,
		SortOrder:         params.Sort,
		LimitVal:          limit,
		OffsetVal:         offset,
	}

	rows, err := s.queries.ListUserRegistrationsWithFilters(ctx, listDbParams)
	if err != nil {
		logger.Error().Err(err).Msg("failed to list user registrations with filters")
		return nil, 0, fmt.Errorf("failed to list user registrations: %w", err)
	}

	if len(rows) == 0 { // Should ideally not happen if totalCount > 0, but as a safeguard
		return []payloads.EventRegistrationResponse{}, totalCount, nil
	}

	user, userErr := s.queries.GetUserByID(ctx, userID) // Fetch user details once for all responses

	responses := make([]payloads.EventRegistrationResponse, 0, len(rows))
	for _, row := range rows {
		registration := db.EventRegistration{
			ID:                       row.ID,
			EventID:                  row.EventID,
			UserID:                   row.UserID,
			Status:                   row.Status,
			PaymentStatus:            row.PaymentStatus,
			RegistrationRole:         row.RegistrationRole,
			RegisteredAt:             row.RegisteredAt,
			AttendedAt:               row.AttendedAt,
			CancellationReasonByUser: row.CancellationReasonByUser,
			AdminNotesOnRegistration: row.AdminNotesOnRegistration,
			WaitlistPriority:         row.WaitlistPriority,
			CreatedAt:                row.CreatedAt,
			UpdatedAt:                row.UpdatedAt,
		}

		responseItem := payloads.EventRegistrationResponse{
			EventRegistration: registration,
			EventTitle:        row.EventTitle,
			EventStartTime:    row.EventStartTime,
			EventEndTime:      row.EventEndTime,
			// EventDescription:      &row.EventDescriptionContent, // If selected
			EventLocationType:        string(row.EventLocationType),
			EventLocationFullAddress: row.EventLocationFullAddress,
			EventLocationOnlineURL:   row.EventLocationOnlineUrl,
			EventStatus:              string(row.EventStatus), // Corrected: use row.EventStatus (from e.status AS event_status)
			EventOrganizationID:      &row.EventOrganizationID,
			// EventOrganizationName handled below
			EventPrice:        row.EventPrice,
			EventContactEmail: row.EventContactEmail,
			EventContactPhone: row.EventContactPhone,
		}

		// Handle EventOrganizationName (string to *string)
		if row.EventOrganizationName != "" {
			responseItem.EventOrganizationName = &row.EventOrganizationName
		}

		if userErr == nil {
			responseItem.UserDisplayName = user.DisplayName
			responseItem.UserEmail = user.Email
			responseItem.UserPhone = user.Phone
		} else {
			logger.Warn().Err(userErr).Str("userID_for_details", userID.String()).Msg("failed to fetch user details for ListUserRegistrations item")
			// Keep responseItem but with missing user details
		}
		responses = append(responses, responseItem)
	}

	return responses, totalCount, nil
}

// ListEventRegistrations implements EventService.
// Note: Added totalCount to return signature for consistency with other paginated list methods.
func (s *eventService) ListEventRegistrations(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID, pageParams payloads.PageRequest) ([]payloads.EventRegistrationResponse, int64, error) {
	// 1. Permission Check & Fetch Event details once
	event, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, 0, errors.New("event not found")
		}
		// s.logger.Error("failed to get event by ID for ListEventRegistrations", "error", err, "eventID", eventID)
		return nil, 0, fmt.Errorf("failed to retrieve event: %w", err)
	}

	if event.OrganizationID != orgID {
		return nil, 0, errors.New("permission denied: event does not belong to the specified organization")
	}

	// TODO: Add more granular permission check: does userID have rights to view registrations for this orgID/eventID?

	// 2. Determine limit and offset
	limit := int32(20) // Default limit
	if pageParams.Limit > 0 {
		limit = int32(pageParams.Limit)
	}
	offset := int32(pageParams.Offset)

	// 3. Call query
	rows, err := s.queries.ListRegistrationsForEvent(ctx, db.ListRegistrationsForEventParams{
		EventID: eventID,
		Limit:   limit,
		Offset:  offset,
	})
	if err != nil {
		// s.logger.Error("failed to list registrations for event", "error", err, "eventID", eventID)
		return nil, 0, fmt.Errorf("failed to list registrations for event: %w", err)
	}

	// 4. Handle empty result
	if len(rows) == 0 {
		return []payloads.EventRegistrationResponse{}, 0, nil
	}

	// 5. Initialize responses slice
	responses := make([]payloads.EventRegistrationResponse, 0, len(rows))

	// 6. Iterate rows and construct response
	for _, row := range rows {
		// Manually construct db.EventRegistration from row
		registration := db.EventRegistration{
			ID:                       row.ID,
			EventID:                  row.EventID,
			UserID:                   row.UserID,
			Status:                   row.Status,
			PaymentStatus:            row.PaymentStatus,
			RegistrationRole:         row.RegistrationRole,
			RegisteredAt:             row.RegisteredAt,
			AttendedAt:               row.AttendedAt,
			CancellationReasonByUser: row.CancellationReasonByUser,
			AdminNotesOnRegistration: row.AdminNotesOnRegistration,
			WaitlistPriority:         row.WaitlistPriority,
			CreatedAt:                row.CreatedAt,
			UpdatedAt:                row.UpdatedAt,
		}

		responseItem := payloads.EventRegistrationResponse{
			EventRegistration: registration,
			EventTitle:        event.Title,         // From event fetched once
			EventStartTime:    event.StartTime,     // From event fetched once
			UserDisplayName:   row.UserDisplayName, // From ListRegistrationsForEventRow
			UserEmail:         row.UserEmail,       // From ListRegistrationsForEventRow
			UserPhone:         row.UserPhone,       // From ListRegistrationsForEventRow
		}
		responses = append(responses, responseItem)
	}

	// 7. Return responses
	return responses, int64(len(responses)), nil
}

// UpdateRegistrationStatusByAdmin implements EventService.
func (s *eventService) UpdateRegistrationStatusByAdmin(ctx context.Context, registrationID uuid.UUID, orgID uuid.UUID, adminUserID uuid.UUID, newStatus string, adminNotes *string) (resp payloads.EventRegistrationResponse, err error) {
	// 1. Validate newStatus
	dbStatus := db.EventRegistrationStatusType(newStatus)
	if !dbStatus.Valid() {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("invalid new registration status: %s", newStatus)
	}

	var updatedReg db.EventRegistration
	var event db.Event // To store event details for response
	var user db.User   // To store user details for response

	// 2. Transaction
	tx, err := s.pool.Begin(ctx)
	if err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback(ctx)
			panic(r)
		} else if err != nil {
			_ = tx.Rollback(ctx)
		}
	}()

	qtx := s.queries.WithTx(tx)

	// 3. Permission Check (within transaction)
	originalReg, err := qtx.GetEventRegistrationByID(ctx, registrationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.EventRegistrationResponse{}, errors.New("event registration not found")
		}
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to retrieve event registration: %w", err)
	}
	originalStatus := originalReg.Status // Store original status

	event, err = qtx.GetEventByID(ctx, originalReg.EventID) // Assign to outer scope event variable
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// This should not happen if registration referred to a valid event
			return payloads.EventRegistrationResponse{}, errors.New("event not found for the registration")
		}
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to retrieve event: %w", err)
	}

	if event.OrganizationID != orgID {
		return payloads.EventRegistrationResponse{}, errors.New("permission denied: event does not belong to the specified organization")
	}

	// TODO: Add more granular permission check for adminUserID's rights in this orgID.

	// 4. Logic for Status Change (Optional - current query handles waitlist logic)
	// If approving a waitlisted user to registered, and event is full, current behavior is admin override.
	// If specific capacity checks are needed here, they would go here.

	// 5. Update Registration
	updateParams := db.UpdateEventRegistrationStatusParams{
		ID:                       registrationID,
		Status:                   dbStatus,   // This is the new status
		AdminNotesOnRegistration: adminNotes, // Corrected field name
	}
	updatedRegRow, err := qtx.UpdateEventRegistrationStatus(ctx, updateParams)
	if err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to update event registration status: %w", err)
	}
	updatedReg = db.EventRegistration{
		ID:                       updatedRegRow.ID,
		EventID:                  updatedRegRow.EventID,
		UserID:                   updatedRegRow.UserID,
		Status:                   updatedRegRow.Status,
		PaymentStatus:            updatedRegRow.PaymentStatus,
		RegistrationRole:         updatedRegRow.RegistrationRole,
		RegisteredAt:             updatedRegRow.RegisteredAt,
		AttendedAt:               updatedRegRow.AttendedAt,
		CancellationReasonByUser: updatedRegRow.CancellationReasonByUser,
		AdminNotesOnRegistration: updatedRegRow.AdminNotesOnRegistration,
		WaitlistPriority:         updatedRegRow.WaitlistPriority,
		CreatedAt:                updatedRegRow.CreatedAt,
		UpdatedAt:                updatedRegRow.UpdatedAt,
	}

	// 6. Commit Transaction
	if err = tx.Commit(ctx); err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// --- Waitlist Promotion Trigger (after commit) ---
	// Check if the original status consumed a slot and the new status (set by admin) frees it up.
	userConsumedSlot := originalStatus == db.EventRegistrationStatusTypeRegistered || originalStatus == db.EventRegistrationStatusTypePendingApproval
	statusFreesSlot := dbStatus == db.EventRegistrationStatusTypeRejectedApproval || dbStatus == db.EventRegistrationStatusTypeAbsent
	// Add any other admin-settable statuses that should trigger promotion, e.g., if you add CancelledByAdmin

	if userConsumedSlot && statusFreesSlot {
		go func() {
			// TODO: CRITICAL - Replace with a robust background job system (e.g., Asynq, Machinery).
			// Current goroutine approach lacks retries, durability, and observability.
			// A job queue would allow for:
			//   - Persistent tasks that survive server restarts.
			//   - Automatic retries with backoff for transient errors.
			//   - Better monitoring and error tracking.
			//   - Controlled concurrency for tasks like this.
			// JOB PAYLOAD: { event_id: event.ID, slots_to_fill: 1 }
			fmt.Printf("INFO: Registration %s status changed from %s to %s by admin, triggering waitlist promotion check for event %s\n", registrationID.String(), originalStatus, dbStatus, event.ID.String())
			promoted, promoErr := s.PromoteFromWaitlist(context.Background(), event.ID, 1)
			if promoErr != nil {
				fmt.Printf("ERROR: Background waitlist promotion failed after admin status update for event %s: %v\n", event.ID.String(), promoErr)
			} else if len(promoted) > 0 {
				fmt.Printf("INFO: Background waitlist promotion successful after admin status update for event %s, promoted: %d\n", event.ID.String(), len(promoted))
			}
		}()
	}

	// 7. Format Response (fetch user details outside transaction)
	user, err = s.queries.GetUserByID(ctx, updatedReg.UserID) // Assign to outer scope user variable
	// Event details are already in 'event'

	response := payloads.EventRegistrationResponse{
		EventRegistration: updatedReg,
		EventTitle:        event.Title,
		EventStartTime:    event.StartTime,
	}

	if err == nil { // user fetch error check
		response.UserDisplayName = user.DisplayName
		response.UserEmail = user.Email
		response.UserPhone = user.Phone
	} else {
		// s.logger.Warn("failed to fetch user details for UpdateRegistrationStatusByAdmin response", "error", err, "userID", updatedReg.UserID)
		response.UserDisplayName = "Error fetching name"
	}

	// 8. Notification (via Job Queue)
	if s.jobService != nil {
		var adminNotesPtr *string
		if adminNotes != nil {
			adminNotesPtr = adminNotes
		}
		notifyPayload := JobPayloadNotifyRegStatusUpdate{
			UserID:     user.ID,
			EventID:    event.ID,
			NewStatus:  string(dbStatus),
			AdminNotes: adminNotesPtr,
		}
		// Enqueue job to notify user of status update
		_, enqueueErr := s.jobService.EnqueueJob(context.Background(), JobTypeNotifyRegStatusUpdate, notifyPayload, 0*time.Second, nil)
		if enqueueErr != nil {
			fmt.Printf("ERROR: Failed to enqueue registration status update notification job for userID %s, eventID %s: %v\n", user.ID.String(), event.ID.String(), enqueueErr)
		}
	} else {
		fmt.Printf("WARN: JobService nil in UpdateRegistrationStatusByAdmin, cannot enqueue notification job.\n")
	}
	// --- Old goroutine notification removed ---

	// Send notification about the admin update
	if s.notificationService != nil {
		payload := map[string]interface{}{
			"UserName":        user.DisplayName,
			"EventName":       event.Title,
			"NewStatus":       string(updatedReg.Status), // Make sure to convert enum to string if needed by template
			"AdminNotes":      adminNotes,                // Use the potentially empty string for admin notes
			"EventDetailsURL": fmt.Sprintf("/events/%s", event.ID.String()),
		}
		if err := s.notificationService.SendToUser(ctx, user.ID, NotificationTypeEventAdminRegistrationUpdate, payload); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("userID", user.ID.String()).Str("registrationID", registrationID.String()).Msg("Failed to send event admin registration update notification")
			// Do not fail the operation due to notification error
		}
	} else {
		log.Ctx(ctx).Warn().Str("registrationID", registrationID.String()).Msg("NotificationService is nil, cannot send admin registration update notification.")
	}

	return s.formatEventRegistrationResponse(updatedReg, event, user), nil
}

// CancelEventRegistration implements EventService.
func (s *eventService) CancelEventRegistration(ctx context.Context, userID uuid.UUID, registrationID uuid.UUID) (resp payloads.EventRegistrationResponse, err error) {
	var finalReg db.EventRegistration // Keep var finalReg declared here due to its scope
	var event db.Event
	var user db.User
	var originalStatus db.EventRegistrationStatusType // To store status before cancellation for waitlist logic

	tx, err := s.pool.Begin(ctx)
	if err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback(ctx)
			panic(r)
		} else if err != nil {
			_ = tx.Rollback(ctx)
		}
	}()

	qtx := s.queries.WithTx(tx)

	// 2. Fetch and Verify Registration
	reg, err := qtx.GetEventRegistrationByID(ctx, registrationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.EventRegistrationResponse{}, errors.New("event registration not found")
		}
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to retrieve event registration: %w", err)
	}

	if reg.UserID != userID {
		return payloads.EventRegistrationResponse{}, errors.New("permission denied: user can only cancel their own registration")
	}

	originalStatus = reg.Status // Store for later

	// 3. Check Current Status
	switch reg.Status {
	case db.EventRegistrationStatusTypeCancelledByUser:
		// Already cancelled by user, fetch details and return current state with a specific error/message.
		_ = tx.Rollback(ctx) // Explicitly rollback as we are not proceeding with an update.
		user, _ = s.queries.GetUserByID(ctx, reg.UserID)
		event, _ = s.queries.GetEventByID(ctx, reg.EventID)
		return s.formatEventRegistrationResponse(reg, event, user), errors.New("registration is already cancelled by the user")
	case db.EventRegistrationStatusTypeAttended,
		db.EventRegistrationStatusTypeAbsent:
		_ = tx.Rollback(ctx)
		user, _ = s.queries.GetUserByID(ctx, reg.UserID)
		event, _ = s.queries.GetEventByID(ctx, reg.EventID)
		return s.formatEventRegistrationResponse(reg, event, user), errors.New("registration is finalized and cannot be cancelled")
		// Default case: Allow cancellation for other statuses like registered, waitlisted, pending_approval etc.
	}

	// 4. Update Status
	// We don't have a cancellation reason from the user in this method signature.
	finalRegRow, err := qtx.CancelEventRegistrationByUser(ctx, db.CancelEventRegistrationByUserParams{
		ID:     registrationID,
		UserID: userID,
		// CancellationReasonByUser: nil, // Handled by sqlc.narg if the field is nillable in params struct and query
	})
	if err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to cancel event registration: %w", err)
	}
	finalReg = db.EventRegistration{ // Use = for assignment as finalReg is already declared
		ID:                       finalRegRow.ID,
		EventID:                  finalRegRow.EventID,
		UserID:                   finalRegRow.UserID,
		Status:                   finalRegRow.Status,
		PaymentStatus:            finalRegRow.PaymentStatus,
		RegistrationRole:         finalRegRow.RegistrationRole,
		RegisteredAt:             finalRegRow.RegisteredAt,
		AttendedAt:               finalRegRow.AttendedAt,
		CancellationReasonByUser: finalRegRow.CancellationReasonByUser,
		AdminNotesOnRegistration: finalRegRow.AdminNotesOnRegistration,
		WaitlistPriority:         finalRegRow.WaitlistPriority,
		CheckInByUserID:          finalRegRow.CheckInByUserID,
		CheckInMethod:            finalRegRow.CheckInMethod,
		CreatedAt:                finalRegRow.CreatedAt,
		UpdatedAt:                finalRegRow.UpdatedAt,
	}

	// 5. Commit Transaction
	if err = tx.Commit(ctx); err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 6. Format Response (event and user details might have been fetched already if status was terminal)
	// Re-fetch event and user for consistency, though event is unlikely to change.
	event, _ = s.queries.GetEventByID(ctx, finalReg.EventID)
	user, _ = s.queries.GetUserByID(ctx, finalReg.UserID)
	formattedResponse := s.formatEventRegistrationResponse(finalReg, event, user)

	// 7. Waitlist Promotion Trigger
	// If the user was previously in a state that consumed a participant slot, try to promote one person from the waitlist.
	if originalStatus == db.EventRegistrationStatusTypeRegistered || originalStatus == db.EventRegistrationStatusTypePendingApproval { // Add other slot-consuming statuses if any (e.g., Confirmed)
		if s.jobService != nil {
			jobPayload := map[string]interface{}{
				"event_id":      finalReg.EventID,
				"slots_to_fill": 1,
			}
			log.Ctx(ctx).Info().Str("eventID", finalReg.EventID.String()).Interface("payload", jobPayload).Msg("[EventService] Attempting to enqueue PromoteFromWaitlist job")
			// Enqueue the job to run immediately (0 delay)
			// Pass the original context instead of context.Background()
			_, enqueueErr := s.jobService.EnqueueJob(ctx, JobTypePromoteWaitlist, jobPayload, 0*time.Second, nil)
			if enqueueErr != nil {
				// Log the error but don't fail the cancellation operation
				log.Ctx(ctx).Error().Err(enqueueErr).Str("eventID", finalReg.EventID.String()).Msg("[EventService] Failed to enqueue PromoteFromWaitlist job")
			} else {
				log.Ctx(ctx).Info().Str("eventID", finalReg.EventID.String()).Msg("[EventService] PromoteFromWaitlist job enqueued successfully (call to jobService.EnqueueJob returned success)")
			}
		} else {
			// Fallback or log error if jobService is unexpectedly nil
			log.Ctx(ctx).Warn().Str("eventID", finalReg.EventID.String()).Msg("[EventService] JobService not available in CancelEventRegistration, cannot enqueue promote job")
			// Optionally, fallback to the old goroutine if critical, but ideally fail if setup is wrong.
			/*
				go func() {
					// ... (existing goroutine logic) ...
				}()
			*/
		}
	}

	// 8. Notification (via Job Queue)
	if s.jobService != nil {
		notifyPayload := JobPayloadNotifyEvent{
			UserID:  user.ID,
			EventID: event.ID,
		}
		log.Ctx(ctx).Info().Str("userID", user.ID.String()).Str("eventID", event.ID.String()).Msg("[EventService] Attempting to enqueue EventCancellation notification job")
		// Enqueue job to notify user of cancellation
		// Pass the original context instead of context.Background()
		_, enqueueErr := s.jobService.EnqueueJob(ctx, JobTypeNotifyEventCancellation, notifyPayload, 0*time.Second, nil)
		if enqueueErr != nil {
			log.Ctx(ctx).Error().Err(enqueueErr).Str("userID", user.ID.String()).Str("eventID", event.ID.String()).Msg("[EventService] Failed to enqueue EventCancellation notification job")
		} else {
			log.Ctx(ctx).Info().Str("userID", user.ID.String()).Str("eventID", event.ID.String()).Msg("[EventService] EventCancellation notification job enqueued successfully")
		}
	} else {
		log.Ctx(ctx).Warn().Msg("[EventService] JobService nil in CancelEventRegistration, cannot enqueue EventCancellation notification job.")
	}
	// --- Old goroutine notification removed ---

	return formattedResponse, nil
}

// Helper function to format EventRegistrationResponse
func (s *eventService) formatEventRegistrationResponse(reg db.EventRegistration, event db.Event, user db.User) payloads.EventRegistrationResponse {
	return payloads.EventRegistrationResponse{
		EventRegistration: db.EventRegistration{
			ID:               reg.ID,
			EventID:          reg.EventID,
			UserID:           reg.UserID,
			Status:           reg.Status,
			PaymentStatus:    reg.PaymentStatus,
			RegistrationRole: reg.RegistrationRole,
			RegisteredAt:     reg.RegisteredAt,
			AttendedAt:       reg.AttendedAt,
			WaitlistPriority: reg.WaitlistPriority,
			CreatedAt:        reg.CreatedAt,
			UpdatedAt:        reg.UpdatedAt,
		},
		EventTitle:      event.Title,
		EventStartTime:  event.StartTime,
		UserDisplayName: user.DisplayName,
		UserEmail:       user.Email,
		UserPhone:       user.Phone,
	}
}

// UpdatePaymentStatus implements EventService.
func (s *eventService) UpdatePaymentStatus(ctx context.Context, registrationID uuid.UUID, newPaymentStatus string, staffOrVolunteerID uuid.UUID) (resp payloads.EventRegistrationResponse, err error) {
	// 1. Validate newPaymentStatus
	dbPaymentStatus := db.PaymentStatusType(newPaymentStatus)
	if !dbPaymentStatus.Valid() {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("invalid payment status: %s", newPaymentStatus)
	}

	var updatedReg db.EventRegistration
	var event db.Event
	var user db.User

	// 2. Transaction
	tx, err := s.pool.Begin(ctx)
	if err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback(ctx)
			panic(r)
		} else if err != nil {
			_ = tx.Rollback(ctx)
		}
	}()

	qtx := s.queries.WithTx(tx)

	// 3. Fetch Registration (to ensure it exists and get details for response)
	// Note: No ownership/permission check here based on requirement "No auth for this API endpoint"
	// The caller (handler) is responsible for ensuring registrationID is valid if needed.
	// However, fetching it prevents updating non-existent records.
	_, err = qtx.GetEventRegistrationByID(ctx, registrationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// If the endpoint is truly public, maybe return a specific error or handle differently.
			// For now, assume the ID should be valid if called.
			return payloads.EventRegistrationResponse{}, errors.New("event registration not found")
		}
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to retrieve event registration: %w", err)
	}

	// 4. Check Current Status (Optional - allow updates in most states)
	// Could add checks here if certain payment status updates are disallowed based on registration status.

	// 5. Update Payment Status
	updatedRegRow, err := qtx.UpdateEventRegistrationPaymentStatus(ctx, db.UpdateEventRegistrationPaymentStatusParams{
		ID:            registrationID,
		PaymentStatus: dbPaymentStatus,
	})
	if err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to update payment status: %w", err)
	}
	updatedReg = db.EventRegistration{
		ID:                       updatedRegRow.ID,
		EventID:                  updatedRegRow.EventID,
		UserID:                   updatedRegRow.UserID,
		Status:                   updatedRegRow.Status,
		PaymentStatus:            updatedRegRow.PaymentStatus,
		RegistrationRole:         updatedRegRow.RegistrationRole,
		RegisteredAt:             updatedRegRow.RegisteredAt,
		AttendedAt:               updatedRegRow.AttendedAt,
		CancellationReasonByUser: updatedRegRow.CancellationReasonByUser,
		AdminNotesOnRegistration: updatedRegRow.AdminNotesOnRegistration,
		WaitlistPriority:         updatedRegRow.WaitlistPriority,
		CreatedAt:                updatedRegRow.CreatedAt,
		UpdatedAt:                updatedRegRow.UpdatedAt,
	}

	// 6. Commit Transaction
	if err = tx.Commit(ctx); err != nil {
		return payloads.EventRegistrationResponse{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 7. Format Response (fetch user/event details outside transaction)
	user, _ = s.queries.GetUserByID(ctx, updatedReg.UserID)    // Use UserID from updatedReg
	event, _ = s.queries.GetEventByID(ctx, updatedReg.EventID) // Use EventID from updatedReg
	response := s.formatEventRegistrationResponse(updatedReg, event, user)

	// 8. Notification
	// TODO: Notify user "Payment status for your registration for [Event] updated to [Status]".
	// E.g.: go s.notificationService.SendPaymentStatusUpdate(context.Background(), updatedReg.UserID, event.Title, string(dbPaymentStatus))

	return response, nil
}

// ApplyForEventVolunteering implements EventService.
func (s *eventService) ApplyForEventVolunteering(ctx context.Context, userID uuid.UUID, eventID uuid.UUID, notes *string) (db.EventVolunteerApplication, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Str("event_id", eventID.String()).Logger()

	// 1. Fetch the event to check its status and organization ID
	event, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Msg("Event not found for volunteering application")
			return db.EventVolunteerApplication{}, ErrEventNotFound
		}
		logger.Error().Err(err).Msg("Error fetching event details")
		return db.EventVolunteerApplication{}, err
	}

	// TODO: Add check here if event.AreVolunteersRequired and if event.VolunteerApplicationDeadline has passed
	// if !event.AreVolunteersRequired {
	// 	return db.EventVolunteerApplication{}, errors.New("event is not seeking volunteers")
	// }
	// if event.VolunteerApplicationDeadline.Valid && time.Now().After(event.VolunteerApplicationDeadline.Time) {
	// 	return db.EventVolunteerApplication{}, errors.New("volunteer application deadline has passed")
	// }

	// 2. Check if the user is a qualified volunteer for the event's organization
	orgVolunteerApp, err := s.queries.GetUserVolunteerApplicationForOrganization(ctx, db.GetUserVolunteerApplicationForOrganizationParams{
		UserID:         userID,
		OrganizationID: event.OrganizationID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Info().Msg("User does not have any volunteer application with the organization")
			return db.EventVolunteerApplication{}, ErrOrgQualificationRequired
		}
		logger.Error().Err(err).Msg("Error checking organization volunteer qualification")
		return db.EventVolunteerApplication{}, err
	}
	if orgVolunteerApp.Status != db.ApplicationStatusEnumApproved {
		logger.Info().Str("org_app_status", string(orgVolunteerApp.Status)).Msg("User is not an approved volunteer for the organization")
		return db.EventVolunteerApplication{}, ErrOrgQualificationRequired
	}

	// 3. Check for an existing event volunteer application for this user and event.
	existingApp, err := s.queries.GetEventVolunteerApplicationByUserAndEvent(ctx, db.GetEventVolunteerApplicationByUserAndEventParams{
		UserID:  userID,
		EventID: eventID,
	})

	if err == nil { // Application exists
		switch existingApp.Status {
		case db.ApplicationStatusEnumApproved, db.ApplicationStatusEnumPending:
			logger.Info().Str("existing_status", string(existingApp.Status)).Msg("User already has an active or pending volunteer application for this event")
			return db.EventVolunteerApplication{}, ErrAlreadyAppliedToVolunteer
		case db.ApplicationStatusEnumRejected, db.ApplicationStatusEnumWithdrawn:
			logger.Info().Str("existing_status", string(existingApp.Status)).Msg("User has a rejected/withdrawn application, attempting to reactivate.")
			updatedAppParams := db.ReactivateEventVolunteerApplicationParams{
				EventID:                eventID,
				UserID:                 userID,
				ApplicationNotesByUser: notes,
			}
			reactivatedApp, reactivateErr := s.queries.ReactivateEventVolunteerApplication(ctx, updatedAppParams)
			if reactivateErr != nil {
				logger.Error().Err(reactivateErr).Msg("Error reactivating event volunteer application")
				return db.EventVolunteerApplication{}, reactivateErr
			}
			logger.Info().Str("application_id", reactivatedApp.ID.String()).Msg("Successfully reactivated event volunteer application.")
			return reactivatedApp, nil
		default:
			logger.Error().Str("existing_status", string(existingApp.Status)).Msg("User has an existing application with an unexpected or non-reappliable status.")
			return db.EventVolunteerApplication{}, fmt.Errorf("existing application has unhandled status: %s", existingApp.Status)
		}
	} else if !errors.Is(err, pgx.ErrNoRows) { // An error other than "not found" occurred
		logger.Error().Err(err).Msg("Error checking existing event volunteer application")
		return db.EventVolunteerApplication{}, err
	}

	// 4. If err was pgx.ErrNoRows (no existing application), create a new one.
	params := db.CreateEventVolunteerApplicationParams{
		EventID:                eventID,
		UserID:                 userID,
		OrganizationID:         event.OrganizationID, // Correctly placed within the struct
		Status:                 db.ApplicationStatusEnumPending,
		ApplicationNotesByUser: notes,
	}

	application, err := s.queries.CreateEventVolunteerApplication(ctx, params)
	if err != nil {
		logger.Error().Err(err).Msg("Error creating event volunteer application")
		return db.EventVolunteerApplication{}, err
	}

	logger.Info().Str("application_id", application.ID.String()).Msg("Successfully applied for event volunteering (new application).")
	return application, nil
}

func (s *eventService) WithdrawEventApplication(ctx context.Context, userID uuid.UUID, appID uuid.UUID) (payloads.EventVolunteerApplicationResponse, error) {
	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Str("application_id", appID.String()).Logger()

	// 1. Fetch the application
	app, err := s.queries.GetEventVolunteerApplicationByID(ctx, appID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Warn().Msg("Event volunteer application not found")
			return payloads.EventVolunteerApplicationResponse{}, ErrVolunteerApplicationNotFound
		}
		logger.Error().Err(err).Msg("Error fetching event volunteer application by ID")
		return payloads.EventVolunteerApplicationResponse{}, err
	}

	// Manual ownership check
	if app.UserID != userID {
		logger.Warn().Str("app_owner_id", app.UserID.String()).Msg("User does not own this event volunteer application")
		return payloads.EventVolunteerApplicationResponse{}, ErrVolunteerApplicationNotFound
	}

	// 2. Check withdrawable status
	if !(app.Status == db.ApplicationStatusEnumPending || app.Status == db.ApplicationStatusEnumApproved) {
		logger.Warn().Str("current_status", string(app.Status)).Msg("Application is not in a withdrawable state")
		return payloads.EventVolunteerApplicationResponse{}, errors.New("application status does not allow withdrawal")
	}

	// 3. Update status to withdrawn
	updatedDbApp, err := s.queries.SetEventVolunteerApplicationStatusToWithdrawnByUser(ctx, db.SetEventVolunteerApplicationStatusToWithdrawnByUserParams{
		ID:     appID,
		UserID: userID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Error().Err(err).Msg("Failed to withdraw event application, possibly due to status change or race condition")
			return payloads.EventVolunteerApplicationResponse{}, errors.New("application status does not allow withdrawal after check")
		}
		logger.Error().Err(err).Msg("Error updating event volunteer application status to withdrawn")
		return payloads.EventVolunteerApplicationResponse{}, err
	}

	// 4. Prepare response
	eventDetails, eventErr := s.queries.GetEventByID(ctx, updatedDbApp.EventID)
	userDetails, userErr := s.queries.GetUserByID(ctx, updatedDbApp.UserID)

	var reviewerDisplayName *string
	if updatedDbApp.ReviewedByUserID != nil { // Check if the pointer is nil
		reviewer, revErr := s.queries.GetUserByID(ctx, *updatedDbApp.ReviewedByUserID) // Dereference the pointer
		if revErr == nil {
			reviewerDisplayName = &reviewer.DisplayName
		} else {
			log.Ctx(ctx).Warn().Err(revErr).Str("reviewer_id", updatedDbApp.ReviewedByUserID.String()).Msg("Failed to fetch reviewer display name")
		}
	}

	response := payloads.EventVolunteerApplicationResponse{
		EventVolunteerApplication: updatedDbApp,
		UserDisplayName:           "N/A", // Default, will be updated
		ReviewerDisplayName:       reviewerDisplayName,
	}

	var orgName string
	if eventErr == nil {
		response.EventTitle = eventDetails.Title
		response.EventStartTime = eventDetails.StartTime
		// Fetch organization name using GetOrganizationByID
		org, orgLookupErr := s.queries.GetOrganizationByID(ctx, eventDetails.OrganizationID)
		if orgLookupErr == nil {
			orgName = org.Name
			response.OrganizationName = orgName
		} else {
			logger.Error().Err(orgLookupErr).Str("org_id", eventDetails.OrganizationID.String()).Msg("Failed to get organization details for withdraw response")
		}
	} else {
		logger.Error().Err(eventErr).Str("event_id", updatedDbApp.EventID.String()).Msg("Failed to get event details for withdraw response")
	}

	if userErr == nil {
		response.UserDisplayName = userDetails.DisplayName
		response.UserEmail = userDetails.Email
		response.UserPhone = userDetails.Phone
	} else {
		logger.Error().Err(userErr).Str("user_id", updatedDbApp.UserID.String()).Msg("Failed to get user details for withdraw response")
	}

	return response, nil
}

// ListUserEventVolunteerApplications implements EventService.
func (s *eventService) ListUserEventVolunteerApplications(ctx context.Context, userID uuid.UUID, pageParams payloads.PageRequest) ([]payloads.EventVolunteerApplicationResponse, error) {
	limit := int32(DefaultPageSize) // Default limit
	if pageParams.Limit > 0 {
		limit = int32(pageParams.Limit)
		if limit > MaxPageSize {
			limit = MaxPageSize
		}
	}
	offset := int32(pageParams.Offset)

	logger := log.Ctx(ctx).With().Str("user_id", userID.String()).Int32("limit", limit).Int32("offset", offset).Logger()

	rows, err := s.queries.ListEventVolunteerApplicationsForUser(ctx, db.ListEventVolunteerApplicationsForUserParams{
		UserID: userID,
		Limit:  limit,
		Offset: offset,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return []payloads.EventVolunteerApplicationResponse{}, nil
		}
		logger.Error().Err(err).Msg("failed to list event volunteer applications for user")
		return nil, fmt.Errorf("failed to list event volunteer applications: %w", err)
	}

	if len(rows) == 0 {
		return []payloads.EventVolunteerApplicationResponse{}, nil
	}

	// Fetch user details once (since it's the same user for all items)
	user, userErr := s.queries.GetUserByID(ctx, userID)
	if userErr != nil {
		logger.Warn().Err(userErr).Msg("Failed to fetch user details for ListUserEventVolunteerApplications; user info will be missing in responses.")
	}

	responses := make([]payloads.EventVolunteerApplicationResponse, 0, len(rows))
	for _, row := range rows {
		// Manually construct db.EventVolunteerApplication from row
		// Note: va.OrganizationID is the event_volunteer_applications.organization_id,
		// while row.EventOrganizationID is from events.organization_id
		// and row.OrganizationName is from the joined organizations table aliased as 'o'
		app := db.EventVolunteerApplication{
			ID:                     row.ID,
			EventID:                row.EventID,
			UserID:                 row.UserID,
			OrganizationID:         row.OrganizationID, // This is the direct OrganizationID from event_volunteer_applications table
			Status:                 row.Status,
			ApplicationNotesByUser: row.ApplicationNotesByUser,
			AdminReviewNotes:       row.AdminReviewNotes,
			AppliedAt:              row.AppliedAt,
			ReviewedAt:             row.ReviewedAt,
			ReviewedByUserID:       row.ReviewedByUserID,
			CreatedAt:              row.CreatedAt,
			UpdatedAt:              row.UpdatedAt,
			AttendedAt:             row.AttendedAt,
		}

		var eventDescription *string
		if row.EventDescription != nil && len(row.EventDescription) > 0 {
			desc := string(row.EventDescription)
			eventDescription = &desc
		}

		var mediaItems []payloads.MediaItemResponse
		if row.MediaItems != nil {
			switch mediaData := row.MediaItems.(type) {
			case []byte:
				if err := json.Unmarshal(mediaData, &mediaItems); err != nil {
					log.Ctx(ctx).Error().Err(err).Str("event_id", row.EventID.String()).Msg("Failed to unmarshal media items (from []byte) for ListUserEventVolunteerApplications")
				}
			case string:
				if err := json.Unmarshal([]byte(mediaData), &mediaItems); err != nil {
					log.Ctx(ctx).Error().Err(err).Str("event_id", row.EventID.String()).Msg("Failed to unmarshal media items (from string) for ListUserEventVolunteerApplications")
				}
			case []interface{}:
				jsonData, err := json.Marshal(mediaData)
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Str("event_id", row.EventID.String()).Msg("Failed to re-marshal media items from []interface{} for ListUserEventVolunteerApplications")
				} else {
					if err := json.Unmarshal(jsonData, &mediaItems); err != nil {
						log.Ctx(ctx).Error().Err(err).Str("event_id", row.EventID.String()).Msg("Failed to unmarshal media items from re-marshaled JSON for ListUserEventVolunteerApplications")
					}
				}
			default:
				log.Ctx(ctx).Warn().Str("user_id", userID.String()).Str("event_id", row.EventID.String()).Interface("media_items_type", fmt.Sprintf("%T", row.MediaItems)).Msg("MediaItems in ListUserEventVolunteerApplications was not []byte, string, or []interface{} as expected. Media items might be missing or incorrect.")
			}
		}

		if mediaItems == nil { // Ensure it's an empty slice, not nil, for JSON serialization in the response.
			mediaItems = []payloads.MediaItemResponse{}
		}

		eventOrgID := row.EventOrganizationID // This is e.organization_id

		responseItem := payloads.EventVolunteerApplicationResponse{
			EventVolunteerApplication: app,
			EventTitle:                row.EventTitle,
			EventStartTime:            row.EventStartTime,
			EventEndTime:              row.EventEndTime,
			EventDescription:          eventDescription,
			EventLocationType:         string(row.EventLocationType),
			EventLocationFullAddress:  row.EventLocationFullAddress,
			EventLocationOnlineURL:    row.EventLocationOnlineUrl,
			EventStatus:               string(row.EventStatus),
			EventOrganizationID:       &eventOrgID,
			OrganizationName:          "", // Will be populated from row.OrganizationName (o.name)
			EventPrice:                row.EventPrice,
			EventContactEmail:         row.EventContactEmail,
			EventContactPhone:         row.EventContactPhone,
			MediaItems:                mediaItems,
		}
		if row.OrganizationName != nil { // o.name
			responseItem.OrganizationName = *row.OrganizationName
		}

		if userErr == nil && user.ID != uuid.Nil {
			responseItem.UserDisplayName = user.DisplayName
			responseItem.UserEmail = user.Email
			responseItem.UserPhone = user.Phone
		} else {
			responseItem.UserDisplayName = "Error fetching name" // Or leave blank
		}

		// ReviewerDisplayName:
		// The current query ListEventVolunteerApplicationsForUserRow does not fetch reviewer's display name.
		// If app.ReviewedByUserID is not nil, a separate query would be needed here to fetch user details for reviewer.
		// For now, this will be omitted or remain whatever default EventVolunteerApplicationResponse sets.
		// If it needs to be populated, further changes to query or additional logic here would be required.

		responses = append(responses, responseItem)
	}

	return responses, nil
}

// ListEventVolunteerApplicationsForEvent implements EventService.
func (s *eventService) ListEventVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, adminUserID uuid.UUID, pageParams payloads.PageRequest) ([]payloads.EventVolunteerApplicationResponse, error) {
	// 1. Permission Check & Fetch Event
	event, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("event not found")
		}
		// s.logger.Error("failed to get event by ID for ListEventVolunteerApplicationsForEvent", "error", err, "eventID", eventID)
		return nil, fmt.Errorf("failed to retrieve event: %w", err)
	}

	if event.OrganizationID != orgID {
		return nil, errors.New("permission denied: event does not belong to the specified organization")
	}

	// TODO: Add more granular permission check: does adminUserID have rights to view volunteer applications for this orgID/eventID?

	// 2. Determine limit and offset
	limit := int32(20) // Default limit
	if pageParams.Limit > 0 {
		limit = int32(pageParams.Limit)
	}
	offset := int32(pageParams.Offset)

	// 3. Call query
	rows, err := s.queries.ListEventVolunteerApplicationsForEvent(ctx, db.ListEventVolunteerApplicationsForEventParams{
		EventID: eventID,
		Limit:   limit,
		Offset:  offset,
	})
	if err != nil {
		// s.logger.Error("failed to list event volunteer applications for event", "error", err, "eventID", eventID)
		return nil, fmt.Errorf("failed to list event volunteer applications: %w", err)
	}

	// 4. Handle empty result
	if len(rows) == 0 {
		return []payloads.EventVolunteerApplicationResponse{}, nil
	}

	// 5. Initialize responses slice
	responses := make([]payloads.EventVolunteerApplicationResponse, 0, len(rows))

	// 6. Iterate rows and construct response
	for _, row := range rows {
		// Manually construct db.EventVolunteerApplication from row
		app := db.EventVolunteerApplication{
			ID:                     row.ID,
			EventID:                row.EventID,
			UserID:                 row.UserID,
			OrganizationID:         row.OrganizationID,
			Status:                 row.Status,
			ApplicationNotesByUser: row.ApplicationNotesByUser,
			AdminReviewNotes:       row.AdminReviewNotes,
			AppliedAt:              row.AppliedAt,
			ReviewedAt:             row.ReviewedAt,
			ReviewedByUserID:       row.ReviewedByUserID,
			CreatedAt:              row.CreatedAt,
			UpdatedAt:              row.UpdatedAt,
		}

		responseItem := payloads.EventVolunteerApplicationResponse{
			EventVolunteerApplication: app,
			EventTitle:                event.Title,         // From event fetched once
			EventStartTime:            event.StartTime,     // From event fetched once
			UserDisplayName:           row.UserDisplayName, // From Row
			UserEmail:                 row.UserEmail,       // From Row
			UserPhone:                 row.UserPhone,       // From Row
		}
		responses = append(responses, responseItem)
	}

	// 9. Return responses
	return responses, nil
}

// ReviewEventVolunteerApplication implements EventService.
func (s *eventService) ReviewEventVolunteerApplication(ctx context.Context, applicationID uuid.UUID, orgID uuid.UUID, eventID uuid.UUID, adminUserID uuid.UUID, newStatus string, adminNotes *string) (resp payloads.EventVolunteerApplicationResponse, err error) {
	// 1. Validate newStatus
	dbStatus := db.ApplicationStatusEnum(newStatus)
	if !dbStatus.Valid() {
		return payloads.EventVolunteerApplicationResponse{}, fmt.Errorf("invalid new volunteer application status for review: %s. Must be one of: pending, approved, rejected, withdrawn", newStatus)
	}

	var finalApp db.EventVolunteerApplication
	var event db.Event // For response
	var user db.User   // For response

	// 2. Transaction
	tx, err := s.pool.Begin(ctx)
	if err != nil {
		return payloads.EventVolunteerApplicationResponse{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback(ctx)
			panic(r) // Re-panic after rollback
		} else if err != nil {
			_ = tx.Rollback(ctx)
		}
	}()

	qtx := s.queries.WithTx(tx)

	// 3. Permission/Existence Check
	app, err := qtx.GetEventVolunteerApplicationDetailsByID(ctx, applicationID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.EventVolunteerApplicationResponse{}, errors.New("volunteer application not found")
		}
		return payloads.EventVolunteerApplicationResponse{}, fmt.Errorf("failed to retrieve volunteer application: %w", err)
	}

	// Capture original status for logging
	originalStatus := app.Status

	if app.OrganizationID != orgID {
		return payloads.EventVolunteerApplicationResponse{}, errors.New("permission denied: application does not belong to this organization")
	}
	// Add EventID check
	if app.EventID != eventID {
		return payloads.EventVolunteerApplicationResponse{}, errors.New("permission denied: application does not belong to this event")
	}

	if app.Status != db.ApplicationStatusEnumPending { // Corrected: Use consolidated enum
		// Log warning instead of blocking the status change
		log.Printf("WARNING: Changing volunteer application status from %s to %s for application ID: %s",
			originalStatus, dbStatus, applicationID)
	}

	// TODO: Add more granular permission check: does adminUserID have rights to review volunteer applications for this orgID?

	// 4. Update Application Status
	updateParams := db.UpdateEventVolunteerApplicationStatusParams{
		ID:               applicationID,
		Status:           dbStatus, // This should now be of type db.ApplicationStatusEnum
		AdminReviewNotes: adminNotes,
		ReviewedByUserID: &adminUserID, // Pass address of adminUserID
	}
	finalApp, err = qtx.UpdateEventVolunteerApplicationStatus(ctx, updateParams)
	if err != nil {
		return payloads.EventVolunteerApplicationResponse{}, fmt.Errorf("failed to update volunteer application status: %w", err)
	}

	// 5. Handle Side Effects Based on Status Transitions
	// Check transition type to handle appropriate side effects
	if originalStatus == db.ApplicationStatusEnumApproved && dbStatus == db.ApplicationStatusEnumRejected {
		// Handle approved → rejected transition
		// Find and delete existing volunteer registration
		existingReg, regErr := qtx.GetEventRegistrationByUserAndEvent(ctx, db.GetEventRegistrationByUserAndEventParams{
			UserID:  finalApp.UserID,
			EventID: finalApp.EventID,
		})

		if regErr == nil && existingReg.RegistrationRole == db.EventRegistrationRoleTypeVolunteer {
			// Delete the volunteer registration
			if delErr := qtx.DeleteEventRegistration(ctx, existingReg.ID); delErr != nil {
				log.Ctx(ctx).Error().
					Err(delErr).
					Str("registrationID", existingReg.ID.String()).
					Str("userID", finalApp.UserID.String()).
					Str("eventID", finalApp.EventID.String()).
					Msg("Failed to delete volunteer registration for rejected application")
				// Do not fail the transaction, but log the error
			} else {
				log.Ctx(ctx).Info().
					Str("registrationID", existingReg.ID.String()).
					Str("userID", finalApp.UserID.String()).
					Str("eventID", finalApp.EventID.String()).
					Msg("Successfully deleted volunteer registration for rejected application")
			}
		}

	} else if originalStatus == db.ApplicationStatusEnumRejected && dbStatus == db.ApplicationStatusEnumApproved {
		// Handle rejected → approved transition
		// Check if registration already exists
		existingReg, regErr := qtx.GetEventRegistrationByUserAndEvent(ctx, db.GetEventRegistrationByUserAndEventParams{
			UserID:  finalApp.UserID,
			EventID: finalApp.EventID,
		})

		// If no registration exists, create one with 'volunteer' role
		if errors.Is(regErr, pgx.ErrNoRows) {
			_, createRegErr := qtx.CreateEventRegistration(ctx, db.CreateEventRegistrationParams{
				EventID:          finalApp.EventID,
				UserID:           finalApp.UserID,
				PaymentStatus:    db.PaymentStatusTypeNotRequired,
				Status:           db.EventRegistrationStatusTypeRegistered,
				RegistrationRole: db.EventRegistrationRoleTypeVolunteer,
			})
			if createRegErr != nil {
				log.Ctx(ctx).Error().
					Err(createRegErr).
					Str("userID", finalApp.UserID.String()).
					Str("eventID", finalApp.EventID.String()).
					Msg("Failed to create volunteer registration for re-approved application")
				// Do not fail the transaction for this
			} else {
				log.Ctx(ctx).Info().
					Str("userID", finalApp.UserID.String()).
					Str("eventID", finalApp.EventID.String()).
					Msg("Successfully created volunteer registration for re-approved application")
			}
		} else if regErr == nil {
			// Registration already exists, log it
			log.Ctx(ctx).Info().
				Str("userID", finalApp.UserID.String()).
				Str("eventID", finalApp.EventID.String()).
				Str("registrationRole", string(existingReg.RegistrationRole)).
				Msg("Registration already exists for re-approved volunteer application")
		}

	} else if dbStatus == db.ApplicationStatusEnumApproved {
		// Handle pending → approved transition (existing logic)
		// Check if they already have a 'participant' registration for this event
		existingReg, regErr := qtx.GetEventRegistrationByUserAndEvent(ctx, db.GetEventRegistrationByUserAndEventParams{
			UserID:  finalApp.UserID,
			EventID: finalApp.EventID,
		})

		// If a registration exists and they were a participant, remove it
		if regErr == nil && existingReg.RegistrationRole == db.EventRegistrationRoleTypeParticipant {
			if delErr := qtx.DeleteEventRegistration(ctx, existingReg.ID); delErr != nil {
				log.Ctx(ctx).Error().Err(delErr).Str("registrationID", existingReg.ID.String()).Msg("Failed to delete participant registration for approved volunteer")
				// Do not fail the transaction, but this is a state inconsistency
			}
			regErr = pgx.ErrNoRows // Set regErr to ErrNoRows to signify we need to create a new registration
		}

		// If no registration exists for the user for this event, create one with the 'volunteer' role
		if errors.Is(regErr, pgx.ErrNoRows) {
			_, createRegErr := qtx.CreateEventRegistration(ctx, db.CreateEventRegistrationParams{
				EventID:          finalApp.EventID,
				UserID:           finalApp.UserID,
				PaymentStatus:    db.PaymentStatusTypeNotRequired,
				Status:           db.EventRegistrationStatusTypeRegistered, // Volunteers are considered registered
				RegistrationRole: db.EventRegistrationRoleTypeVolunteer,    // Set role to 'volunteer'
			})
			if createRegErr != nil {
				log.Ctx(ctx).Error().Err(createRegErr).Str("userID", finalApp.UserID.String()).Str("eventID", finalApp.EventID.String()).Msg("Failed to auto-create 'volunteer' event registration for approved volunteer")
				// Do not fail the transaction for this
			}
		} else if regErr != nil {
			log.Ctx(ctx).Error().Err(regErr).Str("userID", finalApp.UserID.String()).Str("eventID", finalApp.EventID.String()).Msg("DB error while checking for existing registration for approved volunteer")
		}
	}

	// Send approval notification if application was approved
	if dbStatus == db.ApplicationStatusEnumApproved {
		// Fetch event and user details for the notification payload
		eventDetails, eventErr := qtx.GetEventByID(ctx, finalApp.EventID)
		userDetails, userErr := qtx.GetUserByID(ctx, finalApp.UserID)

		if eventErr != nil {
			log.Ctx(ctx).Error().Err(eventErr).Str("eventID", finalApp.EventID.String()).Msg("Failed to fetch event details for volunteer approval notification")
			// Proceed with transaction commit, but notification might be incomplete
		} else if userErr != nil {
			log.Ctx(ctx).Error().Err(userErr).Str("userID", finalApp.UserID.String()).Msg("Failed to fetch user details for volunteer approval notification")
			// Proceed with transaction commit, but notification might be incomplete
		} else {
			// Send Notification if notificationService is available
			if s.notificationService != nil {
				payload := map[string]interface{}{
					"UserName":        userDetails.DisplayName,
					"EventName":       eventDetails.Title,
					"EventDetailsURL": fmt.Sprintf("/events/%s", eventDetails.ID.String()),
				}
				if notifyErr := s.notificationService.SendToUser(ctx, userDetails.ID, NotificationTypeVolunteerApplicationApproved, payload); notifyErr != nil {
					log.Ctx(ctx).Error().Err(notifyErr).Str("userID", userDetails.ID.String()).Msg("Failed to send volunteer application approved notification")
					// Do not rollback transaction for notification failure
				}
			} else {
				log.Ctx(ctx).Warn().Msg("NotificationService not available in EventService, skipping volunteer approval notification.")
			}
		}
	}

	// 6. Commit Transaction
	if err = tx.Commit(ctx); err != nil {
		return payloads.EventVolunteerApplicationResponse{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 7. Format Response (outside transaction)
	event, _ = s.queries.GetEventByID(ctx, finalApp.EventID) // Best effort to get event title/time
	user, _ = s.queries.GetUserByID(ctx, finalApp.UserID)    // Best effort to get user details

	// TODO: 8. Notifications: Notify applicant of approval/rejection. Consider sending a separate notification if an event registration was also created.
	// E.g., go s.notificationService.SendVolunteerApplicationReviewedNotification(context.Background(), finalApp, event, user)

	return s.formatEventVolunteerApplicationResponse(finalApp, event, user), nil
}

// formatEventVolunteerApplicationResponse is a helper to consistently format the response.
func (s *eventService) formatEventVolunteerApplicationResponse(app db.EventVolunteerApplication, event db.Event, user db.User) payloads.EventVolunteerApplicationResponse {
	resp := payloads.EventVolunteerApplicationResponse{
		EventVolunteerApplication: app,
		EventTitle:                event.Title,
		EventStartTime:            event.StartTime,
	}
	if user.ID != uuid.Nil { // Check if user struct is populated (e.g. GetUserByID didn't error hard)
		resp.UserDisplayName = user.DisplayName
		resp.UserEmail = user.Email
		resp.UserPhone = user.Phone
	} else {
		resp.UserDisplayName = "Error fetching name"
	}
	return resp
}

// GetUserEventStatistics implements EventService.
func (s *eventService) GetUserEventStatistics(ctx context.Context, userID uuid.UUID, startDate, endDate *time.Time, pageParams payloads.PageRequest) ([]payloads.UserEventStatisticItem, error) {
	limit := int32(20) // Default limit
	if pageParams.Limit > 0 {
		limit = int32(pageParams.Limit)
	}
	offset := int32(pageParams.Offset)

	dbParams := db.GetUserEventStatisticsParams{
		UserID:          userID,
		Limit:           limit,
		Offset:          offset,
		StartDateFilter: startDate,
		EndDateFilter:   endDate,
	}

	rows, err := s.queries.GetUserEventStatistics(ctx, dbParams)
	if err != nil {
		// s.logger.Error("failed to get user event statistics", "error", err, "userID", userID, "params", dbParams)
		return nil, fmt.Errorf("failed to retrieve user event statistics: %w", err)
	}

	if len(rows) == 0 {
		return []payloads.UserEventStatisticItem{}, nil
	}

	stats := make([]payloads.UserEventStatisticItem, 0, len(rows))
	for _, row := range rows {
		attended, ok := row.Attended.(bool)
		if !ok {
			// s.logger.Warn("Failed to assert 'attended' field to bool for user event stats", "eventID", row.EventID, "userID", userID, "attendedValue", row.Attended)
			attended = false // Default to false if assertion fails
		}

		item := payloads.UserEventStatisticItem{
			EventID:                   row.EventID,
			EventTitle:                row.EventTitle,
			EventStartTime:            row.EventStartTime,
			EventStatus:               string(row.EventStatus),
			RegistrationStatus:        string(row.RegistrationStatus),
			RegistrationPaymentStatus: string(row.RegistrationPaymentStatus),
			Attended:                  attended,
		}
		stats = append(stats, item)
	}

	return stats, nil
}

// GetOrganizationEventStatistics implements EventService.
func (s *eventService) GetOrganizationEventStatistics(ctx context.Context, orgID uuid.UUID, adminUserID uuid.UUID, startDate, endDate *time.Time, tagIDs []uuid.UUID, pageParams payloads.PageRequest) ([]payloads.OrgEventStatisticItem, error) {
	// 1. Fetch user to check their role
	user, err := s.queries.GetUserByID(ctx, adminUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("permission denied: user not found")
		}
		return nil, fmt.Errorf("failed to get user for permission check: %w", err)
	}

	// Bypass membership check for superadmins
	if user.Role != db.UserRoleSuperadmin {
		// 2. Permission Check (Basic: is adminUserID a member of orgID?)
		membership, err := s.queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
			OrganizationID: orgID,
			UserID:         adminUserID,
		})
		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return nil, errors.New("permission denied: user is not a member of the organization or organization does not exist")
			}
			// s.logger.Error("failed to get user organization membership for stats", "error", err, "orgID", orgID, "adminUserID", adminUserID)
			return nil, fmt.Errorf("failed to verify organization membership: %w", err)
		}

		// TODO: Enhance permission check based on membership.Role (e.g., require 'admin' or 'owner' role)
		// For now, any active member can view stats. Add this if more specific roles are needed.
		if !membership.IsActive {
			return nil, errors.New("permission denied: user membership in the organization is not active")
		}
	}

	// 3. Determine limit and offset
	limit := int32(20) // Default limit
	if pageParams.Limit > 0 {
		limit = int32(pageParams.Limit)
	}
	offset := int32(pageParams.Offset)

	// 4. Prepare DB Params (ignoring tagIDs for now as query doesn't support it)
	dbParams := db.GetOrganizationEventStatisticsParams{
		OrganizationID:  orgID,
		Limit:           limit,
		Offset:          offset,
		StartDateFilter: startDate,
		EndDateFilter:   endDate,
	}

	// 5. Call query
	rows, err := s.queries.GetOrganizationEventStatistics(ctx, dbParams)
	if err != nil {
		// s.logger.Error("failed to get organization event statistics", "error", err, "orgID", orgID, "params", dbParams)
		return nil, fmt.Errorf("failed to retrieve organization event statistics: %w", err)
	}

	if len(rows) == 0 {
		return []payloads.OrgEventStatisticItem{}, nil
	}

	// 6. Map to response DTO
	stats := make([]payloads.OrgEventStatisticItem, 0, len(rows))
	for _, row := range rows {
		item := payloads.OrgEventStatisticItem{
			EventID:                    row.EventID,
			EventTitle:                 row.EventTitle,
			EventStartTime:             row.EventStartTime,
			EventStatus:                string(row.EventEventStatus),
			TotalRegisteredOrFinalized: row.TotalRegisteredOrFinalized,
			TotalAttended:              row.TotalAttended,
			TotalPaidRegistrations:     row.TotalPaidRegistrations,
		}
		stats = append(stats, item)
	}

	return stats, nil
}

// ListEventsByOrganization implements EventService.
// Updated to accept filterParams payloads.ListOrganizationEventsRequest
func (s *eventService) ListEventsByOrganization(ctx context.Context, orgID uuid.UUID, userID uuid.UUID, filterParams payloads.ListOrganizationEventsRequest) ([]payloads.EventResponse, int64, error) {
	// TODO: Authorization Checks:
	// 1. Is userID a member of orgID?
	// 2. Does userID have permission to list events for this org (e.g., based on role)?
	// 3. If filterParams.IsAdminView is true, are elevated permissions required?

	// Determine statuses to filter by
	var statuses []string
	if filterParams.Status != nil && *filterParams.Status != "" {
		statuses = []string{*filterParams.Status}
	} else {
		// Default statuses if not provided by filter: typically 'published', 'draft' for internal views
		// Adjust as per application requirements for organization-specific views.
		// For an admin/org member view, they might want to see more than just published events.
		statuses = []string{
			string(db.EventStatusTypePublished),
			string(db.EventStatusTypeDraft),
			string(db.EventStatusTypeArchived),
			string(db.EventStatusTypeHidden),
			string(db.EventStatusTypeCancelled),
		}
	}
	if filterParams.IsAdminView {
		// Admins might see deleted events too
		// statuses = append(statuses, string(db.EventStatusTypeDeleted))
	}

	var verificationTypeKeys []string
	if filterParams.EventVerificationTypeKey != nil && *filterParams.EventVerificationTypeKey != "" {
		keys := strings.Split(*filterParams.EventVerificationTypeKey, ",")
		for _, key := range keys {
			trimmedKey := strings.TrimSpace(key)
			if trimmedKey != "" {
				verificationTypeKeys = append(verificationTypeKeys, trimmedKey)
			}
		}
	}
	if len(verificationTypeKeys) == 0 {
		verificationTypeKeys = nil
	}

	countParams := db.CountEventsByOrganizationParams{
		OrganizationID:            orgID,
		Statuses:                  statuses,
		StartDate:                 filterParams.StartDate,
		EndDate:                   filterParams.EndDate,
		SearchTerm:                filterParams.SearchTerm,
		TagIds:                    filterParams.TagIDs, // Corrected casing from TagIDs
		GovernmentFundingKeys:     filterParams.GovernmentFundingKeys,
		EventVerificationTypeKeys: verificationTypeKeys,
	}

	totalCount, err := s.queries.CountEventsByOrganization(ctx, countParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to count events by organization")
		return nil, 0, fmt.Errorf("failed to count events: %w", err)
	}

	if totalCount == 0 {
		return []payloads.EventResponse{}, 0, nil
	}

	dbParams := db.ListEventsByOrganizationParams{
		OrganizationID:            orgID,
		Limit:                     int32(filterParams.Limit),
		Offset:                    int32(filterParams.Offset),
		Statuses:                  statuses,
		StartDate:                 filterParams.StartDate,
		EndDate:                   filterParams.EndDate,
		SearchTerm:                filterParams.SearchTerm,
		TagIds:                    filterParams.TagIDs, // Corrected casing from TagIDs
		GovernmentFundingKeys:     filterParams.GovernmentFundingKeys,
		EventVerificationTypeKeys: verificationTypeKeys,
	}
	// Ensure default limit if not set or zero
	if dbParams.Limit == 0 {
		dbParams.Limit = DefaultPageSize
	}

	dbEvents, err := s.queries.ListEventsByOrganization(ctx, dbParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to list events by organization from DB")
		return nil, 0, fmt.Errorf("failed to list events: %w", err)
	}

	if len(dbEvents) == 0 {
		return []payloads.EventResponse{}, totalCount, nil
	}

	eventResponses := make([]payloads.EventResponse, 0, len(dbEvents))
	for _, dbEvent := range dbEvents {
		// Pass filterParams.IsAdminView to determine if admin-level details are populated
		resp, err := s.getEventResponseFromDBEvent(ctx, s.queries, dbEvent, userID, filterParams.IsAdminView)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("eventID", dbEvent.ID.String()).Msg("Failed to convert DB event to response for ListEventsByOrganization")
			// Decide whether to skip this event or return an error for the whole request
			continue // Skip problematic event for now
		}
		eventResponses = append(eventResponses, resp)
	}

	return eventResponses, totalCount, nil
}

// ListPublicEvents implements EventService.
func (s *eventService) ListPublicEvents(ctx context.Context, userID uuid.UUID, filterParams payloads.ListPublicEventsRequest) ([]payloads.PublicEventResponse, int64, error) {
	// Default and validate pagination
	limit := DefaultPageSize
	if filterParams.Limit > 0 {
		limit = filterParams.Limit
		if limit > MaxPageSize {
			limit = MaxPageSize
		}
	}
	offset := 0
	if filterParams.Offset > 0 {
		offset = filterParams.Offset
	}

	// Default status to published if not provided
	queryStatuses := []string{string(db.EventStatusTypePublished)}
	if filterParams.Status != nil && *filterParams.Status != "" {
		// TODO: Validate status against allowed public statuses if necessary
		queryStatuses = []string{*filterParams.Status}
	}

	dbListParams := db.ListPublicEventsWithCountsParams{
		Limit:                    int32(limit),
		Offset:                   int32(offset),
		Statuses:                 queryStatuses,
		StartDate:                filterParams.StartDate,  // Direct assignment
		EndDate:                  filterParams.EndDate,    // Direct assignment
		SearchTerm:               filterParams.SearchTerm, // Direct assignment
		TagIds:                   filterParams.TagIDs,
		GovernmentFundingKeys:    filterParams.GovernmentFundingKeys,
		EventVerificationTypeKey: filterParams.EventVerificationTypeKey, // Direct assignment
		OrganizationID:           filterParams.OrganizationID,           // Direct assignment
		OrganizationId2:          filterParams.OrganizationID2,          // Added for second org filter, direct assignment
	}

	log.Ctx(ctx).Info().Interface("dbListParams_TagIds_for_ListPublicEventsWithCounts", dbListParams.TagIds).Str("service_method", "ListPublicEvents").Msg("DEBUG: TagIDs passed to ListPublicEventsWithCountsParams")

	dbEventRows, err := s.queries.ListPublicEventsWithCounts(ctx, dbListParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Interface("params", dbListParams).Msg("Failed to list public events with counts from DB")
		return nil, 0, fmt.Errorf("failed to list public events with counts: %w", err)
	}

	if len(dbEventRows) == 0 {
		return []payloads.PublicEventResponse{}, 0, nil
	}

	// Batch fetch media items
	eventIDs := make([]uuid.UUID, 0, len(dbEventRows))
	for _, eventRow := range dbEventRows {
		eventIDs = append(eventIDs, eventRow.ID)
	}
	mediaItemsByEventID, err := s.batchFetchMediaItemsForEvents(ctx, eventIDs)
	if err != nil {
		// Log error but continue, media items are supplementary
		log.Ctx(ctx).Error().Err(err).Msg("Failed to batch fetch media items for events")
	}

	// Batch fetch tags
	tagsByEventID, err := s.batchFetchTagsForEvents(ctx, eventIDs)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to batch fetch tags for events")
	}

	// Batch fetch current user registration status if user is logged in
	userRegStatuses := make(map[uuid.UUID]*payloads.UserEventStatusInfo)
	if userID != uuid.Nil {
		userRegStatuses, err = s.batchFetchUserEventRegistrationStatus(ctx, userID, eventIDs)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to batch fetch user event registration statuses")
		}
	}

	respEvents := make([]payloads.PublicEventResponse, 0, len(dbEventRows))
	for _, eventRow := range dbEventRows {
		respItem := payloads.PublicEventResponse{
			ID:               eventRow.ID,
			OrganizationID:   eventRow.OrganizationID,
			OrganizationName: eventRow.OrganizationName,

			Title:                 eventRow.Title,
			JsonContent:           eventRow.DescriptionContent,
			LocationType:          string(eventRow.LocationType),
			LocationFullAddress:   eventRow.LocationFullAddress,
			LocationOnlineURL:     eventRow.LocationOnlineUrl,
			StartTime:             eventRow.StartTime,
			EndTime:               eventRow.EndTime,
			Price:                 eventRow.Price,
			GovernmentFundingKeys: eventRow.GovernmentFundingKeys,
			Status:                string(eventRow.Status),
			ParticipantLimit:      eventRow.ParticipantLimit,

			WaitlistLimit:   eventRow.WaitlistLimit, // from eventRow itself
			ContactEmail:    eventRow.ContactEmail,
			ContactPhone:    eventRow.ContactPhone,
			PublishedAt:     eventRow.PublishedAt,
			RegisteredCount: eventRow.RegisteredCount,
			WaitlistedCount: eventRow.WaitlistedCount,
			AttendedCount:   eventRow.AttendedCount,
		}

		if mediaItems, ok := mediaItemsByEventID[eventRow.ID]; ok {
			respItem.MediaItems = mediaItems
			// Populate MediaEventItem for backward compatibility if needed
			for _, mi := range mediaItems {
				if strings.HasPrefix(mi.FileType, "image/") {
					respItem.MediaEventItem = append(respItem.MediaEventItem, mi.FilePath)
				}
			}
		}
		if tags, ok := tagsByEventID[eventRow.ID]; ok {
			respItem.Tags = tags
		}

		// Populate current user registration status if available
		if userStatus, ok := userRegStatuses[eventRow.ID]; ok {
			respItem.CurrentUserRegistrationStatus = userStatus.RegistrationStatus
			respItem.CurrentUserRegistrationID = userStatus.RegistrationID
			respItem.CurrentUserVolunteerStatus = userStatus.VolunteerStatus // Assuming VolunteerStatus is also fetched
			respItem.CurrentUserVolunteerApplicationID = userStatus.VolunteerApplicationID
		}

		// Fetch required verification type keys for the event
		verificationKeys, err := s.queries.ListRequiredVerificationTypesForEvent(ctx, eventRow.ID)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Str("eventID", eventRow.ID.String()).Msg("Failed to list required verification types for event")
		} else {
			respItem.RequiredVerificationTypeKeys = verificationKeys
		}

		respEvents = append(respEvents, respItem)
	}

	// Get total count for pagination - Using the simplified Count query
	countParams := db.CountPublicEventsParams{
		Statuses:                 queryStatuses,
		StartDate:                filterParams.StartDate,  // Direct assignment
		EndDate:                  filterParams.EndDate,    // Direct assignment
		SearchTerm:               filterParams.SearchTerm, // Direct assignment
		TagIds:                   filterParams.TagIDs,
		GovernmentFundingKeys:    filterParams.GovernmentFundingKeys,
		EventVerificationTypeKey: filterParams.EventVerificationTypeKey, // Direct assignment
		OrganizationID:           filterParams.OrganizationID,           // Direct assignment
		OrganizationId2:          filterParams.OrganizationID2,          // Added for second org filter, direct assignment
	}

	totalCount, err := s.queries.CountPublicEvents(ctx, countParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Interface("filters", filterParams).Msg("Failed to count public events")
		// Returning respEvents which might be partially populated, and 0 for count, or handle error differently
		return respEvents, 0, fmt.Errorf("failed to count public events: %w", err) // Return error
	}

	return respEvents, totalCount, nil
}

// batchFetchMediaItemsForEvents retrieves media items for a list of event IDs and groups them.
func (s *eventService) batchFetchMediaItemsForEvents(ctx context.Context, eventIDs []uuid.UUID) (map[uuid.UUID][]payloads.MediaItemResponse, error) {
	if len(eventIDs) == 0 {
		return make(map[uuid.UUID][]payloads.MediaItemResponse), nil
	}
	dbMediaItems, err := s.queries.ListMediaItemsByEventIDs(ctx, eventIDs)
	if err != nil {
		return nil, fmt.Errorf("batchFetchMediaItemsForEvents: failed to list media items: %w", err)
	}

	mediaByEventID := make(map[uuid.UUID][]payloads.MediaItemResponse)
	for _, item := range dbMediaItems {
		mediaByEventID[item.EventID] = append(mediaByEventID[item.EventID], payloads.MediaItemResponse{
			ID:         item.ID,
			FileName:   item.FileName,
			FilePath:   item.FilePath,
			FileType:   item.FileType,
			FileSize:   item.FileSize,
			UploadedAt: item.UploadedAt,
			IsBanner:   item.IsBanner, // Populate IsBanner
		})
	}
	return mediaByEventID, nil
}

// batchFetchTagsForEvents retrieves tags for a list of event IDs and groups them.
func (s *eventService) batchFetchTagsForEvents(ctx context.Context, eventIDs []uuid.UUID) (map[uuid.UUID][]payloads.TagResponse, error) {
	dbTags, err := s.queries.ListTagsByEventIDs(ctx, eventIDs)
	if err != nil {
		return nil, fmt.Errorf("batchFetchTagsForEvents: failed to list tags by event IDs: %w", err)
	}

	tagsByEventID := make(map[uuid.UUID][]payloads.TagResponse)
	for _, tag := range dbTags {
		tagsByEventID[tag.EventID] = append(tagsByEventID[tag.EventID], payloads.TagResponse{
			ID:                 tag.ID,
			NameEn:             tag.NameEn,
			NameZhHk:           tag.NameZhHk,
			NameZhCn:           tag.NameZhCn,
			DescriptionEn:      tag.DescriptionEn,
			DescriptionZhHk:    tag.DescriptionZhHk,
			DescriptionZhCn:    tag.DescriptionZhCn,
			IsGloballyApproved: tag.IsGloballyApproved,
		})
	}
	return tagsByEventID, nil
}

// batchFetchUserEventRegistrationStatus retrieves registration status for a user across multiple events.
func (s *eventService) batchFetchUserEventRegistrationStatus(ctx context.Context, userID uuid.UUID, eventIDs []uuid.UUID) (map[uuid.UUID]*payloads.UserEventStatusInfo, error) {
	if len(eventIDs) == 0 || userID == uuid.Nil {
		return make(map[uuid.UUID]*payloads.UserEventStatusInfo), nil
	}

	dbStatuses, err := s.queries.ListUserRegistrationsForEvents(ctx, db.ListUserRegistrationsForEventsParams{
		UserID:   userID,
		EventIds: eventIDs, // Ensure this field name matches the generated Params struct field name
	})
	if err != nil {
		return nil, fmt.Errorf("batchFetchUserEventRegistrationStatus: failed to list user registrations for events: %w", err)
	}

	statusesByEventID := make(map[uuid.UUID]*payloads.UserEventStatusInfo)
	for _, row := range dbStatuses {
		if row.EventID == nil { // EventID is *uuid.UUID, check for nil
			continue
		}
		currentEventID := *row.EventID
		info := &payloads.UserEventStatusInfo{}

		if row.RegistrationID != nil { // RegistrationID is *uuid.UUID
			info.RegistrationID = row.RegistrationID
			if row.RegistrationStatus.Valid { // RegistrationStatus is NullEventRegistrationStatusType
				regStatusStr := string(row.RegistrationStatus.EventRegistrationStatusType)
				info.RegistrationStatus = &regStatusStr
			}
		}

		if row.VolunteerApplicationID != nil { // VolunteerApplicationID is *uuid.UUID
			info.VolunteerApplicationID = row.VolunteerApplicationID
			if row.VolunteerStatus.Valid { // VolunteerStatus is NullApplicationStatusEnum
				volStatusStr := string(row.VolunteerStatus.ApplicationStatusEnum)
				info.VolunteerStatus = &volStatusStr
			}
		}

		if info.RegistrationID != nil || info.VolunteerApplicationID != nil {
			statusesByEventID[currentEventID] = info
		}
	}
	return statusesByEventID, nil
}

// PromoteFromWaitlist attempts to promote users from the waitlist to registered status.
// It promotes up to 'slotsToFill' users, one by one, in a transaction.
// Returns a list of successfully promoted user registrations or an error.
func (s *eventService) PromoteFromWaitlist(ctx context.Context, eventID uuid.UUID, slotsToFill int) ([]payloads.EventRegistrationResponse, error) {
	if slotsToFill <= 0 {
		return []payloads.EventRegistrationResponse{}, nil // Nothing to do
	}

	// First, get event details, especially RequiresApprovalForRegistration
	eventDetails, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("event not found with ID %s: %w", eventID, err)
		}
		return nil, fmt.Errorf("failed to get event details for promotion: %w", err)
	}

	var promotedRegistrations []payloads.EventRegistrationResponse
	var lastErr error

	for i := 0; i < slotsToFill; i++ {
		tx, err := s.pool.Begin(ctx)
		if err != nil {
			return promotedRegistrations, fmt.Errorf("failed to begin transaction for promotion attempt %d: %w", i+1, err)
		}
		qtx := s.queries.WithTx(tx)

		// Corrected call: Pass eventID directly as the query only takes one parameter.
		// The 'waitlisted' status is part of the query itself.
		oldestWaitlistedReg, err := qtx.GetOldestWaitlistedRegistration(ctx, eventID)
		if err != nil {
			_ = tx.Rollback(ctx)
			if errors.Is(err, pgx.ErrNoRows) {
				lastErr = errors.New("no more users on waitlist")
				break // No more users to promote
			}
			lastErr = fmt.Errorf("failed to get oldest waitlisted registration on attempt %d: %w", i+1, err)
			continue // Try next slot if this one failed transactionally, or break if a systemic error
		}

		newStatus := db.EventRegistrationStatusTypeRegistered
		if eventDetails.RequiresApprovalForRegistration {
			newStatus = db.EventRegistrationStatusTypePendingApproval
		}

		adminNotes := "Promoted from waitlist."
		// Corrected field name: AdminNotes based on the sqlc.narg(admin_notes) in the query.
		updatedRegRow, err := qtx.UpdateEventRegistrationStatus(ctx, db.UpdateEventRegistrationStatusParams{
			ID:                       oldestWaitlistedReg.ID,
			Status:                   newStatus,
			AdminNotesOnRegistration: &adminNotes, // Corrected field name, sqlc.narg needs a pointer for COALESCE
			// WaitlistPriority will be set to NULL by the query logic when status is not 'waitlisted'
		})
		if err != nil {
			_ = tx.Rollback(ctx)
			lastErr = fmt.Errorf("failed to update registration status for user %s on attempt %d: %w", oldestWaitlistedReg.UserID, i+1, err)
			continue // Try next slot if this one failed transactionally, or break if a systemic error
		}

		updatedReg := db.EventRegistration{
			ID:                       updatedRegRow.ID,
			EventID:                  updatedRegRow.EventID,
			UserID:                   updatedRegRow.UserID,
			Status:                   updatedRegRow.Status,
			PaymentStatus:            updatedRegRow.PaymentStatus,
			RegistrationRole:         updatedRegRow.RegistrationRole,
			RegisteredAt:             updatedRegRow.RegisteredAt,
			AttendedAt:               updatedRegRow.AttendedAt,
			CancellationReasonByUser: updatedRegRow.CancellationReasonByUser, // Adjust if not on updatedRegRow
			AdminNotesOnRegistration: updatedRegRow.AdminNotesOnRegistration,
			WaitlistPriority:         updatedRegRow.WaitlistPriority,
			CheckInByUserID:          updatedRegRow.CheckInByUserID,
			CheckInMethod:            updatedRegRow.CheckInMethod,
			CreatedAt:                updatedRegRow.CreatedAt,
			UpdatedAt:                updatedRegRow.UpdatedAt,
		}

		if err = tx.Commit(ctx); err != nil {
			lastErr = fmt.Errorf("failed to commit transaction for promotion attempt %d for user %s: %w", i+1, oldestWaitlistedReg.UserID, err)
			continue
		}

		// Remove slotsFilled and successfulPromotions for now to simplify and focus on type errors
		lastErr = nil // Clear last error on success

		// --- Prepare Response Info (Post-Commit) ---
		promotedUser, userErr := qtx.GetUserByID(ctx, updatedReg.UserID)
		if userErr != nil {
			log.Ctx(ctx).Error().Err(userErr).Msgf("Failed to get user details for promoted user %s", updatedReg.UserID)
		}

		formattedResponse := s.formatEventRegistrationResponse(updatedReg, eventDetails, promotedUser)
		promotedRegistrations = append(promotedRegistrations, formattedResponse)

		// --- Send Notification for Waitlist Promotion ---
		if s.notificationService != nil {
			go func(userToNotify db.User, eventNotified db.Event) { // Capture loop variables
				payload := map[string]interface{}{
					"UserName":        userToNotify.DisplayName,
					"EventName":       eventNotified.Title,
					"EventDetailsURL": fmt.Sprintf("/events/%s", eventNotified.ID.String()),
				}
				// Use context.Background() for goroutine, as original ctx might be cancelled
				if err := s.notificationService.SendToUser(context.Background(), userToNotify.ID, NotificationTypeEventRegistrationFromWaitlist, payload); err != nil {
					log.Error().Err(err).Str("userID", userToNotify.ID.String()).Str("eventID", eventNotified.ID.String()).Msg("Failed to send event registration from waitlist notification")
					// Log error, but do not stop other promotions
				}
			}(promotedUser, eventDetails) // Pass copies of promotedUser and eventDetails to the goroutine
		} else {
			log.Warn().Str("eventID", eventID.String()).Msg("NotificationService is nil, cannot send waitlist promotion notification.")
		}

		// --- Send Notification via Job Queue (existing logic, can be kept or reviewed if redundant) ---
		if s.jobService != nil {
			notifyPayload := JobPayloadNotifyEvent{
				UserID:  updatedReg.UserID,
				EventID: eventDetails.ID,
			}
			_, enqueueErr := s.jobService.EnqueueJob(context.Background(), JobTypeNotifyEventPromotion, notifyPayload, 0*time.Second, nil)
			if enqueueErr != nil {
				fmt.Printf("ERROR: Failed to enqueue event promotion notification job for userID %s, eventID %s: %v\n", updatedReg.UserID.String(), eventDetails.ID.String(), enqueueErr)
			}
		} else {
			fmt.Printf("WARN: JobService is nil in PromoteFromWaitlist, cannot enqueue notification job.\n")
		}
		// --- Old goroutine notification removed ---

	} // end loop

	if lastErr != nil && !errors.Is(lastErr, errors.New("no more users on waitlist")) {
		// If there were errors other than running out of waitlisted users
		return promotedRegistrations, fmt.Errorf("completed promotions with some errors: %w. See logs for details of each attempt", lastErr)
	}

	return promotedRegistrations, nil // Return successfully promoted ones, and nil error if only 'no more users' was encountered
}

// GetPublicEventByID implements EventService for fetching a slimmed-down public event view.
func (s *eventService) GetPublicEventByID(ctx context.Context, eventID uuid.UUID, currentUserID uuid.UUID) (payloads.PublicEventResponse, error) {
	// Fetches with organization name join and counts
	dbEventWithCounts, err := s.queries.GetEventDetailsWithCounts(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.PublicEventResponse{}, ErrEventNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to get event details with counts for public view")
		return payloads.PublicEventResponse{}, fmt.Errorf("failed to retrieve event for public view: %w", err)
	}

	resp := payloads.PublicEventResponse{
		ID:                  dbEventWithCounts.ID,
		OrganizationID:      dbEventWithCounts.OrganizationID,
		OrganizationName:    dbEventWithCounts.OrganizationName,
		Title:               dbEventWithCounts.Title,
		JsonContent:         dbEventWithCounts.DescriptionContent,
		LocationType:        string(dbEventWithCounts.LocationType),
		LocationFullAddress: dbEventWithCounts.LocationFullAddress,
		LocationOnlineURL:   dbEventWithCounts.LocationOnlineUrl,
		StartTime:           dbEventWithCounts.StartTime,
		EndTime:             dbEventWithCounts.EndTime,
		Status:              string(dbEventWithCounts.Status),
		ParticipantLimit:    dbEventWithCounts.ParticipantLimit,
		PublishedAt:         dbEventWithCounts.PublishedAt,
		ContactEmail:        dbEventWithCounts.ContactEmail,
		ContactPhone:        dbEventWithCounts.ContactPhone,
		// New Fields
		RegisteredCount: dbEventWithCounts.RegisteredCount,
		WaitlistedCount: dbEventWithCounts.WaitlistedCount,
		AttendedCount:   dbEventWithCounts.AttendedCount,
		WaitlistLimit:   dbEventWithCounts.WaitlistLimit,
	}

	// Price is *string
	if dbEventWithCounts.Price != nil {
		resp.Price = dbEventWithCounts.Price
	}

	// GovernmentFundingKeys
	resp.GovernmentFundingKeys = dbEventWithCounts.GovernmentFundingKeys

	// Fetch Required Verification Keys
	dbVerifications, verificationErr := s.queries.ListRequiredVerificationTypesForEvent(ctx, dbEventWithCounts.ID)
	if verificationErr == nil {
		resp.RequiredVerificationTypeKeys = dbVerifications
	} else {
		log.Ctx(ctx).Error().Err(verificationErr).Str("eventID", dbEventWithCounts.ID.String()).Msg("Failed to list required verifications for public event")
	}

	// Fetch and map Tags
	dbTags, err := s.queries.ListTagsForEvent(ctx, dbEventWithCounts.ID)
	if err == nil {
		resp.Tags = make([]payloads.TagResponse, len(dbTags))
		for i, t := range dbTags {
			resp.Tags[i] = toEventTagResponse(t)
		}
	} else {
		log.Ctx(ctx).Error().Err(err).Str("eventID", dbEventWithCounts.ID.String()).Msg("Failed to list tags for public event")
	}

	// Fetch and map Event Media Items (formerly Banner Image URLs)
	dbMediaItems, mediaErr := s.queries.ListEventMediaItems(ctx, dbEventWithCounts.ID) // Updated to ListEventMediaItems
	if mediaErr == nil {
		resp.MediaItems = make([]payloads.MediaItemResponse, len(dbMediaItems)) // Assuming a new MediaItemResponse in payloads
		for i, m := range dbMediaItems {
			resp.MediaItems[i] = payloads.MediaItemResponse{
				ID:         m.ID,
				FileName:   m.FileName,
				FilePath:   m.FilePath, // This should be the web-accessible path
				FileType:   m.FileType,
				FileSize:   m.FileSize,
				UploadedAt: m.UploadedAt,
				IsBanner:   m.IsBanner, // Populate IsBanner
			}
		}
		// For backward compatibility or simple banner display, we can still populate BannerImageUrls
		// This part might be removed if BannerImageUrls is fully deprecated from EventResponse
		resp.MediaEventItem = make([]string, 0, len(dbMediaItems))
		for _, m := range dbMediaItems {
			// Heuristic: if it's an image, add to banner URLs. This logic might need refinement.
			if strings.HasPrefix(m.FileType, "image/") {
				resp.MediaEventItem = append(resp.MediaEventItem, m.FilePath) // Ensure FilePath is web-accessible URL
			}
		}
	} else {
		log.Ctx(ctx).Error().Err(mediaErr).Str("eventID", dbEventWithCounts.ID.String()).Msg("Failed to list media items for public event")
	}

	// Populate current user's registration status if a user is provided
	if currentUserID != uuid.Nil {
		reg, regErr := s.queries.GetEventRegistrationByUserAndEvent(ctx, db.GetEventRegistrationByUserAndEventParams{
			UserID:  currentUserID,
			EventID: dbEventWithCounts.ID,
		})
		if regErr == nil {
			statusStr := string(reg.Status)
			resp.CurrentUserRegistrationStatus = &statusStr
			resp.CurrentUserRegistrationID = &reg.ID
		} else if !errors.Is(regErr, pgx.ErrNoRows) { // Corrected: use regErr for logging
			log.Ctx(ctx).Error().Err(regErr).Str("eventID", dbEventWithCounts.ID.String()).Str("userID", currentUserID.String()).Msg("Failed to get user registration status for public event list item")
		}
	}

	return resp, nil
}

// ListPopularEvents retrieves a limited list of popular events.
func (s *eventService) ListPopularEvents(ctx context.Context, limit int) ([]payloads.PopularEvent, error) {
	if limit <= 0 {
		limit = 5 // Default limit
	}
	dbEvents, err := s.queries.ListPopularEvents(ctx, int32(limit))
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return []payloads.PopularEvent{}, nil
		}
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list popular events from DB")
		return nil, fmt.Errorf("failed to retrieve popular events: %w", err)
	}

	respEvents := make([]payloads.PopularEvent, 0, len(dbEvents))
	for _, dbEvent := range dbEvents {
		respEvents = append(respEvents, payloads.PopularEvent{
			ID:        dbEvent.ID.String(),
			Title:     dbEvent.Title,
			StartTime: dbEvent.StartTime,
			Location:  dbEvent.LocationFullAddress,
		})
	}
	return respEvents, nil
}

// ListPopularEventsByOrganization retrieves a limited list of popular events for a specific organization.
func (s *eventService) ListPopularEventsByOrganization(ctx context.Context, orgID uuid.UUID, limit int) ([]payloads.PopularEvent, error) {
	// Validate limit
	if limit <= 0 {
		limit = DefaultPopularEventsLimit // Use a sensible default if limit is not valid
	}

	popularEvents, err := s.queries.ListPopularEventsByOrganization(ctx, db.ListPopularEventsByOrganizationParams{
		OrganizationID: orgID,
		Limit:          int32(limit),
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to list popular events by organization")
		return nil, fmt.Errorf("failed to list popular events by organization: %w", err)
	}

	// Convert to payloads.PopularEvent
	resp := make([]payloads.PopularEvent, len(popularEvents))
	for i, pe := range popularEvents {
		resp[i] = payloads.PopularEvent{
			ID:        pe.ID.String(),
			Title:     pe.Title,
			StartTime: pe.StartTime,
			Location:  pe.LocationFullAddress, // Mapped from pe.LocationFullAddress
			// OrganizationName and BannerImageURL can be added if needed by modifying PopularEvent payload and this mapping
		}
	}

	return resp, nil
}

// ListPendingReviewVolunteerApplicationsForEvent retrieves a paginated list of volunteer applications
// with a 'pending' status for a specific event, along with the total count of such applications.
func (s *eventService) ListPendingReviewVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID, pageID int32, pageSize int32) ([]db.ListPendingReviewEventVolunteerApplicationsForEventRow, int64, error) {
	if pageSize <= 0 {
		pageSize = DefaultPageSize
	}
	if pageSize > MaxPageSize {
		pageSize = MaxPageSize
	}
	if pageID <= 0 {
		pageID = 1
	}
	offset := (pageID - 1) * pageSize

	applications, err := s.queries.ListPendingReviewEventVolunteerApplicationsForEvent(ctx, db.ListPendingReviewEventVolunteerApplicationsForEventParams{
		EventID: eventID,
		Limit:   pageSize,
		Offset:  offset,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to list pending review volunteer applications for event")
		return nil, 0, fmt.Errorf("failed to list pending review volunteer applications for event: %w", err)
	}

	totalCount, err := s.queries.CountPendingReviewEventVolunteerApplicationsForEvent(ctx, eventID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to count pending review volunteer applications for event")
		return nil, 0, fmt.Errorf("failed to count pending review volunteer applications for event: %w", err)
	}

	return applications, totalCount, nil
}

// ListPendingReviewVolunteerApplicationsForOrganization retrieves a paginated list of volunteer applications
// with a 'pending' status for events within a specific organization, along with the total count.
func (s *eventService) ListPendingReviewVolunteerApplicationsForOrganization(ctx context.Context, orgID uuid.UUID, pageID int32, pageSize int32) ([]db.ListPendingReviewEventVolunteerApplicationsForOrganizationRow, int64, error) {
	if pageSize <= 0 {
		pageSize = DefaultPageSize
	}
	if pageSize > MaxPageSize {
		pageSize = MaxPageSize
	}
	if pageID <= 0 {
		pageID = 1
	}
	offset := (pageID - 1) * pageSize

	applications, err := s.queries.ListPendingReviewEventVolunteerApplicationsForOrganization(ctx, db.ListPendingReviewEventVolunteerApplicationsForOrganizationParams{
		OrganizationID: orgID,
		Limit:          pageSize,
		Offset:         offset,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to list pending review volunteer applications for organization")
		return nil, 0, fmt.Errorf("failed to list pending review volunteer applications for organization: %w", err)
	}

	totalCount, err := s.queries.CountPendingReviewEventVolunteerApplicationsForOrganization(ctx, orgID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to count pending review volunteer applications for organization")
		return nil, 0, fmt.Errorf("failed to count pending review volunteer applications for organization: %w", err)
	}

	return applications, totalCount, nil
}

// ListAllPendingReviewVolunteerApplications retrieves a paginated list of all volunteer applications
// with a 'pending' status across all events and organizations, along with the total count.
// This is typically an admin-level function.
func (s *eventService) ListAllPendingReviewVolunteerApplications(ctx context.Context, pageID int32, pageSize int32) ([]db.ListAllPendingReviewEventVolunteerApplicationsRow, int64, error) {
	if pageSize <= 0 {
		pageSize = DefaultPageSize
	}
	if pageSize > MaxPageSize {
		pageSize = MaxPageSize
	}
	if pageID <= 0 {
		pageID = 1
	}
	offset := (pageID - 1) * pageSize

	applications, err := s.queries.ListAllPendingReviewEventVolunteerApplications(ctx, db.ListAllPendingReviewEventVolunteerApplicationsParams{
		Limit:  pageSize,
		Offset: offset,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list all pending review volunteer applications")
		return nil, 0, fmt.Errorf("failed to list all pending review volunteer applications: %w", err)
	}

	totalCount, err := s.queries.CountAllPendingReviewEventVolunteerApplications(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to count all pending review volunteer applications")
		return nil, 0, fmt.Errorf("failed to count all pending review volunteer applications: %w", err)
	}

	return applications, totalCount, nil
}

// ListOrgEventVolunteerApplications retrieves all event volunteer applications for a given organization, with optional status and pagination.
func (s *eventService) ListOrgEventVolunteerApplications(ctx context.Context, orgID uuid.UUID, status *string, pageParams payloads.PageRequest) ([]payloads.EventVolunteerApplicationResponse, int64, error) {
	logger := log.Ctx(ctx).With().Str("org_id", orgID.String()).Logger()
	if status != nil {
		logger = logger.With().Str("status", *status).Logger()
	}
	logger.Debug().Msg("Listing organization event volunteer applications")

	var sqlcStatusFilter db.NullApplicationStatusEnum
	if status != nil && *status != "" { // If any status is provided
		parsedStatus := db.ApplicationStatusEnum(*status)
		if !parsedStatus.Valid() {
			// If parsing fails (invalid status string), log it and treat as no filter or return error
			logger.Warn().Str("provided_status", *status).Msg("Invalid status value provided for filtering organization event volunteer applications. No status filter will be applied.")
			sqlcStatusFilter.Valid = false // Or return an error: return nil, 0, fmt.Errorf("invalid status value: %s", *status)
		} else {
			sqlcStatusFilter.ApplicationStatusEnum = parsedStatus
			sqlcStatusFilter.Valid = true
		}
	} else {
		sqlcStatusFilter.Valid = false // No status filter if API param is empty
	}

	limit := int32(DefaultPageSize)
	if pageParams.Limit > 0 {
		limit = int32(pageParams.Limit)
	}
	offset := int32(pageParams.Offset)

	// Get total count for pagination
	totalCount, err := s.queries.CountOrgEventVolunteerApplicationsWithFilters(ctx, db.CountOrgEventVolunteerApplicationsWithFiltersParams{
		OrganizationID: orgID,
		Status:         sqlcStatusFilter, // Corrected type
	})
	if err != nil {
		logger.Error().Err(err).Msg("Error counting organization event volunteer applications")
		// Don't fail the whole request if count fails, but log and proceed. Pagination might be inaccurate.
		totalCount = 0 // Or handle more gracefully, perhaps by not returning paginated results
	}

	dbAppRows, err := s.queries.ListOrgEventVolunteerApplicationsWithFilters(ctx, db.ListOrgEventVolunteerApplicationsWithFiltersParams{
		OrganizationID: orgID,
		Status:         sqlcStatusFilter, // Corrected type
		Limit:          limit,            // Pass limit to the query
		Offset:         offset,           // Pass offset to the query
	})

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return []payloads.EventVolunteerApplicationResponse{}, 0, nil
		}
		logger.Error().Err(err).Msg("Error listing organization event volunteer applications")
		return nil, 0, err
	}

	var appResponses []payloads.EventVolunteerApplicationResponse
	for _, row := range dbAppRows { // Iterate over dbAppRows (which are of type ListOrgEventVolunteerApplicationsWithFiltersRow)
		app := db.EventVolunteerApplication{
			ID:                     row.ID,
			EventID:                row.EventID,
			UserID:                 row.UserID,
			OrganizationID:         row.OrganizationID,
			Status:                 row.Status,
			ApplicationNotesByUser: row.ApplicationNotesByUser,
			AdminReviewNotes:       row.AdminReviewNotes,
			AppliedAt:              row.AppliedAt,
			ReviewedAt:             row.ReviewedAt,
			ReviewedByUserID:       row.ReviewedByUserID,
			CreatedAt:              row.CreatedAt,
			UpdatedAt:              row.UpdatedAt,
		}
		respItem := payloads.EventVolunteerApplicationResponse{
			EventVolunteerApplication: app,
			EventTitle:                row.EventTitle,
			OrganizationName:          row.OrganizationName,
			UserDisplayName:           row.ApplicantDisplayName,
			UserEmail:                 row.ApplicantEmail,
			UserPhone:                 row.ApplicantPhoneNumber,
			ReviewerDisplayName:       row.ReviewerDisplayName,
		}
		// EventStartTime is missing from ListOrgEventVolunteerApplicationsWithFiltersRow, if needed, add to SQL and mapping.
		appResponses = append(appResponses, respItem)
	}

	// Manual pagination if SQL doesn't support it yet -- REMOVED as SQL now supports it
	// totalCount := int64(len(appResponses))
	// start := int(offset)
	// end := start + int(limit)
	// if start > len(appResponses) {
	// 	appResponses = []payloads.EventVolunteerApplicationResponse{}
	// } else if end > len(appResponses) {
	// 	appResponses = appResponses[start:]
	// } else {
	// 	appResponses = appResponses[start:end]
	// }

	return appResponses, totalCount, nil
}

// GetEventStatistics retrieves statistics about events, including counts per category and top N events by participant count.
func (s *eventService) GetEventStatistics(ctx context.Context, params payloads.EventStatisticsRequest) (*payloads.EventStatisticsResponse, error) {
	var response payloads.EventStatisticsResponse
	var err error

	// Handle default date ranges
	startDate := params.StartDate
	endDate := params.EndDate

	if startDate == nil {
		// Default to 6 months ago from today
		sixMonthsAgo := time.Now().AddDate(0, -6, 0)
		// Consider setting to start of day for sixMonthsAgo for full day coverage
		// defaultStartDate := time.Date(sixMonthsAgo.Year(), sixMonthsAgo.Month(), sixMonthsAgo.Day(), 0, 0, 0, 0, sixMonthsAgo.Location())
		// startDate = &defaultStartDate
		startDate = &sixMonthsAgo // Keeping original start of day logic for 6 months ago for now
		log.Ctx(ctx).Debug().Time("defaultStartDate", *startDate).Msg("Defaulting StartDate for event stats")
	}
	if endDate == nil {
		// Default to end of the current day for EndDate
		now := time.Now()
		defaultEndDate := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
		endDate = &defaultEndDate
		log.Ctx(ctx).Debug().Time("defaultEndDate", *endDate).Msg("Defaulting EndDate for event stats to end of current day")
	}

	// Handle default limit for Top N events
	limit := DefaultPopularEventsLimit // Using existing constant for now, can be specific like DefaultTopEventsLimit
	if params.Limit != nil {
		limit = *params.Limit
	}
	if limit <= 0 {
		limit = 10 // Fallback default if provided limit is invalid
	}

	// Get event category stats
	dbCategoryStats, err := s.queries.GetEventCategoryStats(ctx, db.GetEventCategoryStatsParams{
		StartDate: startDate,
		EndDate:   endDate,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get event category statistics from DB")
		return nil, fmt.Errorf("failed to get event category statistics: %w", err)
	}

	categoryStats := make([]payloads.EventCategoryStat, len(dbCategoryStats))
	for i, stat := range dbCategoryStats {
		categoryStats[i] = payloads.EventCategoryStat{
			CategoryNameEn:     stat.NameEn,
			CategoryNameZhHk:   stat.NameZhHk,
			CategoryNameZhCn:   stat.NameZhCn,
			TotalEvents:        stat.TotalEvents,
			TotalRegistrations: stat.TotalRegistrations,
			TotalAttendees:     stat.TotalAttendees,
		}
	}

	response.CategoryStats = categoryStats

	// Get top events by participant count
	dbTopEvents, err := s.queries.GetTopEventsByParticipantCount(ctx, db.GetTopEventsByParticipantCountParams{
		StartDate: startDate,
		EndDate:   endDate,
		Lim:       int32(limit),
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get top events by participant count from DB")
		return nil, fmt.Errorf("failed to get top events by participant count: %w", err)
	}

	response.TopEvents = make([]payloads.TopEventItem, len(dbTopEvents))
	for i, event := range dbTopEvents {
		response.TopEvents[i] = payloads.TopEventItem{
			ID:           event.ID,
			Title:        event.Title,
			Participants: event.Participants, // This is already int64 from db.GetTopEventsByParticipantCountRow
		}
	}

	// If both queries failed, dbCategoryStats and dbTopEvents might be nil/empty
	// and errors logged. If one succeeded, we return partial data.
	// If both failed the second error (or a combined one) would be returned by previous logic.
	// For clarity: if GetEventCategoryStats fails, err is logged. If GetTopEventsByParticipantCount then also fails,
	// its error is returned, and since CategoryStats would be empty, that error would be the one bubbling up.
	if len(response.CategoryStats) == 0 && len(response.TopEvents) == 0 {
		// This case implies both queries failed and errors were logged.
		// The error from the second query (or a generic one if only first failed then second failed)
		// would have been returned if it was the only one failing.
		// If we reach here, it means the first query failed, logged, and then the second query also failed and returned an error.
		// If the first query failed and the second succeeded, we would not be in this block.
		// If the first succeeded and the second failed, the error was returned above.
		// This specific path suggests the first failed (error logged) and the second also failed (error also logged),
		// and we should return a general error that stats couldn't be fetched.
		// The previous logic already returns an error if the second query fails and the first was empty.
		// If the first failed, and second also fails, that error from the second (topEvents) query would be returned.
		// So, this explicit check might be redundant given the error handling logic flow.
		// For clarity: if GetEventCategoryStats fails, err is logged. If GetTopEventsByParticipantCount then also fails,
		// its error is returned, and since CategoryStats would be empty, that error would be the one bubbling up.
	}

	return &response, nil
}

// GetEventStatisticsForOrganization retrieves statistics for events within a specific organization.
func (s *eventService) GetEventStatisticsForOrganization(ctx context.Context, orgID uuid.UUID, params payloads.EventStatisticsRequest) (*payloads.EventStatisticsResponse, error) {
	var response payloads.EventStatisticsResponse
	response.CategoryStats = []payloads.EventCategoryStat{}
	response.TopEvents = []payloads.TopEventItem{}

	startDate := params.StartDate
	endDate := params.EndDate
	limit := 10 // Default limit
	if params.Limit != nil && *params.Limit > 0 {
		limit = *params.Limit
	}

	// Default date range to last 6 months if not provided
	if startDate == nil {
		sixMonthsAgo := time.Now().AddDate(0, -6, 0)
		// Consider setting to start of day for sixMonthsAgo for full day coverage
		// defaultStartDate := time.Date(sixMonthsAgo.Year(), sixMonthsAgo.Month(), sixMonthsAgo.Day(), 0, 0, 0, 0, sixMonthsAgo.Location())
		// startDate = &defaultStartDate
		startDate = &sixMonthsAgo // Keeping original start of day logic for 6 months ago for now
		log.Ctx(ctx).Debug().Time("defaultStartDate", *startDate).Msg("Defaulting StartDate for org event stats")
	}
	if endDate == nil {
		now := time.Now()
		// Default to end of the current day for EndDate
		defaultEndDate := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
		endDate = &defaultEndDate
		log.Ctx(ctx).Debug().Time("defaultEndDate", *endDate).Msg("Defaulting EndDate for org event stats to end of current day")
	}

	// Get event category stats for organization
	dbCategoryStats, err := s.queries.GetOrganizationEventCategoryStats(ctx, db.GetOrganizationEventCategoryStatsParams{
		OrganizationID: orgID,
		StartDate:      startDate,
		EndDate:        endDate,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to get organization event category stats")
		// Do not return error yet, try to get top events
	} else {
		categoryStats := make([]payloads.EventCategoryStat, len(dbCategoryStats))
		for i, stat := range dbCategoryStats {
			categoryStats[i] = payloads.EventCategoryStat{
				CategoryNameEn:     stat.NameEn,
				CategoryNameZhHk:   stat.NameZhHk,
				CategoryNameZhCn:   stat.NameZhCn,
				TotalEvents:        stat.TotalEvents,
				TotalRegistrations: stat.TotalRegistrations,
				TotalAttendees:     stat.TotalAttendees,
			}
		}
		response.CategoryStats = categoryStats
	}

	// Get top events by participant count for organization
	dbTopEvents, err := s.queries.GetOrganizationTopEventsByParticipantCount(ctx, db.GetOrganizationTopEventsByParticipantCountParams{
		OrganizationID: orgID,
		StartDate:      startDate,
		EndDate:        endDate,
		Lim:            int32(limit),
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orgID", orgID.String()).Msg("Failed to get organization top events by participant count")
		// If category stats also failed, return error here
		if len(response.CategoryStats) == 0 {
			return nil, fmt.Errorf("failed to retrieve any event statistics for organization %s: %w", orgID.String(), err)
		}
	} else {
		response.TopEvents = make([]payloads.TopEventItem, len(dbTopEvents))
		for i, event := range dbTopEvents {
			response.TopEvents[i] = payloads.TopEventItem{
				ID:           event.ID,
				Title:        event.Title,
				Participants: event.Participants, // This is int64 from db.GetOrganizationTopEventsByParticipantCountRow
			}
		}
	}

	// If both queries failed, dbCategoryStats and dbTopEvents might be nil/empty
	// and errors logged. If one succeeded, we return partial data.
	// If both failed the second error (or a combined one) would be returned by previous logic.
	if len(response.CategoryStats) == 0 && len(response.TopEvents) == 0 {
		// This case implies both queries failed and errors were logged.
		// The error from the second query (or a generic one if only first failed then second failed)
		// would have been returned if it was the only one failing.
		// If we reach here, it means the first query failed, logged, and then the second query also failed and returned an error.
		// If the first query failed and the second succeeded, we would not be in this block.
		// If the first succeeded and the second failed, the error was returned above.
		// This specific path suggests the first failed (error logged) and the second also failed (error also logged),
		// and we should return a general error that stats couldn't be fetched.
		// The previous logic already returns an error if the second query fails and the first was empty.
		// If the first failed, and second also fails, that error from the second (topEvents) query would be returned.
		// So, this explicit check might be redundant given the error handling logic flow.
		// For clarity: if GetOrganizationEventCategoryStats fails, err is logged. If GetOrganizationTopEventsByParticipantCount then also fails,
		// its error is returned, and since CategoryStats would be empty, that error would be the one bubbling up.
	}

	return &response, nil
}

// CountVolunteerApplicationsForEvent counts all volunteer applications for a given event.
func (s *eventService) CountVolunteerApplicationsForEvent(ctx context.Context, eventID uuid.UUID) (int64, error) {
	count, err := s.queries.CountVolunteerApplicationsForEvent(ctx, eventID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to count volunteer applications for event")
		// Return 0 and the error, allowing the handler to decide on the HTTP response (e.g., 404 if event not found, 500 for other DB errors)
		return 0, fmt.Errorf("failed to count volunteer applications for event %s: %w", eventID.String(), err)
	}
	return count, nil
}

// SetBannerForEventMediaItem sets a specific media item as the banner for an event.
func (s *eventService) SetBannerForEventMediaItem(ctx context.Context, eventID uuid.UUID, mediaItemID uuid.UUID, orgID uuid.UUID, actingUserID uuid.UUID) (payloads.MediaItemResponse, error) {
	// Authorization: Check if actingUserID has permission to modify the event/media.
	// This might involve checking if the user is an admin of the organization owning the event.
	// Example: Check if actingUserID is an admin of orgID.
	// if !s.isUserAdminOfOrg(ctx, actingUserID, orgID) { // Replace with actual permission check
	// 	return payloads.MediaItemResponse{}, errors.New("user does not have permission to set banner for this event")
	// }

	// Validate event and media item belong to the specified organization
	mediaItem, err := s.queries.GetEventMediaItemByID(ctx, mediaItemID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.MediaItemResponse{}, ErrEventMediaItemNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("mediaItemID", mediaItemID.String()).Msg("Failed to get media item")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to get media item: %w", err)
	}

	if mediaItem.EventID != eventID {
		log.Ctx(ctx).Warn().Str("mediaItemID", mediaItemID.String()).Str("mediaItemEventID", mediaItem.EventID.String()).Str("pathEventID", eventID.String()).Msg("Media item does not belong to the event specified in path")
		return payloads.MediaItemResponse{}, ErrEventMediaItemNotFound // Or a more specific mismatch error
	}

	evt, err := s.queries.GetEventByID(ctx, eventID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return payloads.MediaItemResponse{}, ErrEventNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to get event for banner setting")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to get event: %w", err)
	}

	if evt.OrganizationID != orgID {
		log.Ctx(ctx).Warn().Str("eventID", eventID.String()).Str("eventOrgID", evt.OrganizationID.String()).Str("pathOrgID", orgID.String()).Msg("Event does not belong to the organization specified in path")
		return payloads.MediaItemResponse{}, ErrEventNotFound // Or a more specific mismatch error
	}

	tx, err := s.pool.Begin(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to begin transaction for setting event banner")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	qtx := s.queries.WithTx(tx)

	// Unset any existing banner for the event
	// Corrected: UnsetBannerForEventMediaItems returns only error
	if err := qtx.UnsetBannerForEventMediaItems(ctx, eventID); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("eventID", eventID.String()).Msg("Failed to unset existing banners before setting new one")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to clear existing banner: %w", err)
	}

	// Set the new banner
	updatedMediaItem, err := qtx.SetBannerForEventMediaItem(ctx, db.SetBannerForEventMediaItemParams{
		ID:      mediaItemID,
		EventID: eventID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// This can happen if the media item was deleted between the initial check and this update,
			// or if it doesn't actually belong to the event (though checked above, race condition possible)
			return payloads.MediaItemResponse{}, ErrEventMediaItemNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("mediaItemID", mediaItemID.String()).Str("eventID", eventID.String()).Msg("Failed to set banner for media item")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to set banner: %w", err)
	}

	if err := tx.Commit(ctx); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to commit transaction for setting event banner")
		return payloads.MediaItemResponse{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return payloads.MediaItemResponse{
		ID:         updatedMediaItem.ID,
		FileName:   updatedMediaItem.FileName,
		FilePath:   updatedMediaItem.FilePath,
		FileType:   updatedMediaItem.FileType,
		FileSize:   updatedMediaItem.FileSize,
		UploadedAt: updatedMediaItem.UploadedAt,
		IsBanner:   updatedMediaItem.IsBanner,
	}, nil
}

// GetEventVolunteerApplicationDetails retrieves details for a specific event volunteer application.
// It ensures the requesting user is the applicant and the application belongs to the specified org and event.
func (s *eventService) GetEventVolunteerApplicationDetails(ctx context.Context, requestingUserID uuid.UUID, orgID uuid.UUID, eventID uuid.UUID, appID uuid.UUID) (payloads.EventVolunteerApplicationResponse, error) {
	log.Ctx(ctx).Debug().
		Str("requestingUserID", requestingUserID.String()).
		Str("orgID", orgID.String()).
		Str("eventID", eventID.String()).
		Str("appID", appID.String()).
		Msg("Attempting to get event volunteer application details")

	app, err := s.queries.GetEventVolunteerApplicationByID(ctx, appID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("appID", appID.String()).Msg("Event volunteer application not found by ID")
			return payloads.EventVolunteerApplicationResponse{}, ErrVolunteerApplicationNotFound
		}
		log.Ctx(ctx).Error().Err(err).Str("appID", appID.String()).Msg("Failed to get event volunteer application by ID from DB")
		return payloads.EventVolunteerApplicationResponse{}, fmt.Errorf("database error retrieving application: %w", err)
	}

	// Authorization: Check if the requesting user is the applicant.
	// This check is removed to make the endpoint public.
	/*
		if app.UserID != requestingUserID {
			log.Ctx(ctx).Warn().
				Str("requestingUserID", requestingUserID.String()).
				Str("applicantUserID", app.UserID.String()).
				Str("appID", appID.String()).
				Msg("Forbidden: Requesting user is not the applicant for this volunteer application")
			return payloads.EventVolunteerApplicationResponse{}, ErrForbidden
		}
	*/

	// Validate that the application belongs to the expected event and organization.
	// This is an important sanity check, especially if the appID could somehow be guessed or manipulated.
	if app.EventID != eventID {
		log.Ctx(ctx).Error().
			Str("appEventID", app.EventID.String()).
			Str("expectedEventID", eventID.String()).
			Str("appID", appID.String()).
			Msg("Application event mismatch: The application does not belong to the specified event ID.")
		// Technically the application was found, but not for this event. Treat as not found for this specific request context.
		return payloads.EventVolunteerApplicationResponse{}, ErrVolunteerApplicationNotFound // Or ErrBadRequest
	}

	// Fetch associated event details
	dbEvent, err := s.queries.GetEventByID(ctx, app.EventID) // Using GetEventByID as it's a simpler direct fetch.
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("eventID", app.EventID.String()).Msg("Event not found for volunteer application")
			return payloads.EventVolunteerApplicationResponse{}, ErrEventNotFound // Should ideally not happen if FK constraints are good
		}
		log.Ctx(ctx).Error().Err(err).Str("eventID", app.EventID.String()).Msg("Failed to get event details for volunteer application")
		return payloads.EventVolunteerApplicationResponse{}, fmt.Errorf("database error retrieving event: %w", err)
	}

	// Validate that the event's organization matches the expected orgID from the path.
	if dbEvent.OrganizationID != orgID {
		log.Ctx(ctx).Error().
			Str("eventOrgID", dbEvent.OrganizationID.String()).
			Str("expectedOrgID", orgID.String()).
			Str("eventID", eventID.String()).
			Str("appID", appID.String()).
			Msg("Application organization mismatch: The event's organization does not match the specified organization ID.")
		return payloads.EventVolunteerApplicationResponse{}, ErrOrganizationNotFound // Or ErrBadRequest / ErrVolunteerApplicationNotFound
	}

	// Fetch associated user (applicant) details
	dbUser, err := s.queries.GetUserByID(ctx, app.UserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Str("userID", app.UserID.String()).Msg("User (applicant) not found for volunteer application")
			return payloads.EventVolunteerApplicationResponse{}, ErrUserNotFound // Should ideally not happen
		}
		log.Ctx(ctx).Error().Err(err).Str("userID", app.UserID.String()).Msg("Failed to get user details for volunteer application")
		return payloads.EventVolunteerApplicationResponse{}, fmt.Errorf("database error retrieving user: %w", err)
	}

	return s.formatEventVolunteerApplicationResponse(app, dbEvent, dbUser), nil
}

// GetUserEventVolunteerApplicationDetails retrieves details for a specific event volunteer application owned by the user.
func (s *eventService) GetUserEventVolunteerApplicationDetails(ctx context.Context, userID uuid.UUID, appID uuid.UUID) (payloads.EventVolunteerApplicationResponse, error) {
	log.Ctx(ctx).Debug().Str("user_id", userID.String()).Str("app_id", appID.String()).Msg("Fetching event volunteer application details for user")

	appRow, err := s.queries.GetUserEventVolunteerApplicationByID(ctx, db.GetUserEventVolunteerApplicationByIDParams{
		ID:     appID,
		UserID: userID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Err(err).Msg("Event volunteer application not found for user")
			return payloads.EventVolunteerApplicationResponse{}, ErrVolunteerApplicationNotFound
		}
		log.Ctx(ctx).Error().Err(err).Msg("Error fetching event volunteer application from DB")
		return payloads.EventVolunteerApplicationResponse{}, err
	}

	app := db.EventVolunteerApplication{
		ID:                     appRow.ID,
		EventID:                appRow.EventID,
		UserID:                 appRow.UserID,
		OrganizationID:         appRow.OrganizationID,
		ApplicationNotesByUser: appRow.ApplicationNotesByUser,
		AdminReviewNotes:       appRow.AdminReviewNotes,
		AppliedAt:              appRow.AppliedAt,
		ReviewedAt:             appRow.ReviewedAt,
		ReviewedByUserID:       appRow.ReviewedByUserID,
		CreatedAt:              appRow.CreatedAt,
		UpdatedAt:              appRow.UpdatedAt,
		Status:                 appRow.Status,
		AttendedAt:             appRow.AttendedAt,
	}

	var eventDescription *string
	if appRow.EventDescription != nil && len(appRow.EventDescription) > 0 {
		desc := string(appRow.EventDescription)
		eventDescription = &desc
	}

	var mediaItems []payloads.MediaItemResponse
	if appRow.MediaItems != nil {
		switch mediaData := appRow.MediaItems.(type) {
		case []byte: // Handles JSONB directly if SQLC maps it as raw bytes
			if err := json.Unmarshal(mediaData, &mediaItems); err != nil {
				log.Ctx(ctx).Error().Err(err).Str("event_id", appRow.EventID.String()).Msg("Failed to unmarshal media items (from []byte) for event volunteer application response")
			}
		case string: // Handles if SQLC maps JSON as a string
			if err := json.Unmarshal([]byte(mediaData), &mediaItems); err != nil {
				log.Ctx(ctx).Error().Err(err).Str("event_id", appRow.EventID.String()).Msg("Failed to unmarshal media items (from string) for event volunteer application response")
			}
		case []interface{}: // Correctly handle []interface{}
			// Convert []interface{} (slice of maps) to []payloads.MediaItemResponse
			// A common way is to re-marshal to JSON and then unmarshal to the target struct slice.
			// This is inefficient but often simplest to implement.
			jsonData, err := json.Marshal(mediaData)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("event_id", appRow.EventID.String()).Msg("Failed to re-marshal media items from []interface{}")
			} else {
				if err := json.Unmarshal(jsonData, &mediaItems); err != nil {
					log.Ctx(ctx).Error().Err(err).Str("event_id", appRow.EventID.String()).Msg("Failed to unmarshal media items from re-marshaled JSON")
				}
			}
		default:
			log.Ctx(ctx).Warn().Str("event_id", appRow.EventID.String()).Interface("media_items_type", fmt.Sprintf("%T", appRow.MediaItems)).Msg("Unexpected type for media_items, cannot unmarshal")
		}
	}

	eventOrgID := appRow.EventOrganizationID

	var orgName string
	if appRow.OrganizationName != nil {
		orgName = *appRow.OrganizationName
	}

	response := payloads.EventVolunteerApplicationResponse{
		EventVolunteerApplication: app,
		EventTitle:                appRow.EventTitle,
		EventStartTime:            appRow.EventStartTime,
		EventEndTime:              appRow.EventEndTime,
		EventDescription:          eventDescription,
		EventLocationType:         string(appRow.EventLocationType),
		EventLocationFullAddress:  appRow.EventLocationFullAddress,
		EventLocationOnlineURL:    appRow.EventLocationOnlineUrl,
		EventStatus:               string(appRow.EventStatus),
		EventOrganizationID:       &eventOrgID,
		OrganizationName:          orgName, // Corrected assignment
		EventPrice:                appRow.EventPrice,
		EventContactEmail:         appRow.EventContactEmail,
		EventContactPhone:         appRow.EventContactPhone,
		MediaItems:                mediaItems,
	}

	return response, nil
}
func (s *eventService) GetEventTag(ctx context.Context, tagID uuid.UUID) (payloads.TagResponse, error) {
	tag, err := s.queries.GetEventTag(ctx, tagID)
	if err != nil {
		return payloads.TagResponse{}, err
	}
	return toEventTagResponse(tag), nil
}

func (s *eventService) DeleteEventTag(ctx context.Context, tagID uuid.UUID) error {
	// Superadmin check
	// For simplicity, let's assume a function GetUserRole(ctx) exists that retrieves the user's role.
	// In a real implementation, this would come from the JWT claims in the context.
	// if GetUserRole(ctx) != "superadmin" {
	// 	return fmt.Errorf("permission denied: only superadmins can delete event tags")
	// }

	err := s.queries.DeleteEventTag(ctx, tagID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ErrEventTagNotFound
		}
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete event tag")
		return fmt.Errorf("failed to delete event tag: %w", err)
	}
	return nil
}

func (s *eventService) GetSpecificEventStatistics(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) (*payloads.SpecificEventStatisticsResponse, error) {
	// First, check if the user has permission to view stats for this event (is an org admin)
	isMember, err := s.queries.IsUserMemberOfOrganization(ctx, db.IsUserMemberOfOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrUserNotMemberOfOrg
		}
		log.Ctx(ctx).Error().Err(err).Msg("failed to check organization membership")
		return nil, fmt.Errorf("failed to check organization membership: %w", err)
	}
	if !isMember {
		return nil, ErrUserNotMemberOfOrg // User must be a member to see stats
	}

	resp := &payloads.SpecificEventStatisticsResponse{
		ParticipantsByAge:    []payloads.AgeGroup{},
		VolunteersByAge:      []payloads.AgeGroup{},
		ParticipantsByGender: []payloads.GenderGroup{},
		VolunteersByGender:   []payloads.GenderGroup{},
		ParticipantsByDate:   []payloads.DateGroup{},
		VolunteersByDate:     []payloads.DateGroup{},
	}

	// --- Goroutine setup ---
	// Using a WaitGroup to run queries concurrently and an error channel to collect errors.
	var wg sync.WaitGroup
	errChan := make(chan error, 7) // Buffer for the number of goroutines

	// 1. Get basic counts (participants, volunteers, max)
	wg.Add(1)
	go func() {
		defer wg.Done()
		stats, err := s.queries.GetEventStatistics(ctx, eventID)
		if err != nil && !errors.Is(err, pgx.ErrNoRows) {
			errChan <- fmt.Errorf("failed to get event statistics: %w", err)
			return
		}
		if err == nil {
			resp.ParticipantsCount = int(stats.ParticipantsCount)
			resp.VolunteersCount = int(stats.VolunteersCount)
			// The participant_limit can be null in the DB, so sqlc generates it as a pointer
			if stats.MaxParticipants != nil {
				resp.MaxParticipants = int(*stats.MaxParticipants)
			}
		}
	}()

	// 2. Get participants by age
	wg.Add(1)
	go func() {
		defer wg.Done()
		ageData, err := s.queries.GetEventParticipantsByAge(ctx, eventID)
		if err != nil {
			errChan <- fmt.Errorf("failed to get participants by age: %w", err)
			return
		}
		for _, row := range ageData {
			resp.ParticipantsByAge = append(resp.ParticipantsByAge, payloads.AgeGroup{
				AgeRange: row.AgeRange,
				Count:    int(row.Count),
			})
		}
	}()

	// 3. Get participants by gender
	wg.Add(1)
	go func() {
		defer wg.Done()
		genderData, err := s.queries.GetEventParticipantsByGender(ctx, eventID)
		if err != nil {
			errChan <- fmt.Errorf("failed to get participants by gender: %w", err)
			return
		}
		for _, row := range genderData {
			if genderStr, ok := row.Gender.(string); ok {
				resp.ParticipantsByGender = append(resp.ParticipantsByGender, payloads.GenderGroup{
					Gender: genderStr,
					Count:  int(row.Count),
				})
			}
		}
	}()

	// 4. Get volunteers by age
	wg.Add(1)
	go func() {
		defer wg.Done()
		ageData, err := s.queries.GetEventVolunteersByAge(ctx, eventID)
		if err != nil {
			errChan <- fmt.Errorf("failed to get volunteers by age: %w", err)
			return
		}
		for _, row := range ageData {
			resp.VolunteersByAge = append(resp.VolunteersByAge, payloads.AgeGroup{
				AgeRange: row.AgeRange,
				Count:    int(row.Count),
			})
		}
	}()

	// 5. Get volunteers by gender
	wg.Add(1)
	go func() {
		defer wg.Done()
		genderData, err := s.queries.GetEventVolunteersByGender(ctx, eventID)
		if err != nil {
			errChan <- fmt.Errorf("failed to get volunteers by gender: %w", err)
			return
		}
		for _, row := range genderData {
			if genderStr, ok := row.Gender.(string); ok {
				resp.VolunteersByGender = append(resp.VolunteersByGender, payloads.GenderGroup{
					Gender: genderStr,
					Count:  int(row.Count),
				})
			}
		}
	}()

	// 6. Get participants by date
	wg.Add(1)
	go func() {
		defer wg.Done()
		dateData, err := s.queries.GetEventParticipantsByDate(ctx, eventID)
		if err != nil {
			errChan <- fmt.Errorf("failed to get participants by date: %w", err)
			return
		}
		for _, row := range dateData {
			resp.ParticipantsByDate = append(resp.ParticipantsByDate, payloads.DateGroup{
				Date:  row.Date,
				Count: int(row.Count),
			})
		}
	}()

	// 7. Get volunteers by date
	wg.Add(1)
	go func() {
		defer wg.Done()
		dateData, err := s.queries.GetEventVolunteersByDate(ctx, eventID)
		if err != nil {
			errChan <- fmt.Errorf("failed to get volunteers by date: %w", err)
			return
		}
		for _, row := range dateData {
			resp.VolunteersByDate = append(resp.VolunteersByDate, payloads.DateGroup{
				Date:  row.Date,
				Count: int(row.Count),
			})
		}
	}()

	wg.Wait()
	close(errChan)

	// Check for any errors from the goroutines
	for err := range errChan {
		// Log the first error and return it
		log.Ctx(ctx).Error().Err(err).Msg("Error fetching event statistics")
		return nil, err
	}

	return resp, nil
}
