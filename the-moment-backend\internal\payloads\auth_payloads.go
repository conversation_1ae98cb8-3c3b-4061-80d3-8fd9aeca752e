package payloads

type StaffLoginVerifyPayload struct {
	State        string `json:"state" validate:"required"`
	Email        string `json:"email" validate:"required,email"`
	Password     string `json:"password" validate:"required,min=6"`
	CodeVerifier string `json:"code_verifier" validate:"required"`
}

type RefreshTokenPayload struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// LogoutRequestPayload defines the structure for the logout request.
type LogoutRequestPayload struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}
