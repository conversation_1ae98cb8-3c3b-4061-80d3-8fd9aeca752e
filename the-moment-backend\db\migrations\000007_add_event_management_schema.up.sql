-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Enum Types
CREATE TYPE event_status_type AS ENUM (
    'draft',
    'upcoming',
    'ongoing',
    'past',
    'cancelled'
);

CREATE TYPE event_location_type AS ENUM (
    'physical',
    'online',
    'hybrid'
);

CREATE TYPE event_media_type AS ENUM (
    'image',
    'video_link'
);

CREATE TYPE event_registration_status_type AS ENUM (
    'pending_approval',
    'registered',
    'waitlisted',
    'rejected_approval',
    'cancelled_by_user',
    'attended',
    'absent'
);

CREATE TYPE payment_status_type AS ENUM (
    'paid',
    'unpaid',
    'not_required',
    'refunded'
);

CREATE TYPE event_registration_role_type AS ENUM (
    'participant',
    'volunteer'
);

CREATE TYPE check_in_method_type AS ENUM (
    'qr_scan',
    'manual_check_in'
);

CREATE TYPE event_volunteer_application_status_type AS ENUM (
    'pending',
    'approved',
    'rejected'
);

-- Tables
CREATE TABLE "events" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "organization_id" UUID NOT NULL REFERENCES "organizations"("id") ON DELETE CASCADE,
    "title" VARCHAR(255) NOT NULL,
    "description_markdown" TEXT,
    "location_type" event_location_type NOT NULL,
    "location_address_line1" VARCHAR(255),
    "location_address_line2" VARCHAR(255),
    "location_city" VARCHAR(100),
    "location_state_province_region" VARCHAR(100),
    "location_postal_code" VARCHAR(20),
    "location_country" VARCHAR(100),
    "location_online_url" VARCHAR(2048),
    "start_time" TIMESTAMPTZ NOT NULL,
    "end_time" TIMESTAMPTZ NOT NULL,
    "is_government_funded" BOOLEAN NOT NULL DEFAULT FALSE,
    "status" event_status_type NOT NULL DEFAULT 'draft',
    "participant_limit" INTEGER,
    "waitlist_limit" INTEGER DEFAULT 0,
    "requires_approval_for_registration" BOOLEAN NOT NULL DEFAULT FALSE,
    "created_by_user_id" UUID NOT NULL REFERENCES "users"("id"),
    "published_at" TIMESTAMPTZ,
    "government_funding_keys" TEXT[],
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX ON "events" ("organization_id");
CREATE INDEX ON "events" ("status");
CREATE INDEX ON "events" ("start_time");
CREATE INDEX ON "events" ("created_by_user_id");
CREATE INDEX ON "events" ("location_city"); -- For filtering by city
CREATE INDEX ON "events" ("location_country"); -- For filtering by country
CREATE INDEX events_gin_government_funding_keys ON "events" USING GIN ("government_funding_keys");

CREATE TABLE "event_media_items" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "file_name" VARCHAR(255) NOT NULL,
    "file_path" VARCHAR(1024) NOT NULL UNIQUE,
    "file_type" VARCHAR(100) NOT NULL,
    "file_size" BIGINT NOT NULL,
    "uploaded_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX ON "event_media_items" ("event_id");

CREATE TABLE "event_tags" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "tag_name" VARCHAR(100) NOT NULL,
    "language_code" VARCHAR(10) NOT NULL, -- e.g., 'en', 'zh_HK', 'zh_CN'
    "description" TEXT,
    "created_by_user_id" UUID REFERENCES "users"("id"), -- Nullable if system-defined
    "is_globally_approved" BOOLEAN NOT NULL DEFAULT TRUE,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE ("tag_name", "language_code")
);

CREATE INDEX ON "event_tags" ("language_code");
CREATE INDEX ON "event_tags" ("created_by_user_id");


CREATE TABLE "event_event_tags" (
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "event_tag_id" UUID NOT NULL REFERENCES "event_tags"("id") ON DELETE CASCADE,
    PRIMARY KEY ("event_id", "event_tag_id")
);

CREATE TABLE "event_required_verification_types" (
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "verification_type_key" VARCHAR(50) NOT NULL, -- e.g., 'HK_ID_CARD', 'STUDENT_ID'
    PRIMARY KEY ("event_id", "verification_type_key")
);

CREATE TABLE "event_registrations" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
    "status" event_registration_status_type NOT NULL,
    "payment_status" payment_status_type NOT NULL DEFAULT 'not_required',
    "registration_role" event_registration_role_type NOT NULL DEFAULT 'participant',
    "registered_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "attended_at" TIMESTAMPTZ,
    "check_in_method" check_in_method_type,
    "check_in_by_user_id" UUID REFERENCES "users"("id"),
    "cancellation_reason_by_user" TEXT,
    "admin_notes_on_registration" TEXT,
    "waitlist_priority" TIMESTAMPTZ, -- Set when status becomes 'waitlisted'
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE ("event_id", "user_id")
);

CREATE INDEX ON "event_registrations" ("event_id");
CREATE INDEX ON "event_registrations" ("user_id");
CREATE INDEX ON "event_registrations" ("status");
CREATE INDEX ON "event_registrations" ("payment_status");
CREATE INDEX ON "event_registrations" ("registration_role");
CREATE INDEX ON "event_registrations" ("waitlist_priority") WHERE "status" = 'waitlisted';


CREATE TABLE "event_volunteer_applications" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
    "organization_id" UUID NOT NULL REFERENCES "organizations"("id") ON DELETE CASCADE,
    "status" event_volunteer_application_status_type NOT NULL,
    "application_notes_by_user" TEXT,
    "admin_review_notes" TEXT,
    "applied_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reviewed_at" TIMESTAMPTZ,
    "reviewed_by_user_id" UUID REFERENCES "users"("id"),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE ("event_id", "user_id")
);

CREATE INDEX ON "event_volunteer_applications" ("event_id");
CREATE INDEX ON "event_volunteer_applications" ("user_id");
CREATE INDEX ON "event_volunteer_applications" ("organization_id");
CREATE INDEX ON "event_volunteer_applications" ("status");
CREATE INDEX ON "event_volunteer_applications" ("reviewed_by_user_id");

-- Trigger function to update 'updated_at' columns
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to tables
CREATE TRIGGER set_timestamp_events
BEFORE UPDATE ON "events"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_event_tags
BEFORE UPDATE ON "event_tags"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_event_registrations
BEFORE UPDATE ON "event_registrations"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_event_volunteer_applications
BEFORE UPDATE ON "event_volunteer_applications"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Seed initial event tags with deterministic UUIDs
INSERT INTO "event_tags" ("id", "tag_name", "language_code", "description", "is_globally_approved", "created_by_user_id") VALUES
('e1e1e1e1-0001-4000-8000-000000000001', 'Exchange Group / Exchange Tour', 'en', 'Events related to exchange programs or tours.', TRUE, NULL),
('e1e1e1e1-1001-4000-8000-000000000001', '交流團', 'zh_HK', '交流團相關活動。', TRUE, NULL),
('e1e1e1e1-**************-000000000001', '交流团', 'zh_CN', '交流团相关活动。', TRUE, NULL),

('e1e1e1e1-0002-4000-8000-000000000002', 'Youth Activity', 'en', 'Activities specifically for youth.', TRUE, NULL),
('e1e1e1e1-1002-4000-8000-000000000002', '青少年活動', 'zh_HK', '青少年專屬活動。', TRUE, NULL),
('e1e1e1e1-**************-000000000002', '青少年活动', 'zh_CN', '青少年专属活动。', TRUE, NULL),

('e1e1e1e1-0003-4000-8000-000000000003', 'Senior Activity', 'en', 'Activities designed for seniors.', TRUE, NULL),
('e1e1e1e1-1003-4000-8000-000000000003', '長者活動', 'zh_HK', '長者專屬活動。', TRUE, NULL),
('e1e1e1e1-2003-4000-8000-000000000003', '长者活动', 'zh_CN', '长者专属活动。', TRUE, NULL),

('e1e1e1e1-0004-4000-8000-000000000004', 'Funding Application / Grant', 'en', 'Events or information related to funding applications or grants.', TRUE, NULL),
('e1e1e1e1-1004-4000-8000-000000000004', '資助申請', 'zh_HK', '資助申請或撥款相關活動資訊。', TRUE, NULL),
('e1e1e1e1-2004-4000-8000-000000000004', '资助申请', 'zh_CN', '资助申请或拨款相关活动资讯。', TRUE, NULL),

('e1e1e1e1-0005-4000-8000-000000000005', 'Volunteer Service', 'en', 'Opportunities for volunteer service.', TRUE, NULL),
('e1e1e1e1-1005-4000-8000-000000000005', '義務服務', 'zh_HK', '義務服務機會。', TRUE, NULL),
('e1e1e1e1-2005-4000-8000-000000000005', '义务服务', 'zh_CN', '义务服务机会。', TRUE, NULL),

('e1e1e1e1-0006-4000-8000-000000000006', 'Material Distribution', 'en', 'Events involving the distribution of materials or goods.', TRUE, NULL),
('e1e1e1e1-1006-4000-8000-000000000006', '物資派貨', 'zh_HK', '物資派送或分發活動。', TRUE, NULL),
('e1e1e1e1-2006-4000-8000-000000000006', '物资派送 / 物资分发', 'zh_CN', '物资派送或分发活动。', TRUE, NULL),

('e1e1e1e1-0007-4000-8000-000000000007', 'Government Funding Scheme', 'en', 'Events related to government funding schemes.', TRUE, NULL),
('e1e1e1e1-1007-4000-8000-000000000007', '政府資助計劃', 'zh_HK', '政府資助計劃相關活動。', TRUE, NULL),
('e1e1e1e1-2007-4000-8000-000000000007', '政府资助计划', 'zh_CN', '政府资助计划相关活动。', TRUE, NULL); 