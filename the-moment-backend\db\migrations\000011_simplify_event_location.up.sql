-- Add the new consolidated location address field
ALTER TABLE events ADD COLUMN location_full_address TEXT;

COMMENT ON COLUMN events.location_full_address IS 'Full address details for physical or hybrid events.';

-- Optionally, populate the new field from old fields if data exists (best effort)
-- This might need adjustment based on actual data and desired format.
-- UPDATE events
-- SET location_full_address = concat_ws(', ', 
--     NU<PERSON><PERSON>(location_address_line1, ''), 
--     NU<PERSON><PERSON>(location_address_line2, ''), 
--     NULLIF(location_city, ''), 
--     NULLIF(location_state_province_region, ''), 
--     NULLIF(location_postal_code, ''), 
--     NULLIF(location_country, '')
-- )
-- WHERE location_type = 'physical' OR location_type = 'hybrid'; 
-- Consider adding this update logic manually if needed after checking data.

-- Remove the old individual location fields
ALTER TABLE events
DROP COLUMN IF EXISTS location_address_line1,
DROP COLUMN IF EXISTS location_address_line2,
DROP COLUMN IF EXISTS location_city,
DROP COLUMN IF EXISTS location_state_province_region,
DROP COLUMN IF EXISTS location_postal_code,
DROP COLUMN IF EXISTS location_country; 