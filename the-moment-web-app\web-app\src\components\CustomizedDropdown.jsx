import React, { useState } from 'react';
import { Popover, Checkbox, Button, Space, Divider } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const CustomizedDropdown = ({ allColumns, checkedList, setCheckedList, disabled, fixedColumns = [] }) => {
    const [popoverOpen, setPopoverOpen] = useState(false);
    const { t } = useTranslation();

    // Filter out columns that should not be configurable
    const displayableColumns = allColumns.filter(column => 
        !fixedColumns.includes(column.key)
    );

    const displayableColumnKeys = displayableColumns.map(col => col.key);
    const isAllChecked = displayableColumnKeys.every(key => checkedList.includes(key));
    const isIndeterminate = displayableColumnKeys.some(key => checkedList.includes(key)) && !isAllChecked;

    const handleCheckAll = (e) => {
        if (e.target.checked) {
            // Add all displayable columns to checkedList, keeping existing fixed columns
            const newCheckedList = [...new Set([...checkedList, ...displayableColumnKeys])];
            setCheckedList(newCheckedList);
        } else {
            // Remove all displayable columns from checkedList, keeping fixed columns
            const newCheckedList = checkedList.filter(key => !displayableColumnKeys.includes(key));
            setCheckedList(newCheckedList);
        }
    };

    const handleColumnChange = (columnKey, checked) => {
        if (checked) {
            setCheckedList([...checkedList, columnKey]);
        } else {
            setCheckedList(checkedList.filter(key => key !== columnKey));
        }
    };

    const getColumnTranslationKey = (columnKey) => {
        // Try different translation key patterns
        const possibleKeys = [
            `adminEvents.singleEventReport.attendance.columns.${columnKey}`,
            `userManagement.columns.${columnKey}`,
            `common.columns.${columnKey}`
        ];
        
        for (const key of possibleKeys) {
            const translation = t(key);
            if (translation !== key) {
                return translation;
            }
        }
        
        // Fallback to the column title if available
        const column = displayableColumns.find(col => col.key === columnKey);
        return column?.title || columnKey;
    };

    const popoverContent = (
        <div style={{ width: 200, padding: '8px 0' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
                <Checkbox
                    checked={isAllChecked}
                    indeterminate={isIndeterminate}
                    onChange={handleCheckAll}
                    style={{ fontWeight: 'bold' }}
                >
                    {t('common.checkAll') || 'Check All'}
                </Checkbox>
                <Divider style={{ margin: '8px 0' }} />
                <Checkbox.Group 
                    value={checkedList.filter(key => displayableColumnKeys.includes(key))}
                    style={{ width: '100%' }}
                >
                    <Space direction="vertical" style={{ width: '100%' }}>
                    {displayableColumns.map(column => (
                        <Checkbox
                            key={column.key}
                                value={column.key}
                                onChange={(e) => handleColumnChange(column.key, e.target.checked)}
                                style={{ width: '100%' }}
                            >
                                {getColumnTranslationKey(column.key)}
                        </Checkbox>
                    ))}
                </Space>
                </Checkbox.Group>
            </Space>
        </div>
    );

    return (
        <Popover
            content={popoverContent}
            trigger="click"
            placement="bottomLeft"
            open={popoverOpen}
            onOpenChange={setPopoverOpen}
        >
            <Button disabled={disabled}>
                <SettingOutlined /> {t('customizedDropdown.button')}
            </Button>
        </Popover>
    );
};

export default CustomizedDropdown;
