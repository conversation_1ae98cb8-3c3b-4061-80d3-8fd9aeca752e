.qrcode-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.qrcode-main-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: none;
}

.qrcode-info-section {
  padding: 8px;
}

.qrcode-status {
  margin: 16px 0;
  display: flex;
  align-items: center;
}

.qrcode-status .ant-tag {
  padding: 4px 12px;
  font-size: 14px;
  border-radius: 6px;
  margin-right: 12px;
}

.qrcode-details-link {
  margin-left: auto;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.qrcode-details-link .anticon {
  font-size: 12px;
  transition: transform 0.2s;
}

.qrcode-details-link:hover .anticon {
  transform: translateX(4px);
}

.qrcode-highlights {
  margin: 24px 0;
}

.qrcode-highlight-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  background: #ffffff;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.qrcode-highlight-item .anticon {
  font-size: 20px;
  margin-right: 12px;
  color: #1890ff;
}

.qrcode-steps {
  margin: 24px 0;
}

.qrcode-steps .ant-steps-item-description {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.65);
}

.qrcode-action-buttons {
  margin-top: 32px;
}

.qrcode-action-buttons-right {
  display: flex;
  justify-content: flex-end;
}

.qrcode-right-section {
  height: 100%;
}

.qrcode-display-card {
  text-align: center;
  background: #fafafa;
  border: none;
  margin-bottom: 16px;
}

.qrcode-display {
  margin: 24px 0;
  padding: 24px;
  background: white;
  border-radius: 12px;
  display: inline-block;
}

.qrcode-user-info {
  margin: 16px 0;
}

.qrcode-hint {
  display: block;
  margin-top: 16px;
  font-size: 14px;
}

.qrcode-mobile-hint {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #e6f7ff;
  border-radius: 8px;
  color: #1890ff;
}

.qrcode-mobile-hint .anticon {
  margin-right: 8px;
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .qrcode-container {
    padding: 16px;
  }

  .qrcode-right-section {
    margin-top: 24px;
  }

  .qrcode-highlight-item {
    padding: 8px 12px;
  }

  .qrcode-details-link {
    display: none;
  }
}
  