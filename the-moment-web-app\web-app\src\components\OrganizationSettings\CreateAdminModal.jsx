import React from 'react';
import { Form, Input, Button, Modal, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { adminService } from '../../services/adminService';

const { Text } = Typography;

const CreateAdminModal = ({
    visible,
    loading,
    onCancel,
    onSuccess,
    onError,
    onLoadingChange,
    isSuperAdmin,
    onRefreshAdminUsers,
    organizationId = null // 预留组织ID参数，用于将来修复组织关联问题
}) => {
    const { t } = useTranslation();
    const [form] = Form.useForm();

    const handleCreateAdminUser = async (values) => {
        onLoadingChange?.(true);
        const { displayName, email, password, language } = values;
        const payload = {
            display_name: displayName,
            email: email,
            password: password,
            role: 'admin',
            ...(organizationId && { organization_id: organizationId }), // 提供组织ID
        };
        
        if (language) {
            payload.interface_language = language;
            payload.communication_language = language;
        }

        try {
            await adminService.createAdminUser(payload);
            onSuccess?.(t('organizationSettings.messages.adminUserCreatedSuccessfully'));
            form.resetFields();
            
            // 刷新管理员用户列表
            if (isSuperAdmin && onRefreshAdminUsers) {
                await onRefreshAdminUsers();
            }
        } catch (error) {
            console.error(t('organizationSettings.errors.adminUserCreationFailed'), error);
            let errorMessage = t('organizationSettings.errors.adminUserCreationFailed');
            if (error.response?.data?.message) {
                errorMessage = `${errorMessage} ${error.response.data.message}`;
            } else if (error.message) {
                errorMessage = `${errorMessage} ${error.message}`;
            }
            onError?.(errorMessage);
        }
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <Modal
            title={t('organizationSettings.createAdmin.title')}
            open={visible}
            onCancel={handleCancel}
            confirmLoading={loading}
            onOk={() => form.submit()}
            footer={[
                <Button key="back" onClick={handleCancel}>
                    {t('common.cancel')}
                </Button>,
                <Button key="submit" type="primary" loading={loading} onClick={() => form.submit()}>
                    {t('common.submit')}
                </Button>,
            ]}
        >
            <Form form={form} layout="vertical" onFinish={handleCreateAdminUser}>
                <Form.Item
                    name="displayName"
                    label={t('organizationSettings.createAdmin.displayName')}
                    rules={[
                        { required: true, message: t('organizationSettings.errors.displayNameRequired') },
                        { min: 2, message: t('organizationSettings.errors.displayNameTooShort') },
                        { max: 50, message: t('organizationSettings.errors.displayNameTooLong') }
                    ]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="email"
                    label={t('organizationSettings.createAdmin.email')}
                    rules={[
                        { required: true, message: t('organizationSettings.errors.emailRequired') },
                        { type: 'email', message: t('organizationSettings.errors.emailInvalid') }
                    ]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="password"
                    label={t('organizationSettings.createAdmin.password')}
                    rules={[
                        { required: true, message: t('organizationSettings.errors.newPasswordRequired') },
                        { min: 8, message: t('organizationSettings.errors.passwordTooShort') }
                    ]}
                    hasFeedback
                >
                    <Input.Password />
                </Form.Item>
                <Form.Item
                    name="confirmPassword"
                    label={t('organizationSettings.createAdmin.confirmPassword')}
                    dependencies={['password']}
                    hasFeedback
                    rules={[
                        { required: true, message: t('organizationSettings.errors.confirmPasswordRequired') },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue('password') === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error(t('organizationSettings.errors.passwordsDoNotMatchNewAdmin')));
                            },
                        }),
                    ]}
                >
                    <Input.Password />
                </Form.Item>
                <Form.Item
                    name="language"
                    label={t('organizationSettings.createAdmin.language')}
                >
                    <Input placeholder={t('organizationSettings.createAdmin.languagePlaceholder')} />
                </Form.Item>
            </Form>
            <Text type="secondary" style={{ marginTop: '10px', display: 'block' }}>
                {t('organizationSettings.messages.adminCreateNote')}
            </Text>
        </Modal>
    );
};

export default CreateAdminModal; 