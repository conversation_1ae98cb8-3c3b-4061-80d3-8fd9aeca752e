package models

import "Membership-SAAS-System-Backend/db" // Assuming db is your sqlc package alias

type ApplicationStatus string

const (
	ApplicationStatusPending   ApplicationStatus = "pending"
	ApplicationStatusApproved  ApplicationStatus = "approved"
	ApplicationStatusRejected  ApplicationStatus = "rejected"
	ApplicationStatusWithdrawn ApplicationStatus = "withdrawn"
)

func (s ApplicationStatus) IsValid() bool {
	switch s {
	case ApplicationStatusPending, ApplicationStatusApproved, ApplicationStatusRejected, ApplicationStatusWithdrawn:
		return true
	}
	return false
}

func (s ApplicationStatus) ToSQLC() db.ApplicationStatusEnum { // Assuming db is your sqlc package alias
	return db.ApplicationStatusEnum(s)
}

func AllApplicationStatuses() []string {
	return []string{
		string(ApplicationStatusPending),
		string(ApplicationStatusApproved),
		string(ApplicationStatusRejected),
		string(ApplicationStatusWithdrawn),
	}
}
