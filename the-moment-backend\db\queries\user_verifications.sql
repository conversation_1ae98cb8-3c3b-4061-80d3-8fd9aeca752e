-- name: CreateVerificationDocument :one
INSERT INTO verification_documents (
    user_id,
    file_name,
    mime_type,
    file_data
) VALUES (
    $1, $2, $3, $4
) RETURNING *;

-- name: GetVerificationDocumentByID :one
SELECT * FROM verification_documents
WHERE id = $1 AND user_id = $2;

-- name: GetVerificationDocumentMetaByID :one
SELECT id, user_id, file_name, mime_type, uploaded_at, created_at, updated_at
FROM verification_documents
WHERE id = $1;

-- name: GetVerificationDocumentDataByID :one
SELECT file_data FROM verification_documents
WHERE id = $1;

-- name: DeleteVerificationDocument :exec
DELETE FROM verification_documents
WHERE id = $1 AND user_id = $2;

-- name: CreateUserVerificationRequest :one
INSERT INTO user_verification_requests (
    user_id,
    verification_type,
    status,
    document_id,
    document_id_2
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING *;

-- name: GetUserVerificationRequestByID :one
SELECT
    uvr.id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.submitted_at,
    uvr.document_id,
    uvr.document_id_2,
    sqlc.embed(vd)
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
WHERE uvr.id = $1;

-- name: ListUserVerificationRequestsByUserID :many
SELECT sqlc.embed(uvr), uvr.document_id_2, sqlc.embed(vd)
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
WHERE uvr.user_id = $1
ORDER BY uvr.submitted_at DESC;

-- name: ListPendingVerificationRequests :many
SELECT sqlc.embed(uvr), uvr.document_id_2, sqlc.embed(u), sqlc.embed(vd)
FROM user_verification_requests uvr
JOIN users u ON uvr.user_id = u.id
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
WHERE uvr.status = 'pending'
ORDER BY uvr.submitted_at ASC;

-- name: UpdateUserVerificationRequestStatus :one
UPDATE user_verification_requests
SET
    status = $2,
    reviewed_at = now(),
    reviewed_by_user_id = $3,
    admin_notes = $4
WHERE id = $1
RETURNING *;

-- name: UserDeleteVerificationData :one
UPDATE user_verification_requests
SET
    status = 'data_deleted_by_user',
    document_id = NULL, -- Nullify the link to the document
    document_id_2 = NULL, -- Also nullify the link to the second document
    updated_at = now()
WHERE id = $1 AND user_id = $2
RETURNING *;

-- Specific Verification Type Queries --

-- HK ID Card
-- name: CreateHKIDCardVerification :one
INSERT INTO verification_hk_id_cards (
    verification_request_id,
    chinese_name,
    chinese_commercial_code,
    english_name,
    sex,
    date_of_birth,
    hk_id_number,
    is_permanent_resident
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
) RETURNING *;

-- name: GetHKIDCardVerificationByRequestID :one
SELECT * FROM verification_hk_id_cards
WHERE verification_request_id = $1;

-- Mainland China ID Card
-- name: CreateMainlandChinaIDCardVerification :one
INSERT INTO verification_mainland_china_id_cards (
    verification_request_id,
    chinese_name,
    sex,
    date_of_birth,
    mainland_id_number,
    valid_until
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING *;

-- name: GetMainlandChinaIDCardVerificationByRequestID :one
SELECT * FROM verification_mainland_china_id_cards
WHERE verification_request_id = $1;

-- Home Return Permit -> Mainland Travel Permit
-- name: CreateMainlandTravelPermitVerification :one
INSERT INTO verification_mainland_travel_permits (
    verification_request_id,
    permit_number,
    issue_date,
    expiry_date
) VALUES (
    $1, $2, $3, $4
) RETURNING *;

-- name: GetMainlandTravelPermitVerificationByRequestID :one
SELECT * FROM verification_mainland_travel_permits
WHERE verification_request_id = $1;

-- Passport
-- name: CreatePassportVerification :one
INSERT INTO verification_passports (
    verification_request_id,
    passport_number,
    issuing_country,
    issue_date,
    expiry_date
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING *;

-- name: GetPassportVerificationByRequestID :one
SELECT * FROM verification_passports
WHERE verification_request_id = $1;

-- HK Youth+
-- name: CreateHKYouthPlusVerification :one
INSERT INTO verification_hk_youth_plus (
    verification_request_id,
    member_number,
    document_id
) VALUES (
    $1, $2, $3
) RETURNING *;

-- name: GetHKYouthPlusVerificationByRequestID :one
SELECT * FROM verification_hk_youth_plus
WHERE verification_request_id = $1;

-- Address Proof
-- name: CreateAddressProofVerification :one
INSERT INTO verification_address_proofs (
    verification_request_id,
    full_address
) VALUES (
    $1, $2
) RETURNING *;

-- name: GetAddressProofVerificationByRequestID :one
SELECT * FROM verification_address_proofs
WHERE verification_request_id = $1;

-- Student ID
-- name: CreateStudentIDVerification :one
INSERT INTO verification_student_ids (
    verification_request_id,
    school_name,
    grade,
    expiry_date
) VALUES (
    $1, $2, $3, $4
) RETURNING *;

-- name: GetStudentIDVerificationByRequestID :one
SELECT * FROM verification_student_ids
WHERE verification_request_id = $1;

-- Home Visit
-- name: CreateHomeVisitVerification :one
INSERT INTO verification_home_visits (
    verification_request_id,
    notes
) VALUES (
    $1, $2
) RETURNING *;

-- name: GetHomeVisitVerificationByRequestID :one
SELECT * FROM verification_home_visits
WHERE verification_request_id = $1;

-- name: GetFullVerificationDetails :one
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    
    COALESCE(vd.file_name, '') as file_name,
    COALESCE(vd.mime_type, '') as mime_type,
    vd.uploaded_at as document_uploaded_at,
    
    COALESCE(vd2.file_name, '') AS file_name_2,
    COALESCE(vd2.mime_type, '') AS mime_type_2,
    
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.email as reviewer_email,
    reviewer.display_name as reviewer_display_name,
    hkid.*,
    mcid.*,
    hrp.*,
    pass.*,
    hkyp.*,
    ap.*,
    sid.*,
    hv.*
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
LEFT JOIN verification_documents vd2 ON uvr.document_id_2 = vd2.id
LEFT JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id
LEFT JOIN verification_hk_id_cards hkid ON uvr.id = hkid.verification_request_id AND uvr.verification_type = 'hk_id_card'
LEFT JOIN verification_mainland_china_id_cards mcid ON uvr.id = mcid.verification_request_id AND uvr.verification_type = 'mainland_china_id_card'
LEFT JOIN verification_mainland_travel_permits hrp ON uvr.id = hrp.verification_request_id AND uvr.verification_type = 'mainland_travel_permit'
LEFT JOIN verification_passports pass ON uvr.id = pass.verification_request_id AND uvr.verification_type = 'passport'
LEFT JOIN verification_hk_youth_plus hkyp ON uvr.id = hkyp.verification_request_id AND uvr.verification_type = 'hk_youth_plus'
LEFT JOIN verification_address_proofs ap ON uvr.id = ap.verification_request_id AND uvr.verification_type = 'address_proof'
LEFT JOIN verification_student_ids sid ON uvr.id = sid.verification_request_id AND uvr.verification_type = 'student_id'
LEFT JOIN verification_home_visits hv ON uvr.id = hv.verification_request_id AND uvr.verification_type = 'home_visit'
WHERE uvr.id = $1;

-- name: ListFullUserVerificationRequestsByUserID :many
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    vd2.file_name as file_name_2,
    vd2.mime_type as mime_type_2,
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.email as reviewer_email,
    reviewer.display_name as reviewer_display_name
    -- Specific type details are not included here for brevity in a list view.
    -- Client can call GetFullVerificationDetails for a specific request if needed.
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
LEFT JOIN verification_documents vd2 ON uvr.document_id_2 = vd2.id
LEFT JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id
WHERE uvr.user_id = $1
ORDER BY uvr.submitted_at DESC;

-- name: ListFullPendingVerificationRequests :many
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    u.email as user_email,
    u.display_name as user_display_name
    -- Specific type details are not included here for brevity in a list view.
    -- Client can call GetFullVerificationDetails for a specific request if needed.
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
LEFT JOIN users u ON uvr.user_id = u.id
WHERE uvr.status = 'pending'
ORDER BY uvr.submitted_at ASC;

-- name: ListUserVerificationsByStatusAndType :many
SELECT * FROM user_verification_requests
WHERE user_id = $1 AND status = $2 AND verification_type = $3
ORDER BY submitted_at DESC;

-- name: ListUserVerificationsByStatus :many
SELECT * FROM user_verification_requests
WHERE user_id = $1 AND status = $2
ORDER BY submitted_at DESC;

-- name: ListUserVerificationsByType :many
SELECT * FROM user_verification_requests
WHERE user_id = $1 AND verification_type = $2
ORDER BY submitted_at DESC;

-- name: GetUserVerificationRequestsSimple :many
SELECT * FROM user_verification_requests WHERE user_id = $1 ORDER BY submitted_at DESC;

-- name: GetUserVerificationStatuses :one
SELECT
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'hk_id_card' AND uvr.status = 'approved') AS hk_id_card,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'mainland_china_id_card' AND uvr.status = 'approved') AS mainland_china_id_card,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'mainland_travel_permit' AND uvr.status = 'approved') AS mainland_travel_permit,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'passport' AND uvr.status = 'approved') AS passport,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'hk_youth_plus' AND uvr.status = 'approved') AS hk_youth_plus,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'address_proof' AND uvr.status = 'approved') AS address_proof,
    EXISTS(SELECT 1 FROM user_verification_requests uvr WHERE uvr.user_id = $1 AND uvr.verification_type = 'student_id' AND uvr.status = 'approved') AS student_id
;

-- name: AdminListFullVerificationRequests :many
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.display_name as reviewer_display_name -- Added reviewer display name
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id -- Join for reviewer info
WHERE (uvr.status = sqlc.narg('filter_status') OR sqlc.narg('filter_status') IS NULL)
  AND (
    sqlc.narg('filter_org_id')::UUID IS NULL OR
    EXISTS (
        SELECT 1 FROM user_organization_memberships om
        WHERE om.user_id = u.id AND om.organization_id = sqlc.narg('filter_org_id')::UUID
    )
  )
ORDER BY uvr.submitted_at ASC;

-- name: AdminGetFullVerificationRequestByID :one
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.email as reviewer_email,
    reviewer.display_name as reviewer_display_name
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
LEFT JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id
WHERE uvr.id = $1;

-- name: AdminListFullVerificationRequestsWithFilters :many
SELECT
    uvr.id as request_id,
    uvr.user_id,
    uvr.verification_type,
    uvr.status,
    uvr.document_id,
    uvr.document_id_2,
    uvr.submitted_at,
    uvr.reviewed_at,
    uvr.reviewed_by_user_id,
    uvr.admin_notes,
    uvr.created_at as request_created_at,
    uvr.updated_at as request_updated_at,
    vd.file_name,
    vd.mime_type,
    vd.uploaded_at as document_uploaded_at,
    u.email as user_email,
    u.display_name as user_display_name,
    reviewer.display_name as reviewer_display_name
FROM user_verification_requests uvr
LEFT JOIN verification_documents vd ON uvr.document_id = vd.id
JOIN users u ON uvr.user_id = u.id
LEFT JOIN users reviewer ON uvr.reviewed_by_user_id = reviewer.id
-- Optional: Join with event_registrations if filter_event_id is provided
LEFT JOIN event_registrations er ON uvr.user_id = er.user_id AND er.event_id = sqlc.narg('filter_event_id')::UUID
WHERE
    (uvr.user_id = sqlc.narg('filter_user_id')::UUID OR sqlc.narg('filter_user_id') IS NULL)
  AND (uvr.verification_type = sqlc.narg('filter_verification_type') OR sqlc.narg('filter_verification_type') IS NULL) -- Ensure filter_verification_type is compatible with the ENUM or cast appropriately
  AND (uvr.status = sqlc.narg('filter_status') OR sqlc.narg('filter_status') IS NULL) -- Ensure filter_status is compatible with the ENUM or cast appropriately
  AND (sqlc.narg('filter_org_id')::UUID IS NULL OR EXISTS (
        SELECT 1 FROM user_organization_memberships om
        WHERE om.user_id = u.id AND om.organization_id = sqlc.narg('filter_org_id')::UUID
      ))
  AND (sqlc.narg('filter_event_id')::UUID IS NULL OR er.event_id IS NOT NULL) -- Ensures user is registered for the event if event_id is filtered
ORDER BY uvr.submitted_at DESC -- Or other preferred default order
LIMIT sqlc.arg('lim')
OFFSET sqlc.arg('offs');

-- Companion query for total count for pagination
-- name: CountAdminListFullVerificationRequestsWithFilters :one
SELECT COUNT(*)
FROM user_verification_requests uvr
JOIN users u ON uvr.user_id = u.id
-- Optional: Join with event_registrations if filter_event_id is provided
LEFT JOIN event_registrations er ON uvr.user_id = er.user_id AND er.event_id = sqlc.narg('filter_event_id')::UUID
WHERE
    (uvr.user_id = sqlc.narg('filter_user_id')::UUID OR sqlc.narg('filter_user_id') IS NULL)
  AND (uvr.verification_type = sqlc.narg('filter_verification_type') OR sqlc.narg('filter_verification_type') IS NULL)
  AND (uvr.status = sqlc.narg('filter_status') OR sqlc.narg('filter_status') IS NULL)
  AND (sqlc.narg('filter_org_id')::UUID IS NULL OR EXISTS (
        SELECT 1 FROM user_organization_memberships om
        WHERE om.user_id = u.id AND om.organization_id = sqlc.narg('filter_org_id')::UUID
      ))
  AND (sqlc.narg('filter_event_id')::UUID IS NULL OR er.event_id IS NOT NULL);