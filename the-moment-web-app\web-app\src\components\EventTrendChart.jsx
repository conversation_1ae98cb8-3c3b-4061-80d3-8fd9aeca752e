// 'use client';

import { useState, useMemo, useEffect, useRef } from 'react';
import {
    RiArrowRightUpLine,
    RiSearchLine,
} from '@remixicon/react';
import {
    <PERSON><PERSON>hart,
    Bar<PERSON>hart,
    BarList,
    Card,
    Dialog,
    DialogPanel,
    TextInput,
} from '@tremor/react';
import { Segmented } from 'antd';
import { useTranslation } from 'react-i18next';
import { formatDateOnly } from '../utils/dateFormatter';

const valueFormatter = (number) =>
    `${Intl.NumberFormat('us').format(number).toString()}`;

const getTotalCount = (value) => {
    return value.Participants + value.Volunteers;
};
export default function EventTrendChart({ chartData, topCategories, topEvents }) {
    const { t, i18n } = useTranslation();
    const [modal, setModal] = useState({
        open: false,
        index: 0,
    });
    const [searchQuery, setSearchQuery] = useState('');
    const [chartType, setChartType] = useState('line');
    const containerRef = useRef(null);
    const [containerWidth, setContainerWidth] = useState(0);

    // Update container width on mount and resize
    useEffect(() => {
        const updateWidth = () => {
            if (containerRef.current) {
                setContainerWidth(containerRef.current.offsetWidth);
            }
        };

        updateWidth();
        window.addEventListener('resize', updateWidth);

        return () => window.removeEventListener('resize', updateWidth);
    }, []);

    const eventData = {
        name: 'Events',
        type: 'Participants',
        value: '2.7K',
        categories: [
            {
                name: 'Top Categories',
                data: topCategories || [],
            },
            {
                name: 'Top Events',
                data: (topEvents || []).map(event => ({
                    // Map from API format (id, title, participants) to component format (name, value)
                    name: event.title,
                    value: event.participants
                })),
            },
        ],
    };

    const filteredItems = eventData.categories[modal.index].data.filter((item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()),
    );

    const getModalContent = (index) => {
        if (index === 0) {
            return {
                title: t('adminEvents.report.charts.topCategories'),
                metric: t('adminEvents.report.charts.events')
            };
        }
        return {
            title: t('adminEvents.report.charts.topEvents'),
            metric: t('adminEvents.report.charts.participants')
        };
    };

    const modalContent = getModalContent(modal.index);

    // Memoize legendItems
    const legendItems = useMemo(() => [
        { key: 'Participants', label: t('adminEvents.report.charts.participantsLegend'), color: 'rgb(59, 130, 246)' }, // blue
        { key: 'Volunteers', label: t('adminEvents.report.charts.volunteersLegend'), color: 'rgb(236, 72, 153)' }     // pink
    ], [t]);

    // Memoize customTooltip
    const memoizedCustomTooltip = useMemo(() => {
        return (props) => {
            const { payload, active, label } = props;

            if (!active || !payload) return null;

            const total = payload[0]?.payload ? getTotalCount(payload[0].payload) : 0;
            const categories = {
                'Participants': t('adminEvents.report.charts.participantsLegend'),
                'Volunteers': t('adminEvents.report.charts.volunteersLegend')
            };

            return (
                <div className="w-56 rounded-tremor-default border border-tremor-border bg-tremor-background text-tremor-default shadow-tremor-dropdown">
                    <div className="border-b border-tremor-border px-3 py-2">
                        <p className="font-medium text-tremor-content-strong">
                            {formatDateOnly(label, i18n.language)}
                        </p>
                    </div>
                    <div className="space-y-2 px-3 py-2">
                        {payload.map((category, idx) => (
                            <div key={idx} className="flex space-x-2.5">
                                <span
                                    className={`w-1 bg-${category.color}-500 shrink-0 rounded`}
                                    aria-hidden={true}
                                />
                                <p className="flex w-full items-center justify-between space-x-8 truncate">
                                    <span className="truncate font-medium text-tremor-content-strong">
                                        {categories[category.dataKey]}
                                    </span>
                                    <span className="font-bold text-tremor-content-strong">
                                        {valueFormatter(category.value)}
                                    </span>
                                </p>
                            </div>
                        ))}
                        <div className="border-t border-tremor-border pt-2">
                            <div className="flex space-x-2.5">
                                <span className="w-1 shrink-0 rounded bg-gray-400" aria-hidden={true} />
                                <p className="flex w-full items-center justify-between space-x-8 truncate">
                                    <span className="truncate font-medium text-tremor-content-strong">
                                        {t('adminEvents.report.charts.total')}
                                    </span>
                                    <span className="font-bold text-tremor-content-strong">
                                        {valueFormatter(total)}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };
    }, [t, i18n.language]);

    // Memoize chart configurations
    const chartConfig = useMemo(() => ({
        data: chartData,
        index: "date",
        categories: ['Participants', 'Volunteers'],
        valueFormatter: valueFormatter,
        showLegend: false,
        yAxisWidth: 45,
        colors: ['blue', 'pink'],
        customTooltip: memoizedCustomTooltip,
    }), [chartData, memoizedCustomTooltip]);

    // Memoize the charts to prevent re-renders
    const lineChartMemo = useMemo(() => (
        <div
            className="chart-container absolute inset-0 transition-opacity duration-300"
            style={{
                opacity: chartType === 'line' ? 1 : 0,
                pointerEvents: chartType === 'line' ? 'auto' : 'none'
            }}
        >
            <LineChart
                {...chartConfig}
                connectNulls={true}
                curveType="monotone"
            />
        </div>
    ), [chartConfig, chartType]);

    const barChartMemo = useMemo(() => (
        <div
            className="chart-container absolute inset-0 transition-opacity duration-300"
            style={{
                opacity: chartType === 'bar' ? 1 : 0,
                pointerEvents: chartType === 'bar' ? 'auto' : 'none'
            }}
        >
            <BarChart
                {...chartConfig}
                stack={true}
            />
        </div>
    ), [chartConfig, chartType]);

    // Memoize the legend
    const CustomLegend = useMemo(() => (
        <div className="flex justify-end gap-x-8 mt-8">
            {legendItems.map(({ key, label, color }) => (
                <div key={key} className="flex items-center gap-x-2">
                    <div className="h-2 w-2 rounded-full" style={{ backgroundColor: color }} />
                    <span className="text-tremor-default text-tremor-content">{label}</span>
                </div>
            ))}
        </div>
    ), [legendItems]);

    return (
        <>
            <Card className="overflow-hidden p-6">

                <div className="mb-4">
                    <Segmented
                        options={[
                            { label: t('adminEvents.report.charts.lineChart'), value: 'line' },
                            { label: t('adminEvents.report.charts.barChart'), value: 'bar' }
                        ]}
                        value={chartType}
                        onChange={setChartType}
                    />
                </div>
                <div ref={containerRef} className="relative w-full" style={{ height: '300px' }}>
                    {lineChartMemo}
                    {barChartMemo}

                </div>
                {CustomLegend}


            </Card>

            <div className="mt-8 grid grid-cols-1 gap-8 sm:grid-cols-2">
                {eventData.categories.map((category, idx) => {
                    const currentData = category.data || [];
                    const showMoreButton = currentData.length > 5;

                    return (
                        <Card key={category.name}>
                            <div className="flex items-center justify-between">
                                <p className="text-tremor-default font-medium text-tremor-content-strong">
                                    {t(`adminEvents.report.charts.${category.name === 'Top Events' ? 'topEvents' : 'topCategories'}`)}
                                </p>
                                <span className="text-tremor-label font-medium uppercase text-tremor-content">
                                    {category.name === 'Top Events' ? t('adminEvents.report.charts.participants') : t('adminEvents.report.charts.events')}
                                </span>
                            </div>
                            <BarList
                                data={currentData.slice(0, 5)}
                                valueFormatter={valueFormatter}
                                className="mt-4"
                            />
                            {showMoreButton && (
                                <div className="absolute inset-x-0 bottom-0 flex justify-center rounded-b-tremor-default bg-gradient-to-t from-tremor-background to-transparent py-7">
                                    <button
                                        className="flex items-center justify-center gap-x-1.5 rounded-tremor-full border border-tremor-border bg-tremor-background px-2.5 py-1.5 text-tremor-label font-medium text-tremor-content-strong shadow-tremor-input hover:bg-tremor-background-muted"
                                        onClick={() => setModal({ open: true, index: idx })}
                                    >
                                        {t('adminEvents.report.charts.showMore')}
                                        <RiArrowRightUpLine className="-mr-px size-4 shrink-0" aria-hidden={true} />
                                    </button>
                                </div>
                            )}
                        </Card>
                    );
                })}
            </div>

            <Dialog
                open={modal.open}
                onClose={() => {
                    setModal((prev) => ({ ...prev, open: false }));
                    setSearchQuery('');
                }}
                static={true}
                className="z-[1001]"
            >
                <DialogPanel className="p-0">
                    <div className="px-6 pb-4 pt-6">
                        <TextInput
                            icon={RiSearchLine}
                            placeholder={t('common.search')}
                            className="rounded-tremor-small"
                            value={searchQuery}
                            onChange={(event) => setSearchQuery(event.target.value)}
                        />
                        <div className="flex items-center justify-between pt-4">
                            <p className="text-tremor-default font-medium text-tremor-content-strong">
                                {modalContent.title}
                            </p>
                            <span className="text-tremor-label font-medium uppercase text-tremor-content">
                                {modalContent.metric}
                            </span>
                        </div>
                    </div>
                    <div className="h-96 overflow-y-scroll px-6">
                        {filteredItems.length > 0 ? (
                            <BarList data={filteredItems} valueFormatter={valueFormatter} />
                        ) : (
                            <p className="flex h-full items-center justify-center text-tremor-default text-tremor-content-strong">
                                {t('common.noResults')}
                            </p>
                        )}
                    </div>
                    <div className="mt-4 border-t border-tremor-border bg-tremor-background-muted p-6">
                        <button
                            className="flex w-full items-center justify-center rounded-tremor-small border border-tremor-border bg-tremor-background py-2 text-tremor-default font-medium text-tremor-content-strong shadow-tremor-input hover:bg-tremor-background-muted"
                            onClick={() => {
                                setSearchQuery('');
                                setModal((prev) => ({ ...prev, open: false }));
                            }}
                        >
                            {t('common.goBack')}
                        </button>
                    </div>
                </DialogPanel>
            </Dialog>
        </>
    );
}