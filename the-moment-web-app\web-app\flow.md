# Frontend Integration Guide: Login & Authentication

## Overview

The backend supports three primary authentication flows:

1.  **Regular User Login (Phone OTP):** For existing users to log in using their phone number and an OTP.
2.  **New User Registration (Phone OTP):** For new users to register using their phone number and an OTP.
3.  **Staff Authentication (Email + Password):** For staff members (admins/managers) to log in using their email and password.

All phone-based OTP flows utilize the Proof Key for Code Exchange (PKCE) standard for enhanced security.

## 1. Regular User Authentication (Phone OTP)

This flow is for existing users who have already registered.

**Trigger:** User enters their phone number to log in.

**Process:**

1.  **Check Phone Number:**
    *   Frontend sends the phone number to `POST /api/v1/authn/phone/check`.
    *   **Request Body:**
        ```json
        {
          "phone": "+***********" // E.164 format
        }
        ```
    *   **Backend Response:**
        *   If phone exists: `{"exists": true}`
        *   If phone does not exist: `{"exists": false}`. The frontend should then guide the user to the New User Registration flow (see section 2).

2.  **Initiate OTP Flow (if phone exists):**
    *   Frontend generates a `code_verifier` and `code_challenge` (PKCE).
    *   Frontend calls `POST /api/v1/authn/phone/otp/initiate`.
    *   **Request Body:**
        ```json
        {
          "phone": "+***********",
          "code_challenge": "generated_code_challenge",
          "code_challenge_method": "S256", // Typically S256
          "state": "frontend_generated_opaque_state_value", // Optional, for CSRF protection/flow tracking
          "phone_otp_channel": "sms" // or "whatsapp" (optional, defaults to backend config)
        }
        ```
    *   **Backend Response:**
        *   Success (200 OK): Indicates OTP has been sent. The response may include the `state` if provided.
        *   Error: Handle appropriately (e.g., rate limiting, invalid phone).

3.  **Verify OTP and Log In:**
    *   User receives the OTP on their device and enters it into the frontend.
    *   Frontend calls `POST /api/v1/authn/phone/otp/verify`.
    *   **Request Body:**
        ```json
        {
          "phone": "+***********",
          "otp": "user_entered_otp",
          "code_verifier": "original_code_verifier", // PKCE verifier
          "state": "frontend_generated_opaque_state_value" // Must match the state sent in initiate (if any)
        }
        ```
    *   **Backend Response (Success - 200 OK):**
        ```json
        {
          "access_token": "your_jwt_access_token",
          "refresh_token": "your_jwt_refresh_token",
          "user": {
            "id": "user_id",
            "display_name": "User Name",
            // ... other user details
          }
        }
        ```
        The frontend should store the `access_token` and `refresh_token` securely. The `access_token` is used for subsequent authenticated API calls. The `refresh_token` is used to obtain new access tokens (see section 4).
    *   **Backend Response (Error):** Handle OTP validation errors, PKCE verification failures, etc.

## 2. New User Registration (Phone OTP)

This flow is for users who do not yet have an account.

**Trigger:** User enters their phone number, and the `/authn/phone/check` endpoint returns `{"exists": false}`.

**Process:**

1.  **Initiate Registration OTP Flow:**
    *   Frontend generates a `code_verifier` and `code_challenge` (PKCE).
    *   Frontend calls `POST /api/v1/authn/register/phone/initiate`.
    *   **Request Body:**
        ```json
        {
          "phone": "+***********", // E.164 format
          "code_challenge": "generated_code_challenge",
          "code_challenge_method": "S256",
          "state": "frontend_generated_opaque_state_value",
          "phone_otp_channel": "sms" // or "whatsapp" (optional)
        }
        ```
    *   **Backend Response:**
        *   Success (200 OK): Indicates OTP has been sent.

2.  **Verify OTP and Complete Registration:**
    *   User receives the OTP and enters it. Frontend also collects necessary profile information (e.g., display name).
    *   Frontend calls `POST /api/v1/authn/register/phone/verify`.
    *   **Request Body:**
        ```json
        {
          "phone": "+***********",
          "otp": "user_entered_otp",
          "code_verifier": "original_code_verifier",
          "state": "frontend_generated_opaque_state_value",
          "display_name": "User's Desired Name",
          // ... other optional profile fields like email, language_preference etc.
        }
        ```
    *   **Backend Response (Success - 201 Created):**
        ```json
        {
          "access_token": "your_jwt_access_token",
          "refresh_token": "your_jwt_refresh_token",
          "user": {
            "id": "new_user_id",
            "display_name": "User's Desired Name",
            // ... other user details
          }
        }
        ```
        The frontend should store tokens as described in section 1.3.
    *   **Backend Response (Error):** Handle OTP validation errors, PKCE failures, duplicate user issues (if any race condition), etc.

## 3. Staff Authentication (Email + Password)

This flow is for staff members (admins/managers) with pre-existing credentials.

**Trigger:** Staff member chooses the email/password login option.

**Process:**

1.  **Check Staff Email (Optional but Recommended):**
    *   Frontend sends the email to `POST /api/v1/authn/staff/email/check`.
    *   **Request Body:**
        ```json
        {
          "email": "<EMAIL>"
        }
        ```
    *   **Backend Response:**
        *   If email exists and is a staff account: `{"exists": true, "is_staff": true}` (or similar)
        *   Otherwise: Appropriate error or `{"exists": false}`.

2.  **Initiate Staff Login:**
    *   Frontend generates a `code_verifier` and `code_challenge` (PKCE).
    *   Frontend calls `POST /api/v1/authn/staff/login/initiate`.
    *   **Request Body:**
        ```json
        {
          "email": "<EMAIL>",
          "client_id": "your_frontend_client_id", // e.g., "admin-webapp" or "frontend-app"
          "code_challenge": "generated_code_challenge",
          "code_challenge_method": "S256",
          "state": "frontend_generated_opaque_state_value"
        }
        ```
    *   **Backend Response (Success - 200 OK):**
        *   The response may include the `state` and potentially other flow-related identifiers.

3.  **Verify Password and Log In:**
    *   Frontend collects the password from the staff member.
    *   Frontend calls `POST /api/v1/authn/staff/login/verify`.
    *   **Request Body:**
        ```json
        {
          "email": "<EMAIL>",
          "password": "staff_member_password",
          "code_verifier": "original_code_verifier",
          "state": "frontend_generated_opaque_state_value" // Must match state from initiate
        }
        ```
    *   **Backend Response (Success - 200 OK):**
        ```json
        {
          "access_token": "your_jwt_access_token",
          "refresh_token": "your_jwt_refresh_token",
          "user": { // Staff user details
            "id": "staff_user_id",
            "email": "<EMAIL>",
            // ... other staff details
          }
        }
        ```
        Store tokens as described in section 1.3.
    *   **Backend Response (Error):** Handle invalid credentials, PKCE failures, etc.

**Note on Staff Access:** Staff members can also use the regular phone OTP login if they have a verified phone number. However, this will only grant them access to regular user APIs, not management APIs. The email/password flow is specifically for elevated privileges.

## 4. Token Handling

### Access Token
*   The `access_token` (JWT) should be sent in the `Authorization` header for all subsequent authenticated API requests:
    `Authorization: Bearer <access_token>`
*   Access tokens are short-lived.

### Refresh Token
*   The `refresh_token` is long-lived and should be stored securely (e.g., in an HttpOnly cookie if possible, or secure storage).
*   When an `access_token` expires (or is about to expire), the frontend should use the `refresh_token` to obtain a new `access_token` and `refresh_token` pair by calling:
    `POST /api/v1/authn/token/refresh`
    *   **Request Body:**
        ```json
        {
          "refresh_token": "current_refresh_token"
        }
        ```
    *   **Backend Response (Success - 200 OK):**
        ```json
        {
          "access_token": "new_jwt_access_token",
          "refresh_token": "new_jwt_refresh_token"
        }
        ```
    *   The frontend should then replace the old tokens with the new ones.
    *   If the refresh token is invalid or expired, the user must log in again.

## 5. PKCE (Proof Key for Code Exchange)

*   **Generation:** The frontend is responsible for generating a cryptographically random `code_verifier` for each authentication attempt.
*   From the `code_verifier`, a `code_challenge` is derived using SHA-256 hashing and then Base64 URL encoding (without padding).
    *   `code_challenge = BASE64URL-ENCODE(SHA256(ASCII(code_verifier)))`
*   The `code_challenge_method` should be set to `"S256"`.
*   **Storage:** The `code_verifier` must be stored securely by the frontend until the `verify` step of the flow, after which it can be discarded for that specific flow.
*   **Usage:**
    *   The `code_challenge` and `code_challenge_method` are sent in the `initiate` step.
    *   The `code_verifier` is sent in the `verify` step.

## 6. Error Handling

The frontend should gracefully handle API errors:
*   **400 Bad Request:** Invalid input, missing fields. Display specific error messages if provided by the backend.
*   **401 Unauthorized:** Invalid credentials, expired token (attempt refresh if applicable), or missing token.
*   **403 Forbidden:** User does not have permission for the action.
*   **404 Not Found:** Endpoint or resource not found.
*   **429 Too Many Requests:** Rate limiting. Inform the user to try again later.
*   **5xx Server Errors:** Generic server error. Inform the user and potentially log the issue.

The backend API responses for errors typically follow this structure:
```json
{
  "error": "short_error_code", // e.g., "invalid_otp", "user_not_found"
  "message": "A more descriptive error message."
}
```

The frontend implementation follows the flows outlined above, providing a TypeScript API that handles:

1. **PKCE Generation** - Creates and manages code verifiers and challenges
2. **User Authentication** - Both regular users (phone OTP) and staff (email/password)
3. **Registration** - New user signup with phone verification
4. **Token Management** - Secure handling of access and refresh tokens

### Example Usage

**Check if a phone number exists:**
```typescript
const { exists } = await loginAuthAPI.checkPhone({ phone: "+***********" });

if (exists) {
  // Continue with existing user login flow
} else {
  // Redirect to registration flow
}
```

**Initiate OTP for existing user:**
```typescript
// Generate PKCE parameters
const { code_challenge, state } = await loginAuthAPI.generatePKCE();

// Initiate OTP
const result = await loginAuthAPI.initiatePhoneOTP({
  phone: "+***********",
  code_challenge,
  code_challenge_method: "S256",
  state
});

if (result.success) {
  // Show OTP input form
}
```

**Verify OTP and complete login:**
```typescript
// Get stored PKCE credentials
const credentials = loginAuthAPI.getPKCECredentials();

// Verify OTP
const response = await loginAuthAPI.verifyPhoneOTP({
  phone: "+***********",
  otp: "123456", // User entered OTP
  code_verifier: credentials.code_verifier,
  state: credentials.state
});

if ('access_token' in response) {
  // Login successful, redirect to dashboard
} else {
  // Show error message
  console.error(response.message);
}
```

**Staff Login:**
```typescript
// Check if email is a staff account
const { exists, is_staff } = await loginAuthAPI.checkStaffEmail({ 
  email: "<EMAIL>" 
});

if (exists && is_staff) {
  // Generate PKCE parameters
  const { code_challenge, state } = await loginAuthAPI.generatePKCE();
  
  // Initiate staff login
  await loginAuthAPI.initiateStaffLogin({
    email: "<EMAIL>",
    client_id: "admin-webapp",
    code_challenge,
    code_challenge_method: "S256",
    state
  });
  
  // Show password input form
  // ...
  
  // On password submission
  const credentials = loginAuthAPI.getPKCECredentials();
  const response = await loginAuthAPI.verifyStaffLogin({
    email: "<EMAIL>",
    password: "userPassword", // User entered password
    code_verifier: credentials.code_verifier,
    state: credentials.state
  });
  
  if ('access_token' in response) {
    // Staff login successful
  } else {
    // Show error message
  }
}
```

**Token Refresh:**
```typescript
// When an API call returns a 401 Unauthorized
if (await loginAuthAPI.refreshAccessToken()) {
  // Retry the original API call with the new token
} else {
  // Refresh failed, redirect to login
}
```

This implementation provides a secure, robust authentication system that follows best practices for mobile/web applications.