-- Recreate old enum types
CREATE TYPE volunteer_application_status AS ENUM (
    'pending',
    'approved',
    'rejected'
);

CREATE TYPE event_volunteer_application_status_type AS ENUM (
    'pending',
    'approved',
    'rejected'
);

-- Alter user_volunteer_applications table back
ALTER TABLE user_volunteer_applications ADD COLUMN status_old volunteer_application_status;
UPDATE user_volunteer_applications SET status_old = status::text::volunteer_application_status;
ALTER TABLE user_volunteer_applications DROP COLUMN status;
ALTER TABLE user_volunteer_applications RENAME COLUMN status_old TO status;
ALTER TABLE user_volunteer_applications ALTER COLUMN status SET NOT NULL;
ALTER TABLE user_volunteer_applications ALTER COLUMN status SET DEFAULT 'pending';

-- Recreate index for user_volunteer_applications
DROP INDEX IF EXISTS idx_uq_user_org_pending_application;
CREATE UNIQUE INDEX idx_uq_user_org_pending_application
ON user_volunteer_applications (user_id, organization_id)
WHERE status = 'pending';

-- Alter event_volunteer_applications table back
ALTER TABLE event_volunteer_applications ADD COLUMN status_old event_volunteer_application_status_type;
UPDATE event_volunteer_applications SET status_old = status::text::event_volunteer_application_status_type;
ALTER TABLE event_volunteer_applications DROP COLUMN status;
ALTER TABLE event_volunteer_applications RENAME COLUMN status_old TO status;
ALTER TABLE event_volunteer_applications ALTER COLUMN status SET NOT NULL;
-- Assuming original default for event_volunteer_applications.status was also 'pending' or similar, adjust if necessary.
-- Example: ALTER TABLE event_volunteer_applications ALTER COLUMN status SET DEFAULT 'pending';


-- Drop the consolidated enum type
DROP TYPE IF EXISTS application_status_enum; 