-- Reverts the deletion of specific seeded organizations and users.
BEGIN;

-- Re-seed Users and Capture IDs (similar to 000009_seed_orgs_and_users.up.sql)
DO $$
DECLARE
    org_tongxing_id uuid;
    org_shatinsi_id uuid;
    org_blue_id uuid;
    org_pink_id uuid;

    user_admin_tongxing_id uuid;
    user_manager_shatinsi_id uuid;
    user_admin_shatinsi_id uuid;
    user_admin_blue_id uuid;
    user_admin_pink_id uuid;
    user_regular1_id uuid;
    user_regular2_id uuid;
    user_regular3_id uuid;
    superadmin_id uuid;

    -- Hash for '123456' from original 000009 migration
    hashed_password TEXT := '$2a$12$JZLhcEoAGMPDxUHWUoxVy.qXa/sR2s52ELi565EqPS.LuC/6Ri.Qm'; 
BEGIN
    -- Get Superadmin ID (assuming seeded by 000003 and still exists)
    SELECT id INTO superadmin_id FROM users WHERE email = '<EMAIL>';
    IF superadmin_id IS NULL THEN
        RAISE NOTICE 'Superadmin user (<EMAIL>) not found. Some organization ownerships might not be restored correctly if superadmin was the owner.';
        -- Allow to proceed but with a warning, as other data might still be restorable.
    END IF;

    -- 1. Re-seed Users
    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at, created_at, updated_at)
    VALUES ('Admin TongXing', '<EMAIL>', '+85299990001', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (email) DO NOTHING RETURNING id INTO user_admin_tongxing_id;
    IF user_admin_tongxing_id IS NULL THEN SELECT id INTO user_admin_tongxing_id FROM users WHERE email = '<EMAIL>'; END IF;

    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at, created_at, updated_at)
    VALUES ('Manager ShaTinSi', '<EMAIL>', '+85299990002', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (email) DO NOTHING RETURNING id INTO user_manager_shatinsi_id;
    IF user_manager_shatinsi_id IS NULL THEN SELECT id INTO user_manager_shatinsi_id FROM users WHERE email = '<EMAIL>'; END IF;

    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at, created_at, updated_at)
    VALUES ('Admin ShaTinSi', '<EMAIL>', '+85299990003', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (email) DO NOTHING RETURNING id INTO user_admin_shatinsi_id;
    IF user_admin_shatinsi_id IS NULL THEN SELECT id INTO user_admin_shatinsi_id FROM users WHERE email = '<EMAIL>'; END IF;

    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at, created_at, updated_at)
    VALUES ('Admin BlueOrg', '<EMAIL>', '+85299990004', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (email) DO NOTHING RETURNING id INTO user_admin_blue_id;
    IF user_admin_blue_id IS NULL THEN SELECT id INTO user_admin_blue_id FROM users WHERE email = '<EMAIL>'; END IF;

    INSERT INTO users (display_name, email, phone, hashed_password, is_staff, email_verified_at, phone_verified_at, created_at, updated_at)
    VALUES ('Admin PinkOrg', '<EMAIL>', '+85299990005', hashed_password, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (email) DO NOTHING RETURNING id INTO user_admin_pink_id;
    IF user_admin_pink_id IS NULL THEN SELECT id INTO user_admin_pink_id FROM users WHERE email = '<EMAIL>'; END IF;

    -- Regular Users (these were phone-only and marked as not staff, so using phone as conflict target)
    INSERT INTO users (display_name, phone, hashed_password, phone_verified_at, created_at, updated_at)
    VALUES ('Regular User One', '+85299991001', hashed_password, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (phone) WHERE email IS NULL AND is_staff = false DO NOTHING RETURNING id INTO user_regular1_id;
    IF user_regular1_id IS NULL THEN SELECT id INTO user_regular1_id FROM users WHERE phone = '+85299991001' AND email IS NULL AND is_staff = false; END IF;

    INSERT INTO users (display_name, phone, hashed_password, phone_verified_at, created_at, updated_at)
    VALUES ('Regular User Two', '+85299991002', hashed_password, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (phone) WHERE email IS NULL AND is_staff = false DO NOTHING RETURNING id INTO user_regular2_id;
    IF user_regular2_id IS NULL THEN SELECT id INTO user_regular2_id FROM users WHERE phone = '+85299991002' AND email IS NULL AND is_staff = false; END IF;
    
    INSERT INTO users (display_name, phone, hashed_password, phone_verified_at, created_at, updated_at)
    VALUES ('Regular User Three', '+85299991003', hashed_password, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (phone) WHERE email IS NULL AND is_staff = false DO NOTHING RETURNING id INTO user_regular3_id;
    IF user_regular3_id IS NULL THEN SELECT id INTO user_regular3_id FROM users WHERE phone = '+85299991003' AND email IS NULL AND is_staff = false; END IF;

    -- 2. Re-seed Organizations (with original owners and image/theme from 000021)
    INSERT INTO organizations (name, description, owner_user_id, image_url, theme_color, created_at, updated_at)
    VALUES ('同行明天', '一個關心社區發展的組織', user_admin_tongxing_id, 'https://example.com/tongxing_logo.png', 'blue', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (name) DO NOTHING RETURNING id INTO org_tongxing_id;
    IF org_tongxing_id IS NULL THEN SELECT id INTO org_tongxing_id FROM organizations WHERE name = '同行明天'; END IF;

    INSERT INTO organizations (name, description, owner_user_id, image_url, theme_color, created_at, updated_at)
    VALUES ('沙田西關愛隊', '服務沙田西區居民', user_manager_shatinsi_id, 'https://example.com/shatinsi_logo.png', 'red', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (name) DO NOTHING RETURNING id INTO org_shatinsi_id;
    IF org_shatinsi_id IS NULL THEN SELECT id INTO org_shatinsi_id FROM organizations WHERE name = '沙田西關愛隊'; END IF;

    INSERT INTO organizations (name, description, owner_user_id, image_url, theme_color, created_at, updated_at)
    VALUES ('藍色主題組織', '以藍色為主題的活動', superadmin_id, 'https://example.com/blue_org_logo.png', 'blue', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (name) DO NOTHING RETURNING id INTO org_blue_id;
    IF org_blue_id IS NULL THEN SELECT id INTO org_blue_id FROM organizations WHERE name = '藍色主題組織'; END IF;

    INSERT INTO organizations (name, description, owner_user_id, image_url, theme_color, created_at, updated_at)
    VALUES ('玫紅色主題組織', '以玫紅色為主題的活動', superadmin_id, 'https://example.com/pink_org_logo.png', 'pink', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (name) DO NOTHING RETURNING id INTO org_pink_id;
    IF org_pink_id IS NULL THEN SELECT id INTO org_pink_id FROM organizations WHERE name = '玫紅色主題組織'; END IF;

    -- 3. Re-seed Memberships (referencing original structure from 000009)
    -- Ensure user IDs are not null before attempting to insert memberships

    IF user_admin_tongxing_id IS NOT NULL AND org_tongxing_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_admin_tongxing_id, org_tongxing_id, 'admin', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

    IF user_manager_shatinsi_id IS NOT NULL AND org_shatinsi_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_manager_shatinsi_id, org_shatinsi_id, 'admin', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

    IF user_admin_shatinsi_id IS NOT NULL AND org_shatinsi_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_admin_shatinsi_id, org_shatinsi_id, 'admin', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

    IF user_admin_blue_id IS NOT NULL AND org_blue_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_admin_blue_id, org_blue_id, 'admin', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

    IF superadmin_id IS NOT NULL AND org_blue_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (superadmin_id, org_blue_id, 'admin', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

    IF user_admin_pink_id IS NOT NULL AND org_pink_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_admin_pink_id, org_pink_id, 'admin', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

    IF superadmin_id IS NOT NULL AND org_pink_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (superadmin_id, org_pink_id, 'admin', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

    -- Regular user memberships (if they were re-created)
    IF user_regular1_id IS NOT NULL AND org_tongxing_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_regular1_id, org_tongxing_id, 'member', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;
    IF user_regular1_id IS NOT NULL AND org_blue_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_regular1_id, org_blue_id, 'member', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

    IF user_regular2_id IS NOT NULL AND org_shatinsi_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_regular2_id, org_shatinsi_id, 'member', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;
    
    IF user_regular3_id IS NOT NULL AND org_pink_id IS NOT NULL THEN
        INSERT INTO user_organization_memberships (user_id, organization_id, role, is_active, joined_at)
        VALUES (user_regular3_id, org_pink_id, 'member', true, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, organization_id) DO NOTHING;
    END IF;

END $$;

COMMIT; 