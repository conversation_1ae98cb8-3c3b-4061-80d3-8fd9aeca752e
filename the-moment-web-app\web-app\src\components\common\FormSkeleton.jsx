import React from 'react';
import { Skeleton } from 'antd';

/**
 * A reusable skeleton component for forms
 * @param {Object} props - Component props
 * @param {number} props.rows - Number of field rows to show (default: 4)
 * @param {number} props.titleHeight - Height of the title field (default: 40px)
 * @param {number} props.buttonCount - Number of buttons to show (default: 2)
 * @param {string} props.buttonWidth - Width of buttons (default: 120px)
 * @param {string} props.className - Additional CSS classes
 */
const FormSkeleton = ({ 
  rows = 4, 
  titleHeight = 40, 
  buttonCount = 2, 
  buttonWidth = '120px',
  className = ''
}) => {
  return (
    <div className={`space-y-8 ${className}`}>
      {/* Title field skeleton */}
      <Skeleton.Input 
        active 
        size="large" 
        style={{ width: '100%', height: `${titleHeight}px` }} 
      />

      {/* Content fields skeleton */}
      <div className="pt-4">
        <Skeleton active paragraph={{ rows }} />
      </div>

      {/* Buttons skeleton */}
      <div className="pt-6 flex justify-end space-x-4">
        {Array.from({ length: buttonCount }).map((_, index) => (
          <Skeleton.Button 
            key={index}
            active 
            size="large" 
            style={{ width: buttonWidth }} 
          />
        ))}
      </div>
    </div>
  );
};

export default FormSkeleton; 