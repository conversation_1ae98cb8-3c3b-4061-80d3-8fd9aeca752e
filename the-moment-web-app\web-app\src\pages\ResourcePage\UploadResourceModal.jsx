import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Upload, message, Radio, DatePicker, Button, Space } from 'antd';
import { InboxOutlined, SaveOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

const { Dragger } = Upload;

const ResourceFormModal = ({ visible, onCancel, onOk, initialData, mode = 'create', loading, orgId }) => {
    const [form] = Form.useForm();
    const [fileList, setFileList] = useState([]);
    const [removedFileIds, setRemovedFileIds] = useState([]);
    const { t } = useTranslation();
    const [editingFileId, setEditingFileId] = useState(null);
    const [publishButtonLoading, setPublishButtonLoading] = useState(false);
    const [draftButtonLoading, setDraftButtonLoading] = useState(false);

    useEffect(() => {
        if (visible) {
            if (initialData && mode === 'edit') {
                form.setFieldsValue({
                    title: initialData.title,
                    description: initialData.description,
                    visibility: initialData.visibility || 'org_only',
                    published_at: initialData.published_at ? dayjs(initialData.published_at) : null,
                });

                if (initialData.files && initialData.files.length > 0) {
                    const formattedFiles = initialData.files.map(file => ({
                        uid: file.id,
                        name: file.fileName || file.file_name,
                        status: 'done',
                        url: file.filePath || file.downloadUrl,
                        size: file.fileSize || file.file_size,
                        type: file.fileType || file.file_type,
                        description: file.description || '',
                        originFileObj: file.originFileObj || { type: `application/${file.fileType || file.file_type}`, size: parseFloat(file.fileSize || file.file_size) },
                        id: file.id // Store the ID for tracking
                    }));
                    setFileList(formattedFiles);
                    form.setFieldsValue({ fileUpload: formattedFiles });
                } else {
                    setFileList([]);
                }
            } else {
                form.resetFields();
                form.setFieldsValue({ visibility: 'org_only' });
                setFileList([]);
            }
            setEditingFileId(null);
            setRemovedFileIds([]);
        }
    }, [visible, initialData, form, mode]);

    const allowedTypes = [
        // Allow all file types
    ];

    const allowedExtensions = '*'; // Allow all file extensions
    const maxSize = 20 * 1024 * 1024;
    const maxFiles = 3;

    const handleDescriptionUpdate = (uid, newDescription) => {
        const updatedFileList = fileList.map(file => {
            if (file.uid === uid) {
                return { ...file, description: newDescription };
            }
            return file;
        });
        setFileList(updatedFileList);
        form.setFieldsValue({ fileUpload: updatedFileList });
        setEditingFileId(null);
    };

    const fileItemRender = (originNode, file) => {
        // Temporarily hide the description input box for each file
        const originContent = React.Children.toArray(originNode.props.children);
        const icon = originContent[0];
        const actions = originContent[2];

        return (
            <div className="flex flex-col w-full gap-2 py-2 rounded-lg px-3">
                <div className="flex items-center w-full gap-3">
                    <div className="flex-shrink-0">
                        {icon}
                    </div>

                    <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-gray-900 truncate flex-1">
                                {file.name}
                            </span>
                        </div>
                    </div>

                    <div className="flex-shrink-0 flex items-center">
                        {actions}
                    </div>
                </div>
                {/* Description input temporarily hidden */}
                {/* <div className="pl-8 w-full">
                    {isEditing ? (
                        <Input.TextArea
                            showCount
                            autoSize={{ minRows: 2, maxRows: 4 }}
                            defaultValue={file.description}
                            placeholder={t('uploadResourceModal.form.file.placeholder')}
                            maxLength={200}
                            className="w-full text-sm !rounded-md hover:border-blue-400 focus:border-blue-400 focus:shadow-sm focus:shadow-blue-200"
                            onBlur={(e) => handleDescriptionUpdate(file.uid, e.target.value)}
                            onPressEnter={(e) => {
                                if (!e.shiftKey) {
                                    e.preventDefault();
                                    handleDescriptionUpdate(file.uid, e.target.value);
                                }
                            }}
                            autoFocus
                        />
                    ) : (
                        <div
                            className={`
                                w-full min-h-[32px] px-3 py-1.5 rounded-md cursor-pointer
                                text-sm transition-all duration-200
                                ${file.description
                                    ? 'text-gray-700 hover:bg-gray-100'
                                    : 'text-gray-400 hover:text-gray-500 hover:bg-gray-50'
                                }
                                bg-gray-50
                            `}
                            onClick={() => setEditingFileId(file.uid)}
                        >
                            {file.description || t('uploadResourceModal.form.file.actions.clickToAdd')}
                        </div>
                    )}
                </div> */}
            </div>
        );
    };

    const uploadProps = {
        name: 'file',
        multiple: true,
        fileList,
        itemRender: fileItemRender,
        beforeUpload: (file, files) => {
            if (file.size > maxSize) {
                message.error(t('uploadResourceModal.validation.fileSize', { size: '20MB' }));
                return Upload.LIST_IGNORE;
            }

            const totalFiles = fileList.length + files.length;
            if (totalFiles > maxFiles) {
                message.error(t('uploadResourceModal.validation.fileCount', { count: maxFiles }));
                return Upload.LIST_IGNORE;
            }

            return false;
        },
        onChange: ({ fileList: newFileList }) => {
            const updatedFileList = newFileList.map(file => ({
                ...file,
                description: file.description || ''
            }));
            setFileList(updatedFileList);
        },
        onRemove: file => {
            const newFileList = fileList.filter(f => f.uid !== file.uid);
            setFileList(newFileList);
            if (editingFileId === file.uid) {
                setEditingFileId(null);
            }
            
            // Track removed file ID if it exists
            if (file.id || (typeof file.uid === 'string' && file.uid.match(/^\d+$/))) {
                const fileId = file.id || file.uid;
                setRemovedFileIds(prev => [...prev, fileId]);
            }
        }
    };

    const handleSubmit = async (intendedStatus) => {
        if (intendedStatus === 'published') {
            setPublishButtonLoading(true);
        } else if (intendedStatus === 'draft') {
            setDraftButtonLoading(true);
        }

        try {
            const values = await form.validateFields();
            const filesWithDescriptions = fileList.map(file => ({
                uid: file.uid,
                name: file.name,
                status: file.status,
                url: file.url,
                size: file.size,
                type: file.type,
                description: file.description || '',
                originFileObj: file.originFileObj,
                id: (typeof file.uid === 'string' && !file.originFileObj) ? file.uid : undefined
            }));

            let finalPublishedAt;
            if (intendedStatus === 'published') {
                finalPublishedAt = new Date().toISOString();
            } else { // 'draft'
                finalPublishedAt = values.published_at ? values.published_at.toISOString() : null;
            }

            const submissionData = {
                ...values,
                files: filesWithDescriptions,
                published_at: finalPublishedAt,
                removedFileIds: removedFileIds
            };
            
            await onOk(submissionData, intendedStatus);
        } catch (error) {
            console.error('Validation failed or error during onOk:', error);
            // Specific button loading states are reset in the finally block
        } finally {
            if (intendedStatus === 'published') {
                setPublishButtonLoading(false);
            } else if (intendedStatus === 'draft') {
                setDraftButtonLoading(false);
            }
        }
    };

    const handlePublishClick = () => {
        // Get current form values to build the confirmation message
        const currentValues = form.getFieldsValue(['visibility', 'published_at']);
        const { visibility } = currentValues;
        // published_at could be null or a dayjs object
        const publishedAtValue = currentValues.published_at;

        let confirmationDetails = [];
        const baseContent = t('uploadResourceModal.confirmPublish.contentBase');

        if (visibility === 'public') {
            confirmationDetails.push(t('uploadResourceModal.confirmPublish.details.publicVisibility'));
        } else if (visibility === 'org_only') {
            confirmationDetails.push(t('uploadResourceModal.confirmPublish.details.orgVisibility'));
        }

        if (publishedAtValue && publishedAtValue.isAfter(dayjs().add(1, 'minute'))) { // Check if it's a future schedule
            confirmationDetails.push(t('uploadResourceModal.confirmPublish.details.scheduled', { publishTime: publishedAtValue.format('YYYY-MM-DD HH:mm') }));
        } else {
            confirmationDetails.push(t('uploadResourceModal.confirmPublish.details.immediatePublish'));
        }

        const fullContent = [
            baseContent,
            ...confirmationDetails
        ].join('\n');

        Modal.confirm({
            title: t('uploadResourceModal.confirmPublish.title'),
            content: (<div style={{ whiteSpace: 'pre-line' }}>{fullContent}</div>),
            okText: t('common.actions.publish'),
            cancelText: t('common.actions.cancel'),
            onOk: () => {
                handleSubmit('published');
            },
            onCancel: () => {
                // No action needed, loading states are handled in handleSubmit's finally
            },
        });
    };

    const modalFooter = [
        <Button key="cancel" onClick={() => {
            onCancel();
        }}>
            {t('common.actions.cancel')}
        </Button>,
        <Button icon={<SaveOutlined />} key="draft" loading={draftButtonLoading} onClick={() => {
            handleSubmit('draft');
        }}>
            {t('uploadResourceModal.buttons.saveAsDraft')}
        </Button>,
        <Button key="publish" type="primary" loading={publishButtonLoading} onClick={handlePublishClick}>
            {t('uploadResourceModal.buttons.publish')}
        </Button>,
    ];

    const disabledDate = (current) => {
        // Can not select days before today
        return current && current < dayjs().startOf('day');
    };

    const disabledTime = (date) => {
        const now = dayjs();
        // Add 1 minute because the check is <, so 2 minutes from now means it should be greater than now + 1 minute.
        // For example, if now is 10:00:00, min publish time is 10:02:00.
        // User should be able to select 10:02.
        // disabledMinutes should disable up to 10:01.
        const minPublishTime = now.add(1, 'minute');


        if (date && date.isSame(now, 'day')) { // If selected date is today
            return {
                disabledHours: () => {
                    const hours = [];
                    for (let i = 0; i < minPublishTime.hour(); i++) {
                        hours.push(i);
                    }
                    return hours;
                },
                disabledMinutes: (selectedHour) => {
                    if (selectedHour === minPublishTime.hour()) {
                        const minutes = [];
                        for (let i = 0; i <= minPublishTime.minute(); i++) { // Disable up to and including the minute before the 2-min mark
                            minutes.push(i);
                        }
                        return minutes;
                    } else if (selectedHour < minPublishTime.hour()) {
                        return Array.from({length: 60}, (_, i) => i); // All minutes disabled for past hours
                    }
                    return [];
                },
            };
        }
        return {}; // No restrictions for future dates or if date is not set
    };

    return (
        <Modal
            title={mode === 'create' ? t('uploadResourceModal.title.create') : t('uploadResourceModal.title.edit', { resourceName: initialData?.title || 'Resource' })}
            open={visible}
            onCancel={() => {
                onCancel();
            }}
            footer={modalFooter}
            width={680}
            centered={false}
            style={{ top: 20 }} // Adjust this value as needed
            maskClosable={false}
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    name="title"
                    label={t('uploadResourceModal.form.title.label')}
                    rules={[
                        { required: true, message: t('uploadResourceModal.form.title.rules.required') },
                        { max: 100, message: t('uploadResourceModal.form.title.rules.maxLength') }
                    ]}
                >
                    <Input placeholder={t('uploadResourceModal.form.title.placeholder')} />
                </Form.Item>

                <Form.Item
                    name="description"
                    label={t('uploadResourceModal.form.description.label')}
                    rules={[
                        { max: 500, message: t('uploadResourceModal.form.description.rules.maxLength') }
                    ]}
                >
                    <Input.TextArea
                        placeholder={t('uploadResourceModal.form.description.placeholder')}
                        autoSize={{ minRows: 3, maxRows: 6 }}
                        showCount
                        maxLength={200}
                    />
                </Form.Item>

                <Form.Item
                    name="visibility"
                    label={t('uploadResourceModal.form.visibility.label')}
                    rules={[{ required: true, message: t('uploadResourceModal.form.visibility.rules.required') }]}
                    initialValue="org_only"
                >
                    <Radio.Group>
                        <Radio value="public">{t('uploadResourceModal.form.visibility.options.public')}</Radio>
                        <Radio value="org_only">{t('uploadResourceModal.form.visibility.options.org_only')}</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item
                    label={t('uploadResourceModal.form.published_at.label')}
                    name="published_at"
                >
                    <DatePicker
                        showTime
                        needConfirm={false}
                        format="YYYY-MM-DD HH:mm"
                        showNow={false}
                        placeholder={t('uploadResourceModal.form.published_at.placeholder')}
                        style={{ width: '100%' }}
                        disabledDate={disabledDate}
                        disabledTime={disabledTime}
                    />
                </Form.Item>

                <Form.Item
                    name="fileUpload"
                    label={t('uploadResourceModal.form.file.label')}
                    rules={[{ required: true, message: t('uploadResourceModal.form.file.rules.required') }]}
                    valuePropName="fileList"
                    getValueFromEvent={e => {
                        if (Array.isArray(e)) {
                            return e;
                        }
                        return e?.fileList;
                    }}
                >
                    <Dragger {...uploadProps}>
                        <p className="ant-upload-drag-icon">
                            <InboxOutlined />
                        </p>
                        <p className="ant-upload-text">
                            {t('uploadResourceModal.form.file.dragText')}
                        </p>
                        <p className="ant-upload-hint">
                            {t('uploadResourceModal.form.file.hint.limits', { size: '20MB', count: maxFiles })}
                        </p>
                    </Dragger>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default ResourceFormModal;