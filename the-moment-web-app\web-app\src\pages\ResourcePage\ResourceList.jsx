import React, { useState, useEffect, useCallback } from 'react';
import { List, Card, Input, Space, Button, Dropdown, Modal, message, Popover, Badge, Typography, Popconfirm, Empty, Spin, Select, Tag } from 'antd';
import {
    FileWordOutlined,
    FilePdfOutlined,
    FileExcelOutlined,
    SearchOutlined,
    DownloadOutlined,
    FileTextOutlined,
    FileZipOutlined,
    UploadOutlined,
    MoreOutlined,
    EditOutlined,
    DeleteOutlined,
    UpOutlined,
    DownOutlined,
    RightOutlined,
    // FilterOutlined // Consider if a general filter icon is needed alongside specific filters
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useOrganization, ALL_ORGANIZATION_ID } from '../../contexts/OrganizationContext';
import { getFileIcon } from '../../config/fileIconConfig';
import { useTranslation } from 'react-i18next';
import '../../styles/ResourceList.css';
import UploadResourceModal from './UploadResourceModal';
import { formatShortDatetime } from '../../utils/dateFormatter';
import { resourceService } from '../../services/resourceService';
import { organizationService } from '../../services/organizationService';
import { useNavigate } from 'react-router-dom';

const truncateMiddle = (str, startLen = 20, endLen = 8) => {
    if (str.length <= startLen + endLen) return str;
    return `${str.slice(0, startLen)}...${str.slice(-endLen)}`;
};
// Format file sizze display, "0.8 MB"
const formatFileSize = (sizeInBytes) => {
    if (sizeInBytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(sizeInBytes) / Math.log(k));
    const size = parseFloat((sizeInBytes / Math.pow(k, i)).toFixed(2));
    // Remove trailing zeros after decimal point
    const sizeString = size.toString();
    const finalSize = sizeString.includes('.')
        ? sizeString.replace(/\.0+$/, '').replace(/(\.\d*?)0+$/, '$1') // Remove trailing zeros, and . if it becomes .0 or .00
        : sizeString;
    return `${finalSize} ${sizes[i]}`;
};

// Add new component for file description with quote bar
const FileDescription = React.memo(({ description }) => {
    const { t } = useTranslation();
    if (!description) return null;

    return (
        <div className="file-description mt-2 pl-3 relative">
            <div className="quote-bar absolute left-0 top-0 bottom-0 w-1 bg-gray-300 rounded-full"></div>
            <Typography.Paragraph
                ellipsis={{
                    rows: 2,
                    expandable: 'collapsible',
                    symbol: (expanded) => expanded ? t('resourceList.description.collapse', '收起') : t('resourceList.description.expand', '展開'),
                    onExpand: (e) => {
                        if (e) e.stopPropagation();
                    }
                }}
            >
                {description}
            </Typography.Paragraph>
        </div>
    );
});

// Helper function to trigger file download from a blob
const triggerBlobDownload = (blobData, fileName) => {
    try {
        const url = window.URL.createObjectURL(blobData); // blobData should be a Blob object
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        link.parentNode.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error("Error in triggerBlobDownload:", error);
        message.error('Failed to initiate download process.');
    }
};

// Function to generate popover content with staggered animation
const generateFileListPopover = (files, resourceItem, onFileClick, canDeleteFiles, orgIdForDelete, resourceIdForDelete, deleteFileHandler, t) => {
    // Add a guard clause for undefined or empty files array
    if (!Array.isArray(files) || files.length === 0) {
        return <div style={{ padding: '8px 12px' }}>{t('resourceList.noFilesAttached')}</div>;
    }

    return (
        <div className="file-list-popover overflow-y-auto">
            {files.map((file, index) => (
                <div
                    key={file.id}
                    className="file-item mb-3 p-3 hover:bg-gray-50 rounded-md transition-colors border border-gray-100"
                    style={{ '--item-index': index }}
                    onClick={(e) => {
                        e.stopPropagation();
                        onFileClick(file, resourceItem);
                    }}
                >
                    <div className="flex justify-between items-start">
                        <div
                            className="flex items-start space-x-3 flex-1 min-w-0 cursor-pointer"
                        >
                            <div className="flex-shrink-0 mt-1">{getFileIcon(file.file_type)}</div>
                            <div className="flex-1 min-w-0">
                                <div className="file-name font-medium text-gray-800 break-all">
                                    {truncateMiddle(file.file_name, 70)}
                                </div>
                                <div className="file-meta flex items-center mt-1">
                                    <span className="file-type text-gray-500 text-xs uppercase">
                                        {file.file_type}
                                    </span>
                                    <span className="mx-1 text-gray-300">•</span>
                                    <span className="text-gray-500 text-xs">
                                        {formatFileSize(file.file_size)}
                                    </span>
                                </div>
                                <FileDescription description={file.description || ''} />
                            </div>
                        </div>
                        {canDeleteFiles && (
                            <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    deleteFileHandler(orgIdForDelete, resourceIdForDelete, file.id, file.file_name);
                                }}
                                size="small"
                                className="ml-2 flex-shrink-0"
                                title={t('resourceList.actions.deleteFile')}
                            />
                        )}
                    </div>
                </div>
            ))}
        </div>
    );
};

// Memoized download item component for better performance
const DownloadItem = React.memo(({ file, resourceTitle, onDelete }) => (
    <div className="download-item px-4 py-2 flex justify-between items-center hover:bg-gray-50">
        <div className="download-item-info flex-1 overflow-hidden">
            <Space>
                <span className="flex-shrink-0">{getFileIcon(file.file_type)}</span>
                <div className="download-item-title text-gray-800 truncate max-w-xs">
                    {truncateMiddle(file.file_name, 40)}
                </div>
                <div className="download-item-size text-gray-500 text-sm">
                    {formatFileSize(file.file_size)}
                </div>
            </Space>
        </div>
        <Button
            type="text"
            icon={<DeleteOutlined className="text-red-500 hover:text-red-600" />}
            onClick={() => onDelete(file)}
            className="flex-shrink-0"
        />
    </div>
));

const DownloadGroup = React.memo(({ resourceTitle, files, onDelete }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const { t } = useTranslation();

    const totalSize = files.reduce((sum, file) => {
        return sum + file.file_size;
    }, 0);

    // Add function to handle group deletion
    const handleDeleteGroup = (e) => {
        e.stopPropagation(); // Prevent toggling expansion when clicking delete
        // Call onDelete with special parameter to indicate entire group deletion
        onDelete('__DELETE_GROUP__'); // For group deletion, pass the marker and resourceTitle
    };

    return (
        <div className="download-group">
            <div
                className="download-group-header"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="download-group-title-wrapper">
                    <RightOutlined
                        className={`download-group-arrow ${isExpanded ? 'expanded' : ''}`}
                    />
                    <div className="download-group-title">{resourceTitle}</div>
                </div>
                <div className="download-group-summary">
                    <Space>
                        {t('resourceList.batchDownload.fileCount', { count: files.length, size: formatFileSize(totalSize) })}
                        {/* Add delete group button */}
                        <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={handleDeleteGroup}
                            size="small"
                        />
                    </Space>
                </div>
            </div>
            <div className={`download-group-content ${isExpanded ? 'expanded' : ''}`}>
                {files.map((file) => (
                    <DownloadItem
                        key={file.id}
                        file={file}
                        resourceTitle={resourceTitle}
                        onDelete={onDelete}
                    />
                ))}
            </div>
        </div>
    );
});

const ResourceList = () => {
    const { user, isLoggedIn } = useAuth();
    const { currentOrganization } = useOrganization();
    const isAdmin = user?.role === 'admin' || user?.role === 'super_admin';
    const { t, i18n } = useTranslation();
    const currentLanguage = i18n.language;
    const navigate = useNavigate();

    const [resources, setResources] = useState([]);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [total, setTotal] = useState(0);
    const pageSize = 24;

    // Extract organizationId, simplifying logic
    const organizationId = currentOrganization ? currentOrganization.id : ALL_ORGANIZATION_ID;

    // Other states remain unchanged
    const [expandedDescriptions, setExpandedDescriptions] = useState({});
    const [modalLoading, setModalLoading] = useState(false);
    const [modalData, setModalData] = useState({
        visible: false,
        mode: 'create',
        initialData: null,
        orgId: null
    });
    const [selectedItems, setSelectedItems] = useState([]);
    const [selectionMode, setSelectionMode] = useState(false);
    const [isExpanded, setIsExpanded] = useState(false);
    const [loginModalOpen, setLoginModalOpen] = useState(false);

    // Debounce search term
    useEffect(() => {
        const timerId = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 500);

        return () => clearTimeout(timerId);
    }, [searchTerm]);

    // Data fetching function - depends on minimal necessary external variables
    const fetchResources = useCallback(async (orgIdToFetch, search, page) => {
        if (!orgIdToFetch && orgIdToFetch !== ALL_ORGANIZATION_ID) {
            console.warn("ResourceList: No organization ID provided for fetching resources.");
            setLoading(false);
            setResources([]);
            setTotal(0);
            return;
        }

        console.log('Fetching resources:', { orgIdToFetch, search, page });
        setLoading(true);

        const limit = pageSize;
        const offset = (page - 1) * pageSize;
        const commonApiParams = { 
            limit, 
            offset, 
            searchTerm: search 
        };

        // Determine if the current user is a super admin
        const isSuperAdmin = user?.role === 'super_admin';

        // Add organization_id params using the same logic as EventList.js and PostsList.js
        if (orgIdToFetch) {
            if (orgIdToFetch === ALL_ORGANIZATION_ID) {
                if (isSuperAdmin) {
                    // For super_admin, if "All Organizations" is selected, pass the ALL_ORGANIZATION_ID
                    commonApiParams.organization_id = ALL_ORGANIZATION_ID;
                }
                // For other roles, don't add organization_id
            } else {
                // A specific organization is selected
                commonApiParams.organization_id = orgIdToFetch;
                
                // For non-super admin users, add organization_id2 to include resources from ALL_ORGANIZATION_ID as well
                if (!isSuperAdmin) {
                    commonApiParams.organization_id2 = ALL_ORGANIZATION_ID;
                }
            }
        }

        let fetchedResources = [];
        let totalResources = 0;
        let baseResources = [];

        try {
            // Use resourceService.listPublicResources instead of dual-logic approach
            const response = await resourceService.listPublicResources(commonApiParams);
            baseResources = response.resources || [];
            totalResources = response.total || 0;

            if (baseResources.length > 0) {
                const detailPromises = baseResources.map(resource =>
                    // Assuming resource.organization_id is present in both public and org resource listings.
                    // If public resources don't have organization_id, this detail call might fail or need adjustment.
                    organizationService.getOrgResourceDetail(resource.organization_id, resource.id)
                        .then(detailResponse => detailResponse.data || detailResponse)
                        .catch(err => {
                            console.error(`Failed to fetch details for resource ${resource.id} from org ${resource.organization_id}:`, err);
                            return { ...resource, files: [], errorFetchingDetails: true };
                        })
                );
                const detailedResourcesWithFiles = await Promise.all(detailPromises);
                fetchedResources = detailedResourcesWithFiles.filter(r => r !== null);
            } else {
                fetchedResources = [];
            }

            setResources(fetchedResources);
            setTotal(totalResources); // Use total from initial list call, before client-side detail fetching/filtering

        } catch (error) {
            console.error('Error fetching resources:', error);
            message.error(t('messages.fetchError', 'Failed to fetch resources.')); // Added default message
            setResources([]);
            setTotal(0);
        } finally {
            setLoading(false);
        }
        // Key dependencies:
        // - pageSize: For pagination.
        // - t: For translations.
    }, [pageSize, t, user?.role]);

    // Main data fetching effect - handles three scenarios
    useEffect(() => {
        // Fetch resources when search or page changes
        fetchResources(organizationId, debouncedSearchTerm, currentPage);
    }, [organizationId, debouncedSearchTerm, currentPage, fetchResources]);

    // Delete resource
    const handleDelete = async (resourceItem) => {
        const resourceId = resourceItem.id;
        const resourceOrgId = resourceItem.organization_id;

        Modal.confirm({
            title: t('resourceList.messages.deleteResourceConfirmTitle', { resourceName: truncateMiddle(resourceItem.title, 50) }),
            content: t('resourceList.messages.deleteResourceConfirmContent'),
            okText: t('common.actions.delete'),
            okType: 'danger',
            cancelText: t('common.actions.cancel'),
            onOk: async () => {
                try {
                    if (isAdmin && resourceOrgId) {
                        await organizationService.deleteResource(resourceOrgId, resourceId);
                    } else if (isAdmin) { // Fallback for global or general admin delete
                        await resourceService.deleteResource(resourceId);
                    } else {
                        message.error(t('resourceList.messages.unauthorized'));
                        return;
                    }
                    message.success(t('resourceList.messages.deleteSuccess'));

                    // After successful deletion, check if we need to adjust the page
                    if (resources.length === 1 && currentPage > 1) {
                        setCurrentPage(prevPage => prevPage - 1); // useEffect will fetch the new page
                    } else {
                        // Re-fetch current page; use the same status logic as useEffect
                        fetchResources(organizationId, debouncedSearchTerm, currentPage);
                    }
                } catch (error) {
                    console.error('Error deleting resource:', error);
                    message.error(t('resourceList.messages.deleteFailed'));
                }
            },
        });
    };

    const handleDeleteFile = async (orgId, resourceId, fileId, fileName) => {
        if (!isAdmin || !orgId) {
            message.error(t('resourceList.messages.unauthorized'));
            return;
        }
        Modal.confirm({
            title: t('resourceList.messages.deleteFileConfirmTitle'),
            content: t('resourceList.messages.deleteFileConfirmContent', { fileName: truncateMiddle(fileName) }),
            okText: t('common.actions.delete'),
            okType: 'danger',
            cancelText: t('common.actions.cancel'),
            onOk: async () => {
                try {
                    await organizationService.deleteResourceFile(orgId, resourceId, fileId);
                    message.success(t('resourceList.messages.deleteFileSuccess', { fileName: truncateMiddle(fileName) }));
                    fetchResources(organizationId, debouncedSearchTerm, currentPage);
                } catch (error) {
                    console.error(`Error deleting file ${fileId}:`, error);
                    message.error(t('resourceList.messages.deleteFileFailed', { fileName: truncateMiddle(fileName) }));
                }
            },
        });
    };

    const handleUpload = () => {
        if (!currentOrganization) {
            message.warning(t('resourceList.messages.selectOrgForUpload'));
            return;
        }
        setModalData({
            visible: true,
            mode: 'create',
            initialData: null,
            orgId: currentOrganization.id
        });
    };

    const handleEdit = (item) => {
        if (!currentOrganization) {
            message.warning(t('resourceList.messages.selectOrgForEdit'));
            return;
        }
        // Ensure editing happens in the context of the resource's own org or current if it matches
        const orgContextId = item.organization_id === currentOrganization.id ? currentOrganization.id : null;
        if (!orgContextId && isAdmin) { // Super admin might edit across orgs if API supports it, but file ops need specific org
            message.info(t('resourceList.messages.editContextWarning'));
        }

        setModalData({
            visible: true,
            mode: 'edit',
            initialData: item,
            orgId: item.organization_id || currentOrganization.id
        });
    };

    const handleModalComplete = async (values, intendedStatus) => {
        setModalLoading(true);
        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 500));
        const isEdit = modalData.mode === 'edit';
        let resourceId = isEdit && modalData.initialData ? modalData.initialData.id : null;
        const orgId = modalData.orgId || organizationId || currentOrganization?.id;
        let resourceResponseData = null;

        try {
            if (!orgId) {
                throw new Error('Missing organization ID for resource operation');
            }

            if (isEdit && !resourceId) {
                throw new Error('Missing resource ID for edit operation');
            }

            if (isEdit) {
                const updatePayload = {
                    title: values.title,
                    description: values.description,
                    visibility: values.visibility,
                    status: intendedStatus,
                };
                if (intendedStatus === 'published') {
                    if (values.published_at) {
                        updatePayload.published_at = values.published_at;
                    } else if (modalData.initialData?.status !== 'published') {
                        updatePayload.published_at = new Date().toISOString();
                    }
                } else {
                    updatePayload.published_at = null;
                }

                const response = await organizationService.updateResource(orgId, resourceId, updatePayload);
                resourceResponseData = response;
            } else {
                const createPayload = {
                    title: values.title,
                    description: values.description,
                    visibility: values.visibility,
                    status: intendedStatus,
                    published_at: intendedStatus === 'published'
                        ? (values.published_at || new Date().toISOString())
                        : null,
                };
                const response = await organizationService.createResource(orgId, createPayload);
                resourceResponseData = response;
                if (resourceResponseData && resourceResponseData.id) {
                    resourceId = resourceResponseData.id;
                } else {
                    console.error('[ModalComplete] Create resource response did not include an ID or was not structured as expected. Response was:', resourceResponseData);
                    message.error(t('resourceList.messages.createFailedNoId'));
                    setModalLoading(false);
                    return;
                }
            }

            if (resourceId) {
                const uploadedFilesInfo = [];
                
                // Handle explicit file removal
                if (isEdit && modalData.initialData && modalData.initialData.files) {
                    const currentFileUids = values.files.map(f => f.uid ? f.uid.toString() : null).filter(Boolean);
                    const removedFileIds = values.removedFileIds || [];
                    
                    // Check for files to delete - either not in current IDs or explicitly removed
                    for (const initialFile of modalData.initialData.files) {
                        const fileId = initialFile.id.toString();
                        if (!currentFileUids.includes(fileId) || removedFileIds.includes(fileId)) {
                            try {
                                await organizationService.deleteResourceFile(orgId, resourceId, initialFile.id);
                                message.info(t('resourceList.messages.fileRemoved', { fileName: truncateMiddle(initialFile.file_name || initialFile.fileName) }));
                            } catch (deleteError) {
                                console.error(`Failed to delete file ${initialFile.id}:`, deleteError);
                                message.error(t('resourceList.messages.deleteFileFailed', { fileName: truncateMiddle(initialFile.file_name || initialFile.fileName) }));
                            }
                        }
                    }
                }

                for (const file of values.files) {
                    if (file.originFileObj instanceof File) {
                        try {
                            const uploadResponse = await organizationService.uploadResourceFile(orgId, resourceId, file.originFileObj);
                            uploadedFilesInfo.push({ ...uploadResponse.data, localName: file.name });
                            message.success(t('resourceList.messages.fileUploadSuccess', { fileName: truncateMiddle(file.name) }));
                        } catch (uploadError) {
                            console.error(`[ModalComplete] FAILED to upload file ${file.name}:`, uploadError.response?.data || uploadError.message || uploadError);
                            message.error(t('resourceList.messages.fileUploadFailed', { fileName: truncateMiddle(file.name) }));
                        }
                    } else {
                        console.log(`[ModalComplete] Skipping file (not a new upload or not a File instance): ${file.name}`);
                    }
                }
            }

            await minLoadingTime;

            message.success(t(`resourceList.messages.${isEdit ? 'updateSuccess' : 'uploadSuccess'}` + (intendedStatus === 'draft' ? 'Draft' : '')));
            setModalData({ visible: false, mode: 'create', initialData: null, orgId: null });
            fetchResources(organizationId, debouncedSearchTerm, currentPage);
        } catch (error) {
            let mainMessage = t(`resourceList.messages.${isEdit ? 'updateFailed' : 'uploadFailed'}`);
            if (error.response && error.response.data && error.response.data.message) {
                mainMessage += `: ${error.response.data.message}`;
            } else if (error.message) {
                mainMessage += `: ${error.message}`;
            } else if (typeof error === 'string') {
                mainMessage += `: ${error}`;
            }
            message.error(mainMessage);
            console.error('[ModalComplete] Error in modal complete (final catch):', error, error?.response?.data);
        } finally {
            setModalLoading(false);
        }
    };

    const handleDownloadAllFilesForResource = async (resourceItem, e) => {
        if (e) e.stopPropagation();
        if (!resourceItem || !resourceItem.files || resourceItem.files.length === 0) {
            message.info(t('resourceList.noFilesToDownload'));
            return;
        }
        message.info(t('resourceList.messages.downloadAllFilesStarting', { count: resourceItem.files.length }));
        for (const file of resourceItem.files) {
            // Adding a small delay between downloads can be browser-friendly
            await new Promise(resolve => setTimeout(resolve, 300));
            await handleFileDownload(file, resourceItem);
        }
    };

    // Group items by their resource title and include all files
    const groupedItems = selectedItems.reduce((groups, item) => {
        const group = groups[item.title] || [];
        // Add a check for item.files before calling forEach
        (item.files || []).forEach(file => {
            group.push({
                ...file,
                resourceId: item.id,
                organizationId: item.organization_id,
                resourceTitle: item.title
            });
        });
        groups[item.title] = group;
        return groups;
    }, {});

    const handleSelect = (item) => {
        // If not logged in, show login prompt modal
        if (!isLoggedIn) {
            setLoginModalOpen(true);
            return;
        }
        
        setSelectedItems(prev => {
            // Check if item is already selected
            const isAlreadySelected = prev.some(selected => selected.id === item.id);

            // If already selected, remove it; otherwise, add it
            const newSelectedItems = isAlreadySelected
                ? prev.filter(selected => selected.id !== item.id)
                : [...prev, item];

            // Automatically set selectionMode based on whether there are selected items
            setSelectionMode(newSelectedItems.length > 0);

            return newSelectedItems;
        });
    };

    // Handle batch download
    const handleBatchDownload = async () => {
        if (selectedItems.length === 0) {
            message.info(t('resourceList.batchDownload.noItemsSelected'));
            return;
        }

        let totalFilesToDownload = 0;
        selectedItems.forEach(item => {
            totalFilesToDownload += (item.files || []).length;
        });

        if (totalFilesToDownload === 0) {
            message.info(t('resourceList.batchDownload.noFilesSelected'));
            return;
        }

        message.info(t('resourceList.batchDownload.startingFull', { count: totalFilesToDownload }));

        for (const item of selectedItems) {
            if (item.files && item.files.length > 0) {
                for (const file of item.files) {
                    // Adding a small delay
                    await new Promise(resolve => setTimeout(resolve, 300));
                    await handleFileDownload(file, item);
                }
            }
        }
        // Deselect items after attempting download
        // setSelectionMode(false); // Keep selection mode for now, user might want to retry failed ones or deselect manually
        // setSelectedItems([]);
    };

    // Add handleFileDownload function
    const handleFileDownload = async (fileToDownload, resourceContext) => {
        if (!fileToDownload || !resourceContext) {
            message.error(t('resourceList.messages.downloadErrorMissingInfo'));
            return;
        }

        const fileName = fileToDownload.file_name || fileToDownload.fileName || 'downloadedFile';
        // const userRole = user?.role || 'guest'; // No longer needed for role-based download service call
        // const isAdminUser = userRole === 'admin' || userRole === 'super_admin'; // No longer needed

        message.loading({ content: t('resourceList.messages.downloadStartingFor', { fileName: truncateMiddle(fileName) }), key: `download-${fileToDownload.id}`, duration: 0 });

        try {
            // Always use resourceService.downloadResourceFile
            // It expects (organizationId, resourceIdOrSlug, fileIdOrName)
            // Assuming resourceContext.id can serve as resourceIdOrSlug. If slug is preferred and available, adjust.
            console.log(`Downloading file: orgId=${resourceContext.organization_id}, resourceId/Slug=${resourceContext.id}, fileId=${fileToDownload.id}`);
            const response = await resourceService.downloadResourceFile(
                resourceContext.organization_id,
                resourceContext.id, // Using resource ID as resourceIdOrSlug
                fileToDownload.id
            );

            if (response && response.data) { // Assuming response is Axios-like with data as Blob
                triggerBlobDownload(response.data, fileName);
                message.success({ content: t('resourceList.messages.downloadSuccessFor', { fileName: truncateMiddle(fileName) }), key: `download-${fileToDownload.id}`, duration: 3 });
            } else if (response instanceof Blob) { // If service directly returns a Blob
                triggerBlobDownload(response, fileName);
                message.success({ content: t('resourceList.messages.downloadSuccessFor', { fileName: truncateMiddle(fileName) }), key: `download-${fileToDownload.id}`, duration: 3 });
            }

            else {
                throw new Error('Invalid response data for file download.');
            }
        } catch (error) {
            console.error(`Error downloading file ${fileName}:`, error);
            message.error({ content: t('resourceList.messages.downloadFailedFor', { fileName: truncateMiddle(fileName) }), key: `download-${fileToDownload.id}`, duration: 3 });
        }
    };

    return (
        <div className="py-6 px-4 md:px-6 bg-white">
            <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
                <div className="w-full md:w-auto flex-grow">
                    <Input
                        placeholder={t('resourceList.search.placeholder')}
                        prefix={<SearchOutlined />}
                        onChange={e => setSearchTerm(e.target.value)}
                        style={{ width: '100%', maxWidth: 300 }}
                        size='large'
                        allowClear
                    />
                </div>
            </div>

            {loading ? (
                <div className="flex justify-center items-center min-h-[400px] w-full">
                    <Spin size="large" />
                </div>
            ) : !resources.length ? (
                <div className="flex justify-center items-center min-h-[400px] w-full bg-gray-50 rounded-lg">
                    <Empty />
                </div>
            ) : (
                <List
                    grid={{
                        gutter: 16,
                        xxl: 4, // Extra large screens
                        xl: 4,  // Large screens
                        lg: 3,   // Medium screens
                        md: 2,   // Small screens
                        sm: 2,   // Extra small screens
                        xs: 1,   // Mobile screens
                    }}
                    dataSource={resources}
                    pagination={{
                        current: currentPage,
                        pageSize: pageSize,
                        total: total,
                        onChange: (page) => setCurrentPage(page),
                        showSizeChanger: false,
                        align: 'center',
                        showTotal: total => t('common.totalItems', { total: total })
                    }}
                    renderItem={item => {
                        // Logic for status ribbon (Draft)
                        let statusRibbonText = '';
                        let statusRibbonColor = '';
                        if (item.status === 'draft') {
                            statusRibbonText = t('resourceList.status.draft', 'Draft');
                            statusRibbonColor = 'gold';
                        }

                        // Visibility will be handled by inline tags, so no visibilityRibbonText here

                        const cardComponent = (
                            <Card
                                className={`resource-card ${selectedItems.some(selected => selected.id === item.id) ? 'selected' : ''} hover:shadow-md transition-shadow`}
                                onClick={() => handleSelect(item)}
                                actions={isAdmin && currentOrganization &&
                                    // Hide admin actions for non-super_admin when resource belongs to ALL_ORGANIZATION_ID
                                    !(item.organization_id === ALL_ORGANIZATION_ID && user?.role !== 'super_admin') ? [
                                    // Admin mode: show 3 clear icon actions
                                    <Popover
                                        key="download"
                                        content={generateFileListPopover(
                                            item.files || [],
                                            item,
                                            handleFileDownload,
                                            isAdmin && currentOrganization && item.organization_id === currentOrganization.id,
                                            item.organization_id,
                                            item.id,
                                            handleDeleteFile,
                                            t
                                        )}
                                        trigger="hover"
                                        placement="bottom"
                                        overlayClassName="file-popover-overlay"
                                    >
                                        <DownloadOutlined onClick={(e) => handleDownloadAllFilesForResource(item, e)} />
                                    </Popover>,
                                    <EditOutlined key="edit" onClick={(e) => {
                                        e.stopPropagation();
                                        handleEdit(item);
                                    }} />,
                                    <Popconfirm
                                        key="delete"
                                        title={t('resourceList.messages.deleteConfirm')}
                                        onConfirm={(e) => {
                                            e.stopPropagation();
                                            handleDelete(item);
                                        }}
                                        onCancel={e => e.stopPropagation()}
                                    >
                                        <DeleteOutlined onClick={e => e.stopPropagation()} />
                                    </Popconfirm>
                                ] : isLoggedIn ? [
                                    // Non-admin but logged in mode: only show download with file info
                                    (item.files || []).length > 1 ? (
                                        <Popover
                                            key="files"
                                            content={generateFileListPopover(
                                                item.files || [],
                                                item,
                                                handleFileDownload,
                                                false,
                                                null,
                                                null,
                                                null,
                                                t
                                            )}
                                            trigger="hover"
                                            placement="bottom"
                                            overlayClassName="file-popover-overlay"
                                        >
                                            <Space
                                                size={4}
                                                onClick={(e) => handleDownloadAllFilesForResource(item, e)}
                                                className="download-all-button"
                                            >
                                                <DownloadOutlined />
                                                <span className="file-info text-gray-400 font-medium">
                                                    {(item.files || []).length} files
                                                </span>
                                            </Space>
                                        </Popover>
                                    ) : (item.files || []) && (item.files || []).length === 1 ? (
                                        <Space
                                            size={4}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleFileDownload((item.files || [])[0], item);
                                            }}
                                        >
                                            <DownloadOutlined />
                                            <span className="file-info text-gray-400 font-medium">
                                                {truncateMiddle((item.files || [])[0].file_name, 10)}
                                                <span className="file-size text-gray-500">({formatFileSize((item.files || [])[0].file_size)})</span>
                                            </span>
                                        </Space>
                                    ) : (
                                        <Space size={4}><DownloadOutlined /> <span className="text-gray-400 font-medium">{t('resourceList.noFiles')}</span></Space>
                                    )
                                ] : []}
                            >
                                {selectionMode && (
                                    <div className="selection-indicator" />
                                )}
                                <div>
                                    <Card.Meta
                                        title={<div className="truncate font-medium text-lg text-gray-800">{item.title}</div>}
                                        description={
                                            <>
                                                {/* Metadata line - removed ml-12 as Draft ribbon is now on the right */}
                                                <div className={`text-sm text-gray-700 mb-1`}>
                                                    {item.organization_name && (
                                                        <span className="mr-2">{item.organization_name} •</span>
                                                    )}
                                                    {t('resourceList.updatedAt')} {formatShortDatetime(item.updated_at, currentLanguage)}
                                                </div>
                                                {(item.files || []).length > 0 && (
                                                    <div className="text-sm text-gray-500 mb-2">
                                                        {(item.files || []).length} {t('resourceList.files', { count: (item.files || []).length })}, {
                                                            formatFileSize((item.files || []).reduce((acc, file) => acc + (file.file_size || 0), 0))
                                                        }
                                                    </div>
                                                )}
                                                <div className="border-t border-gray-100"></div>
                                                <Typography.Paragraph
                                                    ellipsis={{
                                                        rows: 2,
                                                        expandable: 'collapsible',
                                                        expanded: expandedDescriptions[item.id],
                                                        symbol: (expanded) => expanded ?
                                                            <span className="text-blue-500 hover:text-blue-600 text-xs">{t('resourceList.description.collapse', '收起')}</span> :
                                                            <span className="text-blue-500 hover:text-blue-600 text-xs">{t('resourceList.description.expand', '展開')}</span>,
                                                        onExpand: (e) => {
                                                            if (e) e.stopPropagation();
                                                            setExpandedDescriptions(prev => ({
                                                                ...prev,
                                                                [item.id]: !prev[item.id]
                                                            }));
                                                        }
                                                    }}
                                                    className="text-sm text-gray-700 mb-0 mt-2"
                                                >
                                                    {item.description}
                                                </Typography.Paragraph>
                                            </>
                                        }
                                    />
                                </div>
                            </Card>
                        );

                        let cardWithRibbons = cardComponent;

                        // Apply status ribbon (Draft) to the top-right
                        if (statusRibbonText) {
                            cardWithRibbons = (
                                <Badge.Ribbon text={statusRibbonText} color={statusRibbonColor} placement="end">
                                    {cardWithRibbons}
                                </Badge.Ribbon>
                            );
                        }

                        return (
                            <List.Item key={item.id}>
                                {cardWithRibbons}
                            </List.Item>
                        );
                    }}
                />
            )}

            <UploadResourceModal
                visible={modalData.visible}
                onCancel={() => {
                    setModalData({ visible: false, mode: 'create', initialData: null, orgId: null });
                    setModalLoading(false);
                }}
                onOk={handleModalComplete}
                mode={modalData.mode}
                initialData={modalData.initialData}
                loading={modalLoading}
            />

            {/* Batch Download Bar */}
            {selectionMode && (
                <div className={`batch-download-bar ${isExpanded ? 'expanded' : ''}`}>
                    <div className="download-bar-header">
                        <Space>
                            <Button
                                type="text"
                                icon={isExpanded ? <DownOutlined /> : <UpOutlined />}
                                onClick={() => setIsExpanded(!isExpanded)}
                            >
                                {t(`resourceList.batchDownload.${isExpanded ? 'hideDetails' : 'showDetails'}`)}
                            </Button>
                        </Space>
                        <span>
                            {t('resourceList.batchDownload.total', {
                                count: selectedItems.reduce((total, item) => total + (item.files ? item.files.length : 0), 0),
                                size: formatFileSize(
                                    selectedItems.reduce((total, item) => {
                                        return total + (item.files ? item.files.reduce((fileTotal, file) => {
                                            // file.file_size is now a number (bytes)
                                            return fileTotal + file.file_size;
                                        }, 0) : 0);
                                    }, 0)
                                )
                            })}
                        </span>
                    </div>

                    <div className={`download-bar-content ${isExpanded ? 'expanded' : ''}`}>
                        {Object.entries(groupedItems).map(([resourceTitle, filesInGroup]) => (
                            <DownloadGroup
                                key={resourceTitle}
                                resourceTitle={resourceTitle}
                                files={filesInGroup}
                                onDelete={async (fileOrGroupId) => {
                                    console.log("[Batch Delete] onDelete triggered with:", fileOrGroupId);
                                    if (fileOrGroupId === '__DELETE_GROUP__') {
                                        console.log("[Batch Delete] Deleting group from download list for resource title:", resourceTitle);
                                        setSelectedItems(prev =>
                                            prev.filter(item => item.title !== resourceTitle)
                                        );
                                    } else {
                                        const fileToDelete = fileOrGroupId;
                                        console.log("[Batch Delete] Removing file from download list:", JSON.parse(JSON.stringify(fileToDelete)));

                                        if (!fileToDelete || !fileToDelete.resourceId || !fileToDelete.id) {
                                            console.error("[Batch Delete] Failed: Missing critical IDs in file object", fileToDelete);
                                            message.error(t('resourceList.messages.removeFromListFailed', { fileName: truncateMiddle(fileToDelete?.file_name || 'Unknown file') }));
                                            return;
                                        }

                                        try {

                                            setSelectedItems(prev => {
                                                const newSelectedItems = prev.map(item => {
                                                    if (item.id === fileToDelete.resourceId) {
                                                        const newFiles = item.files.filter(f => f.id !== fileToDelete.id);
                                                        if (newFiles.length === 0) return null;
                                                        return { ...item, files: newFiles };
                                                    }
                                                    return item;
                                                }).filter(Boolean);
                                                console.log("[Batch Delete] New selectedItems state:", newSelectedItems);
                                                return newSelectedItems;
                                            });
                                        } catch (error) {
                                            console.error(`[Batch Delete] Error removing file from download list ${fileToDelete.id}:`, error);
                                        }
                                    }
                                }}
                            />
                        ))}
                    </div>

                    <div className="download-bar-footer">
                        <Button
                            onClick={() => {
                                setSelectionMode(false);
                                setSelectedItems([]);
                            }}
                        >
                            {t('resourceList.buttons.cancel')}
                        </Button>
                        <Button
                            type="primary"
                            icon={<DownloadOutlined />}
                            onClick={handleBatchDownload}
                            disabled={selectedItems.length === 0}
                        >
                            {t('resourceList.buttons.download')}
                        </Button>
                    </div>
                </div>
            )}

            <Modal
                title={t('resourceList.login.required')}
                open={loginModalOpen}
                onOk={() => {
                    const currentPath = window.location.pathname + window.location.search;
                    setLoginModalOpen(false);
                    navigate(`/login?returnTo=${encodeURIComponent(currentPath)}`);
                }}
                onCancel={() => setLoginModalOpen(false)}
                okText={t('resourceList.login.button')}
                cancelText={t('common.cancel')}
            >
                {t('resourceList.login.message')}
            </Modal>

        </div>
    );
};

export default ResourceList; 