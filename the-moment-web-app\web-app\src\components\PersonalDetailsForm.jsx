import React, { useState, useEffect } from 'react';
import { Form, Input, Select, DatePicker, Radio, Row, Col, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { validateHKID, formatHKID, removeHKIDFormat } from '../utils/HKIDValidator';
import moment from 'moment';
import countryList from 'country-list-with-dial-code-and-flag';

const { Option } = Select;
const { Text } = Typography;

// Helper function to process country data
const processCountryData = (rawCountryList) => {
  // Parse countries from the library using the correct property names
  let countries = rawCountryList.map((country) => {
    // 特殊處理台灣、香港和澳門的本地化名稱和英文名稱
    let localName = country.localName;
    let name = country.name;
    
    // 特殊情況處理
    if (country.code === 'TW') {
      localName = '中國台灣';
      name = 'Taiwan, China';
    } else if (country.code === 'HK') {
      localName = '中國香港';
      name = 'Hong Kong, China';
    } else if (country.code === 'MO') {
      localName = '中國澳門';
      name = 'Macau, China';
    }
    
    return {
      name: name,
      code: country.code,
      dialCode: country.dialCode,
      flag: country.flag,
      localName: localName
    };
  });
  
  // 去除重複的國家（基於國家代碼）
  const uniqueCountryCodes = new Set();
  countries = countries.filter(country => {
    if (uniqueCountryCodes.has(country.code)) {
      return false;
    }
    uniqueCountryCodes.add(country.code);
    return true;
  });
  
  // 將中國大陸、台灣、香港、澳門放在列表最前方
  const priorityCountries = countries.filter(country => 
    ['CN', 'TW', 'HK', 'MO'].includes(country.code)
  );
  const otherCountries = countries.filter(country => 
    !['CN', 'TW', 'HK', 'MO'].includes(country.code)
  );
  
  return [...priorityCountries, ...otherCountries];
};

const PersonalDetailsForm = ({ form, initialDocType }) => {
  const { t } = useTranslation();
  const [selectedDocType, setSelectedDocType] = useState(
    initialDocType === 'hk-id-card' ? 'hkid' :
    initialDocType === 'mainland-travel-permit' ? 'mainlandTravelPermit' :
    // initialDocType === 'hk-youth-card' ? 'hkyouth' :
    initialDocType === 'passport' ? 'passport' : 'hkid'
  );
  const [countries, setCountries] = useState([]);
  
  useEffect(() => {
    // Initialize countries data
    const processedCountries = processCountryData(countryList.getAll());
    setCountries(processedCountries);

    // Set initial document type in form if provided
    if (initialDocType) {
      const documentType = 
        initialDocType === 'hk-id-card' ? 'hkid' :
        initialDocType === 'mainland-travel-permit' ? 'mainlandTravelPermit' :
        // initialDocType === 'hk-youth-card' ? 'hkyouth' :
        initialDocType === 'passport' ? 'passport' : 'hkid';
      
      form.setFieldsValue({
        idDocumentType: documentType
      });
      setSelectedDocType(documentType);
    }
  }, [initialDocType, form]);

  const titles = [
    'Mr.',
    'Mrs.',
    'Ms.',
    'Miss',
    'The Hon.',
    'Dr.',
    'Prof.',
    'JP',
    'MH',
    'BBS',
    'SBS',
    'GBS',
    'Others'
  ];

  const industries = [
    'Accounting',
    'Banking',
    'Construction',
    'Education',
    'Engineering',
    'Finance',
    'Government',
    'Healthcare',
    'Hospitality',
    'Insurance',
    'IT',
    'Legal',
    'Manufacturing',
    'Media',
    'Property',
    'Retail',
    'Self-employed',
    'Student',
    'Trading',
    'Transport',
    'Others'
  ];

  // Effect to sync form's document type with local state
  useEffect(() => {
    const currentDocType = form.getFieldValue('idDocumentType');
    if (currentDocType && currentDocType !== selectedDocType) {
      setSelectedDocType(currentDocType);
    }
  }, [form, selectedDocType]);

  // ID document validation rules based on type
  const getIdValidationRules = (docType) => {
    switch (docType) {
      case 'hkid':
        return [
          { required: true, message: t('registration.form.validation.hkid') },
          {
            validator: async (_, value) => {
              if (!value) return Promise.resolve();
              const result = validateHKID(value);
              if (!result.valid) {
                return Promise.reject(new Error(result.message));
              }
              return Promise.resolve();
            }
          }
        ];
      case 'passport':
        return [
          { required: true, message: t('registration.form.validation.passport') },
          {
            validator: async (_, value) => {
              if (!value || /^[A-Z0-9]{6,12}$/i.test(value)) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(t('registration.form.validation.passport')));
            }
          }
        ];
      case 'mainlandTravelPermit':
        return [
          { required: true, message: t('registration.form.validation.mainlandTravelPermit') },
          {
            validator: async (_, value) => {
              if (!value) return Promise.resolve();
              
              // Check format: H/M followed by 8 digits for permanent number and 2 digits for reissue count
              const formatRegex = /^[HM]\d{8}\d{2}$/;
              if (!formatRegex.test(value)) {
                return Promise.reject(new Error(t('registration.form.validation.mainlandTravelPermit')));
              }
              return Promise.resolve();
            }
          }
        ];
      // case 'hkyouth':
      //   return [
      //     { required: true, message: t('registration.form.validation.hkyouth') },
      //     {
      //       validator: async (_, value) => {
      //         if (!value || /^[A-Z0-9]{8,12}$/i.test(value)) {
      //           return Promise.resolve();
      //         }
      //         return Promise.reject(new Error(t('registration.form.validation.hkyouth')));
      //       }
      //     }
      //   ];
      default:
        return [{ required: true, message: t('registration.form.validation.idNumber') }];
    }
  };

  // Handle ID document input change
  const handleIdDocChange = (e) => {
    if (selectedDocType !== 'hkid') return;
    
    const currentValue = e.target.value;
    const nativeEvent = e.nativeEvent;
    
    if (nativeEvent.inputType === 'deleteContentBackward') {
      const unformatted = removeHKIDFormat(currentValue);
      form.setFieldsValue({ idNumber: unformatted });
      return;
    }

    const result = formatHKID(currentValue);
    if (result.success) {
      form.setFieldsValue({ idNumber: result.formattedID });
    }
  };

  // Handle document type change
  const handleDocTypeChange = (value) => {
    setSelectedDocType(value);
    // Only clear ID number if the value is different from the current form value
    if (value !== form.getFieldValue('idDocumentType')) {
      form.setFieldsValue({ idNumber: '' });
    }
  };

  return (
    <div className="space-y-6 py-4">
      {/* Name Section */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
        <div>
          <Form.Item
            name="title"
            label={t('registration.form.labels.title')}
            rules={[{ required: true, message: t('registration.form.validation.title') }]}
            className="mb-0"
          >
            <Select placeholder={t('registration.form.placeholders.title')} className="w-full">
              {titles.map((title) => (
                <Option key={title} value={title}>{title}</Option>
              ))}
            </Select>
          </Form.Item>
        </div>
        <div>
          <Form.Item
            name="firstName"
            label={t('registration.form.labels.firstName')}
            rules={[{ required: true, message: t('registration.form.validation.firstName') }]}
            className="mb-0"
          >
            <Input placeholder={t('registration.form.placeholders.firstName')} className="w-full" />
          </Form.Item>
        </div>
        <div>
          <Form.Item
            name="lastName"
            label={t('registration.form.labels.lastName')}
            rules={[{ required: true, message: t('registration.form.validation.lastName') }]}
            className="mb-0"
          >
            <Input placeholder={t('registration.form.placeholders.lastName')} className="w-full" />
          </Form.Item>
        </div>
      </div>

      {/* Chinese Name and Date of Birth */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        <div>
          <Form.Item
            name="chineseName"
            label={t('registration.form.labels.chineseName')}
            rules={[{ required: false }]}
            className="mb-0"
          >
            <Input placeholder={t('registration.form.placeholders.chineseName')} className="w-full" />
          </Form.Item>
        </div>
        <div>
          <Form.Item
            name="dateOfBirth"
            label={t('registration.form.labels.dateOfBirth')}
            rules={[{ required: true, message: t('registration.form.validation.dateOfBirth') }]}
            className="mb-0"
          >
            <DatePicker
              className="w-full"
              placeholder={t('registration.form.labels.dateOfBirth')}
              disabledDate={current => current && current > moment().endOf('day')}
            />
          </Form.Item>
        </div>
      </div>

      {/* Gender Section */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        <div>
          <Form.Item
            name="gender"
            label={t('registration.form.labels.gender')}
            rules={[{ required: true, message: t('registration.form.validation.gender') }]}
            className="mb-0"
          >
            <Radio.Group className="w-full">
              <div className="flex space-x-8">
                <Radio value="male">{t('registration.form.gender.male')}</Radio>
                <Radio value="female">{t('registration.form.gender.female')}</Radio>
              </div>
            </Radio.Group>
          </Form.Item>
        </div>
      </div>

      {/* ID Document Section */}
      <div className="grid grid-cols-1 gap-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <div>
            <Form.Item
              name="idDocumentType"
              label={t('registration.form.labels.idDocumentType')}
              rules={[{ required: true, message: t('registration.form.validation.idDocumentType') }]}
              className="mb-0"
            >
              <Select 
                placeholder={t('registration.form.placeholders.idDocumentType')}
                onChange={handleDocTypeChange}
                className="w-full"
              >
                <Option value="hkid">{t('registration.form.idDocumentTypes.hkid')}</Option>
                <Option value="passport">{t('registration.form.idDocumentTypes.passport')}</Option>
                <Option value="mainlandTravelPermit">{t('registration.form.idDocumentTypes.mainlandTravelPermit')}</Option>
                {/* <Option value="hkyouth">{t('registration.form.idDocumentTypes.hkyouth')}</Option> */}
              </Select>
            </Form.Item>
          </div>
          <div>
            <Form.Item
              name="idNumber"
              label={t(`registration.form.labels.${selectedDocType}`)}
              rules={getIdValidationRules(selectedDocType)}
              tooltip={t(`registration.form.tooltips.${selectedDocType}`)}
              validateTrigger={['onChange', 'onBlur']}
              className="mb-0"
            >
              <Input
                placeholder={t(`registration.form.placeholders.${selectedDocType}`)}
                onChange={handleIdDocChange}
                onBlur={handleIdDocChange}
                maxLength={selectedDocType === 'passport' ? 12 : 11}
                className="w-full"
              />
            </Form.Item>
          </div>
        </div>
        {selectedDocType === 'passport' && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <Form.Item
                name="passportCountry"
                label={t('registration.form.labels.passportCountry')}
                rules={[{ required: true, message: t('registration.form.validation.passportCountry') }]}
                className="mb-0"
              >
                <Select
                  showSearch
                  placeholder={t('registration.form.placeholders.passportCountry')}
                  optionFilterProp="children"
                  filterOption={(input, option) => {
                    const searchText = input.toLowerCase();
                    const optionData = option['data-country'];
                    return (
                      optionData.name.toLowerCase().includes(searchText) ||
                      (optionData.localName && optionData.localName.toLowerCase().includes(searchText))
                    );
                  }}
                  className="w-full"
                >
                  {countries.map((country) => (
                    <Option 
                      key={country.code} 
                      value={country.code}
                      data-country={country}
                    >
                      <div className="flex items-center">
                        <span className="mr-2">{country.flag}</span>
                        <span>
                          {country.localName || country.name}
                        </span>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </div>
        )}
      </div>

      {/* Optional Industry and Company section */}
      {/* <div className="grid grid-cols-1 sm:grid-cols-4 gap-6">
        <div className="sm:col-span-1">
          <Form.Item
            name="industry"
            label={t('registration.form.labels.industry')}
            rules={[{ required: true, message: t('registration.form.validation.industry') }]}
            className="mb-0"
          >
            <Select
              showSearch
              placeholder={t('registration.form.labels.industry')}
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              className="w-full"
            >
              {industries.map((industry) => (
                <Option key={industry} value={industry}>
                  {industry}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </div>
        <div className="sm:col-span-3">
          <Form.Item
            name="company"
            label={t('registration.form.labels.company')}
            rules={[{ required: true, message: t('registration.form.validation.company') }]}
            className="mb-0"
          >
            <Input placeholder={t('registration.form.placeholders.company')} className="w-full" />
          </Form.Item>
        </div>
      </div> */}
    </div>
  );
};

export default PersonalDetailsForm; 