-- Update organizations seeded by 000009_seed_orgs_and_users.up.sql with image_url and theme_color

DO $$
DECLARE
    org_tongxing_id uuid;
    org_shatinsi_id uuid;
    org_blue_id uuid;
    org_pink_id uuid;
BEGIN
    -- Get IDs of the organizations by name (as used in 000009)
    SELECT id INTO org_tongxing_id FROM organizations WHERE name = '同行明天';
    SELECT id INTO org_shatinsi_id FROM organizations WHERE name = '沙田西關愛隊';
    SELECT id INTO org_blue_id FROM organizations WHERE name = '藍色主題組織';
    SELECT id INTO org_pink_id FROM organizations WHERE name = '玫紅色主題組織';

    -- Update 同行明天
    IF org_tongxing_id IS NOT NULL THEN
        UPDATE organizations
        SET image_url = 'https://example.com/tongxing_logo.png',
            theme_color = 'blue'
        WHERE id = org_tongxing_id;
    END IF;

    -- Update 沙田西關愛隊
    IF org_shatinsi_id IS NOT NULL THEN
        UPDATE organizations
        SET image_url = 'https://example.com/shatinsi_logo.png',
            theme_color = 'red'
        WHERE id = org_shatinsi_id;
    END IF;

    -- Update 藍色主題組織
    IF org_blue_id IS NOT NULL THEN
        UPDATE organizations
        SET image_url = 'https://example.com/blue_org_logo.png',
            theme_color = 'blue'
        WHERE id = org_blue_id;
    END IF;

    -- Update 玫紅色主題組織
    IF org_pink_id IS NOT NULL THEN
        UPDATE organizations
        SET image_url = 'https://example.com/pink_org_logo.png',
            theme_color = 'pink'
        WHERE id = org_pink_id;
    END IF;

END $$; 