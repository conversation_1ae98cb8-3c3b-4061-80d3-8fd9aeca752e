import React from 'react';
import { Dropdown, Space, Avatar, Badge } from 'antd';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';
import defaultLogo from '../assets/logo/default-logo.png';
import { useAuth } from '../contexts/AuthContext';
import { ALL_ORGANIZATION_ID, useOrganization } from '../contexts/OrganizationContext';

/**
 * 組織下拉選單組件
 * 
 * @param {Object} props
 * @param {Array} props.organizations - 組織列表
 * @param {Object} props.currentOrganization - 當前選中的組織
 * @param {Function} props.handleOrganizationChange - 組織變更處理函數
 * @param {boolean} props.collapsed - 側邊欄是否折疊
 * @param {boolean} props.isMobile - 是否為移動設備
 */
const OrganizationDropdown = ({ 
    organizations, 
    currentOrganization, 
    handleOrganizationChange,
    collapsed,
    isMobile
}) => {
    const { t } = useTranslation();
    const { user } = useAuth();
    const { getOrganizationDisplayName, hasAllOrganizationsAccess, volunteerPendingCounts, verificationsPendingCount } = useOrganization();
    const isSuperAdmin = user?.role === 'super_admin';

    // 組織下拉選單項目
    const organizationMenuItems = [
        {
            key: 'group-title',
            label: t('mainLayout.menu.organization'),
            type: 'group',
        },
    ];

    // 添加组织到下拉菜单
    (organizations || []).forEach(org => {
        const volunteerPendingCount = volunteerPendingCounts[org.id] || 0;
        const shouldShowVolunteerBadge = (user?.role === 'admin' || user?.role === 'super_admin') && volunteerPendingCount > 0;
        
        // For super org (ALL_ORGANIZATION_ID), also show verifications badge for super_admin
        const isSupeOrg = org.id === ALL_ORGANIZATION_ID;
        const shouldShowVerificationsBadge = isSupeOrg && user?.role === 'super_admin' && verificationsPendingCount > 0;
        
        // Calculate total pending count for badge display
        let totalPendingCount = volunteerPendingCount;
        if (shouldShowVerificationsBadge) {
            totalPendingCount += verificationsPendingCount;
        }
        
        const shouldShowBadge = shouldShowVolunteerBadge || shouldShowVerificationsBadge;
        // Use orange color if there are verifications, otherwise pink
        const badgeColor = shouldShowVerificationsBadge ? "orange" : "pink";
        
        organizationMenuItems.push({
            key: org.id,
            label: (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                    <Space>
                        <Avatar size="small" src={org?.image_url || defaultLogo} />
                        <span style={{
                            fontSize: '15px',
                            fontWeight: 600
                        }}>
                            {getOrganizationDisplayName(org)}
                        </span>
                    </Space>
                    {shouldShowBadge && (
                        <Badge 
                            count={totalPendingCount} 
                            size="large" 
                            color={badgeColor}
                            style={{
                                marginLeft: '8px',
                                marginRight: '-8px'
                            }}
                        />
                    )}
                </div>
            ),
            title: '',
            onClick: () => handleOrganizationChange(org.id)
        });
    });
    
    const organizationItems = {
        items: organizationMenuItems
    };

    // 使用Context中的方法获取显示名称
    const displayOrgName = getOrganizationDisplayName(currentOrganization);
    // 获取logo
    let displayOrgLogo = (currentOrganization?.image_url && currentOrganization.image_url.trim() !== "")
        ? currentOrganization.image_url
        : defaultLogo;
    
    // 获取当前组织的待处理数量
    const currentOrgVolunteerPendingCount = currentOrganization ? (volunteerPendingCounts[currentOrganization.id] || 0) : 0;
    const shouldShowCurrentOrgVolunteerBadge = (user?.role === 'admin' || user?.role === 'super_admin') && currentOrgVolunteerPendingCount > 0;
    
    // For current org, check if it's super org and should show verifications badge
    const isCurrentOrgSuperOrg = currentOrganization?.id === ALL_ORGANIZATION_ID;
    const shouldShowCurrentOrgVerificationsBadge = isCurrentOrgSuperOrg && user?.role === 'super_admin' && verificationsPendingCount > 0;
    
    const shouldShowCurrentOrgBadge = shouldShowCurrentOrgVolunteerBadge || shouldShowCurrentOrgVerificationsBadge;
    // Use orange color if there are verifications, otherwise pink
    const currentOrgBadgeColor = shouldShowCurrentOrgVerificationsBadge ? "orange" : "pink";

    


    // Placeholder for loading or if no org is selected
    if (!currentOrganization) {
        if (collapsed && !isMobile) {
            return <div className="h-[30px] w-[30px] bg-gray-200 rounded-full"></div>;
        }
        return (
            <div className={isMobile ? "flex items-center justify-between w-full" : "flex items-center min-w-0 flex-1"}>
                <Space size={isMobile ? 12 : 0}>
                    <div className="h-[30px] w-[30px] bg-gray-200 rounded-full flex-shrink-0 mr-3"></div>
                    <div className="h-5 bg-gray-200 w-24 rounded"></div>
                </Space>
            </div>
        );
    }
    
    // Collapsed state (icon only)
    if (collapsed && !isMobile) {
        return (
            <Dropdown
                menu={organizationItems}
                placement="bottomRight"
                trigger={['click']}
            >
                <div className="cursor-pointer">
                    {shouldShowCurrentOrgBadge ? (
                        <Badge dot color={currentOrgBadgeColor}>
                            <Avatar
                                size={30}
                                src={displayOrgLogo}
                            />
                        </Badge>
                    ) : (
                        <Avatar
                            size={30}
                            src={displayOrgLogo}
                        />
                    )}
                </div>
            </Dropdown>
        );
    }

    // Mobile view
    if (isMobile) {
        return (
            <Dropdown
                menu={organizationItems}
                placement="bottomRight"
                trigger={['click']}
            >
                <div className="flex items-center justify-between w-full cursor-pointer">
                    <Space size={12}>
                        <Avatar
                            size={30}
                            src={displayOrgLogo}
                        />
                        <span className="text-[15px] font-semibold">
                            {displayOrgName}
                        </span>
                    </Space>
                    <Icon
                        icon="mingcute:down-line"
                        className="text-[18px] text-gray-600"
                    />
                </div>
            </Dropdown>
        );
    }

    // Default expanded state
    return (
        <Dropdown
            menu={organizationItems}
            placement="bottomRight"
            trigger={['click']}
        >
            <div className="flex items-center justify-between flex-1 cursor-pointer min-w-0">
                <div className="flex items-center min-w-0 flex-1">
                    <Avatar
                        size={30}
                        src={displayOrgLogo}
                        className="flex-shrink-0 mr-3"
                    />
                    <span className="text-[15px] font-semibold truncate">
                        {displayOrgName}
                    </span>
                </div>
                <Icon
                    icon="mingcute:down-line"
                    className="text-[18px] text-gray-600 flex-shrink-0 ml-2"
                />
            </div>
        </Dropdown>
    );
};

export default OrganizationDropdown; 