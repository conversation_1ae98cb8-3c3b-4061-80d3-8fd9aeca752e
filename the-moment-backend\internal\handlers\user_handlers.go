package handlers

import (
	"Membership-SAAS-System-Backend/db"
	"database/sql"
	"errors" // Added for errors.As
	"net/http"
	"strconv"

	"Membership-SAAS-System-Backend/internal/authn"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services"
	utils "Membership-SAAS-System-Backend/internal/utils"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// UserHandler handles HTTP requests related to user profiles.
type UserHandler struct {
	Service   *services.UserService
	Validator *utils.RequestValidator
}

// NewUserHandler creates a new UserHandler.
func NewUserHandler(service *services.UserService, validator *utils.RequestValidator) *UserHandler {
	return &UserHandler{
		Service:   service,
		Validator: validator,
	}
}

// GetUserProfileHandler godoc
// @Summary Retrieve current user's profile
// @Description Fetches the profile information of the authenticated user.
// @Tags User Profile
// @Produce json
// @Success 200 {object} payloads.UserProfileResponse "User profile data"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 404 {object} payloads.ErrorResponse "User not found"
// @Failure 500 {object} payloads.ErrorResponse "Failed to retrieve user profile"
// @Security BearerAuth
// @Router /users/me [get]
func (h *UserHandler) GetUserProfileHandler(c echo.Context) error {
	ctx := c.Request().Context()
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		// GetValidatedClaims already logs, so just return utils.HandleError
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	userProfile, err := h.Service.GetUserProfile(ctx, claims.UserID)
	if err != nil {
		var nfe *payloads.NotFoundError
		if errors.As(err, &nfe) {
			return utils.HandleError(c, http.StatusNotFound, nfe.Message, err)
		}
		// Logged in UserService, just return generic error
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve user profile", err)
	}

	return c.JSON(http.StatusOK, userProfile)
}

// UpdateUserProfileHandler godoc
// @Summary Update current user's profile or settings
// @Description Updates user profile fields like display name, bio, language preferences, or user settings like notification preferences.
// @Tags User Profile
// @Accept json
// @Produce json
// @Param body body payloads.UpdateUserProfileRequest true "Fields to update (include only fields to be changed)"
// @Success 200 {object} payloads.UserProfileResponse "Updated user profile data"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 404 {object} payloads.ErrorResponse "User not found"
// @Failure 500 {object} payloads.ErrorResponse "Failed to update user profile"
// @Security BearerAuth
// @Router /users/me [patch]  // Note: Also handles settings updates conceptually
func (h *UserHandler) UpdateUserProfileHandler(c echo.Context) error {
	ctx := c.Request().Context()
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	var req payloads.UpdateUserProfileRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}

	// Example of struct validation (if you add tags to UpdateUserProfileRequest)
	// if err := h.Validator.ValidateStruct(req); err != nil {
	// 	   return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	// }

	updatedProfile, err := h.Service.UpdateUserProfile(ctx, claims.UserID, &req)
	if err != nil {
		var nfe *payloads.NotFoundError
		var iae *payloads.IllegalArgumentError
		var afe *payloads.ForbiddenError // For future use if updates have permission aspects

		if errors.As(err, &nfe) {
			return utils.HandleError(c, http.StatusNotFound, nfe.Message, err)
		} else if errors.As(err, &iae) {
			return utils.HandleError(c, http.StatusBadRequest, iae.Message, err)
		} else if errors.As(err, &afe) {
			return utils.HandleError(c, http.StatusForbidden, afe.Message, err)
		}
		// Logged in UserService
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to update user profile", err)
	}

	return c.JSON(http.StatusOK, updatedProfile)
}

// DeleteUserHandler godoc
// @Summary Delete a user
// @Description Deletes a user by their UUID. This is a permanent action.
// @Tags Admin
// @Produce json
// @Param userId path string true "User ID"
// @Success 204 "User deleted successfully"
// @Failure 400 {object} payloads.ErrorResponse "Invalid UUID format"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 404 {object} payloads.ErrorResponse "User not found"
// @Failure 500 {object} payloads.ErrorResponse "Failed to delete user"
// @Security BearerAuth
// @Router /admin/users/{userId} [delete]
func (h *UserHandler) DeleteUserHandler(c echo.Context) error {
	ctx := c.Request().Context()
	userIDStr := c.Param("userId")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid UUID format", err)
	}

	err = h.Service.DeleteUser(ctx, userID)
	if err != nil {
		var nfe *payloads.NotFoundError
		if errors.As(err, &nfe) {
			return utils.HandleError(c, http.StatusNotFound, nfe.Message, err)
		}
		// Logged in UserService, just return generic error
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to delete user", err)
	}

	return c.NoContent(http.StatusNoContent)
}

// UploadProfilePictureHandler handles POST /users/me/profile-picture
// @Summary Upload user profile picture
// @Description Uploads a new profile picture for the current authenticated user.
// @Tags User Profile
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Image file (JPEG, PNG, GIF, WebP; max 5MB)"
// @Success 200 {object} payloads.UserProfileResponse "Updated user profile with new profile_picture_url"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request or file type/size"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 500 {object} payloads.ErrorResponse "Failed to upload profile picture"
// @Router /users/me/profile-picture [post]
func (h *UserHandler) UploadProfilePictureHandler(c echo.Context) error {
	ctx := c.Request().Context()
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}
	userID := claims.UserID

	fileHeader, err := c.FormFile("file") // "file" is the name of the form field for the uploaded image
	if err != nil {
		if errors.Is(err, http.ErrMissingFile) {
			return utils.HandleError(c, http.StatusBadRequest, "Missing 'file' in request form data", err)
		}
		return utils.HandleError(c, http.StatusBadRequest, "Error processing uploaded file: "+err.Error(), err)
	}

	updatedProfileResponse, serviceErr := h.Service.UploadProfilePicture(ctx, userID, fileHeader)
	if serviceErr != nil {
		var fpe *payloads.FileProcessingError
		var iae *payloads.IllegalArgumentError
		if errors.As(serviceErr, &fpe) {
			return utils.HandleError(c, http.StatusBadRequest, fpe.Message, fpe)
		} else if errors.As(serviceErr, &iae) {
			return utils.HandleError(c, http.StatusBadRequest, iae.Message, iae)
		} else if errors.Is(serviceErr, pgx.ErrNoRows) { // Should be handled by service, but as a safeguard
			return utils.HandleError(c, http.StatusNotFound, "User not found", serviceErr)
		}
		// Logged in UserService for other errors
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to upload profile picture", serviceErr)
	}

	// The documentation (tests.md, api_routes.md) suggests the response should be a simple { "url": "..." } object.
	// The service returns the full profile, but we extract the URL here to match the expected API contract.
	if updatedProfileResponse.ProfilePictureURL == nil {
		// This should theoretically not happen if the service succeeds, but as a safeguard:
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve profile picture URL after upload", nil)
	}

	return c.JSON(http.StatusOK, echo.Map{"url": *updatedProfileResponse.ProfilePictureURL})
}

// InitiatePhoneNumberChangeHandler handles POST /users/me/phone/initiate-change
// @Summary Initiate phone number change
// @Description Starts the process to change the user's registered phone number. Sends OTP to new number.
// @Tags User Profile
// @Accept json
// @Produce json
// @Param body body payloads.InitiatePhoneChangeRequest true "New phone number, client ID, and state for phone change initiation"
// @Success 200 {object} payloads.InitiatePhoneChangeResponse "Details of the initiated phone change flow, including state and flow ID"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request or phone already in use"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 409 {object} payloads.ErrorResponse "Conflict if the new phone number is already in use by another user"
// @Failure 500 {object} payloads.ErrorResponse "Failed to initiate phone number change"
// @Security BearerAuth
// @Router /users/me/phone/initiate-change [post]
func (h *UserHandler) InitiatePhoneNumberChangeHandler(c echo.Context) error {
	ctx := c.Request().Context()

	// Use the same validated claims retrieval as other handlers
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Missing or invalid user claims.", err)
	}

	var req payloads.InitiatePhoneChangeRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request payload.", err)
	}
	if err := c.Validate(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	// TODO: Potentially enforce client_id and state from request if needed for specific client flows
	// The service layer uses them now, so they should be passed.

	resp, err := h.Service.InitiatePhoneNumberChange(ctx, claims.UserID, &req)
	if err != nil {
		// Handle specific errors like ConflictError
		var conflictErr *payloads.ConflictError
		if errors.As(err, &conflictErr) {
			return utils.HandleError(c, http.StatusConflict, conflictErr.Message, err)
		}
		// Default internal error
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to initiate phone number change.", err)
	}

	return c.JSON(http.StatusOK, resp)
}

// VerifyPhoneNumberChangeHandler handles POST /users/me/phone/verify-change
// @Summary Verify OTP for new phone number
// @Description Verifies the OTP sent to the new phone number to complete the change.
// @Tags User Profile
// @Accept json
// @Produce json
// @Param body body payloads.VerifyPhoneChangeRequest true "State, OTP, and new phone number for verification"
// @Success 200 {object} payloads.VerifyPhoneChangeResponse "Phone number changed successfully, returns updated user profile"
// @Failure 400 {object} payloads.ErrorResponse "Invalid OTP, state, or request format"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 500 {object} payloads.ErrorResponse "Failed to verify phone number change"
// @Security BearerAuth
// @Router /users/me/phone/verify-change [post]
func (h *UserHandler) VerifyPhoneNumberChangeHandler(c echo.Context) error {
	ctx := c.Request().Context()

	// Use the same validated claims retrieval as other handlers
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Missing or invalid user claims.", err)
	}

	var req payloads.VerifyPhoneChangeRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request payload.", err)
	}
	if err := c.Validate(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	resp, err := h.Service.VerifyPhoneNumberChange(ctx, claims.UserID, &req)
	if err != nil {
		// Handle specific errors from service if needed (e.g., invalid OTP, state mismatch)
		// Assuming VerifyPhoneChangeOTP returns distinct errors that are wrapped
		return utils.HandleError(c, http.StatusBadRequest, "Failed to verify phone number change: "+err.Error(), err)
	}

	return c.JSON(http.StatusOK, resp)
}

// GetUserStats godoc
// @Summary Get User Statistics
// @Description Retrieves statistics for the currently authenticated user.
// @Tags Users
// @Accept json
// @Produce json
// @Success 200 {object} payloads.APIUserStats
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 404 {object} payloads.ErrorResponse "User not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /users/me/stats [get]
// @Security BearerAuth
func (h *UserHandler) GetUserStats(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	stats, err := h.Service.GetUserStats(c.Request().Context(), claims.UserID)
	if err != nil {
		var nfErr *payloads.NotFoundError
		if errors.As(err, &nfErr) {
			return utils.HandleError(c, http.StatusNotFound, nfErr.Message, err)
		}
		// Logged in UserService for other errors
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve user statistics", err)
	}

	return c.JSON(http.StatusOK, stats)
}

// GetUserOrganizationsHandler handles GET /users/me/organizations
// @Summary Get User's Organizations
// @Description Retrieves a list of organizations that the currently authenticated user is an active member of.
// @Tags Users, Organizations
// @Accept json
// @Produce json
// @Success 200 {array} payloads.OrganizationResponse
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /users/me/organizations [get]
// @Security BearerAuth
func (h *UserHandler) GetUserOrganizationsHandler(c echo.Context) error {
	ctx := c.Request().Context()
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	orgs, err := h.Service.ListUserOrganizations(ctx, claims.UserID)
	if err != nil {
		// The service layer logs specific errors. HandleError will use its generic message.
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve user organizations", err)
	}

	// If orgs is nil (e.g. from service layer if explicitly set to nil instead of empty slice on error), ensure it's an empty slice for JSON response
	if orgs == nil {
		orgs = []payloads.OrganizationResponse{}
	}

	return c.JSON(http.StatusOK, orgs)
}

// GetAdminOwnedOrganizationsHandler handles GET /admin/me/organizations
// @Summary Get Admin's Owned Organizations
// @Description Retrieves a list of organizations that the currently authenticated admin owns.
// @Tags Admin, Organizations
// @Accept json
// @Produce json
// @Success 200 {array} payloads.OrganizationResponse
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Router /admin/me/organizations [get]
// @Security BearerAuth
func (h *UserHandler) GetAdminOwnedOrganizationsHandler(c echo.Context) error {
	ctx := c.Request().Context()
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	// Ensure the user is an admin (staff)
	if claims.Role != string(db.UserRoleAdmin) && claims.Role != string(db.UserRoleSuperadmin) {
		return utils.HandleError(c, http.StatusForbidden, "Forbidden: Admin access required", nil)
	}

	orgs, err := h.Service.ListAdminOwnedOrganizations(ctx, claims.UserID)
	if err != nil {
		// The service layer logs specific errors. HandleError will use its generic message.
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve admin's owned organizations", err)
	}

	if orgs == nil {
		orgs = []*payloads.OrganizationResponse{}
	}

	return c.JSON(http.StatusOK, orgs)
}

// HandleGetUserUUID handles GET /users/me/uuid
// @Summary Get current user's UUID
// @Description Retrieves the UUID of the currently authenticated user.
// @Tags User Profile
// @Produce json
// @Success 200 {object} payloads.UserUUIDResponse "User UUID"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Security BearerAuth
// @Router /users/me/uuid [get]
func (h *UserHandler) HandleGetUserUUID(c echo.Context) error {
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	response := payloads.UserUUIDResponse{
		UUID: claims.UserID.String(),
	}

	return c.JSON(http.StatusOK, response)
}

// ListUsersHandler godoc
// @Summary List all users (Admin)
// @Description Retrieves a paginated list of all users. Admin only. Can filter by is_staff status.
// @Tags Admin Users
// @Accept json
// @Produce json
// @Param is_staff query bool false "Filter by staff status"
// @Param page query int false "Page number for pagination (default: 1)"
// @Param limit query int false "Number of items per page (default: 10, max: 100)"
// @Success 200 {object} payloads.PaginatedUsersResponse "List of users with pagination info"
// @Failure 400 {object} utils.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized (JWT missing or invalid)"
// @Failure 403 {object} utils.ErrorResponse "Forbidden (User is not an admin)"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /admin/users [get]
func (h *UserHandler) ListUsersHandler(c echo.Context) error {
	ctx := c.Request().Context()

	// Get Authenticated User Claims
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("ListUsersHandler: Failed to get validated claims")
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid user claims.", err)
	}

	// --- Pagination ---
	page, err := strconv.Atoi(c.QueryParam("page"))
	if err != nil || page < 1 {
		page = 1
	}
	limit, err := strconv.Atoi(c.QueryParam("limit"))
	if err != nil || limit <= 0 {
		limit = 10 // Default limit
	}

	// Get role Query Parameter
	roleFilterParam := c.QueryParam("role")

	// Initialize ListUsersParams
	// Note: Search and Email query params are not part of services.ListUsersParams struct based on user_service.go
	// They can be parsed here if needed for other logic, but won't be passed to listParams.
	// c.QueryParam("search")
	// c.QueryParam("email")
	listParams := services.ListUsersParams{
		Page:     page,
		PageSize: limit,
		// ActorOrgOwnerID will be set conditionally below
		// RoleFilter will be set conditionally below
	}

	// Authorization Logic
	switch roleFilterParam {
	case string(db.UserRoleUser):
		if claims.Role != string(db.UserRoleSuperadmin) {
			return utils.HandleError(c, http.StatusForbidden, "Forbidden: Only superadmins can list regular users.", nil)
		}
		roleUser := string(db.UserRoleUser)
		listParams.RoleFilter = sql.NullString{String: roleUser, Valid: true}
	case string(db.UserRoleAdmin):
		roleAdmin := string(db.UserRoleAdmin)
		listParams.RoleFilter = sql.NullString{String: roleAdmin, Valid: true}
		if claims.Role == string(db.UserRoleSuperadmin) {
			// No further owner filtering needed for superadmin
		} else if claims.Role == string(db.UserRoleAdmin) {
			isOwnerOfAny, err := h.Service.IsActorOwnerOfAnyOrg(c.Request().Context(), claims.UserID)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("admin_user_id", claims.UserID.String()).Msg("ListUsersHandler: Error checking if admin owns any organization")
				return utils.HandleError(c, http.StatusInternalServerError, "Internal server error while checking admin ownership.", err)
			}
			if !isOwnerOfAny {
				return utils.HandleError(c, http.StatusForbidden, "Forbidden: Admins must own an organization to list other admins.", nil)
			}
			listParams.ActorOrgOwnerID = uuid.NullUUID{UUID: claims.UserID, Valid: true}
		} else { // User is not superadmin or admin (e.g., a regular user)
			return utils.HandleError(c, http.StatusForbidden, "Forbidden: Insufficient permissions to list admin users.", nil)
		}
	case "": // roleFilter is empty
		if claims.Role != string(db.UserRoleSuperadmin) {
			return utils.HandleError(c, http.StatusForbidden, "Forbidden: Only superadmins can list all users without a role filter.", nil)
		}
		// listParams.RoleFilter remains nil (which is the zero value for *string)
	default: // roleFilter is something else unrecognized
		return utils.HandleError(c, http.StatusBadRequest, "Invalid role filter value. Allowed values are 'user', 'admin', or empty.", nil)
	}

	// Call Service
	// The service ListUsers returns (*payloads.PaginatedUsersResponse, error)
	paginatedUsersResponse, err := h.Service.ListUsers(c.Request().Context(), listParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Interface("list_params", listParams).Msg("ListUsersHandler: Error calling ListUsers service")
		var iae *payloads.IllegalArgumentError
		if errors.As(err, &iae) {
			return utils.HandleError(c, http.StatusBadRequest, iae.Message, err)
		}
		// Check for other specific error types from service if necessary
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve users.", err)
	}

	return c.JSON(http.StatusOK, paginatedUsersResponse)
}

// GetUserByEmailHandler godoc
// @Summary Get user by email (Admin)
// @Description Retrieves a specific user by their email address. Admin only.
// @Tags Admin Users
// @Accept json
// @Produce json
// @Param email path string true "User Email"
// @Success 200 {object} payloads.AdminUserResponse "User details"
// @Failure 400 {object} utils.ErrorResponse "Invalid email format in path"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden"
// @Failure 404 {object} utils.ErrorResponse "User not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /admin/users/email/{email} [get]
func (h *UserHandler) GetUserByEmailHandler(c echo.Context) error {
	ctx := c.Request().Context()
	email := c.Param("email")

	// Basic validation for email format can be added here if needed,
	// though the service layer might also validate or the DB query will fail cleanly.
	if email == "" { // Path params usually guarantee presence if route matches, but good to check.
		return utils.HandleError(c, http.StatusBadRequest, "Email path parameter is required.", nil)
	}

	userResponse, err := h.Service.FindUserByEmail(ctx, email)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("email", email).Msg("GetUserByEmailHandler: Error finding user by email from service")
		var notFoundErr *payloads.NotFoundError
		if errors.As(err, &notFoundErr) {
			return utils.HandleError(c, http.StatusNotFound, notFoundErr.Message, err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve user by email.", err)
	}

	return c.JSON(http.StatusOK, userResponse)
}

// GetUserByPhoneHandler godoc
// @Summary Get user by phone (Admin)
// @Description Retrieves a specific user by their phone number. Admin only.
// @Tags Admin Users
// @Accept json
// @Produce json
// @Param phone path string true "User Phone Number (ensure URL encoding if it contains special characters like +)"
// @Success 200 {object} payloads.AdminUserResponse "User details"
// @Failure 400 {object} utils.ErrorResponse "Invalid phone format in path"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden"
// @Failure 404 {object} utils.ErrorResponse "User not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /admin/users/phone/{phone} [get]
func (h *UserHandler) GetUserByPhoneHandler(c echo.Context) error {
	ctx := c.Request().Context()
	phone := c.Param("phone")

	if phone == "" {
		return utils.HandleError(c, http.StatusBadRequest, "Phone path parameter is required.", nil)
	}

	// Consider phone number normalization/validation here if needed,
	// or ensure service layer handles it consistently.

	userResponse, err := h.Service.FindUserByPhone(ctx, phone)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("phone", phone).Msg("GetUserByPhoneHandler: Error finding user by phone from service")
		var notFoundErr *payloads.NotFoundError
		if errors.As(err, &notFoundErr) {
			return utils.HandleError(c, http.StatusNotFound, notFoundErr.Message, err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve user by phone.", err)
	}

	return c.JSON(http.StatusOK, userResponse)
}

// GetUserByIDHandler godoc
// @Summary Get user by ID (Admin)
// @Description Retrieves a specific user by their ID. Admin only.
// @Tags Admin Users
// @Accept json
// @Produce json
// @Param userId path string true "User ID (UUID)"
// @Success 200 {object} payloads.AdminUserResponse "User details"
// @Failure 400 {object} utils.ErrorResponse "Invalid user ID format in path"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden"
// @Failure 404 {object} utils.ErrorResponse "User not found"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /admin/users/{userId} [get]
func (h *UserHandler) GetUserByIDHandler(c echo.Context) error {
	ctx := c.Request().Context()
	userIDStr := c.Param("userId")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("userID", userIDStr).Msg("GetUserByIDHandler: Invalid User ID format")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid User ID format in path parameter.", err)
	}

	userResponse, err := h.Service.FindUserByID(ctx, userID) // We'll need to add FindUserByID to UserService
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("userID", userIDStr).Msg("GetUserByIDHandler: Error finding user by ID from service")
		var notFoundErr *payloads.NotFoundError
		if errors.As(err, &notFoundErr) {
			return utils.HandleError(c, http.StatusNotFound, notFoundErr.Message, err)
		}
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to retrieve user by ID.", err)
	}

	return c.JSON(http.StatusOK, userResponse)
}

// CreateStaffUserHandler godoc
// @Summary Create a new staff user (Admin)
// @Description Creates a new user with is_staff set to true. Admin only.
// @Tags Admin Users
// @Accept json
// @Produce json
// @Param body body payloads.CreateStaffUserRequest true "Details of the staff user to create"
// @Success 201 {object} payloads.AdminUserResponse "Newly created staff user details"
// @Failure 400 {object} utils.ErrorResponse "Invalid request payload or validation error (e.g., email exists, password too short)"
// @Failure 401 {object} utils.ErrorResponse "Unauthorized"
// @Failure 403 {object} utils.ErrorResponse "Forbidden"
// @Failure 409 {object} utils.ErrorResponse "Conflict (e.g., email already exists)"
// @Failure 500 {object} utils.ErrorResponse "Internal server error (e.g., failed to hash password or save user)"
// @Security BearerAuth
// @Router /admin/users [post]
func (h *UserHandler) CreateStaffUserHandler(c echo.Context) error {
	ctx := c.Request().Context()

	var req payloads.CreateStaffUserRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request payload.", err)
	}

	if err := h.Validator.ValidateStruct(req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	createdUser, err := h.Service.CreateStaffUser(ctx, &req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("CreateStaffUserHandler: Error creating staff user from service")
		var conflictErr *payloads.ConflictError
		var illegalArgErr *payloads.IllegalArgumentError

		if errors.As(err, &conflictErr) {
			return utils.HandleError(c, http.StatusConflict, conflictErr.Message, err)
		} else if errors.As(err, &illegalArgErr) {
			return utils.HandleError(c, http.StatusBadRequest, illegalArgErr.Message, err)
		}
		// Default to 500 if it's not a specific client error we handle
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to create staff user.", err)
	}

	return c.JSON(http.StatusCreated, createdUser)
}

// AssignOrganizationManagerHandler godoc
// @Summary Assign a user as a manager for an organization
// @Description Assigns a specified user as a manager of the given organization.
// @Description Requires superadmin privileges, or admin privileges if the admin is an owner of the organization.
// @Tags Admin - Organizations
// @Accept json
// @Produce json
// @Param organizationId path string true "Organization ID (UUID)"
// @Param body body payloads.AssignManagerRequest true "User ID to assign as manager"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request payload or organization ID"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden - Insufficient privileges"
// @Failure 404 {object} payloads.ErrorResponse "Organization or User not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /admin/users/organizations/{organizationId}/manager [put]
func (h *UserHandler) AssignOrganizationManagerHandler(c echo.Context) error {
	ctx := c.Request().Context()
	logger := log.Ctx(ctx)

	orgIDStr := c.Param("organizationId")
	organizationId, err := uuid.Parse(orgIDStr)
	if err != nil {
		logger.Warn().Err(err).Str("organizationId", orgIDStr).Msg("Failed to parse organizationId")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	var req payloads.AssignManagerRequest
	if err := c.Bind(&req); err != nil {
		logger.Warn().Err(err).Msg("Failed to bind request payload for AssignManagerRequest")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}

	if err := h.Validator.ValidateStruct(req); err != nil {
		logger.Warn().Err(err).Interface("payload", req).Msg("Validation failed for AssignManagerRequest")
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}
	actorUserID := claims.UserID

	// Authorization Logic
	isSuperAdmin := claims.Role == string(db.UserRoleSuperadmin)
	isAdmin := claims.Role == string(db.UserRoleAdmin)
	authorized := false

	if isSuperAdmin {
		authorized = true
	} else if isAdmin {
		isOwner, err := h.Service.IsActorOwnerOfOrg(c.Request().Context(), actorUserID, organizationId)
		if err != nil {
			logger.Error().Err(err).Msgf("Error checking if admin %s is owner of org %s", actorUserID, organizationId)
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to verify ownership", err)
		}
		if isOwner {
			authorized = true
		}
	}

	if !authorized {
		logger.Warn().Msgf("User %s not authorized to manage managers for org %s", actorUserID, organizationId)
		return utils.HandleError(c, http.StatusForbidden, "Forbidden: You do not have permission to perform this action.", nil)
	}

	// Call the service method
	// This will cause a compile error until UserService.AssignOrganizationManager is implemented in Subtask 2.C
	err = h.Service.AssignOrganizationManager(ctx, organizationId, req.UserID, actorUserID)
	if err != nil {
		var nfe *payloads.NotFoundError
		var ce *payloads.ConflictError
		var iae *payloads.IllegalArgumentError

		if errors.As(err, &nfe) {
			return utils.HandleError(c, http.StatusNotFound, nfe.Message, err)
		}
		if errors.As(err, &ce) {
			return utils.HandleError(c, http.StatusConflict, ce.Message, err)
		}
		if errors.As(err, &iae) {
			return utils.HandleError(c, http.StatusBadRequest, iae.Message, err)
		}
		logger.Error().Err(err).Msg("Failed to assign organization manager")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to assign organization manager", err)
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Organization manager assigned successfully"})
}

// RemoveOrganizationManagerHandler godoc
// @Summary Remove a user as a manager from an organization
// @Description Removes a specified user as a manager of the given organization.
// @Description Requires superadmin privileges, or admin privileges if the admin is an owner of the organization.
// @Tags Admin - Organizations
// @Accept json
// @Produce json
// @Param organizationId path string true "Organization ID (UUID)"
// @Param body body payloads.RemoveManagerRequest true "User ID to remove as manager"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request payload or organization ID"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 403 {object} payloads.ErrorResponse "Forbidden - Insufficient privileges"
// @Failure 404 {object} payloads.ErrorResponse "Organization or User not found"
// @Failure 500 {object} payloads.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /admin/users/organizations/{organizationId}/manager [delete]
func (h *UserHandler) RemoveOrganizationManagerHandler(c echo.Context) error {
	ctx := c.Request().Context()
	logger := log.Ctx(ctx)

	orgIDStr := c.Param("organizationId")
	organizationId, err := uuid.Parse(orgIDStr)
	if err != nil {
		logger.Warn().Err(err).Str("organizationId", orgIDStr).Msg("Failed to parse organizationId")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid organization ID format", err)
	}

	var req payloads.RemoveManagerRequest
	if err := c.Bind(&req); err != nil {
		logger.Warn().Err(err).Msg("Failed to bind request payload for RemoveManagerRequest")
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}

	if err := h.Validator.ValidateStruct(req); err != nil {
		logger.Warn().Err(err).Interface("payload", req).Msg("Validation failed for RemoveManagerRequest")
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}
	actorUserID := claims.UserID

	// Authorization Logic
	isSuperAdmin := claims.Role == string(db.UserRoleSuperadmin)
	isAdmin := claims.Role == string(db.UserRoleAdmin)
	authorized := false

	if isSuperAdmin {
		authorized = true
	} else if isAdmin {
		isOwner, err := h.Service.IsActorOwnerOfOrg(c.Request().Context(), actorUserID, organizationId)
		if err != nil {
			logger.Error().Err(err).Msgf("Error checking if admin %s is owner of org %s", actorUserID, organizationId)
			return utils.HandleError(c, http.StatusInternalServerError, "Failed to verify ownership", err)
		}
		if isOwner {
			authorized = true
		}
	}

	if !authorized {
		logger.Warn().Msgf("User %s not authorized to manage managers for org %s", actorUserID, organizationId)
		return utils.HandleError(c, http.StatusForbidden, "Forbidden: You do not have permission to perform this action.", nil)
	}

	// Call the service method
	// This will cause a compile error until UserService.RemoveOrganizationManager is implemented in Subtask 2.C
	err = h.Service.RemoveOrganizationManager(ctx, organizationId, req.UserID, actorUserID)
	if err != nil {
		var nfe *payloads.NotFoundError
		var iae *payloads.IllegalArgumentError // ConflictError might not be typical for remove unless specific logic

		if errors.As(err, &nfe) {
			return utils.HandleError(c, http.StatusNotFound, nfe.Message, err)
		}
		if errors.As(err, &iae) {
			return utils.HandleError(c, http.StatusBadRequest, iae.Message, err)
		}
		logger.Error().Err(err).Msg("Failed to remove organization manager")
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to remove organization manager", err)
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Organization manager removed successfully"})
}

// InitiateReAuthOTPHandler godoc
// @Summary Initiate a re-authentication OTP flow
// @Description Starts an OTP flow to re-authenticate a logged-in user for sensitive actions.
// @Tags User Profile
// @Accept json
// @Produce json
// @Param body body payloads.InitiateReAuthOTPRequest true "Purpose and optional channel for OTP"
// @Success 200 {object} payloads.InitiateReAuthOTPResponse "Details of the initiated flow"
// @Failure 400 {object} payloads.ErrorResponse "Invalid request payload"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 500 {object} payloads.ErrorResponse "Failed to initiate re-authentication"
// @Security BearerAuth
// @Router /users/me/reauth/otp/initiate [post]
func (h *UserHandler) InitiateReAuthOTPHandler(c echo.Context) error {
	ctx := c.Request().Context()
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	var req payloads.InitiateReAuthOTPRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}
	if err := c.Validate(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	resp, err := h.Service.InitiateReAuthOTP(ctx, claims.UserID, &req)
	if err != nil {
		return utils.HandleError(c, http.StatusInternalServerError, "Failed to initiate re-authentication flow", err)
	}

	return c.JSON(http.StatusOK, resp)
}

// VerifyReAuthOTPHandler godoc
// @Summary Verify a re-authentication OTP
// @Description Verifies the OTP and returns a short-lived token for performing a sensitive action.
// @Tags User Profile
// @Accept json
// @Produce json
// @Param body body payloads.VerifyReAuthOTPRequest true "Flow ID and OTP"
// @Success 200 {object} payloads.VerifyReAuthOTPResponse "Re-authentication token"
// @Failure 400 {object} payloads.ErrorResponse "Invalid OTP or request"
// @Failure 401 {object} payloads.ErrorResponse "Unauthorized"
// @Failure 500 {object} payloads.ErrorResponse "Failed to verify re-authentication"
// @Security BearerAuth
// @Router /users/me/reauth/otp/verify [post]
func (h *UserHandler) VerifyReAuthOTPHandler(c echo.Context) error {
	ctx := c.Request().Context()
	claims, err := authn.GetValidatedClaims(c)
	if err != nil {
		return utils.HandleError(c, http.StatusUnauthorized, "Unauthorized: Invalid claims", err)
	}

	var req payloads.VerifyReAuthOTPRequest
	if err := c.Bind(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}
	if err := c.Validate(&req); err != nil {
		return utils.HandleError(c, http.StatusBadRequest, "Validation failed: "+err.Error(), err)
	}

	resp, err := h.Service.VerifyReAuthOTP(ctx, claims.UserID, &req)
	if err != nil {
		// Service layer returns specific error messages for invalid OTP, etc.
		return utils.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	return c.JSON(http.StatusOK, resp)
}
