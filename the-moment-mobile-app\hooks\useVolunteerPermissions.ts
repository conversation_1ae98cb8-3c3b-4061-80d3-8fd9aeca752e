import { useMemo } from 'react';
import { useFetchUserVolunteerQualifications } from '@/api/user_services';
import { userVolunteerQualificationsStore } from 'stores/user_store';
import { useFocusEffect } from '@react-navigation/native';
import React from 'react';

/**
 * Custom hook to check if user has volunteer permissions
 * Returns both the permission status and refresh functionality
 */
export const useVolunteerPermissions = () => {
  const { refetch: refetchVolunteerQualifications } = useFetchUserVolunteerQualifications();
  
  // Get volunteer qualifications from store
  const volunteerQualifications = userVolunteerQualificationsStore(state => state.volunteerQualifications);
  
  // Check if user has any approved volunteer qualifications
  const hasVolunteerPermissions = useMemo(() => {
    return volunteerQualifications?.some((qualification: any) => 
      qualification.status === 'approved'
    ) || false;
  }, [volunteerQualifications]);
  
  // Refresh volunteer qualifications when screen focuses
  const refreshVolunteerPermissions = React.useCallback(async () => {
    try {
      const result = await refetchVolunteerQualifications();
      if (result.data) {
        const { setVolunteerQualifications } = userVolunteerQualificationsStore.getState();
        setVolunteerQualifications(result.data);
      }
    } catch (error) {
      console.error('[useVolunteerPermissions] Failed to refresh volunteer permissions:', error);
    }
  }, [refetchVolunteerQualifications]);
  
  // Auto-refresh on focus
  useFocusEffect(
    React.useCallback(() => {
      refreshVolunteerPermissions();
    }, [refreshVolunteerPermissions])
  );
  
  return {
    hasVolunteerPermissions,
    volunteerQualifications,
    refreshVolunteerPermissions,
  };
}; 