import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, ScrollView, useWindowDimensions, Animated } from 'react-native';
import { Portal } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { appStyleStore } from 'stores/app_style_store';

interface TextDialogProps {
    visible: boolean;
    title: string;
    message: string;
    confirmText: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    type?: 'warning' | 'success' | 'info';
}

export const TextDialog: React.FC<TextDialogProps> = ({
    visible,
    title,
    message,
    confirmText,
    cancelText,
    onConfirm,
    onCancel,
    type = 'info'
}) => {
    const theme = appStyleStore(state => state.theme);
    const { width: screenWidth } = useWindowDimensions();
    const [animation] = React.useState(new Animated.Value(0));

    useEffect(() => {
        if (visible) {
            // 显示动画：使用弹性动画效果
            Animated.spring(animation, {
                toValue: 1,
                useNativeDriver: true,
                friction: 8,
                tension: 40,
            }).start();
        } else {
            // 隐藏动画：线性淡出
            Animated.timing(animation, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }).start();
        }
    }, [visible]);

    const getIconName = () => {
        switch (type) {
            case 'warning':
                return 'alert-circle';
            case 'success':
                return 'check-circle';
            default:
                return 'information';
        }
    };

    const getIconColor = () => {
        switch (type) {
            case 'warning':
                return theme.system.warning;
            case 'success':
                return theme.system.success;
            default:
                return theme.system.info;
        }
    };

    // 缩放动画插值
    const scale = animation.interpolate({
        inputRange: [0, 1],
        outputRange: [0.8, 1],
    });

    const styles = StyleSheet.create({
        overlay: {
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            justifyContent: 'center',
            alignItems: 'center',
        },
        container: {
            width: screenWidth * 0.85,
            backgroundColor: theme.colors.background,
            borderRadius: 8,
            padding: 20,
        },
        header: {
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
        },
        icon: {
            marginRight: 8,
        },
        title: {
            fontSize: 18,
            fontWeight: '600',
            color: theme.colors.onSurface,
            flex: 1,
        },
        message: {
            fontSize: 16,
            color: theme.colors.onSurfaceVariant,
            lineHeight: 24,
            textAlign: 'left',
            marginBottom: 24,
        },
        buttonContainer: {
            flexDirection: 'row',
            justifyContent: 'flex-end',
            gap: 12,
        },
        button: {
            paddingVertical: 8,
            paddingHorizontal: 16,
            borderRadius: 6,
            minWidth: 80,
            alignItems: 'center',
        },
        cancelButton: {
            backgroundColor: '#F5F5F5',
        },
        confirmButton: {
            backgroundColor: theme.colors.primary,
        },
        cancelButtonText: {
            color: theme.colors.onSurfaceVariant,
            fontSize: 16,
            fontWeight: '500',
        },
        confirmButtonText: {
            color: theme.colors.onPrimary,
            fontSize: 16,
            fontWeight: '500',
        },
    });

    return (
        <Portal>
            <Modal
                visible={visible}
                transparent
                animationType="fade"
                onRequestClose={onCancel}
            >
                <View style={styles.overlay}>
                    <Animated.View
                        style={[
                            styles.container,
                            {
                                transform: [{ scale }],
                            },
                        ]}
                    >
                        <View style={styles.header}>
                            <MaterialCommunityIcons
                                name={getIconName()}
                                size={24}
                                color={getIconColor()}
                                style={styles.icon}
                            />
                            <Text style={styles.title}>{title}</Text>
                        </View>
                        <Text style={styles.message}>{message}</Text>
                        <View style={styles.buttonContainer}>
                            {cancelText && (
                                <TouchableOpacity
                                    style={[styles.button, styles.cancelButton]}
                                    onPress={onCancel}
                                >
                                    <Text style={styles.cancelButtonText}>{cancelText}</Text>
                                </TouchableOpacity>
                            )}
                            <TouchableOpacity
                                style={[styles.button, styles.confirmButton]}
                                onPress={onConfirm}
                            >
                                <Text style={styles.confirmButtonText}>{confirmText}</Text>
                            </TouchableOpacity>
                        </View>
                    </Animated.View>
                </View>
            </Modal>
        </Portal>
    );
}; 