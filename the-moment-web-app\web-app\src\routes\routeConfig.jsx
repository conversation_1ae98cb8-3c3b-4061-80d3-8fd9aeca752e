import Home from '../pages/Home';
import Events from '../pages/EventsPage/EventList';
import EventIntroduction from '../pages/EventsPage/EventIntroduction';
import EventEdit from '../pages/EventsPage/EventEdit';
import EventReportPage from '../pages/AdminEventsData/SingleEventReport';
import AllEventsReport from '../pages/AdminEventsData/AllEventsReport';
import EventCalendar from '../pages/EventCalendarPage/EventCalendar';
import EventQRCode from '../pages/EventCalendarPage/EventQRCode';
import UserSettings from '../pages/UserSettings/UserSettings';
import Login from '../pages/Login';
import AccountRecovery from '../pages/AccountRecovery';
import TermsOfService from '../pages/UserAgreement/TermsOfService';
import PrivacyPolicy from '../pages/UserAgreement/PrivacyPolicy';
import VolunteerApproval from '../pages/VolunteerApproval/VolunteerApproval';
import UserVerificationAdmin from '../pages/UserApproval/UserVerificationList';
import ProofDetails from '../pages/UserApproval/ProofDetails';
import UserStat from '../pages/UserProfile';
import UserManagementPage from '../pages/UserManagement/UserManagementPage';
import VolunteerDetails from '../pages/VolunteerApproval/VolunteerDetails';
import PostListPage from '../pages/PostPage/PostsList';
import PostPage from '../pages/PostPage/PostPage';
import CreatePost from '../pages/PostPage/CreatePost';
import ResourceList from '../pages/ResourcePage/ResourceList';

// Import the new Organization management pages
import OrganizationListPage from '../pages/OrganizationSettingsPage/OrganizationListPage';
import OrganizationSettingsPage from '../pages/OrganizationSettingsPage/OrganizationSettingsPage';
import CreateOrganizationPage from '../pages/OrganizationSettingsPage/CreateOrganizationPage';
// Import Post Management Page
import AdminPostsPage from '../pages/AdminPostsData/AdminPostsListTab';
import AdminResourcesPage from '../pages/AdminResourcesData/AdminResourcesListTab'; // Import new resource management page

// Placeholder for where an EditPost component might be, adjust as needed
// import EditPost from '../pages/PostPage/EditPost'; // Assuming an EditPost component exists or will be created

// Define route configurations
export const publicRoutes = {
    login: {
        path: '/login',
        component: Login,
        meta: {
            translationKey: 'login.title'
        }
    },
    // Temporarily hidden verification routes
    /* 
    identityVerification: {
        path: '/register/identity-verification',
        component: IdentityVerificationProcess,
        meta: {
            translationKey: 'registration.steps.identityVerification.title',
            parent: '/login'
        }
    },
    addressVerification: {
        path: '/register/address-verification',
        component: AddressVerificationProcess,
        requiresIdentityVerification: true,
        meta: {
            translationKey: 'registration.steps.addressVerification.title',
            parent: '/login'
        }
    },
    */
    accountRecovery: {
        path: '/account-recovery',
        component: AccountRecovery,
        meta: {
            translationKey: 'accountRecovery.title'
        }
    },
    terms: {
        path: '/user-agreement/terms',
        component: TermsOfService,
        meta: {
            translationKey: 'termsOfService.title'
        }
    },
    privacy: {
        path: '/user-agreement/privacy',
        component: PrivacyPolicy,
        meta: {
            translationKey: 'privacyPolicy.title'
        }
    }
};

export const protectedRoutes = {
    home: {
        path: '/',
        component: Home,
        meta: {
            translationKey: 'mainLayout.breadcrumbs.home'
        }
    },
    events: {
        path: '/events',
        meta: {
            translationKey: 'mainLayout.breadcrumbs.events.all'
        },
        children: {
            list: {
                path: '',
                component: Events,
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.events.all'
                }
            },
            details: {
                path: ':eventId',
                component: EventIntroduction,
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.events.description'
                },
                children: {
                    edit: {
                        path: 'edit',
                        component: EventEdit,
                        requiredRole: ['super_admin', 'admin'],
                        meta: {
                            translationKey: 'mainLayout.breadcrumbs.events.edit'
                        }
                    },
                    reports: {
                        path: 'event-reports',
                        component: EventReportPage,
                        requiredRole: ['super_admin', 'admin'],
                        requiresVerification: true,
                        meta: {
                            translationKey: 'mainLayout.breadcrumbs.events.reports'
                        },
                        children: {
                            userVerification: {
                                path: 'user/verification',
                                component: ProofDetails,
                                requiredRole: ['super_admin', 'admin'],
                                meta: {
                                    translationKey: 'mainLayout.breadcrumbs.events.participantVerificationDetails',
                                    parent: '/events/:eventId/event-reports'
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    eventsManagement: {
        path: '/events-management',
        requiredRole: ['super_admin', 'admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.events.management'
        },
        children: {
            list: {
                path: '',
                component: AllEventsReport,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.events.management'
                }
            },
            create: {
                path: 'create',
                component: EventEdit,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.events.create'
                }
            },
            edit: {
                path: ':eventId/edit',
                component: EventEdit,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.events.edit'
                }
            },
            details: {
                path: ':eventId',
                component: EventIntroduction,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.events.description'
                },
                children: {
                    edit: {
                        path: 'edit',
                        component: EventEdit,
                        requiredRole: ['super_admin', 'admin'],
                        meta: {
                            translationKey: 'mainLayout.breadcrumbs.events.edit'
                        }
                    },
                    reports: {
                        path: 'event-reports',
                        component: EventReportPage,
                        requiredRole: ['super_admin', 'admin'],
                        meta: {
                            translationKey: 'mainLayout.breadcrumbs.events.reports'
                        }
                    }
                }
            }
        }
    },
    myEvents: {
        path: '/my-events',
        component: EventCalendar,
        requiredRole: ['user', 'super_admin', 'admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.myEvents.title'
        },
        children: {
            qrcode: {
                path: ':eventId/qrcode',
                component: EventQRCode,
                requiredRole: ['user', 'super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.myEvents.qrcode'
                }
            }
        }
    },
    approveVolunteers: {
        path: '/volunteers-approval',
        component: VolunteerApproval,
        requiredRole: ['super_admin', 'admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.userManagement.approveVolunteers'
        },
        children: {
            qualificationDetails: {
                path: 'qualification/details/:id',
                component: VolunteerDetails,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.userManagement.volunteerQualificationDetails',
                    parent: '/volunteers-approval'
                }
            },
            eventRegistrationDetails: {
                path: 'eventRegistration/details/:id',
                component: VolunteerDetails,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.userManagement.volunteerEventRegistrationDetails',
                    parent: '/volunteers-approval'
                }
            }
        }
    },
    verification: {
        path: '/verification',
        component: UserVerificationAdmin,
        requiredRole: ['super_admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.userManagement.verification'
        },
        children: {
            details: {
                path: ':id',
                component: ProofDetails,
                requiredRole: ['super_admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.userManagement.verificationDetails',
                    parent: '/verification'
                }
            }
        }
    },
    userManagement: {
        path: '/user-management',
        component: UserManagementPage,
        requiredRole: ['super_admin', 'admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.userManagement.title'
        }
    },
    posts: {
        path: '/posts',
        meta: {
            translationKey: 'mainLayout.breadcrumbs.news.posts'
        },
        children: {
            list: {
                path: '',
                component: PostListPage,
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.news.posts'
                }
            },
            create: {
                path: 'create',
                component: CreatePost,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.news.createPost'
                }
            },
            details: {
                path: ':postId',
                component: PostPage,
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.news.postDetails'
                },
                children: {
                    edit: {
                        path: 'edit',
                        component: CreatePost,
                        requiredRole: ['super_admin', 'admin'],
                        meta: {
                            translationKey: 'mainLayout.breadcrumbs.news.editPost'
                        }
                    }
                }
            }
        }
    },
    resources: {
        path: '/resources',
        component: () => <ResourceList showStatusFilter={false} defaultStatusFilter="published" />,
        meta: {
            translationKey: 'mainLayout.breadcrumbs.resources.list'
        }
    },
    resourcesManagement: {
        path: '/resources-management',
        component: AdminResourcesPage,
        requiredRole: ['super_admin', 'admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.resourcesManagement'
        }
    },
    settings: {
        path: '/settings',
        component: UserSettings,
        requiredRole: ['user', 'super_admin', 'admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.application'
        }
    },
    userProfile: {
        path: '/user-profile/:userId',
        component: UserStat,
        requiredRole: ['user', 'super_admin', 'admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.userProfile'
        }
    },
    // Add the new route for Organization management
    organizationSettings: {
        path: '/organization-settings',
        component: OrganizationListPage,
        requiredRole: ['super_admin', 'admin'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.organizationSettings.title'
        },
        children: {
            list: {
                path: '',
                component: OrganizationListPage,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.organizationSettings.title'
                }
            },
            create: {
                path: 'create',
                component: CreateOrganizationPage,
                requiredRole: ['super_admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.organizationSettings.create'
                }
            },
            edit: {
                path: ':orgId/edit',
                component: OrganizationSettingsPage,
                requiredRole: ['super_admin', 'admin'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.organizationSettings.edit'
                }
            }
        }
    },
    // Add a posts management route similar to events management
    postsManagement: {
        path: '/posts-management',
        requiredRole: ['super_admin', 'admin', 'staff'],
        meta: {
            translationKey: 'mainLayout.breadcrumbs.posts.management'
        },
        children: {
            list: {
                path: '',
                component: AdminPostsPage,
                requiredRole: ['super_admin', 'admin', 'staff'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.posts.management'
                }
            },
            create: {
                path: 'create',
                component: CreatePost,
                requiredRole: ['super_admin', 'admin', 'staff'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.posts.create'
                }
            },
            edit: {
                path: ':postId/edit',
                component: CreatePost,
                requiredRole: ['super_admin', 'admin', 'staff'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.posts.edit'
                }
            },
            details: {
                path: ':postId',
                component: PostPage,
                requiredRole: ['super_admin', 'admin', 'staff'],
                meta: {
                    translationKey: 'mainLayout.breadcrumbs.posts.details'
                },
                children: {
                    edit: {
                        path: 'edit',
                        component: CreatePost,
                        requiredRole: ['super_admin', 'admin', 'staff'],
                        meta: {
                            translationKey: 'mainLayout.breadcrumbs.posts.edit'
                        }
                    }
                }
            }
        }
    }
};

// Helper function to find route meta info
export const findRouteMetaInfo = (pathname) => {
    const findInRoutes = (routes, parentPath = '', parentRoute = null) => {
        for (const route of Object.values(routes)) {
            const fullPath = `${parentPath}/${route.path}`.replace(/\/+/g, '/');
            const pathRegex = new RegExp(
                `^${fullPath.replace(/:[^\s/]+/g, '[^/]+')}$`
            );

            if (pathRegex.test(pathname)) {
                // If we found a match, we need to handle the parent path
                let finalParentPath = parentRoute ? parentPath : null;

                if (finalParentPath) {
                    // Extract actual values from current pathname
                    const currentParts = pathname.split('/');
                    const parentParts = finalParentPath.split('/');

                    // Replace dynamic parameters in parent path with actual values
                    finalParentPath = parentParts.map((part, index) => {
                        if (part.startsWith(':')) {
                            // Find the corresponding value from the current pathname
                            const paramName = part.substring(1);
                            const fullPathParts = fullPath.split('/');
                            const paramIndex = fullPathParts.findIndex(p => p.startsWith(':' + paramName));
                            return paramIndex !== -1 ? currentParts[paramIndex] : part;
                        }
                        return part;
                    }).join('/');
                }

                return {
                    path: fullPath,
                    translationKey: route.meta?.translationKey,
                    parent: finalParentPath
                };
            }

            if (route.children) {
                const result = findInRoutes(route.children, fullPath, route);
                if (result) return result;
            }
        }
        return null;
    };

    const result = findInRoutes({ ...publicRoutes, ...protectedRoutes });
    return result || { path: '/', translationKey: 'mainLayout.breadcrumbs.home', parent: null };
};

// Helper function to get route by path
export const getRouteByPath = (path) => {
    const allRoutes = { ...publicRoutes, ...protectedRoutes };
    return Object.values(allRoutes).find(route => route.path === path);
}; 