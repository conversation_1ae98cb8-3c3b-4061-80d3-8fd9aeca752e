import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Platform, Modal, Animated, Alert, useWindowDimensions, Image, ActivityIndicator } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import QRCode from 'react-native-qrcode-svg';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { userProfileStore } from 'stores/user_store';
import { useFetchUserId, useFetchUserProfile } from '@/api/user_services';
import { CameraView, useCameraPermissions, BarcodeScanningResult } from 'expo-camera';
import { appStyleStore } from 'stores/app_style_store';
import { eventCheckIn } from '@/api/public_events_services';
import { EventCheckInRequest, EventRegistrationPayload } from '@/api/api_config';
import { useFocusEffect } from '@react-navigation/native';
import { organizationStore } from 'stores/organization_store';
import { useVolunteerPermissions } from 'hooks/useVolunteerPermissions';
import { VerificationModal } from '@/common_modules/VerificationModal';

// 驗證狀態類型定義
type VerificationState = {
  visible: boolean;
  isValid: boolean;
  memberId?: string;
  memberName?: string;
  memberUsername?: string;
  memberPhone?: string;
  eventId?: string;
  eventName?: string;
  eventDate?: string;
  eventLocation?: string;
  isCheckedIn?: boolean;
  checkInError?: string;
  registrationId?: string;
  registrationStatus?: string;
  role?: string;
  isLoadingDetails?: boolean;
};

export function QRCodeScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme);
  const userSettings = userProfileStore(state => state.profile);
  const { data: userIdData } = useFetchUserId();
  const { refetch: refetchUserProfile } = useFetchUserProfile();
  
  // Use volunteer permissions hook
  const { hasVolunteerPermissions } = useVolunteerPermissions();
  const { selectedOrganization } = organizationStore(state => state);
  const styles = StyleSheet.create({
    root: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    container: {
      flex: 1,
      padding: 16,
      paddingBottom: 24,
    },
    customHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#F0F0F0',
      backgroundColor: '#FFFFFF',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: '#333333',
    },
    headerButton: {
      padding: 10,
    },
    scanButtonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    scanButtonText: {
      fontSize: 14,
      fontWeight: '500'
    },
    qrCard: {
      backgroundColor: '#FFFFFF',
      borderRadius: 12,
      padding: 20,
      marginBottom: 16,
      width: '100%',
      maxWidth: 380,
      alignSelf: 'center',
      alignItems: 'center',
      ...Platform.select({
        ios: {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    userCard: {
      backgroundColor: '#fff',
      borderRadius: 16,
      padding: 18,
      marginBottom: 18,
      width: '100%',
      maxWidth: 380,
      alignSelf: 'center',
      flexDirection: 'row',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 8,
      elevation: 2,
    },
    avatar: {
      width: 56,
      height: 56,
      borderRadius: 28,
      marginRight: 16,
      backgroundColor: '#F0F0F0',
    },
    userInfoText: {
      flex: 1,
    },
    userInfoRow: {
      marginVertical: 4,
      flexDirection: 'row',
      alignItems: 'center',
    },
    userInfoLabel: {
      fontSize: 15,
      color: '#666',
      fontWeight: '500',
      marginRight: 8,
    },
    userInfoValue: {
      fontSize: 15,
      color: '#333',
      fontWeight: '500',
    },
    qrPlaceholder: {
      width: 200,
      height: 200,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#F0F0F0',
      borderRadius: 8,
    },
    qrPlaceholderText: {
      marginTop: 10,
      color: '#666',
      fontSize: 14,
      textAlign: 'center',
    },
    qrHint: {
      marginTop: 16,
      fontSize: 14,
      color: '#666',
      textAlign: 'center',
    },
    retryButton: {
      marginTop: 12,
      paddingVertical: 8,
      paddingHorizontal: 16,
      backgroundColor: '#F0F0F0',
      borderRadius: 6,
    },
    retryText: {
      fontSize: 14,
      color: '#007AFF',
      fontWeight: '500',
      textAlign: 'center',
    },
    tipsCard: {
      backgroundColor: '#FFF5E6',
      borderRadius: 12,
      width: '100%',
      maxWidth: 380,
      alignSelf: 'center',
      padding: 16,
      ...Platform.select({
        ios: {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.05,
          shadowRadius: 4,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    tipsTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FF8C00',
      marginBottom: 8,
    },
    tipsContent: {
      fontSize: 14,
      color: '#666',
      lineHeight: 20,
    },
    scannerContainer: {
      flex: 1,
      backgroundColor: '#000',
      justifyContent: 'center',
    },
    cameraContainer: {
      flex: 1,
    },
    cameraPermissionText: {
      color: 'white',
      textAlign: 'center',
    },
    scannerOverlay: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'transparent',
    },
    scannerTarget: {
      width: 250,
      height: 250,
      borderWidth: 2,
      borderColor: '#FFF',
      borderRadius: 12,
      backgroundColor: 'rgba(255,255,255,0.1)',
    },
    closeButton: {
      position: 'absolute',
      top: Platform.OS === 'ios' ? 60 : 30,
      left: 20,
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderRadius: 20,
      padding: 10,
    },

  });
  const [permission, requestPermission] = useCameraPermissions();
  const [scannerVisible, setScannerVisible] = useState(false);
  const [scanned, setScanned] = useState(false);
  // 初始化驗證狀態
  const [verificationState, setVerificationState] = useState<VerificationState>({
    visible: false,
    isValid: false,
  });
  const { mutateAsync: checkInMutation, isPending: isCheckingIn } = eventCheckIn();

  // QR Code status management
  type QRStatus = 'loading' | 'success' | 'error' | 'no-auth';
  const [qrStatus, setQrStatus] = useState<QRStatus>('loading');

  // 生成會員二維碼
  const qrValue = userIdData?.uuid ? `The_Moment_Member-${userIdData.uuid}` : '';

  // Update QR status based on data availability
  useEffect(() => {
    if (userIdData === undefined) {
      // Still loading user data
      setQrStatus('loading');
    } else if (!userIdData?.uuid) {
      // No user data - not authenticated
      setQrStatus('no-auth');
    } else if (qrValue) {
      // Successfully have QR value
      setQrStatus('success');
    } else {
      // Something went wrong
      setQrStatus('error');
    }
  }, [userIdData, qrValue]);

  // Check volunteer permissions (moved to hook)
  const volunteerPermissions = hasVolunteerPermissions;

  // 當頁面獲得焦點時獲取用戶資料
  useFocusEffect(
    React.useCallback(() => {
      refetchUserProfile();
      // Note: Volunteer permissions are now handled by useVolunteerPermissions hook
    }, [refetchUserProfile])
  );

  // Retry function for failed QR code generation
  const handleRetry = () => {
    setQrStatus('loading');
    refetchUserProfile();
  };

  // 處理掃描二維碼按鈕點擊
  const handleScanPress = async () => {
    if (!permission?.granted) {
      const newPermission = await requestPermission();
      if (!newPermission.granted) {
        Alert.alert(
          t('camera.permissionDenied.title'),
          t('camera.permissionDenied.message'),
          [{ text: t('common.ok') }]
        );
        return;
      }
    }
    setScannerVisible(true);
    setScanned(false);
  };

  // 處理掃描到的二維碼數據
  const handleBarCodeScanned = ({ data }: BarcodeScanningResult) => {
    if (scanned) return;

    setScanned(true);
    setScannerVisible(false);

    if (data.startsWith('The_Moment_Member-')) {
      const memberId = data.replace('The_Moment_Member-', '');
      setVerificationState({
        visible: true,
        isValid: true,
        memberId,
        memberName: '', // 完成簽到後會更新
        memberUsername: '',
        memberPhone: '',
        eventId: '', // 將從API響應中更新
        eventName: '',
        eventDate: '',
        eventLocation: '',
        isCheckedIn: false
      });
    } else {
      setVerificationState({
        visible: true,
        isValid: false,
      });
    }
  };

  // 關閉驗證模態框
  const handleVerificationClose = () => {
    setVerificationState({
      ...verificationState,
      visible: false,
    });
  };

  // 處理會員簽到
  const handleCheckIn = async () => {
    if (!verificationState.memberId) return;

    setVerificationState(prev => ({
      ...prev,
      isLoadingDetails: true,
      checkInError: undefined
    }));

    try {
      const payload: EventCheckInRequest = { user_id: verificationState.memberId };
      const response = await checkInMutation(payload);

      if (response) {
        const details: EventRegistrationPayload = response;
        setVerificationState(prev => ({
          ...prev,
          isCheckedIn: true,
          isLoadingDetails: false,
          eventId: details.event_id,
          registrationId: details.id,
          eventName: details.event_title,
          memberName: details.user_display_name,
          registrationStatus: details.status,
          role: details.registration_role,
          eventDate: details.event_start_time,
          eventLocation: details.event_location_full_address || details.event_location_online_url,
          checkInError: undefined,
        }));
      } else {
        console.error('[QRCode] Check-in response missing event_registration details:', response);
        setVerificationState(prev => ({
          ...prev,
          isCheckedIn: false,
          isLoadingDetails: false,
          checkInError: t('error.http.500')
        }));
      }
    } catch (error: any) {
      console.error('[QRCode] Check-in error:', error);
      let errorMessage = t('error.http.default');

      // Check for specific error types
      const status = error?.response?.status;
      if (status === 401) {
        errorMessage = t('error.http.401');
      } else if (status === 403) {
        errorMessage = t('error.http.403');
      } else if (status === 404) {
        errorMessage = t('error.http.404');
      } else if (status >= 500) {
        errorMessage = t('error.http.500');
      }
      setVerificationState(prev => ({
        ...prev,
        isLoadingDetails: false,
        checkInError: errorMessage
      }));
    }
  };

  // 在驗證模態框中使用現有的加載狀態
  const isLoading = verificationState.isLoadingDetails;

  // QR Code rendering function
  const renderQRContent = () => {
    switch (qrStatus) {
      case 'loading':
        return (
          <View style={styles.qrPlaceholder}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.qrPlaceholderText}>
              {t('common.loading')}
            </Text>
          </View>
        );
      
      case 'success':
        return (
          <QRCode
            value={qrValue}
            size={200}
            color={theme.system.text}
            backgroundColor={theme.colors.surface}
          />
        );
      
      case 'error':
        return (
          <View style={styles.qrPlaceholder}>
            <MaterialCommunityIcons name="qrcode-remove" size={48} color="#FF3B30" />
            <Text style={[styles.qrPlaceholderText, { color: '#FF3B30' }]}>
              {t('qrcode.error')}
            </Text>
            <TouchableOpacity onPress={handleRetry} style={styles.retryButton}>
              <Text style={styles.retryText}>{t('qrcode.retry')}</Text>
            </TouchableOpacity>
          </View>
        );
      
      case 'no-auth':
      default:
        return (
          <View style={styles.qrPlaceholder}>
            <MaterialCommunityIcons name="qrcode-remove" size={48} color={theme.system.secondaryText} />
            <Text style={styles.qrPlaceholderText}>
              {t('qrcode.loginRequired')}
            </Text>
          </View>
        );
    }
  };

  return (
    <View style={styles.root}>
      {/* Header */}
      <View style={styles.customHeader}>
        <Text style={styles.headerTitle}>{t('qrcode.title')}</Text>
        {volunteerPermissions && (
          <TouchableOpacity onPress={handleScanPress}>
            <View style={styles.scanButtonContainer}>
              <MaterialCommunityIcons
                name="qrcode-scan"
                size={16}
                color={theme.colors.onSurfaceVariant}
              />
              <Text style={[styles.scanButtonText, { color: theme.colors.onSurfaceVariant }]}>
                {t('qrcode.verification.checkIn')}
              </Text>
            </View>
          </TouchableOpacity>
        )}
      </View>



      <View style={styles.container}>
        {/* User Info Card */}
        <View style={styles.userCard}>
          <Image
            source={userSettings?.profile_picture_url ? { uri: userSettings.profile_picture_url } : require('@/assets/default-images/default-image.jpg')}
            style={styles.avatar}
          />
          <View style={styles.userInfoText}>
            <View style={styles.userInfoRow}>
              <Text style={styles.userInfoLabel}>{t('qrcode.username')}</Text>
              <Text style={styles.userInfoValue}>{userSettings?.display_name || t('qrcode.defaultUsername')}</Text>
            </View>
            {userIdData?.uuid && (
              <View style={styles.userInfoRow}>
                <Text style={styles.userInfoLabel}>{t('qrcode.memberId')}</Text>
                <Text style={styles.userInfoValue}>{userIdData.uuid.slice(0, 8).toUpperCase()}</Text>
              </View>
            )}
          </View>
        </View>

        {/* QR Code Card */}
        <View style={styles.qrCard}>
          {renderQRContent()}
          {qrStatus === 'success' && (
            <Text style={styles.qrHint}>{t('qrcode.scanHint')}</Text>
          )}
        </View>

        {/* 重要提示卡片 */}
        <View style={styles.tipsCard}>
          <Text style={styles.tipsTitle}>{t('qrcode.tips.title')}</Text>
          <Text style={styles.tipsContent}>{t('qrcode.tips.content')}</Text>
        </View>
      </View>

      {/* 相機掃描模態框 */}
      <Modal
        animationType="fade"
        transparent={false}
        visible={scannerVisible}
        onRequestClose={() => setScannerVisible(false)}
      >
        <View style={styles.scannerContainer}>
          {!permission ? (
            <Text style={styles.cameraPermissionText}>{t('camera.requestingPermission')}</Text>
          ) : !permission.granted ? (
            <Text style={styles.cameraPermissionText}>{t('camera.noAccess')}</Text>
          ) : (
            <View style={styles.cameraContainer}>
              <CameraView
                style={StyleSheet.absoluteFillObject}
                onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
                barcodeScannerSettings={{
                  barcodeTypes: ['qr'],
                }}
              />
              <View style={styles.scannerOverlay}>
                <View style={styles.scannerTarget} />
              </View>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setScannerVisible(false)}
              >
                <MaterialCommunityIcons name="close" size={30} color="#FFF" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Modal>

      {/* 掃描結果驗證模態框 */}
      <VerificationModal
        visible={verificationState.visible}
        onClose={handleVerificationClose}
        isValid={verificationState.isValid && !verificationState.checkInError}
        title={(!verificationState.isValid || verificationState.checkInError)
          ? (!verificationState.isValid ? t('qrcode.verification.invalidTitle') : t('common.error'))
          : verificationState.isCheckedIn
          ? t('qrcode.verification.checkedInTitle') || 'Successfully Checked In'
          : t('qrcode.verification.validTitle')
        }
        errorMessage={!verificationState.isValid ? t('qrcode.verification.invalidMessage') : verificationState.checkInError}
        memberInfo={verificationState.isValid && !verificationState.checkInError ? {
          memberId: verificationState.memberId,
          memberName: verificationState.memberName,
          role: verificationState.role,
          registrationStatus: verificationState.registrationStatus,
        } : undefined}
        eventInfo={verificationState.isValid && !verificationState.checkInError && verificationState.isCheckedIn ? {
          eventName: verificationState.eventName,
          eventDate: verificationState.eventDate,
          eventLocation: verificationState.eventLocation,
        } : undefined}
        customContent={verificationState.isValid && !verificationState.checkInError ? (
          <View style={{ alignItems: 'center', marginTop: 16 }}>
            {verificationState.isLoadingDetails ? (
              <Text style={{ fontSize: 16, color: '#333', textAlign: 'center' }}>
                {t('qrcode.verification.loading') || 'Loading event details...'}
              </Text>
            ) : verificationState.isCheckedIn ? (
              <Text style={{ fontSize: 16, color: '#333', textAlign: 'center' }}>
                {t('qrcode.verification.checkedInPrompt') || 'Member has been successfully checked in!'}
              </Text>
            ) : (
              <Text style={{ fontSize: 16, color: '#333', textAlign: 'center' }}>
                {t('qrcode.verification.approvePrompt')}
              </Text>
            )}
          </View>
        ) : undefined}
        actions={(!verificationState.isValid || verificationState.checkInError) ? [
          {
            text: t('common.ok'),
            onPress: handleVerificationClose,
            variant: 'primary'
          }
        ] : verificationState.isCheckedIn ? [
          {
            text: t('common.ok'),
            onPress: handleVerificationClose,
            variant: 'primary'
          }
        ] : [
          {
            text: t('common.cancel'),
            onPress: handleVerificationClose,
            variant: 'secondary'
          },
          {
            text: t('qrcode.verification.checkIn'),
            onPress: handleCheckIn,
            variant: 'primary',
            loading: verificationState.isLoadingDetails || isCheckingIn
          }
        ]}
      />
    </View>
  );
}
export default QRCodeScreen;