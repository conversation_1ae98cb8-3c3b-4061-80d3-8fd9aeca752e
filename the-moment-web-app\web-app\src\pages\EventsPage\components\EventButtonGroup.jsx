import React from 'react';
import { UserOutlined, UserAddOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

/**
 * A component that renders event participation buttons based on user role and event status
 */
const EventButtonGroup = ({
  isApprovedVolunteer,
  isFull,
  isWaitingListFull,
  isLoading,
  onJoinEvent,
  onJoinWaitingList,
  onVolunteerJoin,
  userRole
}) => {
  const { t } = useTranslation();

  // Hide buttons for admin/super_admin
  if (userRole === 'admin' || userRole === 'super_admin') {
    return null; // Or <></> for an empty fragment
  }

  // Loading spinner element
  const LoadingSpinner = () => (
    <>
      <span className="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></span>
      {t('common.loading')}
    </>
  );

  // Get button styles based on button type and state
  const getButtonStyles = (type, isDisabled, isWaitingListFull = false) => {
    const baseStyles = "font-medium py-3 px-4 rounded-lg transition duration-200 ease-in-out h-[46px] flex items-center justify-center";
    const disabledStyles = "cursor-not-allowed";
    
    // Button type specific styles
    const typeStyles = {
      participant: {
        normal: "bg-sky-500 hover:bg-sky-500/90 text-white",
        disabled: "bg-sky-400 text-white"
      },
      volunteer: {
        normal: "bg-pink-500 hover:bg-pink-500/90 text-white",
        disabled: "bg-pink-400 text-white"
      },
      waitingList: {
        normal: "bg-yellow-500 hover:bg-yellow-500/90 text-white",
        disabled: "bg-yellow-400 text-white",
        full: "bg-gray-400 text-white"
      }
    };

    // Determine which style to use
    let buttonStyle;
    if (type === 'waitingList' && isWaitingListFull) {
      buttonStyle = typeStyles[type].full;
    } else {
      buttonStyle = isDisabled ? typeStyles[type].disabled : typeStyles[type].normal;
    }

    return `${baseStyles} ${isDisabled ? disabledStyles : ''} ${buttonStyle}`;
  };

  // Participant button
  const ParticipantButton = ({ className = "", fullWidth = false }) => (
    <button
      onClick={onJoinEvent}
      disabled={isLoading}
      className={`${fullWidth ? 'w-full' : 'flex-1'} ${getButtonStyles('participant', isLoading)} ${className}`}
    >
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <>
          <UserOutlined className="mr-2" />
          {t('eventIntroduction.participation.buttons.participant')}
        </>
      )}
    </button>
  );

  // Volunteer button
  const VolunteerButton = ({ className = "" }) => (
    <button
      onClick={onVolunteerJoin}
      disabled={isLoading}
      className={`flex-1 ${getButtonStyles('volunteer', isLoading)} ${className}`}
    >
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <>
          <UserOutlined className="mr-2" />
          {t('eventIntroduction.participation.buttons.volunteer')}
        </>
      )}
    </button>
  );

  // Waiting list button
  const WaitingListButton = ({ className = "", fullWidth = false }) => {
    // Determine button state
    const isDisabled = isLoading || isWaitingListFull;
    
    return (
      <button
        onClick={onJoinWaitingList}
        disabled={isDisabled}
        className={`${fullWidth ? 'w-full' : 'flex-1'} ${getButtonStyles('waitingList', isLoading, isWaitingListFull)} ${className}`}
      >
        {isLoading ? (
          <LoadingSpinner />
        ) : isWaitingListFull ? (
          <>
            <ExclamationCircleOutlined className="mr-2" />
            {t('eventIntroduction.participation.buttons.waitingListFull')}
          </>
        ) : (
          <>
            <UserAddOutlined className="mr-2" />
            {t('eventIntroduction.participation.buttons.joinWaitingList')}
          </>
        )}
      </button>
    );
  };

  // Waiting list message
  const WaitingListMessage = () => (
    <div className="mt-2 text-center">
      <p className="text-yellow-600 text-sm">
        {t('eventIntroduction.participation.eventFull')}
        <br />
        {isWaitingListFull ? 
          t('eventIntroduction.participation.waitingList.full') : 
          t('eventIntroduction.participation.waitingList.description')}
      </p>
    </div>
  );

  // Render based on user role and event status
  if (isApprovedVolunteer) {
    return (
      <div className="flex flex-col gap-3">
        <div className="flex flex-row lg:flex-col xl:flex-row gap-3">
          {isFull ? (
            <WaitingListButton />
          ) : (
            <ParticipantButton />
          )}
          <VolunteerButton />
        </div>
      </div>
    );
  } else {
    return (
      <>
        {isFull ? (
          <>
            <WaitingListButton fullWidth />
            <WaitingListMessage />
          </>
        ) : (
          <ParticipantButton fullWidth />
        )}
      </>
    );
  }
};

export default EventButtonGroup;
