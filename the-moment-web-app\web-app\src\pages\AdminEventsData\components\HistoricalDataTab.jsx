import React, { createContext, useContext, useState, useEffect, useMemo, useRef, useCallback } from 'react';
import {
    closestCenter,
    DndContext,
    DragOverlay,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import {
    arrayMove,
    horizontalListSortingStrategy,
    SortableContext,
    useSortable,
} from '@dnd-kit/sortable';
import { Table, Button, Radio, Select, DatePicker, Typography, App, Spin, Space, Switch, Tag } from 'antd';
import {
    DownloadOutlined,
    InfoCircleOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
} from '@ant-design/icons';
import '../../../styles/ReportPage.css';
import CustomizedDropdown from '../../../components/CustomizedDropdown';
import { useTranslation } from 'react-i18next';
import { formatSimpleDateTime, formatToYYYYMMDD } from '../../../utils/dateFormatter';
import { fetchApprovedVerificationsForOrg } from '../../../hooks/useVerificationQueries';
import { useOrganization } from '../../../contexts/OrganizationContext';
import { groupVerificationsByUser, getUserVerificationStatus } from '../../../utils/verificationUtils';
import { useNavigate } from 'react-router-dom';
import { exportData } from '../../../utils/exportUtils';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// Create a context for drag state
const DragIndexContext = createContext({
    active: -1,
    over: -1,
});

// Define styles for draggable elements
const dragActiveStyle = (dragState, id) => {
    const { active, over, direction } = dragState;

    let style = {};
    if (active && active === id) {
        style = {
            backgroundColor: 'gray',
            opacity: 0.5,
        };
    } else if (over && id === over && active !== over) {
        style =
            direction === 'right'
                ? {
                    borderRight: '1px dashed gray',
                }
                : {
                    borderLeft: '1px dashed gray',
                };
    }
    return style;
};

// Table header cell component with drag-and-drop functionality
const TableHeaderCell = (props) => {
    const dragState = useContext(DragIndexContext);
    const { attributes, listeners, setNodeRef, isDragging } = useSortable({
        id: props.id,
    });

    // Return plain th for expand control column and actions column
    if (props.className?.includes('ant-table-row-expand-icon-cell') || 
        props.id === 'actions') {
        return <th {...props} />;
    }

    const style = {
        ...props.style,
        cursor: 'move',
        ...(isDragging
            ? {
                position: 'relative',
                zIndex: 9999,
                userSelect: 'none',
            }
            : {}),
        ...dragActiveStyle(dragState, props.id),
    };
    return <th {...props} ref={setNodeRef} style={style} {...attributes} {...listeners} />;
};

const HistoricalDataTab = ({ eventId, registrationsData, loading: parentLoading }) => {
    const { t } = useTranslation();
    const { message } = App.useApp();
    const navigate = useNavigate();
    const { currentOrganization } = useOrganization();

    // State variables
    const [exportFormat, setExportFormat] = useState('csv');
    const [checkedList, setCheckedList] = useState([
        'displayName',
        'applicationDate',
        'registrationRole',
        'status',
        'userPhone',
        'paymentStatus',
        'documentCount',
        'actions'
    ]);
    // Add a list of fixed columns that should always be visible and not toggleable in dropdown
    const [fixedColumns] = useState(['actions']);
    const [reportData, setReportData] = useState({
        participants: [],
        total: 0,
    });
    const [loading, setLoading] = useState(false);
    const [exportLoading, setExportLoading] = useState(false);
    const [expandedRowKeys, setExpandedRowKeys] = useState([]);
    const [verifications, setVerifications] = useState([]);
    const [groupedReportData, setGroupedReportData] = useState([]);

    // State for drag-and-drop indices
    const [dragIndex, setDragIndex] = useState({
        active: -1,
        over: -1,
        direction: null
    });

    // Handle "View All Details" button click
    const handleViewAllDetails = useCallback((e, record) => {
        e.stopPropagation(); // Prevent row expansion
        
        if (!record.documents || record.documents.length === 0) {
            message.info(t('adminEvents.singleEventReport.attendance.verificationDetails.noRecords'));
            return;
        }
        
        // Create data with all document reqIds, types, and statuses, similar to UserVerificationList.jsx
        const documentData = record.documents.map(doc => ({
            reqId: doc.request_id,
            type: doc.verification_type,
            status: doc.status || 'pending'
        }));
        
        // Navigate to ProofDetails with the correct event reports path that includes eventId
        navigate(`/events/${eventId}/event-reports/user/verification`, {
            state: { documentData }
        });
    }, [eventId, message, navigate, t]);

    // Initialize columns with drag-and-drop attributes
    const baseColumns = useMemo(
        () => [
            {
                title: t('adminEvents.singleEventReport.attendance.columns.displayName'),
                dataIndex: 'user_display_name',
                key: 'displayName',
                width: 150,
                onHeaderCell: () => ({ id: 'displayName' }),
                onCell: () => ({ id: 'displayName' }),
            },
            {
                title: t('adminEvents.singleEventReport.attendance.columns.applicationDate'),
                dataIndex: 'registered_at',
                key: 'applicationDate',
                width: 180,
                onHeaderCell: () => ({ id: 'applicationDate' }),
                onCell: () => ({ id: 'applicationDate' }),
                render: (date) => date ? formatSimpleDateTime(date) : '-',
            },
            {
                title: t('adminEvents.singleEventReport.attendance.columns.registrationRole'),
                dataIndex: 'registration_role',
                key: 'registrationRole',
                width: 150,
                onHeaderCell: () => ({ id: 'registrationRole' }),
                onCell: () => ({ id: 'registrationRole' }),
                render: (role) => role ? t(`common.roles.${role.toLowerCase()}`) || role : '-',
            },
            {
                title: t('adminEvents.singleEventReport.attendance.columns.status'),
                dataIndex: 'status',
                key: 'status',
                width: 120,
                onHeaderCell: () => ({ id: 'status' }),
                onCell: () => ({ id: 'status' }),
                render: (status) => {
                    // Replace Tag with plain text
                    const displayStatus = status?.toLowerCase() || 'unknown';
                    return t(`common.status.${displayStatus}`);
                }
            },
            {
                title: t('adminEvents.singleEventReport.attendance.columns.userPhone'),
                dataIndex: 'user_phone',
                key: 'userPhone',
                width: 150,
                onHeaderCell: () => ({ id: 'userPhone' }),
                onCell: () => ({ id: 'userPhone' }),
                render: (phone) => phone || '-',
            },
            {
                title: t('adminEvents.singleEventReport.attendance.columns.paymentStatus'),
                dataIndex: 'payment_status',
                key: 'paymentStatus',
                width: 150,
                onHeaderCell: () => ({ id: 'paymentStatus' }),
                onCell: () => ({ id: 'paymentStatus' }),
                render: (paymentStatus) => {
                    // Replace Tag with plain text
                    const status = paymentStatus?.toLowerCase() || 'unpaid';
                    return t(`common.payment.${status}`) || status;
                }
            },
            {
                title: t('adminEvents.singleEventReport.attendance.columns.documentCount'),
                key: 'documentCount',
                width: 150,
                onHeaderCell: () => ({ id: 'documentCount' }),
                onCell: () => ({ id: 'documentCount' }),
                render: (_, record) => record.documents?.length || 0
            },
            {
                title: t('adminEvents.singleEventReport.attendance.columns.actions'),
                key: 'actions',
                width: 150,
                fixed: 'right',
                onHeaderCell: () => ({ id: 'actions' }),
                onCell: () => ({ id: 'actions' }),
                render: (_, record) => (
                    <Space size="middle">
                        {record.documents && record.documents.length > 0 ? (
                            <button
                                onClick={(e) => handleViewAllDetails(e, record)}
                                className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                            >
                                {t('common.viewAllDetails')}
                            </button>
                        ) : (
                            <span className="text-gray-400 text-sm">
                                {t('adminEvents.singleEventReport.attendance.noDocuments')}
                            </span>
                        )}
                    </Space>
                ),
            }
        ],
        [t, handleViewAllDetails]
    );

    const [columns, setColumns] = useState(baseColumns);

    // Add hidden property to columns
    const columnsWithVisibility = useMemo(() => {
        return columns.map((item) => ({
            ...item,
            hidden: !checkedList.includes(item.key),
        }));
    }, [columns, checkedList]);

    // Process registration data and fetch verification data
    useEffect(() => {
        const processRegistrationData = async () => {
            if (!eventId || !registrationsData || parentLoading) return;
            
            try {
                setLoading(true);
                
                // Get all user IDs from the registrations
                const userIds = registrationsData.map(reg => reg.user_id).filter(Boolean);
                
                // Now fetch verification data using the same method as UserVerificationList.jsx
                try {
                    const orgIdToUse = currentOrganization?.id;
                    
                    // Using large page size to get all pending verifications at once
                    const result = await fetchApprovedVerificationsForOrg(
                        orgIdToUse,
                        1,    // Page
                        1000  // Larger page size to get all
                    );
                    
                    // Store original verifications data for reference
                    const verificationsArray = Array.isArray(result.verifications) ? result.verifications : [];
                    
                    // Filter verifications to only include those from users in our registration data
                    const filteredVerifications = verificationsArray.filter(verification => 
                        userIds.includes(verification.user_id)
                    );
                    
                    setVerifications(filteredVerifications);
                    
                    // First prepare registrations data with proper structure
                    const registrationsMap = new Map();
                    
                    // Group registrations by user_id
                    registrationsData.forEach(reg => {
                        if (!reg.user_id) return;
                        
                        registrationsMap.set(reg.user_id, {
                            ...reg,
                            documents: [] // Initialize empty documents array
                        });
                    });
                    
                    // Now add verification documents to each user
                    filteredVerifications.forEach(verification => {
                        const userId = verification.user_id;
                        if (registrationsMap.has(userId)) {
                            const userRecord = registrationsMap.get(userId);
                            
                            // Add this verification to the user's documents
                            userRecord.documents.push({
                                verification_type: verification.verification_type,
                                status: verification.status,
                                submitted_at: verification.submitted_at,
                                reviewed_at: verification.reviewed_at,
                                request_id: verification.request_id || verification.id || `temp-req-${userId}-${userRecord.documents.length}`
                            });
                            
                            // Update the map
                            registrationsMap.set(userId, userRecord);
                        }
                    });
                    
                    // Convert map to array for display
                    const mergedData = Array.from(registrationsMap.values());
                    
                    // Group documents by verification_type and keep only the most recent one
                    const finalProcessedData = mergedData.map(user => {
                        const uniqueDocumentsByType = {};
                        
                        (user.documents || []).forEach(doc => {
                            const type = doc.verification_type;
                            if (!uniqueDocumentsByType[type] || 
                                (doc.submitted_at && 
                                (!uniqueDocumentsByType[type].submitted_at || 
                                 new Date(doc.submitted_at) > new Date(uniqueDocumentsByType[type].submitted_at)))) {
                                uniqueDocumentsByType[type] = doc;
                            }
                        });
                        
                        const uniqueDocuments = Object.values(uniqueDocumentsByType).map((doc, index) => {
                            if (!doc.request_id) {
                                return {
                                    ...doc,
                                    request_id: doc.id || `temp-req-${user.user_id}-${index}` // Ensure request_id
                                };
                            }
                            return doc;
                        });
                        
                        return {
                            ...user,
                            documents: uniqueDocuments 
                        };
                    });
                    
                    console.log('Merged data with verifications:', finalProcessedData);
                    
                    setGroupedReportData(finalProcessedData);
                    setReportData({
                        participants: finalProcessedData,
                        total: finalProcessedData.length,
                    });
                    
                } catch (error) {
                    console.error('Error fetching verification data:', error);
                    
                    // If verification API fails, still show registration data
                    setGroupedReportData(registrationsData);
                    setReportData({
                        participants: registrationsData.map(reg => ({
                            ...reg,
                            documents: []
                        })),
                        total: registrationsData.length,
                    });
                }
                
            } catch (error) {
                console.error('Error processing participant data:', error);
                message.error(t('messages.fetchError'));
                setReportData({
                    participants: [],
                    total: 0
                });
            } finally {
                setLoading(false);
            }
        };
        
        processRegistrationData();
    }, [eventId, registrationsData, parentLoading, t, message, currentOrganization?.id]);

    // Effect to update columns when baseColumns (translations) change,
    // while preserving the user's drag-and-drop order.
    useEffect(() => {
        setColumns(currentColumnsOrder =>
            currentColumnsOrder.map(colInOrder => {
                const newBaseColDef = baseColumns.find(bc => bc.key === colInOrder.key);
                if (newBaseColDef) {
                    // Return the new column definition, which includes the updated title
                    // and other properties, effectively placing the newly translated column
                    // definition into the existing user-defined order.
                    return { ...newBaseColDef };
                }
                return colInOrder; // Fallback, though should ideally not be reached if keys are consistent
            })
        );
    }, [baseColumns]);

    // Sensors for drag-and-drop
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 1,
            },
        })
    );

    // Handler for when drag ends
    const onDragEnd = ({ active, over }) => {
        if (active.id !== over?.id) {
            setColumns((prevColumns) => {
                const activeIndex = prevColumns.findIndex((i) => i.key === active.id);
                const overIndex = prevColumns.findIndex((i) => i.key === over.id);
                return arrayMove(prevColumns, activeIndex, overIndex);
            });
        }
        setDragIndex({
            active: -1,
            over: -1,
        });
    };

    // Handler for when an item is dragged over another
    const onDragOver = ({ active, over }) => {
        const activeIndex = columns.findIndex((i) => i.key === active.id);
        const overIndex = columns.findIndex((i) => i.key === over?.id);
        setDragIndex({
            active: active.id,
            over: over?.id,
            direction: overIndex > activeIndex ? 'right' : 'left',
        });
    };

    // Only export columns that are checked AND not fixed (like 'actions')
    const handleExport = async () => {
        try {
            setExportLoading(true);
            
            // Get all data with verification details properly structured
            const dataToExport = reportData.participants.map(participant => {
                // Base participant data (parent fields)
                const baseData = {
                    user_display_name: participant.user_display_name || '',
                    registered_at: participant.registered_at ? formatSimpleDateTime(participant.registered_at) : '-',
                    registration_role: participant.registration_role ? t(`common.roles.${participant.registration_role.toLowerCase()}`) || participant.registration_role : '-',
                    status: participant.status ? t(`common.status.${participant.status.toLowerCase()}`) : '-',
                    user_phone: participant.user_phone || '-',
                    payment_status: participant.payment_status ? t(`common.payment.${participant.payment_status.toLowerCase()}`) : '-',
                    documentCount: participant.documents?.length || 0,
                };
                
                // Only include selected columns for the parent data
                // Filter out fixedColumns (like 'actions') from export
                const exportableCheckedList = checkedList.filter(col => !fixedColumns.includes(col));
                const filteredParentData = {};
                exportableCheckedList.forEach(key => {
                    if (key === 'displayName') filteredParentData.user_display_name = baseData.user_display_name;
                    if (key === 'applicationDate') filteredParentData.registered_at = baseData.registered_at;
                    if (key === 'registrationRole') filteredParentData.registration_role = baseData.registration_role;
                    if (key === 'status') filteredParentData.status = baseData.status;
                    if (key === 'userPhone') filteredParentData.user_phone = baseData.user_phone;
                    if (key === 'paymentStatus') filteredParentData.payment_status = baseData.payment_status;
                    if (key === 'documentCount') filteredParentData.documentCount = baseData.documentCount;
                });
                
                // Include verifications as a sub-array (will be properly handled by our export utility)
                if (participant.documents && participant.documents.length > 0) {
                    filteredParentData.verifications = participant.documents.map(doc => ({
                        type: doc.verification_type ? t(`documents.${doc.verification_type.toLowerCase()}`) || doc.verification_type : '-',
                        status: doc.status ? t(`common.${doc.status.toLowerCase() === 'pending_review' ? 'pending' : doc.status.toLowerCase()}`) : '-',
                        submitted_at: doc.submitted_at ? formatSimpleDateTime(doc.submitted_at) : '-',
                        reviewed_at: doc.reviewed_at && !doc.reviewed_at.includes('0001-01-01') ? formatSimpleDateTime(doc.reviewed_at) : '-'
                    }));
                } else {
                    // Ensure verifications array exists even if empty
                    filteredParentData.verifications = [];
                }
                
                return filteredParentData;
            });
            
            // Generate filename
            const filename = `event-participants-${eventId}-${formatToYYYYMMDD(new Date())}`;
            
            // Create column header translations map
            const headerMap = {
                // Parent column headers
                user_display_name: t('adminEvents.singleEventReport.attendance.columns.displayName'),
                registered_at: t('adminEvents.singleEventReport.attendance.columns.applicationDate'),
                registration_role: t('adminEvents.singleEventReport.attendance.columns.registrationRole'),
                status: t('adminEvents.singleEventReport.attendance.columns.status'),
                user_phone: t('adminEvents.singleEventReport.attendance.columns.userPhone'),
                payment_status: t('adminEvents.singleEventReport.attendance.columns.paymentStatus'),
                documentCount: t('adminEvents.singleEventReport.attendance.columns.documentCount'),
                
                // Child column headers (verification details)
                type: t('adminEvents.singleEventReport.attendance.verificationDetails.type'),
                submitted_at: t('adminEvents.singleEventReport.attendance.verificationDetails.submittedAt'),
                reviewed_at: t('adminEvents.singleEventReport.attendance.verificationDetails.reviewedAt'),
                
                // Legacy flattened headers
                verification_types: t('adminEvents.singleEventReport.attendance.verificationDetails.type'),
                verification_statuses: t('adminEvents.singleEventReport.attendance.verificationDetails.status')
            };
            
            // Define parent fields based on checkedList (excluding fixed columns like 'actions')
            const parentFieldsMap = {
                'displayName': 'user_display_name',
                'applicationDate': 'registered_at',
                'registrationRole': 'registration_role',
                'status': 'status',
                'userPhone': 'user_phone',
                'paymentStatus': 'payment_status',
                'documentCount': 'documentCount'
            };
            
            // Only include fields that are checked in the dropdown AND not fixed
            const exportableCheckedList = checkedList.filter(col => !fixedColumns.includes(col));
            const parentFields = exportableCheckedList
                .map(key => parentFieldsMap[key])
                .filter(Boolean);
                
            const childFields = ['type', 'status', 'submitted_at', 'reviewed_at'];
            
            // Export options with translations and hierarchical structure
            const exportOptions = {
                title: t('adminEvents.singleEventReport.attendance.export.title'),
                orientation: 'landscape',
                headerMap: headerMap,
                exportDateLabel: t('common.exportDate') || 'Export date:',
                preserveHierarchy: true, // Enable hierarchical structure
                parentFields: parentFields,
                childFields: childFields,
                useTableRendering: true, // Use table rendering for PNG
                // Add column ordering information
                columnOrder: columns,
                checkedList: checkedList,
                fixedColumns: fixedColumns,
                parentFieldsMap: parentFieldsMap
            };
            
            // For PNG export using screenshot approach (fallback)
            const tableElement = exportFormat === 'png' ? document.querySelector('.ant-table-container') : null;
            
            // Export based on format
            await exportData(dataToExport, exportFormat, filename, exportOptions, tableElement);
            
            message.success(t('adminEvents.singleEventReport.attendance.export.success'));
        } catch (error) {
            console.error('Export error:', error);
            message.error(t('adminEvents.singleEventReport.attendance.export.error'));
        } finally {
            setExportLoading(false);
        }
    };

    // Handle row expansion
    const handleRowExpand = (expanded, record) => {
        if (expanded) {
            setExpandedRowKeys([...expandedRowKeys, record.user_id]);
        } else {
            setExpandedRowKeys(expandedRowKeys.filter(key => key !== record.user_id));
        }
    };

    // Handle row click
    const handleRowClick = (record) => {
        // Only handle click if row is expandable
        if (record.documents && record.documents.length > 0) {
        const isExpanded = expandedRowKeys.includes(record.user_id);
            if (isExpanded) {
                setExpandedRowKeys(expandedRowKeys.filter(key => key !== record.user_id));
            } else {
                setExpandedRowKeys([...expandedRowKeys, record.user_id]);
            }
        }
    };

    // Column definitions for expanded row
    const verificationColumns = [
        {
            title: t('adminEvents.singleEventReport.attendance.verificationDetails.type'),
            dataIndex: 'verification_type',
            key: 'verification_type',
            render: (type) => type ? t(`documents.${type.toLowerCase()}`) || type : '-'
        },
        {
            title: t('adminEvents.singleEventReport.attendance.verificationDetails.status'),
            dataIndex: 'status',
            key: 'status',
            render: (status) => {
                // Replace Tag with plain text
                const displayStatus = status?.toLowerCase() === 'pending_review' ? 'pending' : status?.toLowerCase();
                return t(`common.${displayStatus || 'pending'}`);
            }
        },
        {
            title: t('adminEvents.singleEventReport.attendance.verificationDetails.submittedAt'),
            dataIndex: 'submitted_at',
            key: 'submitted_at',
            render: (time) => time ? formatSimpleDateTime(time) : '-'
        },
        {
            title: t('adminEvents.singleEventReport.attendance.verificationDetails.reviewedAt'),
            dataIndex: 'reviewed_at',
            key: 'reviewed_at',
            render: (time) => {
                // Check if time is null, undefined, or represents 0001-01-01
                if (!time || time.includes('0001-01-01')) {
                    return '-';
                }
                return formatSimpleDateTime(time);
            }
        }
    ];

    // Render expanded row content
    const expandedRowRender = (record) => {
        return (
            <Table
                columns={verificationColumns}
                dataSource={record.documents}
                pagination={false}
                size="small"
                rowKey="request_id"
            />
        );
    };

    return (
        <div>
            <div className="flex justify-between items-center mb-4 sticky top-0 bg-white z-10 border-b py-2">
                <div className="text-lg font-medium text-gray-900">
                    {reportData.total > 0 ? (
                        <div>
                            <span className="mr-2">{t('adminEvents.singleEventReport.attendance.total')}:</span>
                            <span>{reportData.total}</span>
                        </div>
                    ) : (
                        <span>{t('adminEvents.singleEventReport.attendance.title')}</span>
                    )}
                </div>
                <div className="flex items-center gap-3">
                    <Radio.Group
                        options={[
                            { label: 'CSV', value: 'csv' },
                            { label: 'PDF', value: 'pdf' },
                            { label: 'PNG', value: 'png' },
                        ]}
                        onChange={(e) => setExportFormat(e.target.value)}
                        value={exportFormat}
                        disabled={exportLoading}
                        className="mr-1"
                    />
                    <Button
                        type="primary"
                        onClick={handleExport}
                        loading={exportLoading}
                        disabled={reportData.total === 0}
                    >
                        <DownloadOutlined /> {t('adminEvents.singleEventReport.attendance.export.title')}
                    </Button>
                    <CustomizedDropdown
                        allColumns={baseColumns}
                        checkedList={checkedList}
                        setCheckedList={setCheckedList}
                        disabled={loading}
                        fixedColumns={fixedColumns}
                    />
                </div>
            </div>

            {/* Draggable table */}
            <DndContext
                sensors={sensors}
                modifiers={[restrictToHorizontalAxis]}
                onDragEnd={onDragEnd}
                onDragOver={onDragOver}
                collisionDetection={closestCenter}
            >
                <SortableContext
                    items={columnsWithVisibility.filter(col => !col.hidden).map((i) => i.key)}
                    strategy={horizontalListSortingStrategy}
                >
                    <DragIndexContext.Provider value={dragIndex}>
                        <div className="border rounded-lg overflow-hidden" style={{ minWidth: '100%' }}>
                            <Table
                                rowKey="user_id"
                                columns={columnsWithVisibility.filter(col => !col.hidden)}
                                dataSource={reportData?.participants || []}
                                loading={loading}
                                components={{
                                    header: {
                                        cell: TableHeaderCell,
                                    }
                                }}
                                scroll={{
                                    x: columnsWithVisibility.filter(col => !col.hidden)
                                        .reduce((total, col) => total + (col.width || 150), 0)
                                }}
                                pagination={false}
                                virtual={false} // Changed for better compatibility with expandable rows
                                style={{ height: '100%' }}
                                expandable={{
                                    expandedRowRender,
                                    expandedRowKeys,
                                    onExpand: handleRowExpand,
                                    rowExpandable: record => record.documents && record.documents.length > 0,
                                }}
                                onRow={(record) => ({
                                    onClick: () => handleRowClick(record),
                                    style: { cursor: record.documents && record.documents.length > 0 ? 'pointer' : 'default' }
                                })}
                            />
                        </div>
                    </DragIndexContext.Provider>
                </SortableContext>
                <DragOverlay>
                    <table>
                        <thead>
                            <tr>
                                <th
                                    style={{
                                        backgroundColor: 'gray',
                                        padding: 16,
                                    }}
                                >
                                    {columns.find((i) => i.key === dragIndex?.active)?.title}
                                </th>
                            </tr>
                        </thead>
                    </table>
                </DragOverlay>
            </DndContext>


        </div>
    );
};

export default HistoricalDataTab; 